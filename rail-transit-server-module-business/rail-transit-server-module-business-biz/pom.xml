<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.com.sipsg</groupId>
        <artifactId>rail-transit-server-module-business</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>rail-transit-server-module-business-biz</artifactId>
    <description>business 模块业务逻辑</description>

    <dependencies>
        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>rail-transit-server-module-business-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- RPC 相关 -->
        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>common-rpc</artifactId>
        </dependency>

        <!-- 注册中心 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- 配置中心 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>common-web</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>common-swagger</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>common-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>common-redis</artifactId>
        </dependency>

        <!-- 日志相关 -->
        <dependency>
            <groupId>com.yomahub</groupId>
            <artifactId>tlog-web-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>common-operationlog</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>rail-transit-server-module-system-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 认证相关 -->
        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>common-security</artifactId>
        </dependency>

        <!-- 服务保障相关 -->
        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>common-protection</artifactId>
        </dependency>

        <!-- 数据权限相关 -->
        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>common-datapermission</artifactId>
        </dependency>

        <!-- 分页 -->
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
            <version>2.1.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>mybatis-spring</artifactId>
                    <groupId>org.mybatis</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mybatis</artifactId>
                    <groupId>org.mybatis</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.3.2</version>
        </dependency>

        <dependency>
            <groupId>io.github.openfeign.form</groupId>
            <artifactId>feign-form-spring</artifactId>
            <version>3.8.0</version>
        </dependency>

        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-jackson</artifactId>
            <version>11.10</version>
        </dependency>

        <dependency>
            <groupId>io.minio</groupId>
            <artifactId>minio</artifactId>
            <version>8.5.7</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <!-- 打包 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-install-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
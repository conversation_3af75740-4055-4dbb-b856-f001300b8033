package cn.com.sipsg.module.business.module.supervision.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@TableName("tbl_secret_investigation_plan")
@Schema(description = "暗访检查计划")
public class SecretInvestigationPlan {
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键ID")
    private String id;

    @TableField("create_user_id")
    @Schema(description = "创建者")
    private String createUserId;

    @TableField("create_time")
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @TableField("update_user_id")
    @Schema(description = "更新者")
    private String updateUserId;

    @TableField("update_time")
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @TableField("available")
    @Schema(description = "是否有效")
    private Boolean available;

    @TableField("plan_name")
    @Schema(description = "计划名称")
    private String planName;

    @TableField("plan_type")
    @Schema(description = "计划类型 1: 暗访检查")
    private Integer planType;

    @TableField("plan_start_day")
    @Schema(description = "计划开始日期")
    private String planStartDay;

    @TableField("plan_required_count")
    @Schema(description = "检查任务完成次数")
    private Integer planRequiredCount;

    @TableField("plan_status")
    @Schema(description = "计划状态 1:未开始 2:进行中 3:已完成")
    private Integer planStatus;

    @TableField("round_rolling")
    @Schema(description = "是否重复")
    private Boolean roundRolling;

    @TableField("round_count")
    @Schema(description = "重复次数 从1开始，1表示只生成一次任务")
    private Integer roundCount;

    @TableField("frequency")
    @Schema(description = "频率（单位：天）")
    private Integer frequency;

    @TableField("detail")
    @Schema(description = "计划说明")
    private String detail;

    @TableField("remark")
    @Schema(description = "备注")
    private String remark;

    @TableField("extend_file")
    @Schema(description = "附件")
    private String extendFile;
} 
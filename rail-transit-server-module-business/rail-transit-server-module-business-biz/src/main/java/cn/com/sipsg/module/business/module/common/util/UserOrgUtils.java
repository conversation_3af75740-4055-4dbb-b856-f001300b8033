package cn.com.sipsg.module.business.module.common.util;

import cn.com.sipsg.common.exception.BusinessException;
import cn.com.sipsg.common.security.core.LoginUser;
import cn.com.sipsg.common.security.core.util.SecurityUtils;
import cn.com.sipsg.module.business.module.duty.entity.BasePoliceEmp;
import cn.com.sipsg.module.business.module.duty.entity.BasePoliceOrg;
import cn.com.sipsg.module.business.module.duty.mapper.BasePoliceEmpMapper;
import cn.com.sipsg.module.business.module.duty.mapper.BasePoliceOrgMapper;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 用户组织工具类
 * 提供获取当前用户组织信息的通用方法
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class UserOrgUtils {
    private final BasePoliceEmpMapper basePoliceEmpMapper;
    private final BasePoliceOrgMapper basePoliceOrgMapper;
    private final OrgRecursiveUtils orgRecursiveUtils;

    /**
     * 获取当前用户所在组织ID
     * 从 SecurityUtils 中获取当前登录用户信息，并查询关联的警员信息获取组织ID
     *
     * @return 当前用户所在组织ID/辖区ID
     */
    public String getCurrentUserOrgId() {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser == null) {
            throw new BusinessException("用户未登录");
        }

        String userId = loginUser.getUserId();
        if (StrUtil.isBlank(userId)) {
            throw new BusinessException("用户ID为空");
        }

        // 查询当前用户关联的警员信息
        LambdaQueryWrapper<BasePoliceEmp> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BasePoliceEmp::getAuthUserId, userId)
                .eq(BasePoliceEmp::getAvailable, true);

        BasePoliceEmp policeEmp = basePoliceEmpMapper.selectOne(queryWrapper);
        if (policeEmp == null) {
            throw new BusinessException("未找到关联的警员信息");
        }

        return policeEmp.getPoliceOrgId();
    }

    /**
     * 获取当前用户关联的警员信息
     * 根据当前登录用户ID查询关联的警员信息
     *
     * @return 当前用户关联的警员信息，如果没有关联则返回null
     */
    public BasePoliceEmp getCurrentUserPoliceEmp() {
        try {
            // 安全获取用户ID，添加空值检查
            LoginUser loginUser = SecurityUtils.getLoginUser();
            if (loginUser == null || loginUser.getUserId() == null) {
                log.warn("[获取用户警员信息] 当前用户未登录或用户ID为空");
                return null;
            }

            // 验证用户ID是否为有效数字
            try {
                Long.valueOf(loginUser.getUserId());
            } catch (NumberFormatException e) {
                log.error("[获取用户警员信息] 用户ID格式错误: {}", loginUser.getUserId());
                return null;
            }

            // 构建查询条件
            LambdaQueryWrapper<BasePoliceEmp> empQueryWrapper = new LambdaQueryWrapper<>();
            empQueryWrapper.eq(BasePoliceEmp::getAuthUserId, loginUser.getUserId())
                    .eq(BasePoliceEmp::getAvailable, true);

            return basePoliceEmpMapper.selectOne(empQueryWrapper);
        } catch (Exception e) {
            log.error("[获取用户警员信息] 获取当前用户关联的警员信息异常, e", e);
            return null;
        }
    }

    /**
     * /**
     * 获取当前用户所在组织名称
     * 从 SecurityUtils 中获取当前登录用户信息，并查询关联的警员信息获取组织名称
     *
     * @return 当前用户所在组织名称
     */
    public String getCurrentUserOrgName() {
        try {
            String orgId = getCurrentUserOrgId();
            if (StrUtil.isBlank(orgId)) {
                return null;
            }

            // 查询组织信息
            BasePoliceOrg policeOrg = basePoliceOrgMapper.selectById(orgId);
            if (policeOrg != null) {
                return policeOrg.getName();
            }

            return null;
        } catch (Exception e) {
            log.error("获取当前用户组织名称失败", e);
            return null;
        }
    }

    /**
     * 获取当前用户及下级组织ID列表
     *
     * @return 当前用户及下级组织ID列表
     */
    public List<String> getCurrentUserAndSubOrgIds() {
        String orgId = getCurrentUserOrgId();
        return orgId == null ? java.util.Collections.emptyList() : orgRecursiveUtils.getOrgAndChildrenIds(orgId);
    }

    /**
     * 根据当前用户的数据权限，向QueryWrapper添加组织ID过滤条件。
     * 如果是超级管理员，则不添加任何组织过滤；否则，添加当前用户及其下级组织的ID过滤。
     *
     * @param queryWrapper 要添加过滤条件的QueryWrapper对象
     * @param orgIdColumn  组织ID在数据库表中的列名
     */
    public void applyOrgDataPermission(com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<?> queryWrapper, String orgIdColumn) {
        if (!SecurityUtils.isSuperAdmin()) {
            String currentUserOrgId = getCurrentUserOrgId();
            if (currentUserOrgId != null) {
                List<String> orgIds = orgRecursiveUtils.getOrgAndChildrenIds(currentUserOrgId);
                if (orgIds != null && !orgIds.isEmpty()) {
                    queryWrapper.in(orgIdColumn, orgIds);
                } else {
                    queryWrapper.eq(orgIdColumn, currentUserOrgId);
                }
            }
        }
    }

    /**
     * 根据组织ID集合查询组织名称映射
     * 常用于统计场景中将组织ID转换为组织名称
     *
     * @param orgIds 组织ID集合
     * @return 组织ID到组织名称的映射，key为字符串类型的组织ID，value为组织名称
     */
    public Map<String, String> getOrgNameMapByIds(Set<String> orgIds) {
        if (orgIds == null || orgIds.isEmpty()) {
            return Collections.emptyMap();
        }

        try {
            List<BasePoliceOrg> orgs = basePoliceOrgMapper.selectList(
                    new QueryWrapper<BasePoliceOrg>().in("id", orgIds)
            );
            return orgs.stream()
                    .collect(Collectors.toMap(
                            org -> String.valueOf(org.getId()),
                            BasePoliceOrg::getName
                    ));
        } catch (Exception e) {
            log.error("查询组织名称映射异常，orgIds: {}", orgIds, e);
            return Collections.emptyMap();
        }
    }

    public String getUserNameByUserId(String userId) {
        if (userId == null) {
            return "未知用户";
        }

        try {
            // 查询关联的警员信息
            LambdaQueryWrapper<BasePoliceEmp> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BasePoliceEmp::getAuthUserId, userId)
                    .eq(BasePoliceEmp::getAvailable, true);

            BasePoliceEmp policeEmp = basePoliceEmpMapper.selectOne(queryWrapper);
            if (policeEmp != null && StrUtil.isNotBlank(policeEmp.getName())) {
                return policeEmp.getName();
            }

            // 如果没有找到对应的警员信息，返回默认值
            return "系统用户";
        } catch (Exception e) {
            log.warn("获取用户名称] 获取用户名称异常，userId:" + userId, e);
            return "系统用户";
        }
    }
}

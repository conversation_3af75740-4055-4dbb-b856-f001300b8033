package cn.com.sipsg.module.business.module.supervision.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 安检人员按单位分组统计保安证持证情况VO
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "安检人员按单位分组统计保安证持证情况VO")
public class SecurityEmpCertGroupVO {

    @Schema(description = "安检人员总数")
    private Integer totalCount;

    @Schema(description = "按单位分组统计列表")
    private List<SecurityEmpCertGroupItem> groups;

    /**
     * 安检人员单位分组保安证持证情况项
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "安检人员单位分组保安证持证情况项")
    public static class SecurityEmpCertGroupItem {

        @Schema(description = "单位名称")
        private String orgName;

        @Schema(description = "持有保安证人数")
        private Integer withCertCount;

        @Schema(description = "未持有保安证人数")
        private Integer withoutCertCount;
    }
}

package cn.com.sipsg.module.business.module.supervision.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 综合统计-盘查前科情况相关图片
 */
@Data
@TableName("tbl_statistics_investigation_img")
public class StatisticsInvestigationImg implements Serializable {
    /** 主键id */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /** 创建人id */
    private String createUserId;

    /** 创建时间 */
    private LocalDateTime createTime;

    /** 修改人id */
    private String updateUserId;

    /** 修改时间 */
    private LocalDateTime updateTime;

    /** 是否有效 */
    private Boolean available;

    /** 盘查前科情况主表ID */
    private String investigationId;

    /** 图片链接,最大长度1024 */
    private String imgUrl;
}
package cn.com.sipsg.module.business.module.duty.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> lixt
 * @date : 2025-05-10
 * @Description: 班组管理列表VO
 */
@Data
@Schema(description = "班组管理列表VO")
public class DutyTeamVO {
    @Schema(description = "班组编号", example = "T001")
    private String teamCode;

    @Schema(description = "班组名称", example = "巡逻班组")
    private String teamName;

    @Schema(description = "本班位置", example = "南京南站")
    private String teamLocation;

    @Schema(description = "岗位类别ID", example = "1")
    private String postId;

    @Schema(description = "岗位类别名称", example = "巡逻岗")
    private String postName;

    @Schema(description = "班组人员列表")
    private List<TeamMemberVO> members;

    /**
     * 班组成员信息
     */
    @Data
    @Schema(description = "班组成员信息")
    public static class TeamMemberVO {
        @Schema(description = "人员ID", example = "1001")
        private String empId;

        @Schema(description = "人员姓名", example = "张三")
        private String empName;

        @Schema(description = "警号", example = "001234")
        private String policeNumber;
    }
}

package cn.com.sipsg.module.business.module.duty.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> lixt
 * @date : 2025-05-08 11:16:52
 * @Description: 班组表
 */
@Data
@TableName("tbl_duty_turn_group")
public class DutyTurnGroup {

    @TableId(type = IdType.ASSIGN_ID)
    private String id;  // 班组主键ID

    @TableField("police_org_id")
    private String policeOrgId;  // 单位主键

    @TableField("post_id")
    private Long postId;  // 岗位主键

    @TableField("group_name")
    private String groupName;  // 班组名称

    @TableField("station_area_status")
    private Integer stationAreaStatus;  // 是否站区 1: 是 2: 不是

    @TableField("create_time")
    private LocalDateTime createTime;

    @TableField("update_time")
    private LocalDateTime updateTime;

    @TableField("create_user_id")
    private String createUserId;

    @TableField("update_user_id")
    private String updateUserId;

    @TableField("available")
    private Boolean available;
}
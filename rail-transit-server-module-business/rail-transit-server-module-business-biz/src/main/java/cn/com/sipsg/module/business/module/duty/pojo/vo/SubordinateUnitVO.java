package cn.com.sipsg.module.business.module.duty.pojo.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR> lixt
 * @date : 2025-04-27 16:10:53
 * @Description: 下辖单位 VO
 */
@Data
@Schema(description = "下辖单位VO")
public class SubordinateUnitVO {
    @Schema(description = "下辖单位数量")
    private Long count;
    
    @Schema(description = "机关单位数量")
    private Long jgdwCount;

    @Schema(description = "派出所数量")
    private Long pcsCount;
}

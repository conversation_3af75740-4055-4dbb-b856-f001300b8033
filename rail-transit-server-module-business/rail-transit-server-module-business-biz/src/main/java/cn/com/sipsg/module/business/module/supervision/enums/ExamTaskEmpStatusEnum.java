package cn.com.sipsg.module.business.module.supervision.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 考试任务人员关联状态枚举
 * 用于tbl_exam_task_emp表的status字段
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Getter
@AllArgsConstructor
public enum ExamTaskEmpStatusEnum {
    
    NOT_STARTED("01", "未开始"),
    IN_PROGRESS("02", "进行中"),
    PASSED("03", "合格"),
    FAILED("04", "不合格"),
    EXPIRED("05", "已过期"),
    UNKNOWN("", "未知");
    
    /**
     * 状态码
     */
    private final String code;
    
    /**
     * 状态描述
     */
    private final String desc;
    
    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return 枚举实例
     */
    public static ExamTaskEmpStatusEnum getByCode(String code) {
        if (code == null || code.isEmpty()) {
            return UNKNOWN;
        }
        
        for (ExamTaskEmpStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        
        return UNKNOWN;
    }
    
    /**
     * 根据状态描述获取枚举
     *
     * @param desc 状态描述
     * @return 枚举实例
     */
    public static ExamTaskEmpStatusEnum getByDesc(String desc) {
        if (desc == null || desc.isEmpty()) {
            return UNKNOWN;
        }
        
        for (ExamTaskEmpStatusEnum status : values()) {
            if (status.getDesc().equals(desc)) {
                return status;
            }
        }
        
        return UNKNOWN;
    }
}

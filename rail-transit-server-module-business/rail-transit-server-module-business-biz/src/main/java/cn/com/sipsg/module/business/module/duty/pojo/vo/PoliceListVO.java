package cn.com.sipsg.module.business.module.duty.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.List;

/**
 * <AUTHOR> lixt
 * @date : 2025-04-29 10:58:34
 * @Description: 警员列表VO
 */
@Data
@Schema(description = "警员列表VO")
public class PoliceListVO {
    @Schema(description = "主键ID")
    private String id;

    @Schema(description = "姓名")
    private String name;

    @Schema(description = "类型")
    private int type;

    @Schema(description = "编号")
    private String code;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "工作单位")
    private String workUnit;

    @Schema(description = "职务")
    private String duty;

    @Schema(description = "标签")
    private String label;
    
    @Schema(description = "是否系统用户")
    private Boolean isSystemUser;

    @Schema(description = "标签列表")
    private List<TagVO> labelList;

    @Data
    @Schema(description = "标签对象")
    public static class TagVO {
        @Schema(description = "标签编号")
        private String id;
        @Schema(description = "标签名称")
        private String name;
    }
}

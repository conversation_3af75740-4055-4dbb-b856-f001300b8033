package cn.com.sipsg.module.business.module.duty.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR> lixt
 * @date : 2025-05-15
 * @Description: 排班信息VO
 */
@Data
@Schema(description = "排班信息VO", example = "{\"dutyDate\":\"2025-05-10\",\"dutyPersonList\":[{\"id\":\"1\",\"empId\":10001,\"empName\":\"张三\",\"postId\":1,\"postName\":\"巡逻岗\",\"turnId\":1,\"turnName\":\"白班\",\"dutyDate\":\"2025-05-10\"}]}")
public class DutyScheduleVO {
    @Schema(description = "日期", example = "2025-05-10")
    private LocalDate dutyDate;
    
    @Schema(description = "值班人员列表")
    private List<DutyPersonVO> dutyPersonList;
}

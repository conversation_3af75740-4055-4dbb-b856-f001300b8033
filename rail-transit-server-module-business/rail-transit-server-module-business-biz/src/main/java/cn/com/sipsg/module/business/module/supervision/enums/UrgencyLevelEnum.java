package cn.com.sipsg.module.business.module.supervision.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 紧急程度枚举
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Getter
@AllArgsConstructor
public enum UrgencyLevelEnum {

    /**
     * 紧急
     */
    URGENT("urgent", "紧急"),

    /**
     * 重要
     */
    IMPORTANT("important", "重要"),

    /**
     * 一般
     */
    NORMAL("normal", "一般");

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String description;

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举值
     */
    public static UrgencyLevelEnum getByCode(String code) {
        for (UrgencyLevelEnum urgencyLevel : values()) {
            if (urgencyLevel.getCode().equals(code)) {
                return urgencyLevel;
            }
        }
        return null;
    }

    /**
     * 根据描述获取枚举
     *
     * @param description 描述
     * @return 枚举值
     */
    public static UrgencyLevelEnum getByDescription(String description) {
        for (UrgencyLevelEnum urgencyLevel : values()) {
            if (urgencyLevel.getDescription().equals(description)) {
                return urgencyLevel;
            }
        }
        return null;
    }
}
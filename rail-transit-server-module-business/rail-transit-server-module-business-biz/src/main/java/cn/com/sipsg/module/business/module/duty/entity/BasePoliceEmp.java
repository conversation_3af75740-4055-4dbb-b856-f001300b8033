package cn.com.sipsg.module.business.module.duty.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR> lixt
 * @date : 2025-04-22 15:07:15
 * @Description: 警局人员信息表
 */
@Data
@TableName("tbl_base_police_emp")
public class BasePoliceEmp {

    @TableId(type = IdType.ASSIGN_ID)  // 主键自增
    private String id;  // 主键ID

    @TableField("code")
    private String code;  // 警号，人员编号

    @TableField("name")
    private String name;  // 姓名

    @TableField("idcard")
    private String idcard;  // 身份证号

    @TableField("sex")
    private Integer sex;  // 性别，1男，2女

    @TableField("birthday")
    private LocalDate birthday;  // 出生日期

    @TableField("phone")
    private String phone;  // 手机号码

    @TableField("type")
    private Integer type;  // 人员类型，1民警，2辅警，3安保，4警犬

    @TableField("position")
    private String position;  // 岗位

    @TableField("police_org_id")
    private String policeOrgId;  // 所属组织ID

    @TableField("create_user_id")
    private String createUserId;  // 创建人

    @TableField("create_time")
    private LocalDateTime createTime;  // 创建时间

    @TableField("update_user_id")
    private String updateUserId;  // 更新人

    @TableField("update_time")
    private LocalDateTime updateTime;  // 更新时间

    @TableField("available")
    private Boolean available;  // 是否有效

    @TableField("img_base64")
    private String imgBase64;  // 图片（Base64编码）

    @TableField("img_url")
    private String imgUrl;  // 图片链接

    @TableField("duties")
    private String duties;  // 行政职务

    @TableField("police_rank")
    private String policeRank;  // 警衔

    @TableField("nationality")
    private String nationality;  // 民族

    @TableField("auth_user_id")
    private String authUserId;  // 关联用户id

    @TableField("display")
    private Boolean display;  // 是否显示

    @TableField("sign_url")
    private String signUrl;  // 电子签名图片

    @TableField("rank_level")
    private String rankLevel;  // 职级

    @TableField("status")
    private String status;  // 人员状态（在职、离职、退休）

    @TableField("assist_police_code")
    private String assistPoliceCode;  // 辅警编号

    @TableField("job_type")
    private String jobType;  // 岗位类别

    @TableField("assist_police_job")
    private String assistPoliceJob;  // 工作岗位

    @TableField("assist_prethoracic_no")
    private String assistPrethoracicNo;  // 辅警胸牌号

    @TableField("police_phone")
    private String policePhone;  // 警用手机

    @TableField("emergency_contact_name")
    private String emergencyContactName;  // 紧急联系人姓名

    @TableField("emergency_contact_phone")
    private String emergencyContactPhone;  // 紧急联系人电话

    @TableField("address_province")
    private String addressProvince;  // 住址——省份

    @TableField("address_city")
    private String addressCity;  // 住址——城市

    @TableField("address_area")
    private String addressArea;  // 住址——地区

    @TableField("address_street")
    private String addressStreet;  // 住址——街道

    @TableField("address_detail")
    private String addressDetail;  // 住址——详情

    @TableField("party_date")
    private Long partyDate;  // 入党日期

    @TableField("education")
    private String education;  // 学历

    @TableField("political_status")
    private String politicalStatus;  // 政治面貌

    @TableField("responsible_police_id")
    private String responsiblePoliceId;  // 责任民警警号

    @TableField("in_charge_leader_id")
    private String inChargeLeaderId;  // 分管领导警号

    @TableField("main_leader_id")
    private String mainLeaderId;  // 主要领导警号
}
package cn.com.sipsg.module.business.module.supervision.pojo.dto;

import java.time.LocalDateTime;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "盘查前科情况统计分页查询参数")
public class InvestigationPageReqDTO {

    @Schema(description = "开始时间", example = "2023-01-01 00:00:00")
    private LocalDateTime startTime;

    @Schema(description = "结束时间", example = "2023-01-31 23:59:59")
    private LocalDateTime endTime;

    @Schema(description = "单位ID", example = "1001")
    private String policeOrgId;

    @Schema(description = "被盘查人姓名或身份证号", example = "张三")
    private String personKey;

    @Schema(description = "页码", example = "1")
    private Integer pageNum = 1;

    @Schema(description = "每页大小", example = "10")
    private Integer pageSize = 10;
} 
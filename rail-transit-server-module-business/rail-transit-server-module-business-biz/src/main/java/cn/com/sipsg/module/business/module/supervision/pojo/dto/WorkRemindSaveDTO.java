package cn.com.sipsg.module.business.module.supervision.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 工作提醒保存/更新DTO
 */
@Data
@Schema(description = "工作提醒保存/更新DTO")
public class WorkRemindSaveDTO {

    @Schema(description = "主键，新增时为空，修改时必传", example = "1")
    private String id;

    @Schema(description = "提醒标题", example = "重要会议提醒")
    @NotBlank(message = "提醒标题不能为空")
    private String title;

    @Schema(description = "提醒内容", example = "明天上午9点参加安全工作会议")
    private String content;

    @Schema(description = "提醒时间", example = "2023-07-01 09:00:00")
    @NotBlank(message = "提醒时间不能为空")
    private String remindTime;

    @Schema(description = "所属单位信息列表")
    @NotEmpty(message = "所属单位信息不能为空")
    private List<OrgInfoDTO> orgInfos;

    /**
     * 单位信息DTO
     */
    @Data
    @Schema(description = "单位信息DTO")
    public static class OrgInfoDTO {

        @Schema(description = "单位ID", example = "1001")
        @NotBlank(message = "单位ID不能为空")
        private String orgId;

        @Schema(description = "单位名称", example = "合肥市轨道交通公安分局")
        @NotBlank(message = "单位名称不能为空")
        private String orgName;
    }
}
package cn.com.sipsg.module.business.module.supervision.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 站点值班民警数量视图对象
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Data
@Schema(description = "站点值班民警数量视图对象")
public class StationPoliceCountVO {

    @Schema(description = "站点ID")
    private String stationId;

    @Schema(description = "站点编号")
    private String stationCode;

    @Schema(description = "站点名称")
    private String stationName;

    @Schema(description = "值班民警数量")
    private Integer policeCount;

    @Schema(description = "所属单位ID")
    private String orgId;

    @Schema(description = "所属单位名称")
    private String orgName;

    @Schema(description = "线路ID")
    private String subwayId;

    @Schema(description = "线路名称")
    private String subwayName;
}

package cn.com.sipsg.module.business.module.supervision.service.impl;

import cn.com.sipsg.common.exception.BusinessException;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.security.core.util.SecurityUtils;
import cn.com.sipsg.common.util.ValidationUtils;
import cn.com.sipsg.module.business.module.common.util.OrgRecursiveUtils;
import cn.com.sipsg.module.business.module.common.util.UserOrgUtils;
import cn.com.sipsg.module.business.module.duty.entity.BasePoliceEmp;
import cn.com.sipsg.module.business.module.duty.entity.BasePoliceOrg;
import cn.com.sipsg.module.business.module.duty.entity.BaseStation;
import cn.com.sipsg.module.business.module.duty.entity.DutyEmp;
import cn.com.sipsg.module.business.module.duty.enums.EducationEnum;
import cn.com.sipsg.module.business.module.duty.enums.NationEnum;
import cn.com.sipsg.module.business.module.duty.mapper.BasePoliceEmpMapper;
import cn.com.sipsg.module.business.module.duty.mapper.BasePoliceOrgMapper;
import cn.com.sipsg.module.business.module.duty.mapper.BaseStationMapper;
import cn.com.sipsg.module.business.module.duty.mapper.DutyEmpMapper;
import cn.com.sipsg.module.business.module.supervision.entity.*;
import cn.com.sipsg.module.business.module.supervision.enums.SecurityEmpAgeGroupEnum;
import cn.com.sipsg.module.business.module.supervision.mapper.*;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.SecurityEmpCreateReqDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.SecurityEmpPageReqDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.SecurityStationCreateReqDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.SecurityStationPageReqDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.*;
import cn.com.sipsg.module.business.module.supervision.service.SecuritySupervisionService;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 安检督导服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SecuritySupervisionServiceImpl implements SecuritySupervisionService {

    // 常量定义
    private static final String EMPLOYMENT_STATUS_ON_DUTY = "01";
    private static final int ID_CARD_LENGTH = 18;
    private static final Pattern ID_CARD_PATTERN = Pattern.compile(
            "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$"
    );

    private final SecurityStationMapper securityStationMapper;
    private final SecurityEmpMapper securityEmpMapper;
    private final SecurityCheckInfoMapper securityCheckInfoMapper;
    private final ExamTaskEmpMapper examTaskEmpMapper;
    private final DutyEmpMapper dutyEmpMapper;
    private final UserOrgUtils userOrgUtils;
    private final OrgRecursiveUtils orgRecursiveUtils;
    private final BaseSubwayMapper baseSubwayMapper;
    private final BaseSubwayStationMapper baseSubwayStationMapper;
    private final BasePoliceOrgMapper basePoliceOrgMapper;
    private final BaseStationMapper baseStationMapper;
    private final BasePoliceEmpMapper basePoliceEmpMapper;

    /**
     * 获取当前用户所在组织内当天的安检点数量
     * <p>
     * 此方法首先确定当前用户所属的组织ID，然后根据该组织ID和安检点的可用状态
     * 构建查询条件，并限制为当天的记录，最后查询并返回符合条件的安检点数量
     *
     * @return SecurityStationCountVO 包含安检点数量的信息对象
     */
    @Override
    public SecurityStationCountVO getSecurityStationCount() {
        // 构建查询条件
        LambdaQueryWrapper<SecurityStation> queryWrapper;
        if (SecurityUtils.isSuperAdmin()) {
            queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SecurityStation::getAvailable, true);
        } else {
            queryWrapper = buildSecurityStationQueryWrapper();
        }
        // 限制为当天的记录
        LocalDateTime[] todayRange = getTodayTimeRange();
        queryWrapper.between(SecurityStation::getCreateTime, todayRange[0], todayRange[1]);
        // 查询符合条件的安检点数量
        long count = securityStationMapper.selectCount(queryWrapper);
        // 返回结果
        return new SecurityStationCountVO((int) count);
    }

    /**
     * 获取当前用户所在组织内当天的安检人员数量
     *
     * @return SecurityEmpCountVO 包含安检人员数量的信息对象
     */
    @Override
    public SecurityEmpCountVO getSecurityEmpCount() {
        // 构建查询条件
        LambdaQueryWrapper<SecurityEmp> queryWrapper = buildSecurityEmpCountQueryWrapper();

        // 查询符合条件的安检人员数量
        long count = securityEmpMapper.selectCount(queryWrapper);
        return new SecurityEmpCountVO((int) count);
    }

    /**
     * 获取当前用户所在组织内当天的安检次数
     * <p>
     * 此方法首先确定当前用户所属的组织ID，然后查询该组织下的站点和线路，
     * 根据这些站点和线路ID构建查询条件，并限制为当天的安检记录，
     * 最后查询并返回符合条件的安检次数
     *
     * @return SecurityCheckCountVO 包含安检次数的信息对象
     */
    @Override
    public SecurityCheckCountVO getSecurityCheckCount() {
        // 查询该辖区下的站点和线路
        List<SecurityStation> stations;
        if (SecurityUtils.isSuperAdmin()) {
            LambdaQueryWrapper<SecurityStation> allStationQuery = new LambdaQueryWrapper<>();
            allStationQuery.eq(SecurityStation::getAvailable, true);
            stations = securityStationMapper.selectList(allStationQuery);
        } else {
            LambdaQueryWrapper<SecurityStation> stationQueryWrapper = buildSecurityStationQueryWrapper();
            stations = securityStationMapper.selectList(stationQueryWrapper);
        }
        if (stations.isEmpty()) {
            return new SecurityCheckCountVO(0L);
        }
        // 提取站点ID和线路ID
        List<String> stationIds = stations.stream()
                .map(SecurityStation::getSubwayStationId)
                .filter(Objects::nonNull)
                .map(Object::toString)
                .distinct()
                .collect(Collectors.toList());
        List<String> subwayIds = stations.stream()
                .map(SecurityStation::getSubwayId)
                .filter(Objects::nonNull)
                .map(Object::toString)
                .distinct()
                .collect(Collectors.toList());
        // 构建安检信息查询条件
        LambdaQueryWrapper<SecurityCheckInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SecurityCheckInfo::getAvailable, true);
        // 添加站点ID和线路ID条件
        boolean hasConditions = false;
        if (!stationIds.isEmpty()) {
            queryWrapper.in(SecurityCheckInfo::getStationId, stationIds);
            hasConditions = true;
        }
        if (!subwayIds.isEmpty()) {
            if (hasConditions) {
                queryWrapper.or();
            }
            queryWrapper.in(SecurityCheckInfo::getSubwayId, subwayIds);
        }
        // 限制为当天的记录
        LocalDateTime[] todayRange = getTodayTimeRange();
        queryWrapper.between(SecurityCheckInfo::getCreateTime, todayRange[0], todayRange[1]);
        // 查询符合条件的安检次数
        long count = securityCheckInfoMapper.selectCount(queryWrapper);
        // 返回结果
        return new SecurityCheckCountVO(count);
    }

    /**
     * 根据查询日期获取在职安检员总数，并按单位名称分组统计数量和占比
     *
     * @param queryDate 查询日期，如果为null则使用当前日期
     * @return 安检人员按单位分组统计结果
     */
    @Override
    public SecurityEmpGroupVO getSecurityEmpGroupByOrgName(LocalDate queryDate) {
        // 获取指定日期的在职安检人员
        List<SecurityEmp> securityEms = getSecurityEmsByDate(queryDate);

        // 如果没有数据，返回空结果
        if (securityEms.isEmpty()) {
            return new SecurityEmpGroupVO(0, new ArrayList<>());
        }

        // 按单位名称分组统计（去除名称中的空格等特殊字符）
        Map<String, Long> orgNameCountMap = securityEms.stream()
                .collect(Collectors.groupingBy(
                        emp -> {
                            // 获取单位名称，如果为null则返回空字符串
                            String orgName = emp.getCompanyName();
                            if (orgName == null) {
                                return "";
                            }
                            // 去除所有非字母、非数字、非中文字符
                            return orgName.replaceAll("[^\\u4e00-\\u9fa5a-zA-Z0-9]", "");
                        },
                        Collectors.counting()
                ));

        // 计算总数
        int totalCount = securityEms.size();

        // 构建分组结果列表
        List<SecurityEmpGroupVO.SecurityEmpGroupItem> groupItems = new ArrayList<>();

        // 遍历每个单位，计算数量和占比
        for (Map.Entry<String, Long> entry : orgNameCountMap.entrySet()) {
            String orgName = entry.getKey();
            int count = entry.getValue().intValue();

            // 计算占比（保留2位小数）
            BigDecimal percentage = BigDecimal.valueOf(count)
                    .multiply(BigDecimal.valueOf(100))
                    .divide(BigDecimal.valueOf(totalCount), 2, RoundingMode.HALF_UP);

            // 添加到结果列表
            groupItems.add(new SecurityEmpGroupVO.SecurityEmpGroupItem(orgName, count, percentage));
        }

        // 返回结果
        return new SecurityEmpGroupVO(totalCount, groupItems);
    }

    /**
     * 根据查询日期获取在职安检员总数，并按年龄分组统计数量和占比
     *
     * @param queryDate 查询日期，如果为null则使用当前日期
     * @return 安检人员按年龄分组统计结果
     */
    @Override
    public SecurityEmpAgeGroupVO getSecurityEmpGroupByAge(LocalDate queryDate) {
        // 获取指定日期的在职安检人员
        List<SecurityEmp> securityEms = getSecurityEmsByDate(queryDate);

        // 如果没有数据，返回空结果
        if (securityEms.isEmpty()) {
            return new SecurityEmpAgeGroupVO(0, new ArrayList<>());
        }

        // 按年龄分组统计
        Map<String, Long> ageGroupCountMap = securityEms.stream()
                .collect(Collectors.groupingBy(
                        emp -> SecurityEmpAgeGroupEnum.fromAge(emp.getAge()).getDisplayName(),
                        Collectors.counting()
                ));

        // 计算总数
        int totalCount = securityEms.size();

        // 构建分组结果列表
        List<SecurityEmpAgeGroupVO.SecurityEmpAgeGroupItem> groupItems = new ArrayList<>();

        // 确保所有年龄段都存在，即使没有数据
        for (SecurityEmpAgeGroupEnum group : SecurityEmpAgeGroupEnum.getValidGroups()) {
            if (!ageGroupCountMap.containsKey(group.getDisplayName())) {
                ageGroupCountMap.put(group.getDisplayName(), 0L);
            }
        }

        // 按年龄段顺序添加到结果列表
        for (SecurityEmpAgeGroupEnum group : SecurityEmpAgeGroupEnum.getValidGroups()) {
            String ageRange = group.getDisplayName();
            int count = ageGroupCountMap.getOrDefault(ageRange, 0L).intValue();

            // 计算占比（保畲2位小数）
            BigDecimal percentage = BigDecimal.valueOf(count)
                    .multiply(BigDecimal.valueOf(100))
                    .divide(BigDecimal.valueOf(totalCount), 2, RoundingMode.HALF_UP);

            // 添加到结果列表
            groupItems.add(new SecurityEmpAgeGroupVO.SecurityEmpAgeGroupItem(ageRange, count, percentage));
        }

        // 返回结果
        return new SecurityEmpAgeGroupVO(totalCount, groupItems);
    }

    /**
     * 根据查询日期获取在职安检员总数，并按单位名称分组统计保安证持证情况
     *
     * @param queryDate 查询日期，如果为null则使用当前日期
     * @return 安检人员按单位分组统计保安证持证情况结果
     */
    @Override
    public SecurityEmpCertGroupVO getSecurityEmpCertGroupByOrg(LocalDate queryDate) {
        // 获取指定日期的在职安检人员
        List<SecurityEmp> securityEms = getSecurityEmsByDate(queryDate);

        // 如果没有数据，返回空结果
        if (securityEms.isEmpty()) {
            return new SecurityEmpCertGroupVO(0, new ArrayList<>());
        }

        // 按单位名称分组（去除名称中的空格等特殊字符）
        Map<String, List<SecurityEmp>> orgNameGroupMap = securityEms.stream()
                .collect(Collectors.groupingBy(emp -> {
                    // 获取单位名称，如果为null则返回空字符串
                    String orgName = emp.getCompanyName();
                    if (orgName == null) {
                        return "";
                    }
                    // 去除所有非字母、非数字、非中文字符
                    return orgName.replaceAll("[^\\u4e00-\\u9fa5a-zA-Z0-9]", "");
                }));

        // 计算总数
        int totalCount = securityEms.size();

        // 构建分组结果列表
        List<SecurityEmpCertGroupVO.SecurityEmpCertGroupItem> groupItems = new ArrayList<>();

        // 遍历每个单位，计算持证和未持证人数
        for (Map.Entry<String, List<SecurityEmp>> entry : orgNameGroupMap.entrySet()) {
            String orgName = entry.getKey();
            List<SecurityEmp> empList = entry.getValue();
            int orgTotalCount = empList.size();

            // 计算持有保安证的人数（保安证号非空则认为持证）
            int withCertCount = (int) empList.stream()
                    .filter(emp -> emp.getSafeCardNo() != null && !emp.getSafeCardNo().trim().isEmpty())
                    .count();

            // 计算未持证人数
            int withoutCertCount = orgTotalCount - withCertCount;

            // 添加到结果列表
            groupItems.add(new SecurityEmpCertGroupVO.SecurityEmpCertGroupItem(
                    orgName, withCertCount, withoutCertCount));
        }

        // 返回结果
        return new SecurityEmpCertGroupVO(totalCount, groupItems);
    }

    /**
     * 根据查询日期获取安检员总数，并按单位名称分组统计入职和离职数量
     *
     * @param queryDate 查询日期，如果为null则使用当前日期
     * @return 安检人员按单位分组统计入职离职情况结果
     */
    @Override
    public SecurityEmpStatusGroupVO getSecurityEmpStatusGroupByOrg(LocalDate queryDate) {
        // 如果查询日期为空，则使用当前日期
        LocalDate targetDate = queryDate != null ? queryDate : LocalDate.now();

        // 设置查询日期范围（当天的0点到23:59:59）
        LocalDateTime startOfDay = targetDate.atStartOfDay();
        LocalDateTime endOfDay = targetDate.atTime(LocalTime.MAX);

        // 构建查询条件 - 入职人员（在查询日期当天入职的人员）
        LambdaQueryWrapper<SecurityEmp> entryQueryWrapper = new LambdaQueryWrapper<>();
        entryQueryWrapper.eq(SecurityEmp::getAvailable, true);
        entryQueryWrapper.between(SecurityEmp::getEntryTime, startOfDay, endOfDay);

        // 查询当天入职的安检人员
        List<SecurityEmp> entryEms = securityEmpMapper.selectList(entryQueryWrapper);

        // 构建查询条件 - 离职人员（在查询日期当天离职的人员）
        LambdaQueryWrapper<SecurityEmp> resignationQueryWrapper = new LambdaQueryWrapper<>();
        resignationQueryWrapper.eq(SecurityEmp::getAvailable, true);
        resignationQueryWrapper.between(SecurityEmp::getResignationTime, startOfDay, endOfDay);

        // 查询当天离职的安检人员
        List<SecurityEmp> resignationEms = securityEmpMapper.selectList(resignationQueryWrapper);

        // 如果没有入职和离职数据，返回空结果
        if (entryEms.isEmpty() && resignationEms.isEmpty()) {
            return new SecurityEmpStatusGroupVO(new ArrayList<>());
        }

        // 按单位名称分组入职人员（去除名称中的空格等特殊字符）
        Map<String, Long> orgEntryCountMap = entryEms.stream()
                .collect(Collectors.groupingBy(emp -> {
                    // 获取单位名称，如果为null则返回空字符串
                    String orgName = emp.getCompanyName();
                    if (orgName == null) {
                        return "";
                    }
                    // 去除所有非字母、非数字、非中文字符
                    return orgName.replaceAll("[^\\u4e00-\\u9fa5a-zA-Z0-9]", "");
                }, Collectors.counting()));

        // 按单位名称分组离职人员（去除名称中的空格等特殊字符）
        Map<String, Long> orgResignationCountMap = resignationEms.stream()
                .collect(Collectors.groupingBy(emp -> {
                    // 获取单位名称，如果为null则返回空字符串
                    String orgName = emp.getCompanyName();
                    if (orgName == null) {
                        return "";
                    }
                    // 去除所有非字母、非数字、非中文字符
                    return orgName.replaceAll("[^\\u4e00-\\u9fa5a-zA-Z0-9]", "");
                }, Collectors.counting()));

        // 合并所有单位名称
        Set<String> allOrgNames = new HashSet<>();
        allOrgNames.addAll(orgEntryCountMap.keySet());
        allOrgNames.addAll(orgResignationCountMap.keySet());

        // 构建分组结果列表
        List<SecurityEmpStatusGroupVO.SecurityEmpStatusGroupItem> groupItems = new ArrayList<>();

        // 遍历每个单位，计算入职和离职人数
        for (String orgName : allOrgNames) {
            int entryCount = orgEntryCountMap.getOrDefault(orgName, 0L).intValue();
            int resignationCount = orgResignationCountMap.getOrDefault(orgName, 0L).intValue();

            // 添加到结果列表
            groupItems.add(new SecurityEmpStatusGroupVO.SecurityEmpStatusGroupItem(
                    orgName, entryCount, resignationCount));
        }

        // 返回结果
        return new SecurityEmpStatusGroupVO(groupItems);
    }

    /**
     * 构建安检站点查询条件
     *
     * @return 安检站点查询条件
     */
    private LambdaQueryWrapper<SecurityStation> buildSecurityStationQueryWrapper() {
        // 获取当前用户所在辖区ID（与组织ID相同）
        String precinctId = userOrgUtils.getCurrentUserOrgId();
        LambdaQueryWrapper<SecurityStation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SecurityStation::getAvailable, true);
        // 如果不是超级管理员，才加辖区过滤
        if (!SecurityUtils.isSuperAdmin() && precinctId != null) {
            queryWrapper.eq(SecurityStation::getPrecinctId, precinctId);
        }
        return queryWrapper;
    }

    /**
     * 构建安检人员数量查询条件
     *
     * @return 安检人员查询条件
     */
    private LambdaQueryWrapper<SecurityEmp> buildSecurityEmpCountQueryWrapper() {
        // 获取当前用户所在组织ID
        String orgId = userOrgUtils.getCurrentUserOrgId();

        // 构建查询条件
        LambdaQueryWrapper<SecurityEmp> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SecurityEmp::getAvailable, true);
        queryWrapper.eq(SecurityEmp::getEmploymentStatus, EMPLOYMENT_STATUS_ON_DUTY);

        // 如果不是超级管理员，才做组织过滤
        if (!SecurityUtils.isSuperAdmin() && orgId != null) {
            // 查询该组织下的站点和线路
            LambdaQueryWrapper<SecurityStation> stationQueryWrapper = buildSecurityStationQueryWrapper();
            // 获取该组织下的所有线路 ID
            List<String> subwayIds = securityStationMapper.selectList(stationQueryWrapper).stream()
                    .map(SecurityStation::getSubwayId)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());
            // 如果有线路 ID，按线路 ID 过滤安检人员
            if (!subwayIds.isEmpty()) {
                queryWrapper.in(SecurityEmp::getSubwayId, subwayIds);
            } else {
                // 如果没有找到线路，返回空结果
                return new LambdaQueryWrapper<SecurityEmp>().eq(SecurityEmp::getAvailable, false);
            }
        }

        // 限制为当天的记录
        LocalDateTime[] todayRange = getTodayTimeRange();
        queryWrapper.between(SecurityEmp::getCreateTime, todayRange[0], todayRange[1]);

        return queryWrapper;
    }

    /**
     * 获取当天的时间范围
     *
     * @return [开始时间, 结束时间]
     */
    private LocalDateTime[] getTodayTimeRange() {
        LocalDateTime todayStartTime = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime endOfDay = todayStartTime.plusDays(1).minusNanos(1);
        return new LocalDateTime[]{todayStartTime, endOfDay};
    }

    /**
     * 获取指定日期的在职安检人员
     * <p>
     * 根据查询日期获取在职安检人员，包含在指定日期之前创建且在指定日期之后未离职的安检人员
     *
     * @param queryDate 查询日期，如果为null则使用当前日期
     * @return 安检人员列表
     */
    private List<SecurityEmp> getSecurityEmsByDate(LocalDate queryDate) {
        // 如果查询日期为空，则使用当前日期
        LocalDate targetDate = queryDate != null ? queryDate : LocalDate.now();

        // 构建查询条件
        LambdaQueryWrapper<SecurityEmp> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SecurityEmp::getAvailable, true);

        // 只查询在职人员 (01:在职)
        queryWrapper.eq(SecurityEmp::getEmploymentStatus, EMPLOYMENT_STATUS_ON_DUTY);

        // 设置查询日期范围（当天的0点到23:59:59）
        LocalDateTime startOfDay = targetDate.atStartOfDay();
        LocalDateTime endOfDay = targetDate.atTime(LocalTime.MAX);

        // 查询在指定日期之前创建且在指定日期之后未离职的安检人员
        queryWrapper.le(SecurityEmp::getCreateTime, endOfDay);
        queryWrapper.and(wrapper -> wrapper
                .isNull(SecurityEmp::getResignationTime)
                .or()
                .gt(SecurityEmp::getResignationTime, startOfDay));

        // 查询所有符合条件的安检人员
        return securityEmpMapper.selectList(queryWrapper);
    }

    /**
     * 分页查询安检点位信息
     *
     * @param reqDTO 分页查询参数
     * @return 安检点位分页查询结果
     */
    @Override
    public CommonPageVO<SecurityStationPageVO> getSecurityStationPage(SecurityStationPageReqDTO reqDTO) {
        LambdaQueryWrapper<SecurityStation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SecurityStation::getAvailable, true);
        // 只查当前用户及下级单位，超级管理员不过滤
        boolean isSuperAdmin = SecurityUtils.isSuperAdmin();
        log.info("[安检点分页] isSuperAdmin: {}", isSuperAdmin);
        if (!isSuperAdmin) {
            String currentOrgId = userOrgUtils.getCurrentUserOrgId();
            log.info("[安检点分页] currentOrgId: {}", currentOrgId);
            List<String> orgIds = orgRecursiveUtils.getOrgAndChildrenIdsStr(String.valueOf(currentOrgId));
            log.info("[安检点分页] orgIds: {}", orgIds);
            if (orgIds != null && !orgIds.isEmpty()) {
                queryWrapper.in(SecurityStation::getPrecinctId, orgIds);
            }
        }
        // 动态拼接条件
        if (StrUtil.isNotBlank(reqDTO.getName())) {
            queryWrapper.like(SecurityStation::getName, reqDTO.getName());
        }
        if (StrUtil.isNotBlank(reqDTO.getSubwayId())) {
            queryWrapper.eq(SecurityStation::getSubwayId, reqDTO.getSubwayId());
        }
        if (StrUtil.isNotBlank(reqDTO.getSubwayStationId())) {
            queryWrapper.eq(SecurityStation::getSubwayStationId, reqDTO.getSubwayStationId());
        }
        if (StrUtil.isNotBlank(reqDTO.getCompanyName())) {
            queryWrapper.like(SecurityStation::getCompanyName, reqDTO.getCompanyName());
        }
        if (StrUtil.isNotBlank(reqDTO.getPrecinctId())) {
            queryWrapper.eq(SecurityStation::getPrecinctId, reqDTO.getPrecinctId());
        } else if (!isSuperAdmin) {
            // 未传辖区，且不是管理员，默认查当前用户及下级单位
            String currentOrgId = userOrgUtils.getCurrentUserOrgId();
            List<String> orgIds = orgRecursiveUtils.getOrgAndChildrenIdsStr(currentOrgId);
            if (orgIds != null && !orgIds.isEmpty()) {
                queryWrapper.in(SecurityStation::getPrecinctId, orgIds);
            } else {
                // 没有组织，查不到数据
                queryWrapper.eq(SecurityStation::getPrecinctId, "-1");
            }
        }
        // 输出最终SQL和参数（仅开发环境下有效，需MyBatis-Plus支持）
        try {
            log.info("[安检点分页] queryWrapper SQL: {}", queryWrapper.getCustomSqlSegment());
        } catch (Exception e) {
            log.warn("[安检点分页] 获取SQL片段异常: {}", e.getMessage());
        }
        // 分页参数
        Page<SecurityStation> page = new Page<>(reqDTO.getCurrent(), reqDTO.getSize());
        Page<SecurityStation> resultPage = securityStationMapper.selectPage(page, queryWrapper);
        return buildPageVO(resultPage, this::convertToSecurityStationPageVO);
    }

    /**
     * 将安检点位实体转换为分页VO对象
     *
     * @param entity 安检点位实体
     * @return 安检点位分页VO对象
     */
    private SecurityStationPageVO convertToSecurityStationPageVO(SecurityStation entity) {
        SecurityStationPageVO vo = new SecurityStationPageVO();
        vo.setId(entity.getId());
        vo.setName(entity.getName());

        // 设置线路信息
        vo.setSubwayId(entity.getSubwayId() != null ? entity.getSubwayId() : "");
        vo.setSubwayName(entity.getSubwayName());

        // 设置站点信息
        vo.setSubwayStationId(entity.getSubwayStationId() != null ? entity.getSubwayStationId() : "");
        vo.setSubwayStationName(entity.getSubwayStationName());

        // 设置公司信息
        vo.setCompanyName(entity.getCompanyName());

        // 设置辖区信息
        vo.setPrecinctId(entity.getPrecinctId() != null ? entity.getPrecinctId() : "");
        vo.setPrecinct(entity.getPrecinct());

        // 设置在岗人员名称 - 查询当天的在岗人员姓名，用逗号分隔
        LambdaQueryWrapper<SecurityEmp> empQueryWrapper = new LambdaQueryWrapper<>();
        // 根据线路 ID 和在职状态过滤
        empQueryWrapper.eq(entity.getSubwayId() != null, SecurityEmp::getSubwayId, entity.getSubwayId())
                .eq(SecurityEmp::getEmploymentStatus, EMPLOYMENT_STATUS_ON_DUTY) // 01:在职
                .eq(SecurityEmp::getAvailable, true) // 有效记录
                .ge(SecurityEmp::getCreateTime, LocalDate.now().atStartOfDay()) // 当天创建的记录
                .lt(SecurityEmp::getCreateTime, LocalDate.now().plusDays(1).atStartOfDay());

        // 如果有地铁线路名称，也按线路名称过滤
        if (StrUtil.isNotBlank(entity.getSubwayName())) {
            empQueryWrapper.eq(SecurityEmp::getSubwayName, entity.getSubwayName());
        }

        // 查询在岗人员列表并获取姓名
        List<SecurityEmp> onDutyEmpList = securityEmpMapper.selectList(empQueryWrapper);
        String onDutyPersonnelNames = onDutyEmpList.stream()
                .map(SecurityEmp::getName)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.joining(","));
        vo.setOnDutyPersonnel(onDutyPersonnelNames);

        return vo;
    }

    @Override
    public String createSecurityStation(SecurityStationCreateReqDTO reqDTO) {
        if (StrUtil.isBlank(reqDTO.getId())) {
            // 新增逻辑
            LambdaQueryWrapper<SecurityStation> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SecurityStation::getName, reqDTO.getName())
                    .eq(SecurityStation::getAvailable, true);
            if (StrUtil.isNotBlank(reqDTO.getSubwayStationId())) {
                queryWrapper.eq(SecurityStation::getSubwayStationId, reqDTO.getSubwayStationId());
            }
            if (StrUtil.isNotBlank(reqDTO.getSubwayId())) {
                queryWrapper.eq(SecurityStation::getSubwayId, reqDTO.getSubwayId());
            }
            Long count = securityStationMapper.selectCount(queryWrapper);
            if (count > 0) {
                throw new BusinessException("已存在相同名称的安检点位，不允许重复创建");
            }
            SecurityStation securityStation = new SecurityStation();
            String id = IdUtil.getSnowflake().nextIdStr();
            securityStation.setId(id);
            securityStation.setName(reqDTO.getName());
            securityStation.setSubwayStationId(reqDTO.getSubwayStationId());
            if (StrUtil.isBlank(reqDTO.getSubwayStationName()) && StrUtil.isNotBlank(reqDTO.getSubwayStationId())) {
                String stationName = baseSubwayStationMapper.selectStationNameByStationId(reqDTO.getSubwayStationId());
                securityStation.setSubwayStationName(stationName);
            } else {
                securityStation.setSubwayStationName(reqDTO.getSubwayStationName());
            }
            securityStation.setPrecinctId(reqDTO.getPrecinctId());
            securityStation.setPrecinct(reqDTO.getPrecinct());
            securityStation.setSubwayId(reqDTO.getSubwayId());
            if (StrUtil.isBlank(reqDTO.getSubwayName()) && StrUtil.isNotBlank(reqDTO.getSubwayId())) {
                String subwayName = baseSubwayMapper.selectNameBySubwayId(reqDTO.getSubwayId());
                securityStation.setSubwayName(subwayName);
            } else {
                securityStation.setSubwayName(reqDTO.getSubwayName());
            }
            securityStation.setCompanyId(reqDTO.getCompanyId());
            securityStation.setCompanyName(reqDTO.getCompanyName());
            securityStation.setXRayMachineNum(reqDTO.getXRayMachineNum());
            securityStation.setMetalDetectorNum(reqDTO.getMetalDetectorNum());
            securityStation.setLiquidDetectorNum(reqDTO.getLiquidDetectorNum());
            securityStation.setBombDetectorNum(reqDTO.getBombDetectorNum());
            securityStation.setEmergencyBatonNum(reqDTO.getEmergencyBatonNum());
            securityStation.setShieldNum(reqDTO.getShieldNum());
            securityStation.setExpProofBlanketNum(reqDTO.getExpProofBlanketNum());
            securityStation.setRestraintBlanketNum(reqDTO.getRestraintBlanketNum());
            securityStation.setCatchForkNum(reqDTO.getCatchForkNum());
            securityStation.setExpProofTankNum(reqDTO.getExpProofTankNum());
            securityStation.setStabResistantClothNum(reqDTO.getStabResistantClothNum());
            securityStation.setExpProofHelmetNum(reqDTO.getExpProofHelmetNum());
            securityStation.setSecCheckDoorNum(reqDTO.getSecCheckDoorNum());
            String currentUserId = Objects.requireNonNull(SecurityUtils.getLoginUser()).getUserId();
            LocalDateTime now = LocalDateTime.now();
            securityStation.setCreateUserId(currentUserId);
            securityStation.setCreateTime(now);
            securityStation.setUpdateUserId(currentUserId);
            securityStation.setUpdateTime(now);
            securityStation.setAvailable(true);
            if (securityStation.getPrecinctId() == null) {
                String precinctId = userOrgUtils.getCurrentUserOrgId();
                securityStation.setPrecinctId(precinctId);
            }
            securityStationMapper.insert(securityStation);
            return securityStation.getId();
        } else {
            // 修改逻辑
            SecurityStation securityStation = securityStationMapper.selectById(reqDTO.getId());
            if (securityStation == null || Boolean.FALSE.equals(securityStation.getAvailable())) {
                throw new BusinessException("安检点不存在或已删除");
            }
            // 更新字段
            securityStation.setName(reqDTO.getName());
            securityStation.setSubwayStationId(reqDTO.getSubwayStationId());
            if (StrUtil.isBlank(reqDTO.getSubwayStationName()) && StrUtil.isNotBlank(reqDTO.getSubwayStationId())) {
                String stationName = baseSubwayStationMapper.selectStationNameByStationId(reqDTO.getSubwayStationId());
                securityStation.setSubwayStationName(stationName);
            } else {
                securityStation.setSubwayStationName(reqDTO.getSubwayStationName());
            }
            securityStation.setPrecinctId(reqDTO.getPrecinctId());
            securityStation.setPrecinct(reqDTO.getPrecinct());
            securityStation.setSubwayId(reqDTO.getSubwayId());
            if (StrUtil.isBlank(reqDTO.getSubwayName()) && StrUtil.isNotBlank(reqDTO.getSubwayId())) {
                String subwayName = baseSubwayMapper.selectNameBySubwayId(reqDTO.getSubwayId());
                securityStation.setSubwayName(subwayName);
            } else {
                securityStation.setSubwayName(reqDTO.getSubwayName());
            }
            securityStation.setCompanyId(reqDTO.getCompanyId());
            securityStation.setCompanyName(reqDTO.getCompanyName());
            securityStation.setXRayMachineNum(reqDTO.getXRayMachineNum());
            securityStation.setMetalDetectorNum(reqDTO.getMetalDetectorNum());
            securityStation.setLiquidDetectorNum(reqDTO.getLiquidDetectorNum());
            securityStation.setBombDetectorNum(reqDTO.getBombDetectorNum());
            securityStation.setEmergencyBatonNum(reqDTO.getEmergencyBatonNum());
            securityStation.setShieldNum(reqDTO.getShieldNum());
            securityStation.setExpProofBlanketNum(reqDTO.getExpProofBlanketNum());
            securityStation.setRestraintBlanketNum(reqDTO.getRestraintBlanketNum());
            securityStation.setCatchForkNum(reqDTO.getCatchForkNum());
            securityStation.setExpProofTankNum(reqDTO.getExpProofTankNum());
            securityStation.setStabResistantClothNum(reqDTO.getStabResistantClothNum());
            securityStation.setExpProofHelmetNum(reqDTO.getExpProofHelmetNum());
            securityStation.setSecCheckDoorNum(reqDTO.getSecCheckDoorNum());
            String currentUserId = Objects.requireNonNull(SecurityUtils.getLoginUser()).getUserId();
            securityStation.setUpdateUserId(currentUserId);
            securityStation.setUpdateTime(LocalDateTime.now());
            securityStationMapper.updateById(securityStation);
            return securityStation.getId();
        }
    }

    /**
     * 分页查询安检员信息
     *
     * @param reqDTO 分页查询参数
     * @return 安检员分页查询结果
     */
    @Override
    public CommonPageVO<SecurityEmpPageVO> getSecurityEmpPage(SecurityEmpPageReqDTO reqDTO) {
        // 构建基础查询条件
        LambdaQueryWrapper<SecurityEmp> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SecurityEmp::getAvailable, true);

        // 添加查询条件
        if (StrUtil.isNotBlank(reqDTO.getKeyword())) {
            queryWrapper.and(w -> w.like(SecurityEmp::getName, reqDTO.getKeyword())
                    .or().like(SecurityEmp::getIdCardNo, reqDTO.getKeyword())
                    .or().like(SecurityEmp::getPhone, reqDTO.getKeyword()));
        }
        if (StrUtil.isNotBlank(reqDTO.getCompanyName())) {
            queryWrapper.like(SecurityEmp::getCompanyName, reqDTO.getCompanyName());
        }
        if (StrUtil.isNotBlank(reqDTO.getEmploymentStatus())) {
            queryWrapper.eq(SecurityEmp::getEmploymentStatus, reqDTO.getEmploymentStatus());
        }
        if (reqDTO.getHasTrain() != null) {
            queryWrapper.eq(SecurityEmp::getHasTrain, reqDTO.getHasTrain());
        }
        if (StrUtil.isNotBlank(reqDTO.getCreateUserName())) {
            // 先查警员表，找到姓名匹配的警员编号
            List<BasePoliceEmp> emps = basePoliceEmpMapper.selectList(
                    new LambdaQueryWrapper<BasePoliceEmp>()
                            .like(BasePoliceEmp::getName, reqDTO.getCreateUserName())
                            .eq(BasePoliceEmp::getAvailable, true)
            );
            if (!emps.isEmpty()) {
                List<String> userIds = emps.stream().map(BasePoliceEmp::getId).collect(Collectors.toList());
                queryWrapper.in(SecurityEmp::getCreateUserId, userIds);
            } else {
                // 没有匹配的警员，返回空结果
                queryWrapper.in(SecurityEmp::getCreateUserId, "-1");
            }
        }
        if (StrUtil.isNotBlank(reqDTO.getCreateStartTime())) {
            String start = reqDTO.getCreateStartTime();
            if (start.length() == 10) start += " 00:00:00";
            queryWrapper.ge(SecurityEmp::getCreateTime, start);
        }
        if (StrUtil.isNotBlank(reqDTO.getCreateEndTime())) {
            String end = reqDTO.getCreateEndTime();
            if (end.length() == 10) end += " 23:59:59";
            queryWrapper.le(SecurityEmp::getCreateTime, end);
        }

        // 执行分页查询
        Page<SecurityEmp> page = new Page<>(reqDTO.getCurrent(), reqDTO.getSize());
        Page<SecurityEmp> resultPage = securityEmpMapper.selectPage(page, queryWrapper);

        // 批量查创建人姓名
        List<String> userIdList = resultPage.getRecords().stream()
                .map(SecurityEmp::getCreateUserId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        final Map<String, String> userIdNameMap;
        if (!userIdList.isEmpty()) {
            List<BasePoliceEmp> userList = basePoliceEmpMapper.selectList(
                    new LambdaQueryWrapper<BasePoliceEmp>().in(BasePoliceEmp::getId, userIdList)
            );
            userIdNameMap = userList.stream().collect(Collectors.toMap(BasePoliceEmp::getId, BasePoliceEmp::getName));
        } else {
            userIdNameMap = Collections.emptyMap();
        }

        return buildPageVO(resultPage, emp -> {
            SecurityEmpPageVO vo = convertToSecurityEmpPageVO(emp);
            // createBy赋值为姓名
            vo.setCreateBy(userIdNameMap.getOrDefault(emp.getCreateUserId(), ""));
            return vo;
        });
    }

    /**
     * 通用方法：将SecurityEmp的基础属性赋值到VO（使用BeanUtils自动复制）
     */
    private void copySecurityEmpBaseFields(SecurityEmp emp, Object vo) {
        BeanUtils.copyProperties(emp, vo);
    }

    /**
     * 将安检员实体转换为分页VO
     */
    private SecurityEmpPageVO convertToSecurityEmpPageVO(SecurityEmp entity) {
        SecurityEmpPageVO vo = new SecurityEmpPageVO();
        copySecurityEmpBaseFields(entity, vo);
        vo.setHasTrain(entity.getHasTrain());
        vo.setEmploymentStatus(entity.getEmploymentStatus());
        vo.setSecurityCardNo(entity.getSecurityCardNo());
        vo.setSafeCardNo(entity.getSafeCardNo());
        vo.setCreateTime(entity.getCreateTime());
        // createBy赋值在外层处理
        vo.setCompanyName(entity.getCompanyName());
        return vo;
    }

    @Override
    public SecurityEmpProfileVO getSecurityEmpProfile(String id) {
        // 查询安检员基础信息
        SecurityEmp emp = securityEmpMapper.selectById(id);
        if (emp == null) {
            throw new BusinessException("安检员不存在");
        }
        SecurityEmpProfileVO vo = new SecurityEmpProfileVO();
        copySecurityEmpBaseFields(emp, vo);
        vo.setCompanyName(emp.getCompanyName());
        vo.setNation(emp.getNation());
        vo.setEducation(emp.getEducation());
        vo.setPermanentAddress(emp.getPermanentAddress());
        vo.setResidenceAddress(emp.getResidenceAddress());
        vo.setEntryTime(emp.getEntryTime());
        vo.setResignationTime(emp.getResignationTime());
        vo.setJobNumber(emp.getJobNumber());
        vo.setJob(emp.getJob());
        vo.setEmploymentStatus(emp.getEmploymentStatus());
        vo.setSubwayName(emp.getSubwayName());
        vo.setSecurityCardNo(emp.getSecurityCardNo());
        vo.setSafeCardNo(emp.getSafeCardNo());
        vo.setImgUrl(emp.getImgUrl());
        vo.setCreateTime(emp.getCreateTime());
        vo.setUpdateTime(emp.getUpdateTime());
        vo.setCreateUserId(emp.getCreateUserId());
        vo.setUpdateUserId(emp.getUpdateUserId());
        // 统计考试记录数
        LambdaQueryWrapper<ExamTaskEmp> examWrapper = new LambdaQueryWrapper<>();
        examWrapper.eq(ExamTaskEmp::getEmpId, String.valueOf(id)).eq(ExamTaskEmp::getAvailable, true);
        int examCount = examTaskEmpMapper.selectCount(examWrapper).intValue();
        vo.setExamCount(examCount);
        // 统计近30天总班次
        LambdaQueryWrapper<DutyEmp> dutyWrapper = new LambdaQueryWrapper<>();
        dutyWrapper.eq(DutyEmp::getEmpId, id)
                .eq(DutyEmp::getAvailable, true)
                .ge(DutyEmp::getDutyDate, LocalDate.now().minusDays(29))
                .le(DutyEmp::getDutyDate, LocalDate.now());
        int recentTotalShifts = dutyEmpMapper.selectCount(dutyWrapper).intValue();
        vo.setRecentTotalShifts(recentTotalShifts);
        // 培训相关字段保留为0
        vo.setTrainingCount(0);
        vo.setTrainingTimes(0);
        // 其他字段保留原样
        vo.setOrgFullName("");
        vo.setIsConcerned("");
        vo.setCollector("");
        vo.setCollectTime(null);
        vo.setCreateUserName("");
        vo.setHasTrain(emp.getHasTrain());
        // 查询创建人和更新人姓名
        if (emp.getCreateUserId() != null) {
            BasePoliceEmp createUser = basePoliceEmpMapper.selectById(emp.getCreateUserId());
            if (createUser != null) {
                vo.setCreateUserName(createUser.getName());
            }
        }
        if (emp.getUpdateUserId() != null) {
            BasePoliceEmp updateUser = basePoliceEmpMapper.selectById(emp.getUpdateUserId());
            if (updateUser != null) {
                vo.setUpdateUserName(updateUser.getName());
            }
        }
        return vo;
    }

    /**
     * 获取安全管理汇总信息
     * 该方法通过调用多个统计方法来收集安全管理相关的数据，并将其汇总到一个对象中返回
     * 主要包括保安站点数量、人员数量、检查次数和出勤率
     *
     * @return SecuritySupervisionSummaryVO 包含安全管理汇总信息的对象
     */
    @Override
    public SecuritySupervisionSummaryVO getSecuritySupervisionSummary() {
        // 复用原有统计方法
        SecurityStationCountVO stationVO = getSecurityStationCount();
        SecurityEmpCountVO empVO = getSecurityEmpCount();
        SecurityCheckCountVO checkVO = getSecurityCheckCount();
        // 出勤率暂用100%，后续可补真实逻辑
        String attendanceRate = "100%";
        // 创建汇总信息对象并设置统计数据
        SecuritySupervisionSummaryVO vo = new SecuritySupervisionSummaryVO();
        vo.setStationCount(stationVO != null ? stationVO.getCount() : 0);
        vo.setPersonnelCount(empVO != null ? empVO.getCount() : 0);
        vo.setCheckCount(checkVO != null ? (checkVO.getCount() == null ? 0 : checkVO.getCount().intValue()) : 0);
        vo.setAttendanceRate(attendanceRate);
        return vo;
    }

    /**
     * 通用分页VO组装方法
     */
    private <T, V> CommonPageVO<V> buildPageVO(Page<T> resultPage, java.util.function.Function<T, V> converter) {
        List<V> voList = resultPage.getRecords().stream()
                .map(converter)
                .collect(Collectors.toList());
        CommonPageVO<V> pageResult = new CommonPageVO<>();
        pageResult.setCurrent(resultPage.getCurrent());
        pageResult.setSize(resultPage.getSize());
        pageResult.setTotal(resultPage.getTotal());
        pageResult.setRecords(voList);
        return pageResult;
    }

    /**
     * 获取所有地铁线路及其对应的站点信息
     * <p>
     * 此方法首先从数据库中查询所有可用的地铁线路和站点信息，
     * 然后将站点信息根据线路代码进行分组，
     * 最后将线路和对应的站点信息封装成SubwayLineSimpleVO对象列表并返回
     *
     * @return 包含所有地铁线路及其站点信息的列表
     */
    @Override
    public List<SubwayLineSimpleVO> getAllLinesAndStations() {
        // 查询所有可用的地铁线路
        List<BaseSubway> lines = baseSubwayMapper.selectList(new QueryWrapper<BaseSubway>().eq("available", true));
        // 查询所有可用的地铁站点关系
        List<BaseSubwayStation> relations = baseSubwayStationMapper.selectList(new QueryWrapper<BaseSubwayStation>().eq("available", true));
        // 查询所有可用的地铁站点
        List<BaseStation> stations = baseStationMapper.selectList(new QueryWrapper<BaseStation>().eq("available", true));
        Map<String, BaseStation> stationMap = stations.stream().collect(Collectors.toMap(BaseStation::getId, s -> s));
        // 组装
        return lines.stream().map(line -> {
            SubwayLineSimpleVO vo = new SubwayLineSimpleVO();
            vo.setLineCode(line.getCode());
            vo.setLineName(line.getName());
            List<SubwayStationSimpleVO> stationVOs = relations.stream()
                    .filter(rel -> line.getId().toString().equals(rel.getSubwayId()))
                    .sorted(Comparator.comparing(BaseSubwayStation::getOrderNo, Comparator.nullsLast(Integer::compareTo)))
                    .map(rel -> {
                        SubwayStationSimpleVO s = new SubwayStationSimpleVO();
                        s.setStationCode(rel.getStationCode());
                        BaseStation baseStation = stationMap.get(rel.getStationId());
                        s.setStationName(baseStation != null ? baseStation.getName() : rel.getStationName());
                        return s;
                    }).collect(Collectors.toList());
            vo.setStations(stationVOs);
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 获取所有安检站的公司信息
     * <p>
     * 该方法从数据库中查询所有有效安检点位的公司id和名称，并去重后返回
     * 主要涉及对security_station表的查询操作，筛选出可用的安检点位的公司信息
     *
     * @return List<CompanyOptionVO> 包含公司id和名称的列表，用于展示可选的公司信息
     */
    @Override
    public List<CompanyOptionVO> getAllSecurityStationCompanies() {
        // 直接用SQL去重，提升性能
        return securityStationMapper.selectDistinctCompanyOptions();
    }

    /**
     * 获取当前用户可访问的机构选项列表
     * <p>
     * 对于不同权限的用户，返回的机构列表有所不同：
     * - 如果是超级管理员，返回系统中所有可用的机构
     * - 对于普通用户，仅返回其所在机构及其下级机构
     *
     * @return 机构选项列表，每个选项包含机构ID和名称
     */
    @Override
    public List<OrgOptionVO> getOrgOptionsForCurrentUser() {
        List<OrgOptionVO> result = new ArrayList<>();
        if (SecurityUtils.isSuperAdmin()) {
            // 管理员查全部
            List<BasePoliceOrg> allOrg = basePoliceOrgMapper.selectList(new LambdaQueryWrapper<BasePoliceOrg>().eq(BasePoliceOrg::getAvailable, true));
            for (BasePoliceOrg org : allOrg) {
                result.add(new OrgOptionVO(String.valueOf(org.getId()), org.getName()));
            }
        } else {
            // 当前用户及下级单位
            String currentOrgId = userOrgUtils.getCurrentUserOrgId();
            List<String> orgIds = orgRecursiveUtils.getOrgAndChildrenIdsStr(currentOrgId);
            if (orgIds != null && !orgIds.isEmpty()) {
                List<BasePoliceOrg> orgList = basePoliceOrgMapper.selectList(
                        new LambdaQueryWrapper<BasePoliceOrg>()
                                .eq(BasePoliceOrg::getAvailable, true)
                                .in(BasePoliceOrg::getId, orgIds)
                );
                for (BasePoliceOrg org : orgList) {
                    result.add(new OrgOptionVO(String.valueOf(org.getId()), org.getName()));
                }
            }
        }
        return result;
    }

    @Override
    public Boolean deleteSecurityStation(String id) {
        SecurityStation station = securityStationMapper.selectById(id);
        if (station == null) {
            return false;
        }
        station.setAvailable(false);
        int updated = securityStationMapper.updateById(station);
        return updated > 0;
    }

    @Override
    public SecurityStationDetailVO getSecurityStationDetail(String id) {
        SecurityStation entity = securityStationMapper.selectById(id);
        if (entity == null || Boolean.FALSE.equals(entity.getAvailable())) {
            throw new BusinessException("安检点不存在或已删除");
        }
        SecurityStationDetailVO vo = new SecurityStationDetailVO();
        org.springframework.beans.BeanUtils.copyProperties(entity, vo);
        return vo;
    }

    @Override
    public String createSecurityEmp(SecurityEmpCreateReqDTO reqDTO) {
        String imageUrl = reqDTO.getImageUrl();
        BasePoliceEmp currentPoliceEmp = userOrgUtils.getCurrentUserPoliceEmp();
        if (currentPoliceEmp == null || currentPoliceEmp.getId() == null) {
            throw new BusinessException("当前用户未关联警员编号，无法进行操作");
        }
        String currentUserId = String.valueOf(currentPoliceEmp.getId());
        if (reqDTO.getId() == null || reqDTO.getId().isEmpty()) {
            // 新增逻辑
            return saveSecurityEmpToDb(reqDTO, imageUrl, currentUserId);
        } else {
            // 修改逻辑
            SecurityEmp securityEmp = securityEmpMapper.selectById(reqDTO.getId());
            if (securityEmp == null || Boolean.FALSE.equals(securityEmp.getAvailable())) {
                throw new BusinessException("安检员不存在或已删除");
            }
            // 更新基本信息
            securityEmp.setName(reqDTO.getName());
            securityEmp.setIdCardNo(reqDTO.getIdCard());
            securityEmp.setPhone(reqDTO.getContact());
            securityEmp.setGender(reqDTO.getGender());
            // 校验民族，只允许并存储code
            if (!NationEnum.isValid(reqDTO.getNation())) {
                throw new BusinessException("民族不合法");
            }
            securityEmp.setNation(reqDTO.getNation());
            securityEmp.setPermanentAddress(reqDTO.getHouseholdAddress());
            securityEmp.setResidenceAddress(reqDTO.getCurrentAddress());
            securityEmp.setImgUrl(imageUrl);
            // 更新证号
            securityEmp.setSafeCardNo(reqDTO.getSecurityGuardCertNo());
            securityEmp.setSecurityCardNo(reqDTO.getSecurityCertNo());
            // 更新公司信息和是否培训
            if (reqDTO.getCompanyName() != null) {
                securityEmp.setCompanyName(reqDTO.getCompanyName());
            }
            if (reqDTO.getHasTrain() != null) {
                securityEmp.setHasTrain(reqDTO.getHasTrain());
            }
            // 更新在职状态
            if (reqDTO.getJobStatus() != null) {
                securityEmp.setEmploymentStatus(reqDTO.getJobStatus());
            }
            // 更新入职和离职时间
            if (reqDTO.getEntryDate() != null) {
                securityEmp.setEntryTime(reqDTO.getEntryDate().atStartOfDay());
            }
            if (reqDTO.getLeaveDate() != null) {
                securityEmp.setResignationTime(reqDTO.getLeaveDate().atStartOfDay());
            }
            // 更新学历
            if (reqDTO.getEducation() != null) {
                securityEmp.setEducation(reqDTO.getEducation());
            }
            // 更新联系方式
            if (reqDTO.getContact() != null) {
                securityEmp.setPhone(reqDTO.getContact());
            }
            // 更新是否入职培训
            if (reqDTO.getHasTrain() != null) {
                securityEmp.setHasTrain(reqDTO.getHasTrain());
            } 
            // 更新有无安检证
            if (reqDTO.getHasSecurityCert() != null) {
                securityEmp.setSecurityCardNo(reqDTO.getSecurityCertNo());
            }
            // 更新有无安检证
            if (reqDTO.getHasSecurityGuardCert() != null) {
                securityEmp.setSafeCardNo(reqDTO.getSecurityGuardCertNo());
            }
            // 更新审计字段
            securityEmp.setUpdateUserId(currentUserId);
            securityEmp.setUpdateTime(LocalDateTime.now());
            securityEmpMapper.updateById(securityEmp);
            return securityEmp.getId();
        }
    }

    /**
     * 保存安检员信息到数据库
     *
     * @param reqDTO   安检员创建请求DTO
     * @param imageUrl 头像图片URL
     * @return 安检员ID
     */
    private String saveSecurityEmpToDb(SecurityEmpCreateReqDTO reqDTO, String imageUrl, String currentUserId) {
        // 1. 数据校验
        validateSecurityEmpData(reqDTO);

        // 2. 检查身份证号是否已存在
        checkIdCardExists(reqDTO.getIdCard());

        // 3. 创建安检员实体
        SecurityEmp securityEmp = new SecurityEmp();

        // 4. 不再手动生成ID，ASSIGN_ID自动生成
        // 5. 设置基本信息
        securityEmp.setName(reqDTO.getName());
        securityEmp.setIdCardNo(reqDTO.getIdCard());
        securityEmp.setPhone(reqDTO.getContact());
        securityEmp.setGender(reqDTO.getGender());
        // 校验民族，只允许并存储code
        if (!NationEnum.isValid(reqDTO.getNation())) {
            throw new BusinessException("民族不合法");
        }
        securityEmp.setNation(reqDTO.getNation());
        securityEmp.setPermanentAddress(reqDTO.getHouseholdAddress());
        securityEmp.setResidenceAddress(reqDTO.getCurrentAddress());
        securityEmp.setImgUrl(imageUrl);
        // 新增证号字段赋值
        securityEmp.setSafeCardNo(reqDTO.getSecurityGuardCertNo());
        securityEmp.setSecurityCardNo(reqDTO.getSecurityCertNo());

        // 6. 计算年龄（从身份证号提取出生日期）
        Integer age = calculateAgeFromIdCard(reqDTO.getIdCard());
        securityEmp.setAge(age);

        // 7. 设置公司信息和是否培训
        if (reqDTO.getCompanyId() != null) {
            securityEmp.setCompanyId(reqDTO.getCompanyId());
        }
        if (reqDTO.getCompanyName() != null) {
            securityEmp.setCompanyName(reqDTO.getCompanyName());
        }
        if (reqDTO.getHasTrain() != null) {
            securityEmp.setHasTrain(reqDTO.getHasTrain());
        }

        // 8. 设置在职状态
        String employmentStatus = reqDTO.getJobStatus();
        if (StrUtil.isBlank(employmentStatus)) {
            employmentStatus = EMPLOYMENT_STATUS_ON_DUTY; // 默认在职
        }
        securityEmp.setEmploymentStatus(employmentStatus);

        // 9. 设置入职和离职时间
        if (reqDTO.getEntryDate() != null) {
            securityEmp.setEntryTime(reqDTO.getEntryDate().atStartOfDay());
        }
        if (reqDTO.getLeaveDate() != null) {
            securityEmp.setResignationTime(reqDTO.getLeaveDate().atStartOfDay());
        }

        // 10. 设置证书信息
        // 有无安检证
        if (reqDTO.getHasSecurityCert() != null) {
            securityEmp.setSecurityCardNo(reqDTO.getSecurityCertNo());
        }
        // 有无安检证
        if (reqDTO.getHasSecurityGuardCert() != null) {
            securityEmp.setSafeCardNo(reqDTO.getSecurityGuardCertNo());
        }

        // 11. 设置审计字段
        LocalDateTime now = LocalDateTime.now();
        securityEmp.setCreateUserId(currentUserId);
        securityEmp.setCreateTime(now);
        securityEmp.setUpdateUserId(currentUserId);
        securityEmp.setUpdateTime(now);
        securityEmp.setAvailable(true);

        // 12. 保存到数据库
        securityEmpMapper.insert(securityEmp);

        // 返回自动生成的ID
        return securityEmp.getId();
    }

    /**
     * 校验安检员数据
     *
     * @param reqDTO 安检员创建请求DTO
     */
    private void validateSecurityEmpData(SecurityEmpCreateReqDTO reqDTO) {
        if (StrUtil.isBlank(reqDTO.getName())) {
            throw new BusinessException("姓名不能为空");
        }
        if (StrUtil.isBlank(reqDTO.getIdCard())) {
            throw new BusinessException("身份证号不能为空");
        }
        if (StrUtil.isBlank(reqDTO.getContact())) {
            throw new BusinessException("联系方式不能为空");
        }
        if (StrUtil.isBlank(reqDTO.getGender())) {
            throw new BusinessException("性别不能为空");
        }

        // 校验身份证号格式
        if (!isValidIdCard(reqDTO.getIdCard())) {
            throw new BusinessException("身份证号格式不正确");
        }

        // 校验手机号格式
        if (!ValidationUtils.isMobile(reqDTO.getContact())) {
            throw new BusinessException("手机号格式不正确");
        }
        // 校验学历
        if (!EducationEnum.isValid(reqDTO.getEducation())) {
            throw new BusinessException("学历不合法");
        }
        // 校验安检证和安保证号
        if (Boolean.TRUE.equals(reqDTO.getHasSecurityCert()) && (reqDTO.getSecurityCertNo() == null || reqDTO.getSecurityCertNo().trim().isEmpty())) {
            throw new BusinessException("有安检证时必须填写安检证号");
        }
        if (Boolean.TRUE.equals(reqDTO.getHasSecurityGuardCert()) && (reqDTO.getSecurityGuardCertNo() == null || reqDTO.getSecurityGuardCertNo().trim().isEmpty())) {
            throw new BusinessException("有安保证时必须填写保安证号");
        }
    }

    /**
     * 检查身份证号是否已存在
     *
     * @param idCard 身份证号
     */
    private void checkIdCardExists(String idCard) {
        LambdaQueryWrapper<SecurityEmp> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SecurityEmp::getIdCardNo, idCard)
                .eq(SecurityEmp::getAvailable, true);

        Long count = securityEmpMapper.selectCount(queryWrapper);
        if (count > 0) {
            throw new BusinessException("该身份证号已存在，不能重复添加");
        }
    }

    /**
     * 校验身份证号格式
     *
     * @param idCard 身份证号
     * @return 是否有效
     */
    private boolean isValidIdCard(String idCard) {
        if (StrUtil.isBlank(idCard)) {
            return false;
        }

        // 身份证号长度校验
        if (idCard.length() != ID_CARD_LENGTH) {
            return false;
        }

        // 身份证号格式校验（使用预编译的正则表达式）
        return ID_CARD_PATTERN.matcher(idCard).matches();
    }

    /**
     * 从身份证号计算年龄
     *
     * @param idCard 身份证号
     * @return 年龄
     */
    private Integer calculateAgeFromIdCard(String idCard) {
        if (StrUtil.isBlank(idCard) || idCard.length() != ID_CARD_LENGTH) {
            return null;
        }

        try {
            // 提取出生日期（身份证号第7-14位）
            String birthDateStr = idCard.substring(6, 14);
            int year = Integer.parseInt(birthDateStr.substring(0, 4));
            int month = Integer.parseInt(birthDateStr.substring(4, 6));
            int day = Integer.parseInt(birthDateStr.substring(6, 8));

            // 计算年龄
            LocalDate birthDate = LocalDate.of(year, month, day);
            LocalDate currentDate = LocalDate.now();

            int age = currentDate.getYear() - birthDate.getYear();

            // 如果今年的生日还没到，年龄减1
            if (currentDate.getMonthValue() < birthDate.getMonthValue() ||
                    (currentDate.getMonthValue() == birthDate.getMonthValue() &&
                            currentDate.getDayOfMonth() < birthDate.getDayOfMonth())) {
                age--;
            }

            return age;
        } catch (Exception e) {
            log.warn("从身份证号计算年龄失败: {}", idCard, e);
            return null;
        }
    }

    @Override
    public Boolean deleteSecurityEmp(String id) {
        SecurityEmp emp = securityEmpMapper.selectById(id);
        if (emp == null || Boolean.FALSE.equals(emp.getAvailable())) {
            return false;
        }
        emp.setAvailable(false);
        securityEmpMapper.updateById(emp);
        return true;
    }
}
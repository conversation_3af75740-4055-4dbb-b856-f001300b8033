package cn.com.sipsg.module.business.module.duty.service.impl;


import cn.com.sipsg.common.exception.BusinessException;
import cn.com.sipsg.common.mybatis.core.query.MPJLambdaWrapperX;
import cn.com.sipsg.common.security.core.util.SecurityUtils;
import cn.com.sipsg.module.business.module.common.util.OrgRecursiveUtils;
import cn.com.sipsg.module.business.module.common.util.UserOrgUtils;
import cn.com.sipsg.module.business.module.duty.entity.*;
import cn.com.sipsg.module.business.module.duty.enums.EmpTypeEnum;
import cn.com.sipsg.module.business.module.duty.mapper.*;
import cn.com.sipsg.module.business.module.duty.pojo.dto.DutyScheduleEditDTO;
import cn.com.sipsg.module.business.module.duty.pojo.dto.DutyScheduleQueryDTO;
import cn.com.sipsg.module.business.module.duty.pojo.dto.DutyTeamEditDTO;
import cn.com.sipsg.module.business.module.duty.pojo.dto.DutyTurnEditDTO;
import cn.com.sipsg.module.business.module.duty.pojo.dto.PostCategoryAddDTO;
import cn.com.sipsg.module.business.module.duty.pojo.vo.*;
import cn.com.sipsg.module.business.module.duty.service.DutyManageService;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.StationPoliceCountVO;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> lixt
 * @date : 2025-05-07 16:34:31
 * @Description: 值班管理相关接口实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DutyManageServiceImpl extends ServiceImpl<DutyEmpMapper, DutyEmp> implements DutyManageService {
    private final DutyEmpMapper dutyEmpMapper;
    private final DutyTurnMapper dutyTurnMapper;
    private final DutyPostMapper dutyPostMapper;
    private final DutyTurnGroupMapper dutyTurnGroupMapper;
    private final BasePoliceOrgMapper basePoliceOrgMapper;
    private final BaseStationMapper baseStationMapper;
    private final BasePoliceEmpMapper basePoliceEmpMapper;
    private final UserOrgUtils userOrgUtils;
    private final OrgRecursiveUtils orgRecursiveUtils;

    /**
     * 查询当天值班人员动态信息
     * <p>
     * 本方法通过查询数据库中当天的排班信息，统计每个组织的值班人数，
     * 包括电脑值班人数和移动值班人数，并返回统计结果
     * 如果是管理员，则查看全部数据；如果不是管理员，则只查看当前用户所在组织的数据
     *
     * @return 返回一个包含各组织值班信息的列表
     */
    @Override
    public List<OnDutyEmpVO> onDutyDynamic() {
        LocalDate today = LocalDate.now();
        MPJLambdaWrapperX<DutyEmp> wrapper = new MPJLambdaWrapperX<>();
        wrapper
                .select("t.police_org_id")
                .select("org.name as police_org_name")
                .select("COUNT(*) as onDutyCount")
                .leftJoin(BasePoliceOrg.class, "org", BasePoliceOrg::getId, DutyEmp::getPoliceOrgId)
                .eq("t.duty_date", today)
                .eq("t.available", true);

        if (!SecurityUtils.isSuperAdmin()) {
            String currentUserOrgId = userOrgUtils.getCurrentUserOrgId();
            if (currentUserOrgId != null) {
                // 获取本单位及下级单位ID集合
                List<String> orgIds = orgRecursiveUtils.getOrgAndChildrenIds(currentUserOrgId);
                if (orgIds != null && !orgIds.isEmpty()) {
                    wrapper.in("t.police_org_id", orgIds);
                } else {
                    wrapper.eq("t.police_org_id", currentUserOrgId);
                }
            }
        }

        wrapper.groupBy("t.police_org_id", "org.name");
        List<OnDutyEmpVO> list = dutyEmpMapper.selectJoinList(OnDutyEmpVO.class, wrapper);
        log.info("[值班动态] 统计日期：{}，单位数：{}", today, list.size());
        return list;
    }

    /**
     /**
     * 获取班组管理列表
     * 返回包含班组编号（如A组、B组等）和对应的本班位置（如徐庄站、李家庄站等）的列表数据
     *
     * @return 班组列表
     */
    @Override
    public List<DutyTeamVO> listDutyTeam() {
        // 创建多表联查条件
        MPJLambdaWrapperX<DutyTurnGroup> wrapper = new MPJLambdaWrapperX<>();
        wrapper.select(DutyTurnGroup::getId)
                .select(DutyTurnGroup::getGroupName)
                .select("dp.post_name as team_location")
                .select("dp.id as post_id")
                .select("dp.post_name as post_name")
                .leftJoin(DutyPost.class, "dp", DutyPost::getId, DutyTurnGroup::getPostId)
                .eq(DutyTurnGroup::getAvailable, true)
                .orderByAsc(DutyTurnGroup::getGroupName);

        // 查询班组信息
        List<DutyTeamVO> teamList = dutyTurnGroupMapper.selectJoinList(DutyTeamVO.class, wrapper);

        // 为每个班组查询成员信息
        for (DutyTeamVO team : teamList) {
            String groupName = team.getTeamName();
            if (groupName != null && !groupName.isEmpty()) {
                team.setTeamCode(groupName.substring(0, 1));
            }

            // 查询班组成员
            List<DutyTeamVO.TeamMemberVO> members = getTeamMembers(team.getPostId());
            team.setMembers(members);
        }

        log.info("[班组管理] 查询班组列表成功，共{}个班组", teamList.size());
        return teamList;
    }

    /**
     * 新增-修改班组信息
     * 如果DTO中包含ID则为修改操作，否则为新增操作
     *
     * @param dto 班组编辑DTO，包含班组ID、班组名称、岗位ID和成员ID列表
     * @return 操作是否成功
     * @throws BusinessException 当参数不合法时抛出
     */
    @Override
    public Boolean editDutyTeam(DutyTeamEditDTO dto) {
        if (StringUtils.isNotBlank(dto.getId())) {
            // 修改操作
            return updateDutyTeam(dto);
        } else {
            // 新增操作
            return addDutyTeam(dto);
        }
    }

    /**
     * 新增班组信息
     *
     * @param dto 班组编辑DTO
     * @return 新增是否成功
     */
    private Boolean addDutyTeam(DutyTeamEditDTO dto) {
        DutyTurnGroup dutyTurnGroup = new DutyTurnGroup();
        dutyTurnGroup.setGroupName(dto.getGroupName());
        if (dto.getPostId() != null) {
            dutyTurnGroup.setPostId(Long.valueOf(dto.getPostId()));
        }
        dutyTurnGroup.setCreateTime(LocalDateTime.now());
        dutyTurnGroup.setUpdateTime(LocalDateTime.now());
        dutyTurnGroup.setAvailable(true);

        int rows = dutyTurnGroupMapper.insert(dutyTurnGroup);

        // TODO: 如果需要处理班组成员关系，需要在这里处理 memberIds
        // 这可能需要一个单独的班组成员关系表来维护

        log.info("[新增班组] 班组名称：{}，新增结果：{}", dto.getGroupName(), rows > 0);
        return rows > 0;
    }

    /**
     * 修改班组信息
     *
     * @param dto 班组编辑DTO
     * @return 修改是否成功
     */
    private Boolean updateDutyTeam(DutyTeamEditDTO dto) {
        // 查询原始班组记录
        DutyTurnGroup dutyTurnGroup = dutyTurnGroupMapper.selectById(dto.getId());
        if (dutyTurnGroup == null) {
            throw new BusinessException("未找到对应的班组记录");
        }

        // 更新班组基本信息
        if (dto.getGroupName() != null) {
            dutyTurnGroup.setGroupName(dto.getGroupName());
        }
        if (dto.getPostId() != null) {
            dutyTurnGroup.setPostId(Long.valueOf(dto.getPostId()));
        }
        dutyTurnGroup.setUpdateTime(LocalDateTime.now());

        // 更新班组信息
        int rows = dutyTurnGroupMapper.updateById(dutyTurnGroup);

        // TODO: 如果需要更新班组成员关系，需要在这里处理 memberIds
        // 这可能需要一个单独的班组成员关系表来维护

        log.info("[修改班组] 班组ID：{}，班组名称：{}，修改结果：{}", dto.getId(), dto.getGroupName(), rows > 0);
        return rows > 0;
    }

    /**
     * 查询班组成员信息
     *
     * @param postId 岗位ID
     * @return 班组成员列表
     */
    private List<DutyTeamVO.TeamMemberVO> getTeamMembers(String postId) {
        if (postId == null) {
            return new ArrayList<>();
        }

        // 查询当前在该岗位值班的人员（取最近一周的数据）
        LocalDate endDate = LocalDate.now();
        LocalDate startDate = endDate.minusDays(7);

        MPJLambdaWrapperX<DutyEmp> wrapper = new MPJLambdaWrapperX<>();
        wrapper.select("DISTINCT t.emp_id")
                .select("emp.name as emp_name")
                .select("emp.police_number")
                .leftJoin(BasePoliceEmp.class, "emp", BasePoliceEmp::getId, DutyEmp::getEmpId)
                .eq(DutyEmp::getPostId, postId)
                .eq(DutyEmp::getAvailable, true)
                .ge(DutyEmp::getDutyDate, startDate)
                .le(DutyEmp::getDutyDate, endDate)
                .isNotNull("emp.name")
                .orderBy(true, true, "emp.name");

        List<DutyEmp> dutyEmpList = dutyEmpMapper.selectJoinList(DutyEmp.class, wrapper);

        // 转换为TeamMemberVO
        List<DutyTeamVO.TeamMemberVO> members = new ArrayList<>();
        for (DutyEmp dutyEmp : dutyEmpList) {
            BasePoliceEmp emp = basePoliceEmpMapper.selectById(dutyEmp.getEmpId());
            if (emp != null) {
                DutyTeamVO.TeamMemberVO member = new DutyTeamVO.TeamMemberVO();
                member.setEmpId(emp.getId());
                member.setEmpName(emp.getName());
                member.setPoliceNumber(emp.getCode());
                members.add(member);
            }
        }

        return members;
    }

    /**
     * 新增-修改班次类型
     * 如果DTO中包含ID则为修改操作，否则为新增操作
     *
     * @param dto 新增-修改参数，包含ID时为修改，不包含ID时为新增
     * @return 操作是否成功
     */
    @Override
    public Boolean saveDutyTurn(DutyTurnEditDTO dto) {
        if (dto.getId() != null) {
            // 修改操作
            return editDutyTurn(dto);
        } else {
            // 新增操作
            return addDutyTurn(dto);
        }
    }

    /**
     * 编辑班次信息
     *
     * @param dto 班次编辑DTO，包含要更新的班次信息
     * @return 返回一个布尔值，表示班次信息是否成功更新
     * @throws BusinessException 当班次ID为空或班次不存在时抛出服务异常
     */
    @Override
    public Boolean editDutyTurn(DutyTurnEditDTO dto) {
        // 检查班次ID是否为空，如果为空则抛出异常
        if (dto.getId() == null) {
            throw new BusinessException("班次ID不能为空");
        }

        // 根据ID查询班次信息
        DutyTurn turn = dutyTurnMapper.selectById(dto.getId());
        // 如果班次不存在，则抛出异常
        if (turn == null) {
            throw new BusinessException("班次不存在");
        }

        // 将DTO中的属性复制到班次对象中，但不包括ID
        copyDutyTurnProperties(dto, turn, false);

        // 更新班次信息，并获取受影响的行数
        int rows = dutyTurnMapper.updateById(turn);
        // 根据受影响的行数判断更新是否成功，并返回结果
        return rows > 0;
    }


    /**
     * 删除指定的班次
     *
     * @param id 班次的唯一标识，用于定位要删除的班次
     * @return 返回一个布尔值，表示班次是否被成功删除
     * @throws BusinessException 当班次ID为空或班次不存在时，抛出此异常
     */
    @Override
    public Boolean deleteDutyTurn(String id) {
        // 检查传入的班次ID是否为空，如果为空则抛出异常
        if (StringUtils.isBlank(id)) {
            throw new BusinessException("班次ID不能为空");
        }

        // 根据ID查询班次信息，如果查询结果为空，则抛出异常
        DutyTurn turn = dutyTurnMapper.selectById(id);
        if (turn == null) {
            throw new BusinessException("班次不存在");
        }

        // 尝试删除班次，并检查删除操作影响的行数，以判断删除是否成功
        int rows = dutyTurnMapper.deleteById(id);
        return rows > 0;
    }

    /**
     * 新增班次类型
     *
     * @param dto 包含班次信息的DTO，用于创建新的班次类型
     * @return 如果插入操作成功，则返回true；否则返回false
     */
    @Override
    public Boolean addDutyTurn(DutyTurnEditDTO dto) {
        DutyTurn turn = new DutyTurn();
        copyDutyTurnProperties(dto, turn, true);
        int rows = dutyTurnMapper.insert(turn);
        return rows > 0;
    }

    /**
     * 新增-修改岗位类别
     *
     * @param dto 岗位类别新增-修改DTO，包含需要新增或修改的岗位类别信息
     * @return 操作成功返回true，否则返回false
     */
    @Override
    public Boolean addPostCategory(PostCategoryAddDTO dto) {
        // 新增操作：创建新记录
        DutyPost post = new DutyPost();
        post.setPostName(dto.getName());
        post.setPostRole(dto.getRole());
        post.setCreateTime(LocalDateTime.now());
        post.setUpdateTime(LocalDateTime.now());
        post.setAvailable(true);

        // 插入岗位类别到数据库
        int rows = dutyPostMapper.insert(post);
        return rows > 0;
    }

    @Override
    public List<OrgVO> listOrg() {
        // 创建查询条件
        MPJLambdaWrapperX<BasePoliceOrg> wrapper = new MPJLambdaWrapperX<>();
        wrapper.select(BasePoliceOrg::getId)
                .select(BasePoliceOrg::getName)
                .eq(BasePoliceOrg::getAvailable, true);

        // 数据权限控制：管理员看所有的，普通用户看当前单位及下级单位的
        if (!SecurityUtils.isSuperAdmin()) {
            String currentUserOrgId = userOrgUtils.getCurrentUserOrgId();
            if (currentUserOrgId != null) {
                // 获取本单位及下级单位ID集合
                List<String> orgIds = orgRecursiveUtils.getOrgAndChildrenIds(currentUserOrgId);
                if (orgIds != null && !orgIds.isEmpty()) {
                    wrapper.in(BasePoliceOrg::getId, orgIds);
                } else {
                    wrapper.eq(BasePoliceOrg::getId, currentUserOrgId);
                }
            }
        }

        wrapper.orderByAsc(BasePoliceOrg::getId);

        // 查询单位列表并转换为VO
        List<OrgVO> orgList = basePoliceOrgMapper.selectJoinList(OrgVO.class, wrapper);
        log.info("[单位列表] 查询成功，共{}个单位", orgList.size());
        return orgList;
    }

    @Override
    public List<PostDutyDesignVO> listPostDutyDesign(String orgId) {
        // 查询岗位类别
        List<DutyPost> postList = orgId == null ?
                dutyPostMapper.selectList(null) :
                dutyPostMapper.selectList(new QueryWrapper<DutyPost>().eq("police_org_id", orgId));
        // 查询所有班次类型
        List<DutyTurn> turnList = dutyTurnMapper.selectList(null);
        // 组装VO
        List<PostDutyDesignVO> voList = new ArrayList<>();
        for (DutyPost post : postList) {
            PostDutyDesignVO vo = getPostDutyDesignVO(post, turnList);
            voList.add(vo);
        }
        return voList;
    }

    /**
     * 根据岗位信息和班次列表，构建岗位值班设置视图对象
     * 此方法用于将岗位信息和可用的班次信息封装到一个视图对象中，以便于展示或进一步处理
     *
     * @param post     岗位信息，包含岗位的详细数据
     * @param turnList 班次列表，包含所有可用的班次信息
     * @return 返回一个PostDutyDesignVO对象，其中包含了岗位信息和处理后的班次信息
     */
    @NotNull
    private static PostDutyDesignVO getPostDutyDesignVO(DutyPost post, List<DutyTurn> turnList) {
        PostDutyDesignVO vo = new PostDutyDesignVO();
        vo.setPostId(post.getId());
        vo.setPostName(post.getPostName());
        vo.setPostRole(post.getPostRole());

        List<PostDutyDesignVO.DutyTurnVO> turnVOList = turnList.stream()
                .map(turn -> {
                    PostDutyDesignVO.DutyTurnVO turnVO = new PostDutyDesignVO.DutyTurnVO();
                    turnVO.setTurnId(turn.getId());
                    turnVO.setTurnName(turn.getTurnName());
                    // 设置拆分后的时间字段
                    turnVO.setStartTime(turn.getStartTime());
                    turnVO.setStartDay(turn.getStartDay());
                    turnVO.setEndTime(turn.getEndTime());
                    turnVO.setEndDay(turn.getEndDay());
                    return turnVO;
                })
                .collect(java.util.stream.Collectors.toList());
        vo.setTurns(turnVOList);
        return vo;
    }

    /**
     * 根据站点编码获取该站点当天的在岗警力信息。
     *
     * @param stationCode 站点编码，用于查询对应的站点及所属单位信息
     * @return StationOnDutyInfoVO 包含单位名称和在岗警员列表的视图对象
     * @throws BusinessException 当未找到站点或站点未配置所属单位时抛出业务异常
     */
    @Override
    public StationOnDutyInfoVO getStationOnDutyInfo(String stationCode) {
        // 1. 查询站点信息，获取所属单位ID
        BaseStation station = baseStationMapper.selectOne(
                new QueryWrapper<BaseStation>().eq("code", stationCode).last("limit 1")
        );
        if (station == null || station.getPoliceOrgId() == null) {
            throw new BusinessException("未找到该站点或未配置所属单位");
        }
        String orgId = station.getPoliceOrgId();

        // 2. 查询单位名称
        BasePoliceOrg org = basePoliceOrgMapper.selectById(orgId);
        String orgName = org != null ? org.getName() : "";

        // 3. 查询该单位下当天在岗且有效的警力记录
        LocalDate today = LocalDate.now();
        MPJLambdaWrapperX<DutyEmp> empWrapper = new MPJLambdaWrapperX<>();
        empWrapper.select(DutyEmp::getEmpId)
                .select(DutyEmp::getPoliceOrgId)
                .select(DutyEmp::getDutyDate)
                .select(DutyEmp::getAvailable)
                .leftJoin(BasePoliceEmp.class, BasePoliceEmp::getId, DutyEmp::getEmpId)
                .eq(DutyEmp::getPoliceOrgId, orgId)
                .eq(DutyEmp::getDutyDate, today)
                .eq(DutyEmp::getAvailable, true);
        List<DutyEmp> dutyEmpList = dutyEmpMapper.selectJoinList(DutyEmp.class, empWrapper);

        // 4. 查询并封装警员详细信息
        List<StationOnDutyInfoVO.OnDutyPoliceVO> policeList = new ArrayList<>();
        for (DutyEmp dutyEmp : dutyEmpList) {
            BasePoliceEmp emp = null;
            if (dutyEmp.getEmpId() != null) {
                emp = basePoliceEmpMapper.selectById(dutyEmp.getEmpId());
            }
            StationOnDutyInfoVO.OnDutyPoliceVO policeVO = new StationOnDutyInfoVO.OnDutyPoliceVO();
            policeVO.setName(emp != null ? emp.getName() : "");
            policeVO.setType(emp != null ? EmpTypeEnum.getLabelByCode(emp.getType()) : "");
            policeVO.setOrgName(orgName);
            policeVO.setPhone(emp != null ? emp.getPhone() : "");
            policeList.add(policeVO);
        }

        StationOnDutyInfoVO vo = new StationOnDutyInfoVO();
        vo.setOrgName(orgName);
        vo.setOnDutyPoliceList(policeList);
        return vo;
    }


    /**
     * 查询排班信息列表（按日期分组）
     *
     * @param dto 排班查询条件DTO，包含以下参数：
     *            - startDate: 排班开始日期（可选）
     *            - endDate: 排班结束日期（可选）
     *            - postCategoryId: 岗位类别ID（可选）
     *            - orgId: 所属机构ID（可选）
     * @return 按日期分组的排班视图对象列表，每个对象包含：
     * - dutyDate: 排班日期
     * - dutyPersonList: 该日期下的值班人员详情列表
     * @throws BusinessException 当查询发生异常时抛出
     */
    @Override
    public List<DutyScheduleVO> queryDutySchedule(DutyScheduleQueryDTO dto) {
        log.info("[排班查询] 开始查询排班信息，查询条件：{}", dto);
        // 构建多表联查条件
        MPJLambdaWrapperX<DutyEmp> wrapper = new MPJLambdaWrapperX<>();
        wrapper.select(DutyEmp::getId)
                .selectAs(DutyEmp::getId, "id")
                .select(DutyEmp::getEmpId)
                .select(DutyEmp::getDutyDate)
                .select(DutyEmp::getPostId)
                .select(DutyEmp::getTurnId)
                .select("bpe.name as emp_name")
                .select("dp.post_name as post_name")
                .select("dt.turn_name as turn_name")
                .select("t.duty_date as duty_date")
                .leftJoin(BasePoliceEmp.class, "bpe", BasePoliceEmp::getId, DutyEmp::getEmpId)
                .leftJoin(DutyPost.class, "dp", DutyPost::getId, DutyEmp::getPostId)
                .leftJoin(DutyTurn.class, "dt", DutyTurn::getId, DutyEmp::getTurnId)
                .leftJoin(BasePoliceOrg.class, "bpo", BasePoliceOrg::getId, DutyEmp::getPoliceOrgId)
                .eq(DutyEmp::getAvailable, true)
                .eq(DutyEmp::getPoliceOrgId, dto.getOrgId())
                .eq(DutyEmp::getPostId, dto.getPostCategoryId())
                .ge(DutyEmp::getDutyDate, dto.getStartDate())
                .le(DutyEmp::getDutyDate, dto.getEndDate())
                .orderByAsc(DutyEmp::getDutyDate);
        // 执行查询
        List<DutyPersonVO> dutyPersonList = dutyEmpMapper.selectJoinList(DutyPersonVO.class, wrapper);
        log.info("[排班查询] 查询到{}条排班记录", dutyPersonList.size());
        // 按日期分组
        Map<LocalDate, List<DutyPersonVO>> dutyPersonMap = dutyPersonList.stream()
                .collect(Collectors.groupingBy(DutyPersonVO::getDutyDate));
        List<DutyScheduleVO> result = new ArrayList<>();
        dutyPersonMap.forEach((date, persons) -> {
            DutyScheduleVO vo = new DutyScheduleVO();
            vo.setDutyDate(date);
            vo.setDutyPersonList(persons);
            result.add(vo);
        });
        return result;
    }

    /**
     * 更新值班排班信息
     *
     * @param dto 值班排班编辑数据传输对象，包含以下参数：
     *            - id: 必须，排班记录唯一标识
     *            - empId: 新的员工ID
     * @return Boolean 更新是否成功，true表示更新了至少1条记录
     * @throws BusinessException 当参数不合法时抛出
     */
    @Override
    public Boolean editDutySchedule(DutyScheduleEditDTO dto) {
        // 参数基础校验
        if (dto.getId() == null) {
            throw new BusinessException("排班ID不能为空");
        }
        if (dto.getEmpId() == null) {
            throw new BusinessException("人员ID不能为空");
        }

        // 查询原始记录
        DutyEmp dutyEmp = dutyEmpMapper.selectById(dto.getId());
        if (dutyEmp == null) {
            throw new BusinessException("未找到对应的排班记录");
        }

        // 修改empId（值班人员）
        dutyEmp.setEmpId(dto.getEmpId());
        dutyEmp.setUpdateTime(LocalDateTime.now());

        // 执行更新操作并返回结果
        int rows = dutyEmpMapper.updateById(dutyEmp);
        return rows > 0;
    }

    /**
     * 批量编辑排班信息
     *
     * @param dtoList 排班编辑数据传输对象列表
     * @return Boolean 批量更新是否成功
     * @throws BusinessException 当参数为空或更新失败时抛出
     */
    @Override
    public Boolean batchEditDutySchedule(List<DutyScheduleEditDTO> dtoList) {
        if (dtoList == null || dtoList.isEmpty()) {
            throw new BusinessException("排班编辑列表不能为空");
        }

        // 批量处理每个排班编辑请求
        for (DutyScheduleEditDTO dto : dtoList) {
            Boolean result = editDutySchedule(dto);
            if (!result) {
                log.error("批量编辑排班失败，排班ID：{}", dto.getId());
                throw new BusinessException("批量编辑排班失败");
            }
        }

        log.info("[批量编辑排班] 成功编辑{}条排班记录", dtoList.size());
        return true;
    }

    /**
     * 根据机构ID获取可用岗位分类的简单信息列表
     *
     * @param orgId 所属机构ID，用于过滤特定机构的岗位分类
     * @return 包含岗位ID和岗位名称的简单视图对象列表，
     * 仅包含状态为可用的岗位分类
     */
    @Override
    public List<PostCategorySimpleVO> listSimplePostCategory(String orgId) {
        // 查询指定机构下所有可用岗位
        List<DutyPost> postList = dutyPostMapper.selectList(
                new QueryWrapper<DutyPost>().eq("police_org_id", orgId).eq("available", true)
        );
        // 将岗位实体转换为简单视图对象
        List<PostCategorySimpleVO> result = new ArrayList<>();
        for (DutyPost post : postList) {
            result.add(new PostCategorySimpleVO(post.getId(), post.getPostName()));
        }
        return result;
    }

    /**
     * 个人排班统计
     *
     * @param dto 值班查询条件（包含机构ID、岗位类别ID、统计起止日期）
     * @return 统计结果列表，包含每个人员每日在岗状态（按人员ID和日期升序排列）
     */
    @Override
    public List<PersonalDutyStatisticsVO> personalDutyStatistics(DutyScheduleQueryDTO dto) {
        // 获取指定机构下所有可用人员（过滤不可用状态）
        List<BasePoliceEmp> empList = basePoliceEmpMapper.selectList(
                new QueryWrapper<BasePoliceEmp>().eq("police_org_id", dto.getOrgId()).eq("available", true)
        );

        // 查询指定时间范围内的有效排班记录（包含机构、岗位、日期范围过滤）
        List<DutyEmp> dutyList = dutyEmpMapper.selectList(
                new QueryWrapper<DutyEmp>()
                        .eq("police_org_id", dto.getOrgId())
                        .eq("post_id", dto.getPostCategoryId())
                        .ge("duty_date", dto.getStartDate())
                        .le("duty_date", dto.getEndDate())
                        .eq("available", true)
        );

        // 构建统计结果：为每个人员生成每日在岗状态记录
        List<PersonalDutyStatisticsVO> result = new ArrayList<>();
        for (BasePoliceEmp emp : empList) {
            // 遍历查询时间范围内的每一天
            for (LocalDate date = dto.getStartDate(); !date.isAfter(dto.getEndDate()); date = date.plusDays(1)) {
                final LocalDate currentDate = date;

                // 判断当前人员当日是否排班（匹配员工ID和日期）
                boolean onDuty = dutyList.stream().anyMatch(d ->
                        d.getEmpId() != null &&
                                d.getEmpId().equals(emp.getId()) &&
                                currentDate.toString().equals(d.getDutyDate())
                );

                // 构建单条统计记录
                PersonalDutyStatisticsVO vo = new PersonalDutyStatisticsVO();
                vo.setEmpId(emp.getId());
                vo.setEmpName(emp.getName());
                vo.setDate(currentDate.toString());
                vo.setOnDuty(onDuty);
                result.add(vo);
            }
        }
        return result;
    }

    /**
     * 复制值班属性从DTO到DutyTurn对象
     *
     * @param dto   值班编辑DTO，包含用户输入的值班信息
     * @param turn  目标值班对象，将从DTO中复制属性到此对象
     * @param isNew 是否是新建的值班记录，用于确定是否设置创建时间
     *              <p>
     *              此方法不返回任何值，但会直接修改turn对象的属性
     */
    private void copyDutyTurnProperties(DutyTurnEditDTO dto, DutyTurn turn, boolean isNew) {
        // 从DTO复制属性到turn对象，排除id字段以防止ID篡改
        BeanUtils.copyProperties(dto, turn, "id");
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();
        // 如果是新建的值班记录，设置创建时间为当前时间
        if (isNew) {
            turn.setCreateTime(now);
        }
        // 更新值班记录的更新时间为当前时间
        turn.setUpdateTime(now);
    }

    /**
     * 获取当天各站点值班民警数量
     * 统计当天所有站点的在岗民警数量
     *
     * @return 各站点值班民警数量列表
     */
    @Override
    public List<StationPoliceCountVO> getStationPoliceCount() {
        // 查询所有启用状态的站点信息
        List<BaseStation> stationList = baseStationMapper.selectList(
                new QueryWrapper<BaseStation>()
                        .eq("status", 2) // 只查询启用状态的站点
        );

        // 查询当天各组织的值班人员统计
        List<OnDutyEmpVO> orgDutyList = this.onDutyDynamic();

        // 将组织ID与值班人数映射关系存入Map
        Map<String, Integer> orgDutyMap = orgDutyList.stream()
                .collect(Collectors.toMap(
                        OnDutyEmpVO::getPoliceOrgId,
                        OnDutyEmpVO::getOnDutyCount
                ));

        // 构建站点值班民警数量列表
        List<StationPoliceCountVO> stationPoliceList = new ArrayList<>();
        for (BaseStation station : stationList) {
            // 如果站点没有关联警务机构，则跳过
            if (station.getPoliceOrgId() == null) {
                continue;
            }

            String orgId = station.getPoliceOrgId();

            // 查询机构名称
            BasePoliceOrg org = basePoliceOrgMapper.selectById(orgId);
            String orgName = org != null ? org.getName() : "";

            // 获取该机构的值班人数，如果不存在则为0
            int policeCount = orgDutyMap.getOrDefault(orgId, 0);

            // 构建站点值班民警数量VO
            StationPoliceCountVO vo = new StationPoliceCountVO();
            vo.setStationId(station.getId());
            vo.setStationCode(station.getStationCode());
            vo.setStationName(station.getName());
            vo.setPoliceCount(policeCount);
            vo.setOrgId(orgId);
            vo.setOrgName(orgName);

            stationPoliceList.add(vo);
        }

        // 按民警数量降序排序，数量相同时按站点ID升序排序
        stationPoliceList.sort(Comparator.comparing(StationPoliceCountVO::getPoliceCount, Comparator.reverseOrder())
                .thenComparing(StationPoliceCountVO::getStationId));

        return stationPoliceList;
    }

    /**
     * 根据岗位ID查询可选择的值班人员列表
     *
     * @param postId 岗位ID
     * @return 可选择的值班人员列表
     */
    @Override
    public List<DutyPersonSelectVO> listDutyPersonByPost(String orgId,String postId) {
        if (StringUtils.isBlank(postId)) {
            return new ArrayList<>();
        }

        // 查询岗位信息
        DutyPost post = dutyPostMapper.selectById(postId);
        if (post == null) {
            return new ArrayList<>();
        }

        // 查询该岗位下的班组信息
        List<DutyTurnGroup> turnGroups = dutyTurnGroupMapper.selectList(
                new QueryWrapper<DutyTurnGroup>()
                        .eq("post_id", postId)
                        .eq("available", true)
        );

        List<DutyPersonSelectVO> result = new ArrayList<>();

        // 为每个班组查询成员
        for (DutyTurnGroup group : turnGroups) {
            // 查询班组成员（通过最近的值班记录获取）
            LocalDate endDate = LocalDate.now();
            LocalDate startDate = endDate.minusDays(30); // 查询最近30天的值班记录

            MPJLambdaWrapperX<DutyEmp> wrapper = new MPJLambdaWrapperX<>();
            wrapper.select("DISTINCT t.emp_id as empId")
                    .select("emp.name as empName")
                    .select("'" + group.getId() + "' as teamId")
                    .select("'" + group.getGroupName() + "' as teamName")
                    .leftJoin(BasePoliceEmp.class, "emp", BasePoliceEmp::getId, DutyEmp::getEmpId)
                    .eq(DutyEmp::getPostId, postId)
                    .eq(DutyEmp::getAvailable, true)
                    .ge(DutyEmp::getDutyDate, startDate)
                    .le(DutyEmp::getDutyDate, endDate)
                    .isNotNull("emp.name");

            // 增加orgId筛选条件：必须是某单位下的某岗位类别下的班组的所有人
            if (StringUtils.isNotBlank(orgId)) {
                wrapper.eq("t.police_org_id", orgId);
            }

            wrapper.orderBy(true, true, "emp.name");

            List<DutyPersonSelectVO> groupMembers = dutyEmpMapper.selectJoinList(DutyPersonSelectVO.class, wrapper);
            result.addAll(groupMembers);
        }


        log.info("[查询值班人员] 岗位ID：{}，查询到{}个可选人员", postId, result.size());
        return result;
    }
}

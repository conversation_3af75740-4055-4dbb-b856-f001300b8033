package cn.com.sipsg.module.business.module.supervision.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 每日报备VO
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
@Schema(description = "每日报备VO")
public class DailyReportVO {

    @Schema(description = "报备ID")
    private String id;

    @Schema(description = "单位编号")
    private String policeOrgId;

    @Schema(description = "单位名称")
    private String policeOrgName;

    @Schema(description = "录入时间")
    private LocalDateTime inputTime;

    @Schema(description = "负责人姓名")
    private String responsiblePerson;

    @Schema(description = "职务")
    private String duty;

    @Schema(description = "电话")
    private String phone;

    @Schema(description = "值守频点")
    private String dutyFrequencyPoint;
}
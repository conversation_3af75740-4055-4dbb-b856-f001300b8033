package cn.com.sipsg.module.business.module.supervision.service;

import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.module.business.module.supervision.entity.NoticeReport;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.NoticeReportPageReqDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.NoticeReportSaveDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.NoticeReportIssueDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.NoticeReportVO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 通报信息 Service 接口
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
public interface NoticeReportService extends IService<NoticeReport> {

    /**
     * 保存或更新通报信息
     *
     * @param dto 通报信息保存/更新DTO
     * @return 操作结果
     */
    Boolean saveOrUpdateNoticeReport(NoticeReportSaveDTO dto);

    /**
     * 分页查询通报信息
     *
     * @param dto 查询参数DTO
     * @return 通报信息分页列表
     */
    CommonPageVO<NoticeReportVO> pageNoticeReport(NoticeReportPageReqDTO dto);

    /**
     * 获取通报信息详情
     *
     * @param id 通报信息ID
     * @return 通报信息详情
     */
    NoticeReportSaveDTO getNoticeReportDetail(String id);

    /**
     * 删除通报信息
     *
     * @param id 通报信息ID
     * @return 操作结果
     */
    Boolean deleteNoticeReport(String id);

    /**
     * 下发通报信息
     *
     * @param dto 下发请求参数，包含通报信息ID和下发单位列表
     * @return 操作结果
     */
    Boolean issueNoticeReport(NoticeReportIssueDTO dto);

}
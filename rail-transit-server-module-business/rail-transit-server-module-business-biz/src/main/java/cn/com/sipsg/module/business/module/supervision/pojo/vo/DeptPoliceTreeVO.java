package cn.com.sipsg.module.business.module.supervision.pojo.vo;

import cn.com.sipsg.module.business.module.duty.pojo.vo.PoliceListVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 部门-警员树形结构VO
 *
 * <AUTHOR>
 * @since 2025-01-21
 */
@Data
@Schema(description = "部门-警员树形结构VO")
public class DeptPoliceTreeVO {
    
    @Schema(description = "部门ID")
    private String deptId;
    
    @Schema(description = "部门名称")
    private String deptName;
    
    @Schema(description = "部门编号")
    private String deptCode;
    
    @Schema(description = "父部门ID")
    private String parentId;
    
    @Schema(description = "部门下的警员列表")
    private List<PoliceListVO> policeList;
    
    @Schema(description = "子部门列表")
    private List<DeptPoliceTreeVO> children;
    
    @Schema(description = "警员数量")
    private Integer policeCount;
}
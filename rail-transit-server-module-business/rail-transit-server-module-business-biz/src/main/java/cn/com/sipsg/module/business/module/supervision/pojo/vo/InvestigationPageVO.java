package cn.com.sipsg.module.business.module.supervision.pojo.vo;

import java.time.LocalDateTime;
import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "盘查前科情况分页VO")
public class InvestigationPageVO {
    @Schema(description = "盘查前科ID")
    private String id;

    @Schema(description = "盘查民警")
    private String policeName;

    @Schema(description = "单位")
    private String policeOrgName;

    @Schema(description = "被盘查人姓名")
    private String personName;

    @Schema(description = "被盘查人身份证号")
    private String personIdCard;

    @Schema(description = "前科类型列表")
    private List<CriminalRecordTypeVO> personTag;

    @Schema(description = "盘查时间")
    private LocalDateTime time;
}
package cn.com.sipsg.module.business.module.duty.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR> lixt
 * @date : 2025-05-09 11:09:23
 * @Description: 标签VO
 */
@Data
@Schema(description = "标签VO", example = "{\"id\":1,\"tagValue\":\"党员\",\"tagKey\":\"party_member\",\"available\":true,\"businessType\":1}")  
public class TagVO {
    @Schema(description = "标签ID", example = "1")
    private String id;
    
    @Schema(description = "标签值", example = "党员")
    private String tagValue;
    
    @Schema(description = "标签key", example = "party_member")
    private String tagKey;
    
    @Schema(description = "是否有效", example = "true")
    private Boolean available;
    
    @Schema(description = "业务类型", example = "1")
    private Integer businessType;
}
package cn.com.sipsg.module.business.module.supervision.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@TableName("tbl_secret_investigation_plan_org")
@Schema(description = "暗访检查计划关联派出所")
public class SecretInvestigationPlanOrg {
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键ID")
    private String id;

    @TableField("create_user_id")
    @Schema(description = "创建者")
    private String createUserId;

    @TableField("create_time")
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @TableField("update_user_id")
    @Schema(description = "更新者")
    private String updateUserId;

    @TableField("update_time")
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @TableField("available")
    @Schema(description = "是否有效")
    private Boolean available;

    @TableField("plan_id")
    @Schema(description = "暗访检查计划主键")
    private String planId;

    @TableField("receive_org_id")
    @Schema(description = "接受任务单位主键")
    private String receiveOrgId;

    @TableField("investigated_org_id")
    @Schema(description = "被检查单位主键")
    private String investigatedOrgId;
} 
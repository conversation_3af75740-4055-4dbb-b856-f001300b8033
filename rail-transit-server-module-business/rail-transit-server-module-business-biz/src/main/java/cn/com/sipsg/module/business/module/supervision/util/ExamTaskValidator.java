package cn.com.sipsg.module.business.module.supervision.util;

import cn.com.sipsg.common.exception.ServerException;
import cn.com.sipsg.module.business.module.supervision.enums.QuestionTypeEnum;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.ExamTaskCreateReqDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.ExamTaskUpdateReqDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.QuestionBankUploadDTO;
import cn.hutool.core.util.StrUtil;
import java.time.LocalDateTime;

public class ExamTaskValidator {
    /**
     * 校验考试任务创建参数
     */
    public static void validateExamTaskCreateParams(ExamTaskCreateReqDTO reqDTO) {
        if (reqDTO.getStartTime() != null && reqDTO.getEndTime() != null) {
            if (reqDTO.getStartTime().isAfter(reqDTO.getEndTime())) {
                throw new ServerException("考试开始时间不能晚于结束时间");
            }
            LocalDateTime currentTime = LocalDateTime.now();
            if (reqDTO.getStartTime().isBefore(currentTime)) {
                throw new ServerException("考试开始时间不能早于当前时间");
            }
        }
        if (reqDTO.getSingleChoiceScore() == null || reqDTO.getSingleChoiceScore() < 0) {
            throw new ServerException("单选题分数配置无效");
        }
        if (reqDTO.getMultipleChoiceScore() == null || reqDTO.getMultipleChoiceScore() < 0) {
            throw new ServerException("多选题分数配置无效");
        }
        if (reqDTO.getTrueFalseScore() == null || reqDTO.getTrueFalseScore() < 0) {
            throw new ServerException("判断题分数配置无效");
        }
        if (reqDTO.getParticipants() == null || reqDTO.getParticipants().isEmpty()) {
            throw new ServerException("参与人员不能为空");
        }
        for (ExamTaskCreateReqDTO.ParticipantInfo participant : reqDTO.getParticipants()) {
            if (participant.getPoliceCode() == null || participant.getPoliceCode().trim().isEmpty()) {
                throw new ServerException("警员编号不能为空");
            }
            if (participant.getPoliceName() == null || participant.getPoliceName().trim().isEmpty()) {
                throw new ServerException("警员姓名不能为空");
            }
        }
    }

    /**
     * 校验考试任务更新参数
     */
    public static void validateExamTaskUpdateParams(ExamTaskUpdateReqDTO reqDTO) {
        if (reqDTO.getId() == null || reqDTO.getId().trim().isEmpty()) {
            throw new ServerException("考试任务ID不能为空");
        }
        if (reqDTO.getTaskName() == null || reqDTO.getTaskName().trim().isEmpty()) {
            throw new ServerException("考试任务名称不能为空");
        }
        if (reqDTO.getStartTime() == null || reqDTO.getEndTime() == null) {
            throw new ServerException("考试开始时间和结束时间不能为空");
        }
        if (reqDTO.getStartTime().isAfter(reqDTO.getEndTime())) {
            throw new ServerException("考试开始时间不能晚于结束时间");
        }
        if (reqDTO.getDuration() == null || reqDTO.getDuration() <= 0) {
            throw new ServerException("考试时长必须大于0");
        }
        if (reqDTO.getQuestionBankId() == null) {
            throw new ServerException("题库ID不能为空");
        }
        if (reqDTO.getPassScore() == null || reqDTO.getPassScore() < 0) {
            throw new ServerException("合格分数不能为负数");
        }
        // 删除数量相关校验
        // 分值校验
        if (reqDTO.getSingleChoiceScore() == null || reqDTO.getSingleChoiceScore() < 0) {
            throw new ServerException("单选题分数配置无效");
        }
        if (reqDTO.getMultipleChoiceScore() == null || reqDTO.getMultipleChoiceScore() < 0) {
            throw new ServerException("多选题分数配置无效");
        }
        if (reqDTO.getJudgmentScore() == null || reqDTO.getJudgmentScore() < 0) {
            throw new ServerException("判断题分数配置无效");
        }
    }

    /**
     * 校验题库上传数据
     */
    public static void validateUploadData(QuestionBankUploadDTO uploadDTO, int rowNum) {
        if (StrUtil.isBlank(uploadDTO.getQuestionType())) {
            throw new ServerException(String.format("第%d行：题目类型不能为空", rowNum));
        }
        if (StrUtil.isBlank(uploadDTO.getQuestionContent())) {
            throw new ServerException(String.format("第%d行：题目内容不能为空", rowNum));
        }
        if (StrUtil.isBlank(uploadDTO.getCorrectAnswer())) {
            throw new ServerException(String.format("第%d行：正确答案不能为空", rowNum));
        }
        String questionType = uploadDTO.getQuestionType().trim();
        QuestionTypeEnum typeEnum = QuestionTypeEnum.from(questionType);
        if (typeEnum == QuestionTypeEnum.SINGLE_CHOICE || typeEnum == QuestionTypeEnum.MULTIPLE_CHOICE) {
            boolean hasA = StrUtil.isNotBlank(uploadDTO.getOptionA());
            boolean hasB = StrUtil.isNotBlank(uploadDTO.getOptionB());
            if (!hasA || !hasB) {
                throw new ServerException(String.format("第%d行：选择题至少需要选项A和选项B", rowNum));
            }
        }
    }
} 
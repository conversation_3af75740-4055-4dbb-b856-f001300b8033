package cn.com.sipsg.module.business.module.supervision.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

@Data
@TableName("tbl_site_inspection")
public class SiteInspection {
    /** 记录唯一标识主键 */
    @TableId("id")
    private Long id;
    /** 记录创建时间（时间戳） */
    @TableField("create_time")
    private Long createTime;
    /** 记录最后更新时间（时间戳） */
    @TableField("update_time")
    private Long updateTime;
    /** 创建者用户ID */
    @TableField("create_user_id")
    private String createUserId;
    /** 最后更新者用户ID */
    @TableField("update_user_id")
    private String updateUserId;
    /** 是否有效 */
    @TableField("available")
    private Boolean available;
    /** 计划名称 */
    @TableField("name")
    private String name;
    /** 执行主体类型 */
    @TableField("executor_type")
    private Integer executorType;
    /** 巡检类型 */
    @TableField("type")
    private Integer type;
    /** 开始时间（时间戳） */
    @TableField("start_time")
    private Long startTime;
    /** 周期类型，天/周/月 */
    @TableField("revolution_type")
    private Integer revolutionType;
    /** 单次计划持续时间（时间戳） */
    @TableField("duration")
    private Long duration;
    /** 循环次数 */
    @TableField("round_num")
    private Integer roundNum;
    /** 执行状态 */
    @TableField("status")
    private Integer status;
    /** 进度 */
    @TableField("progress")
    private Double progress;
    /** 自定义/周期次数 */
    @TableField("revolution_num")
    private Integer revolutionNum;
    /** 结束时间（时间戳） */
    @TableField("end_time")
    private Long endTime;
    /** 是否周期计划 */
    @TableField("is_revolution")
    private Boolean isRevolution;
    /** 持续类型 */
    @TableField("duration_type")
    private Integer durationType;
} 
package cn.com.sipsg.module.business.module.duty.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.List;

@Data
@Schema(description = "站点在岗警力信息VO", example = "{\"orgName\":\"南京南站\",\"onDutyPoliceList\":[{\"name\":\"张三\",\"type\":\"民警\",\"orgName\":\"南京南站派出所\",\"phone\":\"***********\"}]}")
public class StationOnDutyInfoVO {
    @Schema(description = "所属单位名称", example = "南京南站")
    private String orgName;
    @Schema(description = "在岗警力列表")
    private List<OnDutyPoliceVO> onDutyPoliceList;

    @Data
    @Schema(description = "在岗警力VO", example = "{\"name\":\"张三\",\"type\":\"民警\",\"orgName\":\"南京南站派出所\",\"phone\":\"***********\"}")
    public static class OnDutyPoliceVO {
        @Schema(description = "考勤人员", example = "张三")
        private String name;
        @Schema(description = "人员类型", example = "民警")
        private String type;
        @Schema(description = "所属单位", example = "南京南站派出所")
        private String orgName;
        @Schema(description = "联系方式", example = "***********")
        private String phone;
    }
}
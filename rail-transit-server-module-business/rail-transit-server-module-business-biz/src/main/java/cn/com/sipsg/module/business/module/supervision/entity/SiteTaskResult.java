package cn.com.sipsg.module.business.module.supervision.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

@Data
@TableName("tbl_site_task_result")
public class SiteTaskResult {
    /** 记录唯一标识主键 */
    @TableId("id")
    private Long id;
    /** 记录创建时间 */
    @TableField("create_time")
    private Long createTime;
    /** 记录最后更新时间 */
    @TableField("update_time")
    private Long updateTime;
    /** 创建者用户ID */
    @TableField("create_user_id")
    private String createUserId;
    /** 最后更新者用户ID */
    @TableField("update_user_id")
    private String updateUserId;
    /** 是否有效 */
    @TableField("available")
    private Boolean available;
    /** 巡检检查单ID */
    @TableField("task_id")
    private Long taskId;
    /** 填写结果 */
    @TableField("result")
    private String result;
    /** 巡检检查单ID */
    @TableField("inspection_checklist_id")
    private Long inspectionChecklistId;
    /** 隐患ID */
    @TableField("instance_id")
    private String instanceId;
    /** 隐患表单 */
    @TableField("risk_form")
    private String riskForm;
    /** 检查项ID */
    @TableField("checklist_module_id")
    private Long checklistModuleId;
} 
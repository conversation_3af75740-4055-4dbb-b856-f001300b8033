package cn.com.sipsg.module.business.module.supervision.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR> lixt
 * @date : 2025-05-26 16:01:16
 * @Description: 安检数据统计信息
 */
@Data
@TableName("tbl_security_check_statistics")
public class SecurityCheckStatistics {

    @TableId(type = IdType.AUTO)
    private Long id;  // 主键

    @TableField("create_time")
    private LocalDateTime createTime;  // 创建时间

    @TableField("create_user_id")
    private String createUserId;  // 创建人

    @TableField("update_time")
    private LocalDateTime updateTime;  // 更新时间

    @TableField("update_user_id")
    private String updateUserId;  // 更新人

    @TableField("available")
    private Boolean available;  // 是否有效

    @TableField("data_sum")
    private Long dataSum;  // 总数

    @TableField("record_date")
    private LocalDate recordDate;  // 记录时间

    @TableField("type")
    private String type;  // 记录类型

    @TableField("station_id")
    private String stationId;  // 站点ID

    @TableField("entrance_id")
    private Long entranceId;  // 出入口ID
}
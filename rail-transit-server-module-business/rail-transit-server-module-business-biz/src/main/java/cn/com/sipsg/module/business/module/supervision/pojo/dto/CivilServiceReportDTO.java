package cn.com.sipsg.module.business.module.supervision.pojo.dto;

import cn.com.sipsg.module.business.module.supervision.enums.AuditStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 服务群众上报DTO
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Data
@Schema(description = "服务群众上报DTO")
public class CivilServiceReportDTO {

    @Schema(description = "主键ID", example = "1")
    private String id;

    @Schema(description = "标题名称", example = "设备故障")
    @NotBlank(message = "标题名称不能为空")
    private String title;

    @Schema(description = "上报时间", example = "2025-07-16 08:30:00")
    @NotNull(message = "上报时间不能为空")
    private LocalDateTime reportTime;

    @Schema(description = "内容", example = "地铁站内设备故障，需要维修")
    private String content;

    @Schema(description = "审核状态", example = "PENDING")
    private AuditStatusEnum status;

    @Schema(description = "是否满意：true-满意，false-不满意，null-未评价", example = "true")
    private Boolean isSatisfied;
    
    @Schema(description = "站点编号", example = "S001")
    private String stationCode;

    @Schema(description = "站点名称", example = "东站")
    private String stationName;
    
    @Schema(description = "上报人名称", example = "张三")
    private String reporterName;
}

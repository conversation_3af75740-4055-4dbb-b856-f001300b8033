package cn.com.sipsg.module.business.module.supervision.controller;

import cn.com.sipsg.common.pojo.CommonResult;
import cn.com.sipsg.common.web.core.controller.BaseController;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.CivilServiceReportDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.CivilServiceReportPageDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.CivilServiceReportPageResultDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.CivilServiceStatisticsDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.IdDTO;
import cn.com.sipsg.module.business.module.supervision.service.CivilServiceReportService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 服务群众相关接口控制器
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/civilService")
@Tag(name = "服务群众", description = "服务群众相关接口")
public class CivilServiceController extends BaseController {

    private final CivilServiceReportService civilServiceReportService;

    /**
     * 新增或修改服务群众上报
     */
    @PostMapping("/report/save")
    @Operation(summary = "新增或修改服务群众上报", description = "新增或修改服务群众上报信息")
    public CommonResult<Boolean> saveCivilServiceReport(@Validated @RequestBody CivilServiceReportDTO dto) {
        return handle(() -> CommonResult.data(civilServiceReportService.saveCivilServiceReport(dto)));
    }

    /**
     * 获取服务群众上报分页列表
     */
    @PostMapping("/report/page")
    @Operation(summary = "获取服务群众上报分页列表", description = "获取服务群众上报分页列表，支持按标题名称、上报时间、审核结果等条件查询")
    public CommonResult<IPage<CivilServiceReportPageResultDTO>> getCivilServiceReportPage(@RequestBody CivilServiceReportPageDTO pageDTO) {
        return handle(() -> CommonResult.data(civilServiceReportService.getCivilServiceReportPage(pageDTO)));
    }

    /**
     * 获取服务群众上报详情
     */
    @PostMapping("/report/detail")
    @Operation(summary = "获取服务群众上报详情", description = "根据ID获取服务群众上报详情")
    public CommonResult<CivilServiceReportDTO> getCivilServiceReportDetail(@Validated @RequestBody IdDTO dto) {
        return handle(() -> CommonResult.data(civilServiceReportService.getCivilServiceReportDetail(dto.getId())));
    }

    /**
     * 批量删除服务群众上报
     */
    @PostMapping("/report/batchDelete")
    @Operation(summary = "批量删除服务群众上报", description = "根据ID列表批量删除服务群众上报")
    public CommonResult<Boolean> batchDeleteCivilServiceReport(@RequestBody List<String> ids) {
        return handle(() -> CommonResult.data(civilServiceReportService.batchDeleteCivilServiceReport(ids)));
    }
    
    /**
     * 获取服务群众汇总统计数据（一周）
     */
    @GetMapping("/statistics/weekly")
    @Operation(summary = "获取服务群众汇总统计数据", description = "获取一周内的服务次数、服务对象数量和满意度统计")
    public CommonResult<CivilServiceStatisticsDTO> getWeeklyStatistics() {
        return handle(() -> CommonResult.data(civilServiceReportService.getWeeklyStatistics()));
    }
}
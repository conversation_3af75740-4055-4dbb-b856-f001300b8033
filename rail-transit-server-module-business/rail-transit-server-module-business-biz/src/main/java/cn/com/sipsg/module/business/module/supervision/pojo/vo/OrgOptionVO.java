package cn.com.sipsg.module.business.module.supervision.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "管辖单位下拉选项VO")
public class OrgOptionVO {
    @Schema(description = "单位ID")
    private String orgId;
    @Schema(description = "单位名称")
    private String orgName;
} 
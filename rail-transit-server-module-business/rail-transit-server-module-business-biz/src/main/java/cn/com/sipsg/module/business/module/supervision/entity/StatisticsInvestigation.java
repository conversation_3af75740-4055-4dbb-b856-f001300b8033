package cn.com.sipsg.module.business.module.supervision.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 综合统计-盘查前科情况
 */
@Data
@TableName("tbl_statistics_investigation")
public class StatisticsInvestigation implements Serializable {
    /** 主键id */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /** 创建人id */
    private String createUserId;

    /** 创建时间 */
    private LocalDateTime createTime;

    /** 修改人id */
    private String updateUserId;

    /** 修改时间 */
    private LocalDateTime updateTime;

    /** 是否有效 */
    private Boolean available;

    /** 盘查时间 */
    private LocalDateTime time;

    /** 盘查民警 */
    private String policeName;

    /** 盘查单位ID */
    private String policeOrgId;

    /** 被盘查人姓名 */
    private String personName;

    /** 被盘查人身份证号 */
    private String personIdCard;

    /** 前科类型编号,多个编号以英文逗号分隔拼接 */
    private String personTag;

    /** 备注 */
    private String memos;
}
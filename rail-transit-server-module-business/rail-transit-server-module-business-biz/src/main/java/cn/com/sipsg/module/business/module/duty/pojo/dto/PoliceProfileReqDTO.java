package cn.com.sipsg.module.business.module.duty.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @since 2025-07-23
 */
@Data
@Schema(description = "警员档案信息请求 DTO")
public class PoliceProfileReqDTO {

    @Schema(description = "警员id", required = true, example = "1")
    @NotBlank(message = "警员id不能为空")
    private String id;
}

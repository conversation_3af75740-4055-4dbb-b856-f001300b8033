<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.sipsg.module.business.module.duty.mapper.BasePoliceOrgMapper">
    
    <!-- 根据ID集合批量查询组织 -->
    <select id="selectByIdList" resultType="cn.com.sipsg.module.business.module.duty.entity.BasePoliceOrg">
        SELECT *
        FROM tbl_base_police_org
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}::varchar
        </foreach>
        AND available = true
    </select>
    
</mapper>
package cn.com.sipsg.module.business.module.supervision.pojo.dto;

import lombok.Getter;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 一周内风险查询参数DTO
 */
@Getter
public class WeeklyRiskQueryParamsDTO {
    /**
     * 警员ID列表
     */
    private final List<String> employeeIds;

    /**
     * 开始时间
     */
    private final LocalDateTime startTime;

    /**
     * 结束时间
     */
    private final LocalDateTime endTime;

    /**
     * 构造函数
     */
    public WeeklyRiskQueryParamsDTO(List<String> employeeIds, LocalDateTime startTime, LocalDateTime endTime) {
        this.employeeIds = employeeIds;
        this.startTime = startTime;
        this.endTime = endTime;
    }
} 
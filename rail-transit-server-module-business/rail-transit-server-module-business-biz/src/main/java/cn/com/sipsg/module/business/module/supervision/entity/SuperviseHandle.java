package cn.com.sipsg.module.business.module.supervision.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.sql.Timestamp;

/**
 * 督办信息表
 */
@Data
@TableName("tbl_supervise_handle")
public class SuperviseHandle {
    @TableId(type = IdType.ASSIGN_ID)
    private String id; // 主键ID

    @TableField("create_time")
    private Timestamp createTime; // 创建时间

    @TableField("update_time")
    private Timestamp updateTime; // 更新时间

    @TableField("create_user_id")
    private String createUserId; // 创建者

    @TableField("update_user_id")
    private String updateUserId; // 更新者

    @TableField("available")
    private Boolean available; // 是否有效

    @TableField("title")
    private String title; // 督办标题

    @TableField("sign_org_id")
    private String signOrgId; // 签发单位主键

    @TableField("sign_org_name")
    private String signOrgName; // 签发单位名称

    @TableField("content")
    private String content; // 图片信息（说明或描述）

    @TableField("img_url")
    private String imgUrl; // 图片链接或 base64

    @TableField("extend_file")
    private String extendFile; // 附件内容或链接
}
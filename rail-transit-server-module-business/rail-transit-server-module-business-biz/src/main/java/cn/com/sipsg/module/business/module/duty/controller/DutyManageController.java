package cn.com.sipsg.module.business.module.duty.controller;

import cn.com.sipsg.common.pojo.CommonResult;
import cn.com.sipsg.common.web.core.controller.BaseController;
import cn.com.sipsg.module.business.module.duty.pojo.dto.DutyScheduleEditDTO;
import cn.com.sipsg.module.business.module.duty.pojo.dto.DutyScheduleQueryDTO;
import cn.com.sipsg.module.business.module.duty.pojo.dto.DutyTeamEditDTO;
import cn.com.sipsg.module.business.module.duty.pojo.dto.DutyTurnEditDTO;
import cn.com.sipsg.module.business.module.duty.pojo.dto.PostCategoryAddDTO;
import cn.com.sipsg.module.business.module.duty.pojo.dto.DutyPersonQueryDTO;
import cn.com.sipsg.module.business.module.duty.pojo.dto.DutyPersonQueryDTO;
import cn.com.sipsg.module.business.module.duty.pojo.vo.DutyScheduleVO;
import cn.com.sipsg.module.business.module.duty.pojo.vo.DutyTeamVO;
import cn.com.sipsg.module.business.module.duty.pojo.vo.OnDutyEmpVO;
import cn.com.sipsg.module.business.module.duty.pojo.vo.OrgVO;
import cn.com.sipsg.module.business.module.duty.pojo.vo.PostDutyDesignVO;
import cn.com.sipsg.module.business.module.duty.pojo.vo.StationOnDutyInfoVO;
import cn.com.sipsg.module.business.module.duty.pojo.vo.PostCategorySimpleVO;
import cn.com.sipsg.module.business.module.duty.pojo.vo.PersonalDutyStatisticsVO;
import cn.com.sipsg.module.business.module.duty.pojo.vo.DutyPersonSelectVO;
import cn.com.sipsg.module.business.module.duty.service.DutyManageService;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.IdDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR> lixt
 * @date : 2025-05-10
 * @Description: 值班管理相关接口
 */
@Tag(name = "值班管理")
@RestController
@RequiredArgsConstructor
@RequestMapping("/business/duty/manage")
@Slf4j
public class DutyManageController extends BaseController {
    private final DutyManageService dutyManageService;

    @Operation(summary = "值班动态统计")
    @GetMapping("/on_duty/dynamic")
    public CommonResult<List<OnDutyEmpVO>> onDutyDynamic() {
        return handle(() -> CommonResult.data(dutyManageService.onDutyDynamic()));
    }

    @Operation(summary = "新增-修改班次类型")
    @PostMapping("/duty_turn/save")
    public CommonResult<Boolean> saveDutyTurn(@RequestBody DutyTurnEditDTO dto) {
        return handle(() -> CommonResult.data(dutyManageService.saveDutyTurn(dto)));
    }

    @Operation(summary = "删除班次类型")
    @PostMapping("/duty_turn/delete")
    public CommonResult<Boolean> deleteDutyTurn(@RequestBody @Parameter(description = "班次类型ID", required = true) IdDTO dto) {
        return handle(() -> CommonResult.data(dutyManageService.deleteDutyTurn(dto.getId())));
    }

    @Operation(summary = "新增-修改岗位类别")
    @PostMapping("/post_category/add")
    public CommonResult<Boolean> addPostCategory(@RequestBody PostCategoryAddDTO dto) {
        return handle(() -> CommonResult.data(dutyManageService.addPostCategory(dto)));
    }

    @Operation(summary = "岗位班次设置列表")
    @PostMapping("/post_duty_design/list")
    public CommonResult<List<PostDutyDesignVO>> listPostDutyDesign(@RequestBody IdDTO dto) {
        return handle(() -> CommonResult.data(dutyManageService.listPostDutyDesign(dto.getId())));
    }

    @Operation(summary = "获取班组管理列表")
    @GetMapping("/duty_team/list")
    public CommonResult<List<DutyTeamVO>> listDutyTeam() {
        return handle(() -> CommonResult.data(dutyManageService.listDutyTeam()));
    }

    @Operation(summary = "新增-修改班组信息")
    @PostMapping("/duty_team/edit")
    public CommonResult<Boolean> editDutyTeam(@RequestBody DutyTeamEditDTO dto) {
        return handle(() -> CommonResult.data(dutyManageService.editDutyTeam(dto)));
    }

    @Operation(summary = "获取单位列表")
    @GetMapping("/org/list")
    public CommonResult<List<OrgVO>> listOrg() {
        return handle(() -> CommonResult.data(dutyManageService.listOrg()));
    }

    @Operation(summary = "根据站点编号查询所属单位及在岗警力")
    @PostMapping("/station/on_duty_info")
    public CommonResult<StationOnDutyInfoVO> getStationOnDutyInfo(@RequestBody @Parameter(description = "站点编号", required = true) IdDTO dto) {
        return handle(() -> CommonResult.data(dutyManageService.getStationOnDutyInfo(dto.getId())));
    }
    
    @Operation(summary = "查询排班信息")
    @PostMapping("/duty_schedule/query")
    public CommonResult<List<DutyScheduleVO>> queryDutySchedule(@RequestBody DutyScheduleQueryDTO dto) {
        return handle(() -> CommonResult.data(dutyManageService.queryDutySchedule(dto)));
    }

    @Operation(summary = "批量编辑排班信息")
    @PostMapping("/duty_schedule/batch_edit")
    public CommonResult<Boolean> batchEditDutySchedule(@RequestBody List<DutyScheduleEditDTO> dtoList) {
        return handle(() -> CommonResult.data(dutyManageService.batchEditDutySchedule(dtoList)));
    }

    @Operation(summary = "查询单位下所有岗位类别")
    @PostMapping("/post_category/simple_list")
    public CommonResult<List<PostCategorySimpleVO>> listSimplePostCategory(@RequestBody @Parameter(description = "单位ID", required = true) IdDTO dto) {
        return handle(() -> CommonResult.data(dutyManageService.listSimplePostCategory(dto.getId())));
    }

    @Operation(summary = "个人排班统计")
    @PostMapping("/duty_personal/statistics")
    public CommonResult<List<PersonalDutyStatisticsVO>> personalDutyStatistics(@RequestBody DutyScheduleQueryDTO dto) {
        return handle(() -> CommonResult.data(dutyManageService.personalDutyStatistics(dto)));
    }

    @Operation(summary = "查询岗位类别下的班组人员")
    @PostMapping("/duty_person/list_by_post")
    public CommonResult<List<DutyPersonSelectVO>> listDutyPersonByPost(@RequestBody DutyPersonQueryDTO dto) {
        return handle(() -> CommonResult.data(dutyManageService.listDutyPersonByPost(dto.getOrgId(), dto.getPostId())));
    }
}

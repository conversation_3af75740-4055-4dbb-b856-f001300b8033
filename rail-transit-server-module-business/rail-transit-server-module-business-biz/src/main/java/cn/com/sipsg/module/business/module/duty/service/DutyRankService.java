package cn.com.sipsg.module.business.module.duty.service;

import java.util.List;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.module.business.module.duty.pojo.dto.PatrolListQueryDTO;
import cn.com.sipsg.module.business.module.duty.pojo.vo.PatrolRecordVO;
import cn.com.sipsg.module.business.module.duty.pojo.vo.RankResultVO;
import cn.com.sipsg.module.business.module.duty.pojo.dto.PatrolStatQueryDTO;
import cn.com.sipsg.module.business.module.duty.pojo.vo.PatrolStatVO;

public interface DutyRankService {
    /**
     * 统计派出所盘查勤务排名
     * @param statType 统计类型（日、周、月、年）
     * @param bizType 统计维度（盘查、安检查获、接处警）
     * @return 排名列表
     */
    List<RankResultVO> getPatrolRank(String statType, String bizType);

    /**
     * 统计个人勤务排名
     * @param statType 统计类型（日、周、月、年）
     * @param bizType 业务类型（盘查、安检查获、接处警）
     * @return 排名列表
     */
    List<RankResultVO> getPersonalRank(String statType, String bizType);

    /**
     * 分页查询盘查列表
     * @param queryDTO 查询条件，包含盘查编号、民警名称、盘查时间、盘查单位等
     * @return 分页结果
     */
    CommonPageVO<PatrolRecordVO> getPatrolList(PatrolListQueryDTO queryDTO);

    /**
     * 统计时间范围内的盘查数据
     * @param queryDTO 查询条件，包含开始时间、结束时间
     * @return 统计结果
     */
    PatrolStatVO getPatrolStats(PatrolStatQueryDTO queryDTO);
}
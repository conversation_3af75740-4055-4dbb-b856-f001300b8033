package cn.com.sipsg.module.business.module.supervision.service.impl;

import cn.com.sipsg.common.exception.ServerException;
import cn.com.sipsg.common.mybatis.core.util.MyBatisUtils;
import cn.com.sipsg.common.pojo.bo.PageBO;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.security.core.LoginUser;
import cn.com.sipsg.common.security.core.util.SecurityUtils;
import cn.com.sipsg.module.business.module.common.util.OrgRecursiveUtils;
import cn.com.sipsg.module.business.module.common.util.UserOrgUtils;
import cn.com.sipsg.module.business.module.duty.entity.BasePoliceEmp;
import cn.com.sipsg.module.business.module.duty.entity.BasePoliceOrg;
import cn.com.sipsg.module.business.module.duty.enums.PoliceEmpStatusEnum;
import cn.com.sipsg.module.business.module.duty.mapper.BasePoliceEmpMapper;
import cn.com.sipsg.module.business.module.duty.mapper.BasePoliceOrgMapper;
import cn.com.sipsg.module.business.module.duty.pojo.vo.PoliceListVO;
import cn.com.sipsg.module.business.module.supervision.entity.*;
import cn.com.sipsg.module.business.module.supervision.enums.*;
import cn.com.sipsg.module.business.module.supervision.mapper.*;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.*;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.*;
import cn.com.sipsg.module.business.module.supervision.service.PoliticalTrainingService;
import cn.com.sipsg.module.business.module.supervision.util.ExamTaskValidator;
import cn.hutool.core.util.IdUtil;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import javax.annotation.Resource;

import java.io.File;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import io.minio.BucketExistsArgs;
import io.minio.MakeBucketArgs;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import cn.com.sipsg.module.business.module.supervision.config.MinioConfig;

/**
 * 政工考核考试培训服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PoliticalTrainingServiceImpl extends ServiceImpl<ExamTaskMapper, ExamTask> implements PoliticalTrainingService {

    private final BasePoliceEmpMapper basePoliceEmpMapper;
    private final BasePoliceOrgMapper basePoliceOrgMapper;
    private final ExamTaskEmpMapper examTaskEmpMapper;
    private final ExamTaskEmpBatchMapper examTaskEmpBatchMapper;
    private final ExamQuestionBankMapper examQuestionBankMapper;
    private final ExamQuestionMapper examQuestionMapper;
    private final OrgRecursiveUtils orgRecursiveUtils;
    private final UserOrgUtils userOrgUtils;
    private final ExamTaskEmpQuestionMapper examTaskEmpQuestionMapper;

    @Value("${file.upload-path}")
    private String uploadPath;

    @Value("${file.download-domain:}")
    private String downloadDomain;

    @Resource
    private MinioClient minioClient;
    @Resource
    private MinioConfig minioConfig;

    /**
     * 获取当前用户的待办考试任务（分页）
     * 查询条件：当前用户关联的考试任务，且当前时间在考试结束时间之前的
     *
     * @param pageBO 分页参数
     * @return 分页后的待办考试任务VO列表
     */
    @Override
    public CommonPageVO<ExamTaskVO> getPendingExamTasksByCurrentUserWithPage(PageBO pageBO) {
        try {
            // 获取当前时间
            LocalDateTime currentTime = LocalDateTime.now();

            // 超级管理员查所有未结束考试任务
            if (SecurityUtils.isSuperAdmin()) {
                LambdaQueryWrapper<ExamTask> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(ExamTask::getAvailable, true)
                        .gt(ExamTask::getEndTime, currentTime)
                        .orderByAsc(ExamTask::getEndTime);
                IPage<ExamTask> page = MyBatisUtils.buildPage(pageBO);
                page = this.page(page, queryWrapper);
                return convertPageResult(page, this::convertToVO);
            }

            // 获取当前用户关联的考试任务ID列表
            List<String> taskIds = getCurrentUserRelatedTaskIds();
            if (taskIds.isEmpty()) {
                return new CommonPageVO<>();
            }

            // 构建考试任务查询条件
            LambdaQueryWrapper<ExamTask> queryWrapper = buildBaseExamTaskQueryWrapper(taskIds, currentTime);
            // 按开始时间升序排序
            queryWrapper.orderByAsc(ExamTask::getEndTime);

            // 构建分页对象
            IPage<ExamTask> page = MyBatisUtils.buildPage(pageBO);

            // 执行分页查询
            page = this.page(page, queryWrapper);

            // 将实体转换为VO并返回分页结果
            return convertPageResult(page, this::convertToVO);
        } catch (Exception e) {
            log.error("[待办考试任务分页] 获取待办考试任务分页数据异常", e);
            throw new ServerException("获取待办考试任务分页数据失败", e);
        }
    }

    /**
     * 获取当前用户的最新两条待办考试任务
     * 查询条件：当前用户关联的考试任务，且当前时间在考试结束时间之前的
     * 按截止时间降序排序，只返回最新的两条数据
     *
     * @return 最新的两条待办考试任务VO列表
     */
    @Override
    public List<ExamTaskVO> getLatestTwoPendingExamTasksByCurrentUser() {
        try {
            // 获取当前时间
            LocalDateTime currentTime = LocalDateTime.now();

            // 超级管理员查所有未结束考试任务
            if (SecurityUtils.isSuperAdmin()) {
                LambdaQueryWrapper<ExamTask> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(ExamTask::getAvailable, true)
                        .gt(ExamTask::getEndTime, currentTime)
                        .orderByDesc(ExamTask::getEndTime)
                        .last("LIMIT 2");
                List<ExamTask> examTasks = this.list(queryWrapper);
                return examTasks.stream()
                        .map(this::convertToVO)
                        .collect(Collectors.toList());
            }

            // 获取当前用户关联的考试任务ID列表
            List<String> taskIds = getCurrentUserRelatedTaskIds();
            if (taskIds.isEmpty()) {
                return Collections.emptyList();
            }

            // 构建考试任务查询条件
            LambdaQueryWrapper<ExamTask> queryWrapper = buildBaseExamTaskQueryWrapper(taskIds, currentTime);
            // 按截止时间降序排序并限制返回两条
            queryWrapper.orderByDesc(ExamTask::getEndTime)
                    .last("LIMIT 2");

            // 执行查询
            List<ExamTask> examTasks = this.list(queryWrapper);

            // 将实体转换为VO
            return examTasks.stream()
                    .map(this::convertToVO)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("[最新待办考试任务] 获取最新两条待办考试任务异常", e);
            throw new ServerException("获取最新两条待办考试任务失败", e);
        }
    }

    /**
     * 获取当前用户关联的考试任务ID列表
     *
     * @return 考试任务ID列表
     */
    public List<String> getCurrentUserRelatedTaskIds() {
        try {
            // 1. 查询当前用户关联的警员信息
            BasePoliceEmp currentEmp = userOrgUtils.getCurrentUserPoliceEmp();

            // 如果没有关联的警员信息，返回空列表
            if (currentEmp == null) {
                return Collections.emptyList();
            }

            // 2. 获取当前用户所在单位及下级单位的所有警员
            List<String> orgIds = new ArrayList<>();
            if (currentEmp.getPoliceOrgId() != null) {
                // 获取当前单位及下级单位ID
                orgIds = orgRecursiveUtils.getOrgAndChildrenIds(currentEmp.getPoliceOrgId());
            }

            // 3. 查询这些单位下的所有警员
            List<BasePoliceEmp> allEmps = new ArrayList<>();
            if (!orgIds.isEmpty()) {
                LambdaQueryWrapper<BasePoliceEmp> empWrapper = new LambdaQueryWrapper<>();
                empWrapper.in(BasePoliceEmp::getPoliceOrgId, orgIds)
                        .eq(BasePoliceEmp::getAvailable, true);
                allEmps = basePoliceEmpMapper.selectList(empWrapper);
            }

            // 4. 获取这些警员关联的考试任务ID
            if (allEmps.isEmpty()) {
                return Collections.emptyList();
            }

            List<String> policeCodes = allEmps.stream()
                    .map(BasePoliceEmp::getCode)
                    .filter(code -> code != null && !code.trim().isEmpty())
                    .collect(Collectors.toList());

            if (policeCodes.isEmpty()) {
                return Collections.emptyList();
            }

            LambdaQueryWrapper<ExamTaskEmp> taskEmpQueryWrapper = new LambdaQueryWrapper<>();
            taskEmpQueryWrapper.in(ExamTaskEmp::getPoliceCode, policeCodes)
                    .eq(ExamTaskEmp::getAvailable, true);
            List<ExamTaskEmp> examTaskEms = examTaskEmpMapper.selectList(taskEmpQueryWrapper);

            // 如果没有关联的考试任务，返回空列表
            if (examTaskEms.isEmpty()) {
                return Collections.emptyList();
            }

            // 提取考试任务ID列表并去重
            return examTaskEms.stream()
                    .map(ExamTaskEmp::getTaskId)
                    .distinct()
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("[获取用户考试任务] 获取当前用户关联的考试任务异常", e);
            return Collections.emptyList();
        }
    }

    /**
     * 构建基础的考试任务查询条件
     *
     * @param taskIds     考试任务ID列表
     * @param currentTime 当前时间
     * @return 查询条件包装器
     */
    private LambdaQueryWrapper<ExamTask> buildBaseExamTaskQueryWrapper(List<String> taskIds, LocalDateTime currentTime) {
        LambdaQueryWrapper<ExamTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ExamTask::getId, taskIds)
                .eq(ExamTask::getAvailable, true)
                .gt(ExamTask::getEndTime, currentTime);
        return queryWrapper;
    }

    /**
     * 将考试任务实体转换为视图对象
     *
     * @param entity 考试任务实体
     * @return 考试任务视图对象
     */
    private ExamTaskVO convertToVO(ExamTask entity) {
        if (entity == null) {
            return null;
        }
        ExamTaskVO vo = new ExamTaskVO();
        BeanUtils.copyProperties(entity, vo);
        // 设置题库编号
        vo.setQuestionBankId(entity.getQuestionBankId() == null ? null : String.valueOf(entity.getQuestionBankId()));
        // 使用帮助方法计算总题目数量
        int totalQuestionNum = calculateTotalQuestionNum(entity);
        vo.setTotalQuestionNum(totalQuestionNum);
        
        // 设置考试状态，优先查询用户的实际考试状态
        String status = getUserExamTaskStatus(entity.getId());
        if (status != null) {
            // 如果找到用户的实际考试状态，使用实际状态
            ExamTaskEmpStatusEnum statusEnum = ExamTaskEmpStatusEnum.getByCode(status);
            vo.setStatus(statusEnum.getDesc());
        } else {
            // 如果没有找到用户的考试记录，基于时间判断状态
            LocalDateTime now = LocalDateTime.now();
            if (entity.getStartTime() != null && entity.getEndTime() != null) {
                if (now.isBefore(entity.getStartTime())) {
                    vo.setStatus(ExamTaskEmpStatusEnum.NOT_STARTED.getDesc());
                } else if (now.isAfter(entity.getEndTime())) {
                    vo.setStatus(ExamTaskEmpStatusEnum.EXPIRED.getDesc());
                } else {
                    vo.setStatus(ExamTaskEmpStatusEnum.IN_PROGRESS.getDesc());
                }
            } else {
                vo.setStatus(ExamTaskEmpStatusEnum.UNKNOWN.getDesc());
            }
        }
        return vo;
    }
    
    /**
     * 获取当前用户在指定考试任务中的状态
     *
     * @param taskId 考试任务ID
     * @return 用户考试状态码，如果没有找到则返回null
     */
    private String getUserExamTaskStatus(String taskId) {
        try {
            BasePoliceEmp currentEmp = userOrgUtils.getCurrentUserPoliceEmp();
            if (currentEmp == null) {
                return null;
            }
            
            LambdaQueryWrapper<ExamTaskEmp> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ExamTaskEmp::getTaskId, taskId)
                    .eq(ExamTaskEmp::getEmpId, currentEmp.getId())
                    .eq(ExamTaskEmp::getAvailable, true);
            
            ExamTaskEmp examTaskEmp = examTaskEmpMapper.selectOne(queryWrapper);
            return examTaskEmp != null ? examTaskEmp.getStatus() : null;
        } catch (Exception e) {
            log.warn("[获取用户考试状态] 获取用户考试状态失败，taskId: {}", taskId, e);
            return null;
        }
    }

    /**
     * 获取当前用户的考试成绩按月统计分析
     * 统计当前用户所有考试成绩，按月份分组计算平均分
     * 如果一个月内有多个考试成绩，取平均值
     * 先从考试任务人员表中获取确认考试批次ID，再从考试批次表中获取已完成的考试成绩
     *
     * @return 按月统计的考试成绩分析VO列表
     */
    @Override
    public List<ExamScoreAnalysisVO> getExamScoreAnalysisByMonth() {
        try {
            // 如果是超级管理员，统计所有人
            if (SecurityUtils.isSuperAdmin()) {
                // 1. 查所有ExamTaskEmpBatch中已完成的考试成绩
                LambdaQueryWrapper<ExamTaskEmpBatch> batchQueryWrapper = new LambdaQueryWrapper<>();
                batchQueryWrapper.eq(ExamTaskEmpBatch::getAvailable, true)
                        .in(ExamTaskEmpBatch::getStatus,
                                ExamStatusEnum.SCORE_CONFIRMED.getCode(), // 02: 确认考试成绩
                                ExamStatusEnum.COMPLETED.getCode())       // 03: 已完成
                        .isNotNull(ExamTaskEmpBatch::getScore)
                        .isNotNull(ExamTaskEmpBatch::getEndTime)
                        .orderByDesc(ExamTaskEmpBatch::getEndTime);
                return getExamScoreAnalysisVOS(batchQueryWrapper);
            }
            // 普通用户逻辑不变
            BasePoliceEmp currentEmp = userOrgUtils.getCurrentUserPoliceEmp();
            if (currentEmp == null) {
                return Collections.emptyList();
            }
            LambdaQueryWrapper<ExamTaskEmp> taskEmpQueryWrapper = new LambdaQueryWrapper<>();
            taskEmpQueryWrapper.eq(ExamTaskEmp::getEmpId, String.valueOf(currentEmp.getId()))
                    .eq(ExamTaskEmp::getAvailable, true)
                    .in(ExamTaskEmp::getStatus,
                            ExamTaskEmpStatusEnum.PASSED.getCode(),  // 03: 合格
                            ExamTaskEmpStatusEnum.FAILED.getCode()) // 04: 不合格
                    .isNotNull(ExamTaskEmp::getTaskEmpBatchId)
                    .orderByDesc(ExamTaskEmp::getEndTime);
            List<ExamTaskEmp> examTaskEms = examTaskEmpMapper.selectList(taskEmpQueryWrapper);
            if (examTaskEms.isEmpty()) {
                return Collections.emptyList();
            }
            List<String> batchIds = examTaskEms.stream()
                    .map(ExamTaskEmp::getTaskEmpBatchId)
                    .filter(id -> id != null && !id.isEmpty())
                    .collect(Collectors.toList());
            if (batchIds.isEmpty()) {
                return Collections.emptyList();
            }
            LambdaQueryWrapper<ExamTaskEmpBatch> batchQueryWrapper = new LambdaQueryWrapper<>();
            batchQueryWrapper.in(ExamTaskEmpBatch::getId, batchIds)
                    .eq(ExamTaskEmpBatch::getAvailable, true)
                    .in(ExamTaskEmpBatch::getStatus,
                            ExamStatusEnum.SCORE_CONFIRMED.getCode(), // 02: 确认考试成绩
                            ExamStatusEnum.COMPLETED.getCode())       // 03: 已完成
                    .isNotNull(ExamTaskEmpBatch::getScore)
                    .isNotNull(ExamTaskEmpBatch::getEndTime)
                    .orderByDesc(ExamTaskEmpBatch::getEndTime);
            return getExamScoreAnalysisVOS(batchQueryWrapper);
        } catch (Exception e) {
            log.error("[考试成绩分析] 获取考试成绩月度分析数据异常", e);
            throw new ServerException("获取考试成绩月度分析数据失败", e);
        }
    }

    @NotNull
    private List<ExamScoreAnalysisVO> getExamScoreAnalysisVOS(LambdaQueryWrapper<ExamTaskEmpBatch> batchQueryWrapper) {
        List<ExamTaskEmpBatch> examTaskEmpBatches = examTaskEmpBatchMapper.selectList(batchQueryWrapper);
        if (examTaskEmpBatches.isEmpty()) {
            return Collections.emptyList();
        }
        java.time.format.DateTimeFormatter monthFormatter = java.time.format.DateTimeFormatter.ofPattern("yyyy-MM");
        Map<String, Double> monthlyAverageScores = examTaskEmpBatches.stream()
                .filter(exam -> exam.getEndTime() != null && exam.getScore() != null)
                .collect(Collectors.groupingBy(
                        exam -> exam.getEndTime().format(monthFormatter),
                        Collectors.averagingDouble(exam -> exam.getScore() != null ? exam.getScore() : 0)
                ));
        return monthlyAverageScores.entrySet().stream()
                .map(entry -> {
                    ExamScoreAnalysisVO vo = new ExamScoreAnalysisVO();
                    vo.setMonth(entry.getKey());
                    vo.setAverageScore(entry.getValue());
                    return vo;
                })
                .sorted((a, b) -> a.getMonth().compareTo(b.getMonth()))
                .collect(Collectors.toList());
    }

    /**
     * 根据考试任务名称和状态查询当前用户的考试任务（分页）
     * 支持考试任务名称模糊查询，按照考试截止时间倒序排列，支持按状态过滤
     *
     * @param reqDTO   分页参数和查询条件
     * @param taskName 考试任务名称（模糊查询），传null或空字符串表示不按名称过滤
     * @param status   考试状态（全部/未开始/进行中/不合格/合格），传null或空字符串表示查询全部
     * @return 分页后的考试任务VO列表
     */
    @Override
    public CommonPageVO<ExamTaskVO> searchUserExamTasksWithPage(ExamTaskUserPageReqDTO reqDTO, String taskName, String status) {
        try {
            // 每次查用户考试列表都先刷新所有状态
            examTaskEmpMapper.refreshAllEmpStatus();
            // 检查status参数是否为空字符串，如果是则抛出友好提示
            if (status != null && status.trim().isEmpty()) {
                throw new ServerException("考试状态参数不能为空，请选择有效的考试状态！");
            }

            // 管理员可查看全部
            if (SecurityUtils.isSuperAdmin()) {
                ExamTaskQueryFilterEnum filterEnum = ExamTaskQueryFilterEnum.getByCode(status);
                LocalDateTime now = LocalDateTime.now();
                String currentEmpId = userOrgUtils.getCurrentUserPoliceEmp() != null ? userOrgUtils.getCurrentUserPoliceEmp().getId() : null;
                if (filterEnum == ExamTaskQueryFilterEnum.PASSED) {
                    // 合格：无论是否管理员，都只能查自己的成绩
                    return queryExamTaskEmpBatchForResult(true, taskName, reqDTO, currentEmpId);
                } else if (filterEnum == ExamTaskQueryFilterEnum.FAILED) {
                    // 不合格：无论是否管理员，都只能查自己的成绩
                    return queryExamTaskEmpBatchForResult(false, taskName, reqDTO, currentEmpId);
                } else if (filterEnum == ExamTaskQueryFilterEnum.ALL) {
                    // 管理员查全部时，合并自己的批次表和未开始/已过期的考试
                    return getAllExamTasksWithPageAndNameFilter(currentEmpId, reqDTO, taskName);
                } else if (filterEnum == ExamTaskQueryFilterEnum.IN_PROGRESS) {
                    // 管理员查"我参与的进行中考试"
                    if (currentEmpId == null) {
                        return new CommonPageVO<>();
                    }
                    LambdaQueryWrapper<ExamTaskEmp> empQueryWrapper = new LambdaQueryWrapper<>();
                    empQueryWrapper.eq(ExamTaskEmp::getEmpId, currentEmpId)
                        .eq(ExamTaskEmp::getAvailable, true)
                        .eq(ExamTaskEmp::getStatus, ExamTaskEmpStatusEnum.IN_PROGRESS.getCode());
                    // 如果有名称过滤条件，添加模糊查询条件
                    return getExamTaskVOCommonPageVO(reqDTO, taskName, empQueryWrapper);
                } else {
                    // 未开始、已过期都查ExamTask表
                    LambdaQueryWrapper<ExamTask> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(ExamTask::getAvailable, true);
                    if (taskName != null && !taskName.trim().isEmpty()) {
                        queryWrapper.like(ExamTask::getTaskName, taskName.trim());
                    }
                    if (filterEnum == ExamTaskQueryFilterEnum.NOT_STARTED) {
                        queryWrapper.gt(ExamTask::getStartTime, now);
                    } else if (filterEnum == ExamTaskQueryFilterEnum.EXPIRED) {
                        queryWrapper.lt(ExamTask::getEndTime, now);
                    }
                    queryWrapper.orderByDesc(ExamTask::getEndTime);
                    IPage<ExamTask> page = MyBatisUtils.buildPage(reqDTO);
                    page = this.page(page, queryWrapper);
                    return convertPageResult(page, this::convertToVO);
                }
            }

            // 1. 查询当前用户关联的警员信息
            BasePoliceEmp currentEmp = userOrgUtils.getCurrentUserPoliceEmp();
            // 如果没有关联的警员信息，返回空列表
            if (currentEmp == null) {
                return new CommonPageVO<>();
            }

            // 将查询条件转换为查询过滤条件枚举
            ExamTaskQueryFilterEnum filterEnum = ExamTaskQueryFilterEnum.getByCode(status);

            // 如果是未知状态，则抛出参数异常，提示考试状态参数不合法
            if (filterEnum == ExamTaskQueryFilterEnum.UNKNOWN) {
                throw new ServerException("考试状态参数不合法，请检查传参！");
            }

            // 如果是全部查询，需要合并多个数据源的结果
            if (filterEnum == ExamTaskQueryFilterEnum.ALL) {
                // 合并查询所有状态的考试，并根据名称进行过滤
                return getAllExamTasksWithPageAndNameFilter(currentEmp.getId(), reqDTO, taskName);
            }

            // 2. 查询该警员的所有考试记录
            if (filterEnum == ExamTaskQueryFilterEnum.PASSED) {
                // 合格：只查ExamTaskEmp表status=03
                LambdaQueryWrapper<ExamTaskEmp> empQueryWrapper = new LambdaQueryWrapper<>();
                empQueryWrapper.eq(ExamTaskEmp::getEmpId, String.valueOf(currentEmp.getId()))
                        .eq(ExamTaskEmp::getAvailable, true)
                        .eq(ExamTaskEmp::getStatus, ExamTaskEmpStatusEnum.PASSED.getCode()); // 03: 合格
                // 如果有名称过滤条件，添加模糊查询条件
                return getExamTaskVOCommonPageVO(reqDTO, taskName, empQueryWrapper);
            } else if (filterEnum == ExamTaskQueryFilterEnum.FAILED) {
                // 不合格：只查ExamTaskEmp表status=04
                LambdaQueryWrapper<ExamTaskEmp> empQueryWrapper = new LambdaQueryWrapper<>();
                empQueryWrapper.eq(ExamTaskEmp::getEmpId, String.valueOf(currentEmp.getId()))
                        .eq(ExamTaskEmp::getAvailable, true)
                        .eq(ExamTaskEmp::getStatus, ExamTaskEmpStatusEnum.FAILED.getCode()); // 04: 不合格
                // 如果有名称过滤条件，添加模糊查询条件
                return getExamTaskVOCommonPageVO(reqDTO, taskName, empQueryWrapper);
            } else if (filterEnum == ExamTaskQueryFilterEnum.IN_PROGRESS) {
                // 进行中：只查ExamTaskEmp表status=02
                LambdaQueryWrapper<ExamTaskEmp> empQueryWrapper = new LambdaQueryWrapper<>();
                empQueryWrapper.eq(ExamTaskEmp::getEmpId, String.valueOf(currentEmp.getId()))
                        .eq(ExamTaskEmp::getAvailable, true)
                        .eq(ExamTaskEmp::getStatus, ExamTaskEmpStatusEnum.IN_PROGRESS.getCode()); // 02: 进行中
                // 如果有名称过滤条件，添加模糊查询条件
                return getExamTaskVOCommonPageVO(reqDTO, taskName, empQueryWrapper);
            } else if (filterEnum == ExamTaskQueryFilterEnum.NOT_STARTED) {
                // 未开始状态过滤：从 ExamTaskEmp 表中查询状态为"未开始"的考试
                LambdaQueryWrapper<ExamTaskEmp> empQueryWrapper = new LambdaQueryWrapper<>();
                empQueryWrapper.eq(ExamTaskEmp::getEmpId, String.valueOf(currentEmp.getId()))
                        .eq(ExamTaskEmp::getAvailable, true)
                        .eq(ExamTaskEmp::getStatus, ExamTaskEmpStatusEnum.NOT_STARTED.getCode()); // 01: 未开始
                // 如果有名称过滤条件，添加模糊查询条件
                return getExamTaskVOCommonPageVO(reqDTO, taskName, empQueryWrapper);
            } else if (filterEnum == ExamTaskQueryFilterEnum.EXPIRED) {
                // 已过期状态过滤：从 ExamTaskEmp 表中查询状态为"已过期"的考试
                LambdaQueryWrapper<ExamTaskEmp> empQueryWrapper = new LambdaQueryWrapper<>();
                empQueryWrapper.eq(ExamTaskEmp::getEmpId, String.valueOf(currentEmp.getId()))
                        .eq(ExamTaskEmp::getAvailable, true)
                        .eq(ExamTaskEmp::getStatus, ExamTaskEmpStatusEnum.EXPIRED.getCode()); // 05: 已过期
                // 如果有名称过滤条件，添加模糊查询条件
                return getExamTaskVOCommonPageVO(reqDTO, taskName, empQueryWrapper);
            }

            // 默认返回空结果
            return new CommonPageVO<>();
        } catch (ServerException e) {
            // 业务异常直接抛出，保留原始msg
            throw e;
        } catch (Exception e) {
            log.error("[用户考试搜索] 根据名称和状态搜索用户考试任务异常", e);
            throw new ServerException("搜索用户考试任务失败", e);
        }
    }

    /**
     * 获取考试任务的分页信息
     *
     * @param reqDTO          分页请求参数
     * @param taskName        任务名称，用于模糊查询
     * @param empQueryWrapper 查询包装器，用于构建查询条件
     * @return 返回一个非空的考试任务分页视图对象
     */
    @NotNull
    private CommonPageVO<ExamTaskVO> getExamTaskVOCommonPageVO(ExamTaskUserPageReqDTO reqDTO, String taskName, LambdaQueryWrapper<ExamTaskEmp> empQueryWrapper) {
        // 如果任务名称不为空，则添加模糊查询条件
        if (taskName != null && !taskName.trim().isEmpty()) {
            empQueryWrapper.exists(
                    "SELECT 1 FROM tbl_exam_task t WHERE t.id = tbl_exam_task_emp.task_id " +
                            "AND t.task_name LIKE {0} AND t.available = true",
                    "%" + taskName.trim() + "%");
        }

        // 按结束时间降序排列查询结果
        empQueryWrapper.orderByDesc(ExamTaskEmp::getEndTime);

        // 构建分页对象并执行查询
        IPage<ExamTaskEmp> page = MyBatisUtils.buildPage(reqDTO);
        page = examTaskEmpMapper.selectPage(page, empQueryWrapper);

        // 将查询结果转换为分页视图对象并返回
        return convertPageResult(page, this::convertExamTaskEmpToVO);
    }

    /**
     * 查询合格/不合格考试任务（支持任务名称模糊、分页）
     *
     * @param passExam 是否合格，true表示合格，false表示不合格
     * @param taskName 任务名称，支持模糊查询
     * @param pageBO   分页信息
     * @param empId    人员ID
     * @return 返回一个CommonPageVO对象，包含考试任务信息的分页数据
     */
    private CommonPageVO<ExamTaskVO> queryExamTaskEmpBatchForResult(Boolean passExam, String taskName, PageBO pageBO, String empId) {
        LambdaQueryWrapper<ExamTaskEmp> empQueryWrapper = new LambdaQueryWrapper<>();
        if (empId != null) {
            empQueryWrapper.eq(ExamTaskEmp::getEmpId, empId);
        }
        empQueryWrapper.eq(ExamTaskEmp::getAvailable, true);
        if (taskName != null && !taskName.trim().isEmpty()) {
            empQueryWrapper.exists(
                "SELECT 1 FROM tbl_exam_task t WHERE t.id = tbl_exam_task_emp.task_id " +
                "AND t.task_name LIKE {0} AND t.available = true",
                "%" + taskName.trim() + "%"
            );
        }
        if (passExam != null) {
            if (passExam) {
                empQueryWrapper.eq(ExamTaskEmp::getStatus, ExamTaskEmpStatusEnum.PASSED.getCode()); // 03: 合格
            } else {
                empQueryWrapper.eq(ExamTaskEmp::getStatus, ExamTaskEmpStatusEnum.FAILED.getCode()); // 04: 不合格
            }
        }
        empQueryWrapper.orderByDesc(ExamTaskEmp::getEndTime);
        IPage<ExamTaskEmp> page = MyBatisUtils.buildPage(pageBO);
        page = examTaskEmpMapper.selectPage(page, empQueryWrapper);
        return convertPageResult(page, this::convertExamTaskEmpToVO);
    }


    /**
     * 将考试任务关联记录转换为视图对象
     *
     * @param entity 考试任务关联记录实体
     * @return 考试任务视图对象
     */
    private ExamTaskVO convertExamTaskEmpToVO(ExamTaskEmp entity) {
        if (entity == null) {
            return null;
        }

        ExamTaskVO vo = new ExamTaskVO();

        // 设置基本属性
        vo.setId(entity.getTaskId());
        vo.setTaskName(entity.getTaskName());
        vo.setScore(entity.getScore());

        // 查询考试任务详细信息以获取题目数量等信息
        ExamTask examTask = this.baseMapper.selectById(entity.getTaskId());
        if (examTask != null) {
            // 使用考试任务的计划时间，而不是用户的实际考试时间
            vo.setStartTime(examTask.getStartTime());
            vo.setEndTime(examTask.getEndTime());
            copyExamTaskInfoAndCalculateTotalQuestions(examTask, vo);
        }

        // 直接用数据库status映射为中文
        String oldStatus = entity.getStatus();
        ExamTaskEmpStatusEnum statusEnum = ExamTaskEmpStatusEnum.getByCode(oldStatus);
        vo.setStatus(statusEnum.getDesc());
        return vo;
    }

    /**
     * 将考试任务批次记录转换为视图对象
     *
     * @param entity 考试任务批次记录实体
     * @return 考试任务视图对象
     */
    private ExamTaskVO convertExamTaskEmpBatchToVO(ExamTaskEmpBatch entity) {
        if (entity == null) {
            return null;
        }
        ExamTaskVO vo = new ExamTaskVO();
        vo.setId(entity.getTaskId());
        vo.setScore(entity.getScore());
        ExamTask examTask = this.baseMapper.selectById(entity.getTaskId());
        if (examTask != null) {
            vo.setTaskName(examTask.getTaskName());
            // 使用考试任务的计划时间，而不是用户的实际考试时间
            vo.setStartTime(examTask.getStartTime());
            vo.setEndTime(examTask.getEndTime());
            copyExamTaskInfoAndCalculateTotalQuestions(examTask, vo);
        }
        // 只用ExamTaskEmp表的status
        // 通过taskId和empId查ExamTaskEmp
        LambdaQueryWrapper<ExamTaskEmp> empQueryWrapper = new LambdaQueryWrapper<>();
        empQueryWrapper.eq(ExamTaskEmp::getTaskId, entity.getTaskId())
                .eq(ExamTaskEmp::getEmpId, String.valueOf(entity.getEmpId()))
                .eq(ExamTaskEmp::getAvailable, true);
        ExamTaskEmp examTaskEmp = examTaskEmpMapper.selectOne(empQueryWrapper);
        if (examTaskEmp != null) {
            String oldStatus = examTaskEmp.getStatus();
            ExamTaskEmpStatusEnum statusEnum = ExamTaskEmpStatusEnum.getByCode(oldStatus);
            vo.setStatus(statusEnum.getDesc());
        } else {
            vo.setStatus(ExamTaskEmpStatusEnum.UNKNOWN.getDesc());
        }
        return vo;
    }

    /**
     * 将分页查询结果转换为通用分页VO对象
     *
     * @param page      分页查询结果
     * @param converter 实体转换器函数，用于将实体转换为VO对象
     * @param <T>       实体类型
     * @return 通用分页VO对象
     */
    private <T> CommonPageVO<ExamTaskVO> convertPageResult(IPage<T> page, Function<T, ExamTaskVO> converter) {
        // 将实体转换为VO
        List<ExamTaskVO> voList = page.getRecords().stream()
                .map(converter)
                .collect(Collectors.toList());

        // 构建新的分页对象
        Page<ExamTaskVO> voPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        voPage.setRecords(voList);

        // 将分页结果转换为通用分页对象
        return MyBatisUtils.buildPage(voPage);
    }

    /**
     * 获取所有状态的考试任务（分页），支持按名称过滤
     * 管理员查全部时，展示所有考试任务，自己的成绩/状态优先显示，没有成绩/状态的只显示任务信息
     *
     * @param empId    警员ID
     * @param pageBO   分页参数
     * @param taskName 考试任务名称（模糊查询），传null或空字符串表示不按名称过滤
     * @return 分页后的考试任务VO列表
     */
    private CommonPageVO<ExamTaskVO> getAllExamTasksWithPageAndNameFilter(String empId, PageBO pageBO, String taskName) {
        try {
            // 管理员查询"全部"时，只显示本人参与过的考试任务
            if (empId == null) {
                return new CommonPageVO<>();
            }

            // 直接查询tbl_exam_task_emp表，这里包含最新的考试记录和状态
            LambdaQueryWrapper<ExamTaskEmp> empQueryWrapper = new LambdaQueryWrapper<>();
            empQueryWrapper.eq(ExamTaskEmp::getEmpId, empId)
                    .eq(ExamTaskEmp::getAvailable, true);
            
            // 如果任务名称不为空，则添加模糊查询条件
            if (taskName != null && !taskName.trim().isEmpty()) {
                empQueryWrapper.exists(
                        "SELECT 1 FROM tbl_exam_task t WHERE t.id = tbl_exam_task_emp.task_id " +
                                "AND t.task_name LIKE {0} AND t.available = true",
                        "%" + taskName.trim() + "%");
            }
            
            // 按结束时间降序排列查询结果
            empQueryWrapper.orderByDesc(ExamTaskEmp::getEndTime);

            // 构建分页对象并执行查询
            IPage<ExamTaskEmp> page = MyBatisUtils.buildPage(pageBO);
            page = examTaskEmpMapper.selectPage(page, empQueryWrapper);

            // 将查询结果转换为VO对象
            List<ExamTaskVO> voList = page.getRecords().stream()
                    .map(this::convertExamTaskEmpToVO)
                    .collect(Collectors.toList());

            // 构建分页对象
            Page<ExamTaskVO> voPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
            voPage.setRecords(voList);
            return MyBatisUtils.buildPage(voPage);
        } catch (Exception e) {
            log.error("[用户考试列表] 获取所有状态考试任务异常", e);
            return new CommonPageVO<>();
        }
    }

    /**
     * 计算考试任务的总题目数量
     *
     * @param examTask 考试任务实体
     * @return 总题目数量
     */
    private int calculateTotalQuestionNum(ExamTask examTask) {
        if (examTask == null) {
            return 0;
        }

        int totalQuestionNum = 0;
        if (examTask.getSingleChoiceNum() != null) {
            totalQuestionNum += examTask.getSingleChoiceNum();
        }
        if (examTask.getMultipleChoiceNum() != null) {
            totalQuestionNum += examTask.getMultipleChoiceNum();
        }
        if (examTask.getTrueFalseNum() != null) {
            totalQuestionNum += examTask.getTrueFalseNum();
        }
        return totalQuestionNum;
    }

    /**
     * 复制考试任务的基本信息到VO并计算总题目数量
     *
     * @param examTask 考试任务实体
     * @param vo       考试任务视图对象
     */
    private void copyExamTaskInfoAndCalculateTotalQuestions(ExamTask examTask, ExamTaskVO vo) {
        if (examTask == null || vo == null) {
            return;
        }

        // 复制基本信息
        vo.setExamDuration(examTask.getExamDuration());
        vo.setTotalScore(examTask.getTotalScore());
        vo.setPassingScore(examTask.getPassingScore());
        vo.setSingleChoiceNum(examTask.getSingleChoiceNum());
        vo.setMultipleChoiceNum(examTask.getMultipleChoiceScore());
        vo.setTrueFalseNum(examTask.getTrueFalseNum());
        vo.setRemark(examTask.getRemark());
        
        // 设置题库编号
        vo.setQuestionBankId(examTask.getQuestionBankId() == null ? null : String.valueOf(examTask.getQuestionBankId()));

        // 计算总题目数量
        vo.setTotalQuestionNum(calculateTotalQuestionNum(examTask));
    }

    /**
     * 新增考试任务
     * 创建新的考试任务，包含考试基本信息、时间设置、题目配置等
     *
     * @param reqDTO 考试任务新增请求DTO
     * @return 新增的考试任务ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateExamTask(ExamTaskUpdateReqDTO reqDTO) {
        try {
            // 参数校验
            ExamTaskValidator.validateExamTaskUpdateParams(reqDTO);

            // 获取当前用户信息
            LoginUser loginUser = SecurityUtils.getLoginUser();
            if (loginUser == null) {
                throw new ServerException("用户未登录");
            }
            
            // 获取当前用户关联的警员信息
            BasePoliceEmp currentPoliceEmp = userOrgUtils.getCurrentUserPoliceEmp();
            if (currentPoliceEmp == null) {
                throw new ServerException("当前用户没有关联的警员信息");
            }

            // 查询现有考试任务
            ExamTask existingTask = this.baseMapper.selectById(reqDTO.getId());
            if (existingTask == null || !Boolean.TRUE.equals(existingTask.getAvailable())) {
                throw new ServerException("考试任务不存在或已删除");
            }

            // 更新考试任务实体
            ExamTask examTask = new ExamTask();
            BeanUtils.copyProperties(reqDTO, examTask);
            examTask.setQuestionBankId(reqDTO.getQuestionBankId());
            // 手动补充字段映射
            examTask.setRemark(reqDTO.getTaskRemark());
            examTask.setExamDuration(reqDTO.getDuration());
            examTask.setTrueFalseScore(reqDTO.getJudgmentScore());
            examTask.setPassingScore(reqDTO.getPassScore());
            // 设置更新字段
            examTask.setUpdateUserId(loginUser.getUserId());
            examTask.setUpdateTime(LocalDateTime.now());
            // 计算并设置总分（不再依赖前端传数量，数量由题库统计）
            calculateAndSetTotalScoreForUpdate(examTask, reqDTO);

            // 更新考试任务
            boolean updateResult = this.updateById(examTask);
            if (!updateResult) {
                throw new ServerException("更新考试任务失败");
            }

            // 全量覆盖参与人员：先删后插
            LambdaQueryWrapper<ExamTaskEmp> delWrapper = new LambdaQueryWrapper<>();
            delWrapper.eq(ExamTaskEmp::getTaskId, reqDTO.getId());
            examTaskEmpMapper.delete(delWrapper);
            if (reqDTO.getParticipants() != null && !reqDTO.getParticipants().isEmpty()) {
                String currentEmpId = userOrgUtils.getCurrentUserPoliceEmp() != null ? userOrgUtils.getCurrentUserPoliceEmp().getId() : null;
                if (currentEmpId == null) {
                    throw new ServerException("用户未登录");
                }
                saveExamTaskParticipants(examTask, reqDTO.getParticipants(), currentEmpId);
            }

            log.info("[考试任务更新] 用户ID: {}, 考试任务ID: {}, 考试任务名称: {}",
                    loginUser.getUserId(), examTask.getId(), examTask.getTaskName());

            return true;
        } catch (Exception e) {
            log.error("[考试任务更新] 更新失败: {}", e.getMessage(), e);
            throw new ServerException("更新考试任务失败: " + e.getMessage());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public String createExamTask(ExamTaskCreateReqDTO reqDTO) {
        try {
            // 参数校验
            ExamTaskValidator.validateExamTaskCreateParams(reqDTO);

            // 获取当前用户信息
            String currentEmpId = userOrgUtils.getCurrentUserPoliceEmp() != null ? userOrgUtils.getCurrentUserPoliceEmp().getId() : null;
            if (currentEmpId == null) {
                throw new ServerException("用户未登录");
            }

            // 创建考试任务实体
            ExamTask examTask = new ExamTask();
            BeanUtils.copyProperties(reqDTO, examTask);
            examTask.setQuestionBankId(reqDTO.getQuestionBankId());

            // 设置基础字段
            examTask.setCreateUserId(currentEmpId.toString());
            examTask.setCreateTime(LocalDateTime.now());
            examTask.setUpdateUserId(currentEmpId.toString());
            examTask.setUpdateTime(LocalDateTime.now());
            examTask.setAvailable(true);

            // 从数据库查询题库中各类型题目的数量并设置到ExamTask实体中
            setQuestionCountsFromDatabase(examTask, reqDTO.getQuestionBankId());

            // 根据题目数量和分数计算总分
            calculateAndSetTotalScore(examTask, reqDTO);

            // 保存考试任务
            boolean success = this.save(examTask);
            if (!success) {
                throw new ServerException("保存考试任务失败");
            }

            // 处理参与人员信息
            if (reqDTO.getParticipants() != null && !reqDTO.getParticipants().isEmpty()) {
                saveExamTaskParticipants(examTask, reqDTO.getParticipants(), currentEmpId);
            }

            log.info("[新增考试任务] 成功创建考试任务，ID: {}, 名称: {}, 创建人ID: {}, 参与人员数: {}",
                    examTask.getId(), examTask.getTaskName(), currentEmpId,
                    reqDTO.getParticipants() != null ? reqDTO.getParticipants().size() : 0);

            return examTask.getId();
        } catch (ServerException e) {
            throw e;
        } catch (Exception e) {
            log.error("[新增考试任务] 创建考试任务异常", e);
            throw new ServerException("创建考试任务失败", e);
        }
    }

    /**
     * 从数据库查询题库中各类型题目的数量并设置到ExamTask实体中
     *
     * @param examTask       考试任务实体
     * @param questionBankId 题库ID
     */
    private void setQuestionCountsFromDatabase(ExamTask examTask, String questionBankId) {
        try {
            if (questionBankId == null) {
                log.warn("[设置题目数量] 题库ID为空，跳过题目数量设置");
                return;
            }

            // 查询各类型题目数量
            Long singleChoiceCount = getQuestionCountByQuestionType(questionBankId, QuestionTypeEnum.SINGLE_CHOICE);
            examTask.setSingleChoiceNum(singleChoiceCount.intValue());

            Long multipleChoiceCount = getQuestionCountByQuestionType(questionBankId, QuestionTypeEnum.MULTIPLE_CHOICE);
            examTask.setMultipleChoiceNum(multipleChoiceCount.intValue());

            Long trueFalseCount = getQuestionCountByQuestionType(questionBankId, QuestionTypeEnum.TRUE_FALSE);
            examTask.setTrueFalseNum(trueFalseCount.intValue());

            log.info("[设置题目数量] 题库ID: {}, 单选题: {}, 多选题: {}, 判断题: {}",
                    questionBankId, singleChoiceCount, multipleChoiceCount, trueFalseCount);
        } catch (Exception e) {
            log.error("[设置题目数量] 查询题目数量异常，题库ID: {}", questionBankId, e);
            // 设置默认值，避免空指针
            examTask.setSingleChoiceNum(0);
            examTask.setMultipleChoiceNum(0);
            examTask.setTrueFalseNum(0);
        }
    }

    /**
     * 根据题目数量和分数计算总分并设置到ExamTask实体中
     *
     * @param examTask 考试任务实体
     * @param reqDTO   考试任务创建请求DTO
     */
    private void calculateAndSetTotalScore(ExamTask examTask, ExamTaskCreateReqDTO reqDTO) {
        try {
            int totalScore = 0;
            int totalQuestionCount = 0;

            // 计算单选题总分和题目数量
            if (examTask.getSingleChoiceNum() != null && reqDTO.getSingleChoiceScore() != null) {
                totalScore += examTask.getSingleChoiceNum() * reqDTO.getSingleChoiceScore();
                totalQuestionCount += examTask.getSingleChoiceNum();
            }

            // 计算多选题总分和题目数量
            if (examTask.getMultipleChoiceNum() != null && reqDTO.getMultipleChoiceScore() != null) {
                totalScore += examTask.getMultipleChoiceNum() * reqDTO.getMultipleChoiceScore();
                totalQuestionCount += examTask.getMultipleChoiceNum();
            }

            // 计算判断题总分和题目数量
            if (examTask.getTrueFalseNum() != null && reqDTO.getTrueFalseScore() != null) {
                totalScore += examTask.getTrueFalseNum() * reqDTO.getTrueFalseScore();
                totalQuestionCount += examTask.getTrueFalseNum();
            }

            // 校验题库是否为空
            if (totalQuestionCount == 0) {
                throw new ServerException(String.format("题库ID(%d)中没有可用的题目，无法创建考试任务。请先向题库中添加题目或选择其他题库。", reqDTO.getQuestionBankId()));
            }

            // 设置总分
            examTask.setTotalScore(totalScore);

            // 校验合格分数不能大于总分
            if (reqDTO.getPassingScore() != null && reqDTO.getPassingScore() > totalScore) {
                throw new ServerException(String.format("合格分数(%d)不能大于总分(%d)", reqDTO.getPassingScore(), totalScore));
            }

            log.info("[计算总分] 题库ID: {}, 单选题: {}×{}, 多选题: {}×{}, 判断题: {}×{}, 总分: {}",
                    reqDTO.getQuestionBankId(),
                    examTask.getSingleChoiceNum(), reqDTO.getSingleChoiceScore(),
                    examTask.getMultipleChoiceNum(), reqDTO.getMultipleChoiceScore(),
                    examTask.getTrueFalseNum(), reqDTO.getTrueFalseScore(),
                    totalScore);
        } catch (ServerException e) {
            throw e;
        } catch (Exception e) {
            log.error("[计算总分] 计算总分异常", e);
            throw new ServerException("计算考试总分失败", e);
        }
    }

    /**
     * 保存考试任务参与人员信息
     *
     * @param examTask     考试任务实体
     * @param participants 参与人员列表
     * @param currentEmpId 当前登录用户ID
     */
    private void saveExamTaskParticipants(ExamTask examTask, List<ExamTaskCreateReqDTO.ParticipantInfo> participants, String currentEmpId) {
        try {
            List<ExamTaskEmp> examTaskEmpList = new ArrayList<>();
            LocalDateTime now = LocalDateTime.now();

            for (ExamTaskCreateReqDTO.ParticipantInfo participant : participants) {
                // 根据警员编号查询警员信息
                LambdaQueryWrapper<BasePoliceEmp> empQueryWrapper = new LambdaQueryWrapper<>();
                empQueryWrapper.eq(BasePoliceEmp::getCode, participant.getPoliceCode())
                        .eq(BasePoliceEmp::getAvailable, true);
                BasePoliceEmp policeEmp = basePoliceEmpMapper.selectOne(empQueryWrapper);

                if (policeEmp == null) {
                    log.warn("[保存考试参与人员] 未找到警员编号为 {} 的警员信息", participant.getPoliceCode());
                    continue;
                }

                // 创建考试人员关联记录
                ExamTaskEmp examTaskEmp = new ExamTaskEmp();
                examTaskEmp.setTaskId(examTask.getId());
                examTaskEmp.setTaskName(examTask.getTaskName());
                examTaskEmp.setEmpId(String.valueOf(policeEmp.getId()));
                examTaskEmp.setPoliceCode(participant.getPoliceCode());
                examTaskEmp.setPoliceName(participant.getPoliceName());
                examTaskEmp.setStatus("01"); // 01:未开始
                examTaskEmp.setCreateUserId(currentEmpId);
                examTaskEmp.setCreateTime(now);
                examTaskEmp.setUpdateUserId(currentEmpId);
                examTaskEmp.setUpdateTime(now);
                examTaskEmp.setAvailable(true);

                examTaskEmpList.add(examTaskEmp);
            }

            // 批量保存考试人员关联记录
            if (!examTaskEmpList.isEmpty()) {
                for (ExamTaskEmp examTaskEmp : examTaskEmpList) {
                    int insertResult = examTaskEmpMapper.insert(examTaskEmp);
                    if (insertResult <= 0) {
                        throw new ServerException("保存考试参与人员失败: " + examTaskEmp.getPoliceCode());
                    }
                }
                log.info("[保存考试参与人员] 成功保存 {} 条参与人员记录", examTaskEmpList.size());
            }
        } catch (Exception e) {
            log.error("[保存考试参与人员] 保存参与人员异常", e);
            throw new ServerException("保存考试参与人员失败", e);
        }
    }

    /**
     * 根据题目数量和分数计算总分并设置到ExamTask实体中（更新版本）
     *
     * @param examTask 考试任务实体
     * @param reqDTO   考试任务更新请求DTO
     */
    private void calculateAndSetTotalScoreForUpdate(ExamTask examTask, ExamTaskUpdateReqDTO reqDTO) {
        try {
            // 只根据题库统计数量，分值用reqDTO
            int totalScore = 0;
            int totalQuestionCount = 0;
            // 这里应从题库查数量，假设有方法：getQuestionCountByType
            int singleChoiceCount = getQuestionCountByQuestionType(reqDTO.getQuestionBankId(), QuestionTypeEnum.SINGLE_CHOICE).intValue();
            int multipleChoiceCount = getQuestionCountByQuestionType(reqDTO.getQuestionBankId(), QuestionTypeEnum.MULTIPLE_CHOICE).intValue();
            int judgmentCount = getQuestionCountByQuestionType(reqDTO.getQuestionBankId(), QuestionTypeEnum.TRUE_FALSE).intValue();
            if (reqDTO.getSingleChoiceScore() != null) {
                totalScore += singleChoiceCount * reqDTO.getSingleChoiceScore();
                examTask.setSingleChoiceScore(reqDTO.getSingleChoiceScore());
            }
            if (reqDTO.getMultipleChoiceScore() != null) {
                totalScore += multipleChoiceCount * reqDTO.getMultipleChoiceScore();
                examTask.setMultipleChoiceScore(reqDTO.getMultipleChoiceScore());
            }
            if (reqDTO.getJudgmentScore() != null) {
                totalScore += judgmentCount * reqDTO.getJudgmentScore();
                examTask.setTrueFalseScore(reqDTO.getJudgmentScore());
            }
            totalQuestionCount = singleChoiceCount + multipleChoiceCount + judgmentCount;
            if (totalQuestionCount == 0) {
                throw new ServerException("题库中没有题目，无法创建考试任务");
            }
            examTask.setSingleChoiceNum(singleChoiceCount);
            examTask.setMultipleChoiceNum(multipleChoiceCount);
            examTask.setTrueFalseNum(judgmentCount);
            examTask.setTotalScore(totalScore);
            // 校验合格分数不能大于总分
            if (reqDTO.getPassScore() != null && reqDTO.getPassScore() > totalScore) {
                throw new ServerException("合格分数不能大于总分");
            }
        } catch (Exception e) {
            throw new ServerException("计算总分失败: " + e.getMessage());
        }
    }

    /**
     * 题库分页列表查询
     * 支持题库名称模糊查询和状态过滤，查询条件可选
     * 当查询条件不传值时，查询所有可用的题库数据
     *
     * @param reqDTO 题库查询参数，包含分页信息和过滤条件（可选）
     * @return 分页后的题库VO列表
     */
    @Override
    public CommonPageVO<QuestionBankVO> getQuestionBankListWithPage(QuestionBankPageReqDTO reqDTO) {
        try {
            // 构建查询条件
            LambdaQueryWrapper<ExamQuestionBank> queryWrapper = new LambdaQueryWrapper<>();
            // 只查询可用的题库
            queryWrapper.eq(ExamQuestionBank::getAvailable, true);

            // 题库名称模糊查询（可选）
            if (reqDTO.getName() != null && !reqDTO.getName().trim().isEmpty()) {
                queryWrapper.like(ExamQuestionBank::getName, reqDTO.getName().trim());
            }

            // 题库状态过滤（可选）
            // 当status为null、空字符串或"00"时，查询所有状态的题库
            if (reqDTO.getStatus() != null &&
                    !reqDTO.getStatus().trim().isEmpty() &&
                    !"00".equals(reqDTO.getStatus().trim())) {
                queryWrapper.eq(ExamQuestionBank::getStatus, reqDTO.getStatus().trim());
            }

            // 按创建时间倒序排序
            queryWrapper.orderByDesc(ExamQuestionBank::getCreateTime);

            // 执行分页查询
            IPage<ExamQuestionBank> page = MyBatisUtils.buildPage(reqDTO);
            page = examQuestionBankMapper.selectPage(page, queryWrapper);

            // 转换为VO并返回
            return convertQuestionBankPageResult(page);
        } catch (Exception e) {
            log.error("[题库分页列表] 获取题库分页数据异常", e);
            throw new ServerException("获取题库分页数据失败", e);
        }
    }

    /**
     * 将题库分页结果转换为VO
     *
     * @param page 题库分页结果
     * @return 题库VO分页结果
     */
    private CommonPageVO<QuestionBankVO> convertQuestionBankPageResult(IPage<ExamQuestionBank> page) {
        CommonPageVO<QuestionBankVO> result = new CommonPageVO<>();
        result.setTotal(page.getTotal());
        result.setSize(page.getSize());
        result.setCurrent(page.getCurrent());

        List<QuestionBankVO> voList = page.getRecords().stream()
                .map(this::convertQuestionBankToVO)
                .collect(Collectors.toList());
        result.setRecords(voList);

        return result;
    }

    /**
     * 将题库实体转换为视图对象
     *
     * @param entity 题库实体
     * @return 题库视图对象
     */
    private QuestionBankVO convertQuestionBankToVO(ExamQuestionBank entity) {
        if (entity == null) {
            return null;
        }

        QuestionBankVO vo = new QuestionBankVO();
        BeanUtils.copyProperties(entity, vo);

        // 设置状态描述
        QuestionBankStatusEnum statusEnum = QuestionBankStatusEnum.getByCode(entity.getStatus());
        vo.setStatusDesc(statusEnum.getDesc());

        return vo;
    }

    /**
     * 构建选项JSON字符串
     *
     * @param uploadDTO 上传数据
     * @return 选项JSON字符串
     */
    private String buildOptionJson(QuestionBankUploadDTO uploadDTO) {
        String questionType = uploadDTO.getQuestionType().trim();
        Map<String, String> options = new HashMap<>();
        if ("3".equals(questionType)) { // 判断题
            options.put(QuestionOptionEnum.A.getCode(), "正确");
            options.put(QuestionOptionEnum.B.getCode(), "错误");
        } else { // 单选题或多选题
            for (QuestionOptionEnum option : QuestionOptionEnum.values()) {
                String value = null;
                switch (option) {
                    case A:
                        value = uploadDTO.getOptionA();
                        break;
                    case B:
                        value = uploadDTO.getOptionB();
                        break;
                    case C:
                        value = uploadDTO.getOptionC();
                        break;
                    case D:
                        value = uploadDTO.getOptionD();
                        break;
                    case E:
                        value = uploadDTO.getOptionE();
                        break;
                    case F:
                        value = uploadDTO.getOptionF();
                        break;
                    case G:
                        value = uploadDTO.getOptionG();
                        break;
                }
                if (value != null && !value.trim().isEmpty()) {
                    options.put(option.getCode(), value.trim());
                }
            }
        }
        return JSON.toJSONString(options);
    }

    @Override
    public Boolean disableQuestionBank(String questionBankId) {
        // 权限校验：只有超级管理员才能停用题库
        if (!SecurityUtils.isSuperAdmin()) {
            throw new ServerException("权限不足，只有管理员才能停用题库");
        }

        ExamQuestionBank questionBank = examQuestionBankMapper.selectById(questionBankId);
        if (questionBank == null || !questionBank.getAvailable()) {
            throw new ServerException("题库不存在或已禁用");
        }

        // 获取当前用户对应的警员信息
        BasePoliceEmp currentPoliceEmp = userOrgUtils.getCurrentUserPoliceEmp();
        if (currentPoliceEmp == null) {
            throw new ServerException("当前用户未关联警员信息，无法操作题库");
        }

        questionBank.setStatus(QuestionBankStatusEnum.DISABLED.getCode()); // 02为禁用
        questionBank.setUpdateUserId(currentPoliceEmp.getId()); // 使用警员ID而不是用户ID
        questionBank.setUpdateTime(LocalDateTime.now());
        int result = examQuestionBankMapper.updateById(questionBank);

        return result > 0;
    }

    /**
     * 获取考试任务详细信息及人员分页列表
     *
     * @param reqDTO 考试任务详情请求DTO，包含任务ID、页码、页面大小、状态和关键字等参数
     * @return 返回考试任务详细信息和人员分页数据的VO对象
     * @throws ServerException 如果考试任务不存在或已删除，则抛出服务器异常
     */
    @Override
    public CommonPageVO<ExamTaskDetailVO> getExamTaskDetailWithPersonPage(ExamTaskDetailReqDTO reqDTO) {
        // 1. 查询考试任务主表
        ExamTask examTask = this.baseMapper.selectById(reqDTO.getTaskId());
        if (examTask == null) {
            throw new RuntimeException("考试任务不存在");
        }

        // 2. 分页查询参与人员
        int pageNum = reqDTO.getPageNum() == null ? 1 : reqDTO.getPageNum();
        int pageSize = reqDTO.getPageSize() == null ? 10 : reqDTO.getPageSize();
        Page<ExamTaskEmp> page = new Page<>(pageNum, pageSize);

        com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<ExamTaskEmp> wrapper =
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<ExamTaskEmp>()
                        .eq(ExamTaskEmp::getTaskId, reqDTO.getTaskId())
                        .eq(ExamTaskEmp::getAvailable, true);

        // 添加姓名模糊搜索
        if (reqDTO.getKeyword() != null && !reqDTO.getKeyword().trim().isEmpty()) {
            wrapper.like(ExamTaskEmp::getPoliceName, reqDTO.getKeyword().trim());
        }

        // 添加状态筛选
        if (reqDTO.getStatus() != null && !reqDTO.getStatus().trim().isEmpty() && !"00".equals(reqDTO.getStatus().trim())) {
            wrapper.eq(ExamTaskEmp::getStatus, reqDTO.getStatus().trim());
        }

        com.baomidou.mybatisplus.extension.plugins.pagination.Page<ExamTaskEmp> empPage =
                examTaskEmpMapper.selectPage(page, wrapper);

        // 3. 组装VO列表
        List<ExamTaskDetailVO> voList = empPage.getRecords().stream().map(emp -> {
            ExamTaskDetailVO vo = new ExamTaskDetailVO();
            // 设置人员信息
            vo.setExamNo(empPage.getRecords().indexOf(emp) + 1 + (int) ((empPage.getCurrent() - 1) * empPage.getSize()));
            vo.setPersonName(emp.getPoliceName());
            vo.setPoliceCode(emp.getPoliceCode());
            vo.setEmpId(emp.getEmpId());
            vo.setName(emp.getPoliceName());
            vo.setExamStatus(emp.getStatus());
            vo.setScore(emp.getScore() != null ? String.valueOf(emp.getScore()) : null);
            // 设置考试时间信息
            if (emp.getStartTime() != null) {
                vo.setStartTime(emp.getStartTime().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            }
            if (emp.getStartTime() != null && emp.getEndTime() != null) {
                long durationMinutes = java.time.Duration.between(emp.getStartTime(), emp.getEndTime()).toMinutes();
                vo.setDuration(durationMinutes + "分钟");
            }
            return vo;
        }).collect(Collectors.toList());

        // 4. 返回分页结构
        CommonPageVO<ExamTaskDetailVO> result = new CommonPageVO<>();
        result.setRecords(voList);
        result.setTotal(empPage.getTotal());
        result.setCurrent(empPage.getCurrent());
        result.setSize(empPage.getSize());
        return result;
    }

    /**
     * 根据条件分页查询考试任务
     *
     * @param reqDTO 考试任务分页查询请求DTO，包含查询条件和分页参数
     * @return 返回包含考试任务列表的分页VO对象
     */
    @Override
    public CommonPageVO<ExamTaskPageVO> pageExamTasks(ExamTaskPageReqDTO reqDTO) {
        boolean isAdmin = SecurityUtils.isSuperAdmin();
        List<String> taskIds = null;
        if (!isAdmin) {
            taskIds = getCurrentUserRelatedTaskIds();
            if (taskIds.isEmpty()) {
                CommonPageVO<ExamTaskPageVO> empty = new CommonPageVO<>();
                empty.setTotal(0L);
                empty.setRecords(new ArrayList<>());
                empty.setSize(reqDTO.getSize());
                empty.setCurrent(reqDTO.getCurrent());
                return empty;
            }
        }

        // 只允许全部、未开始、进行中三种状态筛选
        String filterStatus = reqDTO.getStatus();
        LambdaQueryWrapper<ExamTask> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ExamTask::getAvailable, true);
        if (reqDTO.getTaskName() != null && !reqDTO.getTaskName().trim().isEmpty()) {
            wrapper.like(ExamTask::getTaskName, reqDTO.getTaskName().trim());
        }
        if (!isAdmin) {
            wrapper.in(ExamTask::getId, taskIds);
        }
        LocalDateTime now = LocalDateTime.now();
        if ("01".equals(filterStatus)) { // 未开始
            wrapper.gt(ExamTask::getStartTime, now);
        } else if ("02".equals(filterStatus)) { // 进行中
            wrapper.le(ExamTask::getStartTime, now).ge(ExamTask::getEndTime, now);
        }
        wrapper.orderByDesc(ExamTask::getCreateTime);

        // 分页查询
        IPage<ExamTask> page = new Page<>(reqDTO.getCurrent(), reqDTO.getSize());
        page = this.page(page, wrapper);
        List<ExamTask> records = page.getRecords();
        List<ExamTaskPageVO> voList = new ArrayList<>();

        for (ExamTask task : records) {
            ExamTaskPageVO vo = new ExamTaskPageVO();
            vo.setId(task.getId());
            vo.setTaskName(task.getTaskName());
            vo.setExamDuration(task.getExamDuration());
            vo.setTotalScore(task.getTotalScore());
            vo.setPassingScore(task.getPassingScore());
            int totalQuestionNum = 0;
            if (task.getSingleChoiceNum() != null) totalQuestionNum += task.getSingleChoiceNum();
            if (task.getMultipleChoiceNum() != null) totalQuestionNum += task.getMultipleChoiceNum();
            if (task.getTrueFalseNum() != null) totalQuestionNum += task.getTrueFalseNum();
            vo.setTotalQuestionNum(totalQuestionNum);
            vo.setScoreSync(task.getScoreSync() != null && task.getScoreSync() ? "是" : "否");
            java.time.format.DateTimeFormatter dtf = java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            vo.setExamTimeRange(task.getStartTime() == null || task.getEndTime() == null ? "" :
                    dtf.format(task.getStartTime()) + " - " + dtf.format(task.getEndTime()));
            vo.setQuestionBankId(task.getQuestionBankId() == null ? null : String.valueOf(task.getQuestionBankId()));

            // 统计需考试人数、已考试人数、合格率
            LambdaQueryWrapper<ExamTaskEmp> allEmpWrapper = new LambdaQueryWrapper<>();
            allEmpWrapper.eq(ExamTaskEmp::getTaskId, task.getId()).eq(ExamTaskEmp::getAvailable, true);
            int requiredCount = examTaskEmpMapper.selectCount(allEmpWrapper).intValue();
            int finishedCount = examTaskEmpMapper.selectCount(allEmpWrapper.ne(ExamTaskEmp::getStatus, "01")).intValue();
            int passCount = examTaskEmpMapper.selectCount(allEmpWrapper.eq(ExamTaskEmp::getStatus, "03")).intValue();
            vo.setRequiredCount(requiredCount);
            vo.setFinishedCount(finishedCount);
            vo.setPassRate(requiredCount == 0 ? "-" : (int) Math.round(passCount * 100.0 / requiredCount) + "%");

            // 只允许未开始、进行中、已过期三种状态
            if (task.getStartTime() != null && task.getEndTime() != null) {
                if (now.isBefore(task.getStartTime())) {
                    vo.setStatus("未开始");
                } else if (now.isAfter(task.getEndTime())) {
                    vo.setStatus("已过期");
                } else {
                    vo.setStatus("进行中");
                }
            } else {
                vo.setStatus("");
            }

            voList.add(vo);
        }

        CommonPageVO<ExamTaskPageVO> result = new CommonPageVO<>();
        result.setTotal(page.getTotal());
        result.setRecords(voList);
        result.setSize(page.getSize());
        result.setCurrent(page.getCurrent());
        return result;
    }

    /**
     * 获取当前用户的考试详情
     *
     * @param reqDTO 包含任务ID的请求数据传输对象
     * @return MyExamDetailVO 包含考试详细信息的值对象
     */
    @Override
    public MyExamDetailVO getMyExamDetail(MyExamDetailReqDTO reqDTO) {
        String taskId = reqDTO.getTaskId();
        // 获取当前用户
        BasePoliceEmp currentEmp = userOrgUtils.getCurrentUserPoliceEmp();
        if (currentEmp == null) {
            throw new ServerException("未找到当前用户警员信息");
        }
        String empId = currentEmp.getId();
        // 1. 优先查ExamTaskEmpBatch（有多次取最新一条）
        LambdaQueryWrapper<ExamTaskEmpBatch> batchWrapper = new LambdaQueryWrapper<>();
        batchWrapper.eq(ExamTaskEmpBatch::getTaskId, taskId)
                .eq(ExamTaskEmpBatch::getEmpId, String.valueOf(empId))
                .eq(ExamTaskEmpBatch::getAvailable, true)
                .orderByDesc(ExamTaskEmpBatch::getEndTime)
                .last("LIMIT 1");
        ExamTaskEmpBatch batch = examTaskEmpBatchMapper.selectOne(batchWrapper);
        ExamTask examTask = this.baseMapper.selectById(taskId);
        if (examTask == null) {
            throw new ServerException("考试任务不存在");
        }
        MyExamDetailVO vo = new MyExamDetailVO();
        vo.setTaskName(examTask.getTaskName());
        java.time.format.DateTimeFormatter dtf = java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        vo.setExamTimeRange(examTask.getStartTime() == null || examTask.getEndTime() == null ? "" :
                dtf.format(examTask.getStartTime()) + " - " + dtf.format(examTask.getEndTime()));
        vo.setExamInfo("时长" + examTask.getExamDuration() + "分钟/总分" + examTask.getTotalScore() + "/" + examTask.getPassingScore() + "分合格/共" + (examTask.getSingleChoiceNum() + examTask.getMultipleChoiceNum() + examTask.getTrueFalseNum()) + "题");
        vo.setPersonName(currentEmp.getName());
        if (batch != null) {
            String status = batch.getStatus();
            if (ExamTaskEmpStatusEnum.NOT_STARTED.getCode().equals(status) || ExamTaskEmpStatusEnum.EXPIRED.getCode().equals(status)) {
                vo.setScore(null);
                vo.setStartTime(null);
                vo.setDuration(null);
                vo.setResult("");
            } else {
                getExamDetailVo(vo, batch.getScore(), batch.getStartTime(), batch.getEndTime(), null);
                // 合格/不合格
                if (batch.getPassExam() != null) {
                    vo.setResult(batch.getPassExam() ? "合格" : "不合格");
                } else {
                    vo.setResult("-");
                }
            }
        } else {
            // 没有批次表，查ExamTaskEmp
            LambdaQueryWrapper<ExamTaskEmp> empWrapper = new LambdaQueryWrapper<>();
            empWrapper.eq(ExamTaskEmp::getTaskId, taskId)
                    .eq(ExamTaskEmp::getEmpId, String.valueOf(empId))
                    .eq(ExamTaskEmp::getAvailable, true)
                    .orderByDesc(ExamTaskEmp::getEndTime)
                    .last("LIMIT 1");
            ExamTaskEmp emp = examTaskEmpMapper.selectOne(empWrapper);
            if (emp != null) {
                String status = emp.getStatus();
                if (ExamTaskEmpStatusEnum.NOT_STARTED.getCode().equals(status) || ExamTaskEmpStatusEnum.EXPIRED.getCode().equals(status)) {
                    vo.setScore(null);
                    vo.setStartTime(null);
                    vo.setDuration(null);
                    vo.setResult("");
                } else {
                    getExamDetailVo(vo, emp.getScore(), emp.getStartTime(), emp.getEndTime(), emp);
                    // 合格/不合格
                    if (ExamTaskEmpStatusEnum.PASSED.getCode().equals(status)) {
                        vo.setResult("合格");
                    } else if (ExamTaskEmpStatusEnum.FAILED.getCode().equals(status)) {
                        vo.setResult("不合格");
                    } else {
                        vo.setResult("-");
                    }
                }
            } else {
                vo.setScore(null);
                vo.setStartTime(null);
                vo.setDuration(null);
                vo.setResult("");
            }
        }
        return vo;
    }

    private void getExamDetailVo(MyExamDetailVO vo, Integer score, LocalDateTime startTime, LocalDateTime endTime, ExamTaskEmp emp) {
        vo.setScore(score);
        vo.setStartTime(startTime);
        if (startTime != null && endTime != null) {
            long seconds = java.time.Duration.between(startTime, endTime).getSeconds();
            long hour = seconds / 3600;
            long min = (seconds % 3600) / 60;
            long sec = seconds % 60;
            vo.setDuration((hour > 0 ? hour + "小时" : "") + (min > 0 ? min + "分" : "") + sec + "秒");
        } else {
            vo.setDuration("");
        }
    }

    // 保留原有方法供内部调用
    public Integer uploadQuestionBank(MultipartFile file, String questionBankId) {
        try {
            if (file == null || file.isEmpty()) {
                throw new ServerException("上传文件不能为空");
            }
            try (java.io.InputStream is = file.getInputStream()) {
                return uploadQuestionBank(is, questionBankId);
            }
        } catch (Exception e) {
            log.error("[题库上传] 题库上传异常", e);
            throw new ServerException("题库上传失败: " + e.getMessage(), e);
        }
    }

    // 新增：支持InputStream的题库上传方法
    public Integer uploadQuestionBank(InputStream is, String questionBankId) {
        try {
            if (is == null) {
                throw new ServerException("上传文件流不能为空");
            }
            if (questionBankId == null) {
                throw new ServerException("题库ID不能为空");
            }
            // 校验题库是否存在
            ExamQuestionBank questionBank = examQuestionBankMapper.selectById(questionBankId);
            if (questionBank == null || !questionBank.getAvailable()) {
                throw new ServerException("题库不存在或已禁用");
            }
            // 用EasyExcel读取（跳过前8行，从第9行开始读取数据，不使用表头）
            List<QuestionBankUploadDTO> uploadList = EasyExcel.read(is)
                    .head(QuestionBankUploadDTO.class)
                    .headRowNumber(0)
                    .sheet()
                    .doReadSync();
            // 手动跳过前8行数据（因为模板前8行不是数据）
            if (uploadList.size() > 8) {
                uploadList = uploadList.subList(8, uploadList.size());
            } else {
                uploadList = new java.util.ArrayList<>();
            }
            if (uploadList.isEmpty()) {
                throw new ServerException("Excel文件中没有有效数据");
            }
            // 题型中英文映射，兼容中文题型
            for (QuestionBankUploadDTO dto : uploadList) {
                dto.setQuestionType(QuestionTypeEnum.from(dto.getQuestionType()).getCode());
            }
            // 获取当前用户信息
            LoginUser loginUser = SecurityUtils.getLoginUser();
            if (loginUser == null) {
                throw new ServerException("用户未登录");
            }
            
            // 获取当前用户关联的警员信息
            BasePoliceEmp currentPoliceEmp = userOrgUtils.getCurrentUserPoliceEmp();
            if (currentPoliceEmp == null) {
                throw new ServerException("当前用户没有关联的警员信息");
            }
            
            // 获取当前题库中的最大题序
            LambdaQueryWrapper<ExamQuestion> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ExamQuestion::getQuestionBankId, questionBankId)
                    .eq(ExamQuestion::getAvailable, true)
                    .orderByDesc(ExamQuestion::getSerial)
                    .last("LIMIT 1");
            ExamQuestion lastQuestion = examQuestionMapper.selectOne(queryWrapper);
            int currentSerial = lastQuestion != null ? lastQuestion.getSerial() : 0;
            // 批量插入题目
            List<ExamQuestion> questionList = new ArrayList<>();
            LocalDateTime now = LocalDateTime.now();
            for (int i = 0; i < uploadList.size(); i++) {
                QuestionBankUploadDTO uploadDTO = uploadList.get(i);
                ExamTaskValidator.validateUploadData(uploadDTO, i + 9);
                ExamQuestion question = new ExamQuestion();
                question.setQuestionBankId(questionBankId);
                question.setQuestionType(uploadDTO.getQuestionType());
                question.setQuestionStem(uploadDTO.getQuestionContent());
                question.setQuestionAnswer(uploadDTO.getCorrectAnswer());
                String optionJson = buildOptionJson(uploadDTO);
                question.setQuestionOption(optionJson);
                question.setSerial(++currentSerial);
                question.setCreateUserId(currentPoliceEmp.getId()); // 使用警员ID而不是用户ID
                question.setCreateTime(now);
                question.setUpdateUserId(currentPoliceEmp.getId()); // 使用警员ID而不是用户ID
                question.setUpdateTime(now);
                question.setAvailable(true);
                questionList.add(question);
            }
            if (!questionList.isEmpty()) {
                for (ExamQuestion question : questionList) {
                    try {
                        int result = examQuestionMapper.insert(question);
                        if (result <= 0) {
                            throw new ServerException("保存题目失败");
                        }
                    } catch (Exception e) {
                        log.error("[题库上传] 插入题目失败，题目信息: {}", question, e);
                        throw new ServerException("保存题目失败: " + e.getMessage(), e);
                    }
                }
            }
            // 更新题库表
            questionBank = examQuestionBankMapper.selectById(questionBankId);
            if (questionBank != null) {
                questionBank.setUpdateTime(java.time.LocalDateTime.now());
                examQuestionBankMapper.updateById(questionBank);
            }
            // 题目序号已在上传时按文档顺序正确分配，无需重新分配
            
            log.info("[题库上传] 成功导入题目，题库ID: {}, 导入数量: {}, 操作人: {}", questionBankId, questionList.size(), loginUser.getUsername());
            return questionList.size();
        } catch (ServerException e) {
            throw e;
        } catch (Exception e) {
            log.error("[题库上传] 题库上传异常", e);
            throw new ServerException("题库上传失败: " + e.getMessage(), e);
        }
    }



    /**
     * 获取当前用户有权限查看的警员树形结构列表
     *
     * @return 部门警员树形结构列表
     */
    @Override
    public List<DeptPoliceTreeVO> getActivePoliceTreeList() {
        try {
            // 获取当前用户信息
            LoginUser loginUser = SecurityUtils.getLoginUser();
            if (loginUser == null) {
                throw new ServerException("用户未登录");
            }

            // 获取有权限查看的组织ID列表
            Set<String> allowedOrgIds = new HashSet<>();
            if (SecurityUtils.isSuperAdmin()) {
                // 管理员可以查看所有组织
                List<BasePoliceOrg> allOrgs = basePoliceOrgMapper.selectList(
                        new LambdaQueryWrapper<BasePoliceOrg>().eq(BasePoliceOrg::getAvailable, true)
                );
                allowedOrgIds = allOrgs.stream().map(BasePoliceOrg::getId).collect(Collectors.toSet());
            } else {
                // 普通用户只能查看当前单位及下级单位
                String currentUserOrgId = userOrgUtils.getCurrentUserOrgId();
                if (currentUserOrgId != null) {
                    List<String> orgIds = orgRecursiveUtils.getOrgAndChildrenIds(currentUserOrgId);
                    allowedOrgIds = new HashSet<>(orgIds);
                }
            }

            if (allowedOrgIds.isEmpty()) {
                return Collections.emptyList();
            }

            // 查询有权限的组织信息
            List<BasePoliceOrg> orgList = basePoliceOrgMapper.selectByIdList(allowedOrgIds);

            // 构建警员查询条件
            LambdaQueryWrapper<BasePoliceEmp> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BasePoliceEmp::getAvailable, true) // 只查询有效警员
                    .eq(BasePoliceEmp::getStatus, PoliceEmpStatusEnum.ACTIVE.getCode()) // 只查询在职警员
                    .in(BasePoliceEmp::getPoliceOrgId, allowedOrgIds)
                    .orderByAsc(BasePoliceEmp::getName); // 按姓名升序排序

            // 查询警员信息
            List<BasePoliceEmp> policeList = basePoliceEmpMapper.selectList(queryWrapper);

            // 按组织ID分组警员
            Map<String, List<BasePoliceEmp>> policeByOrgMap = policeList.stream()
                    .collect(Collectors.groupingBy(BasePoliceEmp::getPoliceOrgId));

            // 构建部门树形结构
            List<DeptPoliceTreeVO> result = new ArrayList<>();
            Map<String, DeptPoliceTreeVO> deptMap = new HashMap<>();

            // 先创建所有部门节点
            for (BasePoliceOrg org : orgList) {
                DeptPoliceTreeVO deptVO = new DeptPoliceTreeVO();
                deptVO.setDeptId(org.getId());
                deptVO.setDeptName(org.getName());
                deptVO.setDeptCode(org.getCode());
                deptVO.setParentId(org.getParentId());
                deptVO.setChildren(new ArrayList<>());

                // 设置该部门下的警员列表，使用批量VO转换
                List<BasePoliceEmp> deptPoliceList = policeByOrgMap.getOrDefault(org.getId(), Collections.emptyList());
                deptVO.setPoliceList(convertToPoliceListVO(deptPoliceList));
                deptVO.setPoliceCount(deptVO.getPoliceList().size());

                deptMap.put(org.getId(), deptVO);
            }

            // 构建树形结构
            for (DeptPoliceTreeVO deptVO : deptMap.values()) {
                if (deptVO.getParentId() == null || "0".equals(deptVO.getParentId())) {
                    // 根节点
                    result.add(deptVO);
                } else {
                    // 子节点，添加到父节点的children中
                    DeptPoliceTreeVO parent = deptMap.get(deptVO.getParentId());
                    if (parent != null) {
                        parent.getChildren().add(deptVO);
                    } else {
                        // 如果父节点不在权限范围内，则作为根节点
                        result.add(deptVO);
                    }
                }
            }

            return result;

        } catch (ServerException e) {
            throw e;
        } catch (Exception e) {
            log.error("[查询部门警员树形结构] 查询失败", e);
            throw new ServerException("查询部门警员树形结构失败: " + e.getMessage(), e);
        }
    }

    // 批量组织信息Map缓存优化
    private Map<String, String> getOrgNameMap(List<BasePoliceEmp> empList) {
        List<String> orgIds = empList.stream()
                .map(BasePoliceEmp::getPoliceOrgId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        if (orgIds.isEmpty()) return Collections.emptyMap();
        List<BasePoliceOrg> org = basePoliceOrgMapper.selectByIdList(orgIds);
        return org.stream().collect(Collectors.toMap(BasePoliceOrg::getId, BasePoliceOrg::getName));
    }

    /**
     * 将BasePoliceEmp对象列表转换为PoliceListVO对象列表
     *
     * @param empList 包含BasePoliceEmp对象的列表
     * @return 转换后的PoliceListVO对象列表
     */
    private List<PoliceListVO> convertToPoliceListVO(List<BasePoliceEmp> empList) {
        Map<String, String> orgNameMap = getOrgNameMap(empList);
        List<PoliceListVO> voList = new ArrayList<>();
        for (BasePoliceEmp emp : empList) {
            PoliceListVO vo = new PoliceListVO();
            if (emp == null) {
                vo.setName("");
                vo.setType(0);
                vo.setCode("");
                vo.setPhone("");
                vo.setWorkUnit("");
                vo.setDuty("");
                vo.setLabel("");
                vo.setIsSystemUser(false);
                voList.add(vo);
                continue;
            }
            vo.setName(emp.getName());
            vo.setType(emp.getType() != null ? emp.getType() : 0);
            vo.setCode(emp.getCode());
            vo.setPhone(emp.getPhone());
            if (emp.getPoliceOrgId() != null) {
                vo.setWorkUnit(orgNameMap.getOrDefault(emp.getPoliceOrgId(), ""));
            } else {
                vo.setWorkUnit("");
            }
            vo.setDuty("");
            vo.setLabel("");
            vo.setIsSystemUser(emp.getAuthUserId() != null);
            voList.add(vo);
        }
        return voList;
    }

    public ExamTaskEditDetailVO getExamTaskEditDetail(String taskId) {
        ExamTask examTask = this.baseMapper.selectById(taskId);
        if (examTask == null) {
            throw new ServerException("考试任务不存在");
        }
        // 查询题库
        ExamQuestionBank questionBank = examQuestionBankMapper.selectById(examTask.getQuestionBankId());
        // 查询参与人员
        List<ExamTaskEmp> empList = examTaskEmpMapper.selectList(
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<ExamTaskEmp>()
                        .eq(ExamTaskEmp::getTaskId, taskId)
                        .eq(ExamTaskEmp::getAvailable, true)
        );
        // 组装VO
        ExamTaskEditDetailVO vo = new ExamTaskEditDetailVO();
        vo.setTaskId(taskId);
        vo.setName(examTask.getTaskName());
        vo.setRemark(examTask.getRemark());
        vo.setStartTime(examTask.getStartTime());
        vo.setEndTime(examTask.getEndTime());
        vo.setDuration(examTask.getExamDuration());
        vo.setArchive(examTask.getScoreSync());
        vo.setQuestionBankId(examTask.getQuestionBankId());
        vo.setQuestionBankName(questionBank != null ? questionBank.getName() : null);
        vo.setSingleScore(examTask.getSingleChoiceScore());
        vo.setMultiScore(examTask.getMultipleChoiceScore());
        vo.setJudgeScore(examTask.getTrueFalseScore());
        vo.setPassScore(examTask.getPassingScore());
        List<ExamTaskEditDetailVO.Participant> participants = empList.stream()
                .map(emp -> {
                    ExamTaskEditDetailVO.Participant p = new ExamTaskEditDetailVO.Participant();
                    p.setPoliceCode(emp.getPoliceCode());
                    p.setPoliceName(emp.getPoliceName());
                    return p;
                })
                .collect(Collectors.toList());
        vo.setParticipants(participants);
        return vo;
    }

    @Override
    public CommonPageVO<ExamTaskEmpQuestionRecordVO> pageExamTaskEmpQuestionRecords(ExamTaskEmpQuestionRecordPageReqDTO reqDTO) {
        // 1. 查询考试任务信息，获取题库ID
        ExamTask examTask = this.getById(reqDTO.getTaskId());
        if (examTask == null) {
            throw new ServerException("考试任务不存在");
        }
        
        // 2. 查询总数
        int total = examTaskEmpQuestionMapper.countLatestRecordsByEmpAndTask(reqDTO.getEmpId(), reqDTO.getTaskId());
        // 3. 计算分页参数
        int offset = (reqDTO.getPageNum() - 1) * reqDTO.getPageSize();
        // 4. 查询分页数据
        List<ExamTaskEmpQuestion> latestList = examTaskEmpQuestionMapper.selectLatestRecordsByEmpAndTask(
                reqDTO.getEmpId(), reqDTO.getTaskId(), offset, reqDTO.getPageSize());
        // 5. 转VO
        List<ExamTaskEmpQuestionRecordVO> voList = latestList.stream().map(q -> {
            ExamTaskEmpQuestionRecordVO vo = new ExamTaskEmpQuestionRecordVO();
            BeanUtils.copyProperties(q, vo);
            // 根据题库ID和题目序号查询题目详情
            // q.getQuestionId()实际上是题目序号，不是题目的主键ID
            ExamQuestion examQuestion = examQuestionMapper.selectOne(
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<ExamQuestion>()
                    .eq(ExamQuestion::getQuestionBankId, examTask.getQuestionBankId())
                    .eq(ExamQuestion::getSerial, q.getQuestionId().intValue())
                    .eq(ExamQuestion::getAvailable, true)
            );
            if (examQuestion != null && Boolean.TRUE.equals(examQuestion.getAvailable())) {
                ExamTaskEmpQuestionRecordVO.QuestionDetailVO detail = new ExamTaskEmpQuestionRecordVO.QuestionDetailVO();
                detail.setQuestionType(QuestionTypeEnum.from(examQuestion.getQuestionType()).getDesc());
                detail.setQuestionStem(examQuestion.getQuestionStem());
                detail.setQuestionOption(examQuestion.getQuestionOption());
                detail.setCorrectAnswer(examQuestion.getQuestionAnswer());
                // 解析选项JSON，仅处理correctAnswerContent
                Map<String, String> optionMap = new java.util.HashMap<>();
                try {
                    optionMap = JSON.parseObject(
                            examQuestion.getQuestionOption(),
                            new com.alibaba.fastjson2.TypeReference<Map<String, String>>() {
                            }
                    );
                } catch (Exception e) {
                    // ignore
                }
                detail.setCorrectAnswerContent(convertAnswerToContent(examQuestion.getQuestionAnswer(), optionMap));
                vo.setQuestionDetail(detail);
            }
            return vo;
        }).collect(Collectors.toList());
        // 5. 封装分页VO
        CommonPageVO<ExamTaskEmpQuestionRecordVO> voPage = new CommonPageVO<>();
        voPage.setRecords(voList);
        voPage.setTotal((long) total);
        voPage.setCurrent((long) reqDTO.getPageNum());
        voPage.setSize((long) reqDTO.getPageSize());
        return voPage;
    }

    @Override
    public String createOrUpdateExamTask(ExamTaskCreateReqDTO reqDTO) {
        // 有id时，更新，无id时，创建
        if (reqDTO.getId() == null || reqDTO.getId().trim().isEmpty()) {
            return createExamTask(reqDTO);
        } else {
            ExamTaskUpdateReqDTO updateReqDTO = new ExamTaskUpdateReqDTO();
            org.springframework.beans.BeanUtils.copyProperties(reqDTO, updateReqDTO);
            // 手动补充字段映射
            updateReqDTO.setTaskName(reqDTO.getTaskName());
            updateReqDTO.setTaskRemark(reqDTO.getRemark());
            updateReqDTO.setStartTime(reqDTO.getStartTime());
            updateReqDTO.setEndTime(reqDTO.getEndTime());
            updateReqDTO.setDuration(reqDTO.getExamDuration());
            updateReqDTO.setAllowViewScore(reqDTO.getScoreSync() != null && reqDTO.getScoreSync() ? 1 : 0);
            updateReqDTO.setScoreSync(reqDTO.getScoreSync());
            updateReqDTO.setQuestionBankId(reqDTO.getQuestionBankId());
            updateReqDTO.setSingleChoiceScore(reqDTO.getSingleChoiceScore());
            updateReqDTO.setMultipleChoiceScore(reqDTO.getMultipleChoiceScore());
            updateReqDTO.setJudgmentScore(reqDTO.getTrueFalseScore());
            updateReqDTO.setPassScore(reqDTO.getPassingScore());
            updateExamTask(updateReqDTO);
            return reqDTO.getId();
        }
    }

    /**
     * 根据题库ID和题型统计题目数量
     *
     * @param questionBankId 题库ID
     * @param type           题型（"single"/"multiple"/"judgment"）
     * @return 题目数量
     */
    /**
     * 根据题目类型枚举查询题目数量
     * @param questionBankId 题库ID
     * @param questionType 题目类型枚举
     * @return 题目数量
     */
    private Long getQuestionCountByQuestionType(String questionBankId, QuestionTypeEnum questionType) {
        if (questionBankId == null || questionType == null) {
            return 0L;
        }
        
        LambdaQueryWrapper<ExamQuestion> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ExamQuestion::getQuestionBankId, questionBankId)
                .and(w -> w.eq(ExamQuestion::getQuestionType, questionType.getCode())
                        .or().eq(ExamQuestion::getQuestionType, questionType.getDesc()))
                .eq(ExamQuestion::getAvailable, true);
        
        Long count = examQuestionMapper.selectCount(wrapper);
        return count == null ? 0L : count;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer uploadQuestionBank(MultipartFile file, String name, String description) {
        try {
            // 参数校验
            if (file == null || file.isEmpty()) {
                throw new ServerException("上传文件不能为空");
            }
            if (name == null || name.trim().isEmpty()) {
                throw new ServerException("题库名称不能为空");
            }
            // 查找同名题库且可用
            LambdaQueryWrapper<ExamQuestionBank> nameWrapper = new LambdaQueryWrapper<>();
            nameWrapper.eq(ExamQuestionBank::getName, name).eq(ExamQuestionBank::getAvailable, true);
            ExamQuestionBank exist = examQuestionBankMapper.selectOne(nameWrapper);
            if (exist != null) {
                throw new ServerException("同名题库已存在，请勿重复上传");
            }
            
            // 获取当前用户对应的警员信息
            BasePoliceEmp currentPoliceEmp = userOrgUtils.getCurrentUserPoliceEmp();
            if (currentPoliceEmp == null) {
                throw new ServerException("当前用户未关联警员信息，无法创建题库");
            }
            
            LocalDateTime now = LocalDateTime.now();
            // 用雪花算法生成题库ID
            String newId = IdUtil.getSnowflake().nextIdStr();

            // 生成唯一文件名（用于MinIO存储）
            String originalFilename = file.getOriginalFilename();
            String fileExt = "";
            if (originalFilename != null && originalFilename.contains(".")) {
                fileExt = originalFilename.substring(originalFilename.lastIndexOf("."));
            }
            String fileName = System.currentTimeMillis() + fileExt;

            // 上传到 MinIO
            try (InputStream inputStream = file.getInputStream()) {
                String minioBucket = minioConfig.getMinioBucket();
                
                // 检查存储桶是否存在，如果不存在则创建
                boolean bucketExists = minioClient.bucketExists(
                    BucketExistsArgs.builder()
                        .bucket(minioBucket)
                        .build()
                );
                
                if (!bucketExists) {
                    log.info("[题库上传] MinIO存储桶 {} 不存在，正在创建...", minioBucket);
                    minioClient.makeBucket(
                        MakeBucketArgs.builder()
                            .bucket(minioBucket)
                            .build()
                    );
                    log.info("[题库上传] MinIO存储桶 {} 创建成功", minioBucket);
                }
                
                // 添加file/路径前缀，与domain配置保持一致
                String objectName = "file/" + fileName;
                
                minioClient.putObject(
                    PutObjectArgs.builder()
                        .bucket(minioBucket)
                        .object(objectName)
                        .stream(inputStream, file.getSize(), -1)
                        .contentType(file.getContentType())
                        .build()
                );
                
            } catch (Exception e) {
                log.error("[题库上传] MinIO上传失败", e);
                throw new ServerException("题库文件上传MinIO失败: " + e.getMessage(), e);
            }

            // 生成文件URL并确保包含正确的/minio/前缀
            String fileUrl = minioConfig.getDomain() + fileName;
            if (!fileUrl.contains("/minio/")) {
                // 如果URL不包含/minio/前缀，则替换domain部分
                fileUrl = fileUrl.replace("/medox/", "/minio/business/file/");
            }

            // 自动新建题库，fileName存原始文件名，fileUrl存外链
            ExamQuestionBank newBank = new ExamQuestionBank();
            newBank.setId(newId);
            newBank.setName(name);
            newBank.setDescription(description);
            newBank.setAvailable(true);
            newBank.setCreateTime(now);
            newBank.setUpdateTime(now);
            newBank.setCreateUserId(currentPoliceEmp.getId()); // 使用警员ID而不是用户ID
            newBank.setUpdateUserId(currentPoliceEmp.getId()); // 使用警员ID而不是用户ID
            newBank.setFileUrl(fileUrl); // 外链URL
            newBank.setFileName(originalFilename); // 原始文件名
            newBank.setStatus("01"); // 新增：默认启用
            examQuestionBankMapper.insert(newBank);
            String questionBankId = newBank.getId();

            log.info("[题库上传] 文件已上传MinIO: {}, URL: {}", originalFilename, fileUrl);

            // 用新题库ID导入题目，直接用 MultipartFile 的 InputStream
            try (java.io.InputStream fis = file.getInputStream()) {
                return uploadQuestionBank(fis, questionBankId);
            }
        } catch (ServerException e) {
            throw e;
        } catch (Exception e) {
            log.error("[题库上传] 题库上传异常", e);
            throw new ServerException("题库上传失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Boolean enableOrDisableQuestionBank(String questionBankId, Boolean enable) {
        // 权限校验：只有超级管理员才能操作
        if (!SecurityUtils.isSuperAdmin()) {
            throw new ServerException("权限不足，只有管理员才能操作题库启用/停用");
        }
        ExamQuestionBank questionBank = examQuestionBankMapper.selectById(questionBankId);
        if (questionBank == null || !questionBank.getAvailable()) {
            throw new ServerException("题库不存在或已禁用");
        }
        // 获取当前用户ID
        String userId = Objects.requireNonNull(SecurityUtils.getLoginUser()).getUserId();
        if (Boolean.TRUE.equals(enable)) {
            questionBank.setStatus("01"); // 01为启用
        } else {
            questionBank.setStatus("02"); // 02为禁用
        }
        questionBank.setUpdateUserId(userId);
        questionBank.setUpdateTime(LocalDateTime.now());
        int result = examQuestionBankMapper.updateById(questionBank);
        return result > 0;
    }

    @Override
    public QuestionBankVO getQuestionBankDetail(String questionBankId) {
        ExamQuestionBank entity = examQuestionBankMapper.selectById(questionBankId);
        if (entity == null || !entity.getAvailable()) {
            throw new ServerException("题库不存在或已被删除");
        }
        QuestionBankVO vo = convertQuestionBankToVO(entity);
        // 查询题库下所有题目，按序号排序
        LambdaQueryWrapper<ExamQuestion> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ExamQuestion::getQuestionBankId, questionBankId)
                .eq(ExamQuestion::getAvailable, true)
                .orderByAsc(ExamQuestion::getSerial);
        List<ExamQuestion> questionList = examQuestionMapper.selectList(wrapper);
        List<QuestionBankVO.QuestionVO> voList = questionList.stream().map(q -> {
            QuestionBankVO.QuestionVO qvo = new QuestionBankVO.QuestionVO();
            qvo.setId(q.getId());
            // 题型转中文
            QuestionTypeEnum typeEnum = QuestionTypeEnum.from(q.getQuestionType());
            String questionTypeDesc = typeEnum.getDesc();
            qvo.setQuestionType(questionTypeDesc);
            qvo.setQuestionStem(q.getQuestionStem());
            qvo.setQuestionOption(q.getQuestionOption());
            // 多选题答案用逗号分隔（用枚举判断）
            String answer = q.getQuestionAnswer();
            if (typeEnum == QuestionTypeEnum.MULTIPLE_CHOICE && answer != null && !answer.contains(",")) {
                answer = String.join(",", answer.split(""));
            }
            qvo.setSerial(q.getSerial());
            // 设置正确答案（没有taskId时显示正确答案）
            qvo.setCorrectAnswer(answer);
            // 设置题目分数（使用默认分数，如需根据任务获取分数请使用getQuestionBankDetailByTask方法）
            switch (typeEnum) {
                case SINGLE_CHOICE:
                    qvo.setScore(2); // 单选题默认2分
                    break;
                case MULTIPLE_CHOICE:
                    qvo.setScore(3); // 多选题默认3分
                    break;
                case TRUE_FALSE:
                    qvo.setScore(1); // 判断题默认1分
                    break;
                default:
                    qvo.setScore(0); // 未知类型默认0分
                    break;
            }
            return qvo;
        }).collect(java.util.stream.Collectors.toList());
        vo.setQuestions(voList);
        // 精简fileUrl拼接逻辑
        String fileUrl = vo.getFileUrl();
        if (fileUrl != null && !fileUrl.startsWith("http")) {
            if (downloadDomain != null && !downloadDomain.isEmpty()) {
                vo.setFileUrl(downloadDomain + fileUrl);
            } else {
                vo.setFileUrl(fileUrl);
            }
        } else {
            vo.setFileUrl(fileUrl);
        }
        return vo;
    }

    /**
     * 根据考试任务获取题库详情（题目分数根据任务配置设置）
     * @param taskId 考试任务ID
     * @return 题库详情VO
     */
    public QuestionBankVO getQuestionBankDetailByTask(String taskId) {
        // 1. 查询考试任务信息
        ExamTask examTask = this.baseMapper.selectById(taskId);
        if (examTask == null) {
            throw new ServerException("考试任务不存在");
        }
        
        String questionBankId = examTask.getQuestionBankId();
        
        // 2. 查询题库信息
        ExamQuestionBank entity = examQuestionBankMapper.selectById(questionBankId);
        if (entity == null || !entity.getAvailable()) {
            throw new ServerException("题库不存在或已被删除");
        }
        
        QuestionBankVO vo = convertQuestionBankToVO(entity);
        
        // 3. 查询题库下所有题目，按序号排序
        LambdaQueryWrapper<ExamQuestion> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ExamQuestion::getQuestionBankId, questionBankId)
                .eq(ExamQuestion::getAvailable, true)
                .orderByAsc(ExamQuestion::getSerial);
        List<ExamQuestion> questionList = examQuestionMapper.selectList(wrapper);
        
        List<QuestionBankVO.QuestionVO> voList = questionList.stream().map(q -> {
            QuestionBankVO.QuestionVO qvo = new QuestionBankVO.QuestionVO();
            qvo.setId(q.getId());
            // 题型转中文
            QuestionTypeEnum typeEnum = QuestionTypeEnum.from(q.getQuestionType());
            String questionTypeDesc = typeEnum.getDesc();
            qvo.setQuestionType(questionTypeDesc);
            qvo.setQuestionStem(q.getQuestionStem());
            qvo.setQuestionOption(q.getQuestionOption());
            // 多选题答案用逗号分隔（用枚举判断）
            String answer = q.getQuestionAnswer();
            if (typeEnum == QuestionTypeEnum.MULTIPLE_CHOICE && answer != null && !answer.contains(",")) {
                answer = String.join(",", answer.split(""));
            }
            qvo.setSerial(q.getSerial());
            // 有taskId时不设置正确答案（不显示正确答案）
            // qvo.setCorrectAnswer(null); // 默认为null，不需要显式设置
            
            // 根据考试任务配置设置题目分数
            switch (typeEnum) {
                case SINGLE_CHOICE:
                    qvo.setScore(examTask.getSingleChoiceScore() != null ? examTask.getSingleChoiceScore() : 2);
                    break;
                case MULTIPLE_CHOICE:
                    qvo.setScore(examTask.getMultipleChoiceScore() != null ? examTask.getMultipleChoiceScore() : 3);
                    break;
                case TRUE_FALSE:
                    qvo.setScore(examTask.getTrueFalseScore() != null ? examTask.getTrueFalseScore() : 1);
                    break;
                default:
                    qvo.setScore(0); // 未知类型默认0分
                    break;
            }
            return qvo;
        }).collect(java.util.stream.Collectors.toList());
        
        vo.setQuestions(voList);
        
        // 精简fileUrl拼接逻辑
        String fileUrl = vo.getFileUrl();
        if (fileUrl != null && !fileUrl.startsWith("http")) {
            if (downloadDomain != null && !downloadDomain.isEmpty()) {
                vo.setFileUrl(downloadDomain + fileUrl);
            } else {
                vo.setFileUrl(fileUrl);
            }
        } else {
            vo.setFileUrl(fileUrl);
        }
        
        return vo;
    }

    /**
     * 修改题库信息，支持可选文件替换
     *
     * @param reqDTO 修改请求
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateQuestionBank(QuestionBankUpdateReqDTO reqDTO) {
        ExamQuestionBank bank = examQuestionBankMapper.selectById(reqDTO.getQuestionBankId());
        if (bank == null || !bank.getAvailable()) {
            throw new ServerException("题库不存在或已被删除");
        }
        
        // 获取当前用户对应的警员信息
        BasePoliceEmp currentPoliceEmp = userOrgUtils.getCurrentUserPoliceEmp();
        
        bank.setName(reqDTO.getName());
        bank.setDescription(reqDTO.getDescription());
        bank.setUpdateTime(LocalDateTime.now());
        bank.setUpdateUserId(currentPoliceEmp != null ? currentPoliceEmp.getId() : SecurityUtils.getLoginUserId()); // 使用警员ID而不是用户ID
        MultipartFile file = reqDTO.getFile();
        if (file != null && !file.isEmpty()) {
            String originalFilename = file.getOriginalFilename();
            String fileExtension = "";
            if (originalFilename != null && originalFilename.contains(".")) {
                fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
            }
            String fileName = bank.getId() + fileExtension;
            File destFile = new File(uploadPath + File.separator + fileName);
            try {
                file.transferTo(destFile);
            } catch (Exception e) {
                throw new ServerException("文件保存失败: " + e.getMessage(), e);
            }
            String fileUrl = "/upload/" + fileName;
            bank.setFileUrl(fileUrl);
            bank.setFileName(originalFilename);
        }
        examQuestionBankMapper.updateById(bank);
        return true;
    }
    
    /**
     * 计算考试分数（基于本次上传的答题记录）
     * @param taskId 考试任务ID
     * @param questionAnswers 本次上传的答题记录
     * @return 考试分数
     */
    private Integer calculateExamScore(String taskId, List<ExamTaskEmpQuestionUploadDTO.QuestionAnswer> questionAnswers) {
        try {
            log.info("[计算考试分数] 开始计算，taskId: {}, 答题记录数量: {}", taskId, questionAnswers.size());
            
            // 1. 查询考试任务信息，获取题目配置
            ExamTask examTask = this.getById(taskId);
            if (examTask == null) {
                throw new ServerException("考试任务不存在");
            }
            
            log.info("[计算考试分数] 考试任务配置 - 单选题分数: {}, 多选题分数: {}, 判断题分数: {}", 
                    examTask.getSingleChoiceScore(), examTask.getMultipleChoiceScore(), examTask.getTrueFalseScore());
            
            if (questionAnswers.isEmpty()) {
                log.warn("[计算考试分数] 答题记录为空，返回0分");
                return 0;
            }
            
            // 2. 计算总分
            int totalScore = 0;
            int correctCount = 0;
            
            for (ExamTaskEmpQuestionUploadDTO.QuestionAnswer qa : questionAnswers) {
                // 根据题库ID和题目序号查询题目信息
                // qa.getQuestionId()实际上是题目序号，不是题目的主键ID
                ExamQuestion question = examQuestionMapper.selectOne(
                    new LambdaQueryWrapper<ExamQuestion>()
                        .eq(ExamQuestion::getQuestionBankId, examTask.getQuestionBankId())
                        .eq(ExamQuestion::getSerial, qa.getSerial())
                        .eq(ExamQuestion::getAvailable, true)
                );
                if (question != null) {
                    // 判断答案是否正确
                    boolean isCorrect = isAnswerCorrect(question, qa.getAnswer());
                    log.info("[计算考试分数] 题目序号: {}, 题目ID: {}, 题目类型: {}, 正确答案: {}, 用户答案: {}, 是否正确: {}", 
                            qa.getSerial(), question.getId(), question.getQuestionType(), question.getQuestionAnswer(), qa.getAnswer(), isCorrect);
                    
                    if (isCorrect) {
                        correctCount++;
                        int questionScore = 0;
                        // 根据题目类型给分
                        QuestionTypeEnum questionType = QuestionTypeEnum.from(question.getQuestionType());
                        switch (questionType) {
                            case SINGLE_CHOICE:
                                questionScore = examTask.getSingleChoiceScore() != null ? examTask.getSingleChoiceScore() : 2;
                                totalScore += questionScore;
                                break;
                            case MULTIPLE_CHOICE:
                                questionScore = examTask.getMultipleChoiceScore() != null ? examTask.getMultipleChoiceScore() : 3;
                                totalScore += questionScore;
                                break;
                            case TRUE_FALSE:
                                questionScore = examTask.getTrueFalseScore() != null ? examTask.getTrueFalseScore() : 1;
                                totalScore += questionScore;
                                break;
                            default:
                                log.warn("[计算考试分数] 未知题目类型: {}", question.getQuestionType());
                                break;
                        }
                        log.info("[计算考试分数] 题目得分: {}, 当前总分: {}", questionScore, totalScore);
                    }
                }
            }
            
            log.info("[计算考试分数] 计算完成 - 总题数: {}, 正确题数: {}, 总分: {}", questionAnswers.size(), correctCount, totalScore);
            return totalScore;
        } catch (Exception e) {
            log.error("[计算考试分数] 计算失败，taskId: {}", taskId, e);
            return 0;
        }
    }
    
    /**
     * 判断答案是否正确
     * @param question 题目信息
     * @param userAnswer 用户答案
     * @return 是否正确
     */
    private boolean isAnswerCorrect(ExamQuestion question, String userAnswer) {
        if (question == null || userAnswer == null) {
            return false;
        }
        
        String correctAnswer = question.getQuestionAnswer();
        if (correctAnswer == null) {
            return false;
        }
        
        // 处理多选题答案比较（需要考虑选项顺序）
        QuestionTypeEnum questionType = QuestionTypeEnum.from(question.getQuestionType());
        if (questionType == QuestionTypeEnum.MULTIPLE_CHOICE) {
            return compareMultipleChoiceAnswers(correctAnswer, userAnswer);
        }
        
        // 单选题和判断题直接比较
        return correctAnswer.trim().equalsIgnoreCase(userAnswer.trim());
    }
    
    /**
     * 比较多选题答案（忽略选项顺序）
     * @param correctAnswer 正确答案
     * @param userAnswer 用户答案
     * @return 是否正确
     */
    private boolean compareMultipleChoiceAnswers(String correctAnswer, String userAnswer) {
        if (correctAnswer == null || userAnswer == null) {
            return false;
        }
        // 标准答案和用户答案都转为逗号分隔的大写集合
        correctAnswer = correctAnswer.replaceAll("[，\\s]", ","); // 替换中文逗号和空格为英文逗号
        userAnswer = userAnswer.replaceAll("[，\\s]", ",");
        if (!correctAnswer.contains(",") && correctAnswer.length() > 1) {
            correctAnswer = String.join(",", correctAnswer.split(""));
        }
        if (!userAnswer.contains(",") && userAnswer.length() > 1) {
            userAnswer = String.join(",", userAnswer.split(""));
        }
        java.util.Set<String> correctSet = java.util.Arrays.stream(correctAnswer.split(","))
            .map(String::trim)
            .filter(s -> !s.isEmpty())
            .map(String::toUpperCase)
            .collect(java.util.stream.Collectors.toSet());
        java.util.Set<String> userSet = java.util.Arrays.stream(userAnswer.split(","))
            .map(String::trim)
            .filter(s -> !s.isEmpty())
            .map(String::toUpperCase)
            .collect(java.util.stream.Collectors.toSet());
        return correctSet.equals(userSet);
    }

    /**
     * 获取题库用于编辑的详情信息，便于前端页面反显和数据回填
     *
     * @param questionBankId 题库ID
     * @return 题库编辑详情VO
     */
    @Override
    public QuestionBankEditDetailVO getQuestionBankEditDetail(String questionBankId) {
        ExamQuestionBank bank = examQuestionBankMapper.selectById(questionBankId);
        if (bank == null || !bank.getAvailable()) {
            throw new ServerException("题库不存在或已被删除");
        }
        QuestionBankEditDetailVO vo = new QuestionBankEditDetailVO();
        vo.setQuestionBankId(bank.getId());
        vo.setName(bank.getName());
        vo.setDescription(bank.getDescription());
        // 精简fileUrl拼接逻辑
        String fileUrl = bank.getFileUrl();
        if (fileUrl != null && !fileUrl.startsWith("http")) {
            if (downloadDomain != null && !downloadDomain.isEmpty()) {
                vo.setFileUrl(downloadDomain + fileUrl);
            } else {
                vo.setFileUrl(fileUrl);
            }
        } else {
            vo.setFileUrl(fileUrl);
        }
        return vo;
    }

    /**
     * 将答案key（如A,B）转为内容（如A. 2, B. 3）
     */
    private String convertAnswerToContent(String answer, Map<String, String> optionMap) {
        if (answer == null || answer.isEmpty() || optionMap == null || optionMap.isEmpty()) {
            return answer;
        }
        String[] keys = answer.split(",");
        java.util.List<String> contentList = new java.util.ArrayList<>();
        for (String key : keys) {
            key = key.trim();
            if (optionMap.containsKey(key)) {
                contentList.add(key + ". " + optionMap.get(key));
            } else {
                contentList.add(key);
            }
        }
        return String.join(", ", contentList);
    }

    @Override
    public ExamQuestionBank getExamQuestionBankEntity(String questionBankId) {
        return examQuestionBankMapper.selectById(questionBankId);
    }

    @Override
    public String getUploadPath() {
        return uploadPath;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer uploadQuestionBank(MultipartFile file, String questionBankId, String name, String description) {
        if (file == null || file.isEmpty()) {
            throw new ServerException("上传文件不能为空");
        }
        ExamQuestionBank bank = examQuestionBankMapper.selectById(questionBankId);
        if (bank == null || !bank.getAvailable()) {
            throw new ServerException("题库不存在或已被删除");
        }
        // 1. 删除MinIO原文件（如果有fileUrl）
        String oldFileUrl = bank.getFileUrl();
        if (oldFileUrl != null && !oldFileUrl.isEmpty()) {
            try {
                String minioBucket = minioConfig.getMinioBucket();
                
                // 检查存储桶是否存在
                boolean bucketExists = minioClient.bucketExists(
                    io.minio.BucketExistsArgs.builder()
                        .bucket(minioBucket)
                        .build()
                );
                
                if (bucketExists) {
                    String objectName = oldFileUrl.replace(minioConfig.getDomain(), "");
                    minioClient.removeObject(
                        io.minio.RemoveObjectArgs.builder()
                            .bucket(minioBucket)
                            .object(objectName)
                            .build()
                    );
                    log.info("[题库修改] 成功删除MinIO文件: {}", objectName);
                } else {
                    log.warn("[题库修改] MinIO存储桶 {} 不存在，跳过删除文件操作", minioBucket);
                }
            } catch (Exception e) {
                log.warn("[题库修改] 删除MinIO原文件失败: {}", oldFileUrl, e);
            }
        }
        // 2. 删除数据库题库表和题目表相关数据
        examQuestionBankMapper.deleteById(questionBankId);
        LambdaQueryWrapper<ExamQuestion> questionWrapper = new LambdaQueryWrapper<>();
        questionWrapper.eq(ExamQuestion::getQuestionBankId, questionBankId);
        examQuestionMapper.delete(questionWrapper);
        // 3. 重新走新增逻辑
        return uploadQuestionBank(file, name, description);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean uploadExamTaskEmpQuestionRecord(ExamTaskEmpQuestionUploadDTO record) {
        if (record == null) {
            throw new ServerException("上传数据不能为空");
        }
        
        try {
            // 获取当前登录用户ID
            String currentEmpId = userOrgUtils.getCurrentUserPoliceEmp() != null ? userOrgUtils.getCurrentUserPoliceEmp().getId() : null;
            
            // 验证题目答案列表不为空

            if (record.getQuestionAnswers() == null || record.getQuestionAnswers().isEmpty()) {
                throw new ServerException("题目答案列表不能为空");
            }
            
            // 1. 先创建批次记录以获取批次ID
            // 获取考试任务信息，包括合格分数
            ExamTask examTask = this.getById(record.getTaskId());
            if (examTask == null) {
                throw new ServerException("考试任务不存在");
            }
            Integer passingScore = examTask.getPassingScore() != null ? examTask.getPassingScore() : 60; // 默认60分及格
            
            // 获取答题开始时间和结束时间
            LocalDateTime examStartTime = record.getStartTime();
            LocalDateTime examEndTime = record.getEndTime();
            
            // 如果没有提供时间，则使用当前时间作为默认值
            if (examStartTime == null) {
                examStartTime = LocalDateTime.now();
            }
            if (examEndTime == null) {
                examEndTime = LocalDateTime.now();
            }
            
            // 1. 先创建批次记录（暂时不设置分数）
            ExamTaskEmpBatch batchEntity = new ExamTaskEmpBatch();
            batchEntity.setTaskId(record.getTaskId());
            batchEntity.setEmpId(currentEmpId);
            batchEntity.setStartTime(examStartTime);
            batchEntity.setEndTime(examEndTime);
            batchEntity.setScore(0); // 先设置为0，后面计算完再更新
            batchEntity.setStatus(ExamStatusEnum.COMPLETED.getCode());
            batchEntity.setPassExam(false); // 先设置为false，后面计算完再更新
            batchEntity.setCreateTime(LocalDateTime.now());
            batchEntity.setUpdateTime(LocalDateTime.now());
            batchEntity.setAvailable(true);
            
            examTaskEmpBatchMapper.insert(batchEntity);
            String batchId = batchEntity.getId();
            
            // 2. 保存答题记录，直接设置批次ID
            for (ExamTaskEmpQuestionUploadDTO.QuestionAnswer qa : record.getQuestionAnswers()) {
                // 根据题库ID和题目序号查询题目信息
                LambdaQueryWrapper<ExamQuestion> questionQueryWrapper = new LambdaQueryWrapper<>();
                questionQueryWrapper.eq(ExamQuestion::getQuestionBankId, examTask.getQuestionBankId().toString())
                                  .eq(ExamQuestion::getSerial, qa.getSerial().intValue())
                                  .eq(ExamQuestion::getAvailable, true);
                ExamQuestion question = examQuestionMapper.selectOne(questionQueryWrapper);
                if (question == null) {
                    log.warn("[保存答题记录] 题目不存在，题库ID: {}, 题目序号: {}", examTask.getQuestionBankId(), qa.getSerial());
                    continue;
                }
                ExamTaskEmpQuestion entity = new ExamTaskEmpQuestion();
                entity.setTaskId(record.getTaskId());
                entity.setEmpId(currentEmpId);
                // 使用题目的serial作为questionId，而不是题目的主键id
                entity.setQuestionId(question.getSerial().longValue());
                entity.setAnswer(qa.getAnswer());
                // 判断答案是否正确
                Boolean isCorrect = checkAnswerCorrect(examTask.getQuestionBankId().toString(), qa.getSerial().longValue(), qa.getAnswer());
                entity.setAnswerRight(isCorrect);
                // 设置题目序号
                entity.setSerial(qa.getSerial());
                // 设置批次ID
                entity.setTaskEmpBatchId(batchId);
                entity.setCreateTime(LocalDateTime.now());
                entity.setUpdateTime(LocalDateTime.now());
                entity.setAvailable(true);
                examTaskEmpQuestionMapper.insert(entity);
            }
            
            // 3. 计算考试分数（基于本次上传的答题记录）
            Integer score = calculateExamScore(record.getTaskId(), record.getQuestionAnswers());
            
            // 4. 更新批次记录的分数和通过状态
            batchEntity.setScore(score);
            batchEntity.setPassExam(score >= passingScore);
            batchEntity.setUpdateTime(LocalDateTime.now());
            examTaskEmpBatchMapper.updateById(batchEntity);
            
            // 5. 获取任务ID
            String taskId = record.getTaskId();
            
            // 6. 更新tbl_exam_task_emp表的考试信息
            LambdaQueryWrapper<ExamTaskEmp> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ExamTaskEmp::getTaskId, taskId)
                      .eq(ExamTaskEmp::getEmpId, currentEmpId);
            
            ExamTaskEmp examTaskEmp = examTaskEmpMapper.selectOne(queryWrapper);
            if (examTaskEmp != null) {
                LocalDateTime now = LocalDateTime.now();
                
                // 更新考试状态
                examTaskEmp.setStatus(score >= passingScore ? ExamTaskEmpStatusEnum.PASSED.getCode() : ExamTaskEmpStatusEnum.FAILED.getCode());
                
                // 更新考试分数
                examTaskEmp.setScore(score);
                
                // 更新实际考试时间
                examTaskEmp.setStartTime(examStartTime);
                examTaskEmp.setEndTime(examEndTime);
                
                // 更新当前批次ID
                examTaskEmp.setTaskEmpCurBatchId(batchEntity.getId());
                
                // 更新修改时间
                examTaskEmp.setUpdateTime(now);
                
                examTaskEmpMapper.updateById(examTaskEmp);
            }
            
            return true;
        } catch (Exception e) {
            log.error("[上传答题记录] 保存失败", e);
            throw new ServerException("保存答题记录失败: " + e.getMessage());
        }
    }

    /**
     * 检查答案是否正确
     * @param questionBankId 题库ID
     * @param questionSerial 题目序号
     * @param userAnswer 用户答案
     * @return 是否正确
     */
    private Boolean checkAnswerCorrect(String questionBankId, Long questionSerial, String userAnswer) {
        if (questionBankId == null || questionSerial == null || userAnswer == null) {
            return false;
        }
        
        try {
            // 根据题库ID和题目序号查询题目
            LambdaQueryWrapper<ExamQuestion> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ExamQuestion::getQuestionBankId, questionBankId)
                       .eq(ExamQuestion::getSerial, questionSerial.intValue())
                       .eq(ExamQuestion::getAvailable, true);
            ExamQuestion question = examQuestionMapper.selectOne(queryWrapper);
            if (question == null) {
                return false;
            }
            
            String correctAnswer = question.getQuestionAnswer();
            if (correctAnswer == null) {
                return false;
            }
            
            // 去除空格并转换为大写进行比较
            String normalizedUserAnswer = userAnswer.trim().toUpperCase();
            String normalizedCorrectAnswer = correctAnswer.trim().toUpperCase();
            
            return normalizedUserAnswer.equals(normalizedCorrectAnswer);
        } catch (Exception e) {
            log.error("[检查答案] 检查答案是否正确失败，题库ID: {}, 题目序号: {}, 用户答案: {}", questionBankId, questionSerial, userAnswer, e);
            return false;
        }
    }
}
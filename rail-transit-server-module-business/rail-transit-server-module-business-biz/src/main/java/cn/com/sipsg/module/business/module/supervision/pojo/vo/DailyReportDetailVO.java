package cn.com.sipsg.module.business.module.supervision.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 每日报备详情VO
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
@Schema(description = "每日报备详情VO")
public class DailyReportDetailVO {

    @Schema(description = "主键ID")
    private String id;

    @Schema(description = "创建人ID")
    private String createUserId;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改人ID")
    private String updateUserId;

    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    @Schema(description = "是否有效")
    private Boolean available;

    @Schema(description = "填报时间")
    private LocalDateTime formTime;

    @Schema(description = "单位ID")
    private String policeOrgId;

    @Schema(description = "负责人姓名")
    private String responsibleName;

    @Schema(description = "负责人岗位")
    private String responsiblePosition;

    @Schema(description = "负责人电话")
    private String responsiblePhone;

    @Schema(description = "负责人值守频道")
    private String responsibleChannel;

    @Schema(description = "警力-民警数量")
    private Integer strengthPolice;

    @Schema(description = "警力-辅警数量")
    private Integer strengthAuxiliaryPolice;

    @Schema(description = "警力-武警数量")
    private Integer strengthArmedPolice;

    @Schema(description = "警力-其他数量")
    private Integer strengthOther;

    @Schema(description = "装备-枪支数量")
    private Integer equipmentGun;

    @Schema(description = "装备-枪支情况")
    private String equipmentGunMsg;

    @Schema(description = "装备-GPS/北斗")
    private String equipmentGpsMsg;

    @Schema(description = "装备-图像传输设备")
    private String equipmentImgMsg;

    @Schema(description = "车辆-摩托车")
    private Integer vehicleMotorbike;

    @Schema(description = "车辆-电动车")
    private Integer vehicleElectricCar;

    @Schema(description = "巡防投入-车辆")
    private Integer patrolVehicle;

    @Schema(description = "巡防投入-警务通")
    private Integer patrolPoliceCommunication;

    @Schema(description = "巡防投入-PDA")
    private Integer patrolPda;

    @Schema(description = "巡防投入-盘查车辆")
    private Integer patrolQuestionVehicle;

    @Schema(description = "巡防投入-抓获犯罪人员")
    private Integer patrolArrestEmp;

    @Schema(description = "重点部位投入-部位数量")
    private Integer importantPart;

    @Schema(description = "重点部位投入-民警数量")
    private Integer importantPolice;

    @Schema(description = "重点部位投入-辅警数量")
    private Integer importantAuxiliaryPolice;

    @Schema(description = "重点部位投入-武警数量")
    private Integer importantArmedPolice;

    @Schema(description = "重点部位投入-车辆数量")
    private Integer importantVehicle;

    @Schema(description = "重点部位投入-武器数量")
    private Integer importantWeapon;

    @Schema(description = "备注")
    private String memos;
}
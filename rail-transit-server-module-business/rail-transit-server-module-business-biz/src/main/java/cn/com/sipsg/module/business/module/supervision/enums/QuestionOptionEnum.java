package cn.com.sipsg.module.business.module.supervision.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 题目选项枚举
 * 统一管理A、B、C、D、E、F、G等选项
 */
@Getter
@AllArgsConstructor
public enum QuestionOptionEnum {
    A("A", "选项A"),
    B("B", "选项B"),
    C("C", "选项C"),
    D("D", "选项D"),
    E("E", "选项E"),
    F("F", "选项F"),
    G("G", "选项G");

    private final String code;
    private final String label;
} 
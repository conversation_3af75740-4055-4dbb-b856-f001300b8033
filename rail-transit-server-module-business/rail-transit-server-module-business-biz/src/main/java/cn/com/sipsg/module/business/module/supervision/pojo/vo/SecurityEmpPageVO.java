package cn.com.sipsg.module.business.module.supervision.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 安检员分页查询结果VO
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "安检员分页查询结果VO")
public class SecurityEmpPageVO {

    @Schema(description = "安检员ID")
    private String id;

    @Schema(description = "姓名")
    private String name;

    @Schema(description = "性别")
    private String gender;

    @Schema(description = "身份证号")
    private String idCardNo;

    @Schema(description = "所属公司")
    private String companyName;

    @Schema(description = "联系方式")
    private String phone;

    @Schema(description = "年龄")
    private Integer age;

    @Schema(description = "是否培训")
    private Boolean hasTrain;

    @Schema(description = "在职状态, 01:在职, 02:离职, 03:历史人员")
    private String employmentStatus;

    @Schema(description = "安检证号")
    private String securityCardNo;

    @Schema(description = "保安证号")
    private String safeCardNo;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private String createBy;
}
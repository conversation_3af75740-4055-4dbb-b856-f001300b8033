package cn.com.sipsg.module.business.module.supervision.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@Schema(description = "考试任务编辑详情VO")
public class ExamTaskEditDetailVO {
    @Schema(description = "考试任务ID")
    private String taskId;

    @Schema(description = "考试名称")
    private String name;

    @Schema(description = "考试备注")
    private String remark;

    @Schema(description = "考试开始时间")
    private LocalDateTime startTime;

    @Schema(description = "考试结束时间")
    private LocalDateTime endTime;

    @Schema(description = "考试时长（分钟）")
    private Integer duration;

    @Schema(description = "成绩计入档案")
    private Boolean archive;

    @Schema(description = "题库ID")
    private String questionBankId;

    @Schema(description = "题库名称")
    private String questionBankName;

    @Schema(description = "单选分值")
    private Integer singleScore;

    @Schema(description = "多选分值")
    private Integer multiScore;

    @Schema(description = "判断分值")
    private Integer judgeScore;

    @Schema(description = "合格分值")
    private Integer passScore;

    @Schema(description = "参与人员列表")
    private java.util.List<Participant> participants;

    @Data
    public static class Participant {
        @Schema(description = "警号")
        private String policeCode;
        @Schema(description = "姓名")
        private String policeName;
    }
} 
package cn.com.sipsg.module.business.module.duty.pojo.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 警员DTO，扩展了BasePoliceEmp实体，添加了额外的计算字段
 *
 * <AUTHOR>
 * @since 2025-05-23
 */
@Data
@TableName("tbl_base_police_emp")
public class BasePoliceEmpDTO {
    
    @Schema(description = "是否系统用户")
    @TableField(value = "CASE WHEN auth_user_id IS NOT NULL THEN TRUE ELSE FALSE END", exist = false)
    private Boolean isSystemUser;
}

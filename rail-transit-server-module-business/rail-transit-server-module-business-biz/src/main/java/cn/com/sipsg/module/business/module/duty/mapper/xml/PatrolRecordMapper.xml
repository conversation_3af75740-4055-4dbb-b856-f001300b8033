<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.sipsg.module.business.module.duty.mapper.PatrolRecordMapper">
    
    <!-- 结果映射 -->
    <resultMap id="PatrolRecordVOMap" type="cn.com.sipsg.module.business.module.duty.pojo.vo.PatrolRecordVO">
        <result column="id" property="id"/>
        <result column="patrol_no" property="patrolNo"/>
        <result column="police_id" property="policeId"/>
        <result column="police_no" property="policeNo"/>
        <result column="police_name" property="policeName"/>
        <result column="org_id" property="orgId"/>
        <result column="org_name" property="orgName"/>
        <result column="patrol_time" property="patrolTime"/>
        <result column="location" property="location"/>
        <result column="person_count" property="personCount"/>
        <result column="item_count" property="itemCount"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="inspected_person_name" property="inspectedPersonName"/>
        <result column="inspected_person_id_card" property="inspectedPersonIdCard"/>
    </resultMap>
    
    <!-- 公共SQL片段：日期范围 -->
    <sql id="dateRange">
        SELECT
            COALESCE(#{query.startTime}, CURRENT_DATE - INTERVAL '30 days') AS start_date,
            COALESCE(#{query.endTime}, NOW()) AS end_date
    </sql>
    
    <!-- 公共SQL片段：人员盘查过滤条件 -->
    <sql id="personFilter">
        rypcsj &gt;= (SELECT start_date FROM date_range)
        AND rypcsj &lt;= (SELECT end_date FROM date_range)
        <if test="query.orgIds != null and query.orgIds.size() > 0">
            AND pcdwbh = ANY(#{query.orgIds,jdbcType=ARRAY,typeHandler=org.apache.ibatis.type.ArrayTypeHandler})
        </if>
        <if test="query.patrollerNo != null and query.patrollerNo != ''">
            AND pcrbh LIKE CONCAT(#{query.patrollerNo}, '%')
        </if>
        <if test="query.patrollerName != null and query.patrollerName != ''">
            AND pcrxm LIKE CONCAT(#{query.patrollerName}, '%')
        </if>
    </sql>
    
    <!-- 公共SQL片段：物品盘查过滤条件 -->
    <sql id="itemFilter">
        bpcwppcsj &gt;= (SELECT start_date FROM date_range)
        AND bpcwppcsj &lt;= (SELECT end_date FROM date_range)
        <if test="query.orgIds != null and query.orgIds.size() > 0">
            AND bpcwppcdw = ANY(#{query.orgIds,jdbcType=ARRAY,typeHandler=org.apache.ibatis.type.ArrayTypeHandler})
        </if>
        <if test="query.patrollerNo != null and query.patrollerNo != ''">
            AND gzqkbh LIKE CONCAT(#{query.patrollerNo}, '%')
        </if>
        <if test="query.patrollerName != null and query.patrollerName != ''">
            AND bpcwppcrxm LIKE CONCAT(#{query.patrollerName}, '%')
        </if>
    </sql>
    
    <!-- 分页查询盘查记录列表 -->
    <select id="getPatrolRecordList" resultMap="PatrolRecordVOMap">
        WITH date_range AS (
            <include refid="dateRange"/>
        )
        <if test="page != null">
            SELECT
                id,
                patrol_no,
                police_id,
                police_no,
                police_name,
                org_id,
                org_name,
                patrol_time,
                location,
                person_count,
                item_count,
                remark,
                create_time,
                inspected_person_name,
                inspected_person_id_card
            FROM (
                SELECT
                    NULL AS id,
                    gzbh AS patrol_no,
                    NULL AS police_id,
                    pcrbh AS police_no,
                    pcrxm AS police_name,
                    pcdwbh AS org_id,
                    NULL AS org_name,
                    rypcsj AS patrol_time,
                    NULL AS location,
                    1 AS person_count,
                    0 AS item_count,
                    NULL AS remark,
                    rypcsj AS create_time,
                    bpcrxm AS inspected_person_name,
                    bpcrsfz AS inspected_person_id_card
                FROM std_per_pck_zapc_ryxx
                WHERE <include refid="personFilter"/>
                
                UNION ALL
                
                SELECT
                    NULL AS id,
                    gzqkbh AS patrol_no,
                    NULL AS police_id,
                    pcwpgzqkbh AS police_no,
                    bpcwppcrxm AS police_name,
                    bpcwppcdw AS org_id,
                    NULL AS org_name,
                    bpcwppcsj AS patrol_time,
                    NULL AS location,
                    0 AS person_count,
                    1 AS item_count,
                    NULL AS remark,
                    bpcwppcsj AS create_time,
                    NULL AS inspected_person_name,
                    NULL AS inspected_person_id_card
                FROM std_god_pck_zapc_wpxx
                WHERE <include refid="itemFilter"/>
            ) t
            ORDER BY patrol_time DESC
        </if>
        <if test="page == null">
            (
                SELECT
                    NULL AS id,
                    pcwpgzqkbh AS patrol_no,
                    NULL AS police_id,
                    pcwpgzqkbh AS police_no,
                    pcrxm AS police_name,
                    pcdwbh AS org_id,
                    NULL AS org_name,
                    rypcsj AS patrol_time,
                    NULL AS location,
                    1 AS person_count,
                    0 AS item_count,
                    NULL AS remark,
                    rypcsj AS create_time,
                    bpcrxm AS inspected_person_name,
                    bpcrsfz AS inspected_person_id_card
                FROM std_per_pck_zapc_ryxx
                WHERE <include refid="personFilter"/>
                
                UNION ALL
                
                SELECT
                    NULL AS id,
                    pcwpgzqkbh AS patrol_no,
                    NULL AS police_id,
                    pcwpgzqkbh AS police_no,
                    bpcwppcrxm AS police_name,
                    bpcwppcdw AS org_id,
                    NULL AS org_name,
                    bpcwppcsj AS patrol_time,
                    NULL AS location,
                    0 AS person_count,
                    1 AS item_count,
                    NULL AS remark,
                    bpcwppcsj AS create_time,
                    NULL AS inspected_person_name,
                    NULL AS inspected_person_id_card
                FROM std_god_pck_zapc_wpxx
                WHERE <include refid="itemFilter"/>
            )
            ORDER BY patrol_time DESC
        </if>
    </select>
    
    <!-- 获取符合条件的记录总数 -->
    <select id="countPatrolRecords" resultType="int">
        WITH date_range AS (
            <include refid="dateRange"/>
        )
        SELECT (
            SELECT COUNT(*) FROM std_per_pck_zapc_ryxx
            WHERE <include refid="personFilter"/>
        ) + (
            SELECT COUNT(*) FROM std_god_pck_zapc_wpxx
            WHERE <include refid="itemFilter"/>
        )
    </select>
    
    <!-- 获取指定范围的记录（使用LIMIT实现高效分页） -->
    <select id="getPatrolRecordsWithLimit" resultMap="PatrolRecordVOMap">
        WITH date_range AS (
            <include refid="dateRange"/>
        ),
        combined_data AS (
            SELECT
                NULL AS id,
                gzbh AS patrol_no,
                NULL AS police_id,
                pcrbh AS police_no,
                pcrxm AS police_name,
                pcdwbh AS org_id,
                NULL AS org_name,
                rypcsj AS patrol_time,
                NULL AS location,
                1 AS person_count,
                0 AS item_count,
                NULL AS remark,
                rypcsj AS create_time,
                bpcrxm AS inspected_person_name,
                bpcrsfz AS inspected_person_id_card
            FROM std_per_pck_zapc_ryxx
            WHERE <include refid="personFilter"/>
            
            UNION ALL
            
            SELECT
                NULL AS id,
                gzqkbh AS patrol_no,
                NULL AS police_id,
                pcwpgzqkbh AS police_no,
                bpcwppcrxm AS police_name,
                bpcwppcdw AS org_id,
                NULL AS org_name,
                bpcwppcsj AS patrol_time,
                NULL AS location,
                0 AS person_count,
                1 AS item_count,
                NULL AS remark,
                bpcwppcsj AS create_time,
                NULL AS inspected_person_name,
                NULL AS inspected_person_id_card
            FROM std_god_pck_zapc_wpxx
            WHERE <include refid="itemFilter"/>
        )
        SELECT * FROM combined_data
        ORDER BY patrol_time DESC
        LIMIT #{limit} OFFSET #{offset}
    </select>
    
    <!-- 获取所有符合条件的记录（用于内存分页的备用方案） -->
    <select id="getAllPatrolRecords" resultMap="PatrolRecordVOMap">
        WITH date_range AS (
            <include refid="dateRange"/>
        )
        (
            SELECT
                NULL AS id,
                gzbh AS patrol_no,
                NULL AS police_id,
                pcrbh AS police_no,
                pcrxm AS police_name,
                pcdwbh AS org_id,
                NULL AS org_name,
                rypcsj AS patrol_time,
                NULL AS location,
                1 AS person_count,
                0 AS item_count,
                NULL AS remark,
                rypcsj AS create_time
            FROM std_per_pck_zapc_ryxx
            WHERE <include refid="personFilter"/>
            
            UNION ALL
            
            SELECT
                NULL AS id,
                gzqkbh AS patrol_no,
                NULL AS police_id,
                pcwpgzqkbh AS police_no,
                bpcwppcrxm AS police_name,
                bpcwppcdw AS org_id,
                NULL AS org_name,
                bpcwppcsj AS patrol_time,
                NULL AS location,
                0 AS person_count,
                1 AS item_count,
                NULL AS remark,
                bpcwppcsj AS create_time
            FROM std_god_pck_zapc_wpxx
            WHERE <include refid="itemFilter"/>
        )
        ORDER BY patrol_time DESC
    </select>
    
</mapper>

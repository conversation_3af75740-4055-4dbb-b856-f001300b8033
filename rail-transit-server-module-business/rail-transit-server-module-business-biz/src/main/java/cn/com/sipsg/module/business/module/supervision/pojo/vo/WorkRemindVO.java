package cn.com.sipsg.module.business.module.supervision.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 工作提醒VO
 */
@Data
@Schema(description = "工作提醒VO")
public class WorkRemindVO {

    @Schema(description = "ID")
    private String id;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "执法单位编号")
    private String orgId;

    @Schema(description = "执法单位名称")
    private String orgName;

    @Schema(description = "提醒时间")
    private LocalDateTime remindTime;

    @Schema(description = "内容")
    private String content;


    @Schema(description = "创建时间")
    private LocalDateTime createTime;
}
package cn.com.sipsg.module.business.module.duty.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR> lixt
 * @date : 2025-04-22 15:24:44
 * @Description: 人员排班表（最终排班结果）
 */
@Data
@TableName(value = "tbl_duty_emp", autoResultMap = true)
public class DutyEmp {

    @TableId(type = IdType.ASSIGN_ID)  // 主键自增
    private String id;  // 记录唯一标识主键

    @TableField("police_org_id")
    private String policeOrgId;  // 单位主键

    @TableField("duty_date")
    private String dutyDate;  // 日期，格式为yyyy-MM-dd

    @TableField("post_id")
    private String postId;  // 岗位表主键

    @TableField("turn_id")
    private String turnId;  // 班次表主键

    @TableField("area_id")
    private String areaId;  // 站点主键

    @TableField("emp_id")
    private String empId;  // 值班人员主键

    @TableField("create_time")
    private LocalDateTime createTime;  // 记录创建时间

    @TableField("update_time")
    private LocalDateTime updateTime;  // 记录最后更新时间

    @TableField("create_user_id")
    private String createUserId;  // 创建者用户ID

    @TableField("update_user_id")
    private String updateUserId;  // 最后更新者用户ID

    @TableField("available")
    private Boolean available;  // 是否有效

    @TableField("duty_date_time")
    private LocalDate dutyDateTime;  // 值班日期

    @TableField("source_turn_group_id")
    private Long sourceTurnGroupId;  // 来源班组主键，如果是单个添加，则为-1

    @TableField("station_area_status")
    private Integer stationAreaStatus;  // 是否是站区岗位排班，1 是，2 不是
}
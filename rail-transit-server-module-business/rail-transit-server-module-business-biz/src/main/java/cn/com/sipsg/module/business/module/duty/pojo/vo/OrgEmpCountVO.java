package cn.com.sipsg.module.business.module.duty.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "组织分组在岗警力统计VO", example = "{\"orgId\":1001,\"orgName\":\"轨交分局\",\"count\":5,\"pcCount\":3,\"scCount\":2}")
public class OrgEmpCountVO {
    @Schema(description = "组织ID", example = "1001")
    private String orgId;

    @Schema(description = "组织名称", example = "轨交分局")
    private String orgName;

    @Schema(description = "总警力数量", example = "5")
    private Long count;

    @Schema(description = "民警数量", example = "3")
    private Long pcCount;

    @Schema(description = "辅警数量", example = "2")
    private Long scCount;
}
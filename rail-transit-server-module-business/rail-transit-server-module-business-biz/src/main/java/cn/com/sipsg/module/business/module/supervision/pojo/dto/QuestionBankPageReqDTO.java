package cn.com.sipsg.module.business.module.supervision.pojo.dto;

import cn.com.sipsg.common.pojo.bo.PageBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 题库分页查询请求DTO
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "题库分页查询请求DTO")
public class QuestionBankPageReqDTO extends PageBO {

    @Schema(description = "题库名称（模糊查询，可选）")
    private String name;

    @Schema(description = "题库状态（可选），可选值：00-全部，01-启用，02-禁用")
    private String status;
}
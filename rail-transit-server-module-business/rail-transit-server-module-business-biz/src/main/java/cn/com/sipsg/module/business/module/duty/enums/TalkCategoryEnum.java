package cn.com.sipsg.module.business.module.duty.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> lixt
 * @date : 2025-01-20
 * @Description: 谈话类别枚举
 */
@Getter
@AllArgsConstructor
public enum TalkCategoryEnum {

    POLITICAL_DISCIPLINE("1", "违反政治纪律和政治规矩"),
    
    NOT_REPORT("2", "重大事项不请示不报告"),
    
    LAW_ENFORCEMENT("3", "执法不作为慢作为乱作为"),
    
    ILLEGAL_BUSINESS("4", "参与非法集资借贷或从事其他营利性经营活动"),
    
    MANAGEMENT_VIOLATION("5", "违反人车枪酒密网火管理规定"),
    
    ABSENT_FROM_DUTY("6", "长期请病假或经常性请事假等在职不在岗"),
    
    MENTAL_INSTABILITY("7", "思想过激、情绪不稳定、精神抑郁、存在实施个人极端行为倾向"),
    
    OTHER_RISKS("8", "其他影响队伍稳定的风险隐患");

    /**
     * 编码
     */
    private final String code;

    /**
     * 标签
     */
    private final String label;

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举值
     */
    public static TalkCategoryEnum getByCode(String code) {
        for (TalkCategoryEnum category : values()) {
            if (category.getCode().equals(code)) {
                return category;
            }
        }
        return null;
    }

    /**
     * 根据编码获取标签
     *
     * @param code 编码
     * @return 标签
     */
    public static String getLabelByCode(String code) {
        TalkCategoryEnum category = getByCode(code);
        return category != null ? category.getLabel() : null;
    }
}
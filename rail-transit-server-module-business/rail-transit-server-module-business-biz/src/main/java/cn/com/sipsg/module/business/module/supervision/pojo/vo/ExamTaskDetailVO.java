package cn.com.sipsg.module.business.module.supervision.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "考试任务详情VO")
public class ExamTaskDetailVO {
    @Schema(description = "考试编号（序号）")
    private Integer examNo;
    
    @Schema(description = "考试人员姓名")
    private String personName;
    
    @Schema(description = "警号")
    private String policeCode;

    @Schema(description = "人员ID")
    private String empId;

    @Schema(description = "姓名")
    private String name;
    
    @Schema(description = "考试状态")
    private String examStatus;
    
    @Schema(description = "成绩")
    private String score;
    
    @Schema(description = "考试开始时间")
    private String startTime;
    
    @Schema(description = "考试时长")
    private String duration;
} 
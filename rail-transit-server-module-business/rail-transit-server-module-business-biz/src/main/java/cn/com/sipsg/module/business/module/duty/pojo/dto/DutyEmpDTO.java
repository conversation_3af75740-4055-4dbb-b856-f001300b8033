package cn.com.sipsg.module.business.module.duty.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR> lixt
 * @date : 2025-04-24 14:22:01
 * @Description:
 */
@Data
public class DutyEmpDTO {
    @Schema(description = "警员ID", example = "10001")
    private String empId;

    @Schema(description = "站点ID", example = "1")
    private Long stationId;

    @Schema(description = "单位ID", example = "1001")
    private String policeOrgId;

    @Schema(description = "单位名称", example = "地铁公安分局")
    private String policeOrgName;
}

package cn.com.sipsg.module.business.module.supervision.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 日常数据保存/更新DTO
 */
@Data
@Schema(description = "日常数据保存/更新DTO")
public class StatisticsCompreSaveDTO {

    @Schema(description = "主键ID（更新时必填，新增时不填）", example = "1234567890abcdef12345678")
    private String id;

    @Schema(description = "单位ID", required = true, example = "org_001")
    @NotNull(message = "单位ID不能为空")
    private String policeOrgId;

    @Schema(description = "录入时间", required = true, example = "2025-07-22 09:00:00")
    @NotNull(message = "录入时间不能为空")
    private LocalDateTime statisticsTime;

    @Schema(description = "重点人员预警次数", example = "5")
    private Integer keyPersonWarning;

    @Schema(description = "客流预警次数", example = "3")
    private Integer passengerFlowWarning;

    @Schema(description = "安检违禁品预警数", example = "2")
    private Integer securityCheckWarning;

    @Schema(description = "总警力", example = "100")
    private Integer police;

    @Schema(description = "民警数", example = "100")
    private Integer policeCount;

    @Schema(description = "辅警数", example = "50")
    private Integer auxiliaryPolice;

    @Schema(description = "群防群治", example = "30")
    private Integer preventionTreatment;

    @Schema(description = "警务通", example = "80")
    private Integer policeCommunication;

    @Schema(description = "蓝信", example = "60")
    private Integer blueLetter;

    @Schema(description = "PDA", example = "40")
    private Integer pda;

    @Schema(description = "盘查抓获", example = "15")
    private Integer investigation;

    @Schema(description = "吸毒前科", example = "5")
    private Integer drugRecord;

    @Schema(description = "其他违法", example = "10")
    private Integer otherViolations;

    @Schema(description = "接受群众问询", example = "120")
    private Integer peopleInquiries;

    @Schema(description = "捡拾物品（个）", example = "45")
    private Integer pickUp;

    @Schema(description = "群众服务 - 其他", example = "20")
    private Integer massesOther;

    @Schema(description = "101情况 - 下发数", example = "200")
    private Integer distribution;

    @Schema(description = "101情况 - 核查数", example = "180")
    private Integer checksNum;

    @Schema(description = "101情况 - 抓获数", example = "15")
    private Integer captureNum;

    @Schema(description = "票卡预警情况 - 下发数", example = "100")
    private Integer ticketWarningDistribution;

    @Schema(description = "票卡预警情况 - 核查数", example = "90")
    private Integer ticketWarningChecks;

    @Schema(description = "票卡预警情况 - 抓获数", example = "8")
    private Integer ticketWarningCapture;

    @Schema(description = "公安临控", example = "50")
    private Integer policeTempControl;

    @Schema(description = "法院临控", example = "30")
    private Integer courtTempControl;

    @Schema(description = "三逃", example = "3")
    private Integer threeEscapes;

    @Schema(description = "演练次数", example = "8")
    private Integer drillTimes;

    @Schema(description = "扫黑除恶 - 线索排摸", example = "35")
    private String clueArrangement;

    @Schema(description = "扫黑除恶 - 案件侦办", example = "18")
    private String handlingCases;

    @Schema(description = "扫黑除恶 - 宣传动员", example = "60")
    private String mobilization;

    @Schema(description = "扫黑除恶 - 检查督导", example = "25")
    private String supervision;

    @Schema(description = "最小作战单元演练", example = "42")
    private Integer epidemicCheck;

    @Schema(description = "劝离", example = "12")
    private Integer dissuade;

    @Schema(description = "黄码", example = "8")
    private Integer yellowCode;

    @Schema(description = "红码", example = "2")
    private Integer redCode;

    @Schema(description = "体测人数", example = "1500")
    private Integer temperatureCheck;

    @Schema(description = "异常人数", example = "5")
    private Integer abnormal;

    @Schema(description = "站点消毒杀菌情况", example = "25")
    private Integer stationDisinfection;

    @Schema(description = "列车消毒杀菌情况", example = "30")
    private Integer trainDisinfection;

    @Schema(description = "志愿者数", example = "65")
    private Integer volunteers;

    @Schema(description = "进站客流", example = "2000")
    private Integer arrival;

    @Schema(description = "出站客流", example = "1800")
    private Integer departure;

    @Schema(description = "换乘客流", example = "500")
    private Integer transfer;

    @Schema(description = "安检包裹数", example = "3000")
    private Integer securityCheck;

    @Schema(description = "违禁品数", example = "15")
    private Integer contraband;

    @Schema(description = "备注", example = "日常数据统计")
    private String remark;
}
package cn.com.sipsg.module.business.module.supervision.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 通报信息表
 */
@Data
@TableName("tbl_notice_report")
@Schema(description = "通报信息表")
public class NoticeReport {

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private String id;

    /**
     * 创建人
     */
    @TableField("create_user_id")
    @Schema(description = "创建人")
    private String createUserId;

    /**
     * 创建时间
     */
    @TableField("create_time")
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @TableField("update_user_id")
    @Schema(description = "更新人")
    private String updateUserId;

    /**
     * 更新时间
     */
    @TableField("update_time")
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 是否有效
     */
    @TableField("available")
    @Schema(description = "是否有效")
    private Boolean available;

    /**
     * 通报标题
     */
    @TableField("title")
    @Schema(description = "通报标题")
    private String title;

    /**
     * 签发单位ID
     */
    @TableField("org_id")
    @Schema(description = "签发单位ID")
    private String orgId;

    /**
     * 签发单位名称
     */
    @TableField("org_name")
    @Schema(description = "签发单位名称")
    private String orgName;

    /**
     * 签发时间
     */
    @TableField("law_time")
    @Schema(description = "签发时间")
    private LocalDateTime lawTime;

    /**
     * 不规范内容
     */
    @TableField("non_compliance_content")
    @Schema(description = "不规范内容")
    private String nonComplianceContent;

    /**
     * 附件，存储文件url或路径
     */
    @TableField("attachment")
    @Schema(description = "附件，存储文件url或路径")
    private String attachment;
    
    @TableField("receive_org_id")
    @Schema(description = "接收单位ID")
    private String receiveOrgId;

    @TableField("receive_org_name")
    @Schema(description = "接收单位名称")
    private String receiveOrgName;

    /**
     * 下发人ID（警员ID）
     */
    @TableField("issue_user_id")
    @Schema(description = "下发人ID（警员ID）")
    private String issueUserId;

    /**
     * 下发人姓名
     */
    @TableField("issue_user_name")
    @Schema(description = "下发人姓名")
    private String issueUserName;

    /**
     * 接收人ID（警员ID）
     */
    @TableField("receive_user_id")
    @Schema(description = "接收人ID（警员ID）")
    private String receiveUserId;

    /**
     * 接收人姓名
     */
    @TableField("receive_user_name")
    @Schema(description = "接收人姓名")
    private String receiveUserName;
}
package cn.com.sipsg.module.business.module.supervision.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 抓获记录保存/更新DTO
 */
@Data
@Schema(description = "抓获记录保存/更新DTO")
public class PersonnelCapturedSaveDTO {

    @Schema(description = "主键ID（更新时必填，新增时不填）", example = "1234567890abcdef12345678")
    private String id;

    @Schema(description = "抓获人员姓名", required = true, example = "张三")
    @NotNull(message = "抓获人员姓名不能为空")
    private String name;

    @Schema(description = "抓获人员身份证", required = true, example = "320102199001011234")
    @NotNull(message = "抓获人员身份证不能为空")
    private String idNo;

    @Schema(description = "手机号", example = "***********")
    private String phone;

    @Schema(description = "抓获单位ID", required = true, example = "org_001")
    @NotNull(message = "抓获单位ID不能为空")
    private String policeOrgId;

    @Schema(description = "预警来源 1(101预警)、2(票卡预警)、3(盘查)、4(其他)", required = true, example = "1")
    @NotNull(message = "预警来源不能为空")
    private Short warningSources;

    @Schema(description = "抓获类型 1(全国在逃)、2(公安临控)、3(其他)", required = true, example = "1")
    @NotNull(message = "抓获类型不能为空")
    private Short captureType;

    @Schema(description = "抓获地点", required = true, example = "南京南站")
    @NotNull(message = "抓获地点不能为空")
    private String captureSite;

    @Schema(description = "抓获时间", required = true, example = "2024-01-15T14:30:00")
    @NotNull(message = "抓获时间不能为空")
    private LocalDateTime captureDate;

    @Schema(description = "处理结果 1(拘留)、2(移交)", required = true, example = "1")
    @NotNull(message = "处理结果不能为空")
    private Short processResult;

    @Schema(description = "处理结果详情", example = "行政拘留15天")
    private String processResultDetail;

    @Schema(description = "移交单位", example = "南京市公安局")
    private String turnOverOrg;

    @Schema(description = "后续处理 1(行政拘留)、2(刑事拘留)、3(取保候审)、4(排除嫌疑)、5(放行)、6(不予处罚)、99(其他)", example = "1")
    private Short postProcessResult;

    @Schema(description = "现场图片URL列表(JSON字符串)", example = "[\"http://example.com/image1.jpg\", \"http://example.com/image2.jpg\"]")
    private String imageUrls;

    @Schema(description = "备注", example = "现场配合良好")
    private String remark;

    @Schema(description = "协助抓获单位", example = "地铁公安分局")
    private String coOrganizer;
}
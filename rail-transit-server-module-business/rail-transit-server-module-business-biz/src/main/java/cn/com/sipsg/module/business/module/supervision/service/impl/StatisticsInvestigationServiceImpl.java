package cn.com.sipsg.module.business.module.supervision.service.impl;

import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.security.core.util.SecurityUtils;
import cn.com.sipsg.module.business.module.common.util.UserOrgUtils;
import cn.com.sipsg.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import cn.com.sipsg.module.business.module.duty.entity.BasePoliceOrg;
import cn.com.sipsg.module.business.module.duty.mapper.BasePoliceOrgMapper;
import cn.com.sipsg.module.business.module.supervision.entity.StatisticsInvestigation;
import cn.com.sipsg.module.business.module.supervision.entity.StatisticsInvestigationImg;
import cn.com.sipsg.module.business.module.supervision.mapper.StatisticsInvestigationMapper;
import cn.com.sipsg.module.business.module.supervision.mapper.StatisticsInvestigationImgMapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.InvestigationDetailReqDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.InvestigationPageReqDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.InvestigationSaveDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.InvestigationPageVO;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.CriminalRecordTypeVO;
import cn.com.sipsg.module.business.module.supervision.enums.CriminalRecordTypeEnum;
import cn.com.sipsg.module.business.module.supervision.service.StatisticsInvestigationService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.Arrays;

@Service
@RequiredArgsConstructor
@Slf4j
public class StatisticsInvestigationServiceImpl implements StatisticsInvestigationService {
    private final StatisticsInvestigationMapper statisticsInvestigationMapper;
    private final StatisticsInvestigationImgMapper statisticsInvestigationImgMapper;
    private final BasePoliceOrgMapper basePoliceOrgMapper;
    private final UserOrgUtils userOrgUtils;
    private final ObjectMapper objectMapper;

    /**
     * 根据当前用户查询统计调查信息分页数据
     *
     * @param dto 查询参数DTO
     * @return 返回包含调查信息的公共页面VO对象
     */
    @Override
    public CommonPageVO<InvestigationPageVO> pageInvestigationForCurrentUser(InvestigationPageReqDTO dto) {
        LocalDateTime startTime = dto.getStartTime();
        LocalDateTime endTime = dto.getEndTime();
        String policeOrgId = dto.getPoliceOrgId();
        String personKey = dto.getPersonKey();
        int pageNum = dto.getPageNum();
        int pageSize = dto.getPageSize();
        
        // 构建查询条件
        LambdaQueryWrapper<StatisticsInvestigation> wrapper = new LambdaQueryWrapper<>();
        
        // 数据权限控制
        if (SecurityUtils.isSuperAdmin()) {
            // 超级管理员可以查看所有数据
            if (policeOrgId != null && !policeOrgId.trim().isEmpty()) {
                wrapper.eq(StatisticsInvestigation::getPoliceOrgId, policeOrgId);
            }
        } else {
            // 普通用户只能查看自己及下级组织的数据
            String orgId = userOrgUtils.getCurrentUserOrgId();
            List<String> orgIds = getOrgAndChildrenIds(orgId);
            if (policeOrgId != null && !policeOrgId.trim().isEmpty() && orgIds.contains(policeOrgId)) {
                orgIds = java.util.Collections.singletonList(policeOrgId);
            }
            wrapper.in(StatisticsInvestigation::getPoliceOrgId, orgIds);
        }
        
        // 时间范围查询
        if (startTime != null) {
            wrapper.ge(StatisticsInvestigation::getTime, startTime);
        }
        if (endTime != null) {
            wrapper.le(StatisticsInvestigation::getTime, endTime);
        }
        
        // 被盘查人姓名或身份证号模糊查询
        if (personKey != null && !personKey.trim().isEmpty()) {
            wrapper.and(w -> w.like(StatisticsInvestigation::getPersonName, personKey.trim())
                    .or().like(StatisticsInvestigation::getPersonIdCard, personKey.trim()));
        }
        
        // 只查询有效数据
        wrapper.eq(StatisticsInvestigation::getAvailable, true);
        
        return getInvestigationPageVOCommonPageVO(pageNum, pageSize, wrapper);
    }

    /**
     * 获取统计调查页面的通用分页视图对象
     *
     * @param pageNum 页码
     * @param pageSize 每页记录数
     * @param wrapper 查询条件包装器
     * @return 返回包含调查页面信息的通用分页视图对象
     */
    @NotNull
    private CommonPageVO<InvestigationPageVO> getInvestigationPageVOCommonPageVO(int pageNum, int pageSize, LambdaQueryWrapper<StatisticsInvestigation> wrapper) {
        // 按时间降序排列查询条件
        wrapper.orderByDesc(StatisticsInvestigation::getTime);

        // 创建分页对象
        Page<StatisticsInvestigation> page = new Page<>(pageNum, pageSize);

        // 执行分页查询
        IPage<StatisticsInvestigation> result = statisticsInvestigationMapper.selectPage(page, wrapper);

        // 提取查询结果中的组织ID列表，并去重
        List<String> orgIdList = result.getRecords().stream().map(StatisticsInvestigation::getPoliceOrgId).distinct().collect(Collectors.toList());

        // 获取组织ID与名称的映射
        Map<String, String> orgNameMap = getOrgNameMap(orgIdList);

        // 构建调查页面VO列表
        List<InvestigationPageVO> voList = buildInvestigationPageVOList(result.getRecords(), orgNameMap);

        // 创建通用分页VO对象
        CommonPageVO<InvestigationPageVO> voPage = new CommonPageVO<>();

        // 设置分页信息
        voPage.setRecords(voList);
        voPage.setTotal(result.getTotal());
        voPage.setCurrent(result.getCurrent());
        voPage.setSize(result.getSize());

        // 返回通用分页VO对象
        return voPage;
    }

    /**
     * 获取指定组织及其子组织的ID列表
     *
     * @param orgId 指定组织的ID
     * @return 包含指定组织及其子组织ID的列表
     */
    private List<String> getOrgAndChildrenIds(String orgId) {
        // 获取所有组织信息
        List<BasePoliceOrg> all = basePoliceOrgMapper.selectList(null);
        // 初始化结果列表
        List<String> result = new ArrayList<>();
        // 递归收集指定组织及其子组织的ID
        collectOrgAndChildrenIds(orgId, all, result);
        // 返回结果列表
        return result;
    }

    /**
     * 递归收集指定机构及其子机构的ID
     *
     * @param orgId 当前要处理的机构ID
     * @param all 包含所有机构信息的列表
     * @param result 用于存储机构ID的列表，通过递归调用更新
     */
    private void collectOrgAndChildrenIds(String orgId, List<BasePoliceOrg> all, List<String> result) {
        // 将当前机构ID添加到结果列表中
        result.add(orgId);
        // 遍历所有机构，寻找当前机构的子机构
        for (BasePoliceOrg org : all) {
            // 如果找到子机构，则递归调用方法处理子机构
            if (orgId.equals(org.getParentId())) {
                collectOrgAndChildrenIds(org.getId(), all, result);
            }
        }
    }

    /**
     * 根据组织ID列表获取对应的组织名称映射
     *
     * @param orgIdList 组织ID列表，用于查询组织信息
     * @return 返回一个映射，键为组织ID，值为组织名称
     */
    private Map<String, String> getOrgNameMap(List<String> orgIdList) {
        // 如果组织ID列表为空，直接返回空映射
        if (orgIdList.isEmpty()) {
            return Collections.emptyMap();
        }
        // 通过组织ID列表查询数据库中的组织信息，并将结果转换为组织名称映射
        // 使用Stream API收集查询结果，以组织ID作为键，组织名称作为值，并且在键冲突时保留第一个值
        return basePoliceOrgMapper.selectByIdList(orgIdList).stream()
                .collect(Collectors.toMap(BasePoliceOrg::getId, BasePoliceOrg::getName, (a, b) -> a));
    }

    /**
     * 根据统计数据记录构建调查页面VO列表
     * 此方法通过转换给定的统计调查记录列表和组织名称映射来创建和返回一个调查页面VO列表
     * 它主要用于为前端页面提供所需的数据格式
     *
     * @param records    统计调查记录列表，包含调查的相关信息
     * @param orgNameMap 组织ID与组织名称之间的映射，用于获取组织名称
     * @return 返回一个转换后的调查页面VO列表，每个VO包含调查页面所需的信息
     */
    private List<InvestigationPageVO> buildInvestigationPageVOList(List<StatisticsInvestigation> records, Map<String, String> orgNameMap) {
        return records.stream().map(e -> {
            // 创建一个新的调查页面VO对象
            InvestigationPageVO vo = new InvestigationPageVO();
            // 设置盘查前科ID
            vo.setId(e.getId());
            // 设置警察名称
            vo.setPoliceName(e.getPoliceName());
            // 设置警察组织名称，如果找不到对应的组织ID，则默认为空字符串
            vo.setPoliceOrgName(orgNameMap.getOrDefault(e.getPoliceOrgId(), ""));
            // 设置人员姓名
            vo.setPersonName(e.getPersonName());
            // 设置人员身份证号码
            vo.setPersonIdCard(e.getPersonIdCard());
            // 设置前科类型列表
            vo.setPersonTag(convertPersonTagToCriminalRecordTypeVOList(e.getPersonTag()));
            // 设置时间，如果时间不为空，则转换为字符串格式；否则为空字符串
            vo.setTime(e.getTime());
            // 返回转换后的调查页面VO对象
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 删除盘查前科
     *
     * @param id 盘查前科ID
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteInvestigation(String id) {
        try {
            if (id == null) {
                throw new BusinessException("盘查前科ID不能为空");
            }
            
            StatisticsInvestigation entity = statisticsInvestigationMapper.selectById(id);
            if (entity == null || !Boolean.TRUE.equals(entity.getAvailable())) {
                throw new BusinessException("盘查前科记录不存在");
            }
            
            // 逻辑删除
            entity.setAvailable(false);
            entity.setUpdateTime(LocalDateTime.now());
            entity.setUpdateUserId(SecurityUtils.getLoginUserId());
            
            int result = statisticsInvestigationMapper.updateById(entity);
            if (result <= 0) {
                throw new BusinessException("删除盘查前科失败");
            }
            
            // 同时删除相关图片记录
            deleteInvestigationImages(id);
            
            log.info("删除盘查前科成功，ID：{}", id);
            return true;
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("删除盘查前科失败，ID：{}", id, e);
            throw new BusinessException("删除盘查前科失败");
        }
    }

    /**
     * 保存或更新盘查前科情况
     *
     * @param dto 盘查前科情况保存DTO
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveOrUpdateInvestigation(InvestigationSaveDTO dto) {
        try {
            StatisticsInvestigation entity;
            boolean isUpdate = dto.getId() != null && !dto.getId().trim().isEmpty();
            
            if (isUpdate) {
                // 更新操作
                entity = statisticsInvestigationMapper.selectById(dto.getId());
                if (entity == null || !Boolean.TRUE.equals(entity.getAvailable())) {
                    throw new BusinessException("盘查前科记录不存在");
                }
                entity.setUpdateTime(LocalDateTime.now());
                entity.setUpdateUserId(SecurityUtils.getLoginUserId());
            } else {
                // 新增操作
                entity = new StatisticsInvestigation();
                entity.setCreateTime(LocalDateTime.now());
                entity.setCreateUserId(SecurityUtils.getLoginUserId());
                entity.setAvailable(true);
            }
            
            // 映射DTO到实体
            mapDTOToEntity(dto, entity);
            
            int result;
            if (isUpdate) {
                result = statisticsInvestigationMapper.updateById(entity);
                log.info("更新盘查前科成功，ID：{}", entity.getId());
            } else {
                result = statisticsInvestigationMapper.insert(entity);
                log.info("新增盘查前科成功，ID：{}", entity.getId());
            }
            
            if (result <= 0) {
                throw new BusinessException(isUpdate ? "更新盘查前科失败" : "新增盘查前科失败");
            }
            
            // 处理图片保存
            saveInvestigationImages(entity.getId(), dto.getImageUrls(), isUpdate);
            
            return true;
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("保存盘查前科失败", e);
            throw new BusinessException("保存盘查前科失败");
        }
    }

    /**
     * 获取盘查前科情况详情
     *
     * @param dto 详情查询请求参数
     * @return 盘查前科情况详情
     */
    @Override
    public InvestigationSaveDTO getInvestigationDetail(InvestigationDetailReqDTO dto) {
        try {
            StatisticsInvestigation entity = statisticsInvestigationMapper.selectById(dto.getId());
            if (entity == null || !Boolean.TRUE.equals(entity.getAvailable())) {
                throw new BusinessException("盘查前科记录不存在");
            }
            
            return mapEntityToDTO(entity);
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取盘查前科详情失败，ID：{}", dto.getId(), e);
            throw new BusinessException("获取盘查前科详情失败");
        }
    }

    /**
     * 将DTO映射到实体
     */
    private void mapDTOToEntity(InvestigationSaveDTO dto, StatisticsInvestigation entity) {
        entity.setTime(dto.getTime());
        entity.setPoliceName(dto.getPoliceName());
        entity.setPoliceOrgId(dto.getPoliceOrgId());
        entity.setPersonName(dto.getPersonName());
        entity.setPersonIdCard(dto.getPersonIdCard());
        entity.setPersonTag(dto.getPersonTag());
        entity.setMemos(dto.getMemos());
        // 注意：imageUrls字段通过单独的图片表tbl_statistics_investigation_img处理
    }

    /**
     * 将实体映射到DTO
     */
    private InvestigationSaveDTO mapEntityToDTO(StatisticsInvestigation entity) {
        InvestigationSaveDTO dto = new InvestigationSaveDTO();
        dto.setId(entity.getId());
        dto.setTime(entity.getTime());
        dto.setPoliceName(entity.getPoliceName());
        dto.setPoliceOrgId(entity.getPoliceOrgId());
        dto.setPersonName(entity.getPersonName());
        dto.setPersonIdCard(entity.getPersonIdCard());
        dto.setPersonTag(entity.getPersonTag());
        dto.setMemos(entity.getMemos());
        // 查询图片URL列表
        dto.setImageUrls(getInvestigationImageUrls(entity.getId()));
        return dto;
    }

    /**
     * 保存盘查前科情况图片
     *
     * @param investigationId 盘查前科情况ID
     * @param imageUrlsJson   图片URL列表JSON字符串
     * @param isUpdate        是否为更新操作
     */
    private void saveInvestigationImages(String investigationId, String imageUrlsJson, boolean isUpdate) {
        try {
            // 获取当前用户ID
            String currentUserId = SecurityUtils.getLoginUserId();
            
            // 如果是更新操作，先删除原有图片记录
            if (isUpdate) {
                // 查询需要删除的图片记录
                 List<StatisticsInvestigationImg> existingImgs = statisticsInvestigationImgMapper.selectList(
                     StatisticsInvestigationImg::getInvestigationId, investigationId,
                     StatisticsInvestigationImg::getAvailable, true
                 );
                 
                 // 逻辑删除现有图片记录
                 if (existingImgs != null && !existingImgs.isEmpty()) {
                     for (StatisticsInvestigationImg img : existingImgs) {
                         img.setAvailable(false);
                         img.setUpdateUserId(currentUserId);
                         img.setUpdateTime(LocalDateTime.now());
                         statisticsInvestigationImgMapper.updateById(img);
                     }
                 }
            }

            // 如果图片URL列表为空，直接返回
            if (imageUrlsJson == null || imageUrlsJson.trim().isEmpty()) {
                return;
            }

            // 解析JSON字符串为URL列表
            List<String> imageUrls = objectMapper.readValue(imageUrlsJson, new TypeReference<List<String>>() {});
            if (imageUrls == null || imageUrls.isEmpty()) {
                return;
            }

            // 保存新的图片记录
            LocalDateTime now = LocalDateTime.now();
            
            for (String imageUrl : imageUrls) {
                if (imageUrl != null && !imageUrl.trim().isEmpty()) {
                    StatisticsInvestigationImg imgEntity = new StatisticsInvestigationImg();
                    imgEntity.setInvestigationId(investigationId);
                    imgEntity.setImgUrl(imageUrl.trim());
                    imgEntity.setCreateUserId(currentUserId);
                    imgEntity.setCreateTime(now);
                    imgEntity.setAvailable(true);
                    
                    statisticsInvestigationImgMapper.insert(imgEntity);
                }
            }
            
        } catch (Exception e) {
            log.error("保存盘查前科图片失败，investigationId：{}", investigationId, e);
            throw new BusinessException("保存图片失败");
        }
    }

    /**
     * 获取盘查前科情况图片URL列表
     *
     * @param investigationId 盘查前科情况ID
     * @return 图片URL列表JSON字符串
     */
    private String getInvestigationImageUrls(String investigationId) {
        try {
            List<StatisticsInvestigationImg> imgList = statisticsInvestigationImgMapper.selectList(
                 StatisticsInvestigationImg::getInvestigationId, investigationId,
                 StatisticsInvestigationImg::getAvailable, true
             );

            if (imgList == null || imgList.isEmpty()) {
                return null;
            }

            List<String> imageUrls = imgList.stream()
                .map(StatisticsInvestigationImg::getImgUrl)
                .filter(url -> url != null && !url.trim().isEmpty())
                .collect(Collectors.toList());

            return imageUrls.isEmpty() ? null : objectMapper.writeValueAsString(imageUrls);
            
        } catch (Exception e) {
            log.error("获取盘查前科图片失败，investigationId：{}", investigationId, e);
            return null;
        }
     }

    /**
     * 删除盘查前科情况相关图片
     *
     * @param investigationId 盘查前科情况ID
     */
    private void deleteInvestigationImages(String investigationId) {
        try {
            // 逻辑删除图片记录
             List<StatisticsInvestigationImg> imgList = statisticsInvestigationImgMapper.selectList(
                 StatisticsInvestigationImg::getInvestigationId, investigationId,
                 StatisticsInvestigationImg::getAvailable, true
             );
            
            if (imgList != null && !imgList.isEmpty()) {
                String currentUserId = SecurityUtils.getLoginUserId();
                LocalDateTime now = LocalDateTime.now();
                
                for (StatisticsInvestigationImg img : imgList) {
                    img.setAvailable(false);
                    img.setUpdateUserId(currentUserId);
                    img.setUpdateTime(now);
                    statisticsInvestigationImgMapper.updateById(img);
                }
            }
            
        } catch (Exception e) {
            log.error("删除盘查前科图片失败，investigationId：{}", investigationId, e);
            // 图片删除失败不影响主记录删除，只记录日志
        }
    }
    
    /**
     * 将前科类型编号字符串转换为前科类型VO列表
     *
     * @param personTag 前科类型编号字符串，多个编号以英文逗号分隔
     * @return 前科类型VO列表
     */
    private List<CriminalRecordTypeVO> convertPersonTagToCriminalRecordTypeVOList(String personTag) {
        if (personTag == null || personTag.trim().isEmpty()) {
            return new ArrayList<>();
        }
        
        return Arrays.stream(personTag.split(","))
                .map(String::trim)
                .filter(code -> !code.isEmpty())
                .map(code -> {
                    CriminalRecordTypeEnum enumValue = CriminalRecordTypeEnum.getByCode(code);
                    if (enumValue != null) {
                        return new CriminalRecordTypeVO(enumValue.getCode(), enumValue.getName());
                    } else {
                        // 如果找不到对应的枚举值，返回编号和编号作为名称
                        return new CriminalRecordTypeVO(code, code);
                    }
                })
                .collect(Collectors.toList());
    }
}
package cn.com.sipsg.module.business.module.supervision.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import cn.com.sipsg.common.pojo.bo.PageBO;

/**
 * 安检员分页查询请求DTO
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "安检员分页查询请求DTO")
public class SecurityEmpPageReqDTO extends PageBO {

    @Schema(description = "关键字（支持姓名或身份证号模糊查询）", example = "张三")
    private String keyword;

    @Schema(description = "安检公司名称", example = "中铁安保公司")
    private String companyName;

    @Schema(description = "在职状态（0-在职，1-离职，2-退休）", example = "0")
    private String employmentStatus;

    @Schema(description = "是否培训（true-是，false-否）", example = "1")
    private Boolean hasTrain;

    @Schema(description = "创建人名称", example = "李四")
    private String createUserName;

    @Schema(description = "创建开始时间，格式yyyy-MM-dd HH:mm:ss", example = "2024-06-01 00:00:00")
    private String createStartTime;

    @Schema(description = "创建结束时间，格式yyyy-MM-dd HH:mm:ss", example = "2024-06-30 23:59:59")
    private String createEndTime;
}
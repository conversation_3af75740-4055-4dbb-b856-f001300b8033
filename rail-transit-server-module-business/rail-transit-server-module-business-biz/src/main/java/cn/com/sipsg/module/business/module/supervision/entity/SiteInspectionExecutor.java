package cn.com.sipsg.module.business.module.supervision.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

@Data
@TableName("tbl_site_inspection_executor")
public class SiteInspectionExecutor {
    /** 记录唯一标识主键 */
    @TableId("id")
    private Long id;
    /** 记录创建时间（时间戳） */
    @TableField("create_time")
    private Long createTime;
    /** 记录最后更新时间（时间戳） */
    @TableField("update_time")
    private Long updateTime;
    /** 创建者用户ID */
    @TableField("create_user_id")
    private String createUserId;
    /** 最后更新者用户ID */
    @TableField("update_user_id")
    private String updateUserId;
    /** 是否有效 */
    @TableField("available")
    private Boolean available;
    /** 巡检ID */
    @TableField("inspection_id")
    private Long inspectionId;
    /** 执行类型 */
    @TableField("executor_type")
    private Integer executorType;
    /** 人员ID */
    @TableField("police_emp_id")
    private Long policeEmpId;
    /** 单位ID */
    @TableField("police_org_id")
    private String policeOrgId;
    /** 人员标签 */
    @TableField("police_emp_tag")
    private String policeEmpTag;
    /** 单位类型 */
    @TableField("police_org_type")
    private Integer policeOrgType;
}
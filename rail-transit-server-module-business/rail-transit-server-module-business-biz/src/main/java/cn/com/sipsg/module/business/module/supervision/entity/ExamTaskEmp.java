package cn.com.sipsg.module.business.module.supervision.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> lixt
 * @date : 2025-05-21 14:25:55
 * @Description: 考试人员关联表
 */
@Data
@TableName("tbl_exam_task_emp")
public class ExamTaskEmp {

    @TableId(type = IdType.ASSIGN_ID)
    private String id;  // 主键ID

    @TableField("create_user_id")
    private String createUserId;  // 创建人

    @TableField("create_time")
    private LocalDateTime createTime;  // 创建时间

    @TableField("update_user_id")
    private String updateUserId;  // 更新人

    @TableField("update_time")
    private LocalDateTime updateTime;  // 更新时间

    @TableField("available")
    private Boolean available;  // 有效标识

    @TableField("task_id")
    private String taskId;  // 考试任务ID

    @TableField("task_name")
    private String taskName;  // 考试任务名称

    @TableField("emp_id")
    private String empId;  // 人员ID

    @TableField("police_code")
    private String policeCode;  // 警号

    @TableField("police_name")
    private String policeName;  // 姓名

    @TableField("start_time")
    private LocalDateTime startTime;  // 考试实际开始时间

    @TableField("end_time")
    private LocalDateTime endTime;  // 考试实际结束时间

    @TableField("score")
    private Integer score;  // 考试分数

    @TableField("status")
    private String status;  // 考试状态，01:未开始，02:进行中，03:合格，04:不合格

    @TableField("task_emp_batch_id")
    private String taskEmpBatchId;  // 确认考试批次ID

    @TableField("task_emp_cur_batch_id")
    private String taskEmpCurBatchId;  // 当前考试批次ID
}
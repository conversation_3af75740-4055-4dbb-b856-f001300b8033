package cn.com.sipsg.module.business.module.supervision.service.impl;

import cn.com.sipsg.common.exception.BusinessException;
import cn.com.sipsg.common.exception.ServerException;
import cn.com.sipsg.common.pojo.bo.PageBO;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.security.core.util.SecurityUtils;
import cn.com.sipsg.module.business.module.common.util.OrgRecursiveUtils;
import cn.com.sipsg.module.business.module.common.util.UserOrgUtils;
import cn.com.sipsg.module.business.module.duty.entity.BasePoliceEmp;
import cn.com.sipsg.module.business.module.duty.entity.BaseStation;
import cn.com.sipsg.module.business.module.duty.mapper.BasePoliceEmpMapper;
import cn.com.sipsg.module.business.module.duty.mapper.BaseStationMapper;
import cn.com.sipsg.module.business.module.supervision.entity.BaseSubwayStation;
import cn.com.sipsg.module.business.module.supervision.entity.PoliceRisk;
import cn.com.sipsg.module.business.module.supervision.entity.SecretInvestigationTask;
import cn.com.sipsg.module.business.module.supervision.enums.*;
import cn.com.sipsg.module.business.module.supervision.mapper.BaseSubwayStationMapper;
import cn.com.sipsg.module.business.module.supervision.mapper.PoliceRiskMapper;
import cn.com.sipsg.module.business.module.supervision.mapper.SecretInvestigationTaskMapper;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.PoliceRiskCreateReqDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.PoliceRiskPageReqDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.WeeklyRiskQueryParamsDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.PoliceRiskDetailVO;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.PoliceRiskVO;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.RiskCountVO;
import cn.com.sipsg.module.business.module.supervision.service.PoliceRiskService;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 警务风险信息服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PoliceRiskServiceImpl extends ServiceImpl<PoliceRiskMapper, PoliceRisk> implements PoliceRiskService {

    private final PoliceRiskMapper policeRiskMapper;
    private final BasePoliceEmpMapper basePoliceEmpMapper;
    private final BaseStationMapper baseStationMapper;
    private final BaseSubwayStationMapper baseSubwayStationMapper;
    private final UserOrgUtils userOrgUtils;
    private final SecretInvestigationTaskMapper secretInvestigationTaskMapper;
    private final OrgRecursiveUtils orgRecursiveUtils;

    /**
     * 获取当前用户所在组织内所有用户一周内的风险数量
     *
     * @return 组织内一周内的风险数量
     */
    @Override
    public RiskCountVO getOrgWeeklyRiskCount() {
        try {
            log.debug("[组织风险数量] 开始获取组织风险数量统计");
            // 获取一周内的查询参数
            WeeklyRiskQueryParamsDTO queryParams = getWeeklyRiskQueryParams();
            
            if (queryParams.getEmployeeIds().isEmpty()) {
                log.warn("[组织风险数量] 员工ID列表为空，返回零值统计");
                RiskCountVO vo = new RiskCountVO();
                vo.setCheckPointCount(0);
                vo.setInspectionTaskCount(0);
                vo.setRiskHandleCount(0);
                return vo;
            }

            // 查控点数量统计（tbl_police_risk，按组织内所有成员一周内数据统计）
            int checkPointCount = 0;
            try {
                log.debug("[组织风险数量] 开始查询查控点数量，员工ID列表: {}, 时间范围: {} 到 {}", 
                    queryParams.getEmployeeIds(), queryParams.getStartTime(), queryParams.getEndTime());
                
                // 检查员工ID列表是否包含无效数据
                List<String> validEmployeeIds = queryParams.getEmployeeIds().stream()
                    .filter(id -> id != null && !id.trim().isEmpty())
                    .collect(Collectors.toList());
                
                if (validEmployeeIds.isEmpty()) {
                    log.warn("[组织风险数量] 有效员工ID列表为空，查控点数量设为0");
                    checkPointCount = 0;
                } else {
                    log.debug("[组织风险数量] 有效员工ID数量: {}", validEmployeeIds.size());
                    checkPointCount = (int) policeRiskMapper.countWeeklyRisksByEmployeeIds(
                            validEmployeeIds,
                            queryParams.getStartTime(),
                            queryParams.getEndTime());
                }
                log.debug("[组织风险数量] 查控点数量: {}", checkPointCount);
            } catch (Exception e) {
                log.error("[组织风险数量] 查询查控点数量失败，员工ID列表: {}", queryParams.getEmployeeIds(), e);
                throw e;
            }

            // 统计一周内暗访检查任务数量
            int inspectionTaskCount = 0;
            try {
                inspectionTaskCount = Math.toIntExact(
                        secretInvestigationTaskMapper.selectCount(
                                new LambdaQueryWrapper<SecretInvestigationTask>()
                                        .in(SecretInvestigationTask::getHandleEmpId, queryParams.getEmployeeIds())
                                        .between(SecretInvestigationTask::getCreateTime, queryParams.getStartTime(), queryParams.getEndTime())
                                        .eq(SecretInvestigationTask::getAvailable, true)
                        )
                );
                log.debug("[组织风险数量] 暗访检查任务数量: {}", inspectionTaskCount);
            } catch (Exception e) {
                log.error("[组织风险数量] 查询暗访检查任务数量失败", e);
                throw e;
            }

            // 统计一周内已处理隐患数量（hasChange=1 或 hasEnd=1）
            int riskHandleCount = 0;
            try {
                riskHandleCount = Math.toIntExact(
                        policeRiskMapper.selectCount(
                                new LambdaQueryWrapper<PoliceRisk>()
                                        .in(PoliceRisk::getCreateUserId, queryParams.getEmployeeIds())
                                        .between(PoliceRisk::getCreateTime, queryParams.getStartTime(), queryParams.getEndTime())
                                        .eq(PoliceRisk::getAvailable, true)
                                        .and(wrapper -> wrapper.eq(PoliceRisk::getHasChange, HasChangeEnum.YES.getCode()).or().eq(PoliceRisk::getHasEnd, HasEndEnum.YES.getCode()))
                        )
                );
                log.debug("[组织风险数量] 已处理隐患数量: {}", riskHandleCount);
            } catch (Exception e) {
                log.error("[组织风险数量] 查询已处理隐患数量失败", e);
                throw e;
            }

            RiskCountVO vo = new RiskCountVO();
            vo.setCheckPointCount(checkPointCount);
            vo.setInspectionTaskCount(inspectionTaskCount);
            vo.setRiskHandleCount(riskHandleCount);
            log.debug("[组织风险数量] 统计完成 - 查控点: {}, 暗访任务: {}, 已处理隐患: {}", checkPointCount, inspectionTaskCount, riskHandleCount);
            return vo;
        } catch (Exception e) {
            log.error("[组织风险数量] 获取组织风险数量异常", e);
            throw new ServerException("获取组织风险数量失败", e);
        }
    }


    /**
     * 获取当前用户所在组织内所有用户一周内的风险列表
     *
     * @return 组织内一周内的风险列表
     */
    @Override
    public List<PoliceRiskVO> getOrgWeeklyRisks() {
        try {
            // 获取一周内的查询参数
            WeeklyRiskQueryParamsDTO queryParams = getWeeklyRiskQueryParams();

            // 查询这些警员一周内的风险数据
            List<PoliceRisk> riskList = new ArrayList<>();
            if (!queryParams.getEmployeeIds().isEmpty()) {
                riskList = policeRiskMapper.findWeeklyRisksByEmployeeIds(
                        queryParams.getEmployeeIds(),
                        queryParams.getStartTime(),
                        queryParams.getEndTime());
            }

            // 将实体类转换为VO对象
            return riskList.stream().map(this::convertToVO).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("[组织风险列表] 获取组织风险列表数据异常", e);
            throw new ServerException("获取组织风险列表数据失败", e);
        }
    }

    /**
     * 分页查询隐患列表
     *
     * @param reqDTO 查询请求参数，包含分页信息和查询条件
     * @return 返回分页的隐患信息列表
     * <p>
     * 该方法根据不同的查询条件和用户权限，分页查询隐患信息，并将结果封装成CommonPageVO对象返回
     * 查询条件包括：单位、上报人、上报时间区间、状态等
     * 用户权限分为管理员和普通用户，不同权限查询范围不同
     */
    @Override
    public CommonPageVO<PoliceRiskVO> pageRiskList(PoliceRiskPageReqDTO reqDTO) {
        try {
            if (reqDTO == null) reqDTO = new PoliceRiskPageReqDTO();
            // 构建分页对象
            int pageNum = reqDTO.getCurrent() != null ? reqDTO.getCurrent().intValue() : 1;
            int pageSize = reqDTO.getSize() != null ? reqDTO.getSize().intValue() : 10;
            Page<PoliceRisk> page = new Page<>(pageNum, pageSize);

            // 构建查询条件
            LambdaQueryWrapper<PoliceRisk> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(PoliceRisk::getAvailable, true);

            boolean isAdmin = SecurityUtils.isSuperAdmin();
            String scope = reqDTO.getScope();
            String currentOrgId = userOrgUtils.getCurrentUserOrgId();

            // 数据权限管理逻辑
            if ("org".equalsIgnoreCase(scope)) {
                // 本单位隐患：管理员和普通用户都只查询自己单位的（不包含下级单位）
                List<BasePoliceEmp> ems = basePoliceEmpMapper.selectList(
                        new LambdaQueryWrapper<BasePoliceEmp>()
                                .eq(BasePoliceEmp::getPoliceOrgId, currentOrgId)
                                .eq(BasePoliceEmp::getAvailable, true)
                );
                if (!ems.isEmpty()) {
                    List<String> empIds = ems.stream().map(e -> String.valueOf(e.getId())).collect(Collectors.toList());
                    wrapper.in(PoliceRisk::getCreateUserId, empIds);
                } else {
                    PageBO pageBO = new PageBO();
                    pageBO.setCurrent((long) pageNum);
                    pageBO.setSize((long) pageSize);
                    return CommonPageVO.empty(pageBO);
                }
            } else {
                // 全部隐患：管理员可以看所有，普通用户只能看当前单位及下级单位的
                if (!isAdmin) {
                    // 普通用户全部隐患时只能查本单位及下级单位
                    List<String> orgIds = orgRecursiveUtils.getOrgAndChildrenIds(currentOrgId);
                    List<BasePoliceEmp> ems = basePoliceEmpMapper.selectList(
                            new LambdaQueryWrapper<BasePoliceEmp>()
                                    .in(BasePoliceEmp::getPoliceOrgId, orgIds)
                                    .eq(BasePoliceEmp::getAvailable, true)
                    );
                    if (getPoliceRiskByEms(wrapper, ems)) return createEmptyPageResult(pageNum, pageSize);
                }
                // 管理员全部隐患时不加任何限制，可以查看所有隐患
            }

            // 隐患类型筛选
            if (reqDTO.getType() != null && !reqDTO.getType().trim().isEmpty()) {
                wrapper.eq(PoliceRisk::getType, reqDTO.getType().trim());
            }
            // 上报人警号（模糊）
            if (reqDTO.getCreateUserId() != null && !reqDTO.getCreateUserId().trim().isEmpty()) {
                wrapper.like(PoliceRisk::getCreateUserId, reqDTO.getCreateUserId().trim());
            }
            // 上报人姓名（模糊）
            if (reqDTO.getCreateUserName() != null && !reqDTO.getCreateUserName().trim().isEmpty()) {
                // 需先查警员ID列表
                List<BasePoliceEmp> emps = basePoliceEmpMapper.selectList(
                        new LambdaQueryWrapper<BasePoliceEmp>()
                                .like(BasePoliceEmp::getName, reqDTO.getCreateUserName().trim())
                                .eq(BasePoliceEmp::getAvailable, true)
                );
                if (getPoliceRiskByEms(wrapper, emps)) return createEmptyPageResult(pageNum, pageSize);
            }
            // 上报单位
            if (reqDTO.getOrgId() != null && !reqDTO.getOrgId().isEmpty() && !"0".equals(reqDTO.getOrgId())) {
                if (isAdmin) {
                    // 管理员可查任意单位及下级
                    List<String> orgIds = orgRecursiveUtils.getOrgAndChildrenIds(reqDTO.getOrgId());
                    List<BasePoliceEmp> ems = basePoliceEmpMapper.selectList(
                            new LambdaQueryWrapper<BasePoliceEmp>()
                                    .in(BasePoliceEmp::getPoliceOrgId, orgIds)
                                    .eq(BasePoliceEmp::getAvailable, true)
                    );
                    if (getPoliceRiskByEms(wrapper, ems)) return createEmptyPageResult(pageNum, pageSize);
                } else {
                    // 普通用户只能查自己及下级单位
                    List<String> allowedOrgIds = orgRecursiveUtils.getOrgAndChildrenIds(currentOrgId);
                    if (!allowedOrgIds.contains(reqDTO.getOrgId())) {
                        // 不允许查非自己及下级单位
                        return createEmptyPageResult(pageNum, pageSize);
                    }
                    // 查指定单位及下级
                    List<String> orgIds = orgRecursiveUtils.getOrgAndChildrenIds(reqDTO.getOrgId());
                    List<BasePoliceEmp> ems = basePoliceEmpMapper.selectList(
                            new LambdaQueryWrapper<BasePoliceEmp>()
                                    .in(BasePoliceEmp::getPoliceOrgId, orgIds)
                                    .eq(BasePoliceEmp::getAvailable, true)
                    );
                    if (!ems.isEmpty()) {
                        List<String> empIds = ems.stream().map(e -> String.valueOf(e.getId())).collect(java.util.stream.Collectors.toList());
                        wrapper.in(PoliceRisk::getCreateUserId, empIds);
                    } else {
                        PageBO pageBO = new PageBO();
                        pageBO.setCurrent((long) pageNum);
                        pageBO.setSize((long) pageSize);
                        return CommonPageVO.empty(pageBO);
                    }
                }
            }
            // 上报时间区间
            if (reqDTO.getStartTime() != null) {
                wrapper.ge(PoliceRisk::getCreateTime, reqDTO.getStartTime());
            }
            if (reqDTO.getEndTime() != null) {
                wrapper.le(PoliceRisk::getCreateTime, reqDTO.getEndTime());
            }
            // 状态筛选
            if (reqDTO.getStatus() != null && !"all".equalsIgnoreCase(reqDTO.getStatus())) {
                if ("todo".equalsIgnoreCase(reqDTO.getStatus())) {
                    // 待处理：未整改和整改中
                    wrapper.in(PoliceRisk::getHasEnd, Arrays.asList(
                            RiskStatusEnum.NOT_FIXED.getCode(),
                            RiskStatusEnum.FIXING.getCode()
                    ));
                } else if ("done".equalsIgnoreCase(reqDTO.getStatus())) {
                    // 已处理：已整改
                    wrapper.eq(PoliceRisk::getHasEnd, RiskStatusEnum.FIXED.getCode());
                }
            }
            // 检查场所筛选（用stationId字段，模糊查询）
            if (reqDTO.getCheckPlace() != null && !reqDTO.getCheckPlace().trim().isEmpty()) {
                wrapper.like(PoliceRisk::getStationId, reqDTO.getCheckPlace().trim());
            }
            // 整改开始时间筛选
            if (reqDTO.getRectifyStartTime() != null) {
                wrapper.ge(PoliceRisk::getRectifyStartTime, reqDTO.getRectifyStartTime());
            }
            // 整改结束时间筛选
            if (reqDTO.getRectifyEndTime() != null) {
                wrapper.le(PoliceRisk::getRectifyEndTime, reqDTO.getRectifyEndTime());
            }
            // 整改结果筛选（注意：避免与状态筛选冲突，只在未设置状态筛选时生效）
            if (reqDTO.getRectifyResult() != null && !reqDTO.getRectifyResult().trim().isEmpty()
                    && (reqDTO.getStatus() == null || "all".equalsIgnoreCase(reqDTO.getStatus()))) {
                Integer rectifyResultCode;
                String rectifyResult = reqDTO.getRectifyResult().trim();

                // 使用枚举工具方法进行状态映射
                rectifyResultCode = RiskStatusEnum.getCodeByName(rectifyResult);

                // 如果枚举匹配失败，尝试直接解析数字
                if (rectifyResultCode == null) {
                    try {
                        rectifyResultCode = Integer.parseInt(rectifyResult);
                    } catch (NumberFormatException e) {
                        log.warn("[隐患分页列表] 整改结果参数格式错误: {}", reqDTO.getRectifyResult());
                    }
                }
                if (rectifyResultCode != null) {
                    wrapper.eq(PoliceRisk::getHasEnd, rectifyResultCode);
                }
            }
            wrapper.orderByDesc(PoliceRisk::getCreateTime);

            // 分页查询
            IPage<PoliceRisk> riskPage = policeRiskMapper.selectPage(page, wrapper);
            List<PoliceRiskVO> voList = riskPage.getRecords().stream().map(this::convertToVO).collect(Collectors.toList());

            CommonPageVO<PoliceRiskVO> result = new CommonPageVO<>();
            result.setTotal(riskPage.getTotal());
            result.setRecords(voList);
            result.setSize(riskPage.getSize());
            result.setCurrent(riskPage.getCurrent());
            return result;
        } catch (Exception e) {
            log.error("[隐患分页列表] 查询异常", e);
            throw new ServerException("隐患分页查询失败", e);
        }
    }

    @Override
    public PoliceRiskDetailVO getRiskDetail(String id) {
        PoliceRisk risk = policeRiskMapper.selectById(id);
        if (risk == null || Boolean.FALSE.equals(risk.getAvailable())) {
            throw new BusinessException("隐患不存在或已删除");
        }
        PoliceRiskDetailVO vo = new PoliceRiskDetailVO();
        vo.setId(risk.getId());
        vo.setStationCode(getStationCode(risk.getStationId()));
        vo.setStationName(getStationName(risk.getStationId()));
        vo.setCreateUserId(risk.getCreateUserId());
        vo.setCreateUserName(userOrgUtils.getUserNameByUserId(risk.getCreateUserId()));
        vo.setType(risk.getType());
        vo.setTypeName(RiskCategoryEnum.getLabelByCode(risk.getType()));
        vo.setCreateTime(risk.getCreateTime());
        vo.setRectifyStartTime(risk.getRectifyStartTime());
        vo.setRectifyEndTime(risk.getRectifyEndTime());
        vo.setChangeResult(RiskStatusEnum.getNameByCode(risk.getHasChange()));
        vo.setRiskUrl(risk.getRiskUrl());
        vo.setChangeUrl(risk.getChangeUrl());
        vo.setHasUrgency(risk.getHasUrgency());
        vo.setDescription(risk.getDescription());
        vo.setCategory(risk.getCategory());
        vo.setContent(risk.getContent());
        vo.setHasChange(risk.getHasChange());
        vo.setCategoryName(CheckCategoryEnum.getLabelByCode(risk.getCategory()));
        // 可根据实际需要继续补充字段
        return vo;
    }

    /**
     * 根据员工信息获取警察风险
     * 此方法用于根据一组员工信息来查询存在的警察风险它通过员工的认证用户ID来筛选风险记录
     * 如果员工列表为空，则默认认为存在风险并返回true
     *
     * @param wrapper 查询条件包装器，用于构建查询警察风险的条件
     * @param ems     员工信息列表，包含需要查询的员工的认证用户ID
     * @return 如果员工列表为空，返回true；否则，根据查询条件更新wrapper后返回false
     */
    private boolean getPoliceRiskByEms(LambdaQueryWrapper<PoliceRisk> wrapper, List<BasePoliceEmp> ems) {
        // 检查员工信息列表是否为空
        if (!ems.isEmpty()) {
            // 非空情况下，提取员工的认证用户ID，用于后续的查询条件
            List<String> empIds = ems.stream().map(e -> String.valueOf(e.getId())).collect(Collectors.toList());
            // 在查询条件中加入员工认证用户ID的条件，以筛选出相关的警察风险记录
            wrapper.in(PoliceRisk::getCreateUserId, empIds);
        } else {
            // 如果员工信息列表为空，直接返回true，表示存在风险
            return true;
        }
        // 如果执行到此处，说明员工列表非空且查询条件已设置，返回false
        return false;
    }

    /**
     * 创建隐患信息
     * <p>
     * 此方法用于处理隐患信息的创建请求它将接收到的PoliceRiskCreateReqDTO对象中的数据
     * 映射到PoliceRisk实体对象中，并保存到数据库中如果当前用户未关联警员信息，
     * 则上报失败此方法还处理异常情况，如果保存失败，抛出ServerException异常
     *
     * @param reqDTO 包含要创建的隐患信息的请求DTO
     * @return 如果保存成功，返回true；否则，抛出异常
     * @throws BusinessException 如果当前用户未关联警员信息，则抛出此异常
     * @throws ServerException   如果保存失败，则抛出此异常
     */
    @Override
    public Boolean createRisk(PoliceRiskCreateReqDTO reqDTO) {
        try {
            PoliceRisk risk = new PoliceRisk();
            // 设置基本字段
            setRiskBasicFields(risk, reqDTO);
            risk.setAvailable(true);
            // 设置创建人
            BasePoliceEmp policeEmp = userOrgUtils.getCurrentUserPoliceEmp();
            if (policeEmp == null) {
                throw new BusinessException("当前用户未关联警员信息，无法上报隐患");
            }
            risk.setCreateUserId(String.valueOf(policeEmp.getId()));
            risk.setCreateTime(LocalDateTime.now());
            // 其它字段可根据需要补充
            return this.save(risk);
        } catch (Exception e) {
            log.error("[隐患上报] 新增失败", e);
            throw new ServerException("隐患上报失败", e);
        }
    }

    @Override
    public Boolean updateRisk(PoliceRiskCreateReqDTO reqDTO) {
        if (reqDTO == null || reqDTO.getId() == null) {
            throw new BusinessException("隐患ID不能为空");
        }
        PoliceRisk risk = policeRiskMapper.selectById(reqDTO.getId());
        if (risk == null || Boolean.FALSE.equals(risk.getAvailable())) {
            throw new BusinessException("隐患不存在或已删除");
        }
        // 更新字段
        setRiskBasicFields(risk, reqDTO);
        // 更新人
        BasePoliceEmp policeEmp = userOrgUtils.getCurrentUserPoliceEmp();
        if (policeEmp == null) {
            throw new BusinessException("当前用户未关联警员信息，无法更新隐患");
        }
        risk.setUpdateUserId(String.valueOf(policeEmp.getId()));
        risk.setUpdateTime(LocalDateTime.now());
        // 其它字段可根据需要补充
        return this.updateById(risk);
    }

    /**
     * 创建空的分页结果
     *
     * @param pageNum  页码
     * @param pageSize 页大小
     * @return 空的分页结果
     */
    private CommonPageVO<PoliceRiskVO> createEmptyPageResult(int pageNum, int pageSize) {
        PageBO pageBO = new PageBO();
        pageBO.setCurrent((long) pageNum);
        pageBO.setSize((long) pageSize);
        return CommonPageVO.empty(pageBO);
    }

    /**
     * 获取站点编码（线路code-站点code格式）
     *
     * @param stationCode 站点编码
     * @return 站点编码
     */
    private String getStationCode(String stationCode) {
        if (stationCode == null) {
            return "未知编码";
        }

        try {
            // 从地铁线路站点关系表查询信息
            LambdaQueryWrapper<BaseSubwayStation> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(BaseSubwayStation::getStationCode, stationCode)
                    .eq(BaseSubwayStation::getAvailable, true)
                    .last("LIMIT 1");
            BaseSubwayStation subwayStation = baseSubwayStationMapper.selectOne(wrapper);

            if (subwayStation != null && StrUtil.isNotBlank(subwayStation.getSubwayCode())
                    && StrUtil.isNotBlank(subwayStation.getStationCode())) {
                return subwayStation.getSubwayCode() + "-" + subwayStation.getStationCode();
            }

            // 如果没有找到线路站点关系，直接返回原始站点编码
            return stationCode;
        } catch (Exception e) {
            log.warn("[获取站点编码] 获取站点编码异常，stationCode: {}", stationCode, e);
            return "未知编码";
        }
    }

    /**
     * 获取站点名称（线路名称-站点名称格式）
     *
     * @param stationCode 站点编码
     * @return 站点名称
     */
    private String getStationName(String stationCode) {
        if (stationCode == null) {
            return "未知场所";
        }

        try {
            // 从地铁线路站点关系表查询信息
            LambdaQueryWrapper<BaseSubwayStation> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(BaseSubwayStation::getStationCode, stationCode)
                    .eq(BaseSubwayStation::getAvailable, true)
                    .last("LIMIT 1");
            BaseSubwayStation subwayStation = baseSubwayStationMapper.selectOne(wrapper);

            if (subwayStation != null && StrUtil.isNotBlank(subwayStation.getSubwayName())
                    && StrUtil.isNotBlank(subwayStation.getStationName())) {
                return subwayStation.getSubwayName() + "-" + subwayStation.getStationName();
            }

            // 如果没有找到线路站点关系，尝试从基础站点表通过编码查询站点名称
            LambdaQueryWrapper<BaseStation> stationWrapper = new LambdaQueryWrapper<>();
            stationWrapper.eq(BaseStation::getStationCode, stationCode)
                    .eq(BaseStation::getAvailable, true)
                    .last("LIMIT 1");
            BaseStation station = baseStationMapper.selectOne(stationWrapper);
            if (station != null && StrUtil.isNotBlank(station.getName())) {
                return station.getName();
            }

            return "未知场所";
        } catch (Exception e) {
            log.warn("[获取站点名称] 获取站点名称异常，stationCode: {}", stationCode, e);
            return "未知场所";
        }
    }

    /**
     * 获取一周内风险查询的相关参数，包括警员ID列表和时间范围
     * 封装重复的代码逻辑，提高代码复用性
     *
     * @return 查询参数对象
     */
    private WeeklyRiskQueryParamsDTO getWeeklyRiskQueryParams() {
        List<BasePoliceEmp> orgEmployees;
        // 判断是否为超级管理员
        if (cn.com.sipsg.common.security.core.util.SecurityUtils.isSuperAdmin()) {
            // 管理员：查所有警员
            LambdaQueryWrapper<BasePoliceEmp> empQueryWrapper = new LambdaQueryWrapper<>();
            empQueryWrapper.eq(BasePoliceEmp::getAvailable, true);
            orgEmployees = basePoliceEmpMapper.selectList(empQueryWrapper);
        } else {
            // 获取当前用户所在组织ID
            String currentOrgId = userOrgUtils.getCurrentUserOrgId();
            if (currentOrgId == null) {
                log.warn("[组织风险数量] 无法获取当前用户组织ID，返回空员工列表");
                orgEmployees = new ArrayList<>();
            } else {
                // 普通用户：查本单位及下级单位所有警员
                List<String> orgIds = orgRecursiveUtils.getOrgAndChildrenIds(currentOrgId);
                if (orgIds == null || orgIds.isEmpty()) {
                    log.warn("[组织风险数量] 无法获取组织ID列表，当前组织ID: {}", currentOrgId);
                    orgEmployees = new ArrayList<>();
                } else {
                    LambdaQueryWrapper<BasePoliceEmp> empQueryWrapper = new LambdaQueryWrapper<>();
                    empQueryWrapper.in(BasePoliceEmp::getPoliceOrgId, orgIds)
                            .eq(BasePoliceEmp::getAvailable, true);
                    orgEmployees = basePoliceEmpMapper.selectList(empQueryWrapper);
                }
            }
        }

        // 提取所有警员ID（转为String）
        List<String> employeeIds = orgEmployees.stream()
                .filter(emp -> emp != null && emp.getId() != null)
                .map(emp -> String.valueOf(emp.getId()))
                .filter(id -> !"null".equals(id) && !id.trim().isEmpty())
                .collect(Collectors.toList());
        
        log.debug("[组织风险数量] 员工ID转换完成，原始员工数量: {}, 有效员工ID数量: {}", 
            orgEmployees.size(), employeeIds.size());

        // 计算一周的时间范围
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = endTime.minusDays(7);

        log.debug("[组织风险数量] 查询参数 - 员工数量: {}, 时间范围: {} 到 {}", employeeIds.size(), startTime, endTime);
        return new WeeklyRiskQueryParamsDTO(employeeIds, startTime, endTime);
    }

    /**
     * 设置隐患基本字段
     *
     * @param risk   需要设置基本字段的隐患对象
     * @param reqDTO 包含隐患信息的请求数据传输对象
     */
    private void setRiskBasicFields(PoliceRisk risk, PoliceRiskCreateReqDTO reqDTO) {
        // 设置隐患所属的站点ID
        risk.setStationId(reqDTO.getStationId());
        // 设置隐患内容
        risk.setContent(reqDTO.getContent());
        // 设置隐患类型
        risk.setType(reqDTO.getType());
        // 设置现场图片URL
        risk.setRiskUrl(reqDTO.getRiskUrl());
        // 设置整改后图片URL
        risk.setChangeUrl(reqDTO.getChangeUrl());
        // 设置隐患是否已变更
        risk.setHasChange(reqDTO.getHasChange());
        // 根据hasChange赋值hasEnd（用枚举）
        setHasEndByHasChange(risk);
        // 设置隐患类别
        risk.setCategory(reqDTO.getCategory());
        // 设置隐患描述
        risk.setDescription(reqDTO.getDescription());
        // 设置隐患整改开始时间
        risk.setRectifyStartTime(reqDTO.getRectifyStartTime());
        // 设置隐患整改结束时间
        risk.setRectifyEndTime(reqDTO.getRectifyEndTime());
    }

    /**
     * 根据hasChange字段设置hasEnd字段
     * 此方法用于根据风险的变更状态来设置风险的结束状态
     * 如果风险的变更状态为0，则表示风险尚未解决，设置hasEnd为对应未解决的状态码
     * 如果风险的变更状态为1，则表示风险已经解决，设置hasEnd为对应已解决的状态码
     * 如果风险的变更状态既不是0也不是1，则抛出业务异常，提示参数有误
     *
     * @param risk 风险对象，包含hasChange字段，此字段用于判断风险的变更状态
     *             此参数不允许为null，否则在尝试访问hasChange字段时会抛出NullPointerException
     */
    private void setHasEndByHasChange(PoliceRisk risk) {
        // 检查风险对象的hasChange字段是否已初始化
        if (risk.getHasChange() != null) {
            // 根据hasChange的值设置hasEnd的值
            if (risk.getHasChange() == 0) {
                // 未整改，设置hasEnd为未解决状态码
                risk.setHasEnd(RiskStatusEnum.NOT_FIXED.getCode());
            } else if (risk.getHasChange() == 1) {
                // 已整改，设置hasEnd为已解决状态码
                risk.setHasEnd(RiskStatusEnum.FIXED.getCode());
            } else {
                // hasChange值非法，抛出业务异常
                throw new BusinessException("现场整改参数有误，只能为0（未整改）或1（已整改）");
            }
        }
    }


    /**
     * 将PoliceRisk实体转换为PoliceRiskVO
     *
     * @param risk PoliceRisk实体
     * @return PoliceRiskVO对象
     */
    private PoliceRiskVO convertToVO(PoliceRisk risk) {
        PoliceRiskVO vo = new PoliceRiskVO();
        vo.setId(risk.getId());
        vo.setCode(String.valueOf(risk.getId()));

        // 设置站点相关信息
        setStationInfo(vo, risk.getStationId());

        // 设置创建用户信息
        setCreateUserInfo(vo, risk.getCreateUserId());

        // 设置风险类型信息
        setRiskTypeInfo(vo, risk.getType());

        // 设置时间信息
        vo.setCreateTime(risk.getCreateTime());
        vo.setRectifyStartTime(risk.getRectifyStartTime());
        vo.setRectifyEndTime(risk.getRectifyEndTime());

        // 设置整改结果
        vo.setChangeResult(RiskStatusEnum.getNameByCode(risk.getHasChange()));

        // 设置检查类别信息
        setCheckCategoryInfo(vo, risk.getCategory());

        return vo;
    }

    /**
     * 设置站点相关信息
     */
    private void setStationInfo(PoliceRiskVO vo, String stationId) {
        vo.setStationCode(stationId != null ? stationId : "");
        vo.setStationName(getStationName(stationId));
    }

    /**
     * 设置创建用户信息
     */
    private void setCreateUserInfo(PoliceRiskVO vo, String createUserId) {
        vo.setCreateUserId(createUserId);
        vo.setCreateUserName(userOrgUtils.getUserNameByUserId(createUserId));
    }

    /**
     * 设置风险类型信息
     */
    private void setRiskTypeInfo(PoliceRiskVO vo, String type) {
        vo.setType(type);
        vo.setTypeName(RiskCategoryEnum.getLabelByCode(type));
    }

    /**
     * 设置检查类别信息
     */
    private void setCheckCategoryInfo(PoliceRiskVO vo, String category) {
        vo.setCategory(category);
        vo.setCategoryName(CheckCategoryEnum.getLabelByCode(category));
    }
}

package cn.com.sipsg.module.business.module.duty.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.List;

/**
 * <AUTHOR> lixt
 * @date : 2025-01-20
 * @Description: 勤务统计VO（合并警力数量和下辖单位统计）
 */
@Data
@Schema(description = "勤务统计VO")
public class DutyStatisticsVO {
    
    // 警力数量相关字段
    @Schema(description = "总警力数量", example = "10")
    private Long empCount;

    @Schema(description = "民警数量", example = "6")
    private Long pcCount;

    @Schema(description = "辅警数量", example = "4")
    private Long scCount;

    @Schema(description = "各组织分组统计")
    private List<OrgEmpCountVO> orgList;
    
    // 下辖单位相关字段
    @Schema(description = "下辖单位数量")
    private Long unitCount;
    
    @Schema(description = "机关单位数量")
    private Long jgdwCount;

    @Schema(description = "派出所数量")
    private Long pcsCount;
}
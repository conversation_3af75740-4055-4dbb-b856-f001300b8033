package cn.com.sipsg.module.business.module.supervision.util;

import cn.com.sipsg.module.business.module.common.util.UserOrgUtils;
import cn.com.sipsg.module.business.module.duty.entity.BasePoliceOrg;
import cn.com.sipsg.module.business.module.duty.mapper.BasePoliceOrgMapper;
import cn.com.sipsg.module.business.module.supervision.entity.StatisticsTrace;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class StatisticsTraceQueryUtils {
    private final UserOrgUtils userOrgUtils;
    private final BasePoliceOrgMapper basePoliceOrgMapper;

    public StatisticsTraceQueryUtils(UserOrgUtils userOrgUtils, BasePoliceOrgMapper basePoliceOrgMapper) {
        this.userOrgUtils = userOrgUtils;
        this.basePoliceOrgMapper = basePoliceOrgMapper;
    }

    /**
     * 构建当前用户及下级单位的分页查询条件
     * 该方法用于构建一个查询包装器，用于查询当前用户及其下级单位的相关统计追踪信息
     * 主要逻辑包括：获取当前用户所在单位ID，递归获取该单位及其所有子单位ID，
     * 并根据这些ID构建查询条件如果提供了时间范围，则添加到查询条件中
     * 最后，查询结果将按表单时间降序排序
     *
     * @param policeOrgId 警察单位ID，如果提供，则使用该ID而不是当前用户所在单位ID
     * @param startTime   开始时间，如果提供，则查询条件中包含大于等于该时间的记录
     * @param endTime     结束时间，如果提供，则查询条件中包含小于等于该时间的记录
     * @return 返回一个配置好的查询包装器，用于执行分页查询
     */
    public LambdaQueryWrapper<StatisticsTrace> buildCurrentUserOrgQueryWrapper(String policeOrgId, LocalDateTime startTime, LocalDateTime endTime) {
        // 获取当前用户所在单位ID
        String orgId = userOrgUtils.getCurrentUserOrgId();
        // 递归获取orgId及其所有子机构ID
        List<String> orgIds = getOrgAndChildrenIds(policeOrgId != null ? policeOrgId : orgId);
        // 初始化查询包装器
        LambdaQueryWrapper<StatisticsTrace> wrapper = new LambdaQueryWrapper<>();
        // 添加单位ID查询条件
        wrapper.in(StatisticsTrace::getPoliceOrgId, orgIds);
        // 如果提供了开始时间，添加到查询条件中
        if (startTime != null) {
            wrapper.ge(StatisticsTrace::getFormTime, startTime);
        }
        // 如果提供了结束时间，添加到查询条件中
        if (endTime != null) {
            wrapper.le(StatisticsTrace::getFormTime, endTime);
        }
        // 添加按表单时间降序排序条件
        wrapper.orderByDesc(StatisticsTrace::getFormTime);
        // 返回配置好的查询包装器
        return wrapper;
    }

    /**
     * 递归获取指定orgId及其所有子机构ID（含自身）
     *
     * @param orgId 要查询的机构ID
     * @return 包含指定机构及其所有子机构ID的列表
     */
    public List<String> getOrgAndChildrenIds(String orgId) {
        // 查询所有机构信息
        List<BasePoliceOrg> allOrg = basePoliceOrgMapper.selectList(null);
        // 使用HashSet存储机构ID，以避免重复
        Set<String> result = new HashSet<>();
        // 递归收集指定机构及其子机构的ID
        collectOrgAndChildrenIds(allOrg, orgId, result);
        // 将集合转换为列表返回
        return new java.util.ArrayList<>(result);
    }

    /**
     * 递归收集指定组织及其子组织的ID
     *
     * @param allOrg   完整的组织列表，用于查找组织及其子组织
     * @param parentId 当前需要收集的组织的父ID，初始调用时应为根组织的ID
     * @param result   用于存储收集到的组织ID的集合，随着递归的进行不断累积
     */
    private void collectOrgAndChildrenIds(List<BasePoliceOrg> allOrg, String parentId, java.util.Set<String> result) {
        // 将当前组织的ID添加到结果集中
        result.add(parentId);
        // 遍历所有组织，寻找父ID匹配的组织
        for (BasePoliceOrg org : allOrg) {
            // 如果当前组织的父ID与参数parentId匹配，则递归调用本方法收集其子组织的ID
            if (parentId.equals(org.getParentId())) {
                collectOrgAndChildrenIds(allOrg, org.getId(), result);
            }
        }
    }

    /**
     * 将时间戳格式化为人类可读的日期和时间字符串
     * 此方法主要用于将数据库或日志文件中的时间戳转换为用户友好的格式
     * 如果时间戳为null，则返回空字符串，以避免抛出NullPointerException
     *
     * @param ts 时间戳，自1970年1月1日00:00:00 GMT以来的毫秒数
     * @return 格式化后的日期和时间字符串，如果输入为null，则返回空字符串
     */
    public String formatTimestamp(LocalDateTime ts) {
        // 检查时间戳是否为null，如果是，则返回空字符串
        if (ts == null) return "";

        // 创建一个DateTimeFormatter实例，用于格式化日期和时间
        // 指定输出格式为"yyyy-MM-dd HH:mm:ss"，即年-月-日 时:分:秒
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 将时间戳转换为Instant对象，然后将其转换为本地时区的LocalDateTime对象
        // 使用之前创建的DateTimeFormatter格式化LocalDateTime对象，并返回结果字符串
        return ts.atZone(ZoneId.systemDefault()).toLocalDateTime().format(fmt);
    }
}
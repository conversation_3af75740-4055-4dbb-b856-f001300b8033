package cn.com.sipsg.module.business.module.supervision.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 服务群众汇总统计DTO
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Data
@Schema(description = "服务群众汇总统计DTO")
public class CivilServiceStatisticsDTO {

    @Schema(description = "服务次数", example = "120")
    private Integer serviceCount;

    @Schema(description = "服务对象数量", example = "85")
    private Integer serviceObjectCount;

    @Schema(description = "满意度百分比", example = "95.5")
    private Double satisfactionRate;
}
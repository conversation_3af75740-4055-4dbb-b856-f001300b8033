package cn.com.sipsg.module.business.module.supervision.controller;

import cn.com.sipsg.common.pojo.CommonResult;
import cn.com.sipsg.common.web.core.controller.BaseController;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.PoliceRiskDetailVO;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.PoliceRiskVO;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.RiskCountVO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.PoliceRiskPageReqDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.PoliceRiskCreateReqDTO;
import cn.com.sipsg.module.business.module.supervision.service.PoliceRiskService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 警务监督管理控制器
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Tag(name = "治安隐患查控", description = "治安隐患查控相关接口")
@RestController
@RequestMapping("/policeRisk")
@RequiredArgsConstructor
public class PoliceRiskController extends BaseController {

    private final PoliceRiskService policeRiskService;

    /**
     * 隐患分页列表
     * 支持全部/待处理/已处理切换，支持多条件查询，分页返回
     * 数据权限管理：
     * - 管理员全部隐患时可以看所有的
     * - 普通用户全部隐患时只能看当前单位及下级单位的
     * - 普通用户本单位隐患时查询本单位的
     * @param reqDTO 查询参数，可选
     * @return 分页隐患列表
     */
    @Operation(summary = "隐患分页列表", description = "支持全部/待处理/已处理切换，支持多条件查询，分页返回。数据权限：管理员全部隐患看所有，普通用户全部隐患看本单位及下级，普通用户本单位隐患看本单位")
    @PostMapping("/risk/page")
    public CommonResult<CommonPageVO<PoliceRiskVO>> riskPage(@RequestBody(required = false) PoliceRiskPageReqDTO reqDTO) {
        return handle(() -> CommonResult.data(policeRiskService.pageRiskList(reqDTO)));
    }

    /**
     * 治安隐患查控统计汇总
     * 统计当前用户所在组织内所有成员一周内的查控点数量、安检任务数量、隐患处理数量
     *
     * @return 组织查控数量结果
     */
    @Operation(summary = "治安隐患查控统计汇总", description = "统计当前用户所在组织内所有成员一周内的查控点数量、安检任务数量、隐患处理数量")
    @GetMapping("/risk/org-stats")
    public CommonResult<RiskCountVO> orgRiskStats() {
        return handle(() -> CommonResult.data(policeRiskService.getOrgWeeklyRiskCount()));
    }

    /**
     * 隐患上报/修改
     * <p>
     * 图片请先通过 /system/upms/file/upload 上传到MinIO，riskUrl字段填图片url。
     * <br>id 为空为新增，id 不为空为修改。
     * </p>
     * @param reqDTO 隐患信息参数，id为空为新增，id不为空为修改
     * @return 操作结果，true为成功
     */
    @Operation(summary = "隐患上报/修改", description = "新增或修改隐患信息。图片请先通过 /system/upms/file/upload 上传到MinIO，riskUrl字段填图片url。id 为空为新增，id 不为空为修改。")
    @PostMapping("/risk/report")
    public CommonResult<Boolean> reportRisk(@RequestBody PoliceRiskCreateReqDTO reqDTO) {
        if (reqDTO.getId() == null) {
            return handle(() -> CommonResult.data(policeRiskService.createRisk(reqDTO)));
        } else {
            return handle(() -> CommonResult.data(policeRiskService.updateRisk(reqDTO)));
        }
    }

    /**
     * 隐患详情
     * @param id 隐患ID
     * @return 隐患详情
     */
    @Operation(summary = "隐患详情", description = "根据ID查询隐患详情")
    @GetMapping("/risk/detail")
    public CommonResult<PoliceRiskDetailVO> riskDetail(String id) {
        return handle(() -> CommonResult.data(policeRiskService.getRiskDetail(id)));
    }
}
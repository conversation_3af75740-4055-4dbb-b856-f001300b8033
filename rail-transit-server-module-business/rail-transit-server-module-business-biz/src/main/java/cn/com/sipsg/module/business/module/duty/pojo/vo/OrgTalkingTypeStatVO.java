package cn.com.sipsg.module.business.module.duty.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.Map;

@Data
@Schema(description = "组织-谈话类型统计VO")
public class OrgTalkingTypeStatVO {
    @Schema(description = "组织ID")
    private String orgId;
    @Schema(description = "组织名称")
    private String orgName;
    @Schema(description = "各谈话类型数量，key为类型枚举名，value为数量")
    private Map<String, Integer> talkingTypeCount;
}
package cn.com.sipsg.module.business.module.supervision.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;
import cn.com.sipsg.common.pojo.bo.PageBO;

/**
 * 通报信息分页查询请求DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "通报信息分页查询请求DTO")
public class NoticeReportPageReqDTO extends PageBO {

    @Schema(description = "通报标题，模糊匹配")
    private String title;

    @Schema(description = "签发单位ID")
    private String orgId;

    @Schema(description = "签发时间-开始", example = "2023-01-01 00:00:00")
    private LocalDateTime startTime;

    @Schema(description = "签发时间-结束", example = "2023-01-31 23:59:59")
    private LocalDateTime endTime;

    @Schema(description = "筛选类型：ALL-全部通报，ISSUED-我下发的，RECEIVED-我接收的", allowableValues = {"ALL", "ISSUED", "RECEIVED"})
    private String filterType = "ALL";
}
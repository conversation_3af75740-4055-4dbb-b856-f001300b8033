package cn.com.sipsg.module.business.module.duty.pojo.dto.enums;

import cn.com.sipsg.common.enums.BaseEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 学历枚举
 */
@Getter
@AllArgsConstructor
public enum EducationEnum implements BaseEnum {

    PRIMARY_SCHOOL(1, "小学"),
    JUNIOR_HIGH(2, "初中"),
    SENIOR_HIGH(3, "高中"),
    TECHNICAL_SCHOOL(4, "中专"),
    COLLEGE(5, "大专"),
    BACHELOR(6, "本科"),
    MASTER(7, "硕士研究生"),
    DOCTORATE(8, "博士研究生"),
    OTHER(9, "其他");

    @EnumValue
    private final Integer value;
    private final String desc;

}
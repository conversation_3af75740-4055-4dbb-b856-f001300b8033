package cn.com.sipsg.module.business.module.supervision.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@Schema(description = "我的考试详情VO")
public class MyExamDetailVO {
    @Schema(description = "考试任务名称")
    private String taskName;
    @Schema(description = "分数")
    private Integer score;
    @Schema(description = "合格/不合格")
    private String result;
    @Schema(description = "考试信息（时长/总分/合格分/题数）")
    private String examInfo;
    @Schema(description = "考试时间区间")
    private String examTimeRange;
    @Schema(description = "考试人员")
    private String personName;
    @Schema(description = "实际开始时间")
    private LocalDateTime startTime;
    @Schema(description = "考试时长")
    private String duration;
} 
package cn.com.sipsg.module.business.module.supervision.service;

import cn.com.sipsg.module.business.module.supervision.entity.DeviceFaultReport;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.DeviceFaultReportDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.DeviceFaultReportPageDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 设备故障上报表 服务类
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
public interface DeviceFaultReportService extends IService<DeviceFaultReport> {

    /**
     * 保存设备故障上报
     *
     * @param dto 设备故障上报信息
     * @return 设备故障上报ID
     */
    Boolean saveDeviceFaultReport(DeviceFaultReportDTO dto);

    /**
     * 获取设备故障上报分页列表
     *
     * @param pageDTO 分页查询参数
     * @return 分页结果
     */
    IPage<DeviceFaultReportDTO> getDeviceFaultReportPage(DeviceFaultReportPageDTO pageDTO);

    /**
     * 获取设备故障上报详情
     *
     * @param id 设备故障上报ID
     * @return 设备故障上报详情
     */
    DeviceFaultReportDTO getDeviceFaultReportDetail(String id);

    /**
     * 删除设备故障上报
     *
     * @param id 设备故障上报ID
     * @return 是否删除成功
     */
    Boolean deleteDeviceFaultReport(String id);

    /**
     * 批量删除设备故障上报
     *
     * @param ids 设备故障上报ID列表
     * @return 是否删除成功
     */
    Boolean batchDeleteDeviceFaultReport(List<String> ids);
}

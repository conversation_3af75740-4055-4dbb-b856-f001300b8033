package cn.com.sipsg.module.business.module.supervision.mapper;

import cn.com.sipsg.module.business.module.supervision.entity.ExamTask;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 考试任务数据访问接口
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Mapper
public interface ExamTaskMapper extends BaseMapper<ExamTask> {

    /**
     * 查询组织内的待办考试任务
     * 条件：当前时间在考试结束时间之前的
     *
     * @param orgId 组织ID
     * @param currentTime 当前时间
     * @return 考试任务列表
     */
    @Select("SELECT t.* FROM tbl_exam_task t " +
           "WHERE t.available = true " +
           "AND t.end_time > #{currentTime} " +
           "AND EXISTS (" +
           "    SELECT 1 FROM tbl_exam_task_org o " +
           "    WHERE o.task_id = t.id " +
           "    AND o.org_id = #{orgId}" +
           ") " +
           "ORDER BY t.end_time ASC")
    List<ExamTask> findPendingTasksByOrgId(@Param("orgId") Long orgId, @Param("currentTime") LocalDateTime currentTime);
}

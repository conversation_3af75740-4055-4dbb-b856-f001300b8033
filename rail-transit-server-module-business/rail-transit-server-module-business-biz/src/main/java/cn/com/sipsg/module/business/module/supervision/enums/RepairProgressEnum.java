package cn.com.sipsg.module.business.module.supervision.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 维修进度枚举
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Getter
@AllArgsConstructor
public enum RepairProgressEnum {

    /**
     * 已派单
     */
    DISPATCHED("dispatched", "已派单"),

    /**
     * 待处理
     */
    PENDING("pending", "待处理"),

    /**
     * 维修中
     */
    REPAIRING("repairing", "维修中"),

    /**
     * 已修复
     */
    REPAIRED("repaired", "已修复");

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String description;

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举值
     */
    public static RepairProgressEnum getByCode(String code) {
        for (RepairProgressEnum progress : values()) {
            if (progress.getCode().equals(code)) {
                return progress;
            }
        }
        return null;
    }

    /**
     * 根据描述获取枚举
     *
     * @param description 描述
     * @return 枚举值
     */
    public static RepairProgressEnum getByDescription(String description) {
        for (RepairProgressEnum progress : values()) {
            if (progress.getDescription().equals(description)) {
                return progress;
            }
        }
        return null;
    }
}
package cn.com.sipsg.module.business.module.supervision.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 地上地下一体化联勤统计汇总VO
 *
 * 典型场景：返回地面警员数量、地铁警员数、警车数、摩托车数
 */
@Data
public class IntegratedJointDutySummaryVO {

    @Schema(description = "地面警员数量")
    private Long groundPoliceCount;

    @Schema(description = "地铁警员数量")
    private Long subwayPoliceCount;

    @Schema(description = "警车数量")
    private Long policeCarCount;

    @Schema(description = "摩托车数量")
    private Long motorcycleCount;
} 
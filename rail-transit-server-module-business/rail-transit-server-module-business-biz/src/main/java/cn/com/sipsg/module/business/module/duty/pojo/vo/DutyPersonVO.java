package cn.com.sipsg.module.business.module.duty.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDate;

@Data
@Schema(description = "值班人员信息", example = "{\"id\":1,\"empId\":10001,\"empName\":\"张三\",\"postId\":1,\"postName\":\"巡逻岗\",\"turnId\":1,\"turnName\":\"白班\",\"dutyDate\":\"2025-05-10\"}")
public class DutyPersonVO {
    @Schema(description = "值班人员ID", example = "10001")
    private String empId;
    
    @Schema(description = "值班人员姓名", example = "张三")
    private String empName;
    
    @Schema(description = "岗位ID", example = "1")
    private String postId;
    
    @Schema(description = "岗位名称", example = "巡逻岗")
    private String postName;
    
    @Schema(description = "班次ID", example = "1")
    private String turnId;
    
    @Schema(description = "班次名称", example = "白班")
    private String turnName;
    
    @Schema(description = "值班日期", example = "2025-05-10")
    private LocalDate dutyDate;
    
    @Schema(description = "值班ID", example = "1")
    private String id;
}
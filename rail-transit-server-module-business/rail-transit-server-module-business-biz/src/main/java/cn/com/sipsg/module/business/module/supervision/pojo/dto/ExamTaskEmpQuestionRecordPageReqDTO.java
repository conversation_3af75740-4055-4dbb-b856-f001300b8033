package cn.com.sipsg.module.business.module.supervision.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "考试人员答题记录分页请求DTO")
public class ExamTaskEmpQuestionRecordPageReqDTO {
    @Schema(description = "人员ID", required = true)
    private String empId;
    @Schema(description = "考试任务ID", required = true)
    private String taskId;
    @Schema(description = "页码", example = "1")
    private Integer pageNum = 1;
    @Schema(description = "每页数量", example = "10")
    private Integer pageSize = 10;
} 
package cn.com.sipsg.module.business.module.supervision.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 安检人员数量统计VO
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "安检人员数量统计VO")
public class SecurityEmpCountVO {

    @Schema(description = "安检人员数量")
    private Integer count;
}

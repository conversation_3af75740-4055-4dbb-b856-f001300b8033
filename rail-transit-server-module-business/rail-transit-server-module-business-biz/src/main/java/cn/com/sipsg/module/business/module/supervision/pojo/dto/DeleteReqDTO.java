package cn.com.sipsg.module.business.module.supervision.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 删除操作请求DTO
 */
@Data
@Schema(description = "删除操作请求参数")
public class DeleteReqDTO {
    
    @Schema(description = "要删除的记录ID", required = true, example = "1")
    @NotNull(message = "记录ID不能为空")
    private String id;
}

package cn.com.sipsg.module.business.module.supervision.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;

/**
 * 绩效分数表
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("tbl_performance_score")
public class PerformanceScore {

    /**
     * 主键，自增
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 创建人id
     */
    @TableField("create_user_id")
    private String createUserId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新人id
     */
    @TableField("update_user_id")
    private String updateUserId;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否有效
     */
    @TableField("available")
    private Boolean available = true;

    /**
     * 绩效考核项id
     */
    @TableField("performance_item_id")
    private String performanceItemId;

    /**
     * 考核分数
     */
    @TableField("score")
    private Double score;

    /**
     * 被考核单位id
     */
    @TableField("org_id")
    private String orgId;

    /**
     * 绩效填报表id
     */
    @TableField("performance_form_id")
    private String performanceFormId;
}
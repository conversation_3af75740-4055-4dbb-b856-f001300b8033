package cn.com.sipsg.module.business.module.supervision.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDate;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import java.io.IOException;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

@Data
@Schema(description = "安检员创建/修改请求DTO")
public class SecurityEmpCreateReqDTO {
    @Schema(description = "安检员ID，新增时为空，修改时必填", example = "1234567890123456789")
    private String id;

    @Schema(description = "姓名", required = true, example = "张三")
    private String name;

    @Schema(description = "身份证号", required = true, example = "110101199001011234")
    private String idCard;

    @Schema(description = "性别", required = true, example = "男")
    private String gender;

    @Schema(description = "民族（可选值：01-汉族，02-蒙古族，03-回族，...，56-基诺族）", example = "01")
    private String nation;

    @Schema(description = "学历（可选值：1-小学，2-初中，3-高中，4-大专，5-本科，6-硕士，7-博士）", example = "5")
    private String education;

    @Schema(description = "联系方式", required = true, example = "13800138000")
    private String contact;

    @Schema(description = "户籍地址", example = "安徽省合肥市蜀山区")
    private String householdAddress;

    @Schema(description = "现住地址", example = "安徽省合肥市蜀山区长江西路123号")
    private String currentAddress;

    @Schema(description = "头像图片URL", example = "https://example.com/images/security001.jpg")
    private String imageUrl;

    @Schema(description = "安检公司ID", required = true, example = "1234567890123456789")
    private String companyId;

    @Schema(description = "安检公司名称", required = true, example = "安检公司001")
    private String companyName;

    @Schema(description = "在职状态（0-在职、1-离职、2-退休、3-未知）", example = "0")
    private String jobStatus;

    @Schema(description = "入职时间", example = "2020-01-01")
    @JsonDeserialize(using = FlexibleLocalDateDeserializer.class)
    private LocalDate entryDate;

    @Schema(description = "离职时间", example = "")
    @JsonDeserialize(using = FlexibleLocalDateDeserializer.class)
    private LocalDate leaveDate;

    @Schema(description = "有无安检证", example = "true")
    private Boolean hasSecurityCert;

    @Schema(description = "有无安保证", example = "true")
    private Boolean hasSecurityGuardCert;

    @Schema(description = "保安证号", example = "BA20240001")
    private String securityGuardCertNo;

    @Schema(description = "安检证号", example = "AJ20240001")
    private String securityCertNo;
    
    @Schema(description = "是否培训", example = "true")
    private Boolean hasTrain;
}

// 自定义LocalDate反序列化器，支持yyyy-MM-dd和yyyy-MM-dd HH:mm:ss
class FlexibleLocalDateDeserializer extends JsonDeserializer<LocalDate> {
    @Override
    public LocalDate deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        String value = p.getText().trim();
        if (value.isEmpty()) return null;
        try {
            return LocalDate.parse(value, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        } catch (DateTimeParseException e) {
            // 尝试 yyyy-MM-dd HH:mm:ss
            try {
                return LocalDate.parse(value.substring(0, 10), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            } catch (Exception ex) {
                throw new RuntimeException("日期格式不支持: " + value);
            }
        }
    }
}
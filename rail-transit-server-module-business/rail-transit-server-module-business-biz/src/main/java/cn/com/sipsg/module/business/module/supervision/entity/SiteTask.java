package cn.com.sipsg.module.business.module.supervision.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

@Data
@TableName("tbl_site_task")
public class SiteTask {
    /** 记录唯一标识主键 */
    @TableId("id")
    private Long id;
    /** 记录创建时间 */
    @TableField("create_time")
    private Long createTime;
    /** 记录最后更新时间 */
    @TableField("update_time")
    private Long updateTime;
    /** 创建者用户ID */
    @TableField("create_user_id")
    private String createUserId;
    /** 最后更新者用户ID */
    @TableField("update_user_id")
    private String updateUserId;
    /** 是否有效 */
    @TableField("available")
    private Boolean available;
    /** 巡检ID */
    @TableField("inspection_id")
    private Long inspectionId;
    /** 检查单ID（子计划） */
    @TableField("subplan_id")
    private Long subplanId;
    /** 场所ID */
    @TableField("place_id")
    private Long placeId;
    /** 检查单状态 0 待认领 1 待核查 2 未提交 3 已核查 4 缺核查 */
    @TableField("status")
    private Integer status;
    /** 检查单ID */
    @TableField("checklist_id")
    private Long checklistId;
    /** 填表人 */
    @TableField("fill_user_id")
    private Long fillUserId;
    /** 检查单名称 */
    @TableField("checklist_name")
    private String checklistName;
    /** 子计划名称 */
    @TableField("subplan_name")
    private String subplanName;
    /** 填表时间 */
    @TableField("fill_time")
    private Long fillTime;
    /** 检查情况 */
    @TableField("check_content")
    private String checkContent;
    /** 现场图片 */
    @TableField("check_img")
    private String checkImg;
    /** 任务类型，同巡检类型 */
    @TableField("task_type")
    private Integer taskType;
    /** 完成时间 */
    @TableField("finish_time")
    private Long finishTime;
    /** 风险隐患工作流ID，整改复查任务时使用 */
    @TableField("risk_instance_id")
    private String riskInstanceId;
    /** 任务开始时间 */
    @TableField("start_time")
    private Long startTime;
    /** 任务截止时间 */
    @TableField("end_time")
    private Long endTime;
    /** 领取任务时间 */
    @TableField("get_task_time")
    private Long getTaskTime;
} 
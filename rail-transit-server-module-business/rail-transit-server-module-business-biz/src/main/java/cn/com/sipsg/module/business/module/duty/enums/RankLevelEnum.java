package cn.com.sipsg.module.business.module.duty.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum RankLevelEnum {
    FIRST_CLASS_POLICE_COMMISSIONER("01", "一级警务专员"),
    SECOND_CLASS_POLICE_COMMISSIONER("02", "二级警务专员"),
    FIRST_CLASS_SENIOR_SERGEANT("03", "一级高级警长"),
    SECOND_CLASS_SENIOR_SERGEANT("04", "二级高级警长"),
    THIRD_CLASS_SENIOR_SERGEANT("05", "三级高级警长"),
    FOURTH_CLASS_SENIOR_SERGEANT("06", "四级高级警长"),
    FIRST_CLASS_SERGEANT("07", "一级警长"),
    SECOND_CLASS_SERGEANT("08", "二级警长"),
    THIRD_CLASS_SERGEANT("09", "三级警长"),
    FOURTH_CLASS_SERGEANT("10", "四级警长"),
    FIRST_CLASS_CONSTABLE("11", "一级警员"),
    SECOND_CLASS_CONSTABLE("12", "二级警员"),
    POLICE_TECHNOLOGY_FIRST_CLASS_DIRECTOR("13", "警务技术一级总监"),
    POLICE_TECHNOLOGY_SECOND_CLASS_DIRECTOR("14", "警务技术二级总监"),
    POLICE_TECHNOLOGY_FIRST_CLASS_MANAGER("15", "警务技术一级主任"),
    POLICE_TECHNOLOGY_SECOND_CLASS_MANAGER("16", "警务技术二级主任"),
    POLICE_TECHNOLOGY_THIRD_CLASS_MANAGER("17", "警务技术三级主任"),
    POLICE_TECHNOLOGY_SENIOR_MANAGER("18", "警务技术高级主任"),
    POLICE_TECHNOLOGY_FIRST_CLASS_SUPERVISOR("19", "警务技术一级主管"),
    POLICE_TECHNOLOGY_SECOND_CLASS_SUPERVISOR("20", "警务技术二级主管"),
    POLICE_TECHNOLOGY_THIRD_CLASS_SUPERVISOR("21", "警务技术三级主管"),
    POLICE_TECHNOLOGY_FOURTH_CLASS_SUPERVISOR("22", "警务技术四级主管"),
    POLICE_TECHNICIAN("23", "警务技术员"),
    ZHENG_JU_JI("24", "正局级"),
    FU_JU_JI("25", "副局级"),
    ZHENG_CHU_JI("26", "正处级"),
    FU_CHU_JI("27", "副处级"),
    ZHENG_KE_JI("28", "正科级"),
    FU_KE_JI("29", "副科级"),
    KE_YUAN("30", "科员");

    @EnumValue
    private final String code;
    
    @JsonValue
    private final String label;

    RankLevelEnum(String code, String label) {
        this.code = code;
        this.label = label;
    }

    public static boolean isValid(String value) {
        for (RankLevelEnum e : values()) {
            if (e.label.equals(value) || e.code.equals(value)) {
                return true;
            }
        }
        return false;
    }

    public static String getLabelByCode(String code) {
        for (RankLevelEnum e : values()) {
            if (e.code.equals(code)) {
                return e.label;
            }
        }
        return null;
    }

    public static String getCodeByLabel(String label) {
        for (RankLevelEnum e : values()) {
            if (e.label.equals(label)) {
                return e.code;
            }
        }
        return null;
    }

    public static RankLevelEnum getByCode(String code) {
        for (RankLevelEnum e : values()) {
            if (e.code.equals(code)) {
                return e;
            }
        }
        return null;
    }
}
package cn.com.sipsg.module.business.module.duty.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> lixt
 * @date : 2025-05-08 11:18:18
 * @Description: 排班组表
 */
@Data
@TableName("tbl_duty_platoon_group")
public class DutyPlatoonGroup {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id; // 主键

    @TableField("name")
    private String name; // 排班组名称

    @TableField("police_org_id")
    private String policeOrgId; // 单位主键

    @TableField("police_org_name")
    private String policeOrgName; // 单位名称

    @TableField("create_time")
    private LocalDateTime createTime; // 创建时间

    @TableField("create_user_id")
    private String createUserId; // 创建者ID

    @TableField("update_time")
    private LocalDateTime updateTime; // 更新时间

    @TableField("update_user_id")
    private String updateUserId; // 更新者ID

    @TableField("available")
    private Boolean available; // 是否有效

    @TableField("order_no")
    private Integer orderNo; // 排序序号

    @TableField("type")
    private Integer type; // 排班组类型

    @TableField("is_success")
    private Boolean isSuccess; // 是否添加成功
}

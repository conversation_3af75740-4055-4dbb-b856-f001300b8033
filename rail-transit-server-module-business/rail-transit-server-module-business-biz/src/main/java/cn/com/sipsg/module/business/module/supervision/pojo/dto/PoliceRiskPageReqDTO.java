package cn.com.sipsg.module.business.module.supervision.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

import cn.com.sipsg.common.pojo.bo.PageBO;

/**
 * 隐患分页查询参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "隐患分页查询参数")
public class PoliceRiskPageReqDTO extends PageBO {
    @Schema(description = "状态：all全部、todo待处理、done已处理", example = "all")
    private String status;

    @Schema(description = "查询范围：all-全部隐患（管理员看所有，普通用户看本单位及下级），org-本单位隐患（所有用户都看本单位及下级）", example = "all")
    private String scope;

    @Schema(description = "上报人姓名，模糊查询", example = "张三")
    private String createUserName;

    @Schema(description = "上报人警号，模糊查询", example = "2001")
    private String createUserId;

    @Schema(description = "上报人所属单位ID", example = "1001")
    private String orgId;

    @Schema(description = "上报开始时间", example = "2025-07-29 00:00:00")
    private LocalDateTime startTime;

    @Schema(description = "上报结束时间", example = "2024-12-31 23:59:59")
    private LocalDateTime endTime;

    @Schema(description = "整改开始时间", example = "2024-01-15 09:00:00")
    private LocalDateTime rectifyStartTime;

    @Schema(description = "整改结束时间", example = "2024-01-20 17:00:00")
    private LocalDateTime rectifyEndTime; 

    @Schema(description = "检查场所", example = "S101")
    private String checkPlace;

    @Schema(description = "隐患类型：1-设备设施类隐患，2-人员行为类隐患，3-环境及外部风险类隐患，4-管理及制度类隐患，5-专项排查类隐患", example = "1")
    private String type;

    @Schema(description = "整改结果：1-已整改，0-未整改", example = "1")
    private String rectifyResult;
}
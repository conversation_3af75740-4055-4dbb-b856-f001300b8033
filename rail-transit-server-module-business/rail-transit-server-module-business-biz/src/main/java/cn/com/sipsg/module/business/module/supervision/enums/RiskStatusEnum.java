package cn.com.sipsg.module.business.module.supervision.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 整改结果枚举
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Getter
@AllArgsConstructor
public enum RiskStatusEnum {
    NOT_FIXED(0, "未整改"),
    FIXING(2, "整改中"),
    FIXED(1, "已整改");

    private final Integer code;
    private final String name;

    public static RiskStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (RiskStatusEnum status : values()) {
            if (code.equals(status.getCode())) {
                return status;
            }
        }
        return null;
    }

    public static String getNameByCode(Integer code) {
        RiskStatusEnum status = getByCode(code);
        return status != null ? status.getName() : "未知状态";
    }

    /**
     * 根据状态名称获取枚举对象
     * @param name 状态名称
     * @return 对应的枚举对象，未找到时返回null
     */
    public static RiskStatusEnum getByName(String name) {
        if (name == null || name.trim().isEmpty()) {
            return null;
        }
        for (RiskStatusEnum status : values()) {
            if (status.getName().equals(name.trim())) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据状态名称获取状态代码
     * @param name 状态名称
     * @return 对应的状态代码，未找到时返回null
     */
    public static Integer getCodeByName(String name) {
        RiskStatusEnum status = getByName(name);
        return status != null ? status.getCode() : null;
    }
}

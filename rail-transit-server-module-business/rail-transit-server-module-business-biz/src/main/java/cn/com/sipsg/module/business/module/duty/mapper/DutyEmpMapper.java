package cn.com.sipsg.module.business.module.duty.mapper;


import cn.com.sipsg.common.mybatis.core.mapper.BaseMapperX;
import cn.com.sipsg.module.business.module.duty.entity.DutyEmp;
import cn.com.sipsg.module.business.module.duty.pojo.dto.DutyEmpDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR> lixt
 * @date : 2025-04-24 13:45:44
 * @Description:
 */
@Mapper
public interface DutyEmpMapper extends BaseMapperX<DutyEmp> {
    /**
     * 根据值班日期和组织类型获取员工信息
     *
     * @param dutyDate 值班日期
     * @param type     组织类型
     * @return 包含员工信息的列表
     */
    @Select("SELECT DISTINCT de.emp_id, bpo.id AS police_org_id, bpo.name AS police_org_name " +
            "FROM tbl_base_police_org bpo, tbl_duty_emp de " +
            "WHERE de.available = TRUE " +
            "AND de.police_org_id = bpo.id " +
            "AND de.duty_date = #{dutyDate} " +
            "AND bpo.type = #{type}")
    List<DutyEmpDTO> getEmpByDate(String dutyDate, Integer type);
}

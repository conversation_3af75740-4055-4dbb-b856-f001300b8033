package cn.com.sipsg.module.business.module.supervision.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.*;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(description = "考试任务更新请求DTO")
public class ExamTaskUpdateReqDTO {

    @NotBlank(message = "考试任务ID不能为空")
    @Schema(description = "考试任务ID", required = true, example = "1234567890123456789")
    private String id;

    @NotBlank(message = "考试任务名称不能为空")
    @Size(max = 100, message = "考试任务名称长度不能超过100个字符")
    @Schema(description = "考试任务名称", required = true, example = "政工考核考试")
    private String taskName;

    @Schema(description = "考试备注", example = "本次考试为季度考核")
    private String taskRemark;

    @NotNull(message = "考试开始时间不能为空")
    @Schema(description = "考试开始时间", required = true, example = "2025-06-01 09:00:00")
    private LocalDateTime startTime;

    @NotNull(message = "考试结束时间不能为空")
    @Schema(description = "考试结束时间", required = true, example = "2025-06-30 18:00:00")
    private LocalDateTime endTime;

    @NotNull(message = "考试时长不能为空")
    @Min(value = 1, message = "考试时长必须大于0分钟")
    @Max(value = 480, message = "考试时长不能超过480分钟")
    @Schema(description = "考试时长（分钟）", required = true, example = "120")
    private Integer duration;

    @Schema(description = "是否允许查看成绩（0-不允许，1-允许）", example = "1")
    private Integer allowViewScore;

    @NotNull(message = "题库ID不能为空")
    @Schema(description = "题库ID", required = true, example = "1")
    private String questionBankId;

    @NotNull(message = "单选题分值不能为空")
    @Min(value = 0, message = "单选题分值不能小于0")
    @Schema(description = "单选题分值", required = true, example = "2")
    private Integer singleChoiceScore;

    @NotNull(message = "多选题分值不能为空")
    @Min(value = 0, message = "多选题分值不能小于0")
    @Schema(description = "多选题分值", required = true, example = "3")
    private Integer multipleChoiceScore;

    @NotNull(message = "判断题分值不能为空")
    @Min(value = 0, message = "判断题分值不能小于0")
    @Schema(description = "判断题分值", required = true, example = "2")
    private Integer judgmentScore;

    @NotNull(message = "合格分数不能为空")
    @Min(value = 0, message = "合格分数不能小于0")
    @Max(value = 1000, message = "合格分数不能超过1000分")
    @Schema(description = "合格分数", required = true, example = "60")
    private Integer passScore;

    @Schema(description = "是否同步成绩到档案", example = "true")
    private Boolean scoreSync = false;

    @Schema(description = "参与人员列表")
    private List<ExamTaskCreateReqDTO.ParticipantInfo> participants;
}
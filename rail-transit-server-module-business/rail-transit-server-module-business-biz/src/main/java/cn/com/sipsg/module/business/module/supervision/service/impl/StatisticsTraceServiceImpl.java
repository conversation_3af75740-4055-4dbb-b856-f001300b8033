package cn.com.sipsg.module.business.module.supervision.service.impl;

import cn.com.sipsg.common.exception.BusinessException;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.security.core.util.SecurityUtils;
import cn.com.sipsg.module.business.module.common.util.UserOrgUtils;
import cn.com.sipsg.module.business.module.duty.entity.BasePoliceOrg;
import cn.com.sipsg.module.business.module.duty.entity.BaseStation;
import cn.com.sipsg.module.business.module.duty.mapper.BasePoliceOrgMapper;
import cn.com.sipsg.module.business.module.duty.mapper.BaseStationMapper;
import cn.com.sipsg.module.business.module.supervision.entity.StatisticsTrace;
import cn.com.sipsg.module.business.module.supervision.mapper.StatisticsTraceMapper;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.TracePageReqDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.StatisticsTracePageVO;
import cn.com.sipsg.module.business.module.supervision.service.StatisticsTraceService;
import cn.com.sipsg.module.business.module.supervision.util.StatisticsTraceQueryUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class StatisticsTraceServiceImpl implements StatisticsTraceService {
    private final StatisticsTraceMapper statisticsTraceMapper;
    private final BasePoliceOrgMapper basePoliceOrgMapper;
    private final BaseStationMapper baseStationMapper;
    private final UserOrgUtils userOrgUtils;

    /**
     * 分页查询当前用户的相关统计追踪信息
     * -
     *
     * @return 返回包含统计追踪信息的CommonPageVO对象
     */
    @Override
    public CommonPageVO<StatisticsTracePageVO> pageTraceForCurrentUser(TracePageReqDTO dto) {
        // 获取查询条件中的开始时间
        java.time.LocalDateTime startTime = dto.getStartTime();
        // 获取查询条件中的结束时间
        java.time.LocalDateTime endTime = dto.getEndTime();
        // 获取查询条件中的警察机构ID
        String policeOrgId = dto.getPoliceOrgId();
        // 获取查询条件中的页码
        int pageNum = dto.getPageNum();
        // 获取查询条件中的每页大小
        int pageSize = dto.getPageSize();

        // 创建LambdaQueryWrapper对象用于条件查询
        LambdaQueryWrapper<StatisticsTrace> wrapper = new LambdaQueryWrapper<>();

        // 判断当前用户是否为超级管理员
        if (SecurityUtils.isSuperAdmin()) {
            // 如果开始时间不为空，则添加开始时间条件
            if (startTime != null) {
                wrapper.ge(StatisticsTrace::getFormTime, java.sql.Timestamp.valueOf(startTime));
            }
            // 如果结束时间不为空，则添加结束时间条件
            if (endTime != null) {
                wrapper.le(StatisticsTrace::getFormTime, java.sql.Timestamp.valueOf(endTime));
            }
            // 如果警察机构ID不为空，则添加警察机构ID条件
            if (policeOrgId != null) {
                wrapper.eq(StatisticsTrace::getPoliceOrgId, policeOrgId);
            }
        } else {
            // 如果当前用户不是超级管理员，则创建StatisticsTraceQueryUtils对象
            StatisticsTraceQueryUtils queryUtils = new StatisticsTraceQueryUtils(userOrgUtils, basePoliceOrgMapper);
            // 构建当前用户所在机构的查询条件
            wrapper = queryUtils.buildCurrentUserOrgQueryWrapper(policeOrgId, startTime, endTime);
        }

        // 创建Page对象用于分页查询
        Page<StatisticsTrace> page = new Page<>(pageNum, pageSize);
        // 执行分页查询
        IPage<StatisticsTrace> result = statisticsTraceMapper.selectPage(page, wrapper);

        // 构建并返回分页查询结果的VO对象
        return buildPageVO(result);
    }


    /**
     * 批量组装VO，提升性能
     */
    private CommonPageVO<StatisticsTracePageVO> buildPageVO(IPage<StatisticsTrace> result) {
        // 获取查询结果列表
        List<StatisticsTrace> records = result.getRecords();
        // 批量查单位名
        List<String> orgIds = records.stream().map(StatisticsTrace::getPoliceOrgId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<String, String> orgNameMap = orgIds.isEmpty() ? Collections.emptyMap() :
                basePoliceOrgMapper.selectByIdList(orgIds).stream().collect(Collectors.toMap(BasePoliceOrg::getId, BasePoliceOrg::getName));
        // 批量查车站名
        List<String> stationIds = records.stream()
                .flatMap(e -> java.util.stream.Stream.of(e.getInStationId(), e.getOutStationId()))
                .filter(Objects::nonNull)
                .map(Object::toString)
                .distinct().collect(Collectors.toList());
        @SuppressWarnings("deprecation")
        List<BaseStation> stations = stationIds.isEmpty() ? Collections.emptyList() : baseStationMapper.selectBatchIds(stationIds);
        Map<String, String> stationNameMap = stations.isEmpty() ? Collections.emptyMap() :
                stations.stream()
                        .collect(Collectors.toMap(BaseStation::getId, BaseStation::getName));
        // 将查询结果转换为VO列表
        List<StatisticsTracePageVO> voList = records.stream().map(e -> toPageVO(e, orgNameMap, stationNameMap)).collect(Collectors.toList());
        // 构建并返回分页VO
        return CommonPageVO.build(voList, (int) result.getCurrent(), (int) result.getSize(), result.getTotal());
    }

    /**
     * 将统计追踪对象转换为页面显示对象
     * 此方法用于将数据库查询结果或统计数据转换为页面上显示的格式
     * 它通过使用预先获取的组织名称和车站名称映射，来丰富统计追踪对象的信息
     *
     * @param e              统计追踪对象，包含原始数据
     * @param orgNameMap     组织ID与名称的映射，用于获取组织名称
     * @param stationNameMap 车站ID与名称的映射，用于获取车站名称
     * @return 返回一个填充了相关信息的统计追踪页面显示对象
     */
    private StatisticsTracePageVO toPageVO(StatisticsTrace e, java.util.Map<String, String> orgNameMap, java.util.Map<String, String> stationNameMap) {
        StatisticsTraceQueryUtils queryUtils = new StatisticsTraceQueryUtils(userOrgUtils, basePoliceOrgMapper);
        StatisticsTracePageVO vo = new StatisticsTracePageVO();
        // 设置组织名称，如果警察组织ID不为空，则获取对应的组织名称，否则为空字符串
        vo.setOrgName(e.getPoliceOrgId() != null ? orgNameMap.getOrDefault(e.getPoliceOrgId(), "") : "");
        // 设置时间，如果形式时间不为空，则格式化时间，否则为空字符串
        vo.setTime(e.getFormTime() != null ? queryUtils.formatTimestamp(e.getFormTime()) : "");
        // 设置人数
        vo.setPersonCount(e.getPersonNum());
        // 设置进站名称，如果进站ID不为空，则获取对应的车站名称，否则为空字符串
        vo.setInStation(e.getInStationId() != null ? stationNameMap.getOrDefault(e.getInStationId().toString(), "") : "");
        // 设置进站时间，如果进站时间不为空，则格式化时间，否则为空字符串
        vo.setInTime(e.getInStationTime() != null ? queryUtils.formatTimestamp(e.getInStationTime()) : "");
        // 设置出站名称，如果出站ID不为空，则获取对应的车站名称，否则为空字符串
        vo.setOutStation(e.getOutStationId() != null ? stationNameMap.getOrDefault(e.getOutStationId().toString(), "") : "");
        // 设置出站时间，如果出站时间不为空，则格式化时间，否则为空字符串
        vo.setOutTime(e.getOutStationTime() != null ? queryUtils.formatTimestamp(e.getOutStationTime()) : "");
        return vo;
    }

    /**
     * 删除跟防情况
     *
     * @param id 跟防情况ID
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteTrace(String id) {
        try {
            if (id == null) {
                throw new BusinessException("跟防情况ID不能为空");
            }

            StatisticsTrace entity = statisticsTraceMapper.selectById(id);
            if (entity == null || !Boolean.TRUE.equals(entity.getAvailable())) {
                throw new BusinessException("跟防情况记录不存在");
            }

            // 逻辑删除
            entity.setAvailable(false);
            entity.setUpdateTime(LocalDateTime.now());
            entity.setUpdateUserId(SecurityUtils.getLoginUserId());

            int result = statisticsTraceMapper.updateById(entity);
            if (result <= 0) {
                throw new BusinessException("删除跟防情况失败");
            }

            log.info("删除跟防情况成功，ID：{}", id);
            return true;

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("删除跟防情况失败，ID：{}", id, e);
            throw new BusinessException("删除跟防情况失败");
        }
    }
}
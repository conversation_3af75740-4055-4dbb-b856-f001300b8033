package cn.com.sipsg.module.business.module.supervision.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@TableName("tbl_site_place")
public class SitePlace {
    /** 主键 */
    @TableId("id")
    private Long id;
    /** 创建时间 */
    @TableField("create_time")
    private LocalDateTime createTime;
    /** 更新时间 */
    @TableField("update_time")
    private LocalDateTime updateTime;
    /** 创建人 */
    @TableField("create_user_id")
    private String createUserId;
    /** 更新人 */
    @TableField("update_user_id")
    private String updateUserId;
    /** 是否可用 */
    @TableField("available")
    private Boolean available;
    /** 场所名称 */
    @TableField("name")
    private String name;
    /** 场所电话 */
    @TableField("phone")
    private String phone;
    /** 场所分类编码 */
    @TableField("fenlei_no")
    private String fenleiNo;
    /** 场所分类中文名 */
    @TableField("fenlei_cn")
    private String fenleiCn;
    /** 行业编码 */
    @TableField("hangye_no")
    private String hangyeNo;
    /** 行业中文 */
    @TableField("hangye_cn")
    private String hangyeCn;
    /** 二级行业编码 */
    @TableField("hangye2_no")
    private String hangye2No;
    /** 二级行业中文 */
    @TableField("hangye2_cn")
    private String hangye2Cn;
    /** 性质编码 */
    @TableField("xingzhi_no")
    private String xingzhiNo;
    /** 性质中文 */
    @TableField("xingzhi_cn")
    private String xingzhiCn;
    /** 性质小类编码 */
    @TableField("xingzhi2_no")
    private String xingzhi2No;
    /** 性质小类中文 */
    @TableField("xingzhi2_cn")
    private String xingzhi2Cn;
    /** 场所标签 */
    @TableField("biaoqian")
    private String biaoqian;
    /** 营业状态编码 */
    @TableField("yingyezhuangtai_no")
    private String yingyezhuangtaiNo;
    /** 营业状态中文 */
    @TableField("yingyezhuangtai_cn")
    private String yingyezhuangtaiCn;
    /** 建筑层数 */
    @TableField("jianzhucenshu")
    private Integer jianzhucenshu;
    /** 建筑面积 */
    @TableField("jianzhumianji")
    private Double jianzhumianji;
    /** 商业区域 */
    @TableField("shangyequyu")
    private String shangyequyu;
    /** wifi */
    @TableField("wifi")
    private String wifi;
    /** 省市县 */
    @TableField("shengshixian")
    private String shengshixian;
    /** 地址 */
    @TableField("dizhi")
    private String dizhi;
    /** 经度 */
    @TableField("lat")
    private Double lat;
    /** 纬度 */
    @TableField("lng")
    private Double lng;
    /** 治安负责人 */
    @TableField("zhianfuzeren")
    private String zhianfuzeren;
    /** 治安负责人身份证 */
    @TableField("zhianshenfenzheng")
    private String zhianshenfenzheng;
    /** 治安负责人联系电话 */
    @TableField("zhianlianxidianhua")
    private String zhianlianxidianhua;
    /** 治安负责人住址 */
    @TableField("zhianzhuzhi")
    private String zhianzhuzhi;
    /** 责任区 */
    @TableField("zerenqu")
    private String zerenqu;
    /** 社区 */
    @TableField("shequ")
    private String shequ;
    /** 所属派出所ID */
    @TableField("police_org_id")
    private String policeOrgId;
    /** 所属派出所中文 */
    @TableField("police_org_name")
    private String policeOrgName;
    /** 内保登记编码 */
    @TableField("neibaodengji_no")
    private String neibaodengjiNo;
    /** 内保登记中文 */
    @TableField("neibaodengji_cn")
    private String neibaodengjiCn;
    /** 组织机构代码 */
    @TableField("gssw_zuzhijigoudaima")
    private String gsswZuzhijigoudaima;
    /** 营业执照 */
    @TableField("gssw_yingyezhizhao")
    private String gsswYingyezhizhao;
    /** 发照日期 */
    @TableField("gssw_fazhaoriqi")
    private String gsswFazhaoriqi;
    /** 纳税人种类 */
    @TableField("gssw_nashuirenzhonglei")
    private String gsswNashuirenzhonglei;
    /** 有无证照编码 */
    @TableField("gssw_youwuzhengzhao_no")
    private String gsswYouwuzhengzhaoNo;
    /** 有无证照中文 */
    @TableField("gssw_youwuzhengzhao_cn")
    private String gsswYouwuzhengzhaoCn;
    /** 注册资本 */
    @TableField("gssw_zhuceziben")
    private Double gsswZhuceziben;
    /** 有效期 */
    @TableField("gssw_youxiaoqi")
    private String gsswYouxiaoqi;
    /** 开户银行 */
    @TableField("gssw_kaihuyinhang")
    private String gsswKaihuyinhang;
    /** 基本账号 */
    @TableField("gssw_jibenzhanghao")
    private String gsswJibenzhanghao;
    /** 其他账号 */
    @TableField("gssw_qitazhanghao")
    private String gsswQitazhanghao;
    /** 税务登记号 */
    @TableField("gssw_shuiwudengjihao")
    private String gsswShuiwudengjihao;
    /** 登记时间 */
    @TableField("gssw_dengjishijian")
    private String gsswDengjishijian;
    /** 注册时间 */
    @TableField("gssw_zhuceshijian")
    private String gsswZhuceshijian;
    /** 注册地址 */
    @TableField("gssw_zhucedizhi")
    private String gsswZhucedizhi;
    /** 经营范围 */
    @TableField("gssw_jingyingfanwei")
    private String gsswJingyingfanwei;
    /** 备注 */
    @TableField("gssw_beizhu")
    private String gsswBeizhu;
    /** 管理部门 */
    @TableField("glxx_guanlibumen")
    private String glxxGuanlibumen;
    /** 法定代表人 */
    @TableField("glxx_fadingdaibiao")
    private String glxxFadingdaibiao;
    /** 法定代表身份证 */
    @TableField("glxx_fadingdaibiaoshenfenzheng")
    private String glxxFadingdaibiaoshenfenzheng;
    /** 法定代表联系电话 */
    @TableField("glxx_fadingdaibiaolianxidianhua")
    private String glxxFadingdaibiaolianxidianhua;
    /** 法定代表国家地区 */
    @TableField("glxx_fadingdaibiaoguojiadiqu")
    private String glxxFadingdaibiaoguojiadiqu;
    /** 法定代表护照 */
    @TableField("glxx_fadingdaibiaohuzhao")
    private String glxxFadingdaibiaohuzhao;
    /** 法定代表住址 */
    @TableField("glxx_fadingdaibiaozhuzhi")
    private String glxxFadingdaibiaozhuzhi;
    /** 社区关注类别 */
    @TableField("glxx_shequguanzhuleibie")
    private String glxxShequguanzhuleibie;
    /** 内保企业号单位种类 */
    @TableField("glxx_neibaoqiyehaodanweizhonglei")
    private String glxxNeibaoqiyehaodanweizhonglei;
    /** 从业人数 */
    @TableField("rrzb_congyerenshu")
    private Integer rrzbCongyerenshu;
    /** 正式人数 */
    @TableField("rrzb_zhengshirenshu")
    private Integer rrzbZhengshirenshu;
    /** 离退休人数 */
    @TableField("rrzb_lizhituixiurenshu")
    private Integer rrzbLizhituixiurenshu;
    /** 临时工人数 */
    @TableField("rrzb_linshirenshu")
    private Integer rrzbLinshirenshu;
    /** 下岗人数 */
    @TableField("rrzb_xiagangrenshu")
    private Integer rrzbXiagangrenshu;
    /** 外籍人数 */
    @TableField("rrzb_waijirenshu")
    private Integer rrzbWaijirenshu;
    /** 新疆人数 */
    @TableField("rrzb_xinjiangrenshu")
    private Integer rrzbXinjiangrenshu;
    /** 港澳台人数 */
    @TableField("rrzb_gangaotairenshu")
    private Integer rrzbGangaotairenshu;
    /** 警棍数 */
    @TableField("rrzb_jingunshu")
    private Integer rrzbJingunshu;
    /** 电台数 */
    @TableField("rrzb_diantaishu")
    private Integer rrzbDiantaishu;
    /** 汽车数 */
    @TableField("rrzb_qicheshu")
    private Integer rrzbQicheshu;
    /** 防护设备数 */
    @TableField("rrzb_fanghushebeishu")
    private String rrzbFanghushebeishu;
    /** 夜间值班 */
    @TableField("rrzb_yejianzhiban")
    private String rrzbYejianzhiban;
    /** 签订治安责任书时间 */
    @TableField("rwjf_qiandingzhianzerenshushijian")
    private String rwjfQiandingzhianzerenshushijian;
    /** 有无技防 */
    @TableField("rwjf_youwujifang_no")
    private String rwjfYouwujifangNo;
    /** 有无技防 */
    @TableField("rwjf_youwujifang_cn")
    private String rwjfYouwujifangCn;
    /** 有无警务室 */
    @TableField("rwjf_youwujingwushi_no")
    private String rwjfYouwujingwushiNo;
    /** 有无警务室 */
    @TableField("rwjf_youwujingwushi_cn")
    private String rwjfYouwujingwushiCn;
    /** 专职民警数 */
    @TableField("rwjf_zhuanzhiminjingshu")
    private Integer rwjfZhuanzhiminjingshu;
    /** 兼职民警数 */
    @TableField("rwjf_jianzhiminjingshu")
    private Integer rwjfJianzhiminjingshu;
    /** 专职辅助人数 */
    @TableField("rwjf_zhuanzhifuzhushu")
    private Integer rwjfZhuanzhifuzhushu;
    /** 专职保卫人数 */
    @TableField("rwjf_zhuanzhibaoweishu")
    private Integer rwjfZhuanzhibaoweishu;
    /** 兼职保卫人数 */
    @TableField("rwjf_jianzhibaoweishu")
    private Integer rwjfJianzhibaoweishu;
    /** 保卫力量数 */
    @TableField("rwjf_baoweililiangshu")
    private Integer rwjfBaoweililiangshu;
    /** 保安数 */
    @TableField("rwjf_baoanshu")
    private Integer rwjfBaoanshu;
    /** 护卫犬数 */
    @TableField("rwjf_huweiquanshu")
    private Integer rwjfHuweiquanshu;
    /** 50岁保安人数 */
    @TableField("rwjf_sui50baoanshu")
    private Integer rwjfSui50baoanshu;
    /** 公安网联网人脸抓拍摄像头 */
    @TableField("rwjf_gonganwangrenlianshexiangtou")
    private String rwjfGonganwangrenlianshexiangtou;
    /** 摄像头数 */
    @TableField("rwjf_shexiangtoushu")
    private Integer rwjfShexiangtoushu;
    /** 内保从业人员数 */
    @TableField("rwjf_neibaocongyerenyuan")
    private String rwjfNeibaocongyerenyuan;
    /** 是否反恐重要目标 */
    @TableField("rwjf_shifoufankongzhongyaomubiao")
    private String rwjfShifoufankongzhongyaomubiao;
    /** 单位性质 */
    @TableField("swdw_touzidanweixingzhi")
    private String swdwTouzidanweixingzhi;
    /** 投资国家 */
    @TableField("swdw_touziguojia")
    private String swdwTouziguojia;
    /** 审核人警号 */
    @TableField("shxx_shenherenjinghao")
    private String shxxShenherenjinghao;
    /** 审核日期 */
    @TableField("shxx_shenheriqi")
    private String shxxShenheriqi;
    /** 所属企业 */
    @TableField("suoshuqiye")
    private String suoshuqiye;
    /** 未知字段 */
    @TableField("column_102")
    private Integer column102;
    /** 线路 */
    @TableField("line")
    private String line;
    /** 站点id */
    @TableField("station_id")
    private Long stationId;
    /** 未知字段 */
    @TableField("subway_id")
    private Long subwayId;
    /** 公司主键id */
    @TableField("company_id")
    private Long companyId;
    /** 责任民警 */
    @TableField("zrmj")
    private String zrmj;
    /** 公司副本 */
    @TableField("company_replica")
    private Boolean companyReplica;
}
package cn.com.sipsg.module.business.module.supervision.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 警务风险信息视图对象
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Data
@Schema(description = "警务风险信息视图对象")
public class PoliceRiskVO {

    @Schema(description = "风险ID")
    private String id;

    @Schema(description = "编号")
    private String code;

    @Schema(description = "检查场所编号")
    private String stationCode;

    @Schema(description = "检查场所")
    private String stationName;

    @Schema(description = "检查类目，CheckCategoryEnum的code")
    private String category;

    @Schema(description = "检查类目名称，CheckCategoryEnum的label")
    private String categoryName;

    @Schema(description = "上报人编号")
    private String createUserId;

    @Schema(description = "上报人")
    private String createUserName;

    @Schema(description = "隐患类型，RiskCategoryEnum的code")
    private String type;

    @Schema(description = "隐患类型名称，RiskCategoryEnum的label")
    private String typeName;

    @Schema(description = "上报时间")
    private LocalDateTime createTime;

    @Schema(description = "整改期限")
    private LocalDateTime fixLastTime;

    @Schema(description = "整改结果")
    private String changeResult;

    @Schema(description = "整改开始时间")
    private LocalDateTime rectifyStartTime;

    @Schema(description = "整改结束时间")
    private LocalDateTime rectifyEndTime;
}

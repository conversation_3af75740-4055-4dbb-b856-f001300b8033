package cn.com.sipsg.module.business.module.supervision.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 综合统计-跟防情况
 */
@Data
@TableName("tbl_statistics_trace")
public class StatisticsTrace implements Serializable {
    /** 主键id */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /** 创建人id */
    private String createUserId;

    /** 创建时间,unix时间戳,精确到毫秒 */
    private LocalDateTime createTime;

    /** 修改人id */
    private String updateUserId;

    /** 修改时间,unix时间戳,精确到毫秒 */
    private LocalDateTime updateTime;

    /** 是否有效 */
    private Boolean available;

    /** 填报时间,unix时间戳,精确到毫秒 */
    private LocalDateTime formTime;

    /** 单位ID */
    private String policeOrgId;

    /** 人数 */
    private Integer personNum;

    /** 进站ID */
    private Long inStationId;

    /** 进站时间,unix时间戳,精确到毫秒 */
    private LocalDateTime inStationTime;

    /** 出站ID */
    private Long outStationId;

    /** 出站时间,unix时间戳,精确到毫秒 */
    private LocalDateTime outStationTime;

    /** 交接单位ID */
    private String handoverOrgId;

    /** 交接站点ID */
    private Long handoverStationId;

    /** 交接时间,unix时间戳,精确到毫秒 */
    private LocalDateTime handoverTime;

    /** 备注,最大长度256 */
    private String memos;
}
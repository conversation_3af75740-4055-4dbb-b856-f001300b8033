package cn.com.sipsg.module.business.module.duty.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 盘查列表查询条件
 */
@Data
@Schema(description = "盘查列表查询条件")
public class PatrolListQueryDTO {
    
    @Schema(description = "盘查人编号")
    private String patrollerNo;
    
    @Schema(description = "民警（盘查人姓名）")
    private String patrollerName;
    
    @Schema(description = "盘查开始时间")
    private LocalDateTime startTime;
    
    @Schema(description = "盘查结束时间")
    private LocalDateTime endTime;
    
    @Schema(description = "盘查单位ID列表")
    private List<Long> orgIds;
    
    @Schema(description = "页码", required = true)
    private Integer pageNum = 1;
    
    @Schema(description = "每页数量", required = true)
    private Integer pageSize = 10;
}

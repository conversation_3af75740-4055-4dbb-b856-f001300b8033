package cn.com.sipsg.module.business.module.duty.service.impl;


import cn.com.sipsg.common.enums.CommonStatusEnum;
import cn.com.sipsg.common.exception.BusinessException;
import cn.com.sipsg.common.mybatis.core.query.MPJLambdaWrapperX;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.security.core.util.SecurityUtils;
import cn.com.sipsg.common.web.core.util.ExcelUtils;
import cn.com.sipsg.module.business.module.common.util.BeanCopyUtils;
import cn.com.sipsg.module.business.module.common.util.IdCardUtil;
import cn.com.sipsg.module.business.module.common.util.OrgRecursiveUtils;
import cn.com.sipsg.module.business.module.common.util.StatisticsUtils;
import cn.com.sipsg.module.business.module.common.util.UserOrgUtils;
import cn.com.sipsg.module.business.module.duty.entity.BaseDict;
import cn.com.sipsg.module.business.module.duty.entity.BasePoliceEmp;
import cn.com.sipsg.module.business.module.duty.entity.BasePoliceEmpFamily;
import cn.com.sipsg.module.business.module.duty.entity.BasePoliceEmpStation;
import cn.com.sipsg.module.business.module.duty.entity.BasePoliceEmpTag;
import cn.com.sipsg.module.business.module.duty.entity.BasePoliceOrg;
import cn.com.sipsg.module.business.module.duty.entity.BaseTag;
import cn.com.sipsg.module.business.module.duty.entity.DutyEmp;
import cn.com.sipsg.module.business.module.duty.entity.PoliceEmpTalkingRecord;
import cn.com.sipsg.module.business.module.duty.enums.EmpTypeEnum;

import cn.com.sipsg.module.business.module.duty.enums.PoliceEmpStatusEnum;
import cn.com.sipsg.module.business.module.duty.enums.QueryTypeEnum;
import cn.com.sipsg.module.business.module.duty.enums.SexEnum;
import cn.com.sipsg.module.business.module.duty.enums.TalkCategoryEnum;
import cn.com.sipsg.module.business.module.duty.enums.TalkingTypeEnum;
import cn.com.sipsg.module.business.module.duty.mapper.*;
import cn.com.sipsg.module.business.module.duty.pojo.dto.*;
import cn.com.sipsg.module.business.module.duty.pojo.vo.*;
import cn.com.sipsg.module.business.module.duty.service.DutyPostureService;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> lixt
 * @date : 2025-04-24 13:33:08
 * @Description: 勤务档案服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DutyPostureServiceImpl extends ServiceImpl<BasePoliceEmpMapper, BasePoliceEmp> implements DutyPostureService {
    private final BasePoliceEmpMapper basePoliceEmpMapper;
    private final DutyEmpMapper dutyEmpMapper;
    private final BasePoliceOrgMapper basePoliceOrgMapper;
    private final BasePoliceEmpTagMapper basePoliceEmpTagMapper;
    private final BaseTagMapper baseTagMapper;
    private final BaseDictMapper baseDictMapper;
    private final BasePoliceEmpStationMapper basePoliceEmpStationMapper;
    private final BasePoliceEmpFamilyMapper basePoliceEmpFamilyMapper;
    @Resource
    private HttpServletResponse response;
    private final PoliceEmpTalkingRecordMapper policeEmpTalkingRecordMapper;
    private final UserOrgUtils userOrgUtils;
    private final OrgRecursiveUtils orgRecursiveUtils;
    private final StatisticsUtils statisticsUtils;

    /**
     * 复制警员基本信息字段
     * 使用工具类进行智能复制
     *
     * @param source 数据来源对象
     * @param target 目标对象
     */
    private void copyPoliceBaseFields(Object source, Object target) {
        BeanCopyUtils.copyPoliceBaseFields(source, target);
    }

    /**
     * 查询当天排班的警力数量，并根据人员类型分组查询民警数量和辅警数量。
     *
     * @return EmpCountVO 包含总警力数量、民警数量和辅警数量的对象
     */
    @Override
    public EmpCountVO empCount() {
        try {
            EmpCountVO vo = new EmpCountVO();
            QueryWrapper<DutyEmp> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("duty_date", LocalDate.now());

            // 添加组织权限过滤
            userOrgUtils.applyOrgDataPermission(queryWrapper, "police_org_id");

            Long count = 0L;
            try {
                count = dutyEmpMapper.selectCount(queryWrapper);
            } catch (Exception e) {
                log.error("查询当天排班的警力数量失败", e);
                throw new BusinessException("查询当天排班的警力数量失败");
            }
            vo.setCount(count);

            // 查询当天排班的警员ID列表
            List<DutyEmp> dutyEmpList = dutyEmpMapper.selectList(queryWrapper);
            Set<String> empIds = dutyEmpList.stream()
                    .map(DutyEmp::getEmpId)
                    .filter(Objects::nonNull)
                    .map(String::valueOf)
                    .collect(Collectors.toSet());
            if (!empIds.isEmpty()) {
                // 从警员信息表查询民警/辅警类型
                List<BasePoliceEmp> empList = basePoliceEmpMapper.selectList(
                        new QueryWrapper<BasePoliceEmp>().in("id", empIds)
                );
                long pcCount = empList.stream().filter(emp -> emp.getType() != null && emp.getType() == 1).count();
                long scCount = empList.stream().filter(emp -> emp.getType() != null && emp.getType() == 2).count();
                vo.setPcCount(pcCount);
                vo.setScCount(scCount);
                // 查询对应的机构信息
                Set<String> orgIds = dutyEmpList.stream()
                        .map(DutyEmp::getPoliceOrgId)
                        .filter(Objects::nonNull)
                        .map(String::valueOf)
                        .collect(Collectors.toSet());
                if (!orgIds.isEmpty()) {
                    List<BasePoliceOrg> orgList = basePoliceOrgMapper.selectList(
                            new QueryWrapper<BasePoliceOrg>().in("id", orgIds)
                    );
                    List<OrgEmpCountVO> orgEmpCountList = orgList.stream().map(org -> {
                        OrgEmpCountVO orgVo = new OrgEmpCountVO();
                        orgVo.setOrgId(org.getId());
                        orgVo.setOrgName(org.getName());
                        orgVo.setCount(0L);
                        orgVo.setPcCount(0L);
                        orgVo.setScCount(0L);
                        return orgVo;
                    }).collect(Collectors.toList());
                    vo.setOrgList(orgEmpCountList);
                } else {
                    vo.setOrgList(Collections.emptyList());
                }
            } else {
                vo.setOrgList(Collections.emptyList());
            }
            return vo;
        } catch (Exception e) {
            log.error("查询警力数量失败", e);
            throw new BusinessException("查询警力数量失败");
        }
    }

    /**
     * 查询当天值班的单位数量以及按单位类型分组的数量信息
     *
     * @return SubordinateUnitVO 包含当天值班单位数量及按类型分组的数量信息
     */
    @Override
    public SubordinateUnitVO unitCount() {
        try {
            SubordinateUnitVO vo = new SubordinateUnitVO();
            QueryWrapper<DutyEmp> queryWrapper = new QueryWrapper<>();
            queryWrapper.select("COUNT(DISTINCT police_org_id) as count");
            queryWrapper.eq("duty_date", LocalDate.now());

            // 如果不是超级管理员，添加组织权限过滤
            if (!SecurityUtils.isSuperAdmin()) {
                String currentUserOrgId = userOrgUtils.getCurrentUserOrgId();
                if (currentUserOrgId != null) {
                    // 获取本单位及下级单位ID集合
                    List<String> orgIds = orgRecursiveUtils.getOrgAndChildrenIds(currentUserOrgId);
                    if (orgIds != null && !orgIds.isEmpty()) {
                        queryWrapper.in("police_org_id", orgIds);
                    } else {
                        queryWrapper.eq("police_org_id", currentUserOrgId);
                    }
                }
            }

            Long count = 0L;
            try {
                List<Object> result = dutyEmpMapper.selectObjs(queryWrapper);
                if (result != null && !result.isEmpty()) {
                    count = Long.valueOf(result.get(0).toString());
                }
            } catch (Exception e) {
                log.error("查询当天值班的单位数量失败", e);
                throw new BusinessException("查询当天值班的单位数量失败");
            }
            vo.setCount(count);

            // 构建过滤条件，包含数据权限
            Map<String, Object> filters = new HashMap<>();
            filters.put("duty_date", LocalDate.now());

            // 用通用方法统计单位类型分组数量
            List<Map<String, Object>> list = statisticsUtils.groupCountByField(
                    dutyEmpMapper, DutyEmp.class,
                    "police_org_id", "police_org_id", true,
                    filters, "police_org_id",
                    "查询当天值班单位数量失败"
            );
            // 统计各类型单位数量（如有需要可扩展）
            // 这里只做总数统计，具体类型统计可根据业务调整
            vo.setJgdwCount((long) list.size());
            vo.setPcsCount(0L); // 如需分类型统计可补充
            return vo;
        } catch (Exception e) {
            log.error("查询下辖单位失败", e);
            throw new BusinessException("查询下辖单位失败");
        }
    }

    @Override
    public DutyStatisticsVO getDutyStatistics() {
        try {
            DutyStatisticsVO vo = new DutyStatisticsVO();

            // 获取警力数量统计
            EmpCountVO empCountVO = empCount();
            vo.setEmpCount(empCountVO.getCount());
            vo.setPcCount(empCountVO.getPcCount());
            vo.setScCount(empCountVO.getScCount());
            vo.setOrgList(empCountVO.getOrgList());

            // 获取下辖单位统计
            SubordinateUnitVO unitCountVO = unitCount();
            vo.setUnitCount(unitCountVO.getCount());
            vo.setJgdwCount(unitCountVO.getJgdwCount());
            vo.setPcsCount(unitCountVO.getPcsCount());

            return vo;
        } catch (Exception e) {
            log.error("获取勤务统计信息失败", e);
            throw new BusinessException("获取勤务统计信息失败");
        }
    }

    /**
     * 将PoliceListDTO列表转换为去重聚合后的PoliceListVO列表
     *
     * @param records 原始警员数据列表，包含基础信息和标签数据
     * @return 聚合去重后的警员视图对象列表，包含系统用户状态和聚合标签信息
     */
    private List<PoliceListVO> convertToPoliceListVO(List<PoliceListDTO> records) {
        if (records == null || records.isEmpty()) {
            return Collections.emptyList();
        }
        // 查询所有警员ID（id为字符串）
        List<String> empIds = records.stream().map(PoliceListDTO::getId).filter(Objects::nonNull).collect(Collectors.toList());
        // 查询所有标签
        List<BasePoliceEmpTag> tagList = empIds.isEmpty() ? Collections.emptyList() : basePoliceEmpTagMapper.selectList(
                new QueryWrapper<BasePoliceEmpTag>().in("emp_id", empIds)
        );
        // 构建 empId -> 标签对象列表 map
        Map<String, List<PoliceListVO.TagVO>> empTagMap = tagList.stream().collect(Collectors.groupingBy(
                BasePoliceEmpTag::getEmpId,
                Collectors.mapping(tag -> {
                    PoliceListVO.TagVO vo = new PoliceListVO.TagVO();
                    vo.setId(tag.getId() != null ? tag.getId().toString() : null); // tag.id为主键id
                    vo.setName(tag.getTagValue()); // tag_value为标签名称
                    return vo;
                }, Collectors.toList())
        ));
        return records.stream().map(dto -> {
            PoliceListVO vo = new PoliceListVO();
            vo.setId(dto.getId());
            vo.setName(dto.getName());
            vo.setType(dto.getType());
            vo.setCode(dto.getCode());
            vo.setPhone(dto.getPhone());
            vo.setDuty(dto.getDuty());
            vo.setWorkUnit(dto.getWorkUnit());
            vo.setIsSystemUser(false);
            List<PoliceListVO.TagVO> tags = empTagMap.getOrDefault(dto.getId(), Collections.emptyList());
            vo.setLabelList(tags);
            vo.setLabel(tags.stream().map(PoliceListVO.TagVO::getName).collect(Collectors.joining(",")));
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 构建警员信息查询的多表联查条件包装器
     *
     * @param query 查询条件DTO，包含关键字、类型、职务、工作单位等过滤条件
     * @return 构建好的复杂查询条件包装器
     */
    private MPJLambdaWrapperX<BasePoliceEmp> buildBasePoliceEmpQueryWrapper(PoliceListQueryDTO query) {
        MPJLambdaWrapperX<BasePoliceEmp> lambdaWrapper = new MPJLambdaWrapperX<>();
        lambdaWrapper.eq(BasePoliceEmp::getAvailable, true);
        
        // 添加数据权限控制
        if (!SecurityUtils.isSuperAdmin()) {
            // 获取当前用户所在组织ID
            String currentUserOrgId = userOrgUtils.getCurrentUserOrgId();
            if (currentUserOrgId != null) {
                // 获取当前用户组织及其子组织的ID列表
                List<String> orgIds = orgRecursiveUtils.getOrgAndChildrenIds(currentUserOrgId);
                if (orgIds != null && !orgIds.isEmpty()) {
                    lambdaWrapper.in(BasePoliceEmp::getPoliceOrgId, orgIds);
                }
            }
        }
        
        if (query != null) {
            // 关键字模糊查询
            if (query.getKeyword() != null && !query.getKeyword().isEmpty()) {
                lambdaWrapper.and(w -> w
                        .like(BasePoliceEmp::getName, query.getKeyword())
                        .or().like(BasePoliceEmp::getCode, query.getKeyword())
                        .or().like(BasePoliceEmp::getPhone, query.getKeyword())
                );
            }
            // 类型
            if (query.getType() != null) {
                lambdaWrapper.eq(BasePoliceEmp::getType, query.getType());
            }
            // 职务
            if (query.getDuty() != null && !query.getDuty().isEmpty()) {
                lambdaWrapper.eq(BasePoliceEmp::getDuties, query.getDuty());
            }
            // 工作单位
            if (query.getWorkUnit() != null && !query.getWorkUnit().isEmpty()) {
                lambdaWrapper.eq(BasePoliceEmp::getPoliceOrgId, query.getWorkUnit());
            }
        }
        return lambdaWrapper;
    }

    /**
     * 分页查询警察列表数据
     *
     * @param query 查询条件DTO对象，包含分页参数（页码、每页数量）及其他过滤条件
     * @return 分页结果包装对象，包含当前页数据列表及分页信息（总记录数、总页数等）
     */
    @Override
    public CommonPageVO<PoliceListVO> searchPoliceList(PoliceListQueryDTO query) {
        try {
            int pageNum = query.getPageNum() == null ? 1 : query.getPageNum();
            int pageSize = query.getPageSize() == null ? 10 : query.getPageSize();

            // 构建查询条件
            MPJLambdaWrapperX<BasePoliceEmp> lambdaWrapper = buildBasePoliceEmpQueryWrapper(query);

            // 创建分页对象
            Page<PoliceListDTO> page = new Page<>(pageNum, pageSize);

            // 执行查询并返回结果
            return getPoliceListVOCommonPageVO(pageNum, pageSize, page, lambdaWrapper);
        } catch (Exception e) {
            log.error("分页模糊查询警员信息列表失败，详细异常：", e);
            throw new BusinessException("分页模糊查询警员信息列表失败");
        }
    }


    /**
     * 根据查询条件获取警察列表的通用页面VO
     *
     * @param pageNum       页码，表示请求的页面编号
     * @param pageSize      页面大小，表示每页包含的记录数
     * @param page          分页对象，用于封装分页查询的相关信息
     * @param lambdaWrapper 查询条件封装对象，用于指定查询的条件
     * @return 返回一个通用页面VO对象，其中包含警察列表的信息
     */
    private CommonPageVO<PoliceListVO> getPoliceListVOCommonPageVO(long pageNum, long pageSize, Page<PoliceListDTO> page, MPJLambdaWrapperX<BasePoliceEmp> lambdaWrapper) {
        // 执行分页查询，获取警察列表DTO页面对象
        Page<PoliceListDTO> resultPage = basePoliceEmpMapper.selectJoinPage(page, PoliceListDTO.class, lambdaWrapper);
        // 提取查询结果中的记录列表
        List<PoliceListDTO> records = resultPage.getRecords();

        // 将DTO记录转换为VO对象列表
        List<PoliceListVO> voList = convertToPoliceListVO(records);

        // 构建并返回通用页面VO对象（直接构造，避免二次分页）
        return CommonPageVO.<PoliceListVO>builder()
                .records(voList)
                .current(pageNum)
                .size(pageSize)
                .total(resultPage.getTotal())
                .build();
    }


    /**
     * 导出警员信息
     *
     * @param policeList 警员信息列表
     */
    @Override
    public Boolean exportPolice(List<PoliceListVO> policeList) {
        ExcelWriter writer = null;
        try {
            if (policeList == null || policeList.isEmpty()) {
                throw new BusinessException("导出失败：警员信息列表为空");
            }

            List<Map<String, Object>> rows = policeList.stream()
                    .map(this::convertToRow)
                    .collect(Collectors.toList());

            writer = ExcelUtil.getWriter();
            writer.write(rows, true);
            ExcelUtils.export(response, writer, "警员信息导出");
            return true;
        } catch (IllegalArgumentException e) {
            log.error("参数错误，导出警员信息失败: ", e);
            throw new BusinessException("导出失败：参数错误");
        } catch (Exception e) {
            log.error("未知异常，导出警员信息失败: ", e);
            throw new BusinessException("导出失败：" + e.getMessage());
        } finally {
            if (writer != null) {
                writer.close();
            }
        }
    }

    /**
     * 将PoliceListVO对象转换为Map形式的行数据
     * 此方法用于将给定的PoliceListVO对象的属性转换为一个有序的键值对集合，
     * 其中键是字段名称，值是字段对应的值此方法特别处理了类型字段，
     * 通过流操作将其代码转换为对应的标签，如果找不到对应代码，则默认为"未知"
     *
     * @param police PoliceListVO对象，包含警务人员的相关信息
     * @return 包含转换后行数据的Map，其中每个键值对表示一个字段及其值
     */
    private Map<String, Object> convertToRow(PoliceListVO police) {
        Map<String, Object> row = new LinkedHashMap<>();
        row.put("姓名", police.getName());
        row.put("类型", Arrays.stream(EmpTypeEnum.values())
                .filter(e -> e.getCode() == police.getType())
                .findFirst()
                .map(EmpTypeEnum::getLabel)
                .orElse("未知"));
        row.put("编号", police.getCode());
        row.put("手机", police.getPhone());
        row.put("工作单位", police.getWorkUnit());
        row.put("职务", police.getDuty());
        row.put("标签", police.getLabel());
        return row;
    }

    /**
     * 导入警察的Excel文件
     *
     * @param file 要导入的Excel文件
     * @return 包含导入结果的CommonResult对象
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<DutyPoliceImportDTO> importPolice(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new BusinessException("请选择要导入的Excel文件");
        }
        String fileName = file.getOriginalFilename();
        if (fileName == null || !fileName.toLowerCase().endsWith(".xlsx")) {
            throw new BusinessException("请上传Excel文件（.xlsx格式）");
        }

        List<DutyPoliceImportDTO> successList = new ArrayList<>();
        List<String> errorList = new ArrayList<>();

        try {
            List<Map<String, Object>> dataList = ExcelUtils.readAll(file);
            if (dataList.isEmpty()) {
                throw new BusinessException("Excel文件中没有数据");
            }

            for (int rowIndex = 0; rowIndex < dataList.size(); rowIndex++) {
                Map<String, Object> row = dataList.get(rowIndex);
                int displayRow = rowIndex + 1;

                try {
                    String name = getCellValue(row, "姓名");
                    int type = getCellIntValue(row, "类型");
                    String workUnit = getCellValue(row, "工作单位");

                    if (StrUtil.isBlank(name) || type == 0 || StrUtil.isBlank(workUnit)) {
                        errorList.add(String.format("第%d行：姓名、类型、工作单位为必填项", displayRow));
                        continue;
                    }

                    Integer policeType = type;
                    if (!isValidEmpType(policeType)) {
                        errorList.add(String.format("第%d行：人员类型'%d'无效", displayRow, type));
                        continue;
                    }

                    boolean unitExists = basePoliceOrgMapper.selectCount(
                            new MPJLambdaWrapperX<BasePoliceOrg>().eq(BasePoliceOrg::getName, workUnit)
                    ) > 0;
                    if (!unitExists) {
                        errorList.add(String.format("第%d行：工作单位'%s'不存在", displayRow, workUnit));
                        continue;
                    }
                    // 检查身份证号码格式
                    String idCard = getCellValue(row, "身份证号码");
                    if (!IdCardUtil.isValidIdCard(idCard)) {
                        errorList.add(String.format("第%d行：身份证号码格式错误", displayRow));
                        continue;
                    }

                    DutyPoliceImportDTO dto = buildImportDTO(row, name, String.valueOf(type), workUnit);
                    BasePoliceEmp emp = buildPoliceEmp(dto, policeType);
                    basePoliceEmpMapper.insert(emp);
                    savePoliceTag(dto.getLabel(), String.valueOf(emp.getId()));
                    successList.add(dto);
                } catch (IllegalArgumentException e) {
                    log.warn("第{}行数据格式错误：{}", displayRow, e.getMessage());
                    errorList.add(String.format("第%d行：数据格式错误 - %s", displayRow, e.getMessage()));
                } catch (Exception e) {
                    log.error("处理第{}行数据时发生错误", displayRow, e);
                    errorList.add(String.format("第%d行：处理数据时发生错误 - %s", displayRow, e.getMessage()));
                }
            }
        } catch (Exception e) {
            log.error("导入Excel文件时发生未知错误", e);
            throw new BusinessException("导入失败：" + e.getMessage());
        }

        if (!errorList.isEmpty()) {
            throw new BusinessException("导入部分失败：\n" + String.join("\n", errorList));
        }
        return successList;
    }

    /**
     * 获取指定键的单元格值，并去除首尾空格
     *
     * @param row 包含单元格数据的行Map
     * @param key 要获取值的键
     * @return 去除首尾空格后的单元格值，如果值为null则返回空字符串
     */
    private String getCellValue(Map<String, Object> row, String key) {
        Object value = row.get(key);
        if (value == null) {
            return "";
        }
        return StrUtil.trimToEmpty(value.toString());
    }

    /**
     * 获取指定键的单元格值，并转换为int类型
     *
     * @param row 包含单元格数据的行Map
     * @param key 要获取值的键
     * @return 转换后的int值，如果值为null或无法转换则返回0
     */
    private int getCellIntValue(Map<String, Object> row, String key) {
        Object value = row.get(key);
        if (value == null) {
            return 0;
        }
        try {
            if (value instanceof Number) {
                return ((Number) value).intValue();
            } else {
                String strValue = StrUtil.trimToEmpty(value.toString());
                return StrUtil.isBlank(strValue) ? 0 : Integer.parseInt(strValue);
            }
        } catch (NumberFormatException e) {
            log.warn("无法将单元格值转换为int类型，key={}，value={}，e={}", key, value, e.getMessage());
            return 0;
        }
    }

    /**
     * 验证人员类型是否有效
     *
     * @param type 人员类型代码
     * @return 如果类型有效返回true，否则返回false
     */
    private boolean isValidEmpType(Integer type) {
        if (type == null || type <= 0) {
            return false;
        }
        for (EmpTypeEnum empType : EmpTypeEnum.values()) {
            if (empType.getCode() == type) {
                return true;
            }
        }
        return false;
    }

    /**
     * 根据输入的Map数据和额外信息构建DutyPoliceImportDTO对象
     *
     * @param row      包含数据的Map对象
     * @param name     姓名
     * @param type     类型
     * @param workUnit 工作单位
     * @return 构建好的DutyPoliceImportDTO对象
     */
    private DutyPoliceImportDTO buildImportDTO(Map<String, Object> row, String name, String type, String workUnit) {
        DutyPoliceImportDTO dto = new DutyPoliceImportDTO();
        dto.setName(name);
        dto.setType(type);
        dto.setWorkUnit(workUnit);
        dto.setCode(getCellValue(row, "编号"));
        dto.setIdCard(getCellValue(row, "身份证号"));
        dto.setMobile(getCellValue(row, "手机"));
        dto.setPost(getCellValue(row, "职务"));
        dto.setStatus(getCellValue(row, "状态"));
        dto.setGender(getCellValue(row, "性别"));
        dto.setNation(getCellValue(row, "民族"));
        dto.setPolicePhone(getCellValue(row, "警用手机"));
        dto.setPoliceRank(getCellValue(row, "警衔"));
        dto.setRankLevel(getCellValue(row, "职级"));
        dto.setAddressDetail(getCellValue(row, "详细地址"));
        dto.setLabel(getCellValue(row, "标签"));
        dto.setEducation(getCellValue(row, "学历"));
        dto.setPoliticalStatus(getCellValue(row, "政治面貌"));
        return dto;
    }

    /**
     * 根据传入的DutyPoliceImportDTO对象和警察类型构建BasePoliceEmp对象
     *
     * @param dto        传入的DutyPoliceImportDTO对象，包含警察的相关信息
     * @param policeType 警察的类型，以Integer表示
     * @return 构建好的BasePoliceEmp对象
     */
    private BasePoliceEmp buildPoliceEmp(DutyPoliceImportDTO dto, Integer policeType) {
        BasePoliceEmp emp = new BasePoliceEmp();
        emp.setName(dto.getName());
        emp.setType(policeType);
        emp.setCode(dto.getCode());
        emp.setIdcard(dto.getIdCard());
        emp.setPhone(dto.getMobile());
        emp.setStatus(dto.getStatus());
        String sexCode = SexEnum.parse(dto.getGender());
        emp.setSex(sexCode == null ? null : Integer.valueOf(sexCode));
        LocalDate birthday = IdCardUtil.getBirthDateFromIdCard(dto.getIdCard());
        emp.setBirthday(birthday);
        emp.setNationality(dto.getNation());
        emp.setPolicePhone(dto.getPolicePhone());
        emp.setAddressDetail(dto.getAddressDetail());

        emp.setCreateTime(LocalDateTime.now());
        emp.setCreateUserId(SecurityUtils.getLoginUserId());
        emp.setPosition(dto.getPost());
        emp.setEducation(dto.getEducation());
        emp.setPoliticalStatus(dto.getPoliticalStatus());
        return emp;
    }

    /**
     * 保存警察标签
     *
     * @param label 标签内容
     * @param empId 员工ID
     */
    private void savePoliceTag(String label, String empId) {
        if (StrUtil.isNotBlank(label)) {
            // 查询标签是否存在
            BaseTag existingTag = baseTagMapper.selectOne(
                    new MPJLambdaWrapperX<BaseTag>()
                            .eq(BaseTag::getTagValue, label)
                            .eq(BaseTag::getAvailable, true)
            );

            if (existingTag != null) {
                // 标签存在，使用标签ID作为tag_key保存关系
                BasePoliceEmpTag empTag = new BasePoliceEmpTag();
                empTag.setEmpId(empId);
                empTag.setTagKey(String.valueOf(existingTag.getId()));
                empTag.setTagValue(label);
                empTag.setAvailable(true);
                basePoliceEmpTagMapper.insert(empTag);
            } else {
                log.warn("标签'{}'不存在，跳过保存警员标签关系，empId={}", label, empId);
            }
        }
    }

    /**
     * 获取警务组织列表的分页信息
     *
     * @param query 查询条件对象，包含分页信息和查询条件
     * @return 包含警务组织列表信息的分页对象
     */
    @Override
    public CommonPageVO<PoliceOrgListVO> policeOrgList(PoliceOrgListQueryDTO query) {
        try {
            // 设置默认值
            PoliceOrgListQueryDTO newQuery = query == null ? new PoliceOrgListQueryDTO() : query;
            if (newQuery.getPageNum() == null) newQuery.setPageNum(1);
            if (newQuery.getPageSize() == null) newQuery.setPageSize(10);

            Page<BasePoliceOrg> page = new Page<>(newQuery.getPageNum(), newQuery.getPageSize());
            QueryWrapper<BasePoliceOrg> wrapper = getBasePoliceOrgQueryWrapper(newQuery);

            // 数据权限控制：超级管理员看全部，普通用户仅看本部门及下级部门
            if (!SecurityUtils.isSuperAdmin()) {
                String currentUserOrgId = userOrgUtils.getCurrentUserOrgId();
                if (currentUserOrgId != null) {
                    List<String> orgIds = orgRecursiveUtils.getOrgAndChildrenIds(currentUserOrgId);
                    if (orgIds != null && !orgIds.isEmpty()) {
                        wrapper.in("id", orgIds);
                    } else {
                        // 没有权限看到任何数据
                        wrapper.in("id", Collections.singletonList("-1"));
                    }
                } else {
                    // 没有组织ID，限制为无数据
                    wrapper.in("id", Collections.singletonList("-1"));
                }
            }

            return getPoliceOrgListVOCommonPageVO(newQuery.getPageNum(), newQuery.getPageSize(), page, wrapper);
        } catch (Exception e) {
            log.error("分页查询一所一档列表失败", e);
            throw new BusinessException("分页查询一所一档列表失败");
        }
    }

    @NotNull
    private static QueryWrapper<BasePoliceOrg> getBasePoliceOrgQueryWrapper(PoliceOrgListQueryDTO newQuery) {
        QueryWrapper<BasePoliceOrg> wrapper = new QueryWrapper<>();

        // 只查询有效数据
        wrapper.eq("available", true);

        // 优先使用部门名称查询
        if (newQuery.getDeptName() != null && !newQuery.getDeptName().trim().isEmpty()) {
            wrapper.like("name", newQuery.getDeptName());
        } else if (newQuery.getKeyword() != null && !newQuery.getKeyword().trim().isEmpty()) {
            // 关键字模糊查询
            wrapper.and(w -> w.like("name", newQuery.getKeyword())
                    .or().like("alias", newQuery.getKeyword())
                    .or().like("code", newQuery.getKeyword()));
        }
        return wrapper;
    }

    /**
     * 获取包含警员组织列表的通用分页对象
     *
     * @param pageNum  当前页码
     * @param pageSize 每页记录数
     * @param page     分页参数
     * @param wrapper  查询条件包装器
     * @return 包含警员组织列表的通用分页对象
     */
    private CommonPageVO<PoliceOrgListVO> getPoliceOrgListVOCommonPageVO(int pageNum, int pageSize, Page<BasePoliceOrg> page, QueryWrapper<BasePoliceOrg> wrapper) {
        Page<BasePoliceOrg> resultPage = basePoliceOrgMapper.selectPage(page, wrapper);
        List<PoliceOrgListVO> voList = resultPage.getRecords().stream().map(org -> {
            PoliceOrgListVO vo = new PoliceOrgListVO();
            vo.setId(org.getId());
            vo.setName(org.getName());
            vo.setAlias(org.getAlias());
            vo.setCode(org.getCode());
            vo.setType(org.getType());
            vo.setPhone(org.getPhone());
            vo.setAvailable(org.getAvailable());
            vo.setStatus(Boolean.TRUE.equals(org.getAvailable()) ? CommonStatusEnum.ENABLE.getDesc() : CommonStatusEnum.DISABLE.getDesc());
            vo.setParentId(org.getParentId());
            return vo;
        }).collect(Collectors.toList());
        return CommonPageVO.build(voList, pageNum, pageSize, resultPage.getTotal());
    }

    /**
     * 导出警察部门信息到Excel文件
     *
     * @param orgList 需要导出的警察部门信息列表，包含部门基础信息字段。列表不能为空或空集合
     * @throws BusinessException 当发生以下情况时抛出：
     *                           1. 输入参数orgList为空或空集合
     *                           2. 数据转换或文件导出过程中发生参数错误
     *                           3. 其他未明确捕获的异常情况
     */
    @Override
    public boolean exportPoliceOrg(List<PoliceOrgListVO> orgList) {
        ExcelWriter writer = null;
        try {
            // 参数有效性校验：确保部门列表不为空
            if (orgList == null || orgList.isEmpty()) {
                throw new BusinessException("导出失败：部门信息列表为空");
            }

            // 转换数据为Excel可识别的结构，保持字段顺序
            List<Map<String, Object>> rows = orgList.stream().map(org -> {
                Map<String, Object> row = new LinkedHashMap<>();
                row.put("部门名称", org.getName());
                row.put("部门简称", org.getAlias());
                row.put("部门编号", org.getCode());
                row.put("部门联系方式", org.getPhone());
                row.put("父组织ID", org.getParentId());
                return row;
            }).collect(Collectors.toList());

            // 初始化Excel写入器并填充数据
            writer = ExcelUtil.getWriter();
            writer.write(rows, true);  // 第二个参数true表示包含标题行

            // 执行文件导出到HTTP响应流
            ExcelUtils.export(response, writer, "一所一档导出");
            return true;
        } catch (IllegalArgumentException e) {
            // 处理明确的参数异常情况
            log.error("参数错误，导出一所一档信息失败: ", e);
            throw new BusinessException("导出失败：参数错误");
        } catch (Exception e) {
            // 捕获其他未预期的异常
            log.error("未知异常，导出一所一档信息失败: ", e);
            throw new BusinessException("导出失败：" + e.getMessage());
        } finally {
            // 确保释放Excel写入器资源
            if (writer != null) {
                writer.close();
            }
        }
    }

    /**
     * 删除指定的警务组织（逻辑删除，将状态置为无效）
     *
     * @param id 要删除的警务组织的ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deletePoliceOrg(String id) {
        if (id == null) {
            throw new BusinessException("主键ID不能为空");
        }

        // 查询组织信息
        BasePoliceOrg org = basePoliceOrgMapper.selectById(id);
        if (org == null) {
            throw new BusinessException("操作失败，未找到对应组织");
        }

        // 将组织状态置为无效
        org.setAvailable(false);
        org.setUpdateTime(LocalDateTime.now());
        int updated = basePoliceOrgMapper.updateById(org);
        if (updated == 0) {
            throw new BusinessException("操作失败，更新组织状态异常");
        }

        // 查询并将该组织下的所有警员状态置为离职
        List<BasePoliceEmp> empList = basePoliceEmpMapper.selectList(
                new QueryWrapper<BasePoliceEmp>()
                        .eq("police_org_id", id)
                        .eq("available", true)
        );

        if (!empList.isEmpty()) {
            for (BasePoliceEmp emp : empList) {
                emp.setAvailable(false);
                emp.setStatus(PoliceEmpStatusEnum.RESIGNED.getCode());
                emp.setUpdateTime(LocalDateTime.now());
                basePoliceEmpMapper.updateById(emp);
            }
            log.info("已将组织ID={}下的{}\u540d警员置为离职状态", id, empList.size());
        }
        return true;
    }

    /**
     * 获取当前在职警察的统计信息
     * <p>     * 本方法用于统计今日在岗的警察人数，按不同类别（如PC、SC）和所属机构进行分类统计
     *
     * @return EmpCountVO对象，包含总人数、PC和SC人数以及各机构的人数统计
     */
    @Override
    public EmpCountVO getOnDutyPoliceStat() {
        try {
            // 获取今日日期
            LocalDate today = LocalDate.now();
            // 获取今日在岗的警察列表
            List<DutyEmp> dutyEmpList = getTodayOnDutyEmpList(today);
            // 如果在岗警察列表为空，则创建并返回一个空的EmpCountVO对象
            if (dutyEmpList.isEmpty()) {
                EmpCountVO empty = new EmpCountVO();
                empty.setCount(0L);
                empty.setPcCount(0L);
                empty.setScCount(0L);
                empty.setOrgList(Collections.emptyList());
                return empty;
            }
            // 从在岗警察列表中提取警察ID，并去除重复值
            Set<String> empIds = dutyEmpList.stream()
                    .map(DutyEmp::getEmpId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
            // 根据警察ID列表获取警察详细信息列表
            List<BasePoliceEmp> empList = empIds.isEmpty() ? Collections.emptyList() : getEmpListByIds(empIds);
            // 将警察详细信息列表转换为字典，便于快速查找
            Map<String, BasePoliceEmp> empMap = empList.stream()
                    .collect(Collectors.toMap(BasePoliceEmp::getId, e -> e));
            // 从在岗警察列表中提取机构ID，并去除重复值
            Set<String> orgIds = dutyEmpList.stream()
                    .map(DutyEmp::getPoliceOrgId)
                    .filter(Objects::nonNull)
                    .map(String::valueOf)
                    .collect(Collectors.toSet());
            // 根据机构ID列表获取机构详细信息列表
            List<BasePoliceOrg> orgList = orgIds.isEmpty() ? Collections.emptyList() : getOrgListByIds(orgIds);
            // 将机构详细信息列表转换为字典，便于快速查找
            Map<String, String> orgNameMap = orgList.stream().collect(Collectors.toMap(org -> String.valueOf(org.getId()), BasePoliceOrg::getName));
            // 初始化统计变量
            long total = 0L, totalPc = 0L, totalSc = 0L;
            // 用于存储各机构的警察人数统计信息
            Map<String, OrgEmpCountVO> statMap = new HashMap<>();
            // 遍历在岗警察列表，进行统计
            for (DutyEmp dutyEmp : dutyEmpList) {
                String orgId = String.valueOf(dutyEmp.getPoliceOrgId());
                if (orgId == null) continue;
                BasePoliceEmp emp = empMap.get(String.valueOf(dutyEmp.getEmpId()));
                if (emp == null) continue;
                // 初始化或获取当前机构的统计信息对象
                OrgEmpCountVO vo = statMap.computeIfAbsent(orgId, k -> {
                    OrgEmpCountVO v = new OrgEmpCountVO();
                    v.setOrgId(orgId);
                    v.setOrgName(orgNameMap.getOrDefault(orgId, ""));
                    v.setCount(0L);
                    v.setPcCount(0L);
                    v.setScCount(0L);
                    return v;
                });
                // 更新统计信息
                vo.setCount(vo.getCount() + 1);
                total++;
                // 根据警察类别更新统计信息
                if (emp.getType() != null && emp.getType() == 1) {
                    vo.setPcCount(vo.getPcCount() + 1);
                    totalPc++;
                } else if (emp.getType() != null && emp.getType() == 2) {
                    vo.setScCount(vo.getScCount() + 1);
                    totalSc++;
                }
            }
            // 创建并返回统计结果对象
            EmpCountVO result = new EmpCountVO();
            result.setCount(total);
            result.setPcCount(totalPc);
            result.setScCount(totalSc);
            result.setOrgList(new ArrayList<>(statMap.values()));
            return result;
        } catch (Exception e) {
            // 记录错误日志，并抛出自定义异常
            log.error("统计在岗警力信息失败", e);
            throw new BusinessException("统计在岗警力信息失败");
        }
    }

    /**
     * 查询今日在岗且可用的DutyEmp列表，统一异常处理
     */
    private List<DutyEmp> getTodayOnDutyEmpList(LocalDate today) {
        try {
            return dutyEmpMapper.selectList(
                    new QueryWrapper<DutyEmp>()
                            .eq("duty_date", today)
                            .eq("available", true)
            );
        } catch (Exception e) {
            log.error("查询今日在岗DutyEmp列表失败", e);
            throw new BusinessException("查询今日在岗DutyEmp列表失败");
        }
    }

    /**
     * 根据员工ID集合查询BasePoliceEmp列表，统一异常处理
     */
    private List<BasePoliceEmp> getEmpListByIds(Set<String> empIds) {
        try {
            return basePoliceEmpMapper.selectList(
                    new QueryWrapper<BasePoliceEmp>().in("id", empIds)
            );
        } catch (Exception e) {
            log.error("根据ID批量查询BasePoliceEmp失败", e);
            throw new BusinessException("根据ID批量查询BasePoliceEmp失败");
        }
    }

    /**
     * 根据机构ID集合查询BasePoliceOrg列表，统一异常处理
     */
    private List<BasePoliceOrg> getOrgListByIds(Set<String> orgIds) {
        try {
            return basePoliceOrgMapper.selectList(
                    new QueryWrapper<BasePoliceOrg>().in("id", orgIds)
            );
        } catch (Exception e) {
            log.error("根据ID批量查询BasePoliceOrg失败", e);
            throw new BusinessException("根据ID批量查询BasePoliceOrg失败");
        }
    }

    /**
     * 获取职级字典列表
     *
     * @return 职级字典列表
     */
    @Override
    public List<BaseDictVO> getRankLevelDict() {
        try {
            // 查询type是职级的所有字典项
            List<BaseDict> dictList = baseDictMapper.selectList(
                    new QueryWrapper<BaseDict>()
                            .eq("type", "SYSTEM_POLICE_EMP_RANK_LEVEL")
                            .eq("available", true)
                            .orderByAsc("sort")
            );

            // 转换为VO对象
            return dictList.stream().map(dict -> {
                BaseDictVO vo = new BaseDictVO();
                vo.setKey(dict.getKey());
                vo.setValue(dict.getValue());
                return vo;
            }).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取职级字典列表失败", e);
            throw new BusinessException("获取职级字典列表失败");
        }
    }

    /**
     * 获取警员档案详情
     *
     * @param dto 查询条件
     * @return 警员档案详情
     */
    @Override
    public PoliceProfileDTO getPoliceProfile(PoliceProfileReqDTO dto) {
        try {
            // 查询警员基本信息
            BasePoliceEmp emp = basePoliceEmpMapper.selectById(dto.getId());
            if (emp == null) {
                throw new BusinessException("警员信息不存在");
            }

            // 查询警员标签
            List<BasePoliceEmpTag> tags = basePoliceEmpTagMapper.selectList(
                    new QueryWrapper<BasePoliceEmpTag>()
                            .eq("emp_id", dto.getId())
                            .eq("available", true)
            );

            // 构建DTO对象
            PoliceProfileDTO dtoResult = new PoliceProfileDTO();
            dtoResult.setId(emp.getId());

            // 复制基本字段
            copyPoliceBaseFields(emp, dtoResult);

            // 设置特殊字段
            // 直接使用数据库中的原始值，不进行枚举转换
            if (emp.getDuties() != null) {
                dtoResult.setDuties(emp.getDuties());
            }
            if (emp.getPoliceRank() != null) {
                dtoResult.setPoliceRank(emp.getPoliceRank());
            }
            if (emp.getRankLevel() != null) {
                dtoResult.setRankLevel(emp.getRankLevel());
            }
            
            // 直接设置字符串字段，不进行枚举转换
            if (emp.getNationality() != null) {
                dtoResult.setNationality(emp.getNationality());
            }
            if (emp.getEducation() != null) {
                dtoResult.setEducation(emp.getEducation());
            }
            if (emp.getPoliticalStatus() != null) {
                dtoResult.setPoliticalStatus(emp.getPoliticalStatus());
            }

            // 查询管辖站点ID列表
            List<String> stationIds = basePoliceEmpStationMapper.selectStationIdsByEmpId(dto.getId());
            dtoResult.setStationIds(stationIds);

            // 转换标签ID列表
            if (tags != null && !tags.isEmpty()) {
                List<String> tagIdList = new ArrayList<>();
                for (BasePoliceEmpTag empTag : tags) {
                    BaseTag tag = baseTagMapper.selectOne(
                            new MPJLambdaWrapperX<BaseTag>()
                                    .eq(BaseTag::getTagKey, empTag.getTagKey())
                                    .eq(BaseTag::getAvailable, true)
                    );
                    if (tag != null) {
                        tagIdList.add(tag.getId());
                    }
                }
                dtoResult.setTagIds(tagIdList);
            }

            // 设置责任民警、分管领导、主要领导ID
            dtoResult.setResponsiblePoliceId(emp.getResponsiblePoliceId());
            dtoResult.setInChargeLeaderId(emp.getInChargeLeaderId());
            dtoResult.setMainLeaderId(emp.getMainLeaderId());

            // 查询家庭成员信息
            List<BasePoliceEmpFamily> familyList = basePoliceEmpFamilyMapper.selectList(
                    new QueryWrapper<BasePoliceEmpFamily>()
                            .eq("police_emp_id", dto.getId())
                            .eq("available", true)
            );
            if (familyList != null && !familyList.isEmpty()) {
                List<FamilyMemberDTO> familyMembers = familyList.stream().map(family -> {
                    FamilyMemberDTO memberDTO = new FamilyMemberDTO();
                    memberDTO.setId(family.getId());
                    memberDTO.setPoliceId(family.getPoliceEmpId());
                    memberDTO.setRelationship(family.getRelationship());
                    memberDTO.setName(family.getName());
                    return memberDTO;
                }).collect(Collectors.toList());
                dtoResult.setFamilyMembers(familyMembers);
            }

            return dtoResult;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取警员档案详情失败", e);
            throw new BusinessException("获取警员档案详情失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean savePoliceProfile(PoliceProfileDTO profileDTO) {
        try {
            // 校验必填字段
            if (profileDTO.getName() == null || profileDTO.getName().trim().isEmpty()) {
                throw new BusinessException("姓名不能为空");
            }
            if (profileDTO.getType() == null) {
                throw new BusinessException("人员类型不能为空");
            }
            if (profileDTO.getPoliceOrgId() == null) {
                throw new BusinessException("所属组织不能为空");
            }

            BasePoliceEmp emp;
            boolean isNew = false;

            // 判断是新增还是更新
            if (StringUtils.isNotBlank(profileDTO.getId())) {
                // 更新操作
                emp = basePoliceEmpMapper.selectById(profileDTO.getId());
                if (emp == null) {
                    throw new BusinessException("警员信息不存在");
                }
            } else {
                // 新增操作
                // 校验警号是否已存在
                if (StrUtil.isNotBlank(profileDTO.getCode())) {
                    BasePoliceEmp existingEmp = basePoliceEmpMapper.selectOne(
                            new LambdaQueryWrapper<BasePoliceEmp>()
                                    .eq(BasePoliceEmp::getCode, profileDTO.getCode())
                                    .eq(BasePoliceEmp::getAvailable, true)
                    );
                    if (existingEmp != null) {
                        throw new BusinessException("警号[" + profileDTO.getCode() + "]已存在，不允许重复新增");
                    }
                }
                
                emp = new BasePoliceEmp();
                isNew = true;
                emp.setAvailable(true);
                emp.setDisplay(true);
                emp.setCreateTime(LocalDateTime.now());
                emp.setCreateUserId(userOrgUtils.getCurrentUserPoliceEmp().getId());
            }

            // 设置基本信息
            copyPoliceBaseFields(profileDTO, emp);
            
            // 根据身份证号解析出生日期
            if (StrUtil.isNotBlank(profileDTO.getIdcard())) {
                // 先检查是否为有效的身份证号
                if (IdCardUtil.isValidIdCard(profileDTO.getIdcard())) {
                    LocalDate birthday = IdCardUtil.getBirthDateFromIdCard(profileDTO.getIdcard());
                    emp.setBirthday(birthday);
                } else {
                    log.warn("身份证号格式无效，跳过生日解析: {}", profileDTO.getIdcard());
                }
            }

            // 设置特殊字段
            emp.setResponsiblePoliceId(profileDTO.getResponsiblePoliceId()); // 责任民警ID
            emp.setInChargeLeaderId(profileDTO.getInChargeLeaderId()); // 分管领导ID
            emp.setMainLeaderId(profileDTO.getMainLeaderId()); // 主要领导ID // 主要领导警号
            emp.setUpdateTime(LocalDateTime.now());
            emp.setUpdateUserId("system"); // 实际应用中应该从当前登录用户获取

            // 保存警员信息
            if (isNew) {
                basePoliceEmpMapper.insert(emp);
            } else {
                basePoliceEmpMapper.updateById(emp);
                // 删除旧的家庭成员信息
                if (CollectionUtil.isNotEmpty(profileDTO.getFamilyMembers())) {
                    basePoliceEmpFamilyMapper.deleteByPoliceEmpId(emp.getId());
                } 
            }

            // 保存家庭成员信息
            if (CollectionUtil.isNotEmpty(profileDTO.getFamilyMembers())) {
                List<BasePoliceEmpFamily> familyList = profileDTO.getFamilyMembers().stream().map(familyDTO -> {
                    BasePoliceEmpFamily family = new BasePoliceEmpFamily();
                    // 不复制ID字段，避免主键冲突
                    family.setName(familyDTO.getName());
                    family.setRelationship(familyDTO.getRelationship());
                    family.setPoliceEmpId(emp.getId());
                    family.setAvailable(true);
                    family.setCreateTime(LocalDateTime.now());
                    family.setCreateUserId(SecurityUtils.getLoginUserId());
                    return family;
                }).collect(Collectors.toList());
                basePoliceEmpFamilyMapper.insertBatch(familyList);
            }

            // 处理标签关联
            if (profileDTO.getTagIds() != null && !profileDTO.getTagIds().isEmpty()) {
                // 先删除原有标签关联
                basePoliceEmpTagMapper.delete(
                        new QueryWrapper<BasePoliceEmpTag>().eq("emp_id", emp.getId())
                );

                // 添加新的标签关联
                for (String tagId : profileDTO.getTagIds()) {
                    // 查询标签信息
                    BaseTag tag = baseTagMapper.selectById(tagId);
                    if (tag != null && tag.getAvailable()) {
                        BasePoliceEmpTag empTag = new BasePoliceEmpTag();
                        empTag.setEmpId(emp.getId());
                        // 如果标签的tagKey为空，使用标签ID作为tagKey
                        empTag.setTagKey(StrUtil.isNotBlank(tag.getTagKey()) ? tag.getTagKey() : tag.getId());
                        empTag.setTagValue(tag.getTagValue());
                        empTag.setCreateTime(LocalDateTime.now());
                        empTag.setCreateUserId(userOrgUtils.getCurrentUserPoliceEmp().getId());
                        empTag.setUpdateTime(LocalDateTime.now());
                        empTag.setUpdateUserId(userOrgUtils.getCurrentUserPoliceEmp().getId()); 
                        empTag.setAvailable(true);
                        basePoliceEmpTagMapper.insert(empTag);
                    }
                }
            }

            // 处理管辖站点关联
            if (profileDTO.getStationIds() != null) {
                // 先删除原有站点关联
                basePoliceEmpStationMapper.deleteByEmpId(emp.getId());

                // 添加新的站点关联
                if (!profileDTO.getStationIds().isEmpty()) {
                    List<BasePoliceEmpStation> stationRelations = new ArrayList<>();
                    for (String stationId : profileDTO.getStationIds()) {
                        BasePoliceEmpStation empStation = new BasePoliceEmpStation();
                        empStation.setId(IdUtil.getSnowflake().nextIdStr()); // 手动设置ID以避免批量插入时的空值问题
                        empStation.setEmpId(emp.getId());
                        empStation.setStationId(stationId);
                        empStation.setAvailable(true);
                        empStation.setCreateTime(LocalDateTime.now());
                        empStation.setCreateUserId("system"); // 实际应用中应该从当前登录用户获取
                        empStation.setUpdateTime(LocalDateTime.now());
                        empStation.setUpdateUserId("system"); // 实际应用中应该从当前登录用户获取
                        stationRelations.add(empStation);
                    }
                    if (!stationRelations.isEmpty()) {
                        basePoliceEmpStationMapper.batchInsert(stationRelations);
                    }
                }
            }

            return true;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("保存警员档案信息失败", e);
            throw new BusinessException("保存警员档案信息失败");
        }
    }

    /**
     * 新建警务组织（部门）
     *
     * @param createDTO 警务组织创建数据传输对象，包含部门的基本信息
     * @return 新创建的警务组织ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createPoliceOrg(PoliceOrgCreateDTO createDTO) {
        // 参数校验
        if (createDTO == null) {
            throw new BusinessException("部门信息不能为空");
        }

        if (StrUtil.isNotBlank(createDTO.getId())) {
            // 编辑模式
            BasePoliceOrg existingOrg = basePoliceOrgMapper.selectById(createDTO.getId());
            if (existingOrg == null) {
                throw new BusinessException("要编辑的部门不存在");
            }

            // 检查编号是否重复（排除当前部门）
            LambdaQueryWrapper<BasePoliceOrg> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BasePoliceOrg::getCode, createDTO.getCode())
                    .ne(BasePoliceOrg::getId, createDTO.getId());
            if (basePoliceOrgMapper.selectCount(queryWrapper) > 0) {
                throw new BusinessException("部门编号已存在，请更换编号");
            }

            // 更新部门实体
            BasePoliceOrg policeOrg = new BasePoliceOrg();
            policeOrg.setId(createDTO.getId());
            policeOrg.setName(createDTO.getName());
            policeOrg.setCode(createDTO.getCode());
            policeOrg.setType(createDTO.getType());
            policeOrg.setParentId(createDTO.getParentId());
            policeOrg.setPhone(createDTO.getPhone());
            policeOrg.setAlias(createDTO.getAlias());

            // 设置更新时间
            policeOrg.setUpdateTime(LocalDateTime.now());
            policeOrg.setUpdateUserId(userOrgUtils.getCurrentUserPoliceEmp().getId());

            // 保存部门信息
            basePoliceOrgMapper.updateById(policeOrg);

            return policeOrg.getId();
        } else {
            // 新建模式
            // 检查部门编号是否已存在
            LambdaQueryWrapper<BasePoliceOrg> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BasePoliceOrg::getCode, createDTO.getCode());
            if (basePoliceOrgMapper.selectCount(queryWrapper) > 0) {
                throw new BusinessException("部门编号已存在，请更换编号");
            }

            // 创建部门实体
            BasePoliceOrg policeOrg = new BasePoliceOrg();
            policeOrg.setCode(createDTO.getCode());
            policeOrg.setName(createDTO.getName());
            policeOrg.setAlias(createDTO.getAlias());
            policeOrg.setType(createDTO.getType());
            policeOrg.setParentId(createDTO.getParentId());
            policeOrg.setPhone(createDTO.getPhone());

            String empId = userOrgUtils.getCurrentUserPoliceEmp().getId();
            if (StringUtils.isBlank(empId)){
                throw new BusinessException("当前用户未关联警员信息");
            }

            // 设置默认值
            policeOrg.setCreateTime(LocalDateTime.now());
            policeOrg.setCreateUserId(empId);
            policeOrg.setUpdateTime(LocalDateTime.now());
            policeOrg.setUpdateUserId(empId);
            policeOrg.setAvailable(true);
            policeOrg.setEnable(true);
            policeOrg.setInside(true);

            // 保存部门信息
            basePoliceOrgMapper.insert(policeOrg);

            return policeOrg.getId();
        }
    }

    /**
     * 根据查询条件统计组织的通话类型数据
     * 此方法根据查询类型（季度或月份）来调用实际的统计方法
     * 支持数据权限功能：管理员可查看全部数据，普通用户只能查看当前单位及下级单位的数据
     *
     * @param queryDTO 查询条件对象，包含年份和查询类型
     * @return 返回通话类型统计结果列表
     */
    @Override
    public List<OrgTalkingTypeStatVO> statOrgTalkingType(TalkingTypeStatQueryDTO queryDTO) {
        // 提取年份作为查询条件，如果为空则使用当年
        Integer year = queryDTO.getYear();
        if (year == null) {
            year = Calendar.getInstance().get(Calendar.YEAR);
        }

        // 初始化季度和月份为null，根据查询类型进行赋值
        Integer quarter = null;
        Integer month = null;

        // 根据查询类型自动确定当前季度或月份
        if (QueryTypeEnum.QUARTER.name().equals(queryDTO.getQueryType())) {
            // 获取当前季度 (1-4)
            Calendar cal = Calendar.getInstance();
            quarter = (cal.get(Calendar.MONTH) / 3) + 1;
        } else if (QueryTypeEnum.MONTH.name().equals(queryDTO.getQueryType())) {
            // 获取当前月份 (1-12)
            Calendar cal = Calendar.getInstance();
            month = cal.get(Calendar.MONTH) + 1; // Calendar.MONTH 从 0 开始
        }

        // 调用实际的统计方法，传入年份、季度和月份作为参数
        return statOrgTalkingType(year, quarter, month);
    }

    /**
     * 统计组织谈话类型
     * 根据给定的年份、季度、月份统计每个组织的谈话记录数量
     * 支持数据权限功能：管理员可查看全部数据，普通用户只能查看当前单位及下级单位的数据
     *
     * @param year    年份，可选参数
     * @param quarter 季度，可选参数，范围1-4
     * @param month   月份，可选参数，范围1-12
     * @return 返回每个组织的谈话类型统计列表
     */
    @Override
    public List<OrgTalkingTypeStatVO> statOrgTalkingType(Integer year, Integer quarter, Integer month) {
        // 构建查询条件
        QueryWrapper<PoliceEmpTalkingRecord> queryWrapper = getPoliceEmpTalkingRecordQueryWrapper(year, quarter, month);

        // 添加数据权限控制：管理员看全部的，普通用户看当前单位及下级单位的
        userOrgUtils.applyOrgDataPermission(queryWrapper, "police_org_id");

        // 查询谈话记录
        List<PoliceEmpTalkingRecord> records = policeEmpTalkingRecordMapper.selectList(queryWrapper);

        // 按组织分组
        Map<String, OrgTalkingTypeStatVO> orgMap = new HashMap<>();

        // 获取所有组织ID
        Set<String> orgIds = records.stream()
                .map(PoliceEmpTalkingRecord::getPoliceOrgId)
                .filter(Objects::nonNull)
                .map(String::valueOf)
                .collect(Collectors.toSet());

        // 查询组织信息
        final Map<String, String> orgNameMap = userOrgUtils.getOrgNameMapByIds(orgIds);

        // 遍历谈话记录，统计每个组织的谈话类型数量
        for (PoliceEmpTalkingRecord record : records) {
            String orgId = String.valueOf(record.getPoliceOrgId());
            OrgTalkingTypeStatVO vo = orgMap.computeIfAbsent(orgId, k -> {
                OrgTalkingTypeStatVO v = new OrgTalkingTypeStatVO();
                v.setOrgId(orgId);
                v.setOrgName(orgNameMap.getOrDefault(orgId, ""));
                Map<String, Integer> map = new HashMap<>();
                for (TalkingTypeEnum type : TalkingTypeEnum.values()) {
                    map.put(type.name(), 0);
                }
                v.setTalkingTypeCount(map);
                return v;
            });
            String typeKey = record.getTalkingType();
            if (typeKey != null && vo.getTalkingTypeCount().containsKey(typeKey)) {
                vo.getTalkingTypeCount().put(typeKey, vo.getTalkingTypeCount().get(typeKey) + 1);
            }
        }
        return new ArrayList<>(orgMap.values());
    }

    @NotNull
    private static QueryWrapper<PoliceEmpTalkingRecord> getPoliceEmpTalkingRecordQueryWrapper(Integer year, Integer quarter, Integer month) {
        QueryWrapper<PoliceEmpTalkingRecord> queryWrapper = new QueryWrapper<>();

        // 根据年份、季度、月份过滤数据
        if (year != null || quarter != null || month != null) {
            queryWrapper.apply("1=1"); // 确保有一个初始条件

            // 如果提供了年份
            if (year != null) {
                queryWrapper.apply("EXTRACT(YEAR FROM talking_time) = {0}", year);
            }

            // 如果提供了季度
            if (quarter != null && quarter >= 1 && quarter <= 4) {
                int startMonth = (quarter - 1) * 3 + 1;
                int endMonth = quarter * 3;
                if (year != null) {
                    queryWrapper.apply("(EXTRACT(MONTH FROM talking_time) BETWEEN {0} AND {1})", startMonth, endMonth);
                } else {
                    queryWrapper.apply("(EXTRACT(MONTH FROM talking_time) BETWEEN {0} AND {1})", startMonth, endMonth);
                }
            }

            // 如果提供了月份
            if (month != null && month >= 1 && month <= 12) {
                queryWrapper.apply("EXTRACT(MONTH FROM talking_time) = {0}", month);
            }
        }
        return queryWrapper;
    }

    /**
     * 队伍风险隐患统计
     * 支持按年份查询，支持按年、季度、月统计，支持数据权限功能
     *
     * @param queryDTO 查询条件
     * @return 统计结果，按单位、谈话类别分组统计，分页返回
     */
    @Override
    public List<TeamRiskStatVO> getTeamRiskStat(TeamRiskStatQueryDTO queryDTO) {
        try {
            // 构建基础查询条件
            QueryWrapper<PoliceEmpTalkingRecord> baseWrapper = new QueryWrapper<>();

            // 添加组织权限过滤
            userOrgUtils.applyOrgDataPermission(baseWrapper, "police_org_id");

            // 年份过滤
            if (queryDTO.getYear() != null) {
                baseWrapper.apply("EXTRACT(YEAR FROM talking_time) = {0}", queryDTO.getYear());
            }

            // 根据统计类型添加时间过滤条件
            if ("quarter".equals(queryDTO.getStatType())) {
                // 按季度统计 - 获取当前季度
                Calendar cal = Calendar.getInstance();
                int currentQuarter = (cal.get(Calendar.MONTH) / 3) + 1;
                int startMonth = (currentQuarter - 1) * 3 + 1;
                int endMonth = currentQuarter * 3;
                baseWrapper.apply("(EXTRACT(MONTH FROM talking_time) BETWEEN {0} AND {1})", startMonth, endMonth);
            } else if ("month".equals(queryDTO.getStatType())) {
                // 按月统计 - 获取当前月份
                Calendar cal = Calendar.getInstance();
                int currentMonth = cal.get(Calendar.MONTH) + 1;
                baseWrapper.apply("EXTRACT(MONTH FROM talking_time) = {0}", currentMonth);
            }
            // "year"类型不需要额外的月份过滤

            // 使用数据库分组查询获取统计结果（无分页，查全量分组）
            QueryWrapper<PoliceEmpTalkingRecord> statWrapper = baseWrapper.clone();
            statWrapper.select("police_org_id", "talking_type", "COUNT(*) as count")
                    .groupBy("police_org_id", "talking_type");
            List<Map<String, Object>> statResults = policeEmpTalkingRecordMapper.selectMaps(statWrapper);

            // 获取所有涉及的组织ID
            Set<String> orgIds = statResults.stream()
                    .map(result -> String.valueOf(result.get("police_org_id")))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            // 查询组织信息
            final Map<String, String> orgNameMap = userOrgUtils.getOrgNameMapByIds(orgIds);

            // 按组织分组统计
            Map<String, TeamRiskStatVO> orgStatMap = new HashMap<>();

            // 遍历统计结果，按组织和谈话类别统计
            for (Map<String, Object> result : statResults) {
                String orgId = String.valueOf(result.get("police_org_id"));
                String talkCategory = (String) result.get("talking_type");
                Integer count = ((Number) result.get("count")).intValue();

                // 获取或创建组织统计对象
                TeamRiskStatVO orgStat = orgStatMap.computeIfAbsent(orgId, k -> {
                    TeamRiskStatVO vo = new TeamRiskStatVO();
                    vo.setOrgName(orgNameMap.getOrDefault(orgId, ""));
                    vo.setTalkCategoryStats(new HashMap<>());
                    return vo;
                });

                // 统计各谈话类别数量
                if (talkCategory != null) {
                    // 将谈话类别编码转换为中文描述
                    String categoryLabel = TalkCategoryEnum.getLabelByCode(talkCategory);
                    if (categoryLabel != null) {
                        orgStat.getTalkCategoryStats().put(categoryLabel, count);
                    }
                }
            }

            // 确保所有谈话类别都有统计数据（没有记录的类别显示为0）
            for (TeamRiskStatVO vo : orgStatMap.values()) {
                for (TalkCategoryEnum category : TalkCategoryEnum.values()) {
                    vo.getTalkCategoryStats().putIfAbsent(category.getLabel(), 0);
                }
            }

            // 将结果转换为列表并排序（确保分页结果的一致性）
            List<TeamRiskStatVO> resultList = new ArrayList<>(orgStatMap.values());
            resultList.sort(Comparator.comparing(TeamRiskStatVO::getOrgName, Comparator.nullsLast(String::compareTo)));

            // 返回分页结果（使用数据库级分页）
            return resultList;
        } catch (Exception e) {
            log.error("队伍风险隐患统计失败", e);
            throw new BusinessException("队伍风险隐患统计失败");
        }
    }
    
    /**
     * 查询所有警员基本信息
     * 
     * @return 警员基本信息列表（警员id、警员code、警员名称）
     */
    @Override
    public List<PoliceBasicInfoVO> getAllPoliceBasicInfo() {
        try {
            // 查询所有可用的警员基本信息
            QueryWrapper<BasePoliceEmp> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("available", true)
                       .select("id", "code", "name")
                       .orderBy(true, true, "code");
            
            // 应用数据权限过滤
            userOrgUtils.applyOrgDataPermission(queryWrapper, "police_org_id");
            
            List<BasePoliceEmp> empList = basePoliceEmpMapper.selectList(queryWrapper);
            
            // 转换为VO对象
            return empList.stream().map(emp -> {
                PoliceBasicInfoVO vo = new PoliceBasicInfoVO();
                vo.setId(emp.getId());
                vo.setCode(emp.getCode());
                vo.setName(emp.getName());
                return vo;
            }).collect(Collectors.toList());
            
        } catch (Exception e) {
            log.error("查询所有警员基本信息失败", e);
            throw new BusinessException("查询警员信息失败：" + e.getMessage());
        }
    }

    @Override
    public PoliceOrgCreateDTO getPoliceOrgDetail(String id) {
        // 根据ID查询部门信息
        BasePoliceOrg policeOrg = basePoliceOrgMapper.selectById(id);
        if (policeOrg == null) {
            throw new BusinessException("部门不存在");
        }

        // 转换为DTO对象
        PoliceOrgCreateDTO dto = new PoliceOrgCreateDTO();
        dto.setId(policeOrg.getId());
        dto.setCode(policeOrg.getCode());
        dto.setName(policeOrg.getName());
        dto.setAlias(policeOrg.getAlias());
        dto.setType(policeOrg.getType());
        dto.setParentId(policeOrg.getParentId());
        dto.setPhone(policeOrg.getPhone());

        return dto;
    }
}
package cn.com.sipsg.module.business.module.supervision.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 安检点位详情VO
 */
@Data
@Schema(description = "安检点位详情VO")
public class SecurityStationDetailVO {
    @Schema(description = "安检点位ID")
    private String id;
    @Schema(description = "安检点位名称")
    private String name;
    @Schema(description = "线路ID")
    private String subwayId;
    @Schema(description = "线路名称")
    private String subwayName;
    @Schema(description = "站点ID")
    private String subwayStationId;
    @Schema(description = "站点名称")
    private String subwayStationName;
    @Schema(description = "安检公司ID")
    private String companyId;
    @Schema(description = "安检公司名称")
    private String companyName;
    @Schema(description = "管辖单位ID")
    private String precinctId;
    @Schema(description = "管辖单位名称")
    private String precinct;
    @Schema(description = "通道式X光机数量")
    private Integer xRayMachineNum;
    @Schema(description = "手持式金属探测仪数量")
    private Integer metalDetectorNum;
    @Schema(description = "危险液体探测仪数量")
    private Integer liquidDetectorNum;
    @Schema(description = "炸弹检测仪数量")
    private Integer bombDetectorNum;
    @Schema(description = "应急棍数量")
    private Integer emergencyBatonNum;
    @Schema(description = "盾牌数量")
    private Integer shieldNum;
    @Schema(description = "防爆毯数量")
    private Integer expProofBlanketNum;
    @Schema(description = "约束毯数量")
    private Integer restraintBlanketNum;
    @Schema(description = "抓捕叉数量")
    private Integer catchForkNum;
    @Schema(description = "防爆罐数量")
    private Integer expProofTankNum;
    @Schema(description = "防刺服数量")
    private Integer stabResistantClothNum;
    @Schema(description = "防爆头盔数量")
    private Integer expProofHelmetNum;
    @Schema(description = "安检门数量")
    private Integer secCheckDoorNum;
} 
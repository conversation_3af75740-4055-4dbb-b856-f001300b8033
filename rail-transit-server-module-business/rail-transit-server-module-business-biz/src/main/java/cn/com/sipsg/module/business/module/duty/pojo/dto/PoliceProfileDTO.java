package cn.com.sipsg.module.business.module.duty.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR> lixt
 * @date : 2025-05-15
 * @Description: 警员档案信息DTO
 */
@Data
@Schema(description = "警员档案信息DTO")
public class PoliceProfileDTO {
    @Schema(description = "ID，新增时为空", example = "")
    private String id;

    @Schema(description = "姓名", example = "张三")
    @NotBlank(message = "姓名不能为空")
    private String name;

    @Schema(description = "人员类型，1民警，2辅警，3安保，4警犬", example = "1")
    @NotNull(message = "人员类型不能为空")
    private Integer type;

    @Schema(description = "警号", example = "001234")
    @NotBlank(message = "警号不能为空")
    private String code;

    @Schema(description = "辅警编号", example = "FJ001234")
    private String assistPoliceCode;

    @Schema(description = "人员状态（0-在职、1-离职、2-退休、3-未知）", example = "0")
    private String status;

    @Schema(description = "身份证号", example = "110101199001011234")
    @NotBlank(message = "身份证号不能为空")
    private String idcard;

    @Schema(description = "性别，1男，2女", example = "1")
    private Integer sex;

    @Schema(description = "民族", example = "01")
    private String nationality;

    @Schema(description = "学历,1-小学,2-初中,3-高中,4-大专,5-本科,6-硕士,7-博士", example = "1", allowableValues = "1,2,3,4,5,6,7")
    private String education;

    @Schema(description = "手机号码", example = "13800138000")
    @NotBlank(message = "手机号码不能为空")
    private String phone;

    @Schema(description = "警用手机", example = "13900139000")
    private String policePhone;

    @Schema(description = "所属组织ID", example = "ORG001")
    @NotBlank(message = "所属组织ID不能为空")
    private String policeOrgId;

    @Schema(description = "行政职务,01警务技术员,02局长,03领导职务,04一级警务专员,05政治委员,06巡视员,07总队长,08副局长", example = "01", allowableValues = {"01", "02", "03", "04", "05", "06", "07", "08"})
    private String duties;

    @Schema(description = "警衔,01总警监,02副总警监,03一级警监,04二级警监,05三级警监,06一级警督,07二级警督,08三级警督,09一级警司,10二级警司,11三级警司,12一级警员,13二级警员", example = "01", allowableValues = {"01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12", "13"})
    private String policeRank;

    @Schema(description = "职级,01一级警务专员,02二级警务专员,03一级高级警长,04二级高级警长,05三级高级警长,06四级高级警长,07一级警长,08二级警长,09三级警长,10四级警长,11一级警员,12二级警员,13警务技术一级总监,14警务技术二级总监,15警务技术一级主任,16警务技术二级主任,17警务技术三级主任,18警务技术高级主任,19警务技术一级主管,20警务技术二级主管,21警务技术三级主管,22警务技术四级主管,23警务技术员,24正局级,25副局级,26正处级,27副处级,28正科级,29副科级,30科员", example = "01", allowableValues = {"01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30"})
    private String rankLevel;

    @Schema(description = "政治面貌,1-中共党员,2-中共预备党员,3-共青团员,4-群众", example = "1", allowableValues = "1,2,3,4")
    private String politicalStatus;

    @Schema(description = "责任民警ID", example = "100001L")
    private String responsiblePoliceId;

    @Schema(description = "分管领导ID", example = "100002L")
    private String inChargeLeaderId;

    @Schema(description = "主要领导ID", example = "100003L")
    private String mainLeaderId;

    @Schema(description = "住址——省份", example = "安徽省")
    private String addressProvince;

    @Schema(description = "住址——城市", example = "合肥市")
    private String addressCity;

    @Schema(description = "住址——地区", example = "蜀山区")
    private String addressArea;

    @Schema(description = "住址——街道", example = "长江西路街道")
    private String addressStreet;

    @Schema(description = "住址——详情", example = "长江西路123号")
    private String addressDetail;

    @Schema(description = "管辖站点ID列表", example = "[1, 2, 3]")
    private List<String> stationIds;

    @Schema(description = "标签ID列表", example = "[1, 2, 3]")
    private List<String> tagIds;

    @Schema(description = "图片链接", example = "https://example.com/images/police001.jpg")
    private String imgUrl;

    @Schema(description = "家庭成员列表")
    private List<FamilyMemberDTO> familyMembers;
}

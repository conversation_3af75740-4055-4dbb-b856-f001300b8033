package cn.com.sipsg.module.business.module.supervision.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 考试成绩分析视图对象
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Data
@Schema(description = "考试成绩分析视图对象")
public class ExamScoreAnalysisVO {

    @Schema(description = "月份，格式：yyyy-MM")
    private String month;

    @Schema(description = "平均分数")
    private Double averageScore;
}

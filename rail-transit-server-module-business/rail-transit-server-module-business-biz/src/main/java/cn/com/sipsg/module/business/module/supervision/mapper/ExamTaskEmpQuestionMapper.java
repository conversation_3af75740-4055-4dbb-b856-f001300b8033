package cn.com.sipsg.module.business.module.supervision.mapper;

import cn.com.sipsg.module.business.module.supervision.entity.ExamTaskEmpQuestion;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;
 
@Mapper
public interface ExamTaskEmpQuestionMapper extends BaseMapper<ExamTaskEmpQuestion> {
    /**
     * 查询某人员某任务下每题最新一次答题记录（分页）
     * @param empId 人员ID
     * @param taskId 任务ID
     * @param offset 分页起始
     * @param pageSize 分页大小
     * @return 最新答题记录列表
     */
    List<ExamTaskEmpQuestion> selectLatestRecordsByEmpAndTask(@Param("empId") String empId,
                                                             @Param("taskId") String taskId,
                                                             @Param("offset") int offset,
                                                             @Param("pageSize") int pageSize);
    /**
     * 查询总数（每题最新一次）
     */
    int countLatestRecordsByEmpAndTask(@Param("empId") String empId,
                                       @Param("taskId") String taskId);
} 
package cn.com.sipsg.module.business.module.supervision.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.*;
import java.time.LocalDateTime;

/**
 * 考试任务新增请求DTO
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Data
@Schema(description = "考试任务新增请求DTO")
public class ExamTaskCreateReqDTO {

    @Schema(description = "考试任务ID", example = "1234567890123456789")
    private String id;

    @NotBlank(message = "考试任务名称不能为空")
    @Size(max = 100, message = "考试任务名称长度不能超过100个字符")
    @Schema(description = "考试任务名称", required = true, example = "政工考核考试")
    private String taskName;

    @NotNull(message = "考试开始时间不能为空")
    @Schema(description = "考试开始时间", required = true, example = "2025-06-01 09:00:00")
    private LocalDateTime startTime;

    @NotNull(message = "考试结束时间不能为空")
    @Schema(description = "考试结束时间", required = true, example = "2025-06-30 18:00:00")
    private LocalDateTime endTime;

    @NotNull(message = "考试时长不能为空")
    @Min(value = 1, message = "考试时长必须大于0分钟")
    @Max(value = 480, message = "考试时长不能超过480分钟")
    @Schema(description = "考试时长（分钟）", required = true, example = "120")
    private Integer examDuration;

    @Schema(description = "是否同步成绩到档案", example = "true")
    private Boolean scoreSync = false;

    @NotNull(message = "考试题库ID不能为空")
    @Schema(description = "考试题库ID", required = true, example = "1")
    private String questionBankId;

    @NotNull(message = "合格分数不能为空")
    @Min(value = 0, message = "合格分数不能小于0")
    @Schema(description = "合格分数", required = true, example = "60")
    private Integer passingScore;

    @NotNull(message = "单选题分数不能为空")
    @Min(value = 0, message = "单选题分数不能小于0")
    @Schema(description = "单选题分数", required = true, example = "2")
    private Integer singleChoiceScore;

    @NotNull(message = "多选题分数不能为空")
    @Min(value = 0, message = "多选题分数不能小于0")
    @Schema(description = "多选题分数", required = true, example = "3")
    private Integer multipleChoiceScore;

    @NotNull(message = "判断题分数不能为空")
    @Min(value = 0, message = "判断题分数不能小于0")
    @Schema(description = "判断题分数", required = true, example = "1")
    private Integer trueFalseScore;

    @Size(max = 500, message = "备注长度不能超过500个字符")
    @Schema(description = "考试备注", example = "政工考核考试，请认真答题")
    private String remark;

    @NotNull(message = "参与人员不能为空")
    @NotEmpty(message = "参与人员列表不能为空")
    @Schema(description = "参与人员列表", required = true)
    private java.util.List<ParticipantInfo> participants;

    /**
     * 参与人员信息
     */
    @Data
    @Schema(description = "参与人员信息")
    public static class ParticipantInfo {
        
        @NotBlank(message = "警员编号不能为空")
        @Schema(description = "警员编号", required = true, example = "P001")
        private String policeCode;
        
        @NotBlank(message = "警员名称不能为空")
        @Schema(description = "警员名称", required = true, example = "张三")
        private String policeName;
    }
}
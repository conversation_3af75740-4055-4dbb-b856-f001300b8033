package cn.com.sipsg.module.business.module.supervision.pojo.vo;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 风险统计视图对象
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Data
public class RiskStatVO {
    
    /**
     * 总风险数量
     */
    private Long totalCount;
    
    /**
     * 按类型统计结果
     */
    private List<TypeStatItem> typeStats;
    
    /**
     * 按等级统计结果
     */
    private List<LevelStatItem> levelStats;
    
    /**
     * 按日期统计结果
     */
    private Map<String, Long> dateStats;
    
    /**
     * 类型统计项
     */
    @Data
    public static class TypeStatItem {
        /**
         * 风险类型
         */
        private String type;
        
        /**
         * 风险类型名称
         */
        private String typeName;
        
        /**
         * 数量
         */
        private Long count;
    }
    
    /**
     * 等级统计项
     */
    @Data
    public static class LevelStatItem {
        /**
         * 风险等级
         */
        private String level;
        
        /**
         * 风险等级名称
         */
        private String levelName;
        
        /**
         * 数量
         */
        private Long count;
    }
}

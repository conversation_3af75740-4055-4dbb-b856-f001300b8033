package cn.com.sipsg.module.business.module.supervision.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 抓获类型枚举
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
@Getter
@AllArgsConstructor
public enum CaptureTypeEnum {
    NATIONAL_FUGITIVE("1", "全国在逃"),
    POLICE_CONTROL("2", "公安临控"),
    OTHER("3", "其他");

    private final String code;
    private final String name;

    public static CaptureTypeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (CaptureTypeEnum status : values()) {
            if (code.equals(status.getCode())) {
                return status;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        CaptureTypeEnum status = getByCode(code);
        return status != null ? status.getName() : "未知状态";
    }
}

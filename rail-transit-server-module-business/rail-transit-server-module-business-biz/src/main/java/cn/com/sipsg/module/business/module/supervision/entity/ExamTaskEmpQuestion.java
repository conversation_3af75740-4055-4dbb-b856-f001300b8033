package cn.com.sipsg.module.business.module.supervision.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> lixt
 * @date : 2025-05-21 14:28:08
 * @Description: 考试人员答题记录表
 */
@Data
@TableName("tbl_exam_task_emp_question")
public class ExamTaskEmpQuestion {

    @TableId(type = IdType.ASSIGN_ID)
    private String id;  // 主键ID

    @TableField("create_user_id")
    private String createUserId;  // 创建人

    @TableField("create_time")
    private LocalDateTime createTime;  // 创建时间

    @TableField("update_user_id")
    private String updateUserId;  // 更新人

    @TableField("update_time")
    private LocalDateTime updateTime;  // 更新时间

    @TableField("available")
    private Boolean available;  // 有效标识

    @TableField("task_id")
    private String taskId;  // 考试任务ID

    @TableField("emp_id")
    private String empId;  // 考试人员ID

    @TableField("question_id")
    private Long questionId;  // 题目序号(对应tbl_exam_question表的serial字段)

    @TableField("answer")
    private String answer;  // 作答答案

    @TableField("answer_right")
    private Boolean answerRight;  // 回答是否正确

    @TableField("serial")
    private Integer serial;  // 题序

    @TableField("task_emp_batch_id")
    private String taskEmpBatchId;  // 考试批次ID
}
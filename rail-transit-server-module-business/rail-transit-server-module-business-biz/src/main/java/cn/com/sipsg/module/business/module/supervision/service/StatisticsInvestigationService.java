package cn.com.sipsg.module.business.module.supervision.service;

import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.InvestigationDetailReqDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.InvestigationPageReqDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.InvestigationSaveDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.InvestigationPageVO;

public interface StatisticsInvestigationService {
    /**
     * 查询当前用户所在单位及下级单位的盘查前科情况分页列表（统一方法）
     * @param  dto 查询参数
     * @return 分页VO
     */
    CommonPageVO<InvestigationPageVO> pageInvestigationForCurrentUser(InvestigationPageReqDTO dto);

    /**
     * 删除盘查前科
     *
     * @param id 盘查前科ID
     * @return 操作结果
     */
    Boolean deleteInvestigation(String id);

    /**
     * 保存或更新盘查前科情况
     *
     * @param dto 盘查前科情况保存DTO
     * @return 操作结果
     */
    Boolean saveOrUpdateInvestigation(InvestigationSaveDTO dto);

    /**
     * 获取盘查前科情况详情
     *
     * @param dto 详情查询请求参数
     * @return 盘查前科情况详情
     */
    InvestigationSaveDTO getInvestigationDetail(InvestigationDetailReqDTO dto);
}
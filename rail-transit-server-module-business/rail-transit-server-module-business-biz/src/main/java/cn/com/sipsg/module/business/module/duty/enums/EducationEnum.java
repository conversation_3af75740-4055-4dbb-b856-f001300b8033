package cn.com.sipsg.module.business.module.duty.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum EducationEnum {
    PRIMARY("1", "小学"),
    JUNIOR("2", "初中"),
    HIGH("3", "高中"),
    COLLEGE("4", "大专"),
    BACHELOR("5", "本科"),
    MASTER("6", "硕士"),
    DOCTOR("7", "博士");

    @EnumValue
    private final String code;
    
    @JsonValue
    private final String label;

    EducationEnum(String code, String label) {
        this.code = code;
        this.label = label;
    }

    public static boolean isValid(String value) {
        for (EducationEnum e : values()) {
            if (e.label.equals(value) || e.code.equals(value)) {
                return true;
            }
        }
        return false;
    }

    public static String getLabelByCode(String code) {
        for (EducationEnum e : values()) {
            if (e.code.equals(code)) {
                return e.label;
            }
        }
        return null;
    }

    public static String getCodeByLabel(String label) {
        for (EducationEnum e : values()) {
            if (e.label.equals(label)) {
                return e.code;
            }
        }
        return null;
    }

    public static EducationEnum getByCode(String code) {
        for (EducationEnum e : values()) {
            if (e.code.equals(code)) {
                return e;
            }
        }
        return null;
    }

    public static EducationEnum getByLabel(String label) {
        for (EducationEnum e : values()) {
            if (e.label.equals(label)) {
                return e;
            }
        }
        return null;
    }
}
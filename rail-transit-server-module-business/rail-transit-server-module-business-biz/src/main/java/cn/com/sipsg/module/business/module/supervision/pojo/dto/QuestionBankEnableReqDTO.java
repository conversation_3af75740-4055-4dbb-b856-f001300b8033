package cn.com.sipsg.module.business.module.supervision.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "题库启用/停用请求DTO")
public class QuestionBankEnableReqDTO {
    @Schema(description = "题库ID", required = true)
    private String questionBankId;

    @Schema(description = "是否启用（true-启用，false-停用）", required = true)
    private Boolean enable;
} 
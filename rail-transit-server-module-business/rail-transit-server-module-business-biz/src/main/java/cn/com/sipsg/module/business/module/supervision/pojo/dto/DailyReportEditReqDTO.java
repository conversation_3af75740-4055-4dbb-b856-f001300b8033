package cn.com.sipsg.module.business.module.supervision.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 每日报备编辑请求DTO
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
@Data
@Schema(description = "每日报备编辑请求DTO")
public class DailyReportEditReqDTO {

    @Schema(description = "报备ID，编辑时必传", example = "1")
    private String id;

    @Schema(description = "单位编号", example = "320697000000")
    @NotBlank(message = "单位编号不能为空")
    private String policeOrgId;

    @Schema(description = "填报时间", example = "2025-05-14 06:40:31")
    @NotNull(message = "填报时间不能为空")
    private LocalDateTime formTime;

    @Schema(description = "负责人姓名", example = "胡志全")
    @NotBlank(message = "负责人姓名不能为空")
    private String responsibleName;

    @Schema(description = "负责人岗位", example = "副所长")
    @NotBlank(message = "负责人岗位不能为空")
    private String responsiblePosition;

    @Schema(description = "负责人电话", example = "18625271460")
    @NotBlank(message = "负责人电话不能为空")
    private String responsiblePhone;

    @Schema(description = "负责人值守频道", example = "18625271460")
    @NotBlank(message = "负责人值守频道不能为空")
    private String responsibleChannel;

    @Schema(description = "警力-民警数量", example = "8")
    @NotNull(message = "警力-民警数量不能为空")
    private Integer strengthPolice;

    @Schema(description = "警力-辅警数量", example = "58")
    @NotNull(message = "警力-辅警数量不能为空")
    private Integer strengthAuxiliaryPolice;

    @Schema(description = "警力-武警数量", example = "0")
    @NotNull(message = "警力-武警数量不能为空")
    private Integer strengthArmedPolice;

    @Schema(description = "警力-其他数量", example = "0")
    @NotNull(message = "警力-其他数量不能为空")
    private Integer strengthOther;

    @Schema(description = "装备-枪支数量")
    @NotNull(message = "装备-枪支数量不能为空")
    private Integer equipmentGun;

    @Schema(description = "装备-枪支情况", example = "无")
    @NotBlank(message = "装备-枪支情况不能为空")
    private String equipmentGunMsg;

    @Schema(description = "装备-GPS/北斗", example = "无")
    @NotBlank(message = "装备-GPS/北斗不能为空")
    private String equipmentGpsMsg;

    @Schema(description = "装备-图像传输设备", example = "无")
    @NotBlank(message = "装备-图像传输设备不能为空")
    private String equipmentImgMsg;

    @Schema(description = "车辆-摩托车", example = "0")
    @NotNull(message = "车辆-摩托车不能为空")
    private Integer vehicleMotorbike;

    @Schema(description = "车辆-电动车", example = "0")
    @NotNull(message = "车辆-电动车不能为空")
    private Integer vehicleElectricCar;

    @Schema(description = "巡防投入-车辆", example = "0")
    @NotNull(message = "巡防投入-车辆不能为空")
    private Integer patrolVehicle;

    @Schema(description = "巡防投入-警务通", example = "0")
    @NotNull(message = "巡防投入-警务通不能为空")
    private Integer patrolPoliceCommunication;

    @Schema(description = "巡防投入-PDA", example = "0")
    @NotNull(message = "巡防投入-PDA不能为空")
    private Integer patrolPda;

    @Schema(description = "巡防投入-盘查车辆", example = "0")
    @NotNull(message = "巡防投入-盘查车辆不能为空")
    private Integer patrolQuestionVehicle;

    @Schema(description = "巡防投入-抓获犯罪人员", example = "0")
    @NotNull(message = "巡防投入-抓获犯罪人员不能为空")
    private Integer patrolArrestEmp;

    @Schema(description = "重点部位投入-部位数量", example = "0")
    @NotNull(message = "重点部位投入-部位数量不能为空")
    private Integer importantPart;

    @Schema(description = "重点部位投入-民警数量", example = "0")
    private Integer importantPolice;

    @Schema(description = "重点部位投入-辅警数量", example = "0")
    private Integer importantAuxiliaryPolice;

    @Schema(description = "重点部位投入-武警数量", example = "0")
    private Integer importantArmedPolice;

    @Schema(description = "重点部位投入-车辆数量", example = "0")
    private Integer importantVehicle;

    @Schema(description = "重点部位投入-武器数量", example = "0")
    private Integer importantWeapon;

    @Schema(description = "备注", example = "无")
    private String memos;
}
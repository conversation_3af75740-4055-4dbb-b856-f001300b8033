package cn.com.sipsg.module.business.module.duty.mapper;

import cn.com.sipsg.module.business.module.duty.pojo.vo.PoliceListVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface PoliceListMapper {
    List<PoliceListVO> selectPoliceList(@Param("orgIds") List<String> orgIds,
                                        @Param("keyword") String keyword,
                                        @Param("type") Integer type,
                                        @Param("duty") String duty,
                                        @Param("workUnit") String workUnit,
                                        @Param("pageSize") Integer pageSize,
                                        @Param("offset") Integer offset);

    int countPoliceList(@Param("orgIds") List<String> orgIds,
                        @Param("keyword") String keyword,
                        @Param("type") Integer type,
                        @Param("duty") String duty,
                        @Param("workUnit") String workUnit);
}
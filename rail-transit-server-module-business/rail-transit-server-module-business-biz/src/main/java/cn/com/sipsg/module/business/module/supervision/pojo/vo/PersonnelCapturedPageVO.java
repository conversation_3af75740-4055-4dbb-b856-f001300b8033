package cn.com.sipsg.module.business.module.supervision.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 抓获统计分页VO
 */
@Data
@Schema(description = "抓获统计分页VO")
public class PersonnelCapturedPageVO {
    @Schema(description = "抓获ID", example = "1")
    private String id;
    @Schema(description = "抓获对象", example = "张三")
    private String name;
    @Schema(description = "身份证号", example = "320102199001011234")
    private String idNo;
    @Schema(description = "抓获单位", example = "轨交分局")
    private String policeOrgName;
    @Schema(description = "预警来源", example = "101预警")
    private String warningSourcesText;
    @Schema(description = "抓获类型", example = "现行抓获")
    private String captureTypeText;
    @Schema(description = "抓获时间", example = "2024-01-15 14:30:00")
    private String captureDate;
    @Schema(description = "抓获地点", example = "南京南站")
    private String captureSite;
    @Schema(description = "处理结果", example = "行政拘留")
    private String processResultText;
    @Schema(description = "移交单位", example = "南京市公安局")
    private String turnOverOrg;
    @Schema(description = "后续处理", example = "移交检察院")
    private String postProcessResultText;
}
package cn.com.sipsg.module.business.module.supervision.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

@Data
@TableName("tbl_site_place_module")
public class SitePlaceModule {
    /** 记录唯一标识主键 */
    @TableId("id")
    private Long id;
    /** 记录创建时间 */
    @TableField("create_time")
    private Long createTime;
    /** 记录最后更新时间 */
    @TableField("update_time")
    private Long updateTime;
    /** 创建者用户ID */
    @TableField("create_user_id")
    private String createUserId;
    /** 最后更新者用户ID */
    @TableField("update_user_id")
    private String updateUserId;
    /** 是否有效 */
    @TableField("available")
    private Boolean available;
    /** 模块名 */
    @TableField("type_name")
    private String typeName;
    /** 场所ID */
    @TableField("place_id")
    private Long placeId;
    /** 一条记录json */
    @TableField("record")
    private String record;
    /** 模块排序 */
    @TableField("type_sort")
    private Integer typeSort;
} 
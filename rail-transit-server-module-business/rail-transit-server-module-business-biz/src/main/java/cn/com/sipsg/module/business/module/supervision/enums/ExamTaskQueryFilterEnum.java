package cn.com.sipsg.module.business.module.supervision.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 考试任务查询过滤条件枚举
 * 用于前端查询过滤，不对应数据库字段
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Getter
@AllArgsConstructor
public enum ExamTaskQueryFilterEnum {

    ALL("00", "全部"),
    NOT_STARTED("01", "未开始"),
    IN_PROGRESS("02", "进行中"),
    PASSED("03", "合格"),
    FAILED("04", "不合格"),
    EXPIRED("05", "已过期"),
    UNKNOWN("","未知");

    /**
     * 过滤条件码
     */
    private final String code;

    /**
     * 过滤条件描述
     */
    private final String desc;

    /**
     * 根据条件码获取枚举
     *
     * @param code 条件码
     * @return 枚举实例
     */
    public static ExamTaskQueryFilterEnum getByCode(String code) {
        // 如果传入的是空字符串或null，返回未知
        if (code == null || code.isEmpty()) {
            return UNKNOWN;
        }

        for (ExamTaskQueryFilterEnum filter : values()) {
            if (filter.getCode().equals(code)) {
                return filter;
            }
        }

        // 如果找不到匹配的条件码，返回未知
        return UNKNOWN;
    }

    /**
     * 根据条件描述获取枚举
     *
     * @param desc 条件描述
     * @return 枚举实例
     */
    public static ExamTaskQueryFilterEnum getByDesc(String desc) {
        // 如果传入的是空字符串或null，返回未知
        if (desc == null || desc.isEmpty()) {
            return UNKNOWN;
        }

        for (ExamTaskQueryFilterEnum filter : values()) {
            if (filter.getDesc().equals(desc)) {
                return filter;
            }
        }

        // 如果找不到匹配的条件描述，返回未知
        return UNKNOWN;
    }

    /**
     * 获取对应的ExamTaskEmpStatusEnum
     *
     * @return 对应的状态枚举
     */
    public ExamTaskEmpStatusEnum toStatusEnum() {
        switch (this) {
            case NOT_STARTED:
                return ExamTaskEmpStatusEnum.NOT_STARTED;
            case IN_PROGRESS:
                return ExamTaskEmpStatusEnum.IN_PROGRESS;
            case PASSED:
                return ExamTaskEmpStatusEnum.PASSED;
            case FAILED:
                return ExamTaskEmpStatusEnum.FAILED;
            case UNKNOWN:
                return null; // 未知状态不对应具体状态
            case ALL:
            default:
                return null; // 全部查询不对应具体状态
        }
    }
}

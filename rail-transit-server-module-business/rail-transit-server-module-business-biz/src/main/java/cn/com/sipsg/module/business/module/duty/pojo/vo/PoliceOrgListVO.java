package cn.com.sipsg.module.business.module.duty.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "组织列表VO")
public class PoliceOrgListVO {
    @Schema(description = "组织ID")
    private String id;

    @Schema(description = "组织名称")
    private String name;

    @Schema(description = "组织别名")
    private String alias;

    @Schema(description = "组织编号")
    private String code;

    @Schema(description = "组织类型")
    private Integer type;

    @Schema(description = "联系方式")
    private String phone;

    @Schema(description = "状态（如有效/无效）")
    private String status;

    @Schema(description = "有效标识")
    private Boolean available;

    @Schema(description = "父组织ID")
    private String parentId;
}
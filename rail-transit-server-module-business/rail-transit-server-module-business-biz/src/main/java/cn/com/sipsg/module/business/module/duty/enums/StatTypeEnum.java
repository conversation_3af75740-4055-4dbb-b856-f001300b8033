package cn.com.sipsg.module.business.module.duty.enums;

import lombok.Getter;

@Getter
public enum StatTypeEnum {
    DAY("day", "日"),
    WEEK("week", "周"),
    MONTH("month", "月"),
    YEAR("year", "年");

    private final String code;
    private final String label;

    StatTypeEnum(String code, String label) {
        this.code = code;
        this.label = label;
    }

    /**
     * 根据统计类型的代码返回对应的枚举类型
     * 如果提供的代码不匹配任何已定义的统计类型，则抛出IllegalArgumentException异常
     * 此方法主要用于将字符串代码转换为相应的统计类型枚举，以便在程序中更容易地处理和使用这些类型
     *
     * @param code 统计类型的代码表示，不区分大小写
     * @return 对应的统计类型枚举
     * @throws IllegalArgumentException 如果提供的代码不匹配任何已定义的统计类型
     */
    public static StatTypeEnum fromCode(String code) {
        // 遍历所有枚举值，寻找匹配的统计类型代码
        for (StatTypeEnum e : values()) {
            // 使用equalsIgnoreCase确保比较时不区分大小写
            if (e.code.equalsIgnoreCase(code)) {
                // 找到匹配的枚举值，返回之
                return e;
            }
        }
        // 如果没有找到匹配的枚举值，抛出异常
        throw new IllegalArgumentException("不支持的统计类型: " + code);
    }
} 
package cn.com.sipsg.module.business.module.duty.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR> lixt
 * @date : 2025-05-07 10:37:33
 * @Description: 盘查物品信息
 */
@Data
@TableName("std_god_pck_zapc_wpxx")
public class PckZapcWpxx {

    @TableId(type = IdType.ASSIGN_ID)
    private String pcwpgzqkbh;  // 盘查物品工作情况编号

    @TableField("bpcwprybh")
    private String bpcwprybh;  // 盘查物品人员编号

    @TableField("bpcwpajbh")
    private String bpcwpajbh;  // 盘查物品案件编号

    @TableField("bpcwplxdm")
    private String bpcwplxdm;  // 被盘查物品类型代码

    @TableField("bpcwpmc")
    private String bpcwpmc;  // 被盘查物品名称

    @TableField("bpcwptz")
    private String bpcwptz;  // 被盘查物品特征

    @TableField("bpcwpsl")
    private Integer bpcwpsl;  // 被盘查物品数量

    @TableField("bpcwpsldw")
    private String bpcwpsldw;  // 被盘查物品数量单位

    @TableField("bpcwpjz")
    private String bpcwpjz;  // 被盘查物品物品价值

    @TableField("bpcwpys")
    private String bpcwpys;  // 被盘查物品颜色

    @TableField("bpcwpcp")
    private String bpcwpcp;  // 被盘查物品厂牌

    @TableField("bpcwpxh")
    private String bpcwpxh;  // 被盘查物品型号

    @TableField("bpcwpssdw")
    private String bpcwpssdw;  // 被盘查物品所属单位

    @TableField("bpcwpssdwdm")
    private String bpcwpssdwdm;  // 被盘查物品所属单位代码

    @TableField("qtxgwp")
    private String qtxgwp;  // 其他相关物品

    @TableField("bpcwpbz")
    private String bpcwpbz;  // 被盘查物品备注

    @TableField("clxh")
    private String clxh;  // 车辆型号

    @TableField("clhpzldm")
    private String clhpzldm;  // 车辆号牌种类代码

    @TableField("bpcwpbhy")
    private String bpcwpbhy;  // 被盘查物品编号一

    @TableField("bpcwpbhe")
    private String bpcwpbhe;  // 被盘查物品编号二

    @TableField("bpcwpbhs")
    private String bpcwpbhs;  // 被盘查物品编号三

    @TableField("bpcwpbhsi")
    private String bpcwpbhsi;  // 被盘查物品编号四

    @TableField("bpcwpbhw")
    private String bpcwpbhw;  // 被盘查物品编号五

    @TableField("bpcwppcsj")
    private LocalDateTime bpcwppcsj;  // 被盘查物品盘查时间

    @TableField("bpcwppcdd")
    private String bpcwppcdd;  // 被盘查物品盘查地点

    @TableField("pcwpyy")
    private String pcwpyy;  // 盘查物品原因

    @TableField("bpcwppcdw")
    private String bpcwppcdw;  // 被盘查物品盘查单位

    @TableField("bpcwpdjsj")
    private LocalDateTime bpcwpdjsj;  // 被盘查物品登记时间

    @TableField("bpcwpdjr")
    private String bpcwpdjr;  // 被盘查物品登记人

    @TableField("bpcwpdjdw")
    private String bpcwpdjdw;  // 被盘查物品登记单位

    @TableField("bpcwpcljgdm")
    private String bpcwpcljgdm;  // 被盘查物品处理结果代码

    @TableField("bpcwpclqk")
    private String bpcwpclqk;  // 被盘查物品处理情况

    @TableField("bpcwpxgsj")
    private LocalDateTime bpcwpxgsj;  // 被盘查物品修改时间

    @TableField("bpcwpxgr")
    private String bpcwpxgr;  // 被盘查物品修改人

    @TableField("bpcwpxgdw")
    private String bpcwpxgdw;  // 物品盘查修改单位

    @TableField("bpcwplbdm")
    private String bpcwplbdm;  // 被盘查物品类别代码

    @TableField("bpcwpglbh")
    private String bpcwpglbh;  // 盘查物品关联编号

    @TableField("clmdd")
    private String clmdd;  // 被盘查车辆目的地

    @TableField("clyt")
    private String clyt;  // 车辆用途

    @TableField("clysdm")
    private String clysdm;  // 车辆颜色代码

    @TableField("pcdwmc")
    private String pcdwmc;  // 盘查单位名称

    @TableField("djrxm")
    private String djrxm;  // 登记人姓名

    @TableField("djdwmc")
    private String djdwmc;  // 登记单位名称

    @TableField("xgrxm")
    private String xgrxm;  // 修改人姓名

    @TableField("xgdwmc")
    private String xgdwmc;  // 修改单位名称

    @TableField("jdcjccfx")
    private String jdcjccfx;  // 机动车进出城方向

    @TableField("bpcwppcr")
    private String bpcwppcr;  // 被盘查物品盘查人

    @TableField("bpcwppcrxm")
    private String bpcwppcrxm;  // 被盘查物品盘查人姓名

    @TableField("clpp")
    private String clpp;  // 车辆品牌

    @TableField("clsyr")
    private String clsyr;  // 车辆所有人

    @TableField("clsyrsfzmhm")
    private String clsyrsfzmhm;  // 车辆所有人身份证明号码

    @TableField("bpcwpyjdw")
    private String bpcwpyjdw;  // 被盘查物品移交单位

    @TableField("bpcwpyjdwmc")
    private String bpcwpyjdwmc;  // 被盘查物品移交单位名称

    @TableField("bpcwpyjyy")
    private String bpcwpyjyy;  // 被盘查物品移交原因

    @TableField("shangchuansj")
    private LocalDateTime shangchuansj;  // 上传时间

    @TableField("zpsl")
    private Integer zpsl;  // 照片数量

    @TableField("gzqkbh")
    private String gzqkbh;  // 工作情况编号

    @TableField("start_dt")
    private LocalDate startDt;  // 开始日期

    @TableField("end_dt")
    private LocalDate endDt;  // 结束日期

    @TableField("del_flag")
    private String delFlag;  // 删除标志

    @TableField("etl_job")
    private String etlJob;  // ETL任务名
}

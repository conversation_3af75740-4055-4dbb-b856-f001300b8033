package cn.com.sipsg.module.business.module.supervision.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 考试状态枚举
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Getter
@AllArgsConstructor
public enum ExamStatusEnum {
    
    IN_PROGRESS("01", "进行中"),
    SCORE_CONFIRMED("02", "确认考试成绩"),
    COMPLETED("03", "已完成"),
    UNKNOWN("", "未知");
    
    /**
     * 状态码
     */
    private final String code;
    
    /**
     * 状态描述
     */
    private final String desc;
    
    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return 枚举实例
     */
    public static ExamStatusEnum getByCode(String code) {
        if (code == null || code.isEmpty()) {
            return UNKNOWN;
        }
        
        for (ExamStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        
        return UNKNOWN;
    }
    
    /**
     * 根据状态描述获取枚举
     *
     * @param desc 状态描述
     * @return 枚举实例
     */
    public static ExamStatusEnum getByDesc(String desc) {
        if (desc == null || desc.isEmpty()) {
            return UNKNOWN;
        }
        
        for (ExamStatusEnum status : values()) {
            if (status.getDesc().equals(desc)) {
                return status;
            }
        }
        
        return UNKNOWN;
    }
}

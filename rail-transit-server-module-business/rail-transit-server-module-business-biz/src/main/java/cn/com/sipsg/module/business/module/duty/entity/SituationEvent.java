package cn.com.sipsg.module.business.module.duty.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> lixt
 * @date : 2025-05-13 11:01:18
 * @Description: 警情信息表
 */
@Data
@TableName("tbl_situation_event")
public class SituationEvent {

    @TableId(type = IdType.ASSIGN_ID)
    private String id;  // 主键ID

    @TableField("event_code")
    private String eventCode;  // 警情编号

    @TableField("event_type_code")
    private String eventTypeCode;  // 警情类型代码

    @TableField("event_type_name")
    private String eventTypeName;  // 警情类型名称

    @TableField("alarm_person_name")
    private String alarmPersonName;  // 报警人_姓名

    @TableField("alarm_person_phone")
    private String alarmPersonPhone;  // 报警人_联系电话

    @TableField("alarm_person_gender")
    private String alarmPersonGender;  // 报警人_性别代码

    @TableField("alarm_address")
    private String alarmAddress;  // 警情报警地点

    @TableField("alarm_address_lng")
    private Double alarmAddressLng;  // 警情报警位置经度

    @TableField("alarm_address_lat")
    private Double alarmAddressLat;  // 警情报警位置纬度

    @TableField("alarm_address_coordinate")
    private String alarmAddressCoordinate;  // 警情报警位置坐标系

    @TableField("alarm_belong_police_org_code")
    private String alarmBelongPoliceOrgCode;  // 报警地所属辖区编号

    @TableField("alarm_belong_police_org_name")
    private String alarmBelongPoliceOrgName;  // 报警第所属辖区名称

    @TableField("happened_belong_police_org_code")
    private String happenedBelongPoliceOrgCode;  // 案发地所属辖区编号

    @TableField("happened_belong_police_org_name")
    private String happenedBelongPoliceOrgName;  // 案发地所属辖区名称

    @TableField("happened_address")
    private String happenedAddress;  // 警情案发地点

    @TableField("happened_address_lng")
    private Double happenedAddressLng;  // 警情案发位置经度

    @TableField("happened_address_lat")
    private Double happenedAddressLat;  // 警情案发位置维度

    @TableField("happened_address_coordinate")
    private String happenedAddressCoordinate;  // 警情案发位置坐标系

    @TableField("happened_place")
    private String happenedPlace;  // 事发场所

    @TableField("happened_location")
    private String happenedLocation;  // 事发部位

    @TableField("happened_start_time")
    private LocalDateTime happenedStartTime;  // 事发时间初值

    @TableField("happened_end_time")
    private LocalDateTime happenedEndTime;  // 事发时间终值

    @TableField("happened_reason")
    private String happenedReason;  // 事发原因

    @TableField("receive_type")
    private String receiveType;  // 接警类型

    @TableField("receive_time")
    private LocalDateTime receiveTime;  // 接警时间

    @TableField("receive_police_org_code")
    private String receivePoliceOrgCode;  // 接警单位编号

    @TableField("receive_police_org_name")
    private String receivePoliceOrgName;  // 接警单位名称

    @TableField("handle_tag")
    private String handleTag;  // 处警标示

    @TableField("handle_type")
    private String handleType;  // 处警类别

    @TableField("handle_time")
    private LocalDateTime handleTime;  // 处警时间

    @TableField("handle_person_name")
    private String handlePersonName;  // 处警人

    @TableField("handle_police_org_code")
    private String handlePoliceOrgCode;  // 处警单位编号

    @TableField("handle_police_org_name")
    private String handlePoliceOrgName;  // 处警单位名称

    @TableField("handle_result")
    private String handleResult;  // 处警结果

    @TableField("process_result")
    private String processResult;  // 处理结果

    @TableField("dangerous_item_type")
    private String dangerousItemType;  // 危险物质类型

    @TableField("relation_case_code")
    private String relationCaseCode;  // 关联案件编号

    @TableField("relation_case_status")
    private String relationCaseStatus;  // 关联案件状态

    @TableField("label_status")
    private String labelStatus;  // 标注状态

    @TableField("expand_field1")
    private String expandField1;

    @TableField("expand_field2")
    private String expandField2;

    @TableField("expand_field3")
    private String expandField3;

    @TableField("expand_field4")
    private String expandField4;

    @TableField("expand_field5")
    private String expandField5;

    @TableField("expand_field6")
    private String expandField6;

    @TableField("expand_field7")
    private String expandField7;

    @TableField("expand_field8")
    private String expandField8;

    @TableField("expand_field9")
    private String expandField9;

    @TableField("expand_field10")
    private String expandField10;

    @TableField("expand_field11")
    private String expandField11;

    @TableField("expand_field12")
    private String expandField12;

    @TableField("expand_field13")
    private String expandField13;

    @TableField("expand_field14")
    private String expandField14;

    @TableField("expand_field15")
    private String expandField15;

    @TableField("expand_field16")
    private String expandField16;

    @TableField("expand_field17")
    private String expandField17;

    @TableField("expand_field18")
    private String expandField18;

    @TableField("expand_field19")
    private String expandField19;

    @TableField("expand_field20")
    private String expandField20;

    @TableField("handle_person_code")
    private String handlePersonCode;  // 处警人编号

    @TableField("event_call_type_code")
    private String eventCallTypeCode;  // 警情来话类别代码

    @TableField("event_phone_user_info")
    private String eventPhoneUserInfo;  // 报警电话用户信息

    @TableField("event_phone_user_address")
    private String eventPhoneUserAddress;  // 报警电话用户地址

    @TableField("record_org_code")
    private String recordOrgCode;  // 登记单位编号

    @TableField("record_time")
    private LocalDateTime recordTime;  // 登记时间

    @TableField("record_name")
    private String recordName;  // 登记人姓名

    @TableField("modify_name")
    private String modifyName;  // 修改人姓名

    @TableField("receive_name")
    private String receiveName;  // 接警人姓名

    @TableField("handle_detailed_address")
    private String handleDetailedAddress;  // 处警详细地点

    @TableField("record_person_code")
    private String recordPersonCode;  // 登记人编号

    @TableField("case_receiver")
    private String caseReceiver;  // 案件受理人

    @TableField("case_receive_org_code")
    private String caseReceiveOrgCode;  // 案件受理单位编号

    @TableField("leader_handle_time")
    private LocalDateTime leaderHandleTime;  // 领导处理时间

    @TableField("handle_result_code")
    private String handleResultCode;  // 处警结果代码

    @TableField("handle_feedback_time")
    private LocalDateTime handleFeedbackTime;  // 处警反馈时间

    @TableField("event_time_superior_limit")
    private LocalDateTime eventTimeSuperiorLimit;  // 事发时间上限

    @TableField("event_time_low_limit")
    private LocalDateTime eventTimeLowLimit;  // 事发时间下限

    @TableField("extra_process_result")
    private String extraProcessResult;  // 补充处理结果

    @TableField("handle_person_contact")
    private String handlePersonContact;  // 处警人联系方式

    @TableField("detailed_address")
    private String detailedAddress;  // 详址

    @TableField("record_org_name")
    private String recordOrgName;  // 登记单位名称

    @TableField("dealwith_org_id")
    private String dealwithOrgId;  // 处警单位id

    @TableField("dealwith_time")
    private LocalDateTime dealwithTime;  // 处警时间

    @TableField("dealwith_emp_name")
    private String dealwithEmpName;  // 处警人

    @TableField("incident_station_name")
    private String incidentStationName;  // 发生地点

    @TableField("dealwith_result")
    private String dealwithResult;  // 处理结果

    @TableField("dealwith_emp_no")
    private String dealwithEmpNo;  // 处警人警号
}

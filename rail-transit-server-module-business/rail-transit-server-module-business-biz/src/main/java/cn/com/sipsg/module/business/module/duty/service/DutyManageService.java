package cn.com.sipsg.module.business.module.duty.service;


import cn.com.sipsg.module.business.module.duty.pojo.dto.DutyScheduleEditDTO;
import cn.com.sipsg.module.business.module.duty.pojo.dto.DutyScheduleQueryDTO;
import cn.com.sipsg.module.business.module.duty.pojo.dto.DutyTeamEditDTO;
import cn.com.sipsg.module.business.module.duty.pojo.dto.DutyTurnEditDTO;
import cn.com.sipsg.module.business.module.duty.pojo.dto.PostCategoryAddDTO;
import cn.com.sipsg.module.business.module.duty.pojo.vo.DutyScheduleVO;
import cn.com.sipsg.module.business.module.duty.pojo.vo.DutyTeamVO;
import cn.com.sipsg.module.business.module.duty.pojo.vo.OnDutyEmpVO;
import cn.com.sipsg.module.business.module.duty.pojo.vo.OrgVO;
import cn.com.sipsg.module.business.module.duty.pojo.vo.PostDutyDesignVO;
import cn.com.sipsg.module.business.module.duty.pojo.vo.StationOnDutyInfoVO;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.StationPoliceCountVO;
import cn.com.sipsg.module.business.module.duty.pojo.vo.PostCategorySimpleVO;
import cn.com.sipsg.module.business.module.duty.pojo.vo.PersonalDutyStatisticsVO;
import cn.com.sipsg.module.business.module.duty.pojo.vo.DutyPersonSelectVO;

import java.util.List;

/**
 * <AUTHOR> lixt
 * @date : 2025-05-07 16:33:20
 * @Description: 值班管理相关接口
 */
public interface DutyManageService {
    /**
     * 值班动态统计：统计当天在岗的所有单位数量，按组织编号分组统计
     *
     * @return 各单位在岗动态列表
     */
    List<OnDutyEmpVO> onDutyDynamic();

    /**
     * 新增-修改班次类型
     * 如果DTO中包含ID则为修改操作，否则为新增操作
     *
     * @param dto 新增-修改参数，包含ID时为修改，不包含ID时为新增
     * @return 操作是否成功
     */
    Boolean saveDutyTurn(DutyTurnEditDTO dto);

    /**
     * 编辑班次类型
     *
     * @param dto 编辑参数
     * @return 是否成功
     */
    Boolean editDutyTurn(DutyTurnEditDTO dto);

    /**
     * 删除班次类型
     *
     * @param id 班次ID
     * @return 是否成功
     */
    Boolean deleteDutyTurn(String id);

    /**
     * 新增班次类型
     *
     * @param dto 新增参数
     * @return 是否成功
     */
    Boolean addDutyTurn(DutyTurnEditDTO dto);

    /**
     * 新增-修改岗位类别
     * 如果DTO中包含ID则为修改操作，否则为新增操作
     *
     * @param dto 新增-修改参数，包含ID时为修改，不包含ID时为新增
     * @return 操作是否成功
     */
    Boolean addPostCategory(PostCategoryAddDTO dto);

    /**
     * 岗位班次设置列表
     *
     * @param orgId 单位ID，可选
     * @return 岗位班次设计VO列表
     */
    List<PostDutyDesignVO> listPostDutyDesign(String orgId);

    /**
     * 获取班组管理列表
     *
     * @return 班组列表
     */
    List<DutyTeamVO> listDutyTeam();

    /**
     * 编辑班组信息
     *
     * @param dto 班组编辑DTO
     * @return 是否成功
     */
    Boolean editDutyTeam(DutyTeamEditDTO dto);

    /**
     * 获取单位列表
     *
     * @return 单位列表
     */
    List<OrgVO> listOrg();

    /**
     * 根据站点编号查询所属单位及在岗警力情况
     * @param stationCode 站点编号
     * @return 单位名称及在岗警力信息
     */
    StationOnDutyInfoVO getStationOnDutyInfo(String stationCode);
    
    /**
     * 查询指定时间范围内的排班信息
     * 
     * @param dto 包含单位名称、时间范围和岗位类别的查询条件
     * @return 每天的值班人员信息列表
     */
    List<DutyScheduleVO> queryDutySchedule(DutyScheduleQueryDTO dto);
    
    /**
     * 编辑排班信息
     * 
     * @param dto 排班编辑DTO
     * @return 是否成功
     */
    Boolean editDutySchedule(DutyScheduleEditDTO dto);

    /**
     * 批量编辑排班信息
     * 
     * @param dtoList 排班编辑DTO列表
     * @return 是否成功
     */
    Boolean batchEditDutySchedule(List<DutyScheduleEditDTO> dtoList);

    /**
     * 查询单位下所有岗位类别
     * 
     * @param orgId 单位ID
     * @return 岗位类别列表
     */
    List<PostCategorySimpleVO> listSimplePostCategory(String orgId);

    /**
     * 个人排班统计
     *
     * @param dto 查询条件
     * @return 个人排班统计列表
     */
    List<PersonalDutyStatisticsVO> personalDutyStatistics(DutyScheduleQueryDTO dto);

    /**
     * 查询岗位类别下的班组人员
     *
     * @param postId 岗位类别ID
     * @param orgId  组织机构ID
     * @return 班组人员列表
     */
    List<DutyPersonSelectVO> listDutyPersonByPost(String orgId,String postId);
    
    /**
     * 获取当天各站点值班民警数量
     * 统计当天所有站点的在岗民警数量
     *
     * @return 各站点值班民警数量列表
     */
    List<StationPoliceCountVO> getStationPoliceCount();
}
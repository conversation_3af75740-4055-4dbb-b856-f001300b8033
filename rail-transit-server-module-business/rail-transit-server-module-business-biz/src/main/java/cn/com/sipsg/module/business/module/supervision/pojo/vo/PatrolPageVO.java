package cn.com.sipsg.module.business.module.supervision.pojo.vo;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 巡逻情况分页VO
 */
@Data
public class PatrolPageVO {
    private String id; // 主键id
    private String policeOrgId; // 单位ID
    private String policeOrgName; // 单位名称
    private LocalDateTime formTime; // 时间（填报时间）
    private String call; // 巡逻车呼号
    private String vehicle; // 车牌号
    private String area; // 巡逻区域
    // 预留操作字段（如详情、编辑等）
}
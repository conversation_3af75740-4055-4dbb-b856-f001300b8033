package cn.com.sipsg.module.business.module.duty.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> lixt
 * @date : 2025-04-29 16:11:54
 * @Description: 标签分组表
 */
@Data
@TableName("tbl_base_tag_group")
public class BaseTagGroup {

    @TableId(type = IdType.ASSIGN_ID)
    private String id;  // 主键ID

    @TableField("create_time")
    private LocalDateTime createTime;  // 创建时间

    @TableField("create_user_id")
    private String createUserId;  // 创建者用户ID

    @TableField("update_time")
    private LocalDateTime updateTime;  // 最后更新时间

    @TableField("update_user_id")
    private String updateUserId;  // 最后更新者用户ID

    @TableField("available")
    private Boolean available;  // 是否有效

    @TableField("group_name")
    private String groupName;  // 分组名称

    @TableField("business_type")
    private Integer businessType;  // 业务类型
}
package cn.com.sipsg.module.business.module.supervision.controller;

import cn.com.sipsg.common.pojo.CommonResult;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.web.core.controller.BaseController;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.IdDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.WorkRemindQueryDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.WorkRemindSaveDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.WorkRemindVO;
import cn.com.sipsg.module.business.module.supervision.service.WorkRemindService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 工作提醒Controller
 */
@RestController
@RequestMapping("/workRemind")
@RequiredArgsConstructor
@Validated
@Tag(name = "工作提醒管理", description = "工作提醒相关接口")
public class WorkRemindController extends BaseController {

    private final WorkRemindService workRemindService;

    @PostMapping("/save")
    @Operation(summary = "新增或修改工作提醒", description = "如果传入ID则为修改操作，否则为新增操作")
    public CommonResult<Boolean> saveOrUpdateWorkRemind(@Valid @RequestBody WorkRemindSaveDTO dto) {
        return handle(() -> CommonResult.data(workRemindService.saveOrUpdateWorkRemind(dto)));
    }

    @PostMapping("/detail")
    @Operation(summary = "获取工作提醒详情", description = "根据ID获取工作提醒详情信息")
    public CommonResult<WorkRemindSaveDTO> getWorkRemindDetail(@RequestBody @Valid IdDTO dto) {
        return handle(() -> CommonResult.data(workRemindService.getWorkRemindDetail(dto.getId())));
    }

    @PostMapping("/delete")
    @Operation(summary = "删除工作提醒", description = "根据ID删除工作提醒（逻辑删除）")
    public CommonResult<Boolean> deleteWorkRemind(@RequestBody @Valid IdDTO dto) {
        return handle(() -> CommonResult.data(workRemindService.deleteWorkRemind(dto.getId())));
    }

    @PostMapping("/page")
    @Operation(summary = "分页查询工作提醒列表", description = "支持按标题、执法单位编号、执法时间范围查询，支持数据权限")
    public CommonResult<CommonPageVO<WorkRemindVO>> pageWorkRemind(@Valid @RequestBody WorkRemindQueryDTO queryDTO) {
        return handle(() -> CommonResult.data(workRemindService.pageWorkRemind(queryDTO)));
    }
}
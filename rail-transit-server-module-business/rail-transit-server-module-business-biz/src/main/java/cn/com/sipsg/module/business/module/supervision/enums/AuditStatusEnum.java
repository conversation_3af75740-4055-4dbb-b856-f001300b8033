package cn.com.sipsg.module.business.module.supervision.enums;

import lombok.Getter;

/**
 * 审核状态枚举
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Getter
public enum AuditStatusEnum {

    /**
     * 待审核
     */
    PENDING("PENDING", "待审核"),

    /**
     * 已审核通过
     */
    APPROVED("APPROVED", "已审核通过"),

    /**
     * 处理中
     */
    PROCESSING("PROCESSING", "处理中"),

    /**
     * 已结案
     */
    CLOSED("CLOSED", "已结案");

    /**
     * 状态编码
     */
    private final String code;

    /**
     * 状态描述
     */
    private final String description;

    AuditStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }
}
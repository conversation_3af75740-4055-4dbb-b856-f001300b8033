package cn.com.sipsg.module.business.module.supervision.service;

import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.SecurityEmpPageReqDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.SecurityStationCreateReqDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.SecurityStationPageReqDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.*;

import java.time.LocalDate;
import java.util.List;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.SecurityEmpCreateReqDTO;

/**
 * 安检督导服务接口
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
public interface SecuritySupervisionService {
    /**
     * 获取安检点数量
     * 统计当前用户所在辖区的安检点数量
     *
     * @return 安检点数量统计结果
     */
    SecurityStationCountVO getSecurityStationCount();

    /**
     * 获取安检人员数量
     * 统计当前用户所在组织下的安检人员数量
     *
     * @return 安检人员数量统计结果
     */
    SecurityEmpCountVO getSecurityEmpCount();

    /**
     * 获取安检次数
     * 统计当前用户所在组织下的安检次数
     *
     * @return 安检次数统计结果
     */
    SecurityCheckCountVO getSecurityCheckCount();
    
    /**
     * 根据查询日期获取在职安检员总数，并按单位名称分组统计数量和占比
     *
     * @param queryDate 查询日期，如果为null则使用当前日期
     * @return 安检人员按单位分组统计结果
     */
    SecurityEmpGroupVO getSecurityEmpGroupByOrgName(LocalDate queryDate);
    
    /**
     * 根据查询日期获取在职安检员总数，并按年龄分组统计数量和占比
     *
     * @param queryDate 查询日期，如果为null则使用当前日期
     * @return 安检人员按年龄分组统计结果
     */
    SecurityEmpAgeGroupVO getSecurityEmpGroupByAge(LocalDate queryDate);
    
    /**
     * 根据查询日期获取在职安检员总数，并按单位名称分组统计保安证持证情况
     *
     * @param queryDate 查询日期，如果为null则使用当前日期
     * @return 安检人员按单位分组统计保安证持证情况结果
     */
    SecurityEmpCertGroupVO getSecurityEmpCertGroupByOrg(LocalDate queryDate);
    
    /**
     * 根据查询日期获取安检员总数，并按单位名称分组统计入职和离职数量
     *
     * @param queryDate 查询日期，如果为null则使用当前日期
     * @return 安检人员按单位分组统计入职离职情况结果
     */
    SecurityEmpStatusGroupVO getSecurityEmpStatusGroupByOrg(LocalDate queryDate);
    
    /**
     * 分页查询安检点位信息
     *
     * @param reqDTO 分页查询参数
     * @return 安检点位分页查询结果
     */
    CommonPageVO<SecurityStationPageVO> getSecurityStationPage(SecurityStationPageReqDTO reqDTO);
    
    /**
     * 创建安检点位
     *
     * @param reqDTO 安检点位创建请求
     * @return 创建的安检点位ID
     */
    String createSecurityStation(SecurityStationCreateReqDTO reqDTO);
    
    /**
     * 安检员分页列表
     * 分页查询安检员信息，支持按姓名、身份证号、联系电话、单位名称等条件筛选
     *
     * @param reqDTO 分页查询参数
     * @return 安检员分页查询结果
     */
    CommonPageVO<SecurityEmpPageVO> getSecurityEmpPage(SecurityEmpPageReqDTO reqDTO);
    
    /**
     * 获取安检员档案详情
     *
     * @param id 安检员ID
     * @return 安检员档案详情
     */
    SecurityEmpProfileVO getSecurityEmpProfile(String id);

    /**
     * 新增安检员，前端传图片URL
     * @param reqDTO 表单数据
     * @return 新增安检员ID
     */
    String createSecurityEmp(SecurityEmpCreateReqDTO reqDTO);

    /**
     * 安检督导统计汇总
     * @return 安检点、安检人员、安检次数、出勤率
     */
    SecuritySupervisionSummaryVO getSecuritySupervisionSummary();

    /**
     * 查询所有地铁线路及其下属站点（只含编号和名称）
     */
    List<SubwayLineSimpleVO> getAllLinesAndStations();

    /**
     * 获取安检点位所属公司下拉（去重，返回id+name）
     * @return 公司下拉列表
     */
    List<CompanyOptionVO> getAllSecurityStationCompanies();

    /**
     * 获取管辖单位下拉（当前用户及下级单位，管理员全部）
     * @return 单位下拉列表
     */
    List<OrgOptionVO> getOrgOptionsForCurrentUser();

    /**
     * 安检点逻辑删除，将available字段置为false
     * @param id 安检点ID
     * @return 是否删除成功
     */
    Boolean deleteSecurityStation(String id);

    /**
     * 根据ID获取安检点详情
     * @param id 安检点ID
     * @return 安检点详情VO
     */
    SecurityStationDetailVO getSecurityStationDetail(String id);

    /**
     * 安检员逻辑删除
     * @param id 安检员ID
     * @return 是否删除成功
     */
    Boolean deleteSecurityEmp(String id);
}
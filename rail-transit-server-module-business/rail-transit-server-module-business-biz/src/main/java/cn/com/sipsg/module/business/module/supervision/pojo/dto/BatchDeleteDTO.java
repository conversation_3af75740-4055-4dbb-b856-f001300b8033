package cn.com.sipsg.module.business.module.supervision.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 批量删除DTO
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@Schema(description = "批量删除DTO")
public class BatchDeleteDTO {
    
    @NotEmpty(message = "ID列表不能为空")
    @Schema(description = "ID列表", required = true, example = "[\"1\",\"2\",\"3\"]")
    private List<String> ids;
}
package cn.com.sipsg.module.business.module.supervision.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 地铁线路信息表
 */
@Data
@TableName("tbl_base_subway")
public class BaseSubway {
    /** 主键ID */
    @TableId("id")
    private Long id;
    /** 线路编号 */
    @TableField("code")
    private String code;
    /** 线路名称 */
    @TableField("name")
    private String name;
    /** 线路状态 */
    @TableField("status")
    private Integer status;
    /** 耗时,单位分钟 */
    @TableField("time_consuming")
    private Integer timeConsuming;
    /** 里程,单位米 */
    @TableField("distance")
    private Integer distance;
    /** 起始站点ID */
    @TableField("start_station_code")
    private Long startStationCode;
    /** 起始站点名称 */
    @TableField("start_station_name")
    private String startStationName;
    /** 起始站点服务时间，例06:00-22:00 */
    @TableField("start_station_time")
    private String startStationTime;
    /** 终点站点ID */
    @TableField("end_station_code")
    private Long endStationCode;
    /** 未知字段 */
    @TableField("end_station_name")
    private String endStationName;
    /** 终点站点服务时间，例06:00-22:00 */
    @TableField("end_station_time")
    private String endStationTime;
    /** 线路轨迹点集合,JSON格式 */
    @TableField("point_json")
    private String pointJson;
    /** 备注 */
    @TableField("memos")
    private String memos;
    /** 创建人 */
    @TableField("create_user_id")
    private String createUserId;
    /** 创建时间 */
    @TableField("create_time")
    private LocalDateTime createTime;
    /** 更新人 */
    @TableField("update_user_id")
    private String updateUserId;
    /** 更新时间 */
    @TableField("update_time")
    private LocalDateTime updateTime;
    /** 有效标识,TRUE:有效,FALSE:无效 */
    @TableField("available")
    private Boolean available;
    /** 位置类型（1高架、2隧道、3高架+隧道） */
    @TableField("location_type")
    private Integer locationType;
    /** 所属车辆段 */
    @TableField("car_depot")
    private String carDepot;
    /** 所属运营中心 */
    @TableField("operation_center")
    private String operationCenter;
    /** 列车型号 */
    @TableField("train_model")
    private String trainModel;
    /** 所经行政区数 */
    @TableField("district_num")
    private Integer districtNum;
    /** 所经行政区列表 */
    @TableField("district_name")
    private String districtName;
    /** 所经地面派出所数 */
    @TableField("police_station_num")
    private Integer policeStationNum;
    /** 所经地面派出列表 */
    @TableField("police_station_name")
    private String policeStationName;
    /** 所属变电站名称 */
    @TableField("transformer_substation")
    private String transformerSubstation;
    /** 所属冷塔名称 */
    @TableField("cold_tower")
    private String coldTower;
    /** 列车车厢数量 */
    @TableField("carriage_num")
    private Integer carriageNum;
    /** 列车最大载客量 */
    @TableField("max_capacity")
    private Integer maxCapacity;
    /** 列车车门数量 */
    @TableField("door_num")
    private Integer doorNum;
} 
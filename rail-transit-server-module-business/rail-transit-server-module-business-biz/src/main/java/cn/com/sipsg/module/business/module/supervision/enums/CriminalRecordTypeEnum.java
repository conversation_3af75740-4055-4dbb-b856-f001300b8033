package cn.com.sipsg.module.business.module.supervision.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 前科类型枚举
 */
@Getter
@AllArgsConstructor
public enum CriminalRecordTypeEnum {
    
    INTENTIONAL_HOMICIDE("01", "故意杀人"),
    INTENTIONAL_INJURY("02", "故意伤害"),
    ROBBERY("03", "抢劫"),
    THEFT("04", "盗窃"),
    FRAUD("05", "诈骗"),
    CORRUPTION_BRIBERY("06", "贪污受贿"),
    SMUGGLING("07", "走私"),
    DANGEROUS_DRIVING("08", "危险驾驶"),
    SECURITY_ADMINISTRATIVE_FINE("09", "治安行政罚款"),
    ADMINISTRATIVE_DETENTION("10", "行政拘留"),
    CUSTODY_EDUCATION("11", "收容教育"),
    CUSTODY_UPBRINGING("12", "收容教养"),
    DRUG_RELATED("13", "涉毒"),
    PROSTITUTION_RELATED("14", "涉黄"),
    SOCIAL_PHOBIA("15", "社恐");
    
    /**
     * 前科类型编号
     */
    private final String code;
    
    /**
     * 前科类型名称
     */
    private final String name;
    
    /**
     * 根据编号获取枚举
     * 
     * @param code 编号
     * @return 枚举值
     */
    public static CriminalRecordTypeEnum getByCode(String code) {
        for (CriminalRecordTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
    
    /**
     * 根据名称获取枚举
     * 
     * @param name 名称
     * @return 枚举值
     */
    public static CriminalRecordTypeEnum getByName(String name) {
        for (CriminalRecordTypeEnum type : values()) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return null;
    }
}
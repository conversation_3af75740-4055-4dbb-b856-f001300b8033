package cn.com.sipsg.module.business.module.supervision.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

@Data
@TableName("tbl_site_place_area")
public class SitePlaceArea {
    /** 记录唯一标识主键 */
    @TableId("id")
    private Long id;
    /** 记录创建时间 */
    @TableField("create_time")
    private Long createTime;
    /** 记录最后更新时间 */
    @TableField("update_time")
    private Long updateTime;
    /** 创建者用户ID */
    @TableField("create_user_id")
    private String createUserId;
    /** 最后更新者用户ID */
    @TableField("update_user_id")
    private String updateUserId;
    /** 是否有效 */
    @TableField("available")
    private Boolean available;
    /** 场所ID */
    @TableField("place_id")
    private Long placeId;
    /** 区域类型编码 */
    @TableField("area_type_no")
    private String areaTypeNo;
    /** 区域类型名 */
    @TableField("area_type_cn")
    private String areaTypeCn;
    /** 区域名称 */
    @TableField("area_name")
    private String areaName;
    /** 部位编码 */
    @TableField("region_no")
    private String regionNo;
    /** 部位名称 */
    @TableField("region_cn")
    private String regionCn;
    /** 备注 */
    @TableField("remark")
    private String remark;
    /** 排序 */
    @TableField("sort")
    private Integer sort;
} 
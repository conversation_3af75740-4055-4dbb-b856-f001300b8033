package cn.com.sipsg.module.business.module.supervision.entity;

import lombok.Data;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.IdType;

/**
 * 人员抓捕情况
 */
@Data
@TableName("tbl_personnel_captured")
public class PersonnelCaptured {
    /** 主键ID */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;
    /** 抓获人员姓名 */
    @TableField("name")
    private String name;
    /** 抓获人员身份证 */
    @TableField("id_no")
    private String idNo;
    /** 手机号 */
    @TableField("phone")
    private String phone;
    /** 抓获单位ID */
    @TableField("police_org_id")
    private String policeOrgId;
    /** 抓获单位名称 */
    @TableField("police_org_name")
    private String policeOrgName;
    /** 预警来源 1(101预警)、2(票卡预警)、3(盘查)、4(其他) */
    @TableField("warning_sources")
    private Short warningSources;
    /** 抓获类型 1(全国在逃)、2(公安临控)、3(其他) */
    @TableField("capture_type")
    private Short captureType;
    /** 抓获地点 */
    @TableField("capture_site")
    private String captureSite;
    /** 抓获时间 */
    @TableField("capture_date")
    private LocalDateTime captureDate;
    /** 处理结果 1(拘留)、2(移交) */
    @TableField("process_result")
    private Short processResult;
    /** 处理结果详情 */
    @TableField("process_result_detail")
    private String processResultDetail;
    /** 移交单位 */
    @TableField("turn_over_org")
    private String turnOverOrg;
    /** 后续处理 1(行政拘留)、2(刑事拘留)、3(取保候审)、4(排除嫌疑)、5(放行)、6(不予处罚)、99(其他) */
    @TableField("post_process_result")
    private Short postProcessResult;
    /** 现场图片 */
    @TableField("image_resource")
    private String imageResource;
    /** 备注 */
    @TableField("remark")
    private String remark;
    /** 有效标识,TRUE:有效,FALSE:无效 */
    @TableField("available")
    private Boolean available;
    /** 创建人 */
    @TableField("create_user_id")
    private String createUserId;
    /** 创建时间 */
    @TableField("create_time")
    private LocalDateTime createTime;
    /** 更新人 */
    @TableField("update_user_id")
    private String updateUserId;
    /** 更新时间 */
    @TableField("update_time")
    private LocalDateTime updateTime;
    /** 协助抓获单位 */
    @TableField("co_organizer")
    private String coOrganizer;
}
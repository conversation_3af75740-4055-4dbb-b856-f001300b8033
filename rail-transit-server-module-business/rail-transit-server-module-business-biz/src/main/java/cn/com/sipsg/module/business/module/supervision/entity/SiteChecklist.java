package cn.com.sipsg.module.business.module.supervision.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@TableName("tbl_site_checklist")
public class SiteChecklist {
    /** 记录唯一标识主键 */
    @TableId("id")
    private Long id;
    /** 记录创建时间 */
    @TableField("create_time")
    private LocalDateTime createTime;
    /** 记录最后更新时间 */
    @TableField("update_time")
    private LocalDateTime updateTime;
    /** 创建者用户ID */
    @TableField("create_user_id")
    private String createUserId;
    /** 最后更新者用户ID */
    @TableField("update_user_id")
    private String updateUserId;
    /** 是否有效 */
    @TableField("available")
    private Boolean available;
    /** 设备名称或别名 */
    @TableField("name")
    private String name;
    /** 检查单分类ID */
    @TableField("checklist_type_id")
    private Long checklistTypeId;
    /** 检查对象，字符串数组 */
    @TableField(value = "check_obj", typeHandler = org.apache.ibatis.type.ArrayTypeHandler.class)
    private String[] checkObj;
} 
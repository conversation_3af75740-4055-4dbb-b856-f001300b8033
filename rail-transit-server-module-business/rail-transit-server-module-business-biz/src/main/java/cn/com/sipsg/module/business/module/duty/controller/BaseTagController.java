package cn.com.sipsg.module.business.module.duty.controller;


import cn.com.sipsg.common.pojo.CommonResult;
import cn.com.sipsg.common.web.core.controller.BaseController;
import cn.com.sipsg.module.business.module.duty.pojo.dto.TagAddDTO;
import cn.com.sipsg.module.business.module.duty.pojo.dto.TagDeleteDTO;
import cn.com.sipsg.module.business.module.duty.pojo.dto.TagGroupDeleteDTO;
import cn.com.sipsg.module.business.module.duty.pojo.dto.TagGroupAddDTO;
import cn.com.sipsg.module.business.module.duty.pojo.dto.TagGroupBatchUpdateDTO;
import cn.com.sipsg.module.business.module.duty.pojo.dto.TagGroupUpdateDTO;
import cn.com.sipsg.module.business.module.duty.pojo.vo.TagGroupVO;
import cn.com.sipsg.module.business.module.duty.service.BaseTagService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import javax.validation.Valid;

/**
 * <AUTHOR> lixt
 * @date : 2025-04-30 17:05:12
 * @Description: 标签管理
 */
@Tag(name = "标签管理")
@RestController
@RequestMapping("/tag")
@RequiredArgsConstructor
@Slf4j
public class BaseTagController extends BaseController {
    private final BaseTagService baseTagService;

    @Operation(summary = "标签分组及详情")
    @GetMapping("/tag/group")
    public CommonResult<List<TagGroupVO>> tagGroup() {
        return handle(() -> CommonResult.data(baseTagService.tagGroup()));
    }

    @Operation(summary = "添加标签分组")
    @PostMapping("/tag/group/add")
    public CommonResult<Void> addTagGroup(@RequestBody @Valid TagGroupAddDTO dto) {
        return handle(() -> {
            baseTagService.addTagGroup(dto);
            return CommonResult.success();
        });
    }

    @Operation(summary = "添加标签")
    @PostMapping("/tag/add")
    public CommonResult<Void> addTag(@RequestBody @Valid TagAddDTO dto) {
        return handle(() -> {
            baseTagService.addTag(dto);
            return CommonResult.success();
        });
    }

    @Operation(summary = "修改标签的分组")
    @PostMapping("/tag/group/update")
    public CommonResult<Void> updateTagGroup(@RequestBody @Valid TagGroupUpdateDTO dto) {
        return handle(() -> {
            baseTagService.updateTagGroup(dto);
            return CommonResult.success();
        });
    }

    @Operation(summary = "批量修改标签的分组")
    @PostMapping("/tag/group/batch-update")
    public CommonResult<Void> batchUpdateTagGroup(@RequestBody @Valid TagGroupBatchUpdateDTO dto) {
        return handle(() -> {
            baseTagService.batchUpdateTagGroup(dto.getTagGroupMappings());
            return CommonResult.success();
        });
    }

    @Operation(summary = "删除标签")
    @PostMapping("/tag/delete")
    public CommonResult<Void> deleteTag(@RequestBody @Valid TagDeleteDTO dto) {
        return handle(() -> {
            baseTagService.deleteTag(dto);
            return CommonResult.success();
        });
    }

    @Operation(summary = "删除标签分组")
    @PostMapping("/tag/group/delete")
    public CommonResult<Void> deleteTagGroup(@RequestBody @Valid TagGroupDeleteDTO dto) {
        return handle(() -> {
            baseTagService.deleteTagGroup(dto);
            return CommonResult.success();
        });
    }
}

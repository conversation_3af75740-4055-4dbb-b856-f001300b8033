package cn.com.sipsg.module.business.module.supervision.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 设备故障上报表
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tbl_device_fault_report")
public class DeviceFaultReport {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 故障名称
     */
    @TableField("fault_name")
    private String faultName;

    /**
     * 发生站点
     */
    @TableField("occurrence_station")
    private String occurrenceStation;

    /**
     * 故障说明
     */
    @TableField("fault_description")
    private String faultDescription;

    /**
     * 紧急程度
     */
    @TableField("urgency_level")
    private String urgencyLevel;

    /**
     * 上报时间
     */
    @TableField("report_time")
    private LocalDateTime reportTime;

    /**
     * 维修进度
     */
    @TableField("repair_progress")
    private String repairProgress;

    /**
     * 上报单位编号
     */
    @TableField("report_org_id")
    private String reportOrgId;

    /**
     * 上报单位名称
     */
    @TableField("report_org_name")
    private String reportOrgName;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @TableField(value = "creator", fill = FieldFill.INSERT)
    private String creator;

    /**
     * 更新人
     */
    @TableField(value = "updater", fill = FieldFill.INSERT_UPDATE)
    private String updater;

    /**
     * 是否有效
     */
    @TableField("available")
    private Boolean available;
}
package cn.com.sipsg.module.business.module.supervision.service.impl;

import cn.com.sipsg.module.business.module.supervision.entity.SuperviseHandle;
import cn.com.sipsg.module.business.module.supervision.mapper.SuperviseHandleMapper;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.SuperviseRemindVO;
import cn.com.sipsg.module.business.module.common.util.UserOrgUtils;
import cn.com.sipsg.common.security.core.util.SecurityUtils;
import cn.com.sipsg.common.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import java.time.LocalDateTime;
import java.sql.Timestamp;
import java.util.stream.Collectors;
import java.util.Collections;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class NoticeServiceImpl implements NoticeService {
    private final SuperviseHandleMapper superviseHandleMapper;
    private final UserOrgUtils userOrgUtils;

    /**
     * 查询督办提醒列表
     * 支持数据权限控制：管理员看全部，普通用户只能看当前单位及下级单位
     * 
     * @return 督办提醒VO列表
     */
    @Override
    public List<SuperviseRemindVO> listSuperviseRemind() {
        try {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime weekAgo = now.minusDays(7);
            LambdaQueryWrapper<SuperviseHandle> query = new LambdaQueryWrapper<>();
            
            // 数据权限控制：根据用户角色决定可查看的数据范围
            List<String> accessibleOrgIds;
            if (SecurityUtils.isSuperAdmin()) {
                // 管理员可以查看全部数据，不限制组织范围
                accessibleOrgIds = null;
            } else {
                // 普通用户只能查看当前单位及下级单位的数据
                try {
                    accessibleOrgIds = userOrgUtils.getCurrentUserAndSubOrgIds();
                } catch (BusinessException e) {
                    log.warn("[督办提醒查询] 获取用户组织权限失败: {}", e.getMessage());
                    throw new BusinessException("获取用户权限信息失败，请联系管理员或重新登录");
                } catch (Exception e) {
                    log.error("[督办提醒查询] 获取用户组织权限异常", e);
                    throw new BusinessException("系统异常，请稍后重试");
                }
            }
            
            // 如果普通用户没有可访问的组织ID，返回空列表
            if (!SecurityUtils.isSuperAdmin() && (accessibleOrgIds == null || accessibleOrgIds.isEmpty())) {
                log.info("[督办提醒查询] 用户无可访问的组织权限，返回空列表");
                return Collections.emptyList();
            }
            
            // 查询条件：创建时间在一周内，且为有效记录，按创建时间倒序排列
            if (accessibleOrgIds != null && !accessibleOrgIds.isEmpty()) {
                // 普通用户：限制在可访问的组织范围内
                query.in(SuperviseHandle::getSignOrgId, accessibleOrgIds);
            }
            // 管理员：不添加组织限制条件，可以查看全部数据
            
            query.ge(SuperviseHandle::getCreateTime, Timestamp.valueOf(weekAgo))
                 .le(SuperviseHandle::getCreateTime, Timestamp.valueOf(now))
                 .eq(SuperviseHandle::getAvailable, true)
                 .orderByDesc(SuperviseHandle::getCreateTime);
            
            List<SuperviseHandle> list = superviseHandleMapper.selectList(query);
            
            // 将督办处理实体转换为督办提醒VO
            return list.stream().map(e -> {
                SuperviseRemindVO vo = new SuperviseRemindVO();
                vo.setTitle(e.getTitle());
                vo.setCreateTime(e.getCreateTime().toLocalDateTime());
                vo.setSignOrgName(e.getSignOrgName());
                return vo;
            }).collect(Collectors.toList());
            
        } catch (BusinessException e) {
            // 业务异常直接抛出，保持原有的错误信息
            throw e;
        } catch (Exception e) {
            log.error("[督办提醒查询] 查询督办提醒列表异常", e);
            throw new BusinessException("查询督办提醒失败，请稍后重试");
        }
    }
}
package cn.com.sipsg.module.business.module.supervision.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@TableName("tbl_site_checklist_module")
public class SiteChecklistModule {
    /** 记录唯一标识主键 */
    @TableId("id")
    private Long id;
    /** 记录创建时间 */
    @TableField("create_time")
    private LocalDateTime createTime;
    /** 记录最后更新时间 */
    @TableField("update_time")
    private LocalDateTime updateTime;
    /** 创建者用户ID */
    @TableField("create_user_id")
    private String createUserId;
    /** 最后更新者用户ID */
    @TableField("update_user_id")
    private String updateUserId;
    /** 是否有效 */
    @TableField("available")
    private Boolean available;
    /** 检查单ID */
    @TableField("checklist_id")
    private Long checklistId;
    /** 检查类目名 */
    @TableField("module_cn")
    private String moduleCn;
    /** 防护区域类型编码 */
    @TableField("area_type_no")
    private String areaTypeNo;
    /** 防护区域类型名 */
    @TableField("area_type_cn")
    private String areaTypeCn;
    /** 防护部位比编码 */
    @TableField("region_no")
    private String regionNo;
    /** 防护部位名 */
    @TableField("region_cn")
    private String regionCn;
    /** 检查项目 */
    @TableField("project")
    private String project;
    /** 检查内容 */
    @TableField("content")
    private String content;
    /** 检查类目编码 */
    @TableField("module_no")
    private String moduleNo;
    /** 表单类型 */
    @TableField("form_type")
    private Integer formType;
    /** 选项 */
    @TableField("items")
    private String items;
    /** 模块排序 */
    @TableField("module_sort")
    private Integer moduleSort;
    /** 记录排序 */
    @TableField("record_sort")
    private Integer recordSort;
    /** 检查单名称 */
    @TableField("checklist_name")
    private String checklistName;
} 
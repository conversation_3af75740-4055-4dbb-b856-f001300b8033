package cn.com.sipsg.module.business.module.duty.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> lixt
 * @date : 2025-05-08 11:15:53
 * @Description: 岗位表
 */
@Data
@TableName("tbl_duty_post")
public class DutyPost {

    @TableId(type = IdType.ASSIGN_ID)
    private String id; // 主键

    @TableField("police_org_id")
    private String policeOrgId; // 单位主键

    @TableField("post_name")
    private String postName; // 岗位名称

    @TableField("post_role")
    private Integer postRole; // 岗位角色

    @TableField("create_time")
    private LocalDateTime createTime; // 创建时间

    @TableField("update_time")
    private LocalDateTime updateTime; // 更新时间

    @TableField("create_user_id")
    private String createUserId; // 创建者ID

    @TableField("update_user_id")
    private String updateUserId; // 更新者ID

    @TableField("available")
    private Boolean available; // 是否有效
}
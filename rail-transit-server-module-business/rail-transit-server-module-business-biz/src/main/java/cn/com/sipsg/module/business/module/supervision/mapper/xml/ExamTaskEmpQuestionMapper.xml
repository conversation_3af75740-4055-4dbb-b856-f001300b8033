<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.sipsg.module.business.module.supervision.mapper.ExamTaskEmpQuestionMapper">

    <!-- 查询每人每题最新一次答题记录（分页） -->
    <select id="selectLatestRecordsByEmpAndTask" resultType="cn.com.sipsg.module.business.module.supervision.entity.ExamTaskEmpQuestion">
        SELECT t.*
        FROM tbl_exam_task_emp_question t
        INNER JOIN (
            SELECT question_id, MAX(create_time) AS max_time
            FROM tbl_exam_task_emp_question
            WHERE emp_id = #{empId}
              AND task_id = #{taskId}
              AND available = true
            GROUP BY question_id
        ) latest
        ON t.question_id = latest.question_id
        AND t.create_time = latest.max_time
        WHERE t.emp_id = #{empId}
          AND t.task_id = #{taskId}
          AND t.available = true
        ORDER BY t.question_id
        LIMIT #{pageSize} OFFSET #{offset}
    </select>

    <!-- 查询总数（每题最新一次） -->
    <select id="countLatestRecordsByEmpAndTask" resultType="int">
        SELECT COUNT(1)
        FROM (
            SELECT question_id
            FROM tbl_exam_task_emp_question
            WHERE emp_id = #{empId}
              AND task_id = #{taskId}
              AND available = true
            GROUP BY question_id
        ) t
    </select>
</mapper>
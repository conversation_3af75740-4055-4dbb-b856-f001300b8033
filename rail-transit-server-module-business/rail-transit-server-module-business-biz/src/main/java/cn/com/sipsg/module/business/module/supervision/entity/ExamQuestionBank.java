package cn.com.sipsg.module.business.module.supervision.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> lixt
 * @date : 2025-05-21 14:21:13
 * @Description: 试管理-题库表
 */
@Data
@TableName("tbl_exam_question_bank")
public class ExamQuestionBank {

    @TableId(type = IdType.ASSIGN_ID)
    private String id;  // 主键

    @TableField("create_user_id")
    private String createUserId;  // 创建人（警员ID）

    @TableField("create_time")
    private LocalDateTime createTime;  // 创建时间

    @TableField("update_user_id")
    private String updateUserId;  // 更新人（警员ID）

    @TableField("update_time")
    private LocalDateTime updateTime;  // 更新时间

    @TableField("available")
    private Boolean available;  // 是否有效

    @TableField("name")
    private String name;  // 题库名称

    @TableField("description")
    private String description;  // 描述

    @TableField("status")
    private String status;  // 状态（启用/禁用）

    @TableField("file_url")
    private String fileUrl;  // 导入的文件URL

    @TableField("file_name")
    private String fileName;  // 导入的文件名称
}

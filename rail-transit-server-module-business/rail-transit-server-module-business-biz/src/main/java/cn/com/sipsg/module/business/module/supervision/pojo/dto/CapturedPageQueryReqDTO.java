package cn.com.sipsg.module.business.module.supervision.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "抓获统计条件分页参数")
public class CapturedPageQueryReqDTO {
    @Schema(description = "抓获单位ID", example = "1001")
    private String policeOrgId;

    @Schema(description = "预警来源", example = "1")
    private String warningSources;

    @Schema(description = "抓获类型", example = "1")
    private String captureType;

    @Schema(description = "抓获地点", example = "某地铁站")
    private String captureSite;

    @Schema(description = "页码", example = "1")
    private Integer pageNum = 1;

    @Schema(description = "每页大小", example = "10")
    private Integer pageSize = 10;
} 
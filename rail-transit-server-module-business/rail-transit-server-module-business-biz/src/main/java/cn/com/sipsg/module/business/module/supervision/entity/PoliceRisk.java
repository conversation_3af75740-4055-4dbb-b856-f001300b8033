package cn.com.sipsg.module.business.module.supervision.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("tbl_police_risk")
public class PoliceRisk {

    @TableId(type = IdType.ASSIGN_ID)
    private String id;  // 主键

    @TableField("create_user_id")
    private String createUserId;  // 数据创建人

    @TableField(value = "create_time")
    private LocalDateTime createTime;  // 数据创建时间

    @TableField("update_user_id")
    private String updateUserId;  // 数据更新人

    @TableField(value = "update_time")
    private LocalDateTime updateTime;  // 数据更新时间

    @TableField("available")
    private Boolean available;  // 是否有效

    @TableField("station_id")
    private String stationId;  // 站点id

    @TableField("discover_time")
    private LocalDateTime discoverTime;  // 风险采集时间

    @TableField("content")
    private String content;  // 隐患内容

    @TableField("description")
    private String description;  // 隐患描述

    @TableField("risk_url")
    private String riskUrl;  // 现场图片url（json字符串，前端直接传json数组字符串，后端不做解析）

    @TableField("has_change")
    private Integer hasChange;  // 现场整改（0否，1是）

    @TableField("change_url")
    private String changeUrl;  // 整改后图片url（json字符串，前端直接传json数组字符串，后端不做解析）

    @TableField("type")
    private String type;  // 风险类型，存储RiskCategoryEnum的code

    @TableField("has_end")
    private Integer hasEnd;  // 整改结果（0未整改，1整改中，2已整改）

    @TableField("has_urgency")
    private Integer hasUrgency;  // 是否紧急上报（0否，1是）

    @TableField("rectify_start_time")
    private LocalDateTime rectifyStartTime; // 整改开始时间

    @TableField("rectify_end_time")
    private LocalDateTime rectifyEndTime; // 整改结束时间

    @TableField("category")
    private String category;  // 检查类目
}
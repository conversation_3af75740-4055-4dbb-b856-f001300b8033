package cn.com.sipsg.module.business.module.supervision.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 处理结果枚举
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
@Getter
@AllArgsConstructor
public enum ProcessingResultEnum {
    DETAIN("1", "拘留"),
    HANDOVER("2", "移交");

    private final String code;
    private final String name;

    public static ProcessingResultEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (ProcessingResultEnum status : values()) {
            if (code.equals(status.getCode())) {
                return status;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        ProcessingResultEnum status = getByCode(code);
        return status != null ? status.getName() : "未知状态";
    }
}

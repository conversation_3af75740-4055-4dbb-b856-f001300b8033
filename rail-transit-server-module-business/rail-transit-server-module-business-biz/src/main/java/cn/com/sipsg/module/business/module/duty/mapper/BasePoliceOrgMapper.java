package cn.com.sipsg.module.business.module.duty.mapper;


import cn.com.sipsg.common.mybatis.core.mapper.BaseMapperX;
import cn.com.sipsg.module.business.module.duty.entity.BasePoliceOrg;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Collection;

/**
 * <AUTHOR> lixt
 * @date : 2025-04-24 14:57:15
 * @Description:
 */
@Mapper
public interface BasePoliceOrgMapper extends BaseMapperX<BasePoliceOrg> {
    default List<BasePoliceOrg> listOrgPo(){
        return selectList();
    }

    /**
     * 根据ID集合批量查询组织
     */
    List<BasePoliceOrg> selectByIdList(@Param("ids") Collection<String> ids);
}

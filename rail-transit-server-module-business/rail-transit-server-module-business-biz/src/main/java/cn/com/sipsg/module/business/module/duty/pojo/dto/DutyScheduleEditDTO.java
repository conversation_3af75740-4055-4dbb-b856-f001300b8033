package cn.com.sipsg.module.business.module.duty.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR> lixt
 * @date : 2025-05-15
 * @Description: 排班编辑DTO
 */
@Data
@Schema(description = "排班编辑DTO")
public class DutyScheduleEditDTO {
    @Schema(description = "排班记录ID，编辑时必传", example = "1")
    private String id;
    
    @Schema(description = "新的值班人员ID", example = "10001")
    private String empId;
}
package cn.com.sipsg.module.business.module.supervision.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 通报信息保存/更新DTO
 */
@Data
@Schema(description = "通报信息保存/更新DTO")
public class NoticeReportSaveDTO {

    @Schema(description = "主键，新增时为空，修改时必传", example = "1")
    private String id;

    @Schema(description = "通报标题", example = "关于XX事件的通报")
    @NotBlank(message = "通报标题不能为空")
    private String title;

    @Schema(description = "签发单位ID", example = "1001")
    @NotNull(message = "签发单位ID不能为空")
    private String orgId;

    @Schema(description = "签发单位名称", example = "合肥市轨道交通公安分局")
    private String orgName;

    @Schema(description = "签发时间", example = "2023-07-01 10:00:00")
    private String lawTime;

    @Schema(description = "不规范内容")
    private String nonComplianceContent;

    @Schema(description = "接收单位ID", example = "1002")
    private String receiveOrgId;

    @Schema(description = "接收单位名称", example = "合肥市轨道交通运营有限公司")
    private String receiveOrgName;

    @Schema(description = "下发人ID（警员ID）", example = "emp001")
    private String issueUserId;

    @Schema(description = "下发人姓名", example = "张三")
    private String issueUserName;

    @Schema(description = "接收人ID（警员ID）", example = "emp002")
    private String receiveUserId;

    @Schema(description = "接收人姓名", example = "李四")
    private String receiveUserName;
}

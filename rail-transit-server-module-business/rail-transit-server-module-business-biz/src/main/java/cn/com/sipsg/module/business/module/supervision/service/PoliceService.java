package cn.com.sipsg.module.business.module.supervision.service;

import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.module.business.module.duty.pojo.vo.PoliceListVO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.DateQueryDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.OrgPolicePageDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.PoliceFuzzySearchDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.OnDutyCountVO;

import cn.com.sipsg.module.business.module.supervision.pojo.vo.StationPoliceCountListVO;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.LineStationPoliceCountVO;

import java.util.List;

/**
 * 警员服务接口
 * 提供警员相关业务逻辑处理
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
public interface PoliceService {
    
    /**
     * 获取当前用户所在组织的值班民警数量
     * 统计当前用户所在组织当天在岗的民警数量
     *
     * @return 当前组织值班民警数量VO
     */
    OnDutyCountVO getOnDutyPoliceCount();
    
    /**
     * 获取当前用户所在组织的值班辅警数量
     * 统计当前用户所在组织指定日期在岗的辅警数量
     *
     * @param queryDTO 查询参数，包含查询日期
     * @return 当前组织值班辅警数量VO
     */
    OnDutyCountVO getOnDutyAuxiliaryPoliceCount(DateQueryDTO queryDTO);
    
    /**
     * 获取各站点值班民警数量列表
     * 统计当天所有站点的在岗民警数量
     *
     * @return 各站点值班民警数量列表VO
     */
    StationPoliceCountListVO getStationPoliceCount();
    
    /**
     * 获取各站点值班辅警数量列表
     * 统计当天所有站点的在岗辅警数量
     *
     * @return 各站点值班辅警数量列表VO
     */
    StationPoliceCountListVO getStationAuxiliaryPoliceCount(DateQueryDTO queryDTO);
    
    /**
     * 按线路统计当天在岗民警数量
     * @return 线路分组的民警统计列表
     */
    List<LineStationPoliceCountVO> getLineStationPoliceCount();
    
    /**
     * 获取当前用户所在组织的警员分页列表
     * 查询当前用户所在组织下所有警员信息列表
     *
     * @param pageDTO 分页参数DTO
     * @return 警员分页列表
     */
    CommonPageVO<PoliceListVO> getOrgPoliceList(OrgPolicePageDTO pageDTO);
    
    /**
     * 获取当前用户所在组织的辅警分页列表
     * 查询当前用户所在组织下所有辅警信息列表（仅包含辅警，不包含民警、安保等其他人员）
     *
     * @param pageDTO 分页参数DTO
     * @return 辅警分页列表
     */
    CommonPageVO<PoliceListVO> getOrgAuxiliaryPoliceList(OrgPolicePageDTO pageDTO);
    
    /**
     * 模糊查询当前用户所在组织的警员分页列表
     * 根据关键字模糊查询当前用户所在组织下的民警信息（仅包含民警，不包含辅警、安保等其他人员）
     * 支持按姓名、工号、电话号码等字段进行模糊查询
     *
     * @param searchDTO 模糊查询参数DTO
     * @return 警员分页列表
     */
    CommonPageVO<PoliceListVO> fuzzySearchOrgPoliceList(PoliceFuzzySearchDTO searchDTO);
    
    /**
     * 模糊查询当前用户所在组织的辅警分页列表
     * 根据关键字模糊查询当前用户所在组织下的辅警信息（仅包含辅警，不包含民警、安保等其他人员）
     * 支持按姓名、工号、电话号码等字段进行模糊查询
     *
     * @param searchDTO 模糊查询参数DTO
     * @return 辅警分页列表
     */
    CommonPageVO<PoliceListVO> fuzzySearchOrgAuxiliaryPoliceList(PoliceFuzzySearchDTO searchDTO);
}
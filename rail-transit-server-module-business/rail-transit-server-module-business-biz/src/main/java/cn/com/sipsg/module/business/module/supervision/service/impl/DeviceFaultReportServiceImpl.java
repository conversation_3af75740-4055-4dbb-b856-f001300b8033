package cn.com.sipsg.module.business.module.supervision.service.impl;

import cn.com.sipsg.common.security.core.util.SecurityUtils;
import cn.com.sipsg.module.business.module.common.util.UserOrgUtils;
import cn.com.sipsg.module.business.module.supervision.entity.DeviceFaultReport;
import cn.com.sipsg.module.business.module.supervision.mapper.DeviceFaultReportMapper;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.DeviceFaultReportDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.DeviceFaultReportPageDTO;
import cn.com.sipsg.module.business.module.supervision.service.DeviceFaultReportService;
import cn.com.sipsg.module.business.module.duty.entity.BasePoliceEmp;
import cn.com.sipsg.module.business.module.duty.mapper.BasePoliceEmpMapper;
import cn.com.sipsg.module.business.module.duty.entity.BasePoliceEmp;
import cn.com.sipsg.module.business.module.duty.mapper.BasePoliceEmpMapper;

import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 设备故障上报表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DeviceFaultReportServiceImpl extends ServiceImpl<DeviceFaultReportMapper, DeviceFaultReport> implements DeviceFaultReportService {

    private final UserOrgUtils userOrgUtils;
    private final BasePoliceEmpMapper basePoliceEmpMapper;

    @Override
    public Boolean saveDeviceFaultReport(DeviceFaultReportDTO dto) {
        DeviceFaultReport entity;
        String empId = userOrgUtils.getCurrentUserPoliceEmp().getId();
        LocalDateTime now = LocalDateTime.now();
        
        if (StringUtils.isBlank(dto.getId())) {
            // 新增
            entity = new DeviceFaultReport();
            BeanUtils.copyProperties(dto, entity);
            
            // 设置默认维修进度
            if (entity.getRepairProgress() == null) {
                entity.setRepairProgress("pending");
            }

            // 设置上报单位信息
            try {
                String currentOrgId = userOrgUtils.getCurrentUserOrgId();
                String currentOrgName = userOrgUtils.getCurrentUserOrgName();
                entity.setReportOrgId(currentOrgId);
                entity.setReportOrgName(currentOrgName);
            } catch (Exception e) {
                log.error("获取当前用户组织信息失败", e);
            }
            
            // 设置创建人和创建时间
            entity.setCreator(empId);
            entity.setCreateTime(now);
            entity.setUpdater(empId);
            entity.setUpdateTime(now);
            
            return this.save(entity);
        } else {
            // 修改
            entity = this.getById(dto.getId());
            if (entity == null) {
                throw new IllegalArgumentException("设备故障上报不存在");
            }
            
            BeanUtils.copyProperties(dto, entity);
            
            // 设置更新人和更新时间
            entity.setUpdater(empId);
            entity.setUpdateTime(now);
            
            return this.updateById(entity);
        }
    }

    @Override
    public IPage<DeviceFaultReportDTO> getDeviceFaultReportPage(DeviceFaultReportPageDTO pageDTO) {
        // 构建分页对象
        Page<DeviceFaultReport> page = new Page<>(pageDTO.getCurrent(), pageDTO.getSize());
        
        // 构建查询条件
        LambdaQueryWrapper<DeviceFaultReport> queryWrapper = new LambdaQueryWrapper<>();
        
        // 故障名称模糊查询
        if (StringUtils.isNotBlank(pageDTO.getFaultName())) {
            queryWrapper.like(DeviceFaultReport::getFaultName, pageDTO.getFaultName());
        }
        
        // 上报时间范围查询
        if (pageDTO.getReportStartTime() != null) {
            queryWrapper.ge(DeviceFaultReport::getReportTime, pageDTO.getReportStartTime());
        }
        if (pageDTO.getReportEndTime() != null) {
            queryWrapper.le(DeviceFaultReport::getReportTime, pageDTO.getReportEndTime());
        }
        
        // 数据权限控制：根据用户角色决定可查看的数据范围
        if (SecurityUtils.isSuperAdmin()) {
            // 超级管理员可以查看所有数据，不添加组织过滤条件
            log.debug("超级管理员查询设备故障上报，无组织限制");
        } else {
            // 普通用户只能查看当前单位及下级单位的数据
            try {
                List<String> orgIds = userOrgUtils.getCurrentUserAndSubOrgIds();
                if (orgIds != null && !orgIds.isEmpty()) {
                    queryWrapper.in(DeviceFaultReport::getReportOrgId, orgIds);
                    log.debug("普通用户数据权限过滤，可访问组织ID: {}", orgIds);
                } else {
                    // 如果用户没有可访问的组织，返回空结果
                    queryWrapper.eq(DeviceFaultReport::getId, -1);
                    log.warn("普通用户无可访问的组织，返回空结果");
                }
            } catch (Exception e) {
                log.error("获取用户组织权限失败", e);
                // 出现异常时，为安全起见，返回空结果
                queryWrapper.eq(DeviceFaultReport::getId, -1);
            }
        }
        
        // 执行分页查询
        IPage<DeviceFaultReport> entityPage = this.page(page, queryWrapper);
        
        // 转换为DTO分页结果
        Page<DeviceFaultReportDTO> dtoPage = new Page<>(entityPage.getCurrent(), entityPage.getSize(), entityPage.getTotal());
        List<DeviceFaultReportDTO> dtoList = entityPage.getRecords().stream()
                .map(entity -> {
                    DeviceFaultReportDTO dto = new DeviceFaultReportDTO();
                    BeanUtils.copyProperties(entity, dto);
                    // 根据创建人编号查询上报人名称
                    if (StringUtils.isNotBlank(entity.getCreator())) {
                        BasePoliceEmp policeEmp = basePoliceEmpMapper.selectById(entity.getCreator());
                        if (policeEmp != null) {
                            dto.setReporterName(policeEmp.getName());
                        }
                    }
                    return dto;
                })
                .collect(Collectors.toList());
        dtoPage.setRecords(dtoList);
        
        return dtoPage;
    }

    @Override
    public DeviceFaultReportDTO getDeviceFaultReportDetail(String id) {
        DeviceFaultReport entity = this.getById(id);
        if (entity == null) {
            return null;
        }
        
        DeviceFaultReportDTO dto = new DeviceFaultReportDTO();
        BeanUtils.copyProperties(entity, dto);
        
        // 根据创建人编号查询上报人名称
        if (StringUtils.isNotBlank(entity.getCreator())) {
            BasePoliceEmp policeEmp = basePoliceEmpMapper.selectById(entity.getCreator());
            if (policeEmp != null) {
                dto.setReporterName(policeEmp.getName());
            }
        }
        
        return dto;
    }

    @Override
    public Boolean deleteDeviceFaultReport(String id) {
        if (StringUtils.isBlank(id)) {
            throw new IllegalArgumentException("设备故障上报ID不能为空");
        }
        
        DeviceFaultReport entity = this.getById(id);
        if (entity == null) {
            throw new IllegalArgumentException("设备故障上报不存在");
        }
        
        return this.removeById(id);
    }

    @Override
    public Boolean batchDeleteDeviceFaultReport(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new IllegalArgumentException("设备故障上报ID列表不能为空");
        }
        
        // 检查所有记录是否存在
        List<DeviceFaultReport> entities = this.listByIds(ids);
        if (entities.size() != ids.size()) {
            throw new IllegalArgumentException("部分设备故障上报记录不存在");
        }
        
        return this.removeByIds(ids);
    }
}
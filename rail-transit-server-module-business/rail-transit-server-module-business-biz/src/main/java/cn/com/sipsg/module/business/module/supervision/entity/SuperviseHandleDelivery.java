package cn.com.sipsg.module.business.module.supervision.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.sql.Timestamp;

/**
 * 督办信息下发表
 */
@Data
@TableName("tbl_supervise_handle_delivery")
public class SuperviseHandleDelivery {
    @TableId(type = IdType.ASSIGN_ID)
    private String id; // 主键ID

    @TableField("create_time")
    private Timestamp createTime; // 创建时间

    @TableField("update_time")
    private Timestamp updateTime; // 更新时间

    @TableField("create_user_id")
    private String createUserId; // 创建者

    @TableField("update_user_id")
    private String updateUserId; // 更新者

    @TableField("available")
    private Boolean available; // 是否有效

    @TableField("supervision_id")
    private String supervisionId; // 督办表主键

    @TableField("receiver_id")
    private String receiverId; // 接收人主键

    @TableField("receiver_name")
    private String receiverName; // 接收人姓名

    @TableField("receiver_org_id")
    private String receiverOrgId; // 接收人单位主键

    @TableField("receiver_org_name")
    private String receiverOrgName; // 接收人单位名称

    @TableField("handle_content")
    private String handleContent; // 反馈内容

    @TableField("handle_extend_file")
    private String handleExtendFile; // 反馈附件

    @TableField("handle_status")
    private Integer handleStatus; // 处理状态：1 未读，2 已查收，3 已反馈
}
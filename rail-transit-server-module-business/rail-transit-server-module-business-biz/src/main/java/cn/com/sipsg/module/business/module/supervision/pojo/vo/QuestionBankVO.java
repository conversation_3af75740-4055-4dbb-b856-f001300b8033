package cn.com.sipsg.module.business.module.supervision.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 题库视图对象
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
@Schema(description = "题库视图对象")
public class QuestionBankVO {

    private String id;

    @Schema(description = "创建人ID")
    private String createUserId;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新人ID")
    private String updateUserId;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "是否有效")
    private Boolean available;

    @Schema(description = "题库名称")
    private String name;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "状态（启用/禁用）")
    private String status;

    @Schema(description = "状态描述")
    private String statusDesc;

    @Schema(description = "导入的文件URL")
    private String fileUrl;

    @Schema(description = "导入的文件名称")
    private String fileName;

    @Schema(description = "题目列表")
    private List<QuestionVO> questions;

    @Data
    @Schema(description = "题库题目信息")
    public static class QuestionVO {
        @Schema(description = "题目ID")
        private Long id;
        @Schema(description = "题型")
        private String questionType;
        @Schema(description = "题干")
        private String questionStem;
        @Schema(description = "选项（JSON）")
        private String questionOption;
        @Schema(description = "题目序号")
        private Integer serial;
        @Schema(description = "题目分数")
        private Integer score;

        @Schema(description = "正确答案")
        private String correctAnswer;
    }
}
package cn.com.sipsg.module.business.module.supervision.pojo.dto;

import cn.com.sipsg.common.pojo.bo.PageBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 工作提醒查询DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "工作提醒查询DTO")
public class WorkRemindQueryDTO extends PageBO {

    @Schema(description = "标题")
    private String title;

    @Schema(description = "执法单位编号")
    private String orgId;

    @Schema(description = "执法开始时间")
    private LocalDateTime startTime;

    @Schema(description = "执法结束时间")
    private LocalDateTime endTime;
}
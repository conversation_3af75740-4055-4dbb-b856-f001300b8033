package cn.com.sipsg.module.business.module.duty.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> lixt
 * @date : 2025-05-08 11:32:15
 * @Description: 历史排班表
 */
@Data
@TableName("tbl_duty_emp_shift_detail")
public class DutyEmpShiftDetail {

    @TableId(type = IdType.ASSIGN_ID)
    private String id; // 主键

    @TableField("platoon_group_id")
    private String platoonGroupId; // 排班组主键

    @TableField("shifts_id")
    private String shiftsId; // 班次主键

    @TableField("to_date")
    private String toDate; // 排班日期

    @TableField("week")
    private Integer week; // 星期

    @TableField("police_emp_id")
    private String policeEmpId; // 人员ID

    @TableField("police_emp_name")
    private String policeEmpName; // 人员姓名

    @TableField("create_time")
    private LocalDateTime createTime; // 创建时间

    @TableField("create_user_id")
    private String createUserId; // 创建者ID

    @TableField("update_time")
    private LocalDateTime updateTime; // 更新时间

    @TableField("update_user_id")
    private String updateUserId; // 更新者ID

    @TableField("available")
    private Boolean available; // 是否有效

    @TableField("shifts_detail_id")
    private Long shiftsDetailId; // 班次明细ID

    @TableField("shift_id")
    private Long shiftId; // 轮班ID

    @TableField("content")
    private String content; // 值班时间

    @TableField("shift_name")
    private String shiftName; // 轮班名称

    @TableField("platoon_group_name")
    private String platoonGroupName; // 排班组名称
}
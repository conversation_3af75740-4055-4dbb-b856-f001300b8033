package cn.com.sipsg.module.business.module.supervision.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@Schema(description = "考试人员答题记录VO")
public class ExamTaskEmpQuestionRecordVO {
    @Schema(description = "答题记录ID")
    private String id;
    @Schema(description = "考试任务ID")
    private String taskId;
    @Schema(description = "人员ID")
    private String empId;
    @Schema(description = "题目ID")
    private Long questionId;
    @Schema(description = "作答答案")
    private String answer;
    @Schema(description = "是否答对")
    private Boolean answerRight;
    @Schema(description = "题目序号")
    private Integer serial;
    @Schema(description = "批次ID")
    private String taskEmpBatchId;
    @Schema(description = "创建人ID")
    private String createUserId;
    @Schema(description = "创建时间")
    private LocalDateTime createTime;
    @Schema(description = "更新人ID")
    private String updateUserId;
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
    @Schema(description = "是否有效")
    private Boolean available;

    @Schema(description = "题目详情")
    private QuestionDetailVO questionDetail;

    @Data
    @Schema(description = "题目详情VO")
    public static class QuestionDetailVO {
        @Schema(description = "题目类型（单选、多选、判断）")
        private String questionType;
        @Schema(description = "题干")
        private String questionStem;
        @Schema(description = "选项（JSON或List）")
        private Object questionOption;
        @Schema(description = "正确答案")
        private String correctAnswer;
        @Schema(description = "正确答案内容（如B. 4）")
        private String correctAnswerContent;
    }
}
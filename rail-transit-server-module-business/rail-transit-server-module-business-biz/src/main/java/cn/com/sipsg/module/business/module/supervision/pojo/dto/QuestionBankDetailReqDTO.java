package cn.com.sipsg.module.business.module.supervision.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 题库详情请求DTO
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@Schema(description = "题库详情请求DTO")
public class QuestionBankDetailReqDTO {

    @Schema(description = "题库ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotBlank(message = "题库ID不能为空")
    private String questionBankId;

    @Schema(description = "考试任务ID（可选，提供时题目分数根据任务配置设置）", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "1")
    private String taskId;
}
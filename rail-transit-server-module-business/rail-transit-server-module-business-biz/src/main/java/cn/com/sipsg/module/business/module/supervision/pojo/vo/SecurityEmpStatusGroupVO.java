package cn.com.sipsg.module.business.module.supervision.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 安检人员按单位分组统计入职离职情况VO
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "安检人员按单位分组统计入职离职情况VO")
public class SecurityEmpStatusGroupVO {

    @Schema(description = "按单位分组统计列表")
    private List<SecurityEmpStatusGroupItem> groups;

    /**
     * 安检人员单位分组入职离职情况项
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "安检人员单位分组入职离职情况项")
    public static class SecurityEmpStatusGroupItem {

        @Schema(description = "单位名称")
        private String orgName;

        @Schema(description = "入职人数")
        private Integer entryCount;

        @Schema(description = "离职人数")
        private Integer resignationCount;
    }
}

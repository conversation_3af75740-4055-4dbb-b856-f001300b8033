package cn.com.sipsg.module.business.module.supervision.service.impl;

import cn.com.sipsg.module.business.module.common.util.UserOrgUtils;
import cn.com.sipsg.module.business.module.duty.entity.BasePoliceOrg;
import cn.com.sipsg.module.business.module.duty.mapper.BasePoliceOrgMapper;
import cn.com.sipsg.module.business.module.supervision.entity.PerformanceScore;
import cn.com.sipsg.module.business.module.supervision.mapper.PerformanceScoreMapper;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.DepartmentScoreRankVO;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.PerformanceAssessmentSummaryVO;
import cn.com.sipsg.module.business.module.supervision.service.PerformanceScoreService;
import cn.com.sipsg.module.business.module.supervision.enums.CriminalRecordTypeEnum;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.CriminalRecordTypeVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class PerformanceScoreServiceImpl implements PerformanceScoreService {
    private final PerformanceScoreMapper performanceScoreMapper;
    private final BasePoliceOrgMapper basePoliceOrgMapper;
    private final UserOrgUtils userOrgUtils;

    /**
     * 获取当前用户所在组织本月的总评分
     * <p>
     * 本方法首先确定当前用户所属的组织ID，然后根据当前日期计算本月的开始和结束时间
     * 最后，查询数据库中该组织在本月内的所有有效评分记录，并计算总分
     *
     * @return 当前用户所在组织本月的总评分如果当前用户没有所属组织或本月没有评分记录，则返回0.0
     */
    @Override
    public Double getCurrentOrgMonthTotalScore() {
        // 获取当前用户所属的组织ID
        String orgId = userOrgUtils.getCurrentUserOrgId();
        // 如果当前用户没有所属组织，则返回0.0
        if (orgId == null) {
            return 0D;
        }
        // 获取当前日期
        LocalDate now = LocalDate.now();
        // 计算本月开始的时间点（第一天的开始时刻）
        LocalDateTime monthStart = now.withDayOfMonth(1).atStartOfDay();
        // 计算本月结束的时间点（下个月第一天的开始时刻）
        LocalDateTime monthEnd = now.plusMonths(1).withDayOfMonth(1).atStartOfDay();
        // 查询数据库中该组织在本月内的所有有效评分记录，并计算总分
        return performanceScoreMapper.selectList(new LambdaQueryWrapper<PerformanceScore>()
                .eq(PerformanceScore::getOrgId, orgId)
                .eq(PerformanceScore::getAvailable, true)
                .ge(PerformanceScore::getCreateTime, monthStart)
                .lt(PerformanceScore::getCreateTime, monthEnd)
        ).stream().mapToDouble(s -> s.getScore() == null ? 0D : s.getScore()).sum();
    }

    /**
     * 公共方法：根据绩效分数列表统计各org_id的总分并排名，组装VO
     */
    private List<DepartmentScoreRankVO> buildOrgScoreRank(List<PerformanceScore> scoreList) {
        // 根据org_id分组并计算每组的分数总和
        Map<String, Double> scoreMap = scoreList.stream()
                .collect(Collectors.groupingBy(PerformanceScore::getOrgId,
                        Collectors.summingDouble(s -> s.getScore() == null ? 0D : s.getScore())));
        // 提取所有org_id
        List<String> orgIds = new ArrayList<>(scoreMap.keySet());
        // 根据org_id列表查询对应的组织名称
        Map<String, String> orgNameMap = orgIds.isEmpty() ? Collections.emptyMap() :
                basePoliceOrgMapper.selectByIdList(orgIds).stream()
                        .collect(Collectors.toMap(BasePoliceOrg::getId, BasePoliceOrg::getName));
        // 将分数排名按照降序排序
        List<Map.Entry<String, Double>> sorted = scoreMap.entrySet().stream()
                .sorted((a, b) -> Double.compare(b.getValue(), a.getValue()))
                .collect(Collectors.toList());
        // 组装最终的结果列表
        List<DepartmentScoreRankVO> result = new ArrayList<>();
        for (int i = 0; i < sorted.size(); i++) {
            DepartmentScoreRankVO vo = new DepartmentScoreRankVO();
            vo.setDepartmentId(sorted.get(i).getKey());
            // 默认为空字符串，如果找不到对应的组织名称
            vo.setDepartmentName(orgNameMap.getOrDefault(sorted.get(i).getKey(), ""));
            // 排名从1开始
            vo.setRank(i + 1);
            result.add(vo);
        }
        return result;
    }

    /**
     * 获取当前用户组织的部门分数排名
     * <p>
     * 此方法首先确定当前用户所属的组织ID，然后查询该组织内所有有效的绩效分数，
     * 并按部门ID分组计算总分，最后根据总分进行排序并组装成排名视图对象列表
     *
     * @return 返回一个包含部门分数排名的列表，如果当前用户组织ID为空，则返回空列表
     */
    @Override
    public List<DepartmentScoreRankVO> getCurrentOrgDepartmentScoreRank() {
        String orgId = userOrgUtils.getCurrentUserOrgId();
        if (orgId == null) {
            return new ArrayList<>();
        }
        List<PerformanceScore> scoreList = performanceScoreMapper.selectList(
                new LambdaQueryWrapper<PerformanceScore>()
                        .eq(PerformanceScore::getAvailable, true)
                        .eq(PerformanceScore::getOrgId, orgId)
        );
        return buildOrgScoreRank(scoreList);
    }

    /**
     * 获取组织绩效分数排名
     * <p>
     * 此方法用于计算并返回各个部门根据绩效分数的排名情况具体步骤如下：
     * 1. 查询所有有效绩效分数
     * 2. 按部门（org_id）分组统计总分
     * 3. 批量查找所有涉及部门的名称
     * 4. 根据分数排序并组装成目标VO列表
     *
     * @return 绩效分数排名的部门信息列表
     */
    @Override
    public List<DepartmentScoreRankVO> getOrgScoreRank() {
        List<PerformanceScore> scoreList = performanceScoreMapper.selectList(
                new LambdaQueryWrapper<PerformanceScore>()
                        .eq(PerformanceScore::getAvailable, true)
        );
        return buildOrgScoreRank(scoreList);
    }

    /**
     * 获取当前组织的绩效评估摘要信息
     * <p>
     * 此方法汇总了当前组织在本月的绩效评估情况，包括总分、部门排名、任务完成率和出勤率
     * 部门排名通过计算得出，而任务完成率和出勤率目前使用示例数据，未来将替换为真实统计结果
     *
     * @return PerformanceAssessmentSummaryVO对象，包含当前组织的绩效评估摘要信息
     */
    @Override
    public PerformanceAssessmentSummaryVO getCurrentOrgAssessmentSummary() {
        PerformanceAssessmentSummaryVO vo = new PerformanceAssessmentSummaryVO();

        // 本月总分
        Double monthTotalScore = getCurrentOrgMonthTotalScore();
        vo.setMonthTotalScore(monthTotalScore);

        // 部门排名（如3/28）
        List<DepartmentScoreRankVO> rankList = getCurrentOrgDepartmentScoreRank();
        String departmentRank = rankList != null && !rankList.isEmpty() ? String.valueOf(rankList.get(0).getRank()) : "-";
        vo.setDepartmentRank(departmentRank);

        // 任务完成率、出勤率暂用示例数据，后续可补真实统计
        vo.setTaskCompletionRate("100%"); // TODO: 替换为真实统计
        vo.setAttendanceRate("100%"); // TODO: 替换为真实统计

        return vo;
    }

    @Override
    public List<CriminalRecordTypeVO> getAllCriminalRecordTypes() {
        return Arrays.stream(CriminalRecordTypeEnum.values())
                .map(enumValue -> {
                    CriminalRecordTypeVO vo = new CriminalRecordTypeVO();
                    vo.setCode(enumValue.getCode());
                    vo.setName(enumValue.getName());
                    return vo;
                })
                .collect(Collectors.toList());
    }
}
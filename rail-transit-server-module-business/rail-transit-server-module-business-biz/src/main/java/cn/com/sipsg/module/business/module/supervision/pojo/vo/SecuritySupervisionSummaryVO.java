package cn.com.sipsg.module.business.module.supervision.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "安检督导统计汇总VO")
public class SecuritySupervisionSummaryVO {
    @Schema(description = "安检点数量")
    private Integer stationCount;
    @Schema(description = "安检人员数量")
    private Integer personnelCount;
    @Schema(description = "安检次数")
    private Integer checkCount;
    @Schema(description = "出勤率（百分比字符串，如100%）")
    private String attendanceRate;
} 
package cn.com.sipsg.module.business.module.supervision.controller;

import cn.com.sipsg.common.pojo.CommonResult;
import cn.com.sipsg.common.pojo.bo.PageBO;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.security.core.util.SecurityUtils;
import cn.com.sipsg.common.web.core.controller.BaseController;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.*;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.*;
import cn.com.sipsg.module.business.module.supervision.service.PoliticalTrainingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import javax.validation.Valid;
import java.util.List;

/**
 * 政工考核考试培训控制器
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Tag(name = "政工考核考试培训", description = "政工考核考试培训相关接口")
@RestController
@RequestMapping("/political/training")
@RequiredArgsConstructor
public class PoliticalTrainingController extends BaseController {
    private final PoliticalTrainingService politicalTrainingService;

    /**
     * 待办任务查询
     * 查询当前用户关联的考试任务，且当前时间在考试结束时间之前的
     *
     * @param pageBO 分页参数
     * @return 分页后的待办考试任务VO列表
     */
    @Operation(summary = "待办任务查询", description = "查询当前用户关联的考试任务，且当前时间在考试结束时间之前的分页列表")
    @GetMapping("/pending-tasks")
    public CommonResult<CommonPageVO<ExamTaskVO>> getPendingExamTasks(PageBO pageBO) {
        return handle(() -> CommonResult.data(politicalTrainingService.getPendingExamTasksByCurrentUserWithPage(pageBO)));
    }

    /**
     * 最新待办任务查询
     * 查询当前用户关联的考试任务，且当前时间在考试结束时间之前的
     * 按截止时间降序排序，只返回最新的两条数据
     *
     * @return 最新的两条待办考试任务VO列表
     */
    @Operation(summary = "最新待办任务查询", description = "查询当前用户关联的考试任务，按截止时间降序排序，只返回最新的两条数据")
    @GetMapping("/latest-tasks")
    public CommonResult<List<ExamTaskVO>> getLatestPendingExamTasks() {
        return handle(() -> CommonResult.data(politicalTrainingService.getLatestTwoPendingExamTasksByCurrentUser()));
    }

    /**
     * 考试成绩分析
     * 按月统计当前用户的考试成绩，如果一个月内有多个考试成绩，取平均值
     *
     * @return 按月统计的考试成绩分析VO列表
     */
    @Operation(summary = "考试成绩分析", description = "按月统计当前用户的考试成绩，如果一个月内有多个考试成绩，取平均值")
    @GetMapping("/score-analysis")
    public CommonResult<List<ExamScoreAnalysisVO>> getExamScoreAnalysis() {
        return handle(() -> CommonResult.data(politicalTrainingService.getExamScoreAnalysisByMonth()));
    }

    /**
     * 用户考试列表（支持名称模糊和状态过滤，按截止时间倒序）
     *
     * @param reqDTO 包含分页参数、考试任务名称和考试状态的请求DTO
     * @return 分页后的考试任务VO列表
     */
    @Operation(summary = "用户考试列表", description = "查询当前用户的所有考试，支持名称模糊和状态过滤，按截止时间倒序")
    @PostMapping("/user-exams")
    public CommonResult<CommonPageVO<ExamTaskVO>> getUserExams(@Valid @RequestBody ExamTaskUserPageReqDTO reqDTO) {
        return handle(() -> CommonResult.data(
                politicalTrainingService.searchUserExamTasksWithPage(reqDTO, reqDTO.getTaskName(), reqDTO.getStatus())
        ));
    }

    /**
     * 新增或更新考试任务
     * 创建或更新考试任务，包含考试基本信息、时间设置、题目配置等
     *
     * @param reqDTO 考试任务请求DTO
     * @return 新增或更新的考试任务ID
     */
    @Operation(summary = "新增或更新考试任务", description = "创建或更新考试任务，包含考试基本信息、时间设置、题目配置等")
    @PostMapping("/exam-task")
    public CommonResult<String> createOrUpdateExamTask(@Valid @RequestBody ExamTaskCreateReqDTO reqDTO) {
        return handle(() -> CommonResult.data(politicalTrainingService.createOrUpdateExamTask(reqDTO)));
    }

    /**
     * 题库分页列表
     * 支持题库名称模糊查询和状态过滤
     *
     * @param reqDTO 题库查询参数，包含分页信息和过滤条件
     * @return 分页后的题库VO列表
     */
    @Operation(summary = "题库分页列表", description = "查询题库分页列表，支持题库名称模糊查询和状态过滤")
    @PostMapping("/question-banks")
    public CommonResult<CommonPageVO<QuestionBankVO>> getQuestionBankList(@Valid @RequestBody QuestionBankPageReqDTO reqDTO) {
        // 仅管理员可见
        if (!SecurityUtils.isSuperAdmin()) {
            return CommonResult.fail("无权限访问，仅管理员可见");
        }
        return handle(() -> CommonResult.data(politicalTrainingService.getQuestionBankListWithPage(reqDTO)));
    }

    /**
     * 题库详情
     * 根据题库ID查询题库详细信息，支持根据考试任务ID获取正确的题目分数
     * @param reqDTO 题库详情请求DTO
     * @return 题库VO
     */
    @Operation(summary = "题库详情", description = "根据题库ID查询题库详细信息，支持根据考试任务ID获取正确的题目分数")
    @PostMapping("/question-bank/detail")
    public CommonResult<QuestionBankVO> getQuestionBankDetail(@Valid @RequestBody QuestionBankDetailReqDTO reqDTO) {
        if (reqDTO.getTaskId() != null && !reqDTO.getTaskId().trim().isEmpty()) {
            return handle(() -> CommonResult.data(politicalTrainingService.getQuestionBankDetailByTask(reqDTO.getTaskId())));
        } else {
            return handle(() -> CommonResult.data(politicalTrainingService.getQuestionBankDetail(reqDTO.getQuestionBankId())));
        }
    }

    /**
     * 根据考试任务获取题库详情
     * 题目分数根据考试任务配置设置，而不是使用默认分数
     * @param taskId 考试任务ID
     * @return 题库VO
     */
    @Operation(summary = "根据考试任务获取题库详情", description = "题目分数根据考试任务配置设置，而不是使用默认分数")
    @GetMapping("/question-bank/detail-by-task")
    public CommonResult<QuestionBankVO> getQuestionBankDetailByTask(@RequestParam("taskId") String taskId) {
        return handle(() -> CommonResult.data(politicalTrainingService.getQuestionBankDetailByTask(taskId)));
    }

    /**
     * 题库上传/修改
     * 通过Excel文件批量导入题目，支持新建和修改题库（传questionBankId为修改，不传为新增）
     *
     * @param file 上传的Excel文件
     * @param name 题库名称
     * @param description 题库描述
     * @param questionBankId 题库ID（可选，传则为修改）
     * @return 导入成功的题目数量或操作结果
     */
    @Operation(summary = "题库上传/修改", description = "通过Excel文件批量导入题目，支持新建和修改题库（传questionBankId为修改，不传为新增）")
    @PostMapping(value = "/question-bank/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public CommonResult<?> uploadOrUpdateQuestionBank(
            @Parameter(description = "上传的Excel文件", required = false)
            @RequestParam(value = "file", required = false) MultipartFile file,
            @Parameter(description = "题库名称", required = true)
            @RequestParam("name") String name,
            @Parameter(description = "题库描述")
            @RequestParam(value = "description", required = false) String description,
            @Parameter(description = "题库ID（可选，传则为修改，不传为新增）")
            @RequestParam(value = "questionBankId", required = false) String questionBankId) {
        if (questionBankId == null || questionBankId.trim().isEmpty()) {
            // 新增，必须有文件和名称
            if (file == null || file.isEmpty()) {
                return CommonResult.fail("新增题库必须上传文件");
            }
            return CommonResult.data(politicalTrainingService.uploadQuestionBank(file, name, description));
        } else {
            // 修改
            if (file != null && !file.isEmpty()) {
                // 文件+名称/描述一起改
                return CommonResult.data(politicalTrainingService.uploadQuestionBank(file, questionBankId, name, description));
            } else {
                // 只改名称/描述
                QuestionBankUpdateReqDTO reqDTO = new QuestionBankUpdateReqDTO();
                reqDTO.setQuestionBankId(questionBankId);
                reqDTO.setName(name);
                reqDTO.setDescription(description);
                return CommonResult.success(politicalTrainingService.updateQuestionBank(reqDTO) ? "修改成功" : "修改失败");
            }
        }
    }

    /**
     * 题库启用/停用
     * 将指定题库状态设置为启用或禁用
     *
     * @param reqDTO 请求DTO，包含题库ID和启用标志
     * @return 操作结果
     */
    @Operation(summary = "题库启用/停用", description = "将指定题库状态设置为启用或禁用")
    @PostMapping("/question-bank/enable")
    public CommonResult<Boolean> enableOrDisableQuestionBank(@RequestBody QuestionBankEnableReqDTO reqDTO) {
        return handle(() -> CommonResult.data(politicalTrainingService.enableOrDisableQuestionBank(reqDTO.getQuestionBankId(), reqDTO.getEnable())));
    }

    /**
     * 考试任务详情
     * 考试任务详情（带人员分页）
     *
     * @param reqDTO 考试任务详情请求DTO
     * @return 考试任务详情VO列表（每个VO包含一个人员信息）
     */
    @Operation(summary = "考试任务详情", description = "考试任务详情（带人员分页），返回人员列表")
    @PostMapping("/exam/task/detail")
    public CommonResult<CommonPageVO<ExamTaskDetailVO>> getExamTaskDetail(@RequestBody ExamTaskDetailReqDTO reqDTO) {
        return handle(() -> CommonResult.data(
                politicalTrainingService.getExamTaskDetailWithPersonPage(reqDTO)
        ));
    }

    /**
     * 考试任务编辑详情
     * 获取考试任务用于编辑的全部信息
     *
     * @param reqDTO 考试任务ID请求DTO
     * @return 编辑详情VO
     */
    @Operation(summary = "考试任务编辑详情", description = "获取考试任务用于编辑的全部信息")
    @PostMapping("/exam/task/edit-detail")
    public CommonResult<ExamTaskEditDetailVO> getExamTaskEditDetail(@RequestBody ExamTaskIdDTO reqDTO) {
        return handle(() -> CommonResult.data(politicalTrainingService.getExamTaskEditDetail(reqDTO.getTaskId())));
    }

    /**
     * 获取考试任务的分页列表
     *
     * @param reqDTO 考试任务分页请求DTO，包含分页信息、状态筛选和名称模糊匹配参数
     * @return 返回一个CommonResult对象，其中包含分页的考试任务信息
     */
    @Operation(summary = "考试任务分页列表", description = "考试任务分页列表，支持状态筛选和名称模糊")
    @PostMapping("/exam-task/page")
    public CommonResult<CommonPageVO<ExamTaskPageVO>> pageExamTasks(@RequestBody ExamTaskPageReqDTO reqDTO) {
        // 调用service层的分页查询方法，并使用CommonResult.data静态方法封装查询结果
        return handle(() -> CommonResult.data(politicalTrainingService.pageExamTasks(reqDTO)));
    }

    /**
     * 我的考试详情
     *
     * @param reqDTO 请求参数（taskId）
     * @return 个人考试详情VO
     */
    @Operation(summary = "我的考试详情", description = "我的考试详情，查最近一次考试记录")
    @PostMapping("/user-exam/detail")
    public CommonResult<MyExamDetailVO> getMyExamDetail(@RequestBody MyExamDetailReqDTO reqDTO) {
        return handle(() -> CommonResult.data(politicalTrainingService.getMyExamDetail(reqDTO)));
    }

    /**
     * 分页查询考试人员答题记录
     *
     * @param reqDTO 分页请求DTO
     * @return 分页VO
     */
    @Operation(summary = "考试人员答题记录分页", description = "分页查询考试人员答题记录")
    @PostMapping("/exam/task/record/page")
    public CommonResult<CommonPageVO<ExamTaskEmpQuestionRecordVO>> pageExamTaskEmpQuestionRecords(
            @RequestBody ExamTaskEmpQuestionRecordPageReqDTO reqDTO) {
        return handle(() -> CommonResult.data(
                politicalTrainingService.pageExamTaskEmpQuestionRecords(reqDTO)
        ));
    }

    /**
     * 答题记录上传
     * 前端答题完成后上传答题记录
     *
     * @param record 答题记录
     * @return 是否成功
     */
    @Operation(summary = "答题记录上传", description = "前端答题完成后上传答题记录")
    @PostMapping("/exam/task/record/upload")
    public CommonResult<Boolean> uploadExamTaskEmpQuestionRecord(@RequestBody ExamTaskEmpQuestionUploadDTO record) {
        return handle(() -> CommonResult.data(politicalTrainingService.uploadExamTaskEmpQuestionRecord(record)));
    }

    /**
     * 查询部门警员树形结构
     * 以部门为分组查询在职警员信息，返回部门-警员树形结构
     * 管理员可以查看全部警员，普通用户只能查看当前单位及下级单位的警员
     *
     * @return 部门-警员树形结构列表
     */
    @Operation(summary = "查询部门警员树形结构", description = "以部门为分组查询在职警员信息，返回部门-警员树形结构，支持管理员和普通用户权限控制")
    @GetMapping("/police/tree/list")
    public CommonResult<List<DeptPoliceTreeVO>> getActivePoliceTreeList() {
        return handle(() -> CommonResult.data(politicalTrainingService.getActivePoliceTreeList()));
    }

    /**
     * 题库编辑详情
     * 获取题库用于编辑的详情信息，便于前端页面反显和数据回填
     * @param questionBankId 题库ID
     * @return 题库编辑详情VO
     */
    @Operation(summary = "题库编辑详情", description = "获取题库用于编辑的详情信息")
    @GetMapping("/question-bank/edit-detail")
    public CommonResult<QuestionBankEditDetailVO> getQuestionBankEditDetail(@RequestParam("questionBankId") String questionBankId) {
        return handle(() -> CommonResult.data(politicalTrainingService.getQuestionBankEditDetail(questionBankId)));
    }
}
package cn.com.sipsg.module.business.module.supervision.controller;

import cn.com.sipsg.common.pojo.CommonResult;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.web.core.controller.BaseController;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.*;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.*;
import cn.com.sipsg.module.business.module.supervision.service.SecuritySupervisionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

/**
 * 安检督导控制器
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
@Tag(name = "安检督导", description = "安检督导相关接口")
@RestController
@RequestMapping("/security/supervision")
@RequiredArgsConstructor
public class SecuritySupervisionController extends BaseController {

    private final SecuritySupervisionService securitySupervisionService;

    /**
     * 安检督导统计汇总
     * 统计当前用户组织下安检点、安检人员、安检次数、出勤率
     *
     * @return 安检督导统计汇总VO
     */
    @Operation(summary = "安检督导统计汇总", description = "统计当前用户组织下安检点、安检人员、安检次数、出勤率")
    @GetMapping("/supervision/summary")
    public CommonResult<SecuritySupervisionSummaryVO> getSecuritySupervisionSummary() {
        return handle(() -> CommonResult.data(securitySupervisionService.getSecuritySupervisionSummary()));
    }

    /**
     * 在职安检员分组统计
     * 根据查询日期统计在职安检员总数，并按单位名称分组统计数量和占比
     *
     * @param queryDate 查询日期，格式为yyyy-MM-dd，如果为空则使用当前日期
     * @return 安检人员按单位分组统计结果
     */
    @Operation(summary = "在职安检员单位分组统计", description = "根据查询日期统计在职安检员总数，并按单位名称分组统计数量和占比")
    @GetMapping("/personnel/group-by-org")
    public CommonResult<SecurityEmpGroupVO> getSecurityEmpGroupByOrgName(
            @Parameter(description = "查询日期，格式为yyyy-MM-dd，如果为空则使用当前日期")
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate queryDate) {
        return handle(() -> CommonResult.data(securitySupervisionService.getSecurityEmpGroupByOrgName(queryDate)));
    }

    /**
     * 在职安检员年龄分组统计
     * 根据查询日期统计在职安检员总数，并按年龄分组统计数量和占比
     *
     * @param queryDate 查询日期，格式为yyyy-MM-dd，如果为空则使用当前日期
     * @return 安检人员按年龄分组统计结果
     */
    @Operation(summary = "在职安检员年龄分组统计", description = "根据查询日期统计在职安检员总数，并按年龄分组统计数量和占比")
    @GetMapping("/personnel/group-by-age")
    public CommonResult<SecurityEmpAgeGroupVO> getSecurityEmpGroupByAge(
            @Parameter(description = "查询日期，格式为yyyy-MM-dd，如果为空则使用当前日期")
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate queryDate) {
        return handle(() -> CommonResult.data(securitySupervisionService.getSecurityEmpGroupByAge(queryDate)));
    }

    /**
     * 在职安检员保安证持证情况统计
     * 根据查询日期统计在职安检员总数，并按单位名称分组统计保安证持证情况
     *
     * @param queryDate 查询日期，格式为yyyy-MM-dd，如果为空则使用当前日期
     * @return 安检人员按单位分组统计保安证持证情况结果
     */
    @Operation(summary = "在职安检员保安证持证情况统计", description = "根据查询日期统计在职安检员总数，并按单位名称分组统计保安证持证情况")
    @GetMapping("/personnel/cert-by-org")
    public CommonResult<SecurityEmpCertGroupVO> getSecurityEmpCertGroupByOrg(
            @Parameter(description = "查询日期，格式为yyyy-MM-dd，如果为空则使用当前日期")
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate queryDate) {
        return handle(() -> CommonResult.data(securitySupervisionService.getSecurityEmpCertGroupByOrg(queryDate)));
    }

    /**
     * 安检员入职离职情况统计
     * 根据查询日期统计安检员总数，并按单位名称分组统计入职和离职数量
     *
     * @param queryDate 查询日期，格式为yyyy-MM-dd，如果为空则使用当前日期
     * @return 安检人员按单位分组统计入职离职情况结果
     */
    @Operation(summary = "安检员入职离职情况统计", description = "根据查询日期统计安检员总数，并按单位名称分组统计入职和离职数量")
    @GetMapping("/personnel/status-by-org")
    public CommonResult<SecurityEmpStatusGroupVO> getSecurityEmpStatusGroupByOrg(
            @Parameter(description = "查询日期，格式为yyyy-MM-dd，如果为空则使用当前日期")
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate queryDate) {
        return handle(() -> CommonResult.data(securitySupervisionService.getSecurityEmpStatusGroupByOrg(queryDate)));
    }

    /**
     * 安检点位分页查询列表（支持条件和无条件）
     * 分页查询安检点位信息，支持按名称、线路、站点、公司名称等条件筛选，无条件时查全部
     *
     * @param reqDTO 分页查询参数
     * @return 安检点位分页查询结果
     */
    @Operation(summary = "安检点位分页查询列表", description = "分页查询安检点位信息，支持按名称、线路、站点、公司名称等条件筛选，无条件时查全部")
    @PostMapping("/stations/page")
    public CommonResult<CommonPageVO<SecurityStationPageVO>> getSecurityStationPage(@RequestBody SecurityStationPageReqDTO reqDTO) {
        return handle(() -> CommonResult.data(securitySupervisionService.getSecurityStationPage(reqDTO)));
    }

  /**
   * 安检点管理-新增/修改
   * 创建或修改安检点位信息，包含基本信息和设备数量
   *
   * @param reqDTO 安检点位创建/修改请求
   * @return 创建或修改后的安检点位ID
   */
    @Operation(summary = "安检点管理-新增/修改", description = "创建或修改安检点位信息，包含基本信息和设备数量")    
    @PostMapping("/stations/create")
    public CommonResult<String> createSecurityStation(@RequestBody SecurityStationCreateReqDTO reqDTO) {
        return handle(() -> CommonResult.data(securitySupervisionService.createSecurityStation(reqDTO)));
    }

    /**
     * 安检员分页列表（支持条件和无条件）
     * 分页查询安检员信息，支持按姓名、身份证号、联系电话、单位名称等条件筛选
     *
     * @param reqDTO 分页查询参数，可为空
     * @return 安检员分页查询结果
     */
    @Operation(summary = "安检员分页列表", description = "分页查询安检员信息，支持按姓名、身份证号、联系电话、单位名称等条件筛选")
    @PostMapping("/personnel/page")
    public CommonResult<CommonPageVO<SecurityEmpPageVO>> getSecurityEmpPage(@RequestBody(required = false) SecurityEmpPageReqDTO reqDTO) {
        final SecurityEmpPageReqDTO finalReqDTO = (reqDTO == null) ? new SecurityEmpPageReqDTO() : reqDTO;
        return handle(() -> CommonResult.data(securitySupervisionService.getSecurityEmpPage(finalReqDTO)));
    }

    /**
     * 安检员管理-新增/修改
     * 新增或修改安检员，前端传图片URL。ID为空时新增，ID不为空时修改
     * @param reqDTO 安检员表单数据
     * @return 安检员ID
     */
    @Operation(summary = "安检员管理-新增/修改", description = "新增或修改安检员，前端传图片URL。ID为空时新增，ID不为空时修改")
    @PostMapping("/personnel/create")
    public CommonResult<String> createSecurityEmp(@RequestBody SecurityEmpCreateReqDTO reqDTO) {
        return handle(() -> CommonResult.data(securitySupervisionService.createSecurityEmp(reqDTO)));
    }

    /**
     * 获取安检员档案详情
     * 根据安检员ID获取安检员档案详细信息
     *
     * @param reqDTO 请求参数，包含安检员ID
     * @return 安检员档案详情
     */
    @Operation(summary = "安检员档案详情", description = "根据安检员ID获取安检员档案详细信息")
    @PostMapping("/personnel/profile")
    public CommonResult<SecurityEmpProfileVO> getSecurityEmpProfile(@RequestBody SecurityEmpProfileReqDTO reqDTO) {
        return handle(() -> CommonResult.data(securitySupervisionService.getSecurityEmpProfile(reqDTO.getId())));
    }

    /**
     * 查询所有地铁线路及其下属站点（只含编号和名称）
     */
    @Operation(summary = "查询所有地铁线路及其站点", description = "返回所有地铁线路及其下属站点（只含编号和名称）")
    @GetMapping("/subway/lines-stations")
    public CommonResult<List<SubwayLineSimpleVO>> getAllLinesAndStations() {
        return handle(() -> CommonResult.data(securitySupervisionService.getAllLinesAndStations()));
    }

    /**
     * 获取安检点位所属公司
     */
    @Operation(summary = "获取安检点位所属公司", description = "返回所有安检点位的公司id和名称（去重）")
    @GetMapping("/stations/company-list")
    public CommonResult<List<CompanyOptionVO>> getSecurityStationCompanyList() {
        return handle(() -> CommonResult.data(securitySupervisionService.getAllSecurityStationCompanies()));
    }

    /**
     * 获取管辖单位
     */
    @Operation(summary = "获取管辖单位", description = "返回管辖单位id和名称")
    @GetMapping("/stations/org-list")
    public CommonResult<List<OrgOptionVO>> getOrgOptionsForCurrentUser() {
        return handle(() -> CommonResult.data(securitySupervisionService.getOrgOptionsForCurrentUser()));
    }

    /**
     * 安检点管理-删除（逻辑删除）
     * 将指定ID的安检点available字段置为false
     *
     * @param reqDTO 删除请求参数，包含安检点ID
     * @return 是否删除成功
     */
    @Operation(summary = "安检点管理-删除", description = "将指定ID的安检点available字段置为false")
    @PostMapping("/stations/delete")
    public CommonResult<Boolean> deleteSecurityStation(@RequestBody SecurityStationIDReqDTO reqDTO) {
        return handle(() -> CommonResult.data(securitySupervisionService.deleteSecurityStation(reqDTO.getId())));
    }

    /**
     * 查看安检点详情
     * @param reqDTO 安检点ID
     * @return 安检点详情
     */
    @Operation(summary = "安检点详情", description = "根据ID查看安检点详情")
    @PostMapping("/stations/detail")
    public CommonResult<SecurityStationDetailVO> getSecurityStationDetail(@RequestBody SecurityStationIDReqDTO reqDTO) {
        return handle(() -> CommonResult.data(securitySupervisionService.getSecurityStationDetail(reqDTO.getId())));
    }

    /**
     * 安检员逻辑删除
     * @param reqDTO 删除请求参数，包含安检员ID
     * @return 是否删除成功
     */
    @Operation(summary = "安检员逻辑删除", description = "将指定ID的安检员available字段置为false")
    @PostMapping("/personnel/delete")
    public CommonResult<Boolean> deleteSecurityEmp(@RequestBody SecurityEmpProfileReqDTO reqDTO) {
        return handle(() -> CommonResult.data(securitySupervisionService.deleteSecurityEmp(reqDTO.getId())));
    }
}
package cn.com.sipsg.module.business.module.supervision.validation;

import cn.com.sipsg.module.business.module.supervision.enums.UrgencyLevelEnum;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * 紧急程度验证器
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
public class UrgencyLevelValidator implements ConstraintValidator<ValidUrgencyLevel, String> {

    @Override
    public void initialize(ValidUrgencyLevel constraintAnnotation) {
        // 初始化方法，可以为空
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (value == null || value.trim().isEmpty()) {
            return false;
        }
        
        // 检查是否是有效的紧急程度枚举值
        return UrgencyLevelEnum.getByCode(value) != null;
    }
}
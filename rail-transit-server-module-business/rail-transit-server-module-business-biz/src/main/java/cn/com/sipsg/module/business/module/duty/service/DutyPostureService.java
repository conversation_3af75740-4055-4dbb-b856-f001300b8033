package cn.com.sipsg.module.business.module.duty.service;


import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.module.business.module.duty.pojo.dto.*;
import cn.com.sipsg.module.business.module.duty.pojo.vo.*;
import cn.com.sipsg.module.business.module.duty.pojo.vo.PoliceBasicInfoVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR> lixt
 * @date : 2025-04-24 13:32:33
 * @Description:
 */
public interface DutyPostureService {
    /**
     * 获取员工数量信息
     *
     * @return EmpCountVO 员工数量信息对象
     */
    EmpCountVO empCount();

    /**
     * 获取下属单位数量信息
     *
     * @return SubordinateUnitVO 下属单位数量信息对象
     */
    SubordinateUnitVO unitCount();

    /**
     * 获取勤务统计信息（合并警力数量和下辖单位统计）
     *
     * @return DutyStatisticsVO 勤务统计信息对象
     */
    DutyStatisticsVO getDutyStatistics();

    /**
     * 导出警察列表
     *
     * @param policeList 要导出的警察列表，包含多个警察信息对象
     */
    Boolean exportPolice(List<PoliceListVO> policeList);

    /**
     * 搜索警察列表
     * <p>
     * 本方法通过接收查询条件对象，执行警察列表的搜索操作，并返回分页的警察列表信息
     *
     * @param query 查询条件对象，包含分页信息和搜索警察列表的条件
     * @return 返回一个通用的分页视图对象，其中包含符合查询条件的警察列表
     */
    CommonPageVO<PoliceListVO> searchPoliceList(PoliceListQueryDTO query);

    /**
     * 导入警察值班数据
     *
     * @param file 包含警察值班信息的文件
     * @return 导入的DutyPoliceImportDTO对象列表
     */
    List<DutyPoliceImportDTO> importPolice(MultipartFile file);

    /**
     * 查询警察机构列表
     *
     * @param query 查询条件对象，包含分页信息、关键字和部门名称等查询条件
     * @return 返回一个包含警察机构列表的CommonPageVO对象
     */
    CommonPageVO<PoliceOrgListVO> policeOrgList(PoliceOrgListQueryDTO query);

    /**
     * 导出一所一档信息
     *
     * @param orgList 部门列表
     * @return 是否成功
     */
    boolean exportPoliceOrg(List<PoliceOrgListVO> orgList);

    /**
     * 删除一所一档（组织）
     *
     * @param id 组织ID
     * @return 是否成功
     */
    boolean deletePoliceOrg(String id);

    /**
     * 统计各单位在岗警力（民警、辅警）数量
     * @return 统计结果
     */
    EmpCountVO getOnDutyPoliceStat();
    
    /**
     * 获取职级字典列表
     * 
     * @return 职级字典列表
     */
    List<BaseDictVO> getRankLevelDict();
    
    /**
     * 获取警员档案详情
     * 
     * @param dto 查询条件
     * @return 警员档案详情
     */
    PoliceProfileDTO getPoliceProfile(PoliceProfileReqDTO dto);
    
    /**
     * 保存警员档案信息
     * 
     * @param profileDTO 警员档案信息
     * @return 保存后的警员ID
     */
    Boolean savePoliceProfile(PoliceProfileDTO profileDTO);
    
    /**
     * 新建/编辑警务组织（部门）
     * 
     * @param createDTO 警务组织创建/编辑数据传输对象（当id为空时新建，不为空时编辑）
     * @return 警务组织ID
     */
    String createPoliceOrg(PoliceOrgCreateDTO createDTO);

    /**
     * 获取部门详情
     *
     * @param id 部门ID
     * @return 部门详情
     */
    PoliceOrgCreateDTO getPoliceOrgDetail(String id);

    /**
     * 统计各组织各谈话类型数量
     * 支持数据权限功能：管理员可查看全部数据，普通用户只能查看当前单位及下级单位的数据
     * @param queryDTO 查询条件，包含年份和查询类型
     * @return 统计结果
     */
    List<OrgTalkingTypeStatVO> statOrgTalkingType(TalkingTypeStatQueryDTO queryDTO);

    /**
     * 统计各组织各谈话类型数量
     * @param year 年份，可选
     * @param quarter 季度，可选（1-4）
     * @param month 月份，可选（1-12）
     * @return 统计结果
     */
    List<OrgTalkingTypeStatVO> statOrgTalkingType(Integer year, Integer quarter, Integer month);

    /**
     * 队伍风险隐患统计
     * 支持按年份查询，支持按年、季度、月统计，支持数据权限功能
     * 
     * @param queryDTO 查询条件
     * @return 统计结果，按单位、谈话类别分组统计，分页返回
     */
    List<TeamRiskStatVO> getTeamRiskStat(TeamRiskStatQueryDTO queryDTO);
    
    /**
     * 查询所有警员基本信息
     * 
     * @return 警员基本信息列表（警员id、警员code、警员名称）
     */
    List<PoliceBasicInfoVO> getAllPoliceBasicInfo();
}

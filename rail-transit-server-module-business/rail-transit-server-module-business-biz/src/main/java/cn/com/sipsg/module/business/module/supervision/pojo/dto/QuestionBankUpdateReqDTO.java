package cn.com.sipsg.module.business.module.supervision.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

@Data
public class QuestionBankUpdateReqDTO {
    @Schema(description = "题库编号", required = true)
    private String questionBankId;

    @Schema(description = "题库名称", required = true)
    private String name;

    @Schema(description = "题库描述")
    private String description;

    @Schema(description = "可选新文件（如需替换）")
    private MultipartFile file;
} 
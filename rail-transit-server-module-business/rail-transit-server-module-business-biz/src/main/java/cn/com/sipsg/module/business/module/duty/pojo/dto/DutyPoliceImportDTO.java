package cn.com.sipsg.module.business.module.duty.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 警员信息导入DTO
 *
 * <AUTHOR> Trae.Liu
 * @date : 2024-01-08
 */
@Data
@Schema(description = "警员信息导入DTO")
public class DutyPoliceImportDTO {

    @Schema(description = "姓名", example = "张三")
    private String name;

    @Schema(description = "类型", example = "民警")
    private String type;

    @Schema(description = "编号", example = "EMP001")
    private String code;

    @Schema(description = "身份证号", example = "320123199001011234")
    private String idCard;

    @Schema(description = "手机", example = "***********")
    private String mobile;

    @Schema(description = "工作单位", example = "地铁公安分局")
    private String workUnit;

    @Schema(description = "职务", example = "巡逻员")
    private String post;

    @Schema(description = "状态", example = "在岗")
    private String status;

    @Schema(description = "性别", example = "男")
    private String gender;

    @Schema(description = "民族", example = "汉族")
    private String nation;

    @Schema(description = "警用手机", example = "13987654321")
    private String policePhone;

    @Schema(description = "警衔", example = "一级警司")
    private String policeRank;

    @Schema(description = "职级", example = "正科")
    private String rankLevel;

    @Schema(description = "详细地址", example = "南京市玄武区XX路88号")
    private String addressDetail;

    @Schema(description = "标签", example = "党员,巡逻岗")
    private String label;

    @Schema(description = "学历", example = "本科")
    private String education;

    @Schema(description = "政治面貌", example = "党员")
    private String politicalStatus;
}
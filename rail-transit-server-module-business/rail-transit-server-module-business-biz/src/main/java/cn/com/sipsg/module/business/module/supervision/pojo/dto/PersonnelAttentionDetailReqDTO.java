package cn.com.sipsg.module.business.module.supervision.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 关注记录详情查询请求DTO
 */
@Data
@Schema(description = "关注记录详情查询请求DTO")
public class PersonnelAttentionDetailReqDTO {

    @Schema(description = "关注记录ID", required = true, example = "1234567890abcdef12345678")
    @NotBlank(message = "关注记录ID不能为空")
    private String id;
}
package cn.com.sipsg.module.business.module.supervision.controller;

import cn.com.sipsg.common.web.core.controller.BaseController;
import cn.com.sipsg.common.pojo.CommonResult;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.IntegratedJointDutySummaryVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.com.sipsg.module.business.module.supervision.service.IntegratedJointDutyService;

/**
 * 地上地下一体化联勤控制器
 * 
 * 典型场景：地铁地上地下一体化联勤相关业务接口
 *
 * <AUTHOR>
 * @since 2024-05-27
 */
@Tag(name = "地上地下一体化联勤", description = "地上地下一体化联勤相关接口")
@RestController
@RequestMapping("/integrated/joint-duty")
@RequiredArgsConstructor
public class IntegratedJointDutyController extends BaseController {

    @Autowired
    private IntegratedJointDutyService integratedJointDutyService;

    /**
     * 地上地下一体化联勤统计汇总
     * 典型场景：统计地面警员数量、地铁警员数、警车数、摩托车数
     *
     * @return 联勤统计汇总VO
     */
    @Operation(summary = "地上地下一体化联勤统计汇总", description = "统计地面警员数量、地铁警员数、警车数、摩托车数")
    @GetMapping("/summary")
    public CommonResult<IntegratedJointDutySummaryVO> getIntegratedJointDutySummary() {
        return handle(() -> CommonResult.data(integratedJointDutyService.getSummary()));
    }
} 
package cn.com.sipsg.module.business.module.duty.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "盘查统计结果VO")
public class PatrolStatVO {

    @Schema(description = "盘查统计明细列表")
    private List<OrgPatrolStatItem> items;
    
    @Schema(description = "盘查总数")
    private Long totalCount;

    @Schema(description = "参与盘查民警数")
    private Long policeCount;

    @Schema(description = "人均盘查量")
    private Double avgCount;
    
    @Data
    @Schema(description = "单位盘查统计项")
    public static class OrgPatrolStatItem {
        @Schema(description = "单位编号")
        private String orgCode;
        
        @Schema(description = "单位名称")
        private String orgName;
        
        @Schema(description = "盘查总数")
        private Long totalCount;
        
        @Schema(description = "参与盘查民警数")
        private Long policeCount;
        
        @Schema(description = "人均盘查量")
        private Double avgCount;
    }
}

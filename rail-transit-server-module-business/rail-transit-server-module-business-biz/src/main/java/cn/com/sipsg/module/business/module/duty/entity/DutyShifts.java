package cn.com.sipsg.module.business.module.duty.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> lixt
 * @date : 2025-05-08 11:18:41
 * @Description: 排班规则表
 */
@Data
@TableName("tbl_duty_shifts")
public class DutyShifts {

    @TableId(type = IdType.ASSIGN_ID)
    private String id; // 主键

    @TableField("name")
    private String name; // 班次名称

    @TableField("type")
    private Integer type; // 班次类型

    @TableField("platoon_group_id")
    private String platoonGroupId; // 排班组主键

    @TableField("effective_date")
    private String effectiveDate; // 生效日期

    @TableField("create_time")
    private LocalDateTime createTime; // 创建时间

    @TableField("create_user_id")
    private String createUserId; // 创建者ID

    @TableField("update_time")
    private LocalDateTime updateTime; // 更新时间

    @TableField("update_user_id")
    private String updateUserId; // 更新者ID

    @TableField("available")
    private Boolean available; // 是否有效
}

package cn.com.sipsg.module.business.module.supervision.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 后续处理枚举
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
@Getter
@AllArgsConstructor
public enum SubsequentProcessingEnum {
    ADMINISTRATIVE_DETENTION("1", "行政拘留"),
    CRIMINAL_DETENTION("2", "刑事拘留"),
    BAIL("3", "取保候审"),
    SUSPICION_EXCLUDED("4", "排除嫌疑"),
    RELEASE("5", "放行"),
    NO_PUNISHMENT("6", "不予处罚"),
    OTHER("99", "其他");

    private final String code;
    private final String name;

    public static SubsequentProcessingEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (SubsequentProcessingEnum status : values()) {
            if (code.equals(status.getCode())) {
                return status;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        SubsequentProcessingEnum status = getByCode(code);
        return status != null ? status.getName() : "未知状态";
    }
}

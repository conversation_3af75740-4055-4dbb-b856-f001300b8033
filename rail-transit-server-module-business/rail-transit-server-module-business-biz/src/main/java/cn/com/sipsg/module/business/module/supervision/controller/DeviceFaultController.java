package cn.com.sipsg.module.business.module.supervision.controller;

import cn.com.sipsg.common.pojo.CommonResult;
import cn.com.sipsg.common.web.core.controller.BaseController;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.BatchDeleteDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.DeviceFaultReportDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.DeviceFaultReportPageDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.IdDTO;
import cn.com.sipsg.module.business.module.supervision.service.DeviceFaultReportService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 设备故障上报控制器
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/deviceFault")
@Tag(name = "设备故障上报", description = "设备故障上报相关接口")
public class DeviceFaultController extends BaseController {

    private final DeviceFaultReportService deviceFaultReportService;

    @PostMapping("/save")
    @Operation(summary = "保存设备故障上报", description = "新增或修改设备故障上报信息，根据ID是否为空判断新增还是修改")
    public CommonResult<Boolean> saveDeviceFaultReport(@Valid @RequestBody DeviceFaultReportDTO dto) {
        return handle(() -> CommonResult.data(deviceFaultReportService.saveDeviceFaultReport(dto)));
    }

    @PostMapping("/page")
    @Operation(summary = "设备故障上报分页列表", description = "获取设备故障上报分页列表，支持按故障名称和上报时间查询，支持数据权限")
    public CommonResult<IPage<DeviceFaultReportDTO>> getDeviceFaultReportPage(@Valid @RequestBody DeviceFaultReportPageDTO pageDTO) {
        return handle(() -> CommonResult.data(deviceFaultReportService.getDeviceFaultReportPage(pageDTO)));
    }

    @PostMapping("/detail")
    @Operation(summary = "获取设备故障上报详情", description = "根据ID获取设备故障上报详情")
    public CommonResult<DeviceFaultReportDTO> getDeviceFaultReportDetail(@RequestBody @Parameter(description = "设备故障上报ID") IdDTO dto) {
        return handle(() -> CommonResult.data(deviceFaultReportService.getDeviceFaultReportDetail(dto.getId())));
    }

    @PostMapping("/delete")
    @Operation(summary = "删除设备故障上报", description = "根据ID删除设备故障上报")
    public CommonResult<Boolean> deleteDeviceFaultReport(@RequestBody @Parameter(description = "设备故障上报ID") IdDTO dto) {
        return handle(() -> CommonResult.data(deviceFaultReportService.deleteDeviceFaultReport(dto.getId())));
    }

    @PostMapping("/batchDelete")
    @Operation(summary = "批量删除设备故障上报", description = "根据ID列表批量删除设备故障上报")
    public CommonResult<Boolean> batchDeleteDeviceFaultReport(@Valid @RequestBody BatchDeleteDTO dto) {
        return handle(() -> CommonResult.data(deviceFaultReportService.batchDeleteDeviceFaultReport(dto.getIds())));
    }
}

package cn.com.sipsg.module.business.module.supervision.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import cn.com.sipsg.common.pojo.bo.PageBO;

/**
 * 安检点位分页查询请求DTO
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "安检点位分页查询请求DTO")
public class SecurityStationPageReqDTO extends PageBO {

    @Schema(description = "安检点位名称")
    private String name;

    @Schema(description = "线路 ID")
    private String subwayId;

    @Schema(description = "站点 ID")
    private String subwayStationId;

    @Schema(description = "所属公司")
    private String companyName;
    
    @Schema(description = "管辖单位 ID")
    private String precinctId;
}

package cn.com.sipsg.module.business.module.supervision.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 每日报备详情请求DTO
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
@Schema(description = "每日报备详情请求DTO")
public class DailyReportDetailReqDTO {

    @Schema(description = "报备ID", example = "1")
    @NotBlank(message = "报备ID不能为空")
    private String id;
}
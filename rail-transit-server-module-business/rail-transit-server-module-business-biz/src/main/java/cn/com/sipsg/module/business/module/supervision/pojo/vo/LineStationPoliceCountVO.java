package cn.com.sipsg.module.business.module.supervision.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "线路民警统计VO")
public class LineStationPoliceCountVO {
    @Schema(description = "线路ID")
    private String subwayId;

    @Schema(description = "线路名称")
    private String subwayName;

    @Schema(description = "该线路在岗民警总数")
    private Integer policeCount;
} 
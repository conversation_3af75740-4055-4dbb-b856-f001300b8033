package cn.com.sipsg.module.business.module.duty.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.List;

/**
 * <AUTHOR> lixt
 * @date : 2025-04-27 13:46:54
 * @Description: 警力数量 VO
 */
@Data
@Schema(description = "警力数量统计VO", example = "{\"count\":10,\"pcCount\":6,\"scCount\":4,\"orgList\":[]}")
public class EmpCountVO {
    @Schema(description = "总警力数量", example = "10")
    private Long count;

    @Schema(description = "民警数量", example = "6")
    private Long pcCount;

    @Schema(description = "辅警数量", example = "4")
    private Long scCount;

    @Schema(description = "各组织分组统计")
    private List<OrgEmpCountVO> orgList;
}

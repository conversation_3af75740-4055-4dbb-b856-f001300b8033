<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.sipsg.module.business.module.duty.mapper.PoliceListMapper">

    <select id="selectPoliceList" resultType="cn.com.sipsg.module.business.module.duty.pojo.vo.PoliceListVO">
        SELECT 
            t.id AS id,
            t.name,
            t.type,
            t.code,
            t.phone,
            t.police_org_id AS workUnit,
            t.duties AS duty,
            t.resignation_time AS resignationTime,
            CASE WHEN t.auth_user_id IS NOT NULL THEN TRUE ELSE FALSE END AS isSystemUser
        FROM tbl_base_police_emp t
        WHERE t.available = TRUE
        <if test="orgIds != null and orgIds.size > 0">
            AND t.police_org_id IN
            <foreach collection="orgIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="type != null">
            AND t.type = #{type}
        </if>
        <if test="keyword != null and keyword != ''">
            AND (
                t.name LIKE CONCAT('%', #{keyword}, '%')
                OR t.code LIKE CONCAT('%', #{keyword}, '%')
                OR t.phone LIKE CONCAT('%', #{keyword}, '%')
            )
        </if>
        <if test="duty != null and duty != ''">
            AND t.duties = #{duty}
        </if>
        <if test="workUnitList != null and workUnitList.size > 0">
            AND t.police_org_id IN
            <foreach collection="workUnitList" item="unit" open="(" separator="," close=")">
                #{unit}
            </foreach>
        </if>
        ORDER BY t.create_time DESC
        LIMIT #{pageSize} OFFSET #{offset}
    </select>

    <select id="countPoliceList" resultType="int">
        SELECT COUNT(1)
        FROM tbl_base_police_emp t
        WHERE t.available = TRUE
        <if test="orgIds != null and orgIds.size > 0">
            AND t.police_org_id IN
            <foreach collection="orgIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="type != null">
            AND t.type = #{type}
        </if>
        <if test="keyword != null and keyword != ''">
            AND (
                t.name LIKE CONCAT('%', #{keyword}, '%')
                OR t.code LIKE CONCAT('%', #{keyword}, '%')
                OR t.phone LIKE CONCAT('%', #{keyword}, '%')
            )
        </if>
        <if test="duty != null and duty != ''">
            AND t.duties = #{duty}
        </if>
        <if test="workUnitList != null and workUnitList.size > 0">
            AND t.police_org_id IN
            <foreach collection="workUnitList" item="unit" open="(" separator="," close=")">
                #{unit}
            </foreach>
        </if>
    </select>

</mapper>
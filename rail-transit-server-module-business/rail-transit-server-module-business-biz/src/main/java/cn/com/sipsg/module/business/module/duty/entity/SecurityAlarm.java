package cn.com.sipsg.module.business.module.duty.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> lixt
 * @date : 2025-05-07 14:03:40
 * @Description: 安检-违禁品预警表
 */
@Data
@TableName("tbl_security_alarm")
public class SecurityAlarm {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;  // id

    @TableField("create_user_id")
    private String createUserId;  // 创建人

    @TableField("create_time")
    private LocalDateTime createTime;  // 创建时间

    @TableField("update_user_id")
    private String updateUserId;  // 更新人

    @TableField("update_time")
    private LocalDateTime updateTime;  // 更新时间

    @TableField("available")
    private Boolean available;  // 是否有效

    @TableField("line_id")
    private String lineId;  // 线路id

    @TableField("station_id")
    private String stationId;  // 站点id

    @TableField("entrance_id")
    private String entranceId;  // 出入口

    @TableField("alarm_time")
    private LocalDateTime alarmTime;  // 预警时间

    @TableField("handle_status")
    private String handleStatus;  // 违禁品处置状态，默认未处置2

    @TableField("handle_type")
    private String handleType;  // 处置结果类型

    @TableField("handle_time")
    private LocalDateTime handleTime;  // 处置时间

    @TableField("device_id")
    private String deviceId;  // 设备id

    @TableField("device_name")
    private String deviceName;  // 设备名称

    @TableField("relation_id")
    private String relationId;  // 第三方数据id

    @TableField("data_source")
    private String dataSource;  // 数据来源

    @TableField("line_name")
    private String lineName;  // 线路名称

    @TableField("station_name")
    private String stationName;  // 站点名称

    @TableField("entrance_name")
    private String entranceName;  // 出入口名称

    @TableField("check_emp_name")
    private String checkEmpName;  // 安检员名称

    @TableField("get_data_method")
    private String getDataMethod;  // 获取数据的方式

    @TableField("handle_voice")
    private String handleVoice;  // 安检处置语音

    @TableField("approval_user_id")
    private String approvalUserId;  // 审批人userId

    @TableField("approval_status")
    private Integer approvalStatus;  // 1-审批中，2-审批不通过，3-审批通过

    @TableField("false_positive_report_emp_id")
    private Long falsePositiveReportEmpId;  // 误报/取消误报 人员主键

    @TableField("false_positive_report_time")
    private LocalDateTime falsePositiveReportTime;  // 误报/取消误报 操作时间
}

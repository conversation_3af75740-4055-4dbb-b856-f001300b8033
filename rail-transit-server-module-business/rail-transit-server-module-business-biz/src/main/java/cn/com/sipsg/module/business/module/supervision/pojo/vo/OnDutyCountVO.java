package cn.com.sipsg.module.business.module.supervision.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;

/**
 * 当天值班民警数量视图对象
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Data
@Schema(description = "当天值班民警数量视图对象")
public class OnDutyCountVO {

    @Schema(description = "值班民警总数")
    private Long count;

    @Schema(description = "统计日期")
    private LocalDate date;
}

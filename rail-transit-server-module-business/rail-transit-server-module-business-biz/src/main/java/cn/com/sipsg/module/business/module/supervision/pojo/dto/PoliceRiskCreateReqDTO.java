package cn.com.sipsg.module.business.module.supervision.pojo.dto;

import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "隐患上报请求参数")
public class PoliceRiskCreateReqDTO {
    @Schema(description = "隐患ID，新增时传空，修改时传id")
    private String id;

    @Schema(description = "检查场所（站点编号）")
    private String stationId;

    @Schema(description = "检查内容")
    private String content;

    @Schema(description = "隐患类型，1-设备设施类隐患，2-人员行为类隐患，3-环境及外部风险类隐患，4-管理及制度类隐患，5-专项排查类隐患。\n可选值：\n1-设备设施类隐患\n2-人员行为类隐患\n3-环境及外部风险类隐患\n4-管理及制度类隐患\n5-专项排查类隐患")
    private String type;

    @Schema(description = "隐患描述")
    private String description;

    @Schema(description = "现场图片url（json字符串，前端直接传json数组字符串，后端不做解析）")
    private String riskUrl;

    @Schema(description = "整改后图片url（json字符串，前端直接传json数组字符串，后端不做解析）")
    private String changeUrl;

    @Schema(description = "现场整改，0-否 1-是")
    private Integer hasChange;

    @Schema(description = "检查类目，1-人防，2-物防，3-技防，4-通防")
    private String category;

    @Schema(description = "整改开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime rectifyStartTime;

    @Schema(description = "整改结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime rectifyEndTime;
}
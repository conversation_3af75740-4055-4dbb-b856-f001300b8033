package cn.com.sipsg.module.business.module.supervision.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "公司下拉选项VO")
public class CompanyOptionVO {
    @Schema(description = "公司ID")
    private String companyId;
    @Schema(description = "公司名称")
    private String companyName;
} 
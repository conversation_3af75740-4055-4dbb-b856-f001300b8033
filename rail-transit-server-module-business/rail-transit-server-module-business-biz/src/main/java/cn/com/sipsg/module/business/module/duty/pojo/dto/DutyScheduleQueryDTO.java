package cn.com.sipsg.module.business.module.duty.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotNull;

import java.time.LocalDate;

/**
 * <AUTHOR> lixt
 * @date : 2025-05-15
 * @Description: 排班查询DTO
 */
@Data
@Schema(description = "排班查询DTO")
public class DutyScheduleQueryDTO {
    @Schema(description = "单位编号", example = "1001")
    @NotNull(message = "单位编号(orgId)不能为空")
    private String orgId;
    
    @Schema(description = "开始日期", example = "2025-05-02")
    @NotNull(message = "开始日期(startDate)不能为空")
    private LocalDate startDate;
    
    @Schema(description = "结束日期", example = "2025-05-12")
    @NotNull(message = "结束日期(endDate)不能为空")
    private LocalDate endDate;
    
    @Schema(description = "岗位类别ID", example = "1")
    @NotNull(message = "岗位类别(postCategoryId)不能为空")
    private String postCategoryId;
}
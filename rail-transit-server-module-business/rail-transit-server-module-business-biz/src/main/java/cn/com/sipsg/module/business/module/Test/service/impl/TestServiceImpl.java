package cn.com.sipsg.module.business.module.Test.service.impl;

import cn.com.sipsg.common.exception.BusinessException;
import cn.com.sipsg.common.exception.enums.ErrorCodeEnum;
import cn.com.sipsg.module.business.module.Test.mapper.TestMapper;
import cn.com.sipsg.module.business.module.Test.service.TestService;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 测试
 */
@Slf4j
@Service
public class TestServiceImpl implements TestService {
    @Resource
    private TestMapper testMapper;

    /**
     * 测试分页
     *
     * @param pageNumber 页码
     * @param pageSize   条数
     * @return 分页数据
     */
    @Override
    public PageInfo<JSONObject> page(Integer pageNumber, Integer pageSize) {
        if (null == pageNumber || null == pageSize) {
            throw new BusinessException(ErrorCodeEnum.BAD_REQUEST);
        }
        PageHelper.startPage(pageNumber, pageSize);
        List<JSONObject> list = testMapper.page();
        PageInfo<JSONObject> pageInfo = new PageInfo<>(list);
        pageInfo.setList(list);
        return pageInfo;
    }
}

package cn.com.sipsg.module.business.module.duty.enums;

public enum TalkingTypeEnum {
    VIOLATE_DISCIPLINE("违反政治纪律和政治规矩"),
    MAJOR_EVENT_NOT_REPORT("重大事项不请示不报告"),
    LAW_ENFORCEMENT_IRREGULARITY("执法不作为慢作为乱作为"),
    ILLEGAL_FINANCIAL_ACTIVITY("参与非法集资借贷或从事其他营利性经营活动"),
    VIOLATE_ALCOHOL_MANAGEMENT("违反'八严禁酒驾网火'管理规定"),
    LONG_TERM_ABSENCE("长期请病假或经常性请事假等在职不在岗"),
    MENTAL_ABNORMALITY("思想过激、情绪不稳、精神抑郁，存在实施个人极端行为倾向"),
    OTHER("其他影响队伍稳定的风险隐患");

    private final String label;

    TalkingTypeEnum(String label) {
        this.label = label;
    }

    public String getLabel() {
        return label;
    }
} 
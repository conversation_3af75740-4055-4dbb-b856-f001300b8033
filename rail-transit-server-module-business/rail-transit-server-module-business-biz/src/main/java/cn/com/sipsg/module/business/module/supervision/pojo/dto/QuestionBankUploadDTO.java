package cn.com.sipsg.module.business.module.supervision.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 题库上传数据传输对象
 * 
 * Excel模板列顺序（从第0列开始）：
 * 第0列：题型（支持数字代码：1-单选题，2-多选题，3-判断题；也支持中文：单选题、多选题、判断题）
 * 第1列：题干
 * 第2列：答案
 * 第3列：选项A
 * 第4列：选项B
 * 第5列：选项C
 * 第6列：选项D
 * 第7列：选项E
 * 第8列：选项F
 * 第9列：选项G
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@Schema(description = "题库上传数据传输对象")
public class QuestionBankUploadDTO {

    /**
     * 题型
     * 支持数字代码：1-单选题，2-多选题，3-判断题
     * 也支持中文：单选题、多选题、判断题
     */
    @Schema(description = "题型：支持数字代码（1-单选题，2-多选题，3-判断题）或中文（单选题、多选题、判断题）")
    @ExcelProperty(index = 0)
    private String questionType;

    /**
     * 题干
     */
    @Schema(description = "题干")
    @ExcelProperty(index = 1)
    private String questionContent;

    /**
     * 正确答案
     */
    @Schema(description = "答案")
    @ExcelProperty(index = 2)
    private String correctAnswer;

    /**
     * 选项A
     */
    @Schema(description = "选项A")
    @ExcelProperty(index = 3)
    private String optionA;

    /**
     * 选项B
     */
    @Schema(description = "选项B")
    @ExcelProperty(index = 4)
    private String optionB;

    /**
     * 选项C
     */
    @Schema(description = "选项C")
    @ExcelProperty(index = 5)
    private String optionC;

    /**
     * 选项D
     */
    @Schema(description = "选项D")
    @ExcelProperty(index = 6)
    private String optionD;

    /**
     * 选项E
     */
    @Schema(description = "选项E")
    @ExcelProperty(index = 7)
    private String optionE;

    /**
     * 选项F
     */
    @Schema(description = "选项F")
    @ExcelProperty(index = 8)
    private String optionF;

    /**
     * 选项G
     */
    @Schema(description = "选项G")
    @ExcelProperty(index = 9)
    private String optionG;
}
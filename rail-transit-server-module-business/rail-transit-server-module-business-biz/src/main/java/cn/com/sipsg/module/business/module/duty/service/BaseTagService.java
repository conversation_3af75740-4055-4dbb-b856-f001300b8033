package cn.com.sipsg.module.business.module.duty.service;

import cn.com.sipsg.module.business.module.duty.pojo.dto.TagAddDTO;
import cn.com.sipsg.module.business.module.duty.pojo.dto.TagDeleteDTO;
import cn.com.sipsg.module.business.module.duty.pojo.dto.TagGroupAddDTO;
import cn.com.sipsg.module.business.module.duty.pojo.dto.TagGroupBatchUpdateDTO;
import cn.com.sipsg.module.business.module.duty.pojo.dto.TagGroupDeleteDTO;
import cn.com.sipsg.module.business.module.duty.pojo.dto.TagGroupUpdateDTO;
import cn.com.sipsg.module.business.module.duty.pojo.vo.TagGroupVO;

import java.util.List;

/**
 * <AUTHOR> lixt
 * @date : 2025-04-30 17:06:34
 * @Description:
 */
public interface BaseTagService {
    List<TagGroupVO> tagGroup();

    void addTagGroup(TagGroupAddDTO dto);

    void addTag(TagAddDTO dto);

    /**
     * 修改标签的分组
     * @param tagId 标签ID
     * @param groupId 新分组ID
     */
    void updateTagGroup(TagGroupUpdateDTO dto);

    /**
     * 批量修改标签的分组
     * @param tagGroupMappings 标签与分组的映射关系列表
     */
    void batchUpdateTagGroup(List<TagGroupBatchUpdateDTO.TagGroupMapping> tagGroupMappings);

    /**
     * 删除标签
     * @param tagId 标签ID
     */
    void deleteTag(TagDeleteDTO dto);

    /**
     * 删除标签分组
     * @param groupId 分组ID
     */
    void deleteTagGroup(TagGroupDeleteDTO dto);
}

package cn.com.sipsg.module.business.module.duty.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 民族枚举
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Getter
@AllArgsConstructor
public enum NationalityEnum {

    HAN("01", "汉族"),
    <PERSON><PERSON>("02", "回族"),
    MA<PERSON>("03", "满族"),
    ZHUANG("04", "壮族"),
    UNKNOWN("99", "未知");

    private final String code;
    private final String desc;

    public static NationalityEnum getByCode(String code) {
        if (code == null || code.isEmpty()) {
            return UNKNOWN;
        }
        for (NationalityEnum nationality : values()) {
            if (nationality.getCode().equals(code)) {
                return nationality;
            }
        }
        return UNKNOWN;
    }

    public static NationalityEnum getByDesc(String desc) {
        if (desc == null || desc.isEmpty()) {
            return UNKNOWN;
        }
        for (NationalityEnum nationality : values()) {
            if (nationality.getDesc().equals(desc)) {
                return nationality;
            }
        }
        return UNKNOWN;
    }
}
package cn.com.sipsg.module.business.module.supervision.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@TableName("tbl_secret_investigation_task")
@Schema(description = "暗访检查任务")
public class SecretInvestigationTask {
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键ID")
    private String id;

    @TableField("create_user_id")
    @Schema(description = "创建者")
    private String createUserId;

    @TableField("create_time")
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @TableField("update_user_id")
    @Schema(description = "更新者")
    private String updateUserId;

    @TableField("update_time")
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @TableField("available")
    @Schema(description = "是否有效")
    private Boolean available;

    @TableField("plan_id")
    @Schema(description = "计划主键")
    private String planId;

    @TableField("task_order")
    @Schema(description = "任务重复次数序列 从1开始")
    private Integer taskOrder;

    @TableField("receive_org_id")
    @Schema(description = "任务接收单位主键")
    private String receiveOrgId;

    @TableField("investigated_org_id")
    @Schema(description = "被检查单位主键")
    private String investigatedOrgId;

    @TableField("start_day")
    @Schema(description = "开始日期")
    private String startDay;

    @TableField("end_day")
    @Schema(description = "结束日期")
    private String endDay;

    @TableField("task_status")
    @Schema(description = "任务状态 1:待认领 2:待完成 3:已完成 4:未完成")
    private Integer taskStatus;

    @TableField("handle_emp_id")
    @Schema(description = "处理人主键")
    private String handleEmpId;

    @TableField("required_count")
    @Schema(description = "任务要求次数")
    private Integer requiredCount;
} 
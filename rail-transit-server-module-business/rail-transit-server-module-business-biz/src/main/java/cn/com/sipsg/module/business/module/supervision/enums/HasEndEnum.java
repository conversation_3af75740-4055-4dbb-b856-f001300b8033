package cn.com.sipsg.module.business.module.supervision.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum HasEndEnum {
    NO(0, "未结束"),
    YES(1, "结束");

    private final int code;
    private final String desc;

    public static HasEndEnum fromCode(Integer code) {
        if (code == null) return null;
        for (HasEndEnum value : values()) {
            if (value.code == code) return value;
        }
        return null;
    }
} 
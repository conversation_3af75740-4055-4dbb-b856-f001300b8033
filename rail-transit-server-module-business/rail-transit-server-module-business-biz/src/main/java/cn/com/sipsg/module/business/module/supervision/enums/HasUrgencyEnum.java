package cn.com.sipsg.module.business.module.supervision.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum HasUrgencyEnum {
    NO(0, "否"),
    YES(1, "是");

    private final int code;
    private final String desc;

    public static HasUrgencyEnum fromCode(Integer code) {
        if (code == null) return null;
        for (HasUrgencyEnum value : values()) {
            if (value.code == code) return value;
        }
        return null;
    }
} 
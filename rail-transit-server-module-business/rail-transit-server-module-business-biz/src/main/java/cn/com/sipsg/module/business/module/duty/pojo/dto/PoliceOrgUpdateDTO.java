package cn.com.sipsg.module.business.module.duty.pojo.dto;

import lombok.Data;

import javax.validation.constraints.*;

/**
 * 警务组织更新DTO
 * 用于接收前端编辑警务组织（部门）的请求数据
 */
@Data
public class PoliceOrgUpdateDTO {
    
    /**
     * 组织ID
     */
    @NotNull(message = "部门ID不能为空")
    private String id;
    
    /**
     * 部门名称
     */
    @NotBlank(message = "部门名称不能为空")
    @Size(max = 100, message = "部门名称长度不能超过100个字符")
    private String name;
    
    /**
     * 部门编号
     */
    @NotBlank(message = "部门编号不能为空")
    @Size(max = 50, message = "部门编号长度不能超过50个字符")
    @Pattern(regexp = "^[a-zA-Z0-9_-]+$", message = "部门编号只能包含字母、数字、下划线和连字符")
    private String code;
    
    /**
     * 组织类型
     */
    @Min(value = 0, message = "组织类型不能小于0")
    private Integer type;
    
    /**
     * 上级部门ID
     */
    private String parentId;
    
    /**
     * 部门联系方式
     */
    @Pattern(regexp = "^[0-9\\-+()]{0,20}$", message = "电话号码格式不正确")
    private String phone;
}

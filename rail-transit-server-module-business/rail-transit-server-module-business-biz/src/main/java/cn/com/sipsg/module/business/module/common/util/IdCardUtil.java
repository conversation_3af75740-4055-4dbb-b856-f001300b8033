package cn.com.sipsg.module.business.module.common.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.util.regex.Pattern;

public class IdCardUtil {

    /**
     * 校验身份证号码是否合法（支持15位和18位）
     * @param idCard 身份证号码
     * @return true 合法，false 不合法
     */
    public static boolean isValidIdCard(String idCard) {
        if (idCard == null) {
            return false;
        }

        // 去除空格
        idCard = idCard.trim();

        // 15位身份证正则
        String regex15 = "^[1-9]\\d{7}(\\d{2})(\\d{2})(\\d{2})\\d{3}$";
        // 18位身份证正则
        String regex18 = "^[1-9]\\d{5}(19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}[0-9Xx]$";

        if (idCard.length() == 15 && Pattern.matches(regex15, idCard)) {
            return isValidDate("19" + idCard.substring(6, 12));
        } else if (idCard.length() == 18 && Pattern.matches(regex18, idCard)) {
            return isValidDate(idCard.substring(6, 14)) && checkIdCardChecksum(idCard);
        }

        return false;
    }

    /**
     * 校验日期是否合法
     * @param dateStr 日期字符串，格式：yyyyMMdd
     * @return true 合法，false 不合法
     */
    private static boolean isValidDate(String dateStr) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        sdf.setLenient(false);
        try {
            sdf.parse(dateStr);
            return true;
        } catch (ParseException e) {
            return false;
        }
    }

    /**
     * 校验18位身份证号码的最后一位校验码是否正确
     * @param idCard 18位身份证号码
     * @return true 校验码正确，false 错误
     */
    private static boolean checkIdCardChecksum(String idCard) {
        // 系数
        int[] weight = {7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2};
        // 校验位对应值
        char[] checkDigit = {'1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'};

        int sum = 0;
        for (int i = 0; i < 17; i++) {
            int digit = Character.getNumericValue(idCard.charAt(i));
            sum += digit * weight[i];
        }
        int mod = sum % 11;
        char expectedCheckDigit = checkDigit[mod];
        char actualCheckDigit = Character.toUpperCase(idCard.charAt(17));
        return actualCheckDigit == expectedCheckDigit;
    }

        /**
     * 根据身份证号码计算年龄（支持15位和18位身份证）
     * @param idCard 身份证号
     * @return 年龄，如果身份证无效返回 -1
     */
    public static int getAgeFromIdCard(String idCard) {
        if (!isValidIdCard(idCard)) {
            return -1;
        }

        String birthDateStr;
        if (idCard.length() == 18) {
            birthDateStr = idCard.substring(6, 14); // yyyyMMdd
        } else if (idCard.length() == 15) {
            birthDateStr = "19" + idCard.substring(6, 12); // 15位身份证出生日期补19
        } else {
            return -1;
        }

        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            LocalDate birthDate = LocalDate.parse(birthDateStr, formatter);
            LocalDate today = LocalDate.now();
            return Period.between(birthDate, today).getYears();
        } catch (Exception e) {
            return -1; // 日期格式解析失败
        }
    }

        /**
     * 根据身份证号码提取出生日期（返回 LocalDate 类型）
     * @param idCard 身份证号码
     * @return 出生日期 LocalDate，如身份证无效返回 null
     */
    public static LocalDate getBirthDateFromIdCard(String idCard) {
        if (!isValidIdCard(idCard)) {
            return null;
        }

        String birthStr;
        if (idCard.length() == 18) {
            birthStr = idCard.substring(6, 14); // yyyyMMdd
        } else if (idCard.length() == 15) {
            birthStr = "19" + idCard.substring(6, 12); // 15位身份证补上世纪前缀
        } else {
            return null;
        }

        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            return LocalDate.parse(birthStr, formatter);
        } catch (Exception e) {
            return null;
        }
    }
}
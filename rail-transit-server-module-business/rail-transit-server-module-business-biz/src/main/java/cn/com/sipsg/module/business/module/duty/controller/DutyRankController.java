package cn.com.sipsg.module.business.module.duty.controller;

import cn.com.sipsg.common.pojo.CommonResult;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.web.core.controller.BaseController;
import cn.com.sipsg.module.business.module.duty.pojo.dto.PatrolListQueryDTO;
import cn.com.sipsg.module.business.module.duty.pojo.dto.RankQueryDTO;
import cn.com.sipsg.module.business.module.duty.pojo.vo.PatrolRecordVO;
import cn.com.sipsg.module.business.module.duty.pojo.vo.RankResultVO;

import cn.com.sipsg.module.business.module.duty.pojo.dto.PatrolStatQueryDTO;
import cn.com.sipsg.module.business.module.duty.pojo.vo.PatrolStatVO;
import cn.com.sipsg.module.business.module.duty.service.DutyRankService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;


import java.util.List;

/**
 * <AUTHOR> lixt
 * @date : 2025-05-07 10:15:00
 * @Description: 勤务排名相关接口
 */
@Tag(name = "勤务排名")
@RestController
@RequestMapping("/business/duty/rank")
@Slf4j
@RequiredArgsConstructor
public class DutyRankController extends BaseController {

    private final DutyRankService dutyRankService;

    /**
     * 统计派出所盘查勤务排名
     *
     * @param queryDTO 查询条件，包含统计类型和业务类型
     * @return 排名列表
     */
    @Operation(summary = "统计派出所勤务排名")
    @PostMapping("/patrol/rank")
    public CommonResult<List<RankResultVO>> getPatrolRank(@RequestBody RankQueryDTO queryDTO) {
        return handle(() -> CommonResult.data(dutyRankService.getPatrolRank(queryDTO.getStatType(), queryDTO.getBizType())));
    }

    /**
     * 统计个人勤务排名
     *
     * @param queryDTO 查询条件，包含统计类型和业务类型
     * @return 排名列表
     */
    @Operation(summary = "统计个人勤务排名")
    @PostMapping("/personal/rank")
    public CommonResult<List<RankResultVO>> getPersonalRank(@RequestBody RankQueryDTO queryDTO) {
        return handle(() -> CommonResult.data(dutyRankService.getPersonalRank(queryDTO.getStatType(), queryDTO.getBizType())));
    }

    /**
     * 分页查询盘查列表
     *
     * @param queryDTO 查询条件，包含盘查编号、民警名称、盘查时间、盘查单位等
     * @return 分页结果
     */
    @Operation(summary = "分页查询盘查列表")
    @PostMapping("/patrol/list")
    public CommonResult<CommonPageVO<PatrolRecordVO>> getPatrolList(@RequestBody PatrolListQueryDTO queryDTO) {
        return handle(() -> CommonResult.data(dutyRankService.getPatrolList(queryDTO)));
    }

    /**
     * 统计时间范围内的盘查数据
     *
     * @param queryDTO 查询条件，包含开始时间、结束时间
     * @return 统计结果
     */
    @Operation(summary = "统计盘查数据")
    @PostMapping("/patrol/stats")
    public CommonResult<PatrolStatVO> getPatrolStats(@RequestBody PatrolStatQueryDTO queryDTO) {
        return handle(() -> CommonResult.data(dutyRankService.getPatrolStats(queryDTO)));
    }
}
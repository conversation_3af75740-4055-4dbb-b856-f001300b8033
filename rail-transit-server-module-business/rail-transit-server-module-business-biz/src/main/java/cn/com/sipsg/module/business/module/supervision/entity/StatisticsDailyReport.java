package cn.com.sipsg.module.business.module.supervision.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 综合统计-每日报备表
 */
@Data
@TableName("tbl_statistics_daily_report")
@Schema(description = "综合统计-每日报备表")
public class StatisticsDailyReport {
    
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键ID")
    private String id;
    
    @TableField("create_user_id")
    @Schema(description = "创建人ID")
    private String createUserId;
    
    @TableField("create_time")
    @Schema(description = "创建时间")
    private LocalDateTime createTime;
    
    @TableField("update_user_id")
    @Schema(description = "修改人ID")
    private String updateUserId;
    
    @TableField("update_time")
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;
    
    @TableField("available")
    @Schema(description = "是否有效")
    private Boolean available;
    
    @TableField("form_time")
    @Schema(description = "填报时间")
    private LocalDateTime formTime;
    
    @TableField("police_org_id")
    @Schema(description = "单位ID")
    private String policeOrgId;
    
    @TableField("responsible_name")
    @Schema(description = "负责人姓名")
    private String responsibleName;
    
    @TableField("responsible_position")
    @Schema(description = "负责人岗位")
    private String responsiblePosition;
    
    @TableField("responsible_phone")
    @Schema(description = "负责人电话")
    private String responsiblePhone;
    
    @TableField("responsible_channel")
    @Schema(description = "负责人值守频道")
    private String responsibleChannel;
    
    @TableField("strength_police")
    @Schema(description = "警力-民警数量")
    private Integer strengthPolice;
    
    @TableField("strength_auxiliary_police")
    @Schema(description = "警力-辅警数量")
    private Integer strengthAuxiliaryPolice;
    
    @TableField("strength_armed_police")
    @Schema(description = "警力-武警数量")
    private Integer strengthArmedPolice;
    
    @TableField("strength_other")
    @Schema(description = "警力-其他数量")
    private Integer strengthOther;
    
    @TableField("equipment_gun")
    @Schema(description = "装备-枪支数量")
    private Integer equipmentGun;
    
    @TableField("equipment_gun_msg")
    @Schema(description = "装备-枪支情况")
    private String equipmentGunMsg;
    
    @TableField("equipment_gps_msg")
    @Schema(description = "装备-GPS/北斗")
    private String equipmentGpsMsg;
    
    @TableField("equipment_img_msg")
    @Schema(description = "装备-图像传输设备")
    private String equipmentImgMsg;
    
    @TableField("vehicle_motorbike")
    @Schema(description = "车辆-摩托车")
    private Integer vehicleMotorbike;
    
    @TableField("vehicle_electric_car")
    @Schema(description = "车辆-电动车")
    private Integer vehicleElectricCar;
    
    @TableField("patrol_vehicle")
    @Schema(description = "巡防投入-车辆")
    private Integer patrolVehicle;
    
    @TableField("patrol_police_communication")
    @Schema(description = "巡防投入-警务通")
    private Integer patrolPoliceCommunication;
    
    @TableField("patrol_pda")
    @Schema(description = "巡防投入-PDA")
    private Integer patrolPda;
    
    @TableField("patrol_question_vehicle")
    @Schema(description = "巡防投入-盘查车辆")
    private Integer patrolQuestionVehicle;
    
    @TableField("patrol_arrest_emp")
    @Schema(description = "巡防投入-抓获犯罪人员")
    private Integer patrolArrestEmp;
    
    @TableField("important_part")
    @Schema(description = "重点部位投入-部位数量")
    private Integer importantPart;
    
    @TableField("important_police")
    @Schema(description = "重点部位投入-民警数量")
    private Integer importantPolice;
    
    @TableField("important_auxiliary_police")
    @Schema(description = "重点部位投入-辅警数量")
    private Integer importantAuxiliaryPolice;
    
    @TableField("important_armed_police")
    @Schema(description = "重点部位投入-武警数量")
    private Integer importantArmedPolice;
    
    @TableField("important_vehicle")
    @Schema(description = "重点部位投入-车辆数量")
    private Integer importantVehicle;
    
    @TableField("important_weapon")
    @Schema(description = "重点部位投入-武器数量")
    private Integer importantWeapon;
    
    @TableField("memos")
    @Schema(description = "备注")
    private String memos;
}
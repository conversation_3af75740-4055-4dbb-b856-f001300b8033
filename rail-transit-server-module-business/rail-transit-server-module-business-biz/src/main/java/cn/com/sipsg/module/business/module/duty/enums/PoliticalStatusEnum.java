package cn.com.sipsg.module.business.module.duty.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 政治面貌枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum PoliticalStatusEnum {

    /**
     * 中共党员
     */
    PARTY_MEMBER("1", "中共党员"),

    /**
     * 中共预备党员
     */
    PROBATIONARY_PARTY_MEMBER("2", "中共预备党员"),

    /**
     * 共青团员
     */
    YOUTH_LEAGUE_MEMBER("3", "共青团员"),

    /**
     * 群众
     */
    MASSES("4", "群众");

    /**
     * 编码
     */
    @EnumValue
    private final String code;

    /**
     * 标签
     */
    @JsonValue
    private final String label;

    /**
     * 根据编码判断是否有效
     *
     * @param code 编码
     * @return 是否有效
     */
    public static boolean isValid(String code) {
        for (PoliticalStatusEnum value : values()) {
            if (value.getCode().equals(code)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 根据编码获取标签
     *
     * @param code 编码
     * @return 标签
     */
    public static String getLabelByCode(String code) {
        for (PoliticalStatusEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value.getLabel();
            }
        }
        return null;
    }

    /**
     * 根据标签获取编码
     *
     * @param label 标签
     * @return 编码
     */
    public static String getCodeByLabel(String label) {
        for (PoliticalStatusEnum value : values()) {
            if (value.getLabel().equals(label)) {
                return value.getCode();
            }
        }
        return null;
    }

    /**
     * 根据标签获取政治面貌枚举
     *
     * @param label 标签
     * @return 政治面貌枚举
     */
    public static PoliticalStatusEnum getByCode(String code) {
        for (PoliticalStatusEnum e : values()) {
            if (e.code.equals(code)) {
                return e;
            }
        }
        return null;
    }

    public static PoliticalStatusEnum getByLabel(String label) {
        for (PoliticalStatusEnum value : values()) {
            if (value.getLabel().equals(label)) {
                return value;
            }
        }
        return null;
    }
}
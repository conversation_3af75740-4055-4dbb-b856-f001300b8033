package cn.com.sipsg.module.business.module.supervision.mapper;

import cn.com.sipsg.common.mybatis.core.mapper.BaseMapperX;
import cn.com.sipsg.module.business.module.supervision.entity.PoliceRisk;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.stream.Collectors;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 警务风险信息 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Mapper
public interface PoliceRiskMapper extends BaseMapperX<PoliceRisk> {

    /**
     * 根据用户所在单位查询一周内的风险信息
     *
     * @param orgId 组织ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 风险信息列表
     */
    default List<PoliceRisk> findWeeklyRisksByOrgId(Long orgId, LocalDateTime startTime, LocalDateTime endTime) {
        return selectJoinList(PoliceRisk.class,
                new MPJLambdaWrapper<PoliceRisk>()
                        .selectAll(PoliceRisk.class)
                        .leftJoin("sys_user u ON tbl_police_risk.create_user_id = u.user_id")
                        .eq("u.org_id", orgId)
                        .between(PoliceRisk::getCreateTime, startTime, endTime)
                        .eq(PoliceRisk::getAvailable, true));
    }

    /**
     * 按风险类型统计数量
     *
     * @param orgId 组织ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 风险类型统计结果
     */
    default List<Object[]> countRisksByType(Long orgId, LocalDateTime startTime, LocalDateTime endTime) {
        return selectJoinList(Object[].class,
                new MPJLambdaWrapper<PoliceRisk>()
                        .select(PoliceRisk::getType)
                        .selectCount(PoliceRisk::getId, "count")
                        .leftJoin("sys_user u ON tbl_police_risk.create_user_id = u.user_id")
                        .eq("u.org_id", orgId)
                        .between(PoliceRisk::getCreateTime, startTime, endTime)
                        .eq(PoliceRisk::getAvailable, true)
                        .groupBy(PoliceRisk::getType));
    }
    
    /**
     * 根据警员ID列表查询一周内的风险数量
     * 警员ID对应的是创建人的ID
     *
     * @param employeeIds 警员ID列表
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 风险数量
     */
    default long countWeeklyRisksByEmployeeIds(List<String> employeeIds, LocalDateTime startTime, LocalDateTime endTime) {
        if (employeeIds == null || employeeIds.isEmpty()) {
            return 0L;
        }
        LambdaQueryWrapper<PoliceRisk> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(PoliceRisk::getCreateUserId, employeeIds)
                .between(PoliceRisk::getCreateTime, startTime, endTime)
                .eq(PoliceRisk::getAvailable, true);
        return selectCount(queryWrapper);
    }
    
    /**
     * 根据警员ID列表查询一周内的风险列表
     * 警员ID对应的是创建人的ID
     *
     * @param employeeIds 警员ID列表
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 风险列表
     */
    default List<PoliceRisk> findWeeklyRisksByEmployeeIds(List<String> employeeIds, LocalDateTime startTime, LocalDateTime endTime) {
        if (employeeIds == null || employeeIds.isEmpty()) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<PoliceRisk> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(PoliceRisk::getCreateUserId, employeeIds)
                .between(PoliceRisk::getCreateTime, startTime, endTime)
                .eq(PoliceRisk::getAvailable, true)
                .orderByDesc(PoliceRisk::getCreateTime);
        return selectList(queryWrapper);
    }
}

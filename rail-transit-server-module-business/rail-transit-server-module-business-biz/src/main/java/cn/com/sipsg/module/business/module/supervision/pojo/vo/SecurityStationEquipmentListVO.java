package cn.com.sipsg.module.business.module.supervision.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 安检点位设备列表视图对象
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
@Data
@Schema(description = "安检点位设备列表视图对象")
public class SecurityStationEquipmentListVO {

    @Schema(description = "安检点位ID")
    private String stationId;
    
    @Schema(description = "安检点位名称")
    private String stationName;
    
    @Schema(description = "地铁站点名称")
    private String subwayStationName;
    
    @Schema(description = "设备数量列表")
    private List<SecurityStationEquipmentVO> equipmentCounts;
}

package cn.com.sipsg.module.business.module.supervision.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 安检点数量统计视图对象
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "安检点数量统计视图对象")
public class SecurityStationCountVO {

    @Schema(description = "安检点总数")
    private Integer count;
}

package cn.com.sipsg.module.business.module.supervision.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 站点信息视图对象
 * 用于返回站点的基本信息（编号和名称）
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
@Schema(description = "站点信息视图对象")
public class StationInfoVO {

    @Schema(description = "站点编号")
    private String stationCode;

    @Schema(description = "站点名称")
    private String stationName;
}
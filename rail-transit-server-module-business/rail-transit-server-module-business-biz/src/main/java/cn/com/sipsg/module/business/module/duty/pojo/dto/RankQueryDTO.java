package cn.com.sipsg.module.business.module.duty.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 勤务排名查询条件
 */
@Data
@Schema(description = "勤务排名查询条件")
public class RankQueryDTO {
    
    @Schema(description = "统计类型，可选值：DAY-日，WEEK-周，MONTH-月，YEAR-年", required = true, example = "DAY")
    @NotBlank(message = "统计类型不能为空")
    private String statType;
    
    @Schema(description = "业务类型，可选值：PAN_CHA-盘查，AN_JIAN-安检，JIE_CHU_JING-接处警", required = true, example = "PAN_CHA")
    @NotBlank(message = "业务类型不能为空")
    private String bizType;
}

package cn.com.sipsg.module.business.module.supervision.service;

import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.module.business.module.supervision.entity.PersonnelAttention;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.*;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.*;

import java.time.LocalDateTime;
import java.util.List;

public interface StatisticsCompreService {
    /**
     * 查询当前用户所在单位及下级单位的日常数据分页列表
     *
     * @param pageNum  页码，从1开始
     * @param pageSize 每页条数
     * @return 分页封装的日常数据VO列表
     */
    CommonPageVO<StatisticsComparePageVO> pageAllStatisticsCompre(int pageNum, int pageSize);

    /**
     * 按条件查询日常数据分页列表（可指定单位及下属、时间范围）
     *
     * @param startTime   开始时间（可选）
     * @param endTime     结束时间（可选）
     * @param policeOrgId 单位ID（可选）
     * @param pageNum     页码
     * @param pageSize    每页条数
     * @return 分页封装的日常数据VO列表
     */
    CommonPageVO<StatisticsComparePageVO> pageStatisticsCompareByCondition(
            LocalDateTime startTime, LocalDateTime endTime, String policeOrgId, int pageNum, int pageSize);

    /**
     * 按条件查询日常数据分页列表（可指定单位及下属、时间范围）
     *
     * @param dto 查询条件DTO
     * @return 分页封装的日常数据VO列表
     */
    CommonPageVO<StatisticsComparePageVO> pageStatisticsCompareByCondition(StatisticsComparePageReqDTO dto);

    /**
     * 查询当前用户所在单位及下级单位的巡逻情况分页列表（统一方法）
     *
     * @param dto 查询参数
     * @return 分页封装的巡逻情况VO列表
     */
    CommonPageVO<PatrolPageVO> pagePatrolForCurrentUser(PatrolPageReqDTO dto);

    /**
     * 查询当前用户所在单位及下级单位的全部抓获统计数据分页列表
     *
     * @param pageNum  页码，从1开始
     * @param pageSize 每页条数
     * @return 分页封装的抓获统计VO列表
     */
    CommonPageVO<PersonnelCapturedPageVO> pageAllPersonnelCapturedForCurrentUser(int pageNum, int pageSize);

    /**
     * 分页查询当前用户所在单位及下级单位的抓获统计数据（统一方法）
     * 根据查询条件动态决定查询逻辑，如果所有条件都为空则查询全部数据
     *
     * @param dto 查询条件
     * @return 分页封装的抓获统计VO列表
     */
    CommonPageVO<PersonnelCapturedPageVO> pagePersonnelCapturedForCurrentUser(CapturedPageQueryReqDTO dto);

    /**
     * 查询当前用户所在单位及下级单位的全部人员关注数据分页列表
     *
     * @param pageNum  页码，从1开始
     * @param pageSize 每页条数
     * @return 分页封装的人员关注VO列表
     */
    CommonPageVO<PersonnelAttention> pageAllPersonnelAttentionForCurrentUser(int pageNum, int pageSize);

    /**
     * 按条件分页查询当前用户所在单位及下级单位的人员关注数据
     *
     * @param orgId         处置单位ID（可选）
     * @param warningSource 预警来源（可选）
     * @param focusType     关注方式（可选）
     * @param personType    人员类型（可选）
     * @param pageNum       页码，从1开始
     * @param pageSize      每页条数
     * @return 分页封装的人员关注VO列表
     */
    CommonPageVO<PersonnelAttention> pagePersonnelAttentionForCurrentUserByCondition(String orgId, Integer warningSource, Integer focusType, Integer personType, int pageNum, int pageSize);

    /**
     * 分页查询当前用户所在单位及下级单位的人员关注数据（统一方法）
     * 根据查询条件动态决定查询逻辑，如果所有条件都为空则查询全部数据
     *
     * @param dto 查询条件
     * @return 分页封装的人员关注VO列表
     */
    CommonPageVO<PersonnelAttention> pagePersonnelAttentionForCurrentUser(FocusPageQueryReqDTO dto);

    /**
     * 获取每日报备详情
     *
     * @param id 报备ID
     * @return 每日报备详情VO
     */
    DailyReportDetailVO getDailyReportDetail(String id);

    /**
     * 保存或更新每日报备
     *
     * @param dto 每日报备编辑请求DTO
     * @return 操作结果
     */
    Boolean saveOrUpdateDailyReport(DailyReportEditReqDTO dto);

    /**
     * 分页查询每日报备数据
     * 支持按时间范围查询，支持数据权限（管理员看全部，普通用户看当前单位及下级单位）
     *
     * @param dto 分页查询请求DTO
     * @return 分页封装的每日报备VO列表
     */
    CommonPageVO<DailyReportVO> pageDailyReport(DailyReportPageReqDTO dto);

    /**
     * 保存或更新日常数据
     *
     * @param dto 日常数据保存/更新DTO
     * @return 操作结果
     */
    Boolean saveOrUpdateStatisticsCompre(StatisticsCompreSaveDTO dto);

    /**
     * 获取日常数据详情
     *
     * @param id 日常数据ID
     * @return 日常数据详情DTO
     */
    StatisticsCompreSaveDTO getStatisticsCompreDetail(String id);

    /**
     * 保存或更新巡逻信息
     *
     * @param dto 巡逻信息保存/更新DTO
     * @return 操作结果
     */
    Boolean saveOrUpdatePatrol(PatrolSaveDTO dto);

    /**
     * 获取巡逻信息详情
     *
     * @param id 巡逻信息ID
     * @return 巡逻信息详情DTO
     */
    PatrolSaveDTO getPatrolDetail(String id);

    /**
     * 删除日常数据
     *
     * @param id 日常数据ID
     * @return 操作结果
     */
    Boolean deleteStatisticsCompre(String id);

    /**
     * 删除每日报备
     *
     * @param id 每日报备ID
     * @return 操作结果
     */
    Boolean deleteDailyReport(String id);

    /**
     * 删除巡逻情况
     *
     * @param id 巡逻情况ID
     * @return 操作结果
     */
    Boolean deletePatrol(String id);

    /**
     * 删除抓获统计
     *
     * @param id 抓获统计ID
     * @return 操作结果
     */
    Boolean deleteCaptured(String id);

    /**
     * 删除关注统计
     *
     * @param id 关注统计ID
     * @return 操作结果
     */
    Boolean deleteFocus(String id);

    /**
     * 保存或更新抓获记录
     *
     * @param dto 抓获记录保存/更新DTO
     * @return 操作结果
     */
    Boolean saveOrUpdatePersonnelCaptured(PersonnelCapturedSaveDTO dto);

    /**
     * 获取抓获记录详情
     *
     * @param id 抓获记录ID
     * @return 抓获记录详情DTO
     */
    PersonnelCapturedSaveDTO getPersonnelCapturedDetail(String id);

    /**
     * 保存或更新关注记录
     *
     * @param dto 关注记录保存/更新DTO
     * @return 操作结果
     */
    Boolean saveOrUpdatePersonnelAttention(PersonnelAttentionSaveDTO dto);

    /**
     * 获取关注记录详情
     *
     * @param id 关注记录ID
     * @return 关注记录详情DTO
     */
    PersonnelAttentionSaveDTO getPersonnelAttentionDetail(String id);

    /**
     * 查询所有站点信息
     * 返回站点编号和站点名称
     *
     * @return 站点信息列表
     */
    List<StationInfoVO> getAllStations();
}
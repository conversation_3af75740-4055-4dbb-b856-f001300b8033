package cn.com.sipsg.module.business.module.supervision.config;

import io.minio.MinioClient;
import io.minio.SetBucketPolicyArgs;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
public class MinioConfig {
    @Value("${dromara.x-file-storage.minio[0].end-point}")
    private String endpoint;
    @Value("${dromara.x-file-storage.minio[0].access-key}")
    private String accessKey;
    @Value("${dromara.x-file-storage.minio[0].secret-key}")
    private String secretKey;
    @Value("${dromara.x-file-storage.minio[0].bucket-name}")
    private String minioBucket;
    @Value("${dromara.x-file-storage.minio[0].domain}")
    private String domain;

    @Bean
    public MinioClient minioClient() {
        MinioClient client = MinioClient.builder()
                .endpoint(endpoint)
                .credentials(accessKey, secretKey)
                .build();
        
        // 应用启动时确保存储桶存在
        try {
            boolean bucketExists = client.bucketExists(
                io.minio.BucketExistsArgs.builder()
                    .bucket(minioBucket)
                    .build()
            );
            
            if (!bucketExists) {
                client.makeBucket(
                    io.minio.MakeBucketArgs.builder()
                        .bucket(minioBucket)
                        .build()
                );
                log.info("MinIO存储桶 " + minioBucket + " 创建成功");
            } else {
                log.info("MinIO存储桶 " + minioBucket + " 已存在");
            }
            
            // 设置桶策略为公共读取
            String policy = "{" +
                "\"Version\":\"2012-10-17\"," +
                "\"Statement\":[" +
                "{" +
                "\"Effect\":\"Allow\"," +
                "\"Principal\":{\"AWS\":[\"*\"]}," +
                "\"Action\":[\"s3:GetObject\"]," +
                "\"Resource\":[\"arn:aws:s3:::" + minioBucket + "/*\"]" +
                "}" +
                "]" +
                "}";
            
            client.setBucketPolicy(
                SetBucketPolicyArgs.builder()
                    .bucket(minioBucket)
                    .config(policy)
                    .build()
            );
            log.info("MinIO存储桶 " + minioBucket + " 公共读取策略设置成功");
        } catch (Exception e) {
            System.err.println("MinIO存储桶检查/创建失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        return client;
    }

    public String getMinioBucket() {
        return minioBucket;
    }

    public String getDomain() { return domain; }
}
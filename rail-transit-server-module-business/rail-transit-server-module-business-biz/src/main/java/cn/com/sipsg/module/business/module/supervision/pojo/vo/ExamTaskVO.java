package cn.com.sipsg.module.business.module.supervision.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 考试任务视图对象
 * 用于返回前端所需的考试任务数据
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Data
@Schema(description = "考试任务视图对象")
public class ExamTaskVO {

    @Schema(description = "考试任务ID")
    private String id;

    @Schema(description = "题库编号")
    private String questionBankId;

    @Schema(description = "考试任务名称")
    private String taskName;

    @Schema(description = "考试开始时间")
    private LocalDateTime startTime;

    @Schema(description = "考试结束时间")
    private LocalDateTime endTime;

    @Schema(description = "考试时长（分钟）")
    private Integer examDuration;

    @Schema(description = "总分")
    private Integer totalScore;

    @Schema(description = "合格分数")
    private Integer passingScore;

    @Schema(description = "单选题数量")
    private Integer singleChoiceNum;

    @Schema(description = "多选题数量")
    private Integer multipleChoiceNum;

    @Schema(description = "判断题数量")
    private Integer trueFalseNum;

    @Schema(description = "总题目数量")
    private Integer totalQuestionNum;

    @Schema(description = "备注")
    private String remark;
    
    @Schema(description = "考试分数")
    private Integer score;
    
    @Schema(description = "考试状态（未开始/进行中/合格/不合格/已过期）")
    private String status;
    
    @Schema(description = "创建时间")
    private LocalDateTime createTime;
}

package cn.com.sipsg.module.business.module.supervision.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 盘查前科情况保存/详情DTO
 * 用于新增、编辑和详情查询
 */
@Data
@Schema(description = "盘查前科情况保存/详情DTO")
public class InvestigationSaveDTO {

    @Schema(description = "主键ID（更新时必填，新增时不填）", example = "1234567890abcdef12345678")
    private String id;

    @Schema(description = "盘查时间", required = true, example = "2025-07-22 09:00:00")
    @NotNull(message = "盘查时间不能为空")
    private LocalDateTime time;

    @Schema(description = "盘查民警", required = true, example = "张三")
    @NotBlank(message = "盘查民警不能为空")
    private String policeName;

    @Schema(description = "盘查单位ID", required = true, example = "org_001")
    @NotBlank(message = "盘查单位ID不能为空")
    private String policeOrgId;

    @Schema(description = "被盘查人姓名", required = true, example = "李四")
    @NotBlank(message = "被盘查人姓名不能为空")
    private String personName;

    @Schema(description = "被盘查人身份证号", required = true, example = "110101199001011234")
    @NotBlank(message = "被盘查人身份证号不能为空")
    private String personIdCard;

    @Schema(description = "前科类型编号列表,多个编号以英文逗号分隔拼接", example = "04,05")
    private String personTag;

    @Schema(description = "备注", example = "盘查详细情况说明")
    private String memos;

    @Schema(description = "现场图片URL列表，JSON格式字符串", example = "[\"http://example.com/img1.jpg\",\"http://example.com/img2.jpg\"]")
    private String imageUrls;
}
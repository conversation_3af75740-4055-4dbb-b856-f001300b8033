package cn.com.sipsg.module.business.module.supervision.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "考试任务分页VO")
public class ExamTaskPageVO {
    @Schema(description = "考试任务ID")
    private String id;
    @Schema(description = "考试任务名称")
    private String taskName;
    @Schema(description = "考试时长（分钟）")
    private Integer examDuration;
    @Schema(description = "总分")
    private Integer totalScore;
    @Schema(description = "合格分")
    private Integer passingScore;
    @Schema(description = "总题数")
    private Integer totalQuestionNum;
    @Schema(description = "需考试人数")
    private Integer requiredCount;
    @Schema(description = "已考试人数")
    private Integer finishedCount;
    @Schema(description = "合格率（百分比字符串）")
    private String passRate;
    @Schema(description = "考试时间区间")
    private String examTimeRange;
    @Schema(description = "成绩计入档案（是/否）")
    private String scoreSync;
    @Schema(description = "状态")
    private String status;
    @Schema(description = "题库编号")
    private String questionBankId;
}
package cn.com.sipsg.module.business.module.duty.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> lixt
 * @date : 2025-04-24 15:59:25
 * @Description: 警局组织站点关系表
 */
@Data
@TableName("tbl_base_police_org_station")
public class BasePoliceOrgStation {
    @TableId(type = IdType.ASSIGN_ID)  // 主键自增
    private String id;  // 主键ID

    @TableField("police_org_id")
    private String policeOrgId;  // 警局组织ID

    @TableField("station_id")
    private String stationId;  // 站点ID

    @TableField("station_code")
    private String stationCode;  // 站点编码

    @TableField("station_name")
    private String stationName;  // 站点名称

    @TableField("police_org_name")
    private String policeOrgName;  // 警局组织名称

    @TableField("is_transfer")
    private Boolean isTransfer;  // 是否为换乘站

    @TableField("available")
    private Boolean available;  // 是否有效

    @TableField("create_user_id")
    private String createUserId;  // 创建者用户ID

    @TableField("create_time")
    private LocalDateTime createTime;  // 记录创建时间

    @TableField("update_user_id")
    private String updateUserId;  // 最后更新者用户ID

    @TableField("update_time")
    private LocalDateTime updateTime;  // 记录最后更新时间

    @TableField("type")
    private Integer type;  // 地点类型，1-地铁站，2-公交车，3-公交站点，99-其他
}

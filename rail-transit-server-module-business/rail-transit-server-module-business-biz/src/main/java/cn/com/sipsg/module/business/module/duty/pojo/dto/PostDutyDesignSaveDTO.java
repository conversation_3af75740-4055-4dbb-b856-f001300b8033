package cn.com.sipsg.module.business.module.duty.pojo.dto;

import lombok.Data;
import java.util.List;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 岗位班次设置保存DTO
 */
@Data
public class PostDutyDesignSaveDTO {
    @Schema(description = "岗位类别ID", example = "1")
    private Long postId; // 岗位类别ID
    @Schema(description = "绑定的班次类型ID列表", example = "[1,2,3]")
    private List<Long> turnIds; // 绑定的班次类型ID列表
} 
package cn.com.sipsg.module.business.module.supervision.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 绩效考核项表
 */
@Data
@TableName("tbl_performance_item")
public class PerformanceItem {
    /** 主键 */
    @TableId(value = "id")
    private Long id;
    /** 创建人id */
    @TableField("create_user_id")
    private String createUserId;
    /** 创建时间 */
    @TableField("create_time")
    private LocalDateTime createTime;
    /** 更新人id */
    @TableField("update_user_id")
    private String updateUserId;
    /** 更新时间 */
    @TableField("update_time")
    private LocalDateTime updateTime;
    /** 是否有效 */
    @TableField("available")
    private Boolean available;
    /** 考核项名称 */
    @TableField("item_name")
    private String itemName;
    /** 父考核项id */
    @TableField("parent_item_id")
    private Long parentItemId;
    /** 考核单位 */
    @TableField("check_org_id")
    private String checkOrgId;
    /** 是否计入总分(0-否，1-是) */
    @TableField("is_total")
    private Integer isTotal;
    /** 是否需要折算(0-否，1-是) */
    @TableField("is_conversion")
    private Integer isConversion;
    /** 考核项满分分数 */
    @TableField("score")
    private Double score;
    /** 备注说明 */
    @TableField("remark")
    private String remark;
}
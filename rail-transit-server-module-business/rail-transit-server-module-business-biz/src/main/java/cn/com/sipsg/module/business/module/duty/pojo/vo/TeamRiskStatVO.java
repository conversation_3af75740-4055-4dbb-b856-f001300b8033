package cn.com.sipsg.module.business.module.duty.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR> lixt
 * @date : 2025-01-20
 * @Description: 队伍风险隐患统计VO
 */
@Data
@Schema(description = "队伍风险隐患统计VO")
public class TeamRiskStatVO {

    @Schema(description = "组织编号")
    private String orgId;

    @Schema(description = "组织名称")
    private String orgName;

    @Schema(description = "各谈话类别统计数量，key为谈话类别code，value为数量")
    private Map<String, Integer> talkCategoryStats;
}
package cn.com.sipsg.module.business.module.duty.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR> lixt
 * @date : 2025-05-10
 * @Description: 单位信息VO
 */
@Data
@Schema(description = "单位VO", example = "{\"id\":1001,\"name\":\"轨交分局\"}")
public class OrgVO {
    @Schema(description = "单位ID", example = "1001")
    private String id;

    @Schema(description = "单位名称", example = "轨交分局")
    private String name;
}
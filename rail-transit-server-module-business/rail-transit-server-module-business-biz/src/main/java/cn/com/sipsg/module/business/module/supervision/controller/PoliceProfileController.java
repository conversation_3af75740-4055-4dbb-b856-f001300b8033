package cn.com.sipsg.module.business.module.supervision.controller;

import cn.com.sipsg.common.pojo.CommonResult;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.web.core.controller.BaseController;
import cn.com.sipsg.module.business.module.duty.pojo.vo.PoliceListVO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.OrgPolicePageDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.PoliceFuzzySearchDTO;
import cn.com.sipsg.module.business.module.supervision.service.PoliceService;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.OnDutyCountVO;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.StationPoliceCountListVO;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.LineStationPoliceCountVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import java.util.List;

/**
 * 警员档案控制器
 * 提供警员档案的增删改查功能
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Tag(name = "民警档案", description = "民警档案相关接口")
@RestController
@RequestMapping("/police")
@RequiredArgsConstructor
@Slf4j
public class PoliceProfileController extends BaseController {

    private final PoliceService policeService;
    
    /**
     * 查询当前用户所在组织的值班民警数量
     * 统计当前用户所在组织当天在岗的民警数量
     *
     * @return 当前组织值班民警数量
     */
    @Operation(summary = "查询当前组织值班民警数量", description = "统计当前用户所在组织当天在岗的民警数量")
    @GetMapping("/on-duty/count")
    public CommonResult<OnDutyCountVO> getOnDutyPoliceCount() {
        return handle(() -> CommonResult.data(policeService.getOnDutyPoliceCount()));
    }
    
    /**
     * 按站点统计当天各站点民警数量
     * 统计当天所有站点的在岗民警数量
     *
     * @return 各站点值班民警数量列表
     */
    @Operation(summary = "按站点统计当天各站点民警数量", description = "统计当天所有站点的在岗民警数量")
    @GetMapping("/on-duty/stations")
    public CommonResult<StationPoliceCountListVO> getStationPoliceCount() {
        return handle(() -> CommonResult.data(policeService.getStationPoliceCount()));
    }
    
    /**
     * 按线路统计当天在岗民警数量
     * @return 线路分组的民警统计列表
     */
    @Operation(summary = "按线路统计当天在岗民警数量", description = "按线路分组，统计当天在岗民警数量")
    @GetMapping("/on-duty/line-stations")
    public CommonResult<List<LineStationPoliceCountVO>> getLineStationPoliceCount() {
        return handle(() -> CommonResult.data(policeService.getLineStationPoliceCount()));
    }
    
    /**
     * 民警分页/模糊分页列表
     * 查询当前用户所在组织下所有民警信息列表（可带模糊条件）
     *
     * @param searchDTO 分页+模糊参数DTO
     * @return 民警分页列表
     */
    @Operation(summary = "民警分页/模糊分页列表", description = "查询当前用户所在组织下所有民警信息列表（可带模糊条件）")
    @PostMapping("/profile/page")
    public CommonResult<CommonPageVO<PoliceListVO>> getOrgPoliceList(@RequestBody @Valid PoliceFuzzySearchDTO searchDTO) {
        // 如果没有任何查询条件，则走普通分页，否则走模糊分页
        if ((searchDTO.getKeyword() == null || searchDTO.getKeyword().trim().isEmpty())
                && searchDTO.getType() == null
                && (searchDTO.getDuty() == null || searchDTO.getDuty().trim().isEmpty())) {
            // 只分页
            OrgPolicePageDTO pageDTO = new OrgPolicePageDTO();
            pageDTO.setPageNum(searchDTO.getPageNum());
            pageDTO.setPageSize(searchDTO.getPageSize());
            return handle(() -> CommonResult.data(policeService.getOrgPoliceList(pageDTO)));
        } else {
            // 模糊分页
            return handle(() -> CommonResult.data(policeService.fuzzySearchOrgPoliceList(searchDTO)));
        }
    }
}

package cn.com.sipsg.module.business.module.supervision.mapper;

import cn.com.sipsg.module.business.module.supervision.entity.ExamTaskEmp;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import java.util.List;

/**
 * 考试人员关联数据访问接口
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Mapper
public interface ExamTaskEmpMapper extends BaseMapper<ExamTaskEmp> {

    /**
     * 根据组织ID查询所有关联的考试任务
     *
     * @param orgId 组织ID
     * @return 考试任务ID列表
     */
    List<String> findTaskIdsByOrgId(@Param("orgId") Long orgId);

    /**
     * 刷新所有非合格/不合格的考试状态（未开始/进行中/已过期），根据主表时间动态赋值
     */
    @Update("UPDATE tbl_exam_task_emp emp SET status = CASE WHEN task.start_time > NOW() THEN '01' WHEN task.end_time < NOW() THEN '05' ELSE '02' END, update_time = NOW() FROM tbl_exam_task task WHERE emp.task_id = task.id AND emp.status NOT IN ('03', '04')")
    void refreshAllEmpStatus();
}

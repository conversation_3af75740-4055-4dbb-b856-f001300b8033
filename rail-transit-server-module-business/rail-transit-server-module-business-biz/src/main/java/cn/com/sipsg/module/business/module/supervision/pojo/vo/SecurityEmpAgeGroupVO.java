package cn.com.sipsg.module.business.module.supervision.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 安检人员按年龄分组统计VO
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "安检人员按年龄分组统计VO")
public class SecurityEmpAgeGroupVO {

    @Schema(description = "安检人员总数")
    private Integer totalCount;

    @Schema(description = "按年龄分组统计列表")
    private List<SecurityEmpAgeGroupItem> groups;

    /**
     * 安检人员年龄分组项
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "安检人员年龄分组项")
    public static class SecurityEmpAgeGroupItem {

        @Schema(description = "年龄段描述")
        private String ageRange;

        @Schema(description = "安检人员数量")
        private Integer count;

        @Schema(description = "占比（百分比）")
        private BigDecimal percentage;
    }
}

package cn.com.sipsg.module.business.module.common.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

/**
 * Bean属性复制工具类
 * 提供智能的对象属性复制功能，根据源对象和目标对象类型自动选择最合适的复制策略
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Slf4j
public class BeanCopyUtils {

    /**
     * 智能复制对象属性
     * 根据源对象和目标对象类型选择合适的复制策略
     *
     * @param source 数据来源对象
     * @param target 目标对象
     */
    public static void copyProperties(Object source, Object target) {
        if (source == null || target == null) {
            log.warn("复制字段失败：源对象或目标对象为空");
            return;
        }
        
        try {
            // 判断是否为实体到DTO的复制场景
            boolean isEntityToDto = isEntityToDtoScenario(source, target);
            
            if (isEntityToDto) {
                // 实体到DTO的复制，使用反射方式精确控制字段复制
                copyPropertiesByReflection(source, target);
            } else {
                // DTO到实体的复制，使用BeanUtils方式
                copyPropertiesByBeanUtils(source, target);
            }
            
        } catch (Exception e) {
            log.error("复制对象属性失败，source: {}, target: {}", 
                     source.getClass().getSimpleName(), 
                     target.getClass().getSimpleName(), e);
            
            // 降级使用反射方式
            copyPropertiesByReflection(source, target);
        }
    }

    /**
     * 复制警员基本信息字段（兼容原有方法）
     *
     * @param source 数据来源对象
     * @param target 目标对象
     */
    public static void copyPoliceBaseFields(Object source, Object target) {
        copyProperties(source, target);
    }
    
    /**
     * 判断是否为实体到DTO的复制场景
     *
     * @param source 源对象
     * @param target 目标对象
     * @return true 如果是实体到DTO的复制场景
     */
    private static boolean isEntityToDtoScenario(Object source, Object target) {
        String sourceClassName = source.getClass().getSimpleName();
        String targetClassName = target.getClass().getSimpleName();
        
        // 判断源对象是否为实体类
        boolean sourceIsEntity = sourceClassName.contains("Emp") || 
                                sourceClassName.contains("Entity") || 
                                sourceClassName.startsWith("Base") ||
                                sourceClassName.contains("Police");
        
        // 判断目标对象是否为DTO类
        boolean targetIsDto = targetClassName.contains("DTO") || 
                             targetClassName.contains("Dto") ||
                             targetClassName.contains("VO");
        
        return sourceIsEntity && targetIsDto;
    }
    
    /**
     * 使用BeanUtils进行属性复制（适用于DTO到实体的场景）
     *
     * @param source 数据来源对象
     * @param target 目标对象
     */
    private static void copyPropertiesByBeanUtils(Object source, Object target) {
        // 定义需要忽略的系统字段，避免覆盖业务逻辑设置的值
        String[] ignoreProperties = {
            "id", "createTime", "createUserId", "updateTime", "updateUserId",
            "available", "display", "enable"
        };
        
        // 使用Spring的BeanUtils进行属性复制，忽略系统字段
        BeanUtils.copyProperties(source, target, ignoreProperties);
        
        // 额外处理：对于字符串字段，如果是空字符串或去空后的空字符串，进行特殊处理
        copyNonEmptyStringFields(source, target);
    }
    
    /**
     * 复制非空字符串字段，空字符串或去空后的空字符串不进行复制
     *
     * @param source 数据来源对象
     * @param target 目标对象
     */
    private static void copyNonEmptyStringFields(Object source, Object target) {
        // 定义需要特殊处理的字符串字段
        String[] stringFields = {
            "code", "name", "idcard", "phone", "position", "duties", "policeRank", 
            "nationality", "rankLevel", "status", "assistPoliceCode", "jobType", 
            "assistPoliceJob", "assistPrethoracicNo", "policePhone", "emergencyContactName",
            "emergencyContactPhone", "addressProvince", "addressCity", "addressArea", 
            "addressStreet", "addressDetail", "education", "politicalStatus"
        };

        for (String field : stringFields) {
            try {
                // 获取source对象的getter方法
                String getterName = "get" + field.substring(0, 1).toUpperCase() + field.substring(1);
                java.lang.reflect.Method getter = source.getClass().getMethod(getterName);
                Object value = getter.invoke(source);

                // 只有当值不为null且不为空字符串（去空后）时才复制
                if (value != null && value instanceof String) {
                    String strValue = ((String) value).trim();
                    if (!strValue.isEmpty()) {
                        // 获取target对象的setter方法
                        String setterName = "set" + field.substring(0, 1).toUpperCase() + field.substring(1);
                        java.lang.reflect.Method setter = target.getClass().getMethod(setterName, String.class);
                        setter.invoke(target, strValue); // 使用trim后的值
                    }
                }
            } catch (Exception e) {
                // 如果某个字段不存在，忽略异常继续下一个字段
                log.debug("复制字符串字段 {} 失败: {}", field, e.getMessage());
            }
        }
    }
    
    /**
     * 使用反射复制字段的方法
     * 适用于实体到DTO的复制场景
     *
     * @param source 数据来源对象
     * @param target 目标对象
     */
    private static void copyPropertiesByReflection(Object source, Object target) {
        // 定义需要复制的基本字段
        String[] baseFields = {
                "code", "name", "idcard", "sex", "birthday", "phone", "type", "position", "policeOrgId",
                "imgUrl", "duties", "policeRank", "nationality", "rankLevel", "status", "assistPoliceCode",
                "jobType", "assistPoliceJob", "assistPrethoracicNo", "policePhone", "emergencyContactName",
                "emergencyContactPhone", "addressProvince", "addressCity", "addressArea", "addressStreet",
                "addressDetail", "education", "politicalStatus", "responsiblePoliceId", "inChargeLeaderId", 
                "mainLeaderId"
        };

        // 使用反射复制字段
        for (String field : baseFields) {
            try {
                // 获取source对象的getter方法
                String getterName = "get" + field.substring(0, 1).toUpperCase() + field.substring(1);
                java.lang.reflect.Method getter = source.getClass().getMethod(getterName);
                Object value = getter.invoke(source);

                // 获取target对象的setter方法
                String setterName = "set" + field.substring(0, 1).toUpperCase() + field.substring(1);
                java.lang.reflect.Method setter = target.getClass().getMethod(setterName, getter.getReturnType());
                setter.invoke(target, value);
            } catch (Exception e) {
                // 如果某个字段不存在，忽略异常继续下一个字段
                log.debug("复制字段 {} 失败: {}", field, e.getMessage());
            }
        }
    }

    /**
     * 简单的属性复制，使用Spring BeanUtils
     *
     * @param source 源对象
     * @param target 目标对象
     * @param ignoreProperties 需要忽略的属性名
     */
    public static void copyPropertiesSimple(Object source, Object target, String... ignoreProperties) {
        if (source == null || target == null) {
            log.warn("复制字段失败：源对象或目标对象为空");
            return;
        }
        
        try {
            BeanUtils.copyProperties(source, target, ignoreProperties);
        } catch (Exception e) {
            log.error("简单属性复制失败，source: {}, target: {}", 
                     source.getClass().getSimpleName(), 
                     target.getClass().getSimpleName(), e);
        }
    }

    /**
     * 复制属性并创建新对象
     *
     * @param source 源对象
     * @param targetClass 目标类型
     * @param <T> 目标类型泛型
     * @return 复制后的新对象
     */
    public static <T> T copyPropertiesAndCreate(Object source, Class<T> targetClass) {
        if (source == null) {
            return null;
        }
        
        try {
            T target = targetClass.getDeclaredConstructor().newInstance();
            copyProperties(source, target);
            return target;
        } catch (Exception e) {
            log.error("复制属性并创建新对象失败，source: {}, targetClass: {}", 
                     source.getClass().getSimpleName(), 
                     targetClass.getSimpleName(), e);
            return null;
        }
    }
}
package cn.com.sipsg.module.business.module.supervision.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 日常数据详情查询请求DTO
 */
@Data
@Schema(description = "日常数据详情查询请求参数")
public class StatisticsCompreDetailReqDTO {
    
    @Schema(description = "日常数据ID", required = true, example = "1234567890abcdef12345678")
    @NotBlank(message = "日常数据ID不能为空")
    private String id;
}

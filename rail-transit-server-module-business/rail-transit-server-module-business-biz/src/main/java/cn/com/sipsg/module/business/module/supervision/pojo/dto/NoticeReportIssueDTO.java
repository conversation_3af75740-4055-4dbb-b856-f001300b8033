package cn.com.sipsg.module.business.module.supervision.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.Valid;
import java.util.List;

/**
 * 通报信息下发DTO
 */
@Data
@Schema(description = "通报信息下发DTO")
public class NoticeReportIssueDTO {

    /**
     * 通报信息ID
     */
    @NotBlank(message = "通报信息ID不能为空")
    @Schema(description = "通报信息ID", required = true)
    private String id;

    /**
     * 下发单位列表
     */
    @NotEmpty(message = "下发单位不能为空")
    @Valid
    @Schema(description = "下发单位列表", required = true)
    private List<OrgInfo> orgList;

    /**
     * 单位信息内部类
     */
    @Data
    @Schema(description = "单位信息")
    public static class OrgInfo {
        
        /**
         * 单位ID
         */
        @NotBlank(message = "单位ID不能为空")
        @Schema(description = "单位ID", required = true)
        private String orgId;

        /**
         * 单位名称
         */
        @Schema(description = "单位名称")
        private String orgName;
    }
}

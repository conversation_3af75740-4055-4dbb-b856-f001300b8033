package cn.com.sipsg.module.business.module.duty.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> lixt
 * @date : 2025-01-15
 * @Description: 警员家庭成员关联表
 */
@Data
@TableName("tbl_base_police_emp_family")
public class BasePoliceEmpFamily {

    @TableId(type = IdType.ASSIGN_ID)
    private String id;  // 主键ID

    @TableField("police_emp_id")
    private String policeEmpId;  // 警员ID

    @TableField("relationship")
    private String relationship;  // 关系

    @TableField("name")
    private String name;  // 姓名

    @TableField("available")
    private Boolean available;  // 是否有效

    @TableField("create_time")
    private LocalDateTime createTime;  // 创建时间

    @TableField("create_user_id")
    private String createUserId;  // 创建用户ID

    @TableField("update_time")
    private LocalDateTime updateTime;  // 更新时间

    @TableField("update_user_id")
    private String updateUserId;  // 更新用户ID
}
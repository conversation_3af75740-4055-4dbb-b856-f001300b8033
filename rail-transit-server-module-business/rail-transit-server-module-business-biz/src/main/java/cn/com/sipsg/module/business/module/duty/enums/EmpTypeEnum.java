package cn.com.sipsg.module.business.module.duty.enums;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Getter
@Slf4j
public enum EmpTypeEnum {
    POLICE(1, "民警"),
    ASSISTANT(2, "辅警"),
    SECURITY(3, "安保"),
    DOG(4, "警犬");

    private final int code;
    private final String label;

    EmpTypeEnum(int code, String label) {
        this.code = code;
        this.label = label;
    }

    /**
     * 将字符串类型解析为对应的Integer类型代码
     * @param type 要解析的字符串类型
     * @return 对应的Integer类型代码，如果无法解析则返回null
     */
    public static Integer parse(String type) {
        if (type == null) return null;
        for (EmpTypeEnum e : values()) {
            if (e.label.equals(type) || String.valueOf(e.code).equals(type)) {
                return e.code;
            }
        }
        try {
            return Integer.parseInt(type);
        } catch (Exception e) {
            log.warn("EmpTypeEnum.parse异常，type={}，e={}", type, e.getMessage());
            return null;
        }
    }

    public static String getLabelByCode(Integer code) {
        if (code == null) return "";
        for (EmpTypeEnum e : values()) {
            if (e.code == code) {
                return e.label;
            }
        }
        log.warn("EmpTypeEnum.getLabelByCode未匹配到类型，code={}", code);
        return "";
    }
} 
package cn.com.sipsg.module.business.module.supervision.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.io.Serializable;

/**
 * 综合统计-跟防情况相关图片
 */
@Data
@TableName("tbl_statistics_trace_img")
public class StatisticsTraceImg implements Serializable {
    /** 主键id */
    @TableId(type = IdType.INPUT)
    private Long id;

    /** 创建人id */
    private String createUserId;

    /** 创建时间,unix时间戳,精确到毫秒 */
    private Long createTime;

    /** 修改人id */
    private String updateUserId;

    /** 修改时间,unix时间戳,精确到毫秒 */
    private Long updateTime;

    /** 是否有效 */
    private Boolean available;

    /** 跟防情况主表ID */
    private Long traceId;

    /** 图片链接,最大长度1024 */
    private String imgUrl;
} 
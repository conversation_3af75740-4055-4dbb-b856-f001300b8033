package cn.com.sipsg.module.business.module.duty.controller;

import cn.com.sipsg.common.pojo.CommonResult;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.web.core.controller.BaseController;
import cn.com.sipsg.module.business.module.duty.pojo.dto.*;
import cn.com.sipsg.module.business.module.duty.pojo.vo.*;
import cn.com.sipsg.module.business.module.duty.service.DutyPostureService;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.IdDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR> lixt
 * @date : 2025-04-24 10:38:58
 * @Description: 勤务档案
 */
@Tag(name = "勤务档案")
@RestController
@RequiredArgsConstructor
@RequestMapping("/business/duty/posture")
@Slf4j
public class DutyPostureController extends BaseController {
    private final DutyPostureService dutyPostureService;

    @Operation(summary = "勤务统计信息（合并警力数量和下辖单位数量）")
    @GetMapping("/dutyStatistics")
    public CommonResult<DutyStatisticsVO> getDutyStatistics() {
        return handle(() -> CommonResult.data(dutyPostureService.getDutyStatistics()));
    }

    @Operation(summary = "一警一档分页/模糊查询列表")
    @PostMapping("/police/search")
    public CommonResult<CommonPageVO<PoliceListVO>> searchPoliceList(@RequestBody(required = false) PoliceListQueryDTO query) {
        return handle(() -> CommonResult.data(dutyPostureService.searchPoliceList(query)));
    }

    @Operation(summary = "批量导入警员信息")
    @PostMapping(value = "/police/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public CommonResult<List<DutyPoliceImportDTO>> importPolice(@Parameter(description = "待导入的警员信息文件", schema = @Schema(type = "string", format = "binary")) @RequestPart("file") MultipartFile file) {
        return handle(() -> CommonResult.data(dutyPostureService.importPolice(file)));
    }

    @Operation(summary = "导出警员信息")
    @PostMapping("/police/export")
    public CommonResult<Boolean> exportPolice(@RequestBody List<PoliceListVO> policeList) {
        return handle(() -> CommonResult.data(dutyPostureService.exportPolice(policeList)));
    }

    @Operation(summary = "一所一档分页/模糊查询列表")
    @PostMapping("/police/org/list")
    public CommonResult<CommonPageVO<PoliceOrgListVO>> policeOrgList(@RequestBody(required = false) PoliceOrgListQueryDTO query) {
        return handle(() -> CommonResult.data(dutyPostureService.policeOrgList(query)));
    }

    @Operation(summary = "导出一所一档信息")
    @PostMapping("/police/org/export")
    public CommonResult<Boolean> exportPoliceOrg(@RequestBody List<PoliceOrgListVO> orgList) {
        return handle(() -> CommonResult.data(dutyPostureService.exportPoliceOrg(orgList)));
    }

    @Operation(summary = "部门注销")
    @PostMapping("/police/org/delete")
    public CommonResult<Boolean> deletePoliceOrg(@RequestBody IdDTO dto) {
        return handle(() -> CommonResult.data(dutyPostureService.deletePoliceOrg(dto.getId())));
    }

    @Operation(summary = "统计各单位在岗警力（民警、辅警）数量")
    @GetMapping("/on_duty/stat")
    public CommonResult<EmpCountVO> getOnDutyPoliceStat() {
        return handle(() -> CommonResult.data(dutyPostureService.getOnDutyPoliceStat()));
    }

    @Operation(summary = "获取职级字典列表")
    @GetMapping("/job/title/dict")
    public CommonResult<List<BaseDictVO>> getJobTitleDict() {
        return handle(() -> CommonResult.data(dutyPostureService.getRankLevelDict()));
    }

    @Operation(summary = "查看警员档案信息")
    @PostMapping("/police/profile")
    public CommonResult<PoliceProfileDTO> getPoliceProfile(@RequestBody PoliceProfileReqDTO dto) {
        return handle(() -> CommonResult.data(dutyPostureService.getPoliceProfile(dto)));
    }

    @Operation(summary = "保存警员档案信息")
    @PostMapping("/police/save")
    public CommonResult<Boolean> savePoliceProfile(@RequestBody PoliceProfileDTO profileDTO) {
        return handle(() -> CommonResult.data(dutyPostureService.savePoliceProfile(profileDTO)));
    }

    @Operation(summary = "新建/编辑警务组织（部门）", description = "创建新的警务组织或编辑现有组织。当id字段为空时执行新建操作，不为空时执行编辑操作")
    @PostMapping("/police/org")
    public CommonResult<String> createPoliceOrg(@RequestBody PoliceOrgCreateDTO createDTO) {
        return handle(() -> CommonResult.data(dutyPostureService.createPoliceOrg(createDTO)));
    }

    @Operation(summary = "查看部门详情")
    @PostMapping("/police/org/detail")
    public CommonResult<PoliceOrgCreateDTO> getPoliceOrgDetail(@RequestBody @Parameter(description = "部门ID", required = true) IdDTO dto) {
        return handle(() -> CommonResult.data(dutyPostureService.getPoliceOrgDetail(dto.getId())));
    }

    @Operation(summary = "统计各组织各谈话类型数量", description = "支持数据权限功能：管理员可查看全部数据，普通用户只能查看当前单位及下级单位的数据")
    @PostMapping("/talking/stat/by-org")
    public CommonResult<List<OrgTalkingTypeStatVO>> statOrgTalkingType(@RequestBody TalkingTypeStatQueryDTO queryDTO) {
        return handle(() -> CommonResult.data(dutyPostureService.statOrgTalkingType(queryDTO)));
    }

    @Operation(summary = "队伍风险隐患统计", description = "支持按年份查询，支持按年、季度、月统计，支持数据权限功能")
    @PostMapping("/team/risk/stat")
    public CommonResult<List<TeamRiskStatVO>> getTeamRiskStat(@RequestBody TeamRiskStatQueryDTO queryDTO) {
        return handle(() -> CommonResult.data(dutyPostureService.getTeamRiskStat(queryDTO)));
    }

    @Operation(summary = "查询所有警员基本信息", description = "获取所有警员的id、编号、姓名信息")
    @PostMapping("/police/basic/list")
    public CommonResult<List<PoliceBasicInfoVO>> getAllPoliceBasicInfo() {
        return handle(() -> CommonResult.data(dutyPostureService.getAllPoliceBasicInfo()));
    }
}
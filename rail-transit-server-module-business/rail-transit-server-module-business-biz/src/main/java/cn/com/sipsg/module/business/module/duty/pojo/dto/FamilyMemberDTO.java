package cn.com.sipsg.module.business.module.duty.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR> lixt
 * @date : 2025-01-15
 * @Description: 家庭成员信息DTO
 */
@Data
@Schema(description = "家庭成员信息DTO")
public class FamilyMemberDTO {
    @Schema(description = "ID，新增时为空", example = "")
    private String id;

    @Schema(description = "警员ID", example = "")
    private String policeId;

    @Schema(description = "关系", example = "配偶")
    @NotBlank(message = "关系不能为空")
    private String relationship;

    @Schema(description = "姓名", example = "李四")
    @NotBlank(message = "姓名不能为空")
    private String name;
}
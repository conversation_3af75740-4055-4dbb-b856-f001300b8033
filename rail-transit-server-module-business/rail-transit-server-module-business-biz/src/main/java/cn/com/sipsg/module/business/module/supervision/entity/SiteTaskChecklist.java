package cn.com.sipsg.module.business.module.supervision.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

@Data
@TableName("tbl_site_task_checklist")
public class SiteTaskChecklist {
    /** 记录唯一标识主键 */
    @TableId("id")
    private Long id;
    /** 记录创建时间 */
    @TableField("create_time")
    private Long createTime;
    /** 记录最后更新时间 */
    @TableField("update_time")
    private Long updateTime;
    /** 创建者用户ID */
    @TableField("create_user_id")
    private String createUserId;
    /** 最后更新者用户ID */
    @TableField("update_user_id")
    private String updateUserId;
    /** 是否有效 */
    @TableField("available")
    private Boolean available;
    /** 任务ID */
    @TableField("task_id")
    private Long taskId;
    /** 巡检子计划ID */
    @TableField("subplan_id")
    private Long subplanId;
    /** 子计划名称 */
    @TableField("subplan_name")
    private String subplanName;
    /** 检查单ID */
    @TableField("checklist_id")
    private Long checklistId;
    /** 检查单名称 */
    @TableField("checklist_name")
    private String checklistName;
    /** 填表人 */
    @TableField("fill_user_id")
    private Long fillUserId;
    /** 填表时间 */
    @TableField("fill_time")
    private Long fillTime;
    /** 检查单状态 */
    @TableField("status")
    private Integer status;
} 
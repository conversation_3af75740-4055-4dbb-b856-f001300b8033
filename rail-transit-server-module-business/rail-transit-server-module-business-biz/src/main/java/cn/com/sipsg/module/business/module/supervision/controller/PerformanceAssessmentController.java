package cn.com.sipsg.module.business.module.supervision.controller;

import cn.com.sipsg.common.pojo.CommonResult;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.web.core.controller.BaseController;
import cn.com.sipsg.module.business.module.supervision.entity.PersonnelAttention;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.*;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.*;
import cn.com.sipsg.module.business.module.supervision.service.PerformanceScoreService;
import cn.com.sipsg.module.business.module.supervision.service.StatisticsCompreService;
import cn.com.sipsg.module.business.module.supervision.service.StatisticsInvestigationService;
import cn.com.sipsg.module.business.module.supervision.service.StatisticsTraceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 业务工作考核控制器
 * 提供业务工作考核相关接口
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Tag(name = "业务工作考核", description = "业务工作考核相关接口")
@RestController
@RequestMapping("/performance/assessment")
@RequiredArgsConstructor
@Slf4j
public class PerformanceAssessmentController extends BaseController {

    private final PerformanceScoreService performanceScoreService;
    private final StatisticsCompreService statisticsCompreService;
    private final StatisticsTraceService statisticsTraceService;
    private final StatisticsInvestigationService statisticsInvestigationService;

    /**
     * 业务考核统计汇总
     * 本月总分、部门排名、任务完成率、出勤率
     *
     * @return PerformanceAssessmentSummaryVO
     */
    @Operation(summary = "业务考核统计汇总", description = "本接口用于获取当前用户所在单位本月的业务考核统计汇总信息，包括本月总分、部门排名、任务完成率、出勤率等关键绩效指标，便于单位及时掌握整体业务考核情况。")
    @GetMapping("/current-org/assessment-summary")
    public CommonResult<PerformanceAssessmentSummaryVO> getCurrentOrgAssessmentSummary() {
        return handle(() -> CommonResult.data(performanceScoreService.getCurrentOrgAssessmentSummary()));
    }

    /**
     * 【单位绩效排名】查询所有单位的绩效分数排名
     * 典型场景：绩效考核、单位绩效对比等。
     *
     * @return 单位绩效排名列表
     */
    @Operation(summary = "查询所有单位的绩效分数排名", description = "本接口用于查询所有单位的绩效分数排名，适用于绩效考核、单位绩效对比等场景，帮助管理者了解各单位的绩效得分及排名情况，便于横向对比和绩效改进。")
    @GetMapping("/org-score-rank")
    public CommonResult<List<DepartmentScoreRankVO>> getOrgScoreRank() {
        return handle(() -> {
            List<DepartmentScoreRankVO> rankList = performanceScoreService.getOrgScoreRank();
            log.info("[绩效考核] 所有单位绩效排名: {}条", rankList.size());
            return CommonResult.data(rankList);
        });
    }

    /**
     * 【日常数据统计】分页查询
     * 用于分页获取当前用户所在单位及下级单位的全部日常工作数据，或按条件（时间、单位等）统计。
     * 典型场景：业务考核、日常工作量分析等。
     *
     * @param dto 查询参数DTO
     * @return 日常数据分页列表
     */
    @Operation(summary = "【日常数据统计】分页查询", description = "本接口用于分页获取当前用户所在单位及下级单位的全部日常工作数据，支持按时间、单位等条件统计，适用于业务考核、日常工作量分析等场景，便于数据的分层管理和统计分析。")
    @PostMapping("/statistics-compare/query")
    public CommonResult<CommonPageVO<StatisticsComparePageVO>> pageStatisticsCompare(@RequestBody StatisticsComparePageReqDTO dto) {
        return handle(() -> CommonResult.data(
                statisticsCompreService.pageStatisticsCompareByCondition(dto)
        ));
    }

    /**
     * 【巡逻情况统计】分页查询
     * 用于分页获取当前用户所在单位及下级单位的全部巡逻情况数据，或按条件（时间等）统计。
     * 典型场景：警力调度、巡逻任务分析等。
     *
     * @param dto 查询参数DTO
     * @return 巡逻情况分页列表
     */
    @Operation(summary = "【巡逻情况统计】分页查询", description = "本接口用于分页获取当前用户所在单位及下级单位的全部巡逻情况数据，支持按时间等条件统计，适用于警力调度、巡逻任务分析等场景，便于掌握巡逻工作开展情况。")
    @PostMapping("/patrol/page")
    public CommonResult<CommonPageVO<PatrolPageVO>> pagePatrolForCurrentUser(@RequestBody PatrolPageReqDTO dto) {
        return handle(() -> CommonResult.data(statisticsCompreService.pagePatrolForCurrentUser(dto)));
    }

    /**
     * 【跟防情况统计】分页查询
     * 用于分页获取当前用户所在单位及下级单位的全部跟防情况数据，或按条件（时间范围、单位id）统计。
     * 典型场景：警力跟防管理、执法监督等。
     *
     * @param dto 查询参数DTO
     * @return 跟防情况分页数据
     */
    @Operation(summary = "【跟防情况统计】分页查询", description = "本接口用于分页获取当前用户所在单位及下级单位的全部跟防情况数据，支持按时间范围、单位id等条件统计，适用于警力跟防管理、执法监督等场景，便于跟踪和分析跟防任务执行情况。")
    @PostMapping("/trace/page")
    public CommonResult<CommonPageVO<StatisticsTracePageVO>> pageTraceForCurrentUser(@RequestBody TracePageReqDTO dto) {
        return handle(() -> CommonResult.data(statisticsTraceService.pageTraceForCurrentUser(dto)));
    }

    /**
     * 【盘查前科情况统计】分页查询
     * 用于分页获取当前用户所在单位及下级单位的全部盘查前科情况数据，或按条件（数据时间、单位id、被盘查人姓名/身份证号）统计。
     * 典型场景：治安盘查、重点人员管理等。
     *
     * @param dto 查询参数DTO
     * @return 分页VO
     */
    @Operation(summary = "【盘查前科情况统计】分页查询", description = "本接口用于分页获取当前用户所在单位及下级单位的全部盘查前科情况数据，支持按数据时间、单位id、被盘查人姓名/身份证号等条件统计，适用于治安盘查、重点人员管理等场景，便于分析盘查工作成效。")
    @PostMapping("/investigation/page")
    public CommonResult<CommonPageVO<InvestigationPageVO>> pageInvestigationForCurrentUser(@RequestBody InvestigationPageReqDTO dto) {
        return handle(() -> CommonResult.data(statisticsInvestigationService.pageInvestigationForCurrentUser(dto)));
    }

    /**
     * 【抓获统计】分页查询
     * 用于分页获取当前用户所在单位及下级单位的抓获统计数据，支持按抓获单位、预警来源、抓获类型、抓获地点等条件筛选。
     * 典型场景：抓捕情况统计、重点人员管理等。
     *
     * @param dto 查询参数DTO
     * @return 抓获统计分页列表
     */
    @Operation(summary = "【抓获统计】分页查询", description = "本接口用于分页获取当前用户所在单位及下级单位的抓获统计数据，支持按抓获单位、预警来源、抓获类型、抓获地点等条件筛选，适用于抓捕情况统计、重点人员管理等场景，便于灵活查询和分析抓获数据。")
    @PostMapping("/captured/page")
    public CommonResult<CommonPageVO<PersonnelCapturedPageVO>> pagePersonnelCapturedForCurrentUser(@RequestBody CapturedPageQueryReqDTO dto) {
        return handle(() -> CommonResult.data(statisticsCompreService.pagePersonnelCapturedForCurrentUser(dto)));
    }

    /**
     * 【关注统计】分页查询
     * 用于分页获取当前用户所在单位及下级单位的关注统计数据，支持可选条件查询。
     * 当不传入查询条件时，返回全部数据；传入条件时，按条件筛选。
     * 典型场景：关注人员管理、重点人员分析等。
     *
     * @param dto 查询参数DTO
     * @return 关注统计分页列表
     */
    @Operation(summary = "【关注统计】分页查询", description = "本接口用于分页获取当前用户所在单位及下级单位的关注统计数据，支持按处置单位、预警来源、关注方式、人员类型等条件筛选，适用于关注人员管理、重点人员分析等场景，便于灵活查询和分析关注对象。")
    @PostMapping("/focus/page")
    public CommonResult<CommonPageVO<PersonnelAttention>> pagePersonnelAttentionForCurrentUser(@RequestBody FocusPageQueryReqDTO dto) {
        return handle(() -> CommonResult.data(statisticsCompreService.pagePersonnelAttentionForCurrentUser(dto)));
    }

    /**
     * 【每日报备详情】查询
     * 用于获取指定每日报备的详细信息。
     * 典型场景：每日报备编辑、查看详情等。
     *
     * @param dto 查询参数DTO
     * @return 每日报备详情
     */
    @Operation(summary = "【每日报备详情】查询", description = "本接口用于获取指定每日报备的详细信息，适用于每日报备编辑、查看详情等场景，便于获取完整的报备数据进行编辑或查看。")
    @PostMapping("/daily-report/detail")
    public CommonResult<DailyReportDetailVO> getDailyReportDetail(@RequestBody @Valid DailyReportDetailReqDTO dto) {
        return handle(() -> CommonResult.data(statisticsCompreService.getDailyReportDetail(dto.getId())));
    }

    /**
     * 【每日报备编辑】保存或更新
     * 用于保存或更新每日报备信息。
     * 典型场景：每日报备数据录入、修改等。
     *
     * @param dto 每日报备编辑请求DTO
     * @return 操作结果
     */
    @Operation(summary = "【每日报备编辑】保存或更新", description = "本接口用于保存或更新每日报备信息，适用于每日报备数据录入、修改等场景，支持新增和编辑两种操作模式。")
    @PostMapping("/daily-report/save")
    public CommonResult<Boolean> saveOrUpdateDailyReport(@RequestBody @Valid DailyReportEditReqDTO dto) {
        return handle(() -> CommonResult.data(statisticsCompreService.saveOrUpdateDailyReport(dto)));
    }

    /**
     * 【每日报备】分页查询
     * 用于分页获取当前用户所在单位及下级单位的每日报备数据，支持按时间范围查询。
     * 数据权限：管理员可查看全部数据，普通用户只能查看当前单位及下级单位数据。
     * 典型场景：每日报备数据统计、历史数据查询等。
     *
     * @param dto 分页查询参数DTO
     * @return 每日报备分页列表
     */
    @Operation(summary = "【每日报备】分页查询", description = "本接口用于分页获取每日报备数据，支持按时间范围查询，管理员可查看全部数据，普通用户只能查看当前单位及下级单位数据，适用于每日报备数据统计、历史数据查询等场景。")
    @PostMapping("/daily-report/page")
    public CommonResult<CommonPageVO<DailyReportVO>> pageDailyReport(@RequestBody @Valid DailyReportPageReqDTO dto) {
        return handle(() -> CommonResult.data(statisticsCompreService.pageDailyReport(dto)));
    }

    /**
     * 【日常数据编辑】保存或更新
     * 用于保存或更新日常数据信息。
     * 典型场景：日常数据录入、修改等。
     *
     * @param dto 日常数据保存/更新DTO
     * @return 操作结果
     */
    @Operation(summary = "【日常数据编辑】保存或更新", description = "本接口用于保存或更新日常数据信息，适用于日常数据录入、修改等场景，支持新增和编辑两种操作模式。")
    @PostMapping("/statistics-compre/save")
    public CommonResult<Boolean> saveOrUpdateStatisticsCompare(@RequestBody @Valid StatisticsCompreSaveDTO dto) {
        return handle(() -> CommonResult.data(statisticsCompreService.saveOrUpdateStatisticsCompre(dto)));
    }

    /**
     * 【日常数据详情】查询
     * 用于获取指定日常数据的详细信息。
     * 典型场景：日常数据编辑、查看详情等。
     *
     * @param dto 日常数据详情查询请求参数
     * @return 日常数据详情
     */
    @Operation(summary = "【日常数据详情】查询", description = "本接口用于获取指定日常数据的详细信息，适用于日常数据编辑、查看详情等场景，便于获取完整的数据进行编辑或查看。")
    @PostMapping("/statistics-compre/detail")
    public CommonResult<StatisticsCompreSaveDTO> getStatisticsCompareDetail(@RequestBody @Valid StatisticsCompreDetailReqDTO dto) {
        return handle(() -> CommonResult.data(statisticsCompreService.getStatisticsCompreDetail(dto.getId())));
    }

    /**
     * 【巡逻信息详情】查询
     * 用于获取指定巡逻信息的详细信息。
     * 典型场景：巡逻信息编辑、查看详情等。
     *
     * @param dto 巡逻信息详情查询请求参数
     * @return 巡逻信息详情
     */
    @Operation(summary = "【巡逻信息详情】查询", description = "本接口用于获取指定巡逻信息的详细信息，适用于巡逻信息编辑、查看详情等场景，便于获取完整的巡逻数据进行编辑或查看。")
    @PostMapping("/patrol/detail")
    public CommonResult<PatrolSaveDTO> getPatrolDetail(@RequestBody @Valid PatrolDetailReqDTO dto) {
        return handle(() -> CommonResult.data(statisticsCompreService.getPatrolDetail(dto.getId())));
    }

    /**
     * 【巡逻信息编辑】保存或更新
     * 用于保存或更新巡逻信息，支持多个巡逻记录的批量操作。
     * 典型场景：巡逻信息录入、修改等。
     *
     * @param dto 巡逻信息保存/更新DTO
     * @return 操作结果
     */
    @Operation(summary = "【巡逻信息编辑】保存或更新", description = "本接口用于保存或更新巡逻信息，支持多个巡逻记录的批量操作，适用于巡逻信息录入、修改等场景，支持新增和编辑两种操作模式。")
    @PostMapping("/patrol/save")
    public CommonResult<Boolean> saveOrUpdatePatrol(@RequestBody @Valid PatrolSaveDTO dto) {
        return handle(() -> CommonResult.data(statisticsCompreService.saveOrUpdatePatrol(dto)));
    }

    /**
     * 【日常数据删除】删除
     * 用于删除指定的日常数据记录。
     * 典型场景：数据清理、错误数据删除等。
     *
     * @param dto 删除请求参数
     * @return 操作结果
     */
    @Operation(summary = "【日常数据删除】删除", description = "本接口用于删除指定的日常数据记录，适用于数据清理、错误数据删除等场景，删除操作不可逆，请谨慎使用。")
    @PostMapping("/statistics-compre/delete")
    public CommonResult<Boolean> deleteStatisticsCompre(@RequestBody @Valid DeleteReqDTO dto) {
        return handle(() -> CommonResult.data(statisticsCompreService.deleteStatisticsCompre(dto.getId())));
    }

    /**
     * 【每日报备删除】删除
     * 用于删除指定的每日报备记录。
     * 典型场景：数据清理、错误报备删除等。
     *
     * @param dto 删除请求参数
     * @return 操作结果
     */
    @Operation(summary = "【每日报备删除】删除", description = "本接口用于删除指定的每日报备记录，适用于数据清理、错误报备删除等场景，删除操作不可逆，请谨慎使用。")
    @PostMapping("/daily-report/delete")
    public CommonResult<Boolean> deleteDailyReport(@RequestBody @Valid DeleteReqDTO dto) {
        return handle(() -> CommonResult.data(statisticsCompreService.deleteDailyReport(dto.getId())));
    }

    /**
     * 【巡逻情况删除】删除
     * 用于删除指定的巡逻情况记录。
     * 典型场景：数据清理、错误巡逻记录删除等。
     *
     * @param dto 删除请求参数
     * @return 操作结果
     */
    @Operation(summary = "【巡逻情况删除】删除", description = "本接口用于删除指定的巡逻情况记录，适用于数据清理、错误巡逻记录删除等场景，删除操作不可逆，请谨慎使用。")
    @PostMapping("/patrol/delete")
    public CommonResult<Boolean> deletePatrol(@RequestBody @Valid DeleteReqDTO dto) {
        return handle(() -> CommonResult.data(statisticsCompreService.deletePatrol(dto.getId())));
    }

    /**
     * 【跟防情况删除】删除
     * 用于删除指定的跟防情况记录。
     * 典型场景：数据清理、错误跟防记录删除等。
     *
     * @param dto 删除请求参数
     * @return 操作结果
     */
    @Operation(summary = "【跟防情况删除】删除", description = "本接口用于删除指定的跟防情况记录，适用于数据清理、错误跟防记录删除等场景，删除操作不可逆，请谨慎使用。")
    @PostMapping("/trace/delete")
    public CommonResult<Boolean> deleteTrace(@RequestBody @Valid DeleteReqDTO dto) {
        return handle(() -> CommonResult.data(statisticsTraceService.deleteTrace(dto.getId())));
    }

    /**
     * 【盘查前科情况编辑】保存或更新
     * 用于保存或更新盘查前科情况信息。
     * 典型场景：盘查前科数据录入、修改等。
     *
     * @param dto 盘查前科情况保存/更新DTO
     * @return 操作结果
     */
    @Operation(summary = "【盘查前科情况编辑】保存或更新", description = "本接口用于保存或更新盘查前科情况信息，适用于盘查前科数据录入、修改等场景，支持新增和编辑两种操作模式。")
    @PostMapping("/investigation/save")
    public CommonResult<Boolean> saveOrUpdateInvestigation(@RequestBody @Valid InvestigationSaveDTO dto) {
        return handle(() -> CommonResult.data(statisticsInvestigationService.saveOrUpdateInvestigation(dto)));
    }

    /**
     * 【盘查前科情况详情】查询
     * 用于获取指定盘查前科情况的详细信息。
     * 典型场景：盘查前科情况编辑、查看详情等。
     *
     * @param dto 盘查前科情况详情查询请求参数
     * @return 盘查前科情况详情
     */
    @Operation(summary = "【盘查前科情况详情】查询", description = "本接口用于获取指定盘查前科情况的详细信息，适用于盘查前科情况编辑、查看详情等场景，便于获取完整的盘查前科数据进行编辑或查看。")
    @PostMapping("/investigation/detail")
    public CommonResult<InvestigationSaveDTO> getInvestigationDetail(@RequestBody @Valid InvestigationDetailReqDTO dto) {
        return handle(() -> CommonResult.data(statisticsInvestigationService.getInvestigationDetail(dto)));
    }

    /**
     * 【盘查前科删除】删除
     * 用于删除指定的盘查前科记录。
     * 典型场景：数据清理、错误盘查记录删除等。
     *
     * @param dto 删除请求参数
     * @return 操作结果
     */
    @Operation(summary = "【盘查前科删除】删除", description = "本接口用于删除指定的盘查前科记录，适用于数据清理、错误盘查记录删除等场景，删除操作不可逆，请谨慎使用。")
    @PostMapping("/investigation/delete")
    public CommonResult<Boolean> deleteInvestigation(@RequestBody @Valid DeleteReqDTO dto) {
        return handle(() -> CommonResult.data(statisticsInvestigationService.deleteInvestigation(dto.getId())));
    }

    /**
     * 【抓获统计删除】删除
     * 用于删除指定的抓获统计记录。
     * 典型场景：数据清理、错误抓获记录删除等。
     *
     * @param dto 删除请求参数
     * @return 操作结果
     */
    @Operation(summary = "【抓获统计删除】删除", description = "本接口用于删除指定的抓获统计记录，适用于数据清理、错误抓获记录删除等场景，删除操作不可逆，请谨慎使用。")
    @PostMapping("/captured/delete")
    public CommonResult<Boolean> deleteCaptured(@RequestBody @Valid DeleteReqDTO dto) {
        return handle(() -> CommonResult.data(statisticsCompreService.deleteCaptured(dto.getId())));
    }

    /**
     * 【关注统计删除】删除
     * 用于删除指定的关注统计记录。
     * 典型场景：数据清理、错误关注记录删除等。
     *
     * @param dto 删除请求参数
     * @return 操作结果
     */
    @Operation(summary = "【关注统计删除】删除", description = "本接口用于删除指定的关注统计记录，适用于数据清理、错误关注记录删除等场景，删除操作不可逆，请谨慎使用。")
    @PostMapping("/focus/delete")
    public CommonResult<Boolean> deleteFocus(@RequestBody @Valid DeleteReqDTO dto) {
        return handle(() -> CommonResult.data(statisticsCompreService.deleteFocus(dto.getId())));
    }

    /**
     * 【抓获记录编辑】保存或更新
     * 用于保存或更新抓获记录信息。
     * 典型场景：抓获记录录入、修改等。
     *
     * @param dto 抓获记录保存/更新DTO
     * @return 操作结果
     */
    @Operation(summary = "【抓获记录编辑】保存或更新", description = "本接口用于保存或更新抓获记录信息，适用于抓获记录录入、修改等场景，支持新增和编辑两种操作模式。")
    @PostMapping("/captured/save")
    public CommonResult<Boolean> saveOrUpdatePersonnelCaptured(@RequestBody @Valid PersonnelCapturedSaveDTO dto) {
        return handle(() -> CommonResult.data(statisticsCompreService.saveOrUpdatePersonnelCaptured(dto)));
    }

    /**
     * 【抓获记录详情】查询
     * 用于获取指定抓获记录的详细信息。
     * 典型场景：抓获记录编辑、查看详情等。
     *
     * @param dto 抓获记录详情查询请求参数
     * @return 抓获记录详情
     */
    @Operation(summary = "【抓获记录详情】查询", description = "本接口用于获取指定抓获记录的详细信息，适用于抓获记录编辑、查看详情等场景，便于获取完整的抓获数据进行编辑或查看。")
    @PostMapping("/captured/detail")
    public CommonResult<PersonnelCapturedSaveDTO> getPersonnelCapturedDetail(@RequestBody @Valid PersonnelCapturedDetailReqDTO dto) {
        return handle(() -> CommonResult.data(statisticsCompreService.getPersonnelCapturedDetail(dto.getId())));
    }

    /**
     * 【关注记录编辑】保存或更新
     * 用于保存或更新关注记录信息。
     * 典型场景：关注记录录入、修改等。
     *
     * @param dto 关注记录保存/更新DTO
     * @return 操作结果
     */
    @Operation(summary = "【关注记录编辑】保存或更新", description = "本接口用于保存或更新关注记录信息，适用于关注记录录入、修改等场景，支持新增和编辑两种操作模式。")
    @PostMapping("/focus/save")
    public CommonResult<Boolean> saveOrUpdatePersonnelAttention(@RequestBody @Valid PersonnelAttentionSaveDTO dto) {
        return handle(() -> CommonResult.data(statisticsCompreService.saveOrUpdatePersonnelAttention(dto)));
    }

    /**
    /**
     * 【关注记录详情】查询
     * 用于获取指定关注记录的详细信息。
     * 典型场景：关注记录编辑、查看详情等。
     *
     * @param dto 关注记录详情查询请求参数
     * @return 关注记录详情
     */
    @Operation(summary = "【关注记录详情】查询", description = "本接口用于获取指定关注记录的详细信息，适用于关注记录编辑、查看详情等场景，便于获取完整的关注数据进行编辑或查看。")
    @PostMapping("/focus/detail")
    public CommonResult<PersonnelAttentionSaveDTO> getPersonnelAttentionDetail(@RequestBody @Valid PersonnelAttentionDetailReqDTO dto) {
        return handle(() -> CommonResult.data(statisticsCompreService.getPersonnelAttentionDetail(dto.getId())));
    }

    /**
     * 【站点信息查询】查询所有站点信息
     * 用于获取所有站点的编号和名称信息。
     * 典型场景：下拉选择框、站点选择等。
     *
     * @return 站点信息列表
     */
    @Operation(summary = "【站点信息查询】查询所有站点信息", description = "本接口用于查询所有站点的编号和名称信息，适用于下拉选择框、站点选择等场景，便于用户选择站点。")
    @PostMapping("/stations/all")
    public CommonResult<List<StationInfoVO>> getAllStations() {
        return handle(() -> CommonResult.data(statisticsCompreService.getAllStations()));
    }

    /**
     * 【前科类型枚举】查询所有前科类型
     * 用于获取所有前科类型的编号和名称信息。
     * 典型场景：下拉选择框、前科类型选择等。
     *
     * @return 前科类型列表
     */
    @Operation(summary = "【前科类型枚举】查询所有前科类型", description = "本接口用于查询所有前科类型的编号和名称信息，适用于下拉选择框、前科类型选择等场景，便于用户选择前科类型。")
    @GetMapping("/criminal-record-types")
    public CommonResult<List<CriminalRecordTypeVO>> getCriminalRecordTypes() {
        return CommonResult.data(performanceScoreService.getAllCriminalRecordTypes());
    }
}
package cn.com.sipsg.module.business.module.supervision.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.sql.Timestamp;

/**
 * GPS采集的警车实时定位数据
 */
@Data
@TableName("tbl_gps_police_car")
public class GpsPoliceCar {
    @TableId(type = IdType.AUTO)
    private Long id; // 主键

    @TableField("vehicle_id")
    private String vehicleId; // 警车唯一标识

    @TableField("registration_num")
    private String registrationNum; // 车牌号

    @TableField("sim")
    private String sim; // 车载设备 SIM 卡号

    @TableField("team_name")
    private String teamName; // 所属中队名称

    @TableField("type_name")
    private String typeName; // 警车类型

    @TableField("data_state")
    private String dataState; // 数据状态，如正常、失联等

    @TableField("gps_time")
    private Timestamp gpsTime; // GPS 上报时间

    @TableField("longitude")
    private String longitude; // 经度坐标

    @TableField("latitude")
    private String latitude; // 纬度坐标

    @TableField("speed")
    private String speed; // 当前速度

    @TableField("direction")
    private String direction; // 方向（角度制）

    @TableField("status_word")
    private String statusWord; // 状态字

    @TableField("message_time")
    private Timestamp messageTime; // 消息生成时间

    @TableField("distance_to_end")
    private String distanceToEnd; // 距离目的地距离

    @TableField("write_time")
    private Timestamp writeTime; // 数据写入时间

    @TableField("center_name")
    private String centerName; // 中心名称（业务来源单位）

    @TableField("company_name")
    private String companyName; // 所属公司名称

    @TableField("unit_name")
    private String unitName; // 所属单位名称

    @TableField("team_code")
    private String teamCode; // 中队编码

    @TableField("topic")
    private String topic; // 消息主题（MQTT等）

    @TableField("create_time")
    private Long createTime; // 记录创建时间（毫秒时间戳）

    @TableField("update_time")
    private Long updateTime; // 记录更新时间（毫秒时间戳）
} 
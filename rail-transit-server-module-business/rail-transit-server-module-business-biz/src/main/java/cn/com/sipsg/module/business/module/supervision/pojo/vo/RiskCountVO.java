package cn.com.sipsg.module.business.module.supervision.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 风险数量统计视图对象
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Data
@Schema(description = "治安隐患查控统计VO")
public class RiskCountVO {
    @Schema(description = "查控点数量")
    private Integer checkPointCount;
    @Schema(description = "安检任务数量")
    private Integer inspectionTaskCount;
    @Schema(description = "隐患处理数量")
    private Integer riskHandleCount;
}

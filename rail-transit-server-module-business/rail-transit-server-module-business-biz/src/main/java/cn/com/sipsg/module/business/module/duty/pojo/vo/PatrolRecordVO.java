package cn.com.sipsg.module.business.module.duty.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 盘查记录视图对象
 */
@Data
@Schema(description = "盘查记录视图对象")
public class PatrolRecordVO {
    
    @Schema(description = "ID")
    private Long id;
    
    @Schema(description = "盘查编号")
    private String patrolNo;
    
    @Schema(description = "民警ID")
    private Long policeId;
    
    @Schema(description = "民警编号")
    private String policeNo;
    
    @Schema(description = "民警名称")
    private String policeName;
    
    @Schema(description = "盘查单位ID")
    private String orgId;
    
    @Schema(description = "盘查单位名称")
    private String orgName;
    
    @Schema(description = "盘查时间")
    private LocalDateTime patrolTime;
    
    @Schema(description = "盘查地点")
    private String location;
    
    @Schema(description = "被盘查人数")
    private Integer personCount;
    
    @Schema(description = "查获物品数量")
    private Integer itemCount;
    
    @Schema(description = "备注")
    private String remark;
    
    @Schema(description = "创建时间")
    private LocalDateTime createTime;
}

package cn.com.sipsg.module.business.module.supervision.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 综合统计-巡逻情况
 */
@Data
@TableName("tbl_statistics_daily_patrol")
public class StatisticsDailyPatrol implements Serializable {
    /** 主键id */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /** 创建人id */
    private String createUserId;

    /** 创建时间,unix时间戳,精确到毫秒 */
    private LocalDateTime createTime;

    /** 修改人id */
    private String updateUserId;

    /** 修改时间,unix时间戳,精确到毫秒 */
    private LocalDateTime updateTime;

    /** 是否有效 */
    private Boolean available;

    /** 填报时间,unix时间戳,精确到毫秒 */
    private LocalDateTime formTime;

    /** 单位ID */
    private String policeOrgId;

    /** 巡逻车呼号 */
    private String call;

    /** 车牌号 */
    private String vehicle;

    /** 巡逻区域 */
    private String area;

    /** 备注 */
    private String memos;
}
package cn.com.sipsg.module.business.module.duty.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> lixt
 * @date : 2025-05-08 16:50:12
 * @Description: 安检-处置人信息
 */
@Data
@TableName("tbl_security_alarm_handle")
public class SecurityAlarmHandle {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;  // id

    @TableField("create_user_id")
    private String createUserId;  // 创建人

    @TableField("create_time")
    private LocalDateTime createTime;  // 创建时间

    @TableField("update_user_id")
    private String updateUserId;  // 更新人

    @TableField("update_time")
    private LocalDateTime updateTime;  // 更新时间

    @TableField("available")
    private Boolean available;  // 是否有效

    @TableField("alarm_id")
    private String alarmId;  // 预警id

    @TableField("handle_emp_name")
    private String handleEmpName;  // 处置人姓名

    @TableField("handle_emp_phone")
    private String handleEmpPhone;  // 处置人手机号码

    @TableField("handle_emp_code")
    private String handleEmpCode;  // 处置人编码

    @TableField("handle_emp_type")
    private String handleEmpType;  // 处置人类型
}
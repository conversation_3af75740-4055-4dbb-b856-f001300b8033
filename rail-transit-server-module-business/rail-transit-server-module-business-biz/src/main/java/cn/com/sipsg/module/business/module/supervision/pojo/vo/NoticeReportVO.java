package cn.com.sipsg.module.business.module.supervision.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 通报信息VO
 */
@Data
@Schema(description = "通报信息VO")
public class NoticeReportVO {

    @Schema(description = "主键")
    private String id;

    @Schema(description = "创建人")
    private String createUserId;

    @Schema(description = "创建人名称")
    private String createUserName;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新人")
    private String updateUserId;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "通报标题")
    private String title;

    @Schema(description = "执法单位ID")
    private String orgId;

    @Schema(description = "执法单位名称")
    private String orgName;

    @Schema(description = "执法时间")
    private LocalDateTime lawTime;

    @Schema(description = "不规范内容")
    private String nonComplianceContent;

    @Schema(description = "附件，存储文件url或路径")
    private String attachment;

    @Schema(description = "接收单位ID")
    private String receiveOrgId;

    @Schema(description = "接收单位名称")
    private String receiveOrgName;

    @Schema(description = "下发人ID（警员ID）")
    private String issueUserId;

    @Schema(description = "下发人姓名")
    private String issueUserName;

    @Schema(description = "接收人ID（警员ID）")
    private String receiveUserId;

    @Schema(description = "接收人姓名")
    private String receiveUserName;
}
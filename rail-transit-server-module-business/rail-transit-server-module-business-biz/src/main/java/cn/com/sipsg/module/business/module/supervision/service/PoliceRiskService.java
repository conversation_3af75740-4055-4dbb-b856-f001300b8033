package cn.com.sipsg.module.business.module.supervision.service;

import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.PoliceRiskPageReqDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.PoliceRiskCreateReqDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.PoliceRiskDetailVO;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.PoliceRiskVO;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.RiskCountVO;

import java.util.List;

/**
 * 警务风险信息服务接口
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
public interface PoliceRiskService {

    /**
     * 获取当前用户所在组织内所有用户一周内的风险数量
     *
     * @return 组织内一周内的风险数量
     */
    RiskCountVO getOrgWeeklyRiskCount();

    /**
     * 获取当前用户所在组织内所有用户一周内的风险列表
     *
     * @return 组织内一周内的风险列表
     */
    List<PoliceRiskVO> getOrgWeeklyRisks();

    /**
     * 隐患分页多条件查询
     */
    CommonPageVO<PoliceRiskVO> pageRiskList(PoliceRiskPageReqDTO reqDTO);

    /**
     * 新增隐患上报
     */
    Boolean createRisk(PoliceRiskCreateReqDTO reqDTO);

    /**
     * 修改隐患信息
     */
    Boolean updateRisk(PoliceRiskCreateReqDTO reqDTO);

    /**
     * 根据ID查询隐患详情
     */
    PoliceRiskDetailVO getRiskDetail(String id);
}

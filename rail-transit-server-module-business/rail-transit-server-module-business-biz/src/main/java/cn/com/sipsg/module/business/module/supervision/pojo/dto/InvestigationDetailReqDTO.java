package cn.com.sipsg.module.business.module.supervision.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotBlank;

/**
 * 盘查前科情况详情查询请求参数
 */
@Data
@Schema(description = "盘查前科情况详情查询请求参数")
public class InvestigationDetailReqDTO {
    
    @Schema(description = "盘查前科情况ID", required = true, example = "1234567890abcdef12345678")
    @NotBlank(message = "盘查前科情况ID不能为空")
    private String id;
}
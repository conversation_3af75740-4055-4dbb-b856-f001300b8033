package cn.com.sipsg.module.business.module.duty.mapper;

import cn.com.sipsg.module.business.module.duty.entity.BasePoliceEmpFamily;
import cn.com.sipsg.common.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> lixt
 * @date : 2025-01-15
 * @Description: 警员家庭成员关联表Mapper
 */
@Mapper
public interface BasePoliceEmpFamilyMapper extends BaseMapperX<BasePoliceEmpFamily> {

    /**
     * 根据警员ID查询家庭成员列表
     *
     * @param policeEmpId 警员ID
     * @return 家庭成员列表
     */
    List<BasePoliceEmpFamily> selectByPoliceEmpId(@Param("policeEmpId") String policeEmpId);

    /**
     * 根据警员ID删除家庭成员
     *
     * @param policeEmpId 警员ID
     */
    void deleteByPoliceEmpId(@Param("policeEmpId") String policeEmpId);
}
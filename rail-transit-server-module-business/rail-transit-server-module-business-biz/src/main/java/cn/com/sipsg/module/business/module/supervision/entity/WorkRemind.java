package cn.com.sipsg.module.business.module.supervision.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 工作提醒表
 */
@Data
@TableName("tbl_work_remind")
@Schema(description = "工作提醒表")
public class WorkRemind {

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private String id;

    /**
     * 创建人
     */
    @TableField("create_user_id")
    @Schema(description = "创建人")
    private String createUserId;

    /**
     * 创建时间
     */
    @TableField("create_time")
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @TableField("update_user_id")
    @Schema(description = "更新人")
    private String updateUserId;

    /**
     * 更新时间
     */
    @TableField("update_time")
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 是否有效
     */
    @TableField("available")
    @Schema(description = "是否有效")
    private Boolean available;

    /**
     * 提醒标题
     */
    @TableField("title")
    @Schema(description = "提醒标题")
    private String title;

    /**
     * 提醒内容
     */
    @TableField("content")
    @Schema(description = "提醒内容")
    private String content;

    /**
     * 提醒时间
     */
    @TableField("remind_time")
    @Schema(description = "提醒时间")
    private LocalDateTime remindTime;

    /**
     * 所属单位ID
     */
    @TableField("org_id")
    @Schema(description = "所属单位ID")
    private String orgId;

    /**
     * 所属单位名称
     */
    @TableField("org_name")
    @Schema(description = "所属单位名称")
    private String orgName;
}
package cn.com.sipsg.module.business.module.supervision.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 综合统计-巡逻情况-时段
 */
@Data
@TableName("tbl_statistics_daily_patrol_period")
public class StatisticsDailyPatrolPeriod implements Serializable {
    /** 主键id */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /** 创建人id */
    @TableField("create_user_id")
    private String createUserId;

    /** 创建时间,unix时间戳,精确到毫秒 */
    @TableField("create_time")
    private LocalDateTime createTime;

    /** 修改人id */
    @TableField("update_user_id")
    private String updateUserId;

    /** 修改时间,unix时间戳,精确到毫秒 */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /** 是否有效 */
    @TableField("available")
    private Boolean available;

    /** 巡逻情况主表ID */
    @TableField("daily_patrol_id")
    private String dailyPatrolId;

    /** 巡逻时间段开始 */
    @TableField("start_time")
    private LocalDateTime startTime;

    /** 巡逻时间段结束 */
    @TableField("end_time")
    private LocalDateTime endTime;

    /** 民警姓名 */
    @TableField("police_name")
    private String policeName;

    /** 民警电话 */
    @TableField("police_phone")
    private String policePhone;

    /** 民警值守频道 */
    @TableField("police_channel")
    private String policeChannel;

    /** 警力-民警数量 */
    @TableField("strength_police")
    private Integer strengthPolice;

    /** 警力-辅警数量 */
    @TableField("strength_auxiliary_police")
    private Integer strengthAuxiliaryPolice;

    /** 警力-武警数量 */
    @TableField("strength_armed_police")
    private Integer strengthArmedPolice;

    /** 警力-其他数量 */
    @TableField("strength_other")
    private Integer strengthOther;

    /** 装备-枪支数量 */
    @TableField("equipment_gun")
    private Integer equipmentGun;

    /** 装备-枪支情况 */
    @TableField("equipment_gun_msg")
    private String equipmentGunMsg;

    /** 装备-GPS/北斗 */
    @TableField("equipment_gps_msg")
    private String equipmentGpsMsg;

    /** 装备-图像传输设备 */
    @TableField("equipment_img_msg")
    private String equipmentImgMsg;
} 
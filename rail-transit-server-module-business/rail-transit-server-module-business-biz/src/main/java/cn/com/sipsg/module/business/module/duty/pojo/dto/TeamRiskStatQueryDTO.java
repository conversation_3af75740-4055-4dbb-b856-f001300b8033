package cn.com.sipsg.module.business.module.duty.pojo.dto;

import cn.com.sipsg.common.pojo.bo.PageBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> lixt
 * @date : 2025-01-20
 * @Description: 队伍风险隐患统计查询DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "队伍风险隐患统计查询DTO")
public class TeamRiskStatQueryDTO extends PageBO {

    @Schema(description = "年份，不传则不按年份过滤")
    private Integer year;

    @Schema(description = "统计类型：year-按年统计，quarter-按季度统计，month-按月统计", requiredMode = Schema.RequiredMode.REQUIRED)
    private String statType;
}

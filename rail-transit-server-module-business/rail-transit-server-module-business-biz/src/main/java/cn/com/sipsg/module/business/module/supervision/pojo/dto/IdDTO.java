package cn.com.sipsg.module.business.module.supervision.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * ID传递DTO
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Data
@Schema(description = "ID传递DTO")
public class IdDTO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotBlank(message = "ID不能为空")
    private String id;
}
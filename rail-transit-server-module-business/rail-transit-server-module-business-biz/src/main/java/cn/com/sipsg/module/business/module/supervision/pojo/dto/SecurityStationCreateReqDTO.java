package cn.com.sipsg.module.business.module.supervision.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 安检点位创建请求DTO
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Data
@Schema(description = "安检点位创建请求DTO")
public class SecurityStationCreateReqDTO {

    @Schema(description = "安检点位名称", required = true)
    @NotBlank(message = "安检点位名称不能为空")
    private String name;

    @Schema(description = "安检点ID，新增时传空，修改时传id")
    private String id;

    @Schema(description = "线路 ID", required = true)
    @NotBlank(message = "线路ID不能为空")
    private String subwayId;
    
    @Schema(description = "线路名称")
    private String subwayName;
    
    @Schema(description = "站点 ID", required = true)
    @NotBlank(message = "站点ID不能为空")
    private String subwayStationId;
    
    @Schema(description = "站点名称")
    private String subwayStationName;

    @Schema(description = "安检公司ID", required = true)
    @NotBlank(message = "安检公司ID不能为空")
    private String companyId;
    
    @Schema(description = "安检公司名称")
    private String companyName;
    
    @Schema(description = "管辖单位 ID", required = true)
    @NotBlank(message = "管辖单位ID不能为空")
    private String precinctId;
    
    @Schema(description = "管辖单位名称")
    private String precinct;
    
    @Schema(description = "通道式X光机数量")
    private Integer xRayMachineNum = 0;

    @Schema(description = "手持式金属探测仪数量")
    private Integer metalDetectorNum = 0;

    @Schema(description = "危险液体探测仪数量")
    private Integer liquidDetectorNum = 0;

    @Schema(description = "炸弹检测仪数量")
    private Integer bombDetectorNum = 0;

    @Schema(description = "应急棍数量")
    private Integer emergencyBatonNum = 0;

    @Schema(description = "盾牌数量")
    private Integer shieldNum = 0;

    @Schema(description = "防爆毯数量")
    private Integer expProofBlanketNum = 0;

    @Schema(description = "约束毯数量")
    private Integer restraintBlanketNum = 0;

    @Schema(description = "抓捕叉数量")
    private Integer catchForkNum = 0;

    @Schema(description = "防爆罐数量")
    private Integer expProofTankNum = 0;

    @Schema(description = "防刺服数量")
    private Integer stabResistantClothNum = 0;

    @Schema(description = "防爆头盔数量")
    private Integer expProofHelmetNum = 0;
    
    @Schema(description = "安检门数量")
    private Integer secCheckDoorNum = 0;
}

package cn.com.sipsg.module.business.module.supervision.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "关注统计条件分页参数")
public class FocusPageQueryReqDTO {
    @Schema(description = "处置单位ID", example = "单位A")
    private String orgId;

    @Schema(description = "预警来源", example = "1")
    private Integer warningSource;

    @Schema(description = "关注方式", example = "方式A")
    private Integer focusType;

    @Schema(description = "人员类型", example = "类型A")
    private Integer personType;

    @Schema(description = "页码", example = "1")
    private Integer pageNum = 1;

    @Schema(description = "每页大小", example = "10")
    private Integer pageSize = 10;
} 
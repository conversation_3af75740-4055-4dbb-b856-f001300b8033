package cn.com.sipsg.module.business.module.supervision.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 安检员档案详情VO
 *
 * <AUTHOR>
 * @since 2025-05-29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "安检员档案详情VO")
public class SecurityEmpProfileVO {

    @Schema(description = "安检员ID")
    private String id;

    @Schema(description = "姓名")
    private String name;

    @Schema(description = "性别")
    private String gender;

    @Schema(description = "身份证号")
    private String idCardNo;

    @Schema(description = "所属公司")
    private String companyName;

    @Schema(description = "联系方式")
    private String phone;

    @Schema(description = "年龄")
    private Integer age;

    @Schema(description = "民族")
    private String nation;

    @Schema(description = "学历")
    private String education;

    @Schema(description = "户籍地址")
    private String permanentAddress;

    @Schema(description = "居住地址")
    private String residenceAddress;

    @Schema(description = "入职时间")
    private LocalDateTime entryTime;

    @Schema(description = "离职时间")
    private LocalDateTime resignationTime;

    @Schema(description = "工号")
    private String jobNumber;

    @Schema(description = "岗位")
    private String job;

    @Schema(description = "在职状态, 01:在职, 02:离职, 03:历史人员")
    private String employmentStatus;

    @Schema(description = "地铁线路名称")
    private String subwayName;

    @Schema(description = "安检证号")
    private String securityCardNo;

    @Schema(description = "保安证号")
    private String safeCardNo;

    @Schema(description = "照片URL")
    private String imgUrl;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "创建人")
    private String createUserId;

    @Schema(description = "更新人")
    private String updateUserId;

    @Schema(description = "培训记录数")
    private Integer trainingCount;

    @Schema(description = "考试记录数")
    private Integer examCount;

    @Schema(description = "公司全称")
    private String orgFullName;

    @Schema(description = "是否关注")
    private String isConcerned;

    @Schema(description = "采集人")
    private String collector;

    @Schema(description = "采集时间")
    private LocalDateTime collectTime;

    @Schema(description = "背景审查结果")
    private String backgroundReviewResult;

    @Schema(description = "背景审查详情")
    private String backgroundReviewDetail;

    @Schema(description = "背景审查状态")
    private String backgroundReviewStatus;

    @Schema(description = "近30天总班次")
    private Integer recentTotalShifts;

    @Schema(description = "创建人姓名")
    private String createUserName;

    @Schema(description = "更新人姓名")
    private String updateUserName;

    @Schema(description = "培训次数")
    private Integer trainingTimes;

    @Schema(description = "是否入职培训")
    private Boolean hasTrain;
}

package cn.com.sipsg.module.business.module.supervision.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import cn.com.sipsg.module.business.module.supervision.entity.BaseSubway;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface BaseSubwayMapper extends BaseMapper<BaseSubway> {
    @Select("SELECT name FROM tbl_base_subway WHERE code = #{subwayId} AND available = true")
    String selectNameBySubwayId(@Param("subwayId") String subwayId);
} 
package cn.com.sipsg.module.business.module.duty.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR> lixt
 * @date : 2025-05-07 10:40:23
 * @Description: 盘查人员信息
 */
@Data
@TableName("std_per_pck_zapc_ryxx")
public class PerCheckPersonInfo {
    @TableField("rybdfsdm")
    private String rybdfsdm;  // 人员比对方式代码

    @TableField("rybdjgdm")
    private String rybdjgdm;  // 人员比对结果代码

    @TableField("pcdwbh")
    private String pcdwbh;  // 盘查单位编号

    @TableField("ryyjdw")
    private String ryyjdw;  // 人员移交单位

    @TableField("djrbh")
    private String djrbh;  // 登记人编号

    @TableField("djsj")
    private LocalDateTime djsj;  // 登记时间

    @TableField("djdwbh")
    private String djdwbh;  // 登记单位编号

    @TableField("xgrbh")
    private String xgrbh;  // 修改人编号

    @TableField("xgsj")
    private LocalDateTime xgsj;  // 修改时间

    @TableField("xgdwbh")
    private String xgdwbh;  // 修改单位编号

    @TableField("rytz")
    private String rytz;  // 人员特征

    @TableField("wpbh")
    private String wpbh;  // 物品编号

    @TableField("jccfx")
    private String jccfx;  // 进出城方向

    @TableField("pcdwmc")
    private String pcdwmc;  // 盘查单位名称

    @TableField("yjdwmc")
    private String yjdwmc;  // 移交单位名称

    @TableField("djdwmc")
    private String djdwmc;  // 登记单位名称

    @TableField("djrxm")
    private String djrxm;  // 登记人姓名

    @TableField("xgdwmc")
    private String xgdwmc;  // 修改单位名称

    @TableField("xgrxm")
    private String xgrxm;  // 修改人姓名

    @TableField("xzz")
    private String xzz;  // 现住址

    @TableField("pcrbh")
    private String pcrbh;  // 盘查人编号

    @TableField("pcrxm")
    private String pcrxm;  // 盘查人姓名

    @TableField("gltxrybh")
    private String gltxrybh;  // 关联同行人员编号

    @TableField("yjyy")
    private String yjyy;  // 移交原因

    @TableField("bpcrsfz")
    private String bpcrsfz;  // 被盘查人身份证

    @TableField("bpcrxm")
    private String bpcrxm;  // 被盘查人姓名

    @TableField("bpcrxbdm")
    private String bpcrxbdm;  // 被盘查人性别代码

    @TableField("bpcrfwcs")
    private String bpcrfwcs;  // 被盘查人服务处所

    @TableField("shangchuansj")
    private LocalDateTime shangchuansj;  // 上传时间

    @TableField("zpsl")
    private Integer zpsl;  // 照片数量

    @TableField("start_dt")
    private LocalDate startDt;  // 开始日期

    @TableField("end_dt")
    private LocalDate endDt;  // 结束日期

    @TableField("del_flag")
    private String delFlag;  // 删除标志

    @TableField("etl_job")
    private String etlJob;  // ETL任务名

    @TableField("pcrybh")
    private String pcrybh;  // 盘查人员编号

    @TableField("gzbh")
    private String gzbh;  // 工作编号

    @TableField("guanlirybh")
    private String guanlirybh;  // 管理人员编号

    @TableField("ajbh")
    private String ajbh;  // 案件编号

    @TableField("jkrybh")
    private String jkrybh;  // 缉控人员编号

    @TableField("qtlxfs")
    private String qtlxfs;  // 其他联系方式

    @TableField("rycljgdm")
    private String rycljgdm;  // 人员处理结果代码

    @TableField("ryclqk")
    private String ryclqk;  // 人员处理情况

    @TableField("rybz")
    private String rybz;  // 人员备注

    @TableField("rypcyydm")
    private String rypcyydm;  // 人员盘查原因代码

    @TableField("rypcdd")
    private String rypcdd;  // 人员盘查地点

    @TableField("rypcsj")
    private LocalDateTime rypcsj;  // 人员盘查时间
}

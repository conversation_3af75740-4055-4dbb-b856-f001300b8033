package cn.com.sipsg.module.business.module.supervision.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 考试结果枚举
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Getter
@AllArgsConstructor
public enum ExamResultEnum {
    
    PASSED(true, "合格"),
    FAILED(false, "不合格"),
    UNKNOWN(null, "未知");
    
    /**
     * 是否合格
     */
    private final Boole<PERSON> passed;
    
    /**
     * 结果描述
     */
    private final String desc;
    
    /**
     * 根据是否合格获取枚举
     *
     * @param passed 是否合格
     * @return 枚举实例
     */
    public static ExamResultEnum getByPassed(Boolean passed) {
        if (passed == null) {
            return UNKNOWN;
        }
        
        return passed ? PASSED : FAILED;
    }
    
    /**
     * 根据结果描述获取枚举
     *
     * @param desc 结果描述
     * @return 枚举实例
     */
    public static ExamResultEnum getByDesc(String desc) {
        if (desc == null || desc.isEmpty()) {
            return UNKNOWN;
        }
        
        for (ExamResultEnum result : values()) {
            if (result.getDesc().equals(desc)) {
                return result;
            }
        }
        
        return UNKNOWN;
    }
}

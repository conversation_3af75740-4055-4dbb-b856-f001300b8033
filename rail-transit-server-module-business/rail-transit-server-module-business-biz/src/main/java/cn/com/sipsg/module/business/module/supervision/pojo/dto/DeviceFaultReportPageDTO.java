package cn.com.sipsg.module.business.module.supervision.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import cn.com.sipsg.common.pojo.bo.PageBO;

/**
 * 设备故障上报分页查询DTO
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "设备故障上报分页查询DTO")
public class DeviceFaultReportPageDTO extends PageBO {
    @Schema(description = "故障名称")
    private String faultName;

    @Schema(description = "上报开始时间")
    private LocalDateTime reportStartTime;

    @Schema(description = "上报结束时间")
    private LocalDateTime reportEndTime;
}
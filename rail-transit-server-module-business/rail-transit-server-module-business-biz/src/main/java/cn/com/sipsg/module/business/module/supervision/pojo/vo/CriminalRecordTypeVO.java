package cn.com.sipsg.module.business.module.supervision.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 前科类型VO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "前科类型VO")
public class CriminalRecordTypeVO {
    
    @Schema(description = "前科类型编号")
    private String code;
    
    @Schema(description = "前科类型名称")
    private String name;
}
package cn.com.sipsg.module.business.module.supervision.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> lixt
 * @date : 2025-05-26 15:40:39
 * @Description: 安检人员基础信息
 */
@Data
@TableName("tbl_security_emp")
public class SecurityEmp {

    @TableId(type = IdType.ASSIGN_ID)
    private String id;  // 主键ID

    @TableField("create_user_id")
    private String createUserId;  // 创建人

    @TableField("create_time")
    private LocalDateTime createTime;  // 创建时间

    @TableField("update_user_id")
    private String updateUserId;  // 更新人

    @TableField("update_time")
    private LocalDateTime updateTime;  // 更新时间

    @TableField("available")
    private Boolean available;  // 有效标识,TRUE:有效,FALSE:无效

    @TableField("name")
    private String name;  // 姓名

    @TableField("id_card_no")
    private String idCardNo;  // 身份证号

    @TableField("phone")
    private String phone;  // 联系电话

    @TableField("img_url")
    private String imgUrl;  // 图片地址

    @TableField("gender")
    private String gender;  // 性别，字典

    @TableField("age")
    private Integer age;  // 年龄

    @TableField("nation")
    private String nation;  // 民族

    @TableField("education")
    private String education;  // 学历

    @TableField("permanent_address")
    private String permanentAddress;  // 户籍地址

    @TableField("residence_address")
    private String residenceAddress;  // 居住地址

    @TableField("company_name")
    private String companyName;  // 安检公司名称

    @TableField("employment_status")
    private String employmentStatus;  // 从业状态, 01:在职, 02:离职, 03:历史人员

    @TableField("job_number")
    private String jobNumber;  // 工号

    @TableField("job")
    private String job;  // 岗位

    @TableField("entry_time")
    private LocalDateTime entryTime;  // 入职时间

    @TableField("resignation_time")
    private LocalDateTime resignationTime;  // 离职时间

    @TableField("subway_code")
    private String subwayCode;  // 地铁线路编号

    @TableField("subway_name")
    private String subwayName;  // 地铁线路名称

    @TableField("subway_id")
    private String subwayId;  // 地铁线路ID

    @TableField("safe_card_no")
    private String safeCardNo;  // 保安证号

    @TableField("security_card_no")
    private String securityCardNo;  // 安检证号

    @TableField("has_train")
    private Boolean hasTrain;  // 是否培训

    @TableField("company_id")
    private String companyId;  // 安检公司编号
}
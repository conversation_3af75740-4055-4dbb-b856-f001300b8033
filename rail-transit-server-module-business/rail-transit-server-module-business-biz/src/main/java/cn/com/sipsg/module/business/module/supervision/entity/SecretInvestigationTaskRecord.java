package cn.com.sipsg.module.business.module.supervision.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@TableName("tbl_secret_investigation_task_record")
@Schema(description = "暗访检查任务记录")
public class SecretInvestigationTaskRecord {
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键ID")
    private String id;

    @TableField("create_user_id")
    @Schema(description = "创建者")
    private String createUserId;

    @TableField("create_time")
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @TableField("update_user_id")
    @Schema(description = "更新者")
    private String updateUserId;

    @TableField("update_time")
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @TableField("available")
    @Schema(description = "是否有效")
    private Boolean available;

    @TableField("task_id")
    @Schema(description = "任务主键")
    private String taskId;

    @TableField("from_task")
    @Schema(description = "是否是任务驱动 如果不是则是主动检查")
    private Boolean fromTask;

    @TableField("handle_emp_id")
    @Schema(description = "处理人员 如果是主动检查，手动输入 如果是计划的，则从任务认领人带过来")
    private String handleEmpId;

    @TableField("receive_org_id")
    @Schema(description = "检查单位（任务接受单位）主键")
    private String receiveOrgId;

    @TableField("checked_org_id")
    @Schema(description = "被检查单位主键")
    private String checkedOrgId;

    @TableField("checked_station_id")
    @Schema(description = "检查站点")
    private String checkedStationId;

    @TableField("checked_security_id")
    @Schema(description = "检查安检点")
    private String checkedSecurityId;

    @TableField("check_time")
    @Schema(description = "检查时间")
    private LocalDateTime checkTime;

    @TableField("veil")
    @Schema(description = "掩饰物")
    private String veil;

    @TableField("security_danger_type")
    @Schema(description = "违禁品类型 SYSTEM_SECURITY_DANGER_TYPE")
    private Integer securityDangerType;

    @TableField("danger_info")
    @Schema(description = "违禁品详情")
    private String dangerInfo;

    @TableField("checkedout")
    @Schema(description = "是否检出")
    private Boolean checkedout;

    @TableField("process_standard")
    @Schema(description = "处理流程是否标准")
    private Boolean processStandard;

    @TableField("security_check_emp")
    @Schema(description = "安检人员主键 逗号分隔")
    private String securityCheckEmp;

    @TableField("remark")
    @Schema(description = "备注")
    private String remark;

    @TableField("together_emp")
    @Schema(description = "随行警员主键 逗号分割")
    private String togetherEmp;

    @TableField("extend_file")
    @Schema(description = "附件")
    private String extendFile;
} 
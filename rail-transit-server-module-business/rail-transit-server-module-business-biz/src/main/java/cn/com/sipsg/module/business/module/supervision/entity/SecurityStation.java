package cn.com.sipsg.module.business.module.supervision.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> lixt
 * @date : 2025-05-26 14:04:45
 * @Description: 安检点位信息
 */
@Data
@TableName("tbl_security_station")
public class SecurityStation {

    @TableId(type = IdType.INPUT)
    private String id;  // 主键ID（数据库为字符串类型）

    @TableField("create_user_id")
    private String createUserId;  // 创建人

    @TableField("create_time")
    private LocalDateTime createTime;  // 创建时间

    @TableField("update_user_id")
    private String updateUserId;  // 更新人

    @TableField("update_time")
    private LocalDateTime updateTime;  // 更新时间

    @TableField("available")
    private Boolean available;  // 有效标识,TRUE:有效,FALSE:无效

    @TableField("name")
    private String name;  // 安检点位名称

    @TableField("subway_station_id")
    private String subwayStationId;  // 地点站点ID

    @TableField("subway_station_name")
    private String subwayStationName;  // 地铁站点名称

    @TableField("precinct_id")
    private String precinctId;  // 所属辖区ID--组织树，派出所类型的组织ID

    @TableField("precinct")
    private String precinct;  // 所属辖区名称

    @TableField("subway_id")
    private String subwayId;  // 所属地铁ID

    @TableField("subway_name")
    private String subwayName;  // 所属地铁名称

    @TableField("company_id")
    private String companyId;  // 负责安检公司ID，字典配置的key(SECURITY_STATION_COMPANY)

    @TableField("company_name")
    private String companyName;  // 负责安检公司名称

    @TableField("x_ray_machine_num")
    private Integer xRayMachineNum;  // 通道式X光机数量

    @TableField("metal_detector_num")
    private Integer metalDetectorNum;  // 手持式金属探测仪数量

    @TableField("liquid_detector_num")
    private Integer liquidDetectorNum;  // 危险液体探测仪数量

    @TableField("bomb_detector_num")
    private Integer bombDetectorNum;  // 炸弹检测仪数量

    @TableField("emergency_baton_num")
    private Integer emergencyBatonNum;  // 应急棍数量

    @TableField("shield_num")
    private Integer shieldNum;  // 盾牌数量

    @TableField("exp_proof_blanket_num")
    private Integer expProofBlanketNum;  // 防爆毯数量

    @TableField("restraint_blanket_num")
    private Integer restraintBlanketNum;  // 约束毯数量

    @TableField("catch_fork_num")
    private Integer catchForkNum;  // 抓捕叉数量

    @TableField("exp_proof_tank_num")
    private Integer expProofTankNum;  // 防爆罐数量

    @TableField("stab_resistant_cloth_num")
    private Integer stabResistantClothNum;  // 防刺服数量

    @TableField("exp_proof_helmet_num")
    private Integer expProofHelmetNum;  // 防爆头盔数量

    @TableField("sec_check_door_num")
    private Integer secCheckDoorNum;  // 安检门数量

    @TableField("sort")
    private Integer sort;  // 排序-结合 importId 分批

    @TableField("import_id")
    private Long importId;  // 导入ID

    @TableField("site_place_relation_id")
    private Long sitePlaceRelationId;  // 场所关联id
}

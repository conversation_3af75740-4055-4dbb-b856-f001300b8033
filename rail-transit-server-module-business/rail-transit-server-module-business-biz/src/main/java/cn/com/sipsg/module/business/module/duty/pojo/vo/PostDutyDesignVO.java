package cn.com.sipsg.module.business.module.duty.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.List;

/**
 * 岗位班次设置VO
 */
@Data
public class PostDutyDesignVO {
    @Schema(description = "岗位类别ID", example = "1")
    private String postId;
    
    @Schema(description = "岗位类别名称", example = "巡逻岗")
    private String postName;
    
    @Schema(description = "角色 1-民警 2-辅警", example = "1")
    private Integer postRole;
    
    @Schema(description = "班次类型列表")
    private List<DutyTurnVO> turns;

    @Data
    @Schema(description = "班次信息", example = "{\"turnId\":1,\"turnName\":\"白班\",\"turnDetail\":\"当日09:00-次日09:00\"}")
    public static class DutyTurnVO {
        @Schema(description = "班次ID", example = "1")
        private String turnId;
        
        @Schema(description = "班次名称", example = "白班")
        private String turnName;
        
        @Schema(description = "上班时间", example = "09:00")
        private String startTime; // 上班时间
        
        @Schema(description = "上班日 1: 当日 2: 次日", example = "1")
        private Integer startDay; // 上班日 1: 当日 2: 次日
        
        @Schema(description = "下班时间", example = "09:00")
        private String endTime; // 下班时间
        
        @Schema(description = "下班日 1: 当日 2: 次日", example = "2")
        private Integer endDay; // 下班日 1: 当日 2: 次日
    }
}
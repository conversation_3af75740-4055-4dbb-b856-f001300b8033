package cn.com.sipsg.module.business.module.supervision.pojo.dto;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "考试答题记录上传DTO")
@Data
public class ExamTaskEmpQuestionUploadDTO {
    @Schema(description = "考试任务ID", required = true)
    @NotBlank(message = "考试任务ID不能为空")
    private String taskId;

    @Schema(description = "答题开始时间")
    private LocalDateTime startTime;

    @Schema(description = "答题结束时间")
    private LocalDateTime endTime;

    @Schema(description = "题目答案列表", required = true)
    @NotEmpty(message = "题目答案列表不能为空")
    @Valid
    private List<QuestionAnswer> questionAnswers;

    @Schema(description = "题目答案对")
    @Data
    public static class QuestionAnswer {
        @Schema(description = "题目ID", required = true)
        @NotNull(message = "题目ID不能为空")
        private Long questionId;

        @Schema(description = "题目序号", required = true)
        @NotNull(message = "题目序号不能为空")
        private Integer serial;

        @Schema(description = "作答内容，单选题如'A'，多选题如'A,D'", required = true)
        @NotBlank(message = "作答内容不能为空")
        private String answer;
    }
}
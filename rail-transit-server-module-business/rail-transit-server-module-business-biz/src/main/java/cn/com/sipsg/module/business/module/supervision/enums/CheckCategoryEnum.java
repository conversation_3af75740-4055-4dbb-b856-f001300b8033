package cn.com.sipsg.module.business.module.supervision.enums;

/**
 * 检查类目枚举
 */
public enum CheckCategoryEnum {
    HUMAN("1", "人防"),
    MATERIAL("2", "物防"),
    TECH("3", "技防"),
    COMM("4", "通防");

    private final String code;
    private final String label;

    CheckCategoryEnum(String code, String label) {
        this.code = code;
        this.label = label;
    }

    public String getCode() {
        return code;
    }

    public String getLabel() {
        return label;
    }

    public static String getLabelByCode(String code) {
        for (CheckCategoryEnum e : values()) {
            if (e.code.equals(code)) {
                return e.label;
            }
        }
        return null;
    }
} 
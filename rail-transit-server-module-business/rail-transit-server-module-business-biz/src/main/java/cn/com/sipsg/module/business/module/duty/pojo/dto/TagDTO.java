package cn.com.sipsg.module.business.module.duty.pojo.dto;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR> lixt
 * @date : 2025-04-30 17:37:17
 * @Description: 标签dto
 */
@Data
public class TagDTO {
    @Schema(description = "标签ID", example = "1")
    private String id;
    @Schema(description = "分组ID", example = "1")
    private String groupId;
    @Schema(description = "标签值", example = "党员")
    private String tagValue;
    @Schema(description = "标签key", example = "party_member")
    private String tagKey;
    @Schema(description = "是否有效", example = "true")
    private Boolean available;
    @Schema(description = "业务类型", example = "1")
    private Integer businessType;
}

package cn.com.sipsg.module.business.module.duty.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR> lixt
 * @date : 2025-04-30 17:36:27
 * @Description:
 */
@Data
public class GroupCountDTO {
    @Schema(description = "分组ID", example = "1")
    private String groupId;
    @Schema(description = "分组名称", example = "政治面貌")
    private String groupName;
    @Schema(description = "数量", example = "10")
    private Long count;
}

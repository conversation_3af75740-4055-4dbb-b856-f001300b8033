package cn.com.sipsg.module.business.module.duty.entity;


import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR> lixt
 * @date : 2025-04-30 15:26:41
 * @Description: 基础字典表
 */
@Data
@TableName("tbl_base_dict")
public class BaseDict {

    @TableId(type = IdType.ASSIGN_ID)
    private String id;  // 主键ID

    @TableField("type")
    private String type;  // 类型名称

    @TableField("description")
    private String description;  // 描述

    @TableField("key")
    private String key;  // 字典值

    @TableField("value")
    private String value;  // 字典值描述

    @TableField("sort")
    private Integer sort;  // 排序

    @TableField("parent_key")
    private String parentKey;  // 父Key

    @TableField("relation_id")
    private String relationId;  // 来源关联ID

    @TableField("create_user_id")
    private String createUserId;  // 创建人

    @TableField("create_time")
    private LocalDateTime createTime;  // 创建时间（时间戳）

    @TableField("update_user_id")
    private String updateUserId;  // 更新人

    @TableField("update_time")
    private LocalDateTime updateTime;  // 更新时间（时间戳）

    @TableField("available")
    private Boolean available;  // 是否有效

    @TableField("remark")
    private String remark;  // 备注
}
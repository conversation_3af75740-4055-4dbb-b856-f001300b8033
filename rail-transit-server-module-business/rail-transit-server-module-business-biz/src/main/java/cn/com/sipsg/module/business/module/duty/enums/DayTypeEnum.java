package cn.com.sipsg.module.business.module.duty.enums;

import lombok.Getter;

@Getter
public enum DayTypeEnum {
    TODAY(1, "当日"),
    NEXT_DAY(2, "次日");

    private final Integer code;
    private final String desc;

    DayTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDescByCode(Integer code) {
        for (DayTypeEnum dayType : values()) {
            if (dayType.getCode().equals(code)) {
                return dayType.getDesc();
            }
        }
        return "";
    }
} 
package cn.com.sipsg.module.business.module.duty.service.impl;


import cn.com.sipsg.module.business.module.duty.entity.EmpSignIn;
import cn.com.sipsg.module.business.module.duty.mapper.EmpSignInMapper;
import cn.com.sipsg.module.business.module.duty.service.IEmpSignInService;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR> lixt
 * @date : 2025-04-24 15:27:00
 * @Description:
 */
@Service
@RequiredArgsConstructor
public class IEmpSignInServiceImpl extends ServiceImpl<EmpSignInMapper, EmpSignIn> implements IEmpSignInService {
    private final EmpSignInMapper empSignInMapper;

    public List<EmpSignIn> getFinishWorker() {
        // 获取当天所有的签到记录
        List<EmpSignIn> list = empSignInMapper.getAllSignRecord(DateUtil.parse(DateUtil.format(new Date(), "yyyy-MM-dd") + " 00:00:00", "yyyy-MM-dd HH:mm:ss").getTime(),
                DateUtil.parse(DateUtil.format(new Date(), "yyyy-MM-dd") + " 23:59:59", "yyyy-MM-dd HH:mm:ss").getTime());
        // 创建一个Map来存储警员ID和对应的签到记录
        Map<String, EmpSignIn> map = new HashMap<>();
        // 如果签到记录列表为空，返回一个空的集合
        if (null == list)
            return Collections.emptyList();
        // 遍历签到记录列表，将记录放入Map中，避免重复
        list.forEach(e -> map.putIfAbsent(e.getPoliceEmpId(), e));
        // 如果Map为空，返回一个空的集合
        if (map.isEmpty())
            return Collections.emptyList();
        // 创建一个列表来存储已完成工作的签到记录
        List<EmpSignIn> r = new ArrayList<>();
        // 遍历Map中的值
        map.values().forEach(e -> {
            // 如果签到类型等于2，表示该记录已完成工作
            if (2 == e.getSignType())
                r.add(e);
        });
        // 返回包含已完成工作签到记录的列表
        return r;
    }
}

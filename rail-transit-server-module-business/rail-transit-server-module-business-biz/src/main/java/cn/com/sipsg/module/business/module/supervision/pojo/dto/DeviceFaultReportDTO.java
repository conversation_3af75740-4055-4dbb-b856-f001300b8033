package cn.com.sipsg.module.business.module.supervision.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 设备故障上报DTO
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@Schema(description = "设备故障上报DTO")
public class DeviceFaultReportDTO {

    @Schema(description = "主键ID", example = "1")
    private String id;

    @Schema(description = "故障名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "电梯门无法正常开关")
    @NotBlank(message = "故障名称不能为空")
    private String faultName;

    @Schema(description = "发生站点", requiredMode = Schema.RequiredMode.REQUIRED, example = "人民广场站")
    @NotBlank(message = "发生站点不能为空")
    private String occurrenceStation;

    @Schema(description = "故障说明", example = "电梯门卡住，无法正常开关，影响乘客通行")
    private String faultDescription;

    @Schema(description = "紧急程度", requiredMode = Schema.RequiredMode.REQUIRED, example = "urgent", allowableValues = {"urgent", "important", "normal"})
    @NotBlank(message = "紧急程度不能为空")
    private String urgencyLevel;

    @Schema(description = "上报时间", requiredMode = Schema.RequiredMode.REQUIRED, example = "2025-07-29 00:00:00")
    @NotNull(message = "上报时间不能为空")
    private LocalDateTime reportTime;

    @Schema(description = "维修进度", example = "pending", allowableValues = {"dispatched", "pending", "repairing", "repaired"})
    private String repairProgress;

    @Schema(description = "上报人名称", example = "张三")
    private String reporterName;
}

package cn.com.sipsg.module.business.module.supervision.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@TableName("tbl_site_inspection_place")
public class SiteInspectionPlace {
    /** 记录唯一标识主键 */
    @TableId("id")
    private Long id;
    /** 记录创建时间 */
    @TableField("create_time")
    private LocalDateTime createTime;
    /** 记录最后更新时间 */
    @TableField("update_time")
    private LocalDateTime updateTime;
    /** 创建者用户ID */
    @TableField("create_user_id")
    private String createUserId;
    /** 最后更新者用户ID */
    @TableField("update_user_id")
    private String updateUserId;
    /** 是否有效 */
    @TableField("available")
    private Boolean available;
    /** 场所ID */
    @TableField("place_id")
    private Long placeId;
    /** 巡检计划ID */
    @TableField("inspection_id")
    private Long inspectionId;
} 
package cn.com.sipsg.module.business.module.duty.pojo.dto;

import lombok.Data;
import javax.validation.constraints.Pattern;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 警务组织创建DTO
 * 用于接收前端创建警务组织（部门）的请求数据
 */
@Data
public class PoliceOrgCreateDTO {
    @Schema(description = "部门ID,编辑时必填，新建时为空")
    private String id;
    
    @Schema(description = "部门编号")
    private String code;
    
    @Schema(description = "部门名称")
    private String name;
    
    @Schema(description = "部门别名")
    private String alias;
    
    @Schema(description = "组织类型")
    private Integer type;
    
    @Schema(description = "上级组织ID")
    private String parentId;
    
    @Pattern(regexp = "^[0-9\\-+()]{0,20}$", message = "电话号码格式不正确")
    @Schema(description = "部门联系方式")
    private String phone;
}

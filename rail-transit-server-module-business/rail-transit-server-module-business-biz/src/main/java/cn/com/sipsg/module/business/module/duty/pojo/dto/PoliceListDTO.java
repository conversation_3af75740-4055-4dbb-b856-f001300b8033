package cn.com.sipsg.module.business.module.duty.pojo.dto;


import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR> lixt
 * @date : 2025-04-29 15:41:14
 * @Description:
 */
@Data
public class PoliceListDTO {
    @Schema(description = "姓名", example = "张三")
    private String name;
    @Schema(description = "类型", example = "1")
    private int type;
    @Schema(description = "编号", example = "EMP001")
    private String code;
    @Schema(description = "手机号", example = "***********")
    private String phone;
    @Schema(description = "工作单位", example = "地铁公安分局")
    private String workUnit;
    @Schema(description = "职务", example = "巡逻员")
    private String duty;
    @Schema(description = "标签", example = "党员,巡逻岗")
    private String label;
    @Schema(description = "主键ID", example = "1")
    private String id;

    @Schema(description = "是否系统用户", example = "true")
    @TableField("CASE WHEN auth_user_id IS NOT NULL THEN TRUE ELSE FALSE END")
    private Boolean isSystemUser;
}

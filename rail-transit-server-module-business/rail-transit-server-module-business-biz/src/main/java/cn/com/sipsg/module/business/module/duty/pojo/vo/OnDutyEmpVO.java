package cn.com.sipsg.module.business.module.duty.pojo.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR> lixt
 * @date : 2025-04-24 13:30:17
 * @Description: 在岗警力统计 VO
 */
@Data
@Schema(description = "单位在岗警力统计VO", example = "{\"policeOrgId\":1001,\"policeOrgName\":\"轨交分局\",\"onDutyCount\":5}")
public class OnDutyEmpVO {
    @Schema(description = "单位ID", example = "1001")
    private String policeOrgId;

    @Schema(description = "单位名称", example = "轨交分局")
    private String policeOrgName;

    @Schema(description = "在岗警力数量", example = "5")
    private Integer onDutyCount;
}

package cn.com.sipsg.module.business.module.duty.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum DutiesEnum {
    POLICE_TECHNICIAN("01", "警务技术员"),
    BUREAU_CHIEF("02", "局长"),
    LEADERSHIP_POSITION("03", "领导职务"),
    LEVEL_1_POLICE_COMMISSIONER("04", "一级警务专员"),
    POLITICAL_COMMISSAR("05", "政治委员"),
    INSPECTOR("06", "巡视员"),
    GENERAL_COMMANDER("07", "总队长"),
    DEPUTY_BUREAU_CHIEF("08", "副局长");

    @EnumValue
    private final String code;
    
    @JsonValue
    private final String label;

    DutiesEnum(String code, String label) {
        this.code = code;
        this.label = label;
    }

    public static boolean isValid(String value) {
        for (DutiesEnum e : values()) {
            if (e.label.equals(value) || e.code.equals(value)) {
                return true;
            }
        }
        return false;
    }

    public static String getLabelByCode(String code) {
        for (DutiesEnum e : values()) {
            if (e.code.equals(code)) {
                return e.label;
            }
        }
        return null;
    }

    public static String getCodeByLabel(String label) {
        for (DutiesEnum e : values()) {
            if (e.label.equals(label)) {
                return e.code;
            }
        }
        return null;
    }

    public static DutiesEnum getByCode(String code) {
        for (DutiesEnum e : values()) {
            if (e.code.equals(code)) {
                return e;
            }
        }
        return null;
    }
}
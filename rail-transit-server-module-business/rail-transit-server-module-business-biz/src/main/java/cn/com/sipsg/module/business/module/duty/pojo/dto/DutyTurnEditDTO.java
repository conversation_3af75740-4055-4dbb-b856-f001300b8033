package cn.com.sipsg.module.business.module.duty.pojo.dto;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 班次类型DTO
 */
@Data
public class DutyTurnEditDTO {
    @Schema(description = "班次主键ID，编辑时必传", example = "1")
    private String id; // 班次主键ID，编辑时必传
    @Schema(description = "班次名称", example = "白班")
    private String turnName; // 班次名称
    @Schema(description = "上班时间", example = "08:00")
    private String startTime; // 上班时间
    @Schema(description = "上班日 1: 当日 2: 次日", example = "1")
    private Integer startDay; // 上班日 1: 当日 2: 次日
    @Schema(description = "下班时间", example = "16:00")
    private String endTime; // 下班时间
    @Schema(description = "下班日 1: 当日 2: 次日", example = "1")
    private Integer endDay; // 下班日 1: 当日 2: 次日
    @Schema(description = "是否有效", example = "true")
    private Boolean available; // 是否有效
} 
package cn.com.sipsg.module.business.module.duty.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR> lixt
 * @date : 2025-05-15
 * @Description: 班组编辑DTO
 */
@Data
@Schema(description = "班组编辑DTO")
public class DutyTeamEditDTO {
    @Schema(description = "班组ID，编辑时必传", example = "1")
    private String id;
    
    @Schema(description = "班组名称", example = "A组")
    @NotBlank(message = "班组名称不能为空")
    private String groupName;
    
    @Schema(description = "岗位ID", example = "1")
    private String postId;
    
    @Schema(description = "班组成员ID列表", example = "[\"10001\", \"10002\", \"10003\"]")
    private List<String> memberIds;
}
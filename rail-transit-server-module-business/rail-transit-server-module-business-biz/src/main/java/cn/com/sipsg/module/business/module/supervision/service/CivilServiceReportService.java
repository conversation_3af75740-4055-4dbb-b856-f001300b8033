package cn.com.sipsg.module.business.module.supervision.service;

import cn.com.sipsg.module.business.module.supervision.entity.CivilServiceReport;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.CivilServiceReportDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.CivilServiceReportPageDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.CivilServiceReportPageResultDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.CivilServiceStatisticsDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 服务群众上报表 服务类
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
public interface CivilServiceReportService extends IService<CivilServiceReport> {

    /**
     * 保存服务群众上报
     *
     * @param dto 服务群众上报信息
     * @return 是否保存成功
     */
    Boolean saveCivilServiceReport(CivilServiceReportDTO dto);

    /**
     * 获取服务群众上报分页列表
     *
     * @param pageDTO 分页查询参数
     * @return 分页结果
     */
    IPage<CivilServiceReportPageResultDTO> getCivilServiceReportPage(CivilServiceReportPageDTO pageDTO);

    /**
     * 获取服务群众上报详情
     *
     * @param id 服务群众上报ID
     * @return 服务群众上报详情
     */
    CivilServiceReportDTO getCivilServiceReportDetail(String id);

    /**
     * 批量删除服务群众上报
     *
     * @param ids 服务群众上报ID列表
     * @return 是否删除成功
     */
    Boolean batchDeleteCivilServiceReport(List<String> ids);
    
    /**
     * 获取服务群众汇总统计数据（一周）
     *
     * @return 服务群众汇总统计数据
     */
    CivilServiceStatisticsDTO getWeeklyStatistics();
}
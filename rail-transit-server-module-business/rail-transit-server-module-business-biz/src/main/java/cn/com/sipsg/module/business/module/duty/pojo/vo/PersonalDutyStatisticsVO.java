package cn.com.sipsg.module.business.module.duty.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "个人每日值班统计VO", example = "{\"empId\":10001,\"empName\":\"张三\",\"date\":\"2025-05-10\",\"onDuty\":true}")
public class PersonalDutyStatisticsVO {
    @Schema(description = "人员ID", example = "10001")
    private String empId;
    @Schema(description = "人员姓名", example = "张三")
    private String empName;
    @Schema(description = "日期", example = "2025-05-10")
    private String date;
    @Schema(description = "是否值班", example = "true")
    private Boolean onDuty;
} 
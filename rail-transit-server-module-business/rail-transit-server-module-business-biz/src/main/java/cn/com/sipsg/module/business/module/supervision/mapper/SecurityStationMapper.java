package cn.com.sipsg.module.business.module.supervision.mapper;


import cn.com.sipsg.common.mybatis.core.mapper.BaseMapperX;
import cn.com.sipsg.module.business.module.supervision.entity.SecurityStation;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.CompanyOptionVO;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR> lixt
 * @date : 2025-05-26 14:05:37
 * @Description:
 */
public interface SecurityStationMapper extends BaseMapperX<SecurityStation> {
    /**
     * 查询所有安检点位的唯一公司id和名称
     */
    @Select("SELECT DISTINCT company_id, company_name FROM tbl_security_station WHERE available = true AND company_id IS NOT NULL AND company_name IS NOT NULL")
    List<CompanyOptionVO> selectDistinctCompanyOptions();

    /**
     * 根据ID查询安检点详情
     */
    SecurityStation selectById(String id);
}

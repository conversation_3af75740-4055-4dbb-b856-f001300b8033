package cn.com.sipsg.module.business.module.supervision.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 每日报备分页查询请求DTO
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
@Schema(description = "每日报备分页查询请求DTO")
public class DailyReportPageReqDTO {

    @Schema(description = "开始时间", example = "2025-07-29 00:00:00")
    private LocalDateTime startTime;

    @Schema(description = "结束时间", example = "2024-12-31 23:59:59")
    private LocalDateTime endTime;

    @Schema(description = "页码", example = "1")
    private Integer pageNum = 1;

    @Schema(description = "每页大小", example = "10")
    private Integer pageSize = 10;
}
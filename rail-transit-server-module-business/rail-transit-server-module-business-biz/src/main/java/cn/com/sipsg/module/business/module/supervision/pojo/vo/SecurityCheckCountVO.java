package cn.com.sipsg.module.business.module.supervision.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 安检次数统计VO
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "安检次数统计VO")
public class SecurityCheckCountVO {

    @Schema(description = "安检次数")
    private Long count;
}

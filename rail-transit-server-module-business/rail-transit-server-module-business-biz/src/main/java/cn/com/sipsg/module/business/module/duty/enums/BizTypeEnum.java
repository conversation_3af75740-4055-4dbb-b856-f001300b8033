package cn.com.sipsg.module.business.module.duty.enums;

import lombok.Getter;

@Getter
public enum BizTypeEnum {
    PAN_CHA("PAN_CHA", "盘查"),
    AN_JIAN("AN_JIAN", "安检查获"),
    JIE_CHU_JING("JIE_CHU_JING", "接处警");

    private final String code;
    private final String label;

    BizTypeEnum(String code, String label) {
        this.code = code;
        this.label = label;
    }

    /**
     * 根据业务类型代码获取对应的枚举类型
     * 此方法用于将字符串代码转换为BizTypeEnum枚举类型，以便统一业务类型处理
     * 如果找不到匹配的枚举类型，则抛出IllegalArgumentException异常
     *
     * @param code 业务类型代码，不区分大小写
     * @return 对应的BizTypeEnum枚举类型
     * @throws IllegalArgumentException 如果代码不匹配任何已定义的业务类型
     */
    public static BizTypeEnum fromCode(String code) {
        // 遍历所有BizTypeEnum枚举值
        for (BizTypeEnum e : values()) {
            // 检查当前枚举值的代码是否与输入代码相同，不区分大小写
            if (e.code.equalsIgnoreCase(code)) {
                // 如果相同，返回当前枚举值
                return e;
            }
        }
        // 如果没有找到匹配的枚举值，抛出异常
        throw new IllegalArgumentException("不支持的业务类型: " + code);
    }
} 
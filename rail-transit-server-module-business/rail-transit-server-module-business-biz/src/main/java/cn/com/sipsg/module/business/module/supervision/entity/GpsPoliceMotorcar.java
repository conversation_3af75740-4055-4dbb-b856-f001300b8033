package cn.com.sipsg.module.business.module.supervision.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.sql.Timestamp;

/**
 * GPS采集的警用摩托车实时定位数据
 */
@Data
@TableName("tbl_gps_police_motorcar")
public class GpsPoliceMotorcar {
    @TableId(type = IdType.AUTO)
    private Long id; // 主键

    @TableField("imei")
    private String imei; // 设备IMEI码

    @TableField("name")
    private String name; // 设备名称或别名

    @TableField("device_type")
    private String deviceType; // 设备类型

    @TableField("hphm")
    private String hphm; // 号牌号码

    @TableField("gbbh_list")
    private String gbbhList; // 国标编码列表

    @TableField("dept_code")
    private String deptCode; // 部门编码

    @TableField("dept_name")
    private String deptName; // 部门名称

    @TableField("longitude")
    private String longitude; // 经度坐标

    @TableField("latitude")
    private String latitude; // 纬度坐标

    @TableField("time")
    private Timestamp time; // 采集时间

    @TableField("speed")
    private String speed; // 当前速度

    @TableField("heading")
    private String heading; // 朝向（角度）

    @TableField("table_name")
    private String tableName; // 来源表名

    @TableField("op_type")
    private String opType; // 操作类型

    @TableField("current_ts")
    private Timestamp currentTs; // 当前系统时间

    @TableField("topic")
    private String topic; // 消息主题

    @TableField("create_time")
    private Long createTime; // 创建时间（毫秒）

    @TableField("update_time")
    private Long updateTime; // 更新时间（毫秒）
} 
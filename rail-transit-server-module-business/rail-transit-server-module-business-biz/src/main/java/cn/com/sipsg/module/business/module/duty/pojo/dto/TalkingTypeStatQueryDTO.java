package cn.com.sipsg.module.business.module.duty.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 谈话类型统计查询参数
 */
@Data
@Schema(description = "谈话类型统计查询参数")
public class TalkingTypeStatQueryDTO {
    
    @Schema(description = "年份，不传默认当年")
    private Integer year;
    
    @Schema(description = "查询类型: YEAR-年度, QUARTER-季度, MONTH-月度，不传不影响年份查询")
    private String queryType;
}

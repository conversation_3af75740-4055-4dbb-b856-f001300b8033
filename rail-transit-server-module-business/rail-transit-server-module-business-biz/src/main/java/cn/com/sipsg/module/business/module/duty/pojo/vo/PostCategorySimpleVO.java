package cn.com.sipsg.module.business.module.duty.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "岗位类别精简VO", example = "{\"postId\":1,\"postName\":\"局领导\"}")
public class PostCategorySimpleVO {
    @Schema(description = "岗位ID", example = "1")
    private String postId;
    @Schema(description = "岗位名称", example = "局领导")
    private String postName;
} 
package cn.com.sipsg.module.business.module.common.util;

import cn.com.sipsg.module.business.module.duty.entity.BasePoliceOrg;
import cn.com.sipsg.module.business.module.duty.mapper.BasePoliceOrgMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 单位递归工具类
 */
@Component
@RequiredArgsConstructor
public class OrgRecursiveUtils {
    private final BasePoliceOrgMapper basePoliceOrgMapper;

    /**
     * 递归获取指定单位及所有下级单位ID
     *
     * @param orgId 根单位ID
     * @return 单位及下级单位ID集合
     */
    public List<String> getOrgAndChildrenIds(String orgId) {
        // 获取所有单位信息
        List<BasePoliceOrg> all = basePoliceOrgMapper.listOrgPo();
        // 使用HashSet存储单位ID，以避免重复
        Set<String> result = new HashSet<>();
        // 递归收集单位ID
        collectOrgIds(orgId, all, result);
        // 将Set转换为List返回
        return new ArrayList<>(result);
    }

    /**
     * 递归收集指定机构ID及其所有子机构的ID
     *
     * @param orgId  当前处理的机构ID
     * @param all    包含所有机构信息的列表
     * @param result 用于存储收集到的机构ID的集合
     */
    private void collectOrgIds(String orgId, List<BasePoliceOrg> all, Set<String> result) {
        // 将当前机构ID添加到结果集合中
        result.add(orgId);
        // 遍历所有机构，寻找当前机构的子机构
        for (BasePoliceOrg org : all) {
            // 如果找到子机构，则递归调用本方法收集子机构的ID
            if (orgId.equals(org.getParentId())) {
                collectOrgIds(org.getId(), all, result);
            }
        }
    }

    /**
     * 递归获取指定单位及所有下级单位ID（String类型）
     * @param orgId 根单位ID（String）
     * @return 单位及下级单位ID集合（String）
     */
    public List<String> getOrgAndChildrenIdsStr(String orgId) {
        List<BasePoliceOrg> all = basePoliceOrgMapper.listOrgPo();
        Set<String> result = new HashSet<>();
        collectOrgIdsStr(orgId, all, result);
        return new ArrayList<>(result);
    }

    private void collectOrgIdsStr(String orgId, List<BasePoliceOrg> all, Set<String> result) {
        result.add(orgId);
        for (BasePoliceOrg org : all) {
            if (orgId.equals(org.getParentId())) {
                collectOrgIdsStr(org.getId(), all, result);
            }
        }
    }
}
package cn.com.sipsg.module.business.module.supervision.pojo.dto;

import cn.com.sipsg.common.pojo.bo.PageBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "考试任务分页请求DTO")
public class ExamTaskPageReqDTO extends PageBO {
    @Schema(description = "考试任务名称（模糊查询，可选）")
    private String taskName;

    @Schema(
        description = "考试状态（全部/未开始/进行中/不合格/合格/已过期，可选）" +
                      "，可选值：00-全部，01-未开始，02-进行中，03-合格，04-不合格，05-已过期",
        allowableValues = {"00", "01", "02", "03", "04", "05"}
    )
    private String status;
} 
package cn.com.sipsg.module.business.module.duty.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR> lixt
 * @date : 2025-01-21
 * @Description: 警员基本信息VO
 */
@Data
@Schema(description = "警员基本信息VO")
public class PoliceBasicInfoVO {
    @Schema(description = "警员ID")
    private String id;

    @Schema(description = "警员编号")
    private String code;

    @Schema(description = "警员姓名")
    private String name;
}
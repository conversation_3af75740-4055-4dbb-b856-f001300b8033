package cn.com.sipsg.module.business.module.supervision.service.impl;

import cn.com.sipsg.common.exception.BusinessException;
import cn.com.sipsg.common.mybatis.core.query.MPJLambdaWrapperX;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.security.core.util.SecurityUtils;

import cn.com.sipsg.module.business.module.duty.entity.BasePoliceEmp;
import cn.com.sipsg.module.business.module.duty.entity.BasePoliceOrg;
import cn.com.sipsg.module.business.module.duty.entity.BaseStation;
import cn.com.sipsg.module.business.module.duty.entity.DutyEmp;
import cn.com.sipsg.module.business.module.duty.entity.BaseDict;
import cn.com.sipsg.module.business.module.duty.entity.BasePoliceEmpTag;
import cn.com.sipsg.module.business.module.duty.enums.EmpTypeEnum;
import cn.com.sipsg.module.business.module.duty.enums.StationStatusEnum;
import cn.com.sipsg.module.business.module.duty.mapper.BasePoliceEmpMapper;
import cn.com.sipsg.module.business.module.duty.mapper.BasePoliceOrgMapper;
import cn.com.sipsg.module.business.module.duty.mapper.BaseStationMapper;
import cn.com.sipsg.module.business.module.duty.mapper.DutyEmpMapper;
import cn.com.sipsg.module.business.module.duty.mapper.PoliceListMapper;
import cn.com.sipsg.module.business.module.duty.pojo.dto.PoliceListDTO;
import cn.com.sipsg.module.business.module.duty.pojo.vo.PoliceListVO;
import cn.com.sipsg.module.business.module.duty.service.DutyManageService;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.DateQueryDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.OrgPolicePageDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.PoliceFuzzySearchDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.OnDutyCountVO;

import cn.com.sipsg.module.business.module.supervision.pojo.vo.StationPoliceCountListVO;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.StationPoliceCountVO;
import cn.com.sipsg.module.business.module.supervision.service.PoliceService;
import cn.com.sipsg.module.business.module.common.util.UserOrgUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
import cn.com.sipsg.module.business.module.common.util.OrgRecursiveUtils;
import cn.com.sipsg.module.business.module.supervision.entity.BaseSubway;
import cn.com.sipsg.module.business.module.supervision.entity.BaseSubwayStation;
import cn.com.sipsg.module.business.module.supervision.mapper.BaseSubwayMapper;
import cn.com.sipsg.module.business.module.supervision.mapper.BaseSubwayStationMapper;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.LineStationPoliceCountVO;

/**
 * 警员服务实现类
 * 提供警员相关业务逻辑处理
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class PoliceServiceImpl implements PoliceService {

    private final DutyManageService dutyManageService;
    private final BasePoliceEmpMapper basePoliceEmpMapper;
    private final DutyEmpMapper dutyEmpMapper;
    private final BaseStationMapper baseStationMapper;
    private final BasePoliceOrgMapper basePoliceOrgMapper;

    private final UserOrgUtils userOrgUtils;
    private final PoliceListMapper policeListMapper;
    private final OrgRecursiveUtils orgRecursiveUtils;
    private final BaseSubwayMapper baseSubwayMapper;
    private final BaseSubwayStationMapper baseSubwayStationMapper;

    /**
     * 获取当前用户所在组织的值班民警数量
     * 统计当前用户所在组织当天在岗的民警数量
     *
     * @return 当前组织值班民警数量VO
     */
    @Override
    public OnDutyCountVO getOnDutyPoliceCount() {
        // 查询当天值班人数
        LocalDate today = LocalDate.now();
        String todayStr = today.toString(); // yyyy-MM-dd

        QueryWrapper<DutyEmp> queryWrapper = new QueryWrapper<DutyEmp>()
                .eq("duty_date", todayStr)
                .eq("available", true);

        if (!SecurityUtils.isSuperAdmin()) {
            String orgId = getCurrentUserOrgId();
            if (orgId != null) {
                // 获取本单位及下级单位ID集合
                List<String> orgIds = orgRecursiveUtils.getOrgAndChildrenIds(orgId);
                if (orgIds != null && !orgIds.isEmpty()) {
                    queryWrapper.in("police_org_id", orgIds);
                } else {
                    queryWrapper.eq("police_org_id", orgId);
                }
            }
        }
        // 管理员不加组织限制，查全部

        Long count = dutyEmpMapper.selectCount(queryWrapper);

        OnDutyCountVO result = new OnDutyCountVO();
        result.setCount(count);
        result.setDate(today);
        String currentOrgId = getCurrentUserOrgId();
        log.info("[值班民警统计] 组织ID: {}, 当天值班民警数量: {}", currentOrgId != null ? currentOrgId : "全部", count);
        return result;
    }

    /**
     * 获取当前用户所在组织的值班辅警数量
     * 统计当前用户所在组织指定日期在岗的辅警数量
     *
     * @param queryDTO 查询参数，包含查询日期
     * @return 当前组织值班辅警数量VO
     */
    @Override
    public OnDutyCountVO getOnDutyAuxiliaryPoliceCount(DateQueryDTO queryDTO) {
        // 获取查询日期，如果DTO中没有提供，则使用当前日期
        LocalDate queryDate = (queryDTO != null && queryDTO.getQueryDate() != null) ? queryDTO.getQueryDate() : LocalDate.now();
        // 将日期格式化为yyyy-MM-dd字符串，与DutyEmp实体中的dutyDate字段类型匹配
        String queryDateStr = queryDate.toString();

        // 构建查询条件：指定日期 + 可用状态 + 辅警类型（通过子查询关联 tbl_base_police_emp）
        QueryWrapper<DutyEmp> queryWrapper = new QueryWrapper<DutyEmp>()
                .eq("duty_date", queryDateStr)
                .eq("available", true)
                .inSql("emp_id", "SELECT id FROM tbl_base_police_emp WHERE type = " + EmpTypeEnum.ASSISTANT.getCode()); // 只统计辅警
        
        // 如果不是管理员，则只查询当前用户所在组织的数据
        if (!SecurityUtils.isSuperAdmin()) {
            String orgId = getCurrentUserOrgId();
            queryWrapper.eq("police_org_id", orgId);
            log.info("[值班辅警统计] 普通用户，组织ID: {}", orgId);
        } else {
            log.info("[值班辅警统计] 管理员用户，查询全部数据");
        }
        
        Long count = dutyEmpMapper.selectCount(queryWrapper);

        // 构建返回对象
        OnDutyCountVO result = new OnDutyCountVO();
        result.setCount(count);
        result.setDate(queryDate);

        log.info("[值班辅警统计] 查询日期: {}, 值班辅警数量: {}", queryDate, count);
        return result;
    }

    /**
     * 获取各站点值班民警数量列表
     * 统计当天所有站点的在岗民警数量
     *
     * @return 各站点值班民警数量列表VO
     */
    @Override
    public StationPoliceCountListVO getStationPoliceCount() {
        // 调用服务层方法获取各站点值班民警数量
        List<StationPoliceCountVO> stationPoliceList = dutyManageService.getStationPoliceCount();

        // 计算总值班人数
        int totalCount = stationPoliceList.stream()
                .mapToInt(StationPoliceCountVO::getPoliceCount)
                .sum();

        // 构建返回对象
        StationPoliceCountListVO result = new StationPoliceCountListVO();
        result.setDate(LocalDate.now());
        result.setTotalCount(totalCount);
        result.setStationList(stationPoliceList);

        log.info("[站点值班民警统计] 统计日期：{}，站点数：{}", result.getDate(), stationPoliceList.size());
        return result;
    }

    /**
     * 获取各站点值班辅警数量列表
     * 统计当天所有站点的在岗辅警数量
     *
     * @return 各站点值班辅警数量列表VO
     */
    @Override
    public StationPoliceCountListVO getStationAuxiliaryPoliceCount(DateQueryDTO queryDTO) {
        LocalDate queryDate = Optional.ofNullable(queryDTO).map(DateQueryDTO::getQueryDate).orElse(LocalDate.now());

        // 构建值班辅警查询条件
        QueryWrapper<DutyEmp> dutyEmpQueryWrapper = new QueryWrapper<DutyEmp>()
                .select("DISTINCT police_org_id")
                .eq("duty_date", queryDate)
                .eq("available", true)
                .inSql("emp_id", "SELECT id FROM tbl_base_police_emp WHERE type = " + EmpTypeEnum.ASSISTANT.getCode());
        
        // 如果不是管理员，则只查询当前用户所在组织的数据
        if (!SecurityUtils.isSuperAdmin()) {
            String orgId = getCurrentUserOrgId();
            dutyEmpQueryWrapper.eq("police_org_id", orgId);
            log.info("[站点辅警统计] 普通用户，组织ID: {}", orgId);
        } else {
            log.info("[站点辅警统计] 管理员用户，查询全部站点");
        }

        // 查询所有启用状态的站点信息
        List<BaseStation> stationList = dutyEmpMapper.selectList(dutyEmpQueryWrapper).stream().map(dutyEmp -> {
            // 查询站点信息
            return baseStationMapper.selectOne(
                    new QueryWrapper<BaseStation>()
                            .eq("police_org_id", dutyEmp.getPoliceOrgId())
                            .eq("status", StationStatusEnum.ENABLED.getCode()) // 只查询启用状态的站点
            );
        }).filter(Objects::nonNull).collect(Collectors.toList());

        // 构建站点值班辅警数量列表
        List<StationPoliceCountVO> stationPoliceList = new ArrayList<>();
        for (BaseStation station : stationList) {
            // 查询该站点当天值班辅警数量
            Long count = dutyEmpMapper.selectCount(
                    new QueryWrapper<DutyEmp>()
                            .eq("police_org_id", station.getPoliceOrgId())
                            .eq("duty_date", queryDate)
                            .eq("available", true)
                            .inSql("emp_id", "SELECT id FROM tbl_base_police_emp WHERE type = " + EmpTypeEnum.ASSISTANT.getCode())
            );

            // 查询机构名称
            BasePoliceOrg org = basePoliceOrgMapper.selectById(station.getPoliceOrgId());
            String orgName = org != null ? org.getName() : "";

            // 构建站点值班辅警数量VO
            StationPoliceCountVO vo = new StationPoliceCountVO();
            vo.setStationId(station.getId());
            vo.setStationCode(station.getStationCode());
            vo.setStationName(station.getName());
            vo.setPoliceCount(count.intValue());
            vo.setOrgId(station.getPoliceOrgId());
            vo.setOrgName(orgName);

            stationPoliceList.add(vo);
        }

        // 按辅警数量降序排序，数量相同时按站点ID升序排序
        stationPoliceList.sort((a, b) -> {
            int result = Integer.compare(b.getPoliceCount(), a.getPoliceCount());
            if (result == 0) {
                return a.getStationId().compareTo(b.getStationId());
            }
            return result;
        });

        // 计算总值班人数
        int totalCount = stationPoliceList.stream()
                .mapToInt(StationPoliceCountVO::getPoliceCount)
                .sum();

        // 构建返回对象
        StationPoliceCountListVO result = new StationPoliceCountListVO();
        result.setDate(queryDate);
        result.setTotalCount(totalCount);
        result.setStationList(stationPoliceList);

        log.info("[站点值班辅警统计] 统计日期：{}，站点数：{}", result.getDate(), stationPoliceList.size());
        return result;
    }

    /**
     * 获取各线路各站点在岗民警数量列表
     * 统计当天所有线路各站点在岗民警数量
     *
     * @return 各线路各站点在岗民警数量列表VO
     */
    @Override
    public List<LineStationPoliceCountVO> getLineStationPoliceCount() {
        LocalDate today = LocalDate.now();
        String todayStr = today.toString();
        // 查询所有有效地铁线路
        List<BaseSubway> subwayList = baseSubwayMapper.selectList(new QueryWrapper<BaseSubway>().eq("available", true));
        // 查询所有有效线路-站点关系
        List<BaseSubwayStation> subwayStationList = baseSubwayStationMapper.selectList(new QueryWrapper<BaseSubwayStation>().eq("available", true));
        // 查询所有有效站点
        List<BaseStation> stationList = baseStationMapper.selectList(new QueryWrapper<BaseStation>().eq("available", true).eq("status", 2));
        // 查询当天所有在岗民警的DutyEmp
        List<DutyEmp> dutyEmpList = dutyEmpMapper.selectList(new QueryWrapper<DutyEmp>().eq("duty_date", todayStr).eq("available", true));
        // 组装线路分组统计
        List<LineStationPoliceCountVO> lineList = new ArrayList<>();
        int totalCount = 0;
        for (BaseSubway subway : subwayList) {
            LineStationPoliceCountVO lineVO = new LineStationPoliceCountVO();
            lineVO.setSubwayId(subway.getCode());
            lineVO.setSubwayName(subway.getName());
            // 找到该线路下所有站点ID
            List<String> stationIds = subwayStationList.stream()
                .filter(rel -> subway.getCode().equals(rel.getSubwayId()))
                .map(BaseSubwayStation::getStationId)
                .collect(Collectors.toList());
            // 找到这些站点对应的policeOrgId
            Set<String> orgIds = stationList.stream()
                .filter(s -> stationIds.contains(s.getId()))
                .map(BaseStation::getPoliceOrgId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
            // 统计该线路下所有站点当天在岗民警数量
            int policeCount = (int) dutyEmpList.stream()
                .filter(d -> orgIds.contains(d.getPoliceOrgId()))
                .count();
            lineVO.setPoliceCount(policeCount);
            lineList.add(lineVO);
        }
        return lineList;
    }

    /**
     * 获取当前用户所在组织的警员分页列表
     * 查询当前用户所在组织下所有警员信息列表
     *
     * @param pageDTO 分页参数DTO
     * @return 警员分页列表
     */
    @Override
    public CommonPageVO<PoliceListVO> getOrgPoliceList(OrgPolicePageDTO pageDTO) {
        try {
            // 获取当前用户所在组织ID
            String orgId = getCurrentUserOrgId();
            
            // 如果是超级管理员，则不限制组织ID，可以查看所有民警
            if (SecurityUtils.isSuperAdmin()) {
                orgId = null;
                log.info("[一警一档分页列表] 当前用户为超级管理员，查询所有民警");
            } else {
                log.info("[一警一档分页列表] 当前用户所在组织ID: {}", orgId);
            }

            List<String> orgIds = null;
            if (!SecurityUtils.isSuperAdmin()) {
                orgIds = orgRecursiveUtils.getOrgAndChildrenIds(orgId);
            }
            // 分页查询
            int offset = (pageDTO.getPageNum() - 1) * pageDTO.getPageSize();
            List<PoliceListVO> pageList = policeListMapper.selectPoliceList(orgIds, null, null, null, null, pageDTO.getPageSize(), offset);
            // 查询总数
            int total = policeListMapper.countPoliceList(orgIds, null, null, null, null);

            CommonPageVO<PoliceListVO> result = new CommonPageVO<>();
            result.setTotal((long) total);
            result.setRecords(pageList);
            result.setSize(pageDTO.getPageSize());
            result.setCurrent(pageDTO.getPageNum());
            return result;
        } catch (Exception e) {
            log.error("[一警一档分页列表] 查询失败", e);
            throw new BusinessException("查询警员列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前用户所在组织的辅警分页列表
     * 查询当前用户所在组织下所有辅警信息列表（仅包含辅警，不包含民警、安保等其他人员）
     *
     * @param pageDTO 分页参数DTO
     * @return 辅警分页列表
     */
    @Override
    public CommonPageVO<PoliceListVO> getOrgAuxiliaryPoliceList(OrgPolicePageDTO pageDTO) {
        try {
            // 获取当前用户所在组织ID
            String orgId = getCurrentUserOrgId();
            log.info("[辅警分页列表] 当前用户所在组织ID: {}", orgId);

            // 构建查询条件
            MPJLambdaWrapperX<BasePoliceEmp> wrapper = buildAuxiliaryPoliceEmpQueryWrapper(orgId);

            // 执行分页查询并返回结果
            return executePolicePageQuery(pageDTO, wrapper, "辅警分页列表");
        } catch (Exception e) {
            log.error("[辅警分页列表] 查询失败", e);
            throw new BusinessException("查询辅警列表失败: " + e.getMessage());
        }
    }

    /**
     * 执行警员分页查询并转换结果
     * 封装了分页查询、数据转换和结果构建的通用逻辑
     *
     * @param pageDTO   分页参数DTO
     * @param wrapper   查询条件包装器
     * @param logPrefix 日志前缀，用于区分不同的查询类型
     * @return 警员分页列表
     */
    private CommonPageVO<PoliceListVO> executePolicePageQuery(OrgPolicePageDTO pageDTO,
                                                              MPJLambdaWrapperX<BasePoliceEmp> wrapper,
                                                              String logPrefix) {
        try {
            // 创建分页对象
            int pageNum = pageDTO.getPageNum() == null ? 1 : pageDTO.getPageNum();
            int pageSize = pageDTO.getPageSize() == null ? 10 : pageDTO.getPageSize();
            Page<PoliceListDTO> page = new Page<>(pageNum, pageSize);
            
            // 执行多表联查分页查询
            Page<PoliceListDTO> resultPage = basePoliceEmpMapper.selectJoinPage(page, PoliceListDTO.class, wrapper);
            
            // 转换结果
            List<PoliceListVO> voList = convertToPoliceListVO(resultPage.getRecords());
            
            // 构建分页结果
            CommonPageVO<PoliceListVO> result = CommonPageVO.build(voList, pageNum, pageSize, resultPage.getTotal());

            log.info("[{}] 查询成功，总记录数: {}, 当前页记录数: {}",
                    logPrefix, result.getTotal(), result.getRecords().size());
            return result;
        } catch (Exception e) {
            log.error("[{}] 查询失败", logPrefix, e);
            throw new BusinessException("分页查询失败: " + e.getMessage());
        }
    }

    /**
     * 构建辅警信息查询的多表联查条件包装器
     * 根据组织ID过滤辅警信息，只返回辅警（type=2）
     *
     * @param orgId 组织ID
     * @return 构建好的复杂查询条件包装器
     */
    private MPJLambdaWrapperX<BasePoliceEmp> buildAuxiliaryPoliceEmpQueryWrapper(String orgId) {
        MPJLambdaWrapperX<BasePoliceEmp> wrapper = new MPJLambdaWrapperX<>();
        wrapper.select(BasePoliceEmp::getName)
                .select(BasePoliceEmp::getType)
                .select(BasePoliceEmp::getCode)
                .select(BasePoliceEmp::getPhone)
                .selectAs(BasePoliceOrg::getName, "workUnit")
                .selectAs(BaseDict::getValue, "duty")
                .selectAs(BasePoliceEmpTag::getTagValue, "label")
                .select("CASE WHEN t.auth_user_id IS NOT NULL THEN TRUE ELSE FALSE END as isSystemUser")
                .eq(BasePoliceEmp::getAvailable, true)
                .eq(BasePoliceEmp::getType, EmpTypeEnum.ASSISTANT.getCode());
        // 只有非管理员才加组织限制
        if (!SecurityUtils.isSuperAdmin() && orgId != null) {
            wrapper.eq(BasePoliceEmp::getPoliceOrgId, orgId);
        }
        wrapper.orderByDesc(BasePoliceEmp::getCreateTime)
                .leftJoin(BasePoliceOrg.class, BasePoliceOrg::getId, BasePoliceEmp::getPoliceOrgId)
                .leftJoin(BasePoliceEmpTag.class, BasePoliceEmpTag::getEmpId, BasePoliceEmp::getId)
                .leftJoin(BaseDict.class, w -> w.apply("CAST(t3.\"key\" AS varchar) = CAST(t.duties AS varchar) AND t3.type = {0}", "SYSTEM_POLICE_EMP_RANK_LEVEL"));
        return wrapper;
    }

    /**
     * 模糊查询当前用户所在组织的警员分页列表
     * 根据关键字模糊查询当前用户所在组织下的民警信息（仅包含民警，不包含辅警、安保等其他人员）
     * 支持按姓名、工号、电话号码等字段进行模糊查询
     *
     * @param searchDTO 模糊查询参数DTO
     * @return 警员分页列表
     */
    @Override
    public CommonPageVO<PoliceListVO> fuzzySearchOrgPoliceList(PoliceFuzzySearchDTO searchDTO) {
        try {
            // 获取当前用户所在组织ID
            String orgId = getCurrentUserOrgId();
            
            // 如果是超级管理员，则不限制组织ID，可以查看所有民警
            if (cn.com.sipsg.common.security.core.util.SecurityUtils.isSuperAdmin()) {
                orgId = null;
                log.info("[警员模糊查询] 当前用户为超级管理员，查询所有民警，关键字: {}", searchDTO.getKeyword());
            } else {
                log.info("[警员模糊查询] 当前用户所在组织ID: {}, 查询关键字: {}", orgId, searchDTO.getKeyword());
            }

            // 构建查询条件
            MPJLambdaWrapperX<BasePoliceEmp> wrapper = buildFuzzySearchQueryWrapper(orgId, searchDTO);

            // 创建分页参数对象
            OrgPolicePageDTO pageDTO = new OrgPolicePageDTO();
            pageDTO.setPageNum(searchDTO.getPageNum());
            pageDTO.setPageSize(searchDTO.getPageSize());

            // 执行分页查询并返回结果
            return executePolicePageQuery(pageDTO, wrapper, "警员模糊查询分页列表");
        } catch (Exception e) {
            log.error("[警员模糊查询] 查询失败", e);
            throw new BusinessException("模糊查询警员列表失败: " + e.getMessage());
        }
    }

    /**
     * 模糊查询当前用户所在组织的辅警分页列表
     * 根据关键字模糊查询当前用户所在组织下的辅警信息（仅包含辅警，不包含民警、安保等其他人员）
     * 支持按姓名、工号、电话号码等字段进行模糊查询
     *
     * @param searchDTO 模糊查询参数DTO
     * @return 辅警分页列表
     */
    @Override
    public CommonPageVO<PoliceListVO> fuzzySearchOrgAuxiliaryPoliceList(PoliceFuzzySearchDTO searchDTO) {
        try {
            // 获取当前用户所在组织ID
            String orgId = getCurrentUserOrgId();
            log.info("[辅警模糊查询] 当前用户所在组织ID: {}, 查询关键字: {}", orgId, searchDTO.getKeyword());

            // 强制设置类型为辅警
            searchDTO.setType(EmpTypeEnum.ASSISTANT.getCode());

            // 构建查询条件
            MPJLambdaWrapperX<BasePoliceEmp> wrapper = buildFuzzySearchQueryWrapper(orgId, searchDTO);

            // 创建分页参数对象
            OrgPolicePageDTO pageDTO = new OrgPolicePageDTO();
            pageDTO.setPageNum(searchDTO.getPageNum());
            pageDTO.setPageSize(searchDTO.getPageSize());

            // 执行分页查询并返回结果
            return executePolicePageQuery(pageDTO, wrapper, "辅警模糊查询分页列表");
        } catch (Exception e) {
            log.error("[辅警模糊查询] 查询失败", e);
            throw new BusinessException("模糊查询辅警列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前用户所在组织ID
     * 封装了获取当前用户所在组织ID的通用逻辑，避免代码重复
     *
     * @return 当前用户所在组织ID
     */
    private String getCurrentUserOrgId() {
        return userOrgUtils.getCurrentUserOrgId();
    }

    /**
     * 构建模糊查询条件包装器
     * 根据组织ID和搜索条件过滤警员信息，只返回民警（type=1）
     *
     * @param orgId     组织ID
     * @param searchDTO 模糊查询参数DTO
     * @return 构建好的复杂查询条件包装器
     */
    private MPJLambdaWrapperX<BasePoliceEmp> buildFuzzySearchQueryWrapper(String orgId, PoliceFuzzySearchDTO searchDTO) {
        MPJLambdaWrapperX<BasePoliceEmp> wrapper = new MPJLambdaWrapperX<>();
        wrapper.select(BasePoliceEmp::getName)
                .select(BasePoliceEmp::getType)
                .select(BasePoliceEmp::getCode)
                .select(BasePoliceEmp::getPhone)
                .selectAs(BasePoliceOrg::getName, "workUnit")
                .selectAs(BaseDict::getValue, "duty")
                .selectAs(BasePoliceEmpTag::getTagValue, "label")
                .select("CASE WHEN t.auth_user_id IS NOT NULL THEN TRUE ELSE FALSE END as isSystemUser")
                .eq(BasePoliceEmp::getAvailable, true)
                .eq(BasePoliceEmp::getType, EmpTypeEnum.ASSISTANT.getCode());
        // 只有非管理员才加组织限制
        if (!SecurityUtils.isSuperAdmin() && orgId != null) {
            wrapper.eq(BasePoliceEmp::getPoliceOrgId, orgId);
        }
        // 关键字模糊查询
        if (searchDTO.getKeyword() != null && !searchDTO.getKeyword().isEmpty()) {
            wrapper.and(w -> w
                    .like(BasePoliceEmp::getName, searchDTO.getKeyword())
                    .or().like(BasePoliceEmp::getCode, searchDTO.getKeyword())
                    .or().like(BasePoliceEmp::getPhone, searchDTO.getKeyword())
            );
        }
        // 职务
        if (searchDTO.getDuty() != null && !searchDTO.getDuty().isEmpty()) {
            wrapper.eq(BasePoliceEmp::getDuties, searchDTO.getDuty());
        }
        // 不加工作单位条件（DTO无此字段）
        wrapper.orderByDesc(BasePoliceEmp::getCreateTime)
                .leftJoin(BasePoliceOrg.class, BasePoliceOrg::getId, BasePoliceEmp::getPoliceOrgId)
                .leftJoin(BasePoliceEmpTag.class, BasePoliceEmpTag::getEmpId, BasePoliceEmp::getId)
                .leftJoin(BaseDict.class, w -> w.apply("CAST(t3.\"key\" AS varchar) = CAST(t.duties AS varchar) AND t3.type = {0}", "SYSTEM_POLICE_EMP_RANK_LEVEL"));
        return wrapper;
    }

    /**
     * 将PoliceListDTO列表转换为PoliceListVO列表
     *
     * @param records 原始警员数据列表
     * @return 转换后的警员视图对象列表
     */
    private List<PoliceListVO> convertToPoliceListVO(List<PoliceListDTO> records) {
        if (records == null || records.isEmpty()) {
            return Collections.emptyList();
        }

        return records.stream().map(dto -> {
            PoliceListVO vo = new PoliceListVO();
            vo.setName(dto.getName());
            vo.setType(dto.getType());
            vo.setCode(dto.getCode());
            vo.setPhone(dto.getPhone());
            vo.setWorkUnit(dto.getWorkUnit());
            vo.setDuty(dto.getDuty());
            vo.setLabel(dto.getLabel());
            // 设置标签列表（兼容老数据，只有字符串时转为TagVO，id和name都用字符串）
            if (dto.getLabel() != null && !dto.getLabel().isEmpty()) {
                List<PoliceListVO.TagVO> tagVOList = Arrays.stream(dto.getLabel().split(","))
                    .map(s -> {
                        PoliceListVO.TagVO tag = new PoliceListVO.TagVO();
                        tag.setId(s);
                        tag.setName(s);
                        return tag;
                    }).collect(Collectors.toList());
                vo.setLabelList(tagVOList);
            } else {
                vo.setLabelList(Collections.emptyList());
            }
            return vo;
        }).collect(Collectors.toList());
    }
}


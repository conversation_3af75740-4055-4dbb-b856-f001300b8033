package cn.com.sipsg.module.business.module.duty.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR> lixt
 * @date : 2025-04-22 15:34:25
 * @Description: 地铁站点信息表
 */
@Data
@TableName("tbl_base_station")
public class BaseStation {

    @TableId(type = IdType.ASSIGN_ID)
    private String id;  // 主键ID

    @TableField("station_code")
    private String stationCode;  // 站点编号

    @TableField("name")
    private String name;  // 站点名称

    @TableField("status")
    private Integer status;  // 车站状态，1未启用，2启用，3废弃

    @TableField("security_level")
    private Integer securityLevel;  // 安保级别

    @TableField("phone")
    private String phone;  // 电话

    @TableField("memos")
    private String memos;  // 备注

    @TableField("passenger_control_time")
    // 客流管控时间段，如07:00-09:00
    private String passengerControlTime;

    @TableField("create_user_id")
    private String createUserId;  // 创建人

    @TableField("create_time")
    private LocalDateTime createTime;  // 创建时间

    @TableField("update_user_id")
    private String updateUserId;  // 更新人

    @TableField("update_time")
    private LocalDateTime updateTime;  // 更新时间

    @TableField("available")
    private Boolean available;  // 有效标识，TRUE:有效，FALSE:无效

    @TableField("lat")
    private BigDecimal lat;  // 纬度

    @TableField("lng")
    private BigDecimal lng;  // 经度

    @TableField("location_type")
    private Integer locationType;  // 车站位置类型，1高架，2隧道

    @TableField("police_room_phone")
    private String policeRoomPhone;  // 警务室电话

    @TableField("control_room_phone")
    private String controlRoomPhone;  // 车控室电话

    @TableField("public_phone")
    private String publicPhone;  // 车站公共电话

    @TableField("police_community")
    private String policeCommunity;  // 警务社区名称

    @TableField("operation_center")
    private String operationCenter;  // 所属运营中心

    @TableField("station_area")
    private String stationArea;  // 地铁站区名称

    @TableField("building_area")
    private BigDecimal buildingArea;  // 建筑总面积

    @TableField("layer_num")
    private String layerNum;  // 层数

    @TableField("layer_height")
    private Integer layerHeight;  // 层高

    @TableField("check_points_num")
    private Integer checkPointsNum;  // 安检点数量

    @TableField("ventilation_pavilion_num")
    private Integer ventilationPavilionNum;  // 风亭数量

    @TableField("transformer_substation")
    private String transformerSubstation;  // 所属变电站名称

    @TableField("cold_tower")
    private String coldTower;  // 所属冷塔名称

    @TableField("joint_unit")
    private String jointUnit;  // 联勤单位

    @TableField("police_org_id")
    private String policeOrgId;  // 派出所辖区id

    @TableField("police_org_name")
    private String policeOrgName;  // 派出所辖区名称

    @TableField("is_transfer")
    private Boolean isTransfer;  // 是否是换乘站

    @TableField("alias")
    private String alias;  // 站点别名

    @TableField("max_flow_count")
    private Long maxFlowCount;  // 最大容纳客流量

    @TableField("platform_type")
    private String platformType;  // 站台样式

    @TableField("platform_num")
    private Integer platformNum;  // 站台数量

    @TableField("coordinate_system")
    private String coordinateSystem;  // 坐标系（WGS-84、GCJ-02、BD-09）

    @TableField("code")
    private Long code;  // 设备/站点唯一编码
}
package cn.com.sipsg.module.business.module.duty.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR> lixt
 * @date : 2025-01-15
 * @Description: 排班人员选择VO
 */
@Data
@Schema(description = "排班人员选择VO", example = "{\"empId\":10001,\"empName\":\"张三\",\"teamId\":1,\"teamName\":\"第一班组\"}")
public class DutyPersonSelectVO {
    
    @Schema(description = "人员编号", example = "10001")
    private Long empId;
    
    @Schema(description = "人员姓名", example = "张三")
    private String empName;
    
    @Schema(description = "班组ID", example = "1")
    private Long teamId;
    
    @Schema(description = "班组名称", example = "第一班组")
    private String teamName;
}
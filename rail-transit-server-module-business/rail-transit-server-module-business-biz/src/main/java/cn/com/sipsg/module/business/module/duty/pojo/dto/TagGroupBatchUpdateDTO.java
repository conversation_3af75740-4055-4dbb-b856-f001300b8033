package cn.com.sipsg.module.business.module.duty.pojo.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 标签分组批量修改请求体
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TagGroupBatchUpdateDTO {
    
    @Schema(description = "标签分组映射列表", required = true)
    @NotEmpty(message = "标签分组映射列表不能为空")
    @Valid
    @JsonProperty("tagGroupMappings")
    private List<TagGroupMapping> tagGroupMappings;
    
    /**
     * 标签与分组的映射关系
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class TagGroupMapping {
        @Schema(description = "标签ID", required = true)
        @NotNull(message = "标签ID不能为空")
        @JsonProperty("tagId")
        private String tagId;
        
        @Schema(description = "新分组ID", required = true)
        @NotNull(message = "新分组ID不能为空")
        @JsonProperty("groupId")
        private String groupId;
    }
}
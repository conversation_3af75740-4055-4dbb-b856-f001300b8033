package cn.com.sipsg.module.business.module.supervision.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

import cn.com.sipsg.common.pojo.bo.PageBO;

/**
 * 服务群众上报分页查询DTO
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "服务群众上报分页查询DTO")
public class CivilServiceReportPageDTO extends PageBO {

    @Schema(description = "标题名称", example = "设备故障")
    private String title;

    @Schema(description = "审核状态", example = "PENDING")
    private String status;

    @Schema(description = "上报开始时间", example = "2025-07-16 00:00:00")
    private LocalDateTime reportStartTime;

    @Schema(description = "上报结束时间", example = "2025-07-16 23:59:59")
    private LocalDateTime reportEndTime;
}

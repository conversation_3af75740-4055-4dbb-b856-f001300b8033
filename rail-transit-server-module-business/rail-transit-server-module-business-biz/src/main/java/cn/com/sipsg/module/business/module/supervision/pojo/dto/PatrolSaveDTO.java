package cn.com.sipsg.module.business.module.supervision.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 巡逻信息编辑保存DTO
 * 用于巡逻信息的新增和编辑操作
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
@Schema(description = "巡逻信息编辑保存DTO")
public class PatrolSaveDTO {

    @Schema(description = "主键ID，新增时为空，编辑时必填")
    private String id;

    @Schema(description = "单位ID")
    @NotBlank(message = "单位ID不能为空")
    private String policeOrgId;

    @Schema(description = "填报时间")
    @NotNull(message = "填报时间不能为空")
    private LocalDateTime formTime;

    @Schema(description = "巡逻车呼号")
    @NotBlank(message = "巡逻车呼号不能为空")
    private String call;

    @Schema(description = "车牌号")
    @NotBlank(message = "车牌号不能为空")
    private String vehicle;

    @Schema(description = "巡逻区域")
    @NotBlank(message = "巡逻区域不能为空")
    private String area;

    @Schema(description = "巡逻信息列表")
    @NotEmpty(message = "巡逻信息列表不能为空")
    @Valid
    private List<PatrolInfoDTO> patrolInfoList;

    @Schema(description = "备注")
    private String remark;

    /**
     * 巡逻信息明细DTO
     */
    @Data
    @Schema(description = "巡逻信息明细DTO")
    public static class PatrolInfoDTO {
        @Schema(description = "主键ID，新增时为空，编辑时必填")
        private String id;

        @Schema(description = "巡逻开始时间")
        private LocalDateTime startTime;

        @Schema(description = "巡逻结束时间")
        private LocalDateTime endTime;

        @Schema(description = "姓名")
        private String name;

        @Schema(description = "手机号")
        private String phone;

        @Schema(description = "值守频点")
        private String freq;

        @Schema(description = "民警数")
        private Integer policeNum;

        @Schema(description = "武警数")
        private Integer fireNum;

        @Schema(description = "辅警数")
        private Integer auxNum;

        @Schema(description = "其他警力数")
        private Integer otherNum;

        @Schema(description = "枪支数")
        private Integer gunNum;

        @Schema(description = "枪支情况")
        private String gunSituation;

        @Schema(description = "GPS/北斗")
        private String gps;

        @Schema(description = "图片传输设备")
        private String device;
    }
}
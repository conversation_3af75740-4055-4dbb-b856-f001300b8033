package cn.com.sipsg.module.business.module.supervision.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 关注记录保存/更新DTO
 */
@Data
@Schema(description = "关注记录保存/更新DTO")
public class PersonnelAttentionSaveDTO {

    @Schema(description = "主键ID（更新时必填，新增时不填）", example = "1234567890abcdef12345678")
    private String id;

    @Schema(description = "关注方式 1(见面核查)、2(监控关注)、3(便衣贴靠)、4(出警跟防)、99(其他)", required = true, example = "1")
    @NotNull(message = "关注方式不能为空")
    private Integer attentionType;

    @Schema(description = "处置时间", required = true, example = "2025-07-23 09:00:00")
    @NotNull(message = "处置时间不能为空")
    private LocalDateTime disposeTime;

    @Schema(description = "预警来源 1(101预警)、2(票卡预警)、3(盘查)、4(其他)", required = true, example = "1")
    @NotNull(message = "预警来源不能为空")
    private Integer warningSources;

    @Schema(description = "人员类型 1(在苏新疆人)、2(涉恐人员)、3(精神病肇事肇祸)、4(个人扬言极端)、99(其他)", required = true, example = "1")
    @NotNull(message = "人员类型不能为空")
    private Integer personnelType;

    @Schema(description = "处置单位ID", required = true, example = "123456")
    @NotNull(message = "处置单位ID不能为空")
    private String policeOrgId;

    @Schema(description = "处置单位名称", required = true, example = "某某派出所")
    private String policeOrgName;

    @Schema(description = "关注对象人数", example = "1")
    private Integer attentionNum;

    @Schema(description = "进站站点编号", example = "ST001")
    private String inStationCode;

    @Schema(description = "进站站点名称", example = "某某站")
    private String inStationName;

    @Schema(description = "出站站点编号", example = "ST002")
    private String outStationCode;

    @Schema(description = "出站站点名称", example = "某某站")
    private String outStationName;

    @Schema(description = "交接单位ID", example = "123457")
    private String handoverPoliceOrgId;

    @Schema(description = "交接单位名称", example = "某某派出所")
    private String handoverPoliceOrgName;

    @Schema(description = "交接站点编号", example = "ST003")
    private String handoverStationCode;

    @Schema(description = "交接站点名称", example = "某某站")
    private String handoverStationName;

    @Schema(description = "交接时间", example = "2025-07-23 10:00:00")
    private LocalDateTime handoverTime;

    @Schema(description = "现场图片URL列表(JSON字符串)", example = "[\"http://example.com/image1.jpg\", \"http://example.com/image2.jpg\"]")
    private String imageUrls;

    @Schema(description = "备注", example = "相关备注信息")
    private String remark;
}
package cn.com.sipsg.module.business.module.duty.mapper;


import cn.com.sipsg.common.mybatis.core.mapper.BaseMapperX;
import cn.com.sipsg.module.business.module.duty.entity.EmpSignIn;
import cn.com.sipsg.module.business.module.duty.pojo.dto.SignInEmpDTO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> lixt
 * @date : 2025-04-24 15:18:26
 * @Description:
 */
@Mapper
public interface EmpSignInMapper extends BaseMapperX<EmpSignIn> {
    /**
     * 根据日期范围查询签到信息
     *
     * @param startTime 查询的起始时间
     * @param endTime   查询的结束时间
     * @return 返回在指定时间范围内的签到员工信息列表
     */
//    @Select("SELECT " +
//            "s.police_emp_id AS emp_id, " +
//            "s.location_relation_id, " +
//            "s.location_info_type " +
//            "FROM tbl_sign_in s " +
//            "WHERE s.available = true " +
//            "AND s.sign_time >= #{startTime} " +
//            "AND s.sign_time <= #{endTime} " +
//            "ORDER BY s.sign_time DESC")
//    List<SignInEmpDTO> signEmpByDate(@Param("startTime") long startTime, @Param("endTime") long endTime);
    default List<SignInEmpDTO> signEmpByDate(@Param("startTime") long startTime, @Param("endTime") long endTime) {
        // 使用 BaseMapperX 提供的 selectList 方法，构建查询条件
        return selectList(
                new LambdaQueryWrapper<EmpSignIn>()
                        .eq(EmpSignIn::getAvailable, true) // available = true
                        .ge(EmpSignIn::getSignTime, startTime) // sign_time >= startTime
                        .le(EmpSignIn::getSignTime, endTime) // sign_time <= endTime
                        .orderByDesc(EmpSignIn::getSignTime) // 按照 sign_time 降序排序
        ).stream()
                .map(signIn -> {
                    // 将查询结果映射成 DTO 对象
                    SignInEmpDTO dto = new SignInEmpDTO();
                    dto.setEmpId(signIn.getPoliceEmpId());
                    dto.setLocationRelationId(signIn.getLocationRelationId());
                    dto.setLocationInfoType(signIn.getLocationInfoType());
                    return dto;
                })
                .collect(Collectors.toList());
    }


    /**
     * 获取所有签到记录
     *
     * @param startTime 开始时间戳
     * @param endTime   结束时间戳
     * @return 返回在指定时间范围内的所有签到记录，按签到时间降序排列
     */
    default List<EmpSignIn> getAllSignRecord(@Param("startTime") long startTime, @Param("endTime") long endTime) {
        LambdaQueryWrapper<EmpSignIn> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EmpSignIn::getAvailable, true)
                .ge(EmpSignIn::getSignTime, startTime)
                .le(EmpSignIn::getSignTime, endTime)
                .orderByDesc(EmpSignIn::getSignTime);

        return selectList(queryWrapper);
    }

    ;
}

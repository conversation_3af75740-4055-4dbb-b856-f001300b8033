package cn.com.sipsg.module.business.module.duty.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> lixt
 * @date : 2025-05-08 11:19:05
 * @Description: 排班规则明细表
 */
@Data
@TableName("tbl_duty_shifts_detail")
public class DutyShiftsDetail {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id; // 主键

    @TableField("week")
    private Integer week; // 星期

    @TableField("platoon_group_id")
    private String platoonGroupId; // 排班组主键

    @TableField("shifts_id")
    private String shiftsId; // 班次主键

    @TableField("content")
    private String content; // 值班时间

    @TableField("serial_num")
    private Integer serialNum; // 序号

    @TableField("create_time")
    private LocalDateTime createTime; // 创建时间

    @TableField("create_user_id")
    private String createUserId; // 创建者ID

    @TableField("update_time")
    private LocalDateTime updateTime; // 更新时间

    @TableField("update_user_id")
    private String updateUserId; // 更新者ID

    @TableField("available")
    private Boolean available; // 是否有效
}

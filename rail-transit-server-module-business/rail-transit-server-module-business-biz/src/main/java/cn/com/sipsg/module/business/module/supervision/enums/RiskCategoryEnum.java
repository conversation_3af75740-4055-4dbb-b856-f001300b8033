package cn.com.sipsg.module.business.module.supervision.enums;

/**
 * 隐患类型枚举
 */
public enum RiskCategoryEnum {
    DEVICE("1", "设备设施类隐患"),
    PERSON("2", "人员行为类隐患"),
    ENVIRONMENT("3", "环境及外部风险类隐患"),
    MANAGEMENT("4", "管理及制度类隐患"),
    SPECIAL("5", "专项排查类隐患");

    private final String code;
    private final String label;

    RiskCategoryEnum(String code, String label) {
        this.code = code;
        this.label = label;
    }

    public String getCode() {
        return code;
    }

    public String getLabel() {
        return label;
    }

    public static String getLabelByCode(String code) {
        for (RiskCategoryEnum e : values()) {
            if (e.code.equals(code)) {
                return e.label;
            }
        }
        return null;
    }
} 
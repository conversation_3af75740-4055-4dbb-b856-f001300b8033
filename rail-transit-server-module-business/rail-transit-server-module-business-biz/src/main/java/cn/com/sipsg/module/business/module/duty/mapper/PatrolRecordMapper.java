package cn.com.sipsg.module.business.module.duty.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import cn.com.sipsg.module.business.module.duty.pojo.vo.PatrolRecordVO;
import cn.com.sipsg.module.business.module.duty.pojo.dto.PatrolListQueryDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface PatrolRecordMapper extends BaseMapper<PatrolRecordVO> {
    
    /**
     * 获取巡查记录列表（兼容方法，不推荐使用）
     * 
     * @param page 分页参数
     * @param query 查询条件
     * @return 巡查记录分页列表
     */
    Page<PatrolRecordVO> getPatrolRecordList(@Param("page") Page<?> page, @Param("query") PatrolListQueryDTO query);
    
    /**
     * 获取符合条件的巡查记录总数
     * 
     * @param query 查询条件
     * @return 记录总数
     */
    int countPatrolRecords(@Param("query") PatrolListQueryDTO query);
    
    /**
     * 获取指定范围的巡查记录列表（使用LIMIT实现分页）
     * 
     * @param query 查询条件
     * @param offset 起始偏移量
     * @param limit 获取数量
     * @return 巡查记录列表
     */
    List<PatrolRecordVO> getPatrolRecordsWithLimit(@Param("query") PatrolListQueryDTO query, @Param("offset") int offset, @Param("limit") int limit);
    
    /**
     * 获取所有符合条件的巡查记录（用于内存分页的备用方案）
     * 
     * @param query 查询条件
     * @return 巡查记录列表
     */
    List<PatrolRecordVO> getAllPatrolRecords(@Param("query") PatrolListQueryDTO query);
}

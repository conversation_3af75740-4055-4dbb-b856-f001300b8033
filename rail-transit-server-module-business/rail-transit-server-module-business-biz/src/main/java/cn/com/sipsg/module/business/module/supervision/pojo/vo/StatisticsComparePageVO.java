package cn.com.sipsg.module.business.module.supervision.pojo.vo;

import lombok.Data;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "日常数据统计分页VO")
@Data
public class StatisticsComparePageVO {
    @Schema(description = "主键ID")
    private String id;
    @Schema(description = "单位ID")
    private String unitId; // 单位ID
    @Schema(description = "单位名称")
    private String unitName; // 单位名称
    @Schema(description = "数据时间")
    private LocalDateTime statisticsTime; // 数据时间

    @Schema(description = "出警力（人次）站区")
    private PoliceForceVO policeForce; // 出警力（人次）站区
    @Schema(description = "身份检查情况")
    private IdentityCheckVO identityCheck; // 身份检查情况

    @Schema(description = "警力信息")
    @Data
    public static class PoliceForceVO {
        @Schema(description = "总警力")
        private Integer police; // 总警力
        @Schema(description = "民警")
        private Integer policeCount; // 民警
        @Schema(description = "辅警")
        private Integer auxiliaryPolice; // 辅警
        @Schema(description = "群防群治")
        private Integer preventionTreatment; // 群防群治
    }

    @Schema(description = "身份检查信息")
    @Data
    public static class IdentityCheckVO {
        @Schema(description = "检查总数")
        private Integer checksNum; // 检查总数
        @Schema(description = "警务通")
        private Integer policeCommunication; // 警务通
        @Schema(description = "PDA")
        private Integer pda; // PDA
        @Schema(description = "蓝信")
        private Integer blueLetter; // 蓝信
        @Schema(description = "盘查抓获")
        private Integer investigation; // 盘查抓获
        @Schema(description = "吸毒前科")
        private Integer drugRecord; // 吸毒前科
        @Schema(description = "其他违法")
        private Integer otherViolations; // 其他违法
    }
}
package cn.com.sipsg.module.business.module.supervision.pojo.dto;

import cn.com.sipsg.module.business.module.supervision.enums.AuditStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 服务群众上报分页结果DTO
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Data
@Schema(description = "服务群众上报分页结果DTO")
public class CivilServiceReportPageResultDTO {

    @Schema(description = "主键ID", example = "1")
    private String id;

    @Schema(description = "标题名称", example = "设备故障")
    private String title;

    @Schema(description = "上报时间", example = "2025-07-16 08:30:00")
    private LocalDateTime reportTime;

    @Schema(description = "内容", example = "地铁站内设备故障，需要维修")
    private String content;

    @Schema(description = "审核状态", example = "PENDING")
    private AuditStatusEnum status;

    @Schema(description = "是否满意：true-满意，false-不满意，null-未评价", example = "true")
    private Boolean isSatisfied;
    
    @Schema(description = "站点编号", example = "S001")
    private String stationCode;

    @Schema(description = "站点名称", example = "站点1")
    private String stationName;
    
    @Schema(description = "上报人名称", example = "张三")
    private String reporterName;
}
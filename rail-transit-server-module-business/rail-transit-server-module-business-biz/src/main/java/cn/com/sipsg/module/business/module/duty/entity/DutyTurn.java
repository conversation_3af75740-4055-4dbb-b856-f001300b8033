package cn.com.sipsg.module.business.module.duty.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> lixt
 * @date : 2025-05-08 11:16:26
 * @Description: 班次表
 */
@Data
@TableName("tbl_duty_turn")
public class DutyTurn {

    @TableId(type = IdType.ASSIGN_ID)
    private String id;  // 班次主键ID

    @TableField("police_org_id")
    private String policeOrgId;  // 单位主键

    @TableField("turn_name")
    private String turnName;  // 班次名称

    @TableField("start_time")
    private String startTime;  // 上班时间

    @TableField("start_day")
    private Integer startDay;  // 上班日 1: 当日 2: 次日

    @TableField("end_time")
    private String endTime;  // 下班时间

    @TableField("end_day")
    private Integer endDay;  // 下班日 1: 当日 2: 次日

    @TableField("create_time")
    private LocalDateTime createTime;

    @TableField("update_time")
    private LocalDateTime updateTime;

    @TableField("create_user_id")
    private String createUserId;

    @TableField("update_user_id")
    private String updateUserId;

    @TableField("available")
    private Boolean available;
}
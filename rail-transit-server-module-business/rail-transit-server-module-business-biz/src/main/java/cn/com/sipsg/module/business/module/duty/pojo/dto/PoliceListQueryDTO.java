package cn.com.sipsg.module.business.module.duty.pojo.dto;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;

@Data
public class PoliceListQueryDTO {
    @Schema(description = "姓名/警号/身份证号", example = "张三")
    private String keyword;
    @Schema(description = "人员类型：1民警，2辅警，3安保，4警犬", example = "1")
    private Integer type; 
    @Schema(description = "职务", example = "001")
    private String duty;
    @Schema(description = "工作单位", example = "001")
    private String workUnit;
    @Schema(description = "页码", example = "1")
    private Integer pageNum;
    @Schema(description = "每页数量", example = "10")
    private Integer pageSize = 10;
}
package cn.com.sipsg.module.business.module.supervision.enums;

import lombok.Getter;

/**
 * 安检人员年龄分组枚举
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Getter
public enum SecurityEmpAgeGroupEnum {
    
    UNDER_18("18岁及以下", 0, 18),
    AGE_18_TO_28("18-28", 19, 28),
    AGE_28_TO_40("28-40", 29, 40),
    OVER_40("40岁及以上", 41, Integer.MAX_VALUE),
    UNKNOWN("未知", null, null);
    
    private final String displayName;
    private final Integer minAge;
    private final Integer maxAge;
    
    SecurityEmpAgeGroupEnum(String displayName, Integer minAge, Integer maxAge) {
        this.displayName = displayName;
        this.minAge = minAge;
        this.maxAge = maxAge;
    }
    
    /**
     * 根据年龄获取对应的年龄分组
     *
     * @param age 年龄
     * @return 年龄分组
     */
    public static SecurityEmpAgeGroupEnum fromAge(Integer age) {
        if (age == null) {
            return UNKNOWN;
        }
        
        for (SecurityEmpAgeGroupEnum group : values()) {
            if (group != UNKNOWN && 
                    (group.minAge <= age && age <= group.maxAge)) {
                return group;
            }
        }
        
        return UNKNOWN;
    }
    
    /**
     * 获取所有有效的年龄分组（不包括UNKNOWN）
     *
     * @return 有效的年龄分组数组
     */
    public static SecurityEmpAgeGroupEnum[] getValidGroups() {
        return new SecurityEmpAgeGroupEnum[] {
            UNDER_18, AGE_18_TO_28, AGE_28_TO_40, OVER_40
        };
    }
}

package cn.com.sipsg.module.business.module.duty.enums;

import cn.com.sipsg.common.enums.BaseEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldNameConstants;

/**
 * 站点状态枚举
 *
 * <AUTHOR>
 * @since 2025-05-23
 */
@Getter
@FieldNameConstants
@RequiredArgsConstructor
public enum StationStatusEnum implements BaseEnum {

    NOT_ENABLED(1, "未启用"),
    ENABLED(2, "启用"),
    ABANDONED(3, "废弃");

    /**
     * 状态值
     */
    @EnumValue
    @JsonValue
    private final Integer code;

    /**
     * 状态名
     */
    private final String desc;

    /**
     * 判断站点是否启用
     *
     * @param status 状态值
     * @return 是否启用
     */
    public static boolean isEnabled(Integer status) {
        return ENABLED.getCode().equals(status);
    }

    /**
     * 判断站点是否未启用
     *
     * @param status 状态值
     * @return 是否未启用
     */
    public static boolean isNotEnabled(Integer status) {
        return NOT_ENABLED.getCode().equals(status);
    }

    /**
     * 判断站点是否废弃
     *
     * @param status 状态值
     * @return 是否废弃
     */
    public static boolean isAbandoned(Integer status) {
        return ABANDONED.getCode().equals(status);
    }
}

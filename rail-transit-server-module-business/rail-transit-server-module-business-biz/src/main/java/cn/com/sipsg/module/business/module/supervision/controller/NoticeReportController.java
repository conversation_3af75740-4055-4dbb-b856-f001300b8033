package cn.com.sipsg.module.business.module.supervision.controller;

import cn.com.sipsg.module.business.module.supervision.pojo.dto.NoticeReportSaveDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.NoticeReportIssueDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.SuperviseRemindVO;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.com.sipsg.common.web.core.controller.BaseController;
import cn.com.sipsg.common.pojo.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.IdDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.NoticeReportPageReqDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.NoticeReportVO;
import cn.com.sipsg.module.business.module.supervision.service.NoticeReportService;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 通知公告相关接口
 */
@Tag(name = "通知公告", description = "通知公告相关接口")
@RequiredArgsConstructor
@RestController
@RequestMapping("/noticeReport")
public class NoticeReportController extends BaseController {
    private final NoticeReportService noticeReportService;

    /**
     * 【通报信息编辑】保存或更新
     * 用于保存或更新通报信息。
     *
     * @param dto 通报信息保存/更新DTO
     * @return 操作结果
     */
    @Operation(summary = "【通报信息编辑】保存或更新", description = "本接口用于保存或更新通报信息，支持新增和编辑两种操作模式。")
    @PostMapping("/save")
    public CommonResult<Boolean> saveOrUpdateNoticeReport(@RequestBody @Valid NoticeReportSaveDTO dto) {
        return handle(() -> CommonResult.data(noticeReportService.saveOrUpdateNoticeReport(dto)));
    }

    /**
     * 【通报信息】分页查询
     * 用于分页获取通报信息数据，支持按标题、执法单位ID、执法时间范围查询。
     * 支持tab筛选：ALL-全部通报、ISSUED-我下发的、RECEIVED-我接收的
     *
     * @param dto 分页查询参数DTO
     * @return 通报信息分页列表
     */
    @Operation(summary = "【通报信息】分页查询", description = "本接口用于分页获取通报信息数据，支持按标题、执法单位ID、执法时间范围查询，支持tab筛选功能。")
    @PostMapping("/page")
    public CommonResult<CommonPageVO<NoticeReportVO>> pageNoticeReport(@RequestBody @Valid NoticeReportPageReqDTO dto) {
        return handle(() -> CommonResult.data(noticeReportService.pageNoticeReport(dto)));
    }

    /**
     * 【通报信息详情】查询
     * 用于获取指定通报信息的详细信息。
     *
     * @param dto 通报信息详情查询请求参数
     * @return 通报信息详情
     */
    @Operation(summary = "【通报信息详情】查询", description = "本接口用于获取指定通报信息的详细信息。")
    @PostMapping("/detail")
    public CommonResult<NoticeReportSaveDTO> getNoticeReportDetail(@RequestBody @Valid IdDTO dto) {
        return handle(() -> CommonResult.data(noticeReportService.getNoticeReportDetail(dto.getId())));
    }

    /**
     * 【通报信息删除】删除
     * 用于删除指定的通报信息记录。
     *
     * @param dto 删除请求参数
     * @return 操作结果
     */
    @Operation(summary = "【通报信息删除】删除", description = "本接口用于删除指定的通报信息记录，删除操作不可逆，请谨慎使用。")
    @PostMapping("/delete")
    public CommonResult<Boolean> deleteNoticeReport(@RequestBody @Valid IdDTO dto) {
        return handle(() -> CommonResult.data(noticeReportService.deleteNoticeReport(dto.getId())));
    }

    /**
     * 【通报信息下发】下发
     * 用于将通报信息下发给指定的单位。
     *
     * @param dto 下发请求参数，包含通报信息ID和下发单位列表
     * @return 操作结果
     */
    @Operation(summary = "【通报信息下发】下发", description = "本接口用于将通报信息下发给指定的单位，支持同时下发给多个单位。")
    @PostMapping("/issue")
    public CommonResult<Boolean> issueNoticeReport(@RequestBody @Valid NoticeReportIssueDTO dto) {
        return handle(() -> CommonResult.data(noticeReportService.issueNoticeReport(dto)));
    }
}
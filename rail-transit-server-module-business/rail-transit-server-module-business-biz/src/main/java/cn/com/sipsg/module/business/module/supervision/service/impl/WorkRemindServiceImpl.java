package cn.com.sipsg.module.business.module.supervision.service.impl;

import cn.com.sipsg.common.exception.BusinessException;
import cn.com.sipsg.common.mybatis.core.query.LambdaQueryWrapperX;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.security.core.util.SecurityUtils;
import cn.com.sipsg.module.business.module.common.util.OrgRecursiveUtils;
import cn.com.sipsg.module.business.module.common.util.UserOrgUtils;
import cn.com.sipsg.module.business.module.supervision.entity.WorkRemind;
import cn.com.sipsg.module.business.module.supervision.mapper.WorkRemindMapper;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.WorkRemindQueryDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.WorkRemindSaveDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.WorkRemindVO;
import cn.com.sipsg.module.business.module.supervision.service.WorkRemindService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 工作提醒Service实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WorkRemindServiceImpl implements WorkRemindService {

    private final WorkRemindMapper workRemindMapper;
    private final UserOrgUtils userOrgUtils;
    private final OrgRecursiveUtils orgRecursiveUtils;

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 保存或更新工作提醒信息。
     * <p>
     * 根据传入的 {@link WorkRemindSaveDTO} 对象判断是新增还是更新操作：
     * - 若 DTO 中包含有效的 id，则执行更新操作；
     * - 否则执行新增操作。
     * </p>
     * <p>
     * 在更新时会校验数据是否存在，若不存在则抛出业务异常；
     * 新增时设置创建时间和创建人等基础信息。
     * </p>
     * <p>
     * 该方法支持事务管理，遇到任何异常都会回滚。
     * </p>
     *
     * @param dto 工作提醒保存传输对象，包含标题、内容、提醒时间及关联单位信息，不能为空
     * @return 操作是否成功，成功返回 true，失败抛出异常
     * @throws BusinessException 当工作提醒不存在（更新场景）或保存过程中发生错误时抛出
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveOrUpdateWorkRemind(WorkRemindSaveDTO dto) {
        try {
            WorkRemind workRemind;
            boolean isUpdate = StringUtils.hasText(dto.getId());

            if (isUpdate) {
                // 修改操作：查询原记录并校验是否存在
                workRemind = workRemindMapper.selectById(dto.getId());
                if (workRemind == null) {
                    throw new BusinessException("工作提醒不存在");
                }
                workRemind.setUpdateTime(LocalDateTime.now());
                workRemind.setUpdateUserId(SecurityUtils.getLoginUserId());
            } else {
                // 新增操作：初始化基本字段
                workRemind = new WorkRemind();
                workRemind.setCreateTime(LocalDateTime.now());
                workRemind.setCreateUserId(SecurityUtils.getLoginUserId());
                workRemind.setUpdateTime(LocalDateTime.now());
                workRemind.setUpdateUserId(SecurityUtils.getLoginUserId());
                workRemind.setAvailable(true);
            }

            // 设置通用字段值
            workRemind.setTitle(dto.getTitle());
            workRemind.setContent(dto.getContent());

            // 将多个单位ID拼接为逗号分隔字符串存储
            String orgIds = dto.getOrgInfos().stream()
                    .map(WorkRemindSaveDTO.OrgInfoDTO::getOrgId)
                    .collect(Collectors.joining(","));
            workRemind.setOrgId(orgIds);

            // 将多个单位名称拼接为逗号分隔字符串存储
            String orgNames = dto.getOrgInfos().stream()
                    .map(WorkRemindSaveDTO.OrgInfoDTO::getOrgName)
                    .collect(Collectors.joining(","));
            workRemind.setOrgName(orgNames);

            // 处理提醒时间字段，格式不正确时使用当前时间
            if (StringUtils.hasText(dto.getRemindTime())) {
                try {
                    workRemind.setRemindTime(LocalDateTime.parse(dto.getRemindTime(), DATE_TIME_FORMATTER));
                } catch (DateTimeParseException e) {
                    log.warn("提醒时间格式解析失败，使用当前时间: {}", dto.getRemindTime());
                    workRemind.setRemindTime(LocalDateTime.now());
                }
            } else {
                workRemind.setRemindTime(LocalDateTime.now());
            }

            // 执行数据库插入或更新操作
            int result = isUpdate ? workRemindMapper.updateById(workRemind) : workRemindMapper.insert(workRemind);
            return result > 0;
        } catch (Exception e) {
            log.error("保存工作提醒失败", e);
            throw new BusinessException("保存工作提醒失败: " + e.getMessage());
        }
    }


    /**
     * 根据ID获取工作提醒的详细信息
     *
     * @param id 工作提醒的唯一标识符
     * @return WorkRemindSaveDTO 包含工作提醒详细信息的数据传输对象
     * @throws BusinessException 当工作提醒不存在或获取详情失败时抛出业务异常
     */
    @Override
    public WorkRemindSaveDTO getWorkRemindDetail(String id) {
        try {
            WorkRemind workRemind = workRemindMapper.selectById(id);
            if (workRemind == null || !workRemind.getAvailable()) {
                throw new BusinessException("工作提醒不存在");
            }

            WorkRemindSaveDTO dto = new WorkRemindSaveDTO();
            dto.setId(workRemind.getId());
            dto.setTitle(workRemind.getTitle());
            dto.setContent(workRemind.getContent());

            // 将逗号分隔的组织ID和名称转换为OrgInfoDTO列表
            List<WorkRemindSaveDTO.OrgInfoDTO> orgInfos = new ArrayList<>();
            if (StringUtils.hasText(workRemind.getOrgId()) && StringUtils.hasText(workRemind.getOrgName())) {
                String[] orgIds = workRemind.getOrgId().split(",");
                String[] orgNames = workRemind.getOrgName().split(",");

                // 确保ID和名称数量一致，避免数组越界
                int minLength = Math.min(orgIds.length, orgNames.length);
                for (int i = 0; i < minLength; i++) {
                    WorkRemindSaveDTO.OrgInfoDTO orgInfo = new WorkRemindSaveDTO.OrgInfoDTO();
                    orgInfo.setOrgId(orgIds[i].trim());
                    orgInfo.setOrgName(orgNames[i].trim());
                    orgInfos.add(orgInfo);
                }
            }
            dto.setOrgInfos(orgInfos);

            // 格式化提醒时间
            if (workRemind.getRemindTime() != null) {
                dto.setRemindTime(workRemind.getRemindTime().format(DATE_TIME_FORMATTER));
            }

            return dto;
        } catch (Exception e) {
            log.error("获取工作提醒详情失败", e);
            throw new BusinessException("获取工作提醒详情失败: " + e.getMessage());
        }
    }


    /**
     * 删除工作提醒
     *
     * @param id 工作提醒ID
     * @return 删除结果，true表示删除成功，false表示删除失败
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWorkRemind(String id) {
        try {
            WorkRemind workRemind = workRemindMapper.selectById(id);
            if (workRemind == null) {
                throw new BusinessException("工作提醒不存在");
            }

            // 逻辑删除
            workRemind.setAvailable(false);
            workRemind.setUpdateTime(LocalDateTime.now());
            workRemind.setUpdateUserId(SecurityUtils.getLoginUserId());

            int result = workRemindMapper.updateById(workRemind);
            return result > 0;
        } catch (Exception e) {
            log.error("删除工作提醒失败", e);
            throw new BusinessException("删除工作提醒失败: " + e.getMessage());
        }
    }


    /**
     * 分页查询工作提醒信息
     *
     * @param queryDTO 查询条件封装对象，包含标题、组织ID、开始时间、结束时间、当前页码和页大小等参数
     * @return 返回分页结果封装对象，包含工作提醒的VO列表及分页信息（总记录数、当前页、页大小等）
     */
    @Override
    public CommonPageVO<WorkRemindVO> pageWorkRemind(WorkRemindQueryDTO queryDTO) {
        try {
            // 构建查询条件
            LambdaQueryWrapperX<WorkRemind> wrapper = new LambdaQueryWrapperX<WorkRemind>()
                    .eq(WorkRemind::getAvailable, true)
                    .likeIfPresent(WorkRemind::getTitle, queryDTO.getTitle())
                    .likeIfPresent(WorkRemind::getOrgId, queryDTO.getOrgId())
                    .geIfPresent(WorkRemind::getRemindTime, queryDTO.getStartTime())
                    .leIfPresent(WorkRemind::getRemindTime, queryDTO.getEndTime())
                    .orderByDesc(WorkRemind::getCreateTime);

            // 数据权限控制：根据用户角色决定是否添加组织过滤条件
            if (SecurityUtils.isSuperAdmin()) {
                // 超级管理员可以查看所有数据，不添加组织过滤条件
                log.debug("当前用户为超级管理员，可查看所有工作提醒数据");
            } else {
                // 普通用户只能查看当前单位及下级单位的数据
                try {
                    String currentOrgId = userOrgUtils.getCurrentUserOrgId();
                    if (StringUtils.hasText(currentOrgId)) {
                        List<String> orgIds = orgRecursiveUtils.getOrgAndChildrenIdsStr(currentOrgId);
                        if (!orgIds.isEmpty()) {
                            wrapper.in(WorkRemind::getOrgId, orgIds);
                            log.debug("普通用户数据权限过滤，可访问组织ID: {}", orgIds);
                        } else {
                            // 如果没有找到任何组织ID，返回空结果
                            wrapper.eq(WorkRemind::getOrgId, "INVALID_ORG_ID");
                            log.warn("当前用户无可访问的组织权限");
                        }
                    } else {
                        // 如果当前用户没有关联组织，返回空结果
                        wrapper.eq(WorkRemind::getOrgId, "INVALID_ORG_ID");
                        log.warn("当前用户未关联任何组织");
                    }
                } catch (Exception e) {
                    log.error("获取用户组织权限失败", e);
                    // 出现异常时，为安全起见，返回空结果
                    wrapper.eq(WorkRemind::getOrgId, "INVALID_ORG_ID");
                }
            }

            // 执行分页查询
            IPage<WorkRemind> page = new Page<>(queryDTO.getCurrent(), queryDTO.getSize());
            IPage<WorkRemind> resultPage = workRemindMapper.selectPage(page, wrapper);

            // 将查询结果转换为VO对象列表
            List<WorkRemindVO> voList = resultPage.getRecords().stream()
                    .map(this::convertToVO)
                    .collect(Collectors.toList());

            // 构建并返回分页VO结果
            return CommonPageVO.<WorkRemindVO>builder()
                    .records(voList)
                    .total(resultPage.getTotal())
                    .current(resultPage.getCurrent())
                    .size(resultPage.getSize())
                    .build();
        } catch (Exception e) {
            log.error("分页查询工作提醒失败", e);
            throw new BusinessException("分页查询工作提醒失败: " + e.getMessage());
        }
    }


    /**
     * 转换为VO
     *
     * @param workRemind 工作提醒实体对象
     * @return 转换后的工作提醒VO对象
     */
    private WorkRemindVO convertToVO(WorkRemind workRemind) {
        // 创建VO对象并设置基本属性
        WorkRemindVO vo = new WorkRemindVO();
        vo.setId(workRemind.getId());
        vo.setTitle(workRemind.getTitle());
        vo.setOrgName(workRemind.getOrgName());
        vo.setRemindTime(workRemind.getRemindTime());
        vo.setContent(workRemind.getContent());
        vo.setCreateTime(workRemind.getCreateTime());
        return vo;
    }

}
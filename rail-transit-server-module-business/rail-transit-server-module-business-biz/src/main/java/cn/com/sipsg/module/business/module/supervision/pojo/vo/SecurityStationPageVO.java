package cn.com.sipsg.module.business.module.supervision.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 安检点位分页查询结果VO
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "安检点位分页查询结果VO")
public class SecurityStationPageVO {

    @Schema(description = "安检点位 ID")
    private String id;

    @Schema(description = "安检点位名称")
    private String name;
    
    @Schema(description = "线路 ID")
    private String subwayId;

    @Schema(description = "所属地铁线路")
    private String subwayName;
    
    @Schema(description = "站点 ID")
    private String subwayStationId;

    @Schema(description = "地铁站点名称")
    private String subwayStationName;

    @Schema(description = "负责安检公司名称")
    private String companyName;
    
    @Schema(description = "管辖单位 ID")
    private String precinctId;

    @Schema(description = "所属辖区名称")
    private String precinct;
    
    @Schema(description = "在岗人员")
    private String onDutyPersonnel;
}

package cn.com.sipsg.module.business.module.supervision.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 地铁换乘站归属线路关系
 */
@Data
@TableName("tbl_base_subway_transfer_affiliation")
public class BaseSubwayTransferAffiliation {
    /** 主键ID */
    @TableId("id")
    private Long id;
    /** 线路ID */
    @TableField("subway_id")
    private String subwayId;
    /** 站点ID */
    @TableField("station_id")
    private Long stationId;
    /** 线路编码 */
    @TableField("subway_code")
    private String subwayCode;
    /** 站点编码 */
    @TableField("station_code")
    private String stationCode;
    /** 线路名称 */
    @TableField("subway_name")
    private String subwayName;
    /** 站点名称 */
    @TableField("station_name")
    private String stationName;
    /** 创建人 */
    @TableField("create_user_id")
    private String createUserId;
    /** 创建时间 */
    @TableField("create_time")
    private LocalDateTime createTime;
    /** 更新人 */
    @TableField("update_user_id")
    private String updateUserId;
    /** 更新时间 */
    @TableField("update_time")
    private LocalDateTime updateTime;
    /** 有效标识,TRUE:有效,FALSE:无效 */
    @TableField("available")
    private Boolean available;
} 
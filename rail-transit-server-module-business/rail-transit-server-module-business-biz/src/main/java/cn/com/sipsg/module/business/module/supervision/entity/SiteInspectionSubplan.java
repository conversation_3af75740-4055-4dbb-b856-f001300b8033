package cn.com.sipsg.module.business.module.supervision.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@TableName("tbl_site_inspection_subplan")
public class SiteInspectionSubplan {
    /** 记录唯一标识主键 */
    @TableId("id")
    private Long id;
    /** 记录创建时间 */
    @TableField("create_time")
    private LocalDateTime createTime;
    /** 记录最后更新时间 */
    @TableField("update_time")
    private LocalDateTime updateTime;
    /** 创建者用户ID */
    @TableField("create_user_id")
    private String createUserId;
    /** 最后更新者用户ID */
    @TableField("update_user_id")
    private String updateUserId;
    /** 是否有效 */
    @TableField("available")
    private Boolean available;
    /** 巡检ID */
    @TableField("inspection_id")
    private Long inspectionId;
    /** 开始时间 */
    @TableField("start_time")
    private LocalDateTime startTime;
    /** 结束时间 */
    @TableField("end_time")
    private LocalDateTime endTime;
    /** 名字 */
    @TableField("name")
    private String name;
    /** 状态 */
    @TableField("status")
    private Integer status;
    /** 进度 */
    @TableField("progress")
    private Double progress;
} 
package cn.com.sipsg.module.business.module.supervision.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 巡逻信息详情查询请求DTO
 */
@Data
@Schema(description = "巡逻信息详情查询请求参数")
public class PatrolDetailReqDTO {
    
    @Schema(description = "巡逻信息ID", required = true, example = "1")
    @NotNull(message = "巡逻信息ID不能为空")
    private String id;
}

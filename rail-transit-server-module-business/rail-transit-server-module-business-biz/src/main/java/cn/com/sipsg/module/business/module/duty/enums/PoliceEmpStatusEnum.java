package cn.com.sipsg.module.business.module.duty.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 警员状态枚举
 * 用于tbl_base_police_emp表的status字段
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Getter
@AllArgsConstructor
public enum PoliceEmpStatusEnum {

    /**
     * 在职
     */
    ACTIVE("0", "在职"),

    /**
     * 离职
     */
    RESIGNED("1", "离职"),

    /**
     * 退休
     */
    RETIRED("2", "退休"),

    /**
     * 未知状态
     */
    UNKNOWN("3", "未知");

    /**
     * 状态编码
     */
    private final String code;

    /**
     * 状态描述
     */
    private final String desc;

    /**
     * 根据编码获取枚举
     *
     * @param code 状态编码
     * @return 对应的枚举值
     */
    public static PoliceEmpStatusEnum getByCode(String code) {
        if (code == null || code.isEmpty()) {
            return UNKNOWN;
        }
        
        for (PoliceEmpStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        
        return UNKNOWN;
    }

    /**
     * 根据描述获取枚举
     *
     * @param desc 状态描述
     * @return 对应的枚举值
     */
    public static PoliceEmpStatusEnum getByDesc(String desc) {
        if (desc == null || desc.isEmpty()) {
            return UNKNOWN;
        }
        
        for (PoliceEmpStatusEnum status : values()) {
            if (status.getDesc().equals(desc)) {
                return status;
            }
        }
        
        return UNKNOWN;
    }

    /**
     * 检查是否为有效状态（排除未知状态）
     *
     * @return 是否为有效状态
     */
    public boolean isValid() {
        return this != UNKNOWN;
    }

    /**
     * 检查是否为在职状态
     *
     * @return 是否为在职状态
     */
    public boolean isActive() {
        return this == ACTIVE;
    }
}
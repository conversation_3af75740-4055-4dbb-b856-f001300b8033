package cn.com.sipsg.module.business.module.duty.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> lixt
 * @date : 2025-04-24 14:29:35
 * @Description: 警局组织信息表
 */
@Data
@TableName("tbl_base_police_org")
public class BasePoliceOrg {
    @TableId(type = IdType.ASSIGN_ID)  // 主键自增
    private String id;  // 主键ID

    @TableField("code")
    private String code;  // 组织编号

    @TableField("name")
    private String name;  // 组织名称

    @TableField("alias")
    private String alias;  // 组织别名

    @TableField("type")
    private Integer type;  // 组织类型

    @TableField("parent_id")
    private String parentId;  // 父组织ID

    @TableField("memos")
    private String memos;  // 备注

    @TableField("create_user_id")
    private String createUserId;  // 创建人

    @TableField("create_time")
    private LocalDateTime createTime;  // 创建时间

    @TableField("update_user_id")
    private String updateUserId;  // 更新人

    @TableField("update_time")
    private LocalDateTime updateTime;  // 更新时间

    @TableField("available")
    private Boolean available;  // 有效标识，TRUE:有效，FALSE:无效

    @TableField("phone")
    private String phone;  // 派出所值台电话

    @TableField("distance_from_branch")
    private Integer distanceFromBranch;  // 派出所距离分局距离,米

    @TableField("wireless_call_number")
    private String wirelessCallNumber;  // 无线呼号

    @TableField("car_depot_num")
    private Integer carDepotNum;  // 管辖车辆段数量

    @TableField("car_depot_name")
    private String carDepotName;  // 管辖车辆段名称

    @TableField("joint_unit")
    private String jointUnit;  // 联勤单位

    @TableField("district_num")
    private Integer districtNum;  // 辖区跨越行政区数量

    @TableField("district_name")
    private String districtName;  // 辖区跨越行政区区名

    @TableField("lng")
    private Double lng;  // 经度

    @TableField("lat")
    private Double lat;  // 纬度

    @TableField("enable")
    private Boolean enable;  // 启用标识

    @TableField("inside")
    private Boolean inside;  // 内部数据标识

    @TableField("announcement")
    private String announcement;  // 分局公告

    @TableField("place_type")
    private Integer placeType;  // 1公交，2地铁

    @TableField("place_type_name")
    private String placeTypeName;  // 公交地铁
}

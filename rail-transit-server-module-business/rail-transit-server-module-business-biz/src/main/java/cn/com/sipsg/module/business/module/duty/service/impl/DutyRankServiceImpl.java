package cn.com.sipsg.module.business.module.duty.service.impl;

import cn.com.sipsg.common.exception.ServerException;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.module.business.module.duty.entity.*;
import cn.com.sipsg.module.business.module.duty.enums.BizTypeEnum;
import cn.com.sipsg.module.business.module.duty.enums.StatTypeEnum;
import cn.com.sipsg.module.business.module.duty.mapper.*;
import cn.com.sipsg.module.business.module.duty.pojo.dto.PatrolListQueryDTO;
import cn.com.sipsg.module.business.module.duty.pojo.dto.PatrolStatQueryDTO;
import cn.com.sipsg.module.business.module.duty.pojo.vo.PatrolRecordVO;
import cn.com.sipsg.module.business.module.duty.pojo.vo.PatrolStatVO;
import cn.com.sipsg.module.business.module.duty.pojo.vo.RankResultVO;
import cn.com.sipsg.module.business.module.duty.service.DutyRankService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> lixt
 * @date : 2025-05-05 10:30:43
 * @Description: 标签相关接口实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DutyRankServiceImpl implements DutyRankService {
    private final PerCheckPersonInfoMapper perCheckPersonInfoMapper;
    private final PckZapcWpxxMapper pckZapcWpxxMapper;
    private final SecurityAlarmMapper securityAlarmMapper;
    private final BasePoliceOrgMapper basePoliceOrgMapper;
    private final BasePoliceEmpMapper basePoliceEmpMapper;
    private final SecurityAlarmHandleMapper securityAlarmHandleMapper;
    private final SituationEventMapper situationEventMapper;
    private final PatrolRecordMapper patrolRecordMapper;

    /**
     * 计算统计周期的起始时间
     * 根据当前时间和统计类型枚举，确定统计周期开始的时间点
     *
     * @param statTypeEnum 统计类型枚举，包括日、周、月、年等
     * @param now          当前时间
     * @return 统计周期的起始时间
     * @throws IllegalArgumentException 如果传入的统计类型不支持，则抛出此异常
     */
    private LocalDateTime getStatStartTime(StatTypeEnum statTypeEnum, LocalDateTime now) {
        switch (statTypeEnum) {
            case DAY:
                // 对于每日统计，起始时间为当日的0点
                return now.toLocalDate().atStartOfDay();
            case WEEK:
                // 对于每周统计，起始时间为当周周一的0点
                return now.minusDays(now.getDayOfWeek().getValue() - 1).toLocalDate().atStartOfDay();
            case MONTH:
                // 对于每月统计，起始时间为当月第一天的0点
                return now.withDayOfMonth(1).toLocalDate().atStartOfDay();
            case YEAR:
                // 对于每年统计，起始时间为当年第一天的0点
                return now.withDayOfYear(1).toLocalDate().atStartOfDay();
            default:
                // 如果传入的统计类型不受支持，则抛出异常
                throw new IllegalArgumentException("不支持的统计类型: " + statTypeEnum);
        }
    }

    /**
     * 获取巡查排名信息
     *
     * @param statType 统计类型代码，用于确定统计周期（如日、周、月、年）
     * @param bizType  业务类型代码，用于区分不同的业务场景（如盘查）
     * @return 返回一个包含各组织巡查排名信息的列表，包括组织代码、名称、人员盘查数量、物品盘查数量、总盘查数量及排名
     * <p>
     * 该方法根据给定的统计类型和业务类型，计算出在指定周期内各组织的巡查排名情况
     * 它首先确定统计周期的起始时间，然后根据业务类型筛选出需要的数据，最后计算各组织的盘查数量并排序
     */
    @Override
    public List<RankResultVO> getPatrolRank(String statType, String bizType) {
        // 根据统计类型代码获取对应的枚举对象
        StatTypeEnum statTypeEnum = StatTypeEnum.fromCode(statType);
        // 根据业务类型代码获取对应的枚举对象
        BizTypeEnum bizTypeEnum = BizTypeEnum.fromCode(bizType);
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime start = getStatStartTime(statTypeEnum, now);
        log.info("[单位排名] 统计类型: {}, 业务类型: {}, 起始时间: {}, 结束时间: {}", statTypeEnum, bizTypeEnum, start, now);

        // 统计周期的结束时间为当前时间
        if (bizTypeEnum == BizTypeEnum.PAN_CHA) {
            // 盘查统计逻辑（已实现）
            QueryWrapper<PerCheckPersonInfo> personWrapper = new QueryWrapper<>();
            // 设置查询条件，时间在[start, now]区间内
            personWrapper.ge("rypcsj", start).le("rypcsj", now);
            // 根据查询条件获取人员信息列表
            List<PerCheckPersonInfo> personList = perCheckPersonInfoMapper.selectList(personWrapper);

            // 获取物品盘查信息列表
            List<PckZapcWpxx> thingList = getThingCheckList(start, now);

            // 统计每个单位的人员数量
            Map<String, Long> personCountMap = personList.stream()
                    .collect(Collectors.groupingBy(PerCheckPersonInfo::getPcdwbh, Collectors.counting()));
            // 统计每个单位的物品数量
            Map<String, Long> thingCountMap = thingList.stream()
                    .collect(Collectors.groupingBy(PckZapcWpxx::getBpcwppcdw, Collectors.counting()));

            // 合并所有单位的编码
            Set<String> allOrgCodes = mergeKeySets(personCountMap, thingCountMap);
            // 存储排名结果的列表
            Map<String, Long> totalCountMap = new HashMap<>();
            Map<String, String> orgNameMap = new HashMap<>();
            for (String orgCode : allOrgCodes) {
                long personCount = personCountMap.getOrDefault(orgCode, 0L);
                long thingCount = thingCountMap.getOrDefault(orgCode, 0L);
                long total = personCount + thingCount;
                String orgName = personList.stream().filter(p -> orgCode.equals(p.getPcdwbh())).map(PerCheckPersonInfo::getPcdwmc).findFirst()
                        .orElseGet(() -> thingList.stream().map(PckZapcWpxx::getPcdwmc).filter(orgCode::equals).findFirst().orElse(""));
                totalCountMap.put(orgCode, total);
                orgNameMap.put(orgCode, orgName);
            }
            List<RankResultVO> result = buildRankResultList(totalCountMap, orgNameMap);
            log.info("[单位排名] 统计结果数量: {}", result.size());
            return result;
        } else if (bizTypeEnum == BizTypeEnum.AN_JIAN) {
            // 安检查获统计逻辑（通过处置人表统计）
            Map<String, Long> handlerCountMap = getSecurityAlarmHandlerCountMap(start, now);
            if (handlerCountMap.isEmpty()) {
                return new ArrayList<>();
            }

            // 3. 查询处置人所属组织
            Set<String> handlerIds = handlerCountMap.keySet();
            List<BasePoliceEmp> handlerList = basePoliceEmpMapper.selectList(
                    new QueryWrapper<BasePoliceEmp>().in("id", handlerIds)
            );
            Map<String, String> handlerOrgMap = buildMap(handlerList, e -> String.valueOf(e.getId()), BasePoliceEmp::getPoliceOrgId);

            // 4. 统计每个组织下的安检处置数量
            Map<String, Long> orgCountMap = new HashMap<>();
            for (Map.Entry<String, Long> entry : handlerCountMap.entrySet()) {
                String handlerId = entry.getKey();
                Long count = entry.getValue();
                String orgId = handlerOrgMap.get(handlerId);
                if (orgId != null) {
                    orgCountMap.put(orgId, orgCountMap.getOrDefault(orgId, 0L) + count);
                }
            }

            // 5. 查询组织名称
            Set<String> orgIds = orgCountMap.keySet();
            List<BasePoliceOrg> orgList = basePoliceOrgMapper.selectList(
                    new QueryWrapper<BasePoliceOrg>().in("id", orgIds)
            );
            Map<String, String> orgNameMap = buildMap(orgList, BasePoliceOrg::getId, BasePoliceOrg::getName);


            // 6. 组装结果
            List<RankResultVO> result = new ArrayList<>();
            for (Map.Entry<String, Long> entry : orgCountMap.entrySet()) {
                String orgId = entry.getKey();
                Long count = entry.getValue();
                String orgName = orgNameMap.getOrDefault(orgId, "");
                RankResultVO vo = new RankResultVO();
                vo.setCode(orgId);
                vo.setName(String.valueOf(orgName));
                vo.setCount(count);
                result.add(vo);
            }
            result.sort((a, b) -> Long.compare(b.getCount(), a.getCount()));
            log.info("[安检查获单位排名-处置人归属] 统计结果数量: {}", result.size());
            return result;
        } else if (bizTypeEnum == BizTypeEnum.JIE_CHU_JING) {
            // 接处警统计逻辑
            // 1. 查询指定时间范围内的警情信息
            List<SituationEvent> eventList = getSituationEvents(start, now);
            if (eventList.isEmpty()) {
                return new ArrayList<>();
            }
            // 2. 按处警单位编号分组统计数量
            Map<String, Long> orgCountMap = eventList.stream()
                    .filter(e -> e.getHandlePoliceOrgCode() != null)
                    .collect(Collectors.groupingBy(SituationEvent::getHandlePoliceOrgCode, Collectors.counting()));
            if (orgCountMap.isEmpty()) {
                return new ArrayList<>();
            }
            // 3. 查询单位名称
            List<BasePoliceOrg> orgList = basePoliceOrgMapper.selectList(
                    new QueryWrapper<BasePoliceOrg>().in("code", orgCountMap.keySet())
            );
            Map<String, String> orgNameMap = buildMap(orgList, BasePoliceOrg::getCode, BasePoliceOrg::getName);
            // 4. 组装结果
            List<RankResultVO> result = buildRankResultList(orgCountMap, orgNameMap);
            log.info("[接处警单位排名] 统计结果数量: {}", result.size());
            return result;
        } else {
            throw new UnsupportedOperationException("暂不支持该业务类型: " + bizType);
        }
    }

    /**
     * 获取个人排名信息
     * <p>
     * 根据指定的统计类型和业务类型，计算个人排名结果
     *
     * @param statType 统计类型代码，用于确定统计周期（如日、周、月统计）
     * @param bizType  业务类型代码，用于区分不同的业务场景（如盘查、安检、接处警）
     * @return 返回一个包含排名结果的列表，每个元素代表一个个人的排名信息，包括组织代码、组织名称和计数
     * @throws UnsupportedOperationException 如果业务类型不支持，则抛出此异常
     */
    @Override
    public List<RankResultVO> getPersonalRank(String statType, String bizType) {
        // 将统计类型代码转换为枚举对象
        StatTypeEnum statTypeEnum = StatTypeEnum.fromCode(statType);
        // 将业务类型代码转换为枚举对象
        BizTypeEnum bizTypeEnum = BizTypeEnum.fromCode(bizType);
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();
        // 根据统计类型和当前时间计算统计起始时间
        LocalDateTime start = getStatStartTime(statTypeEnum, now);
        // 记录日志，包括统计类型、业务类型和时间范围
        log.info("[个人排名] 统计类型: {}, 业务类型: {}, 起始时间: {}, 结束时间: {}", statTypeEnum, bizTypeEnum, start, now);

        // 根据不同的业务类型执行相应的统计逻辑
        if (bizTypeEnum == BizTypeEnum.PAN_CHA) {
            // 盘查个人统计
            // 创建查询条件，筛选在指定时间范围内的人员盘查记录
            QueryWrapper<PerCheckPersonInfo> personWrapper = new QueryWrapper<>();
            personWrapper.ge("rypcsj", start).le("rypcsj", now);
            // 执行查询，获取人员盘查记录列表
            List<PerCheckPersonInfo> personList = perCheckPersonInfoMapper.selectList(personWrapper);
            // 按盘查人编号分组统计人员盘查数量
            Map<String, Long> personCountMap = personList.stream()
                    .collect(Collectors.groupingBy(PerCheckPersonInfo::getPcrbh, Collectors.counting()));

            // 创建查询条件，筛选在指定时间范围内的物品盘查记录
            // 获取物品盘查信息列表
            List<PckZapcWpxx> thingList = getThingCheckList(start, now);
            // 按盘查人编号分组统计物品盘查数量
            Map<String, Long> thingCountMap = thingList.stream()
                    .collect(Collectors.groupingBy(PckZapcWpxx::getBpcwprybh, Collectors.counting()));

            // 合并所有盘查人员的编号
            Set<String> allPersonCodes = mergeKeySets(personCountMap, thingCountMap);

            // 组装结果
            List<RankResultVO> result = new ArrayList<>();
            for (String pcrbh : allPersonCodes) {
                // 获取人员盘查数量，没有则为0
                long personCount = personCountMap.getOrDefault(pcrbh, 0L);
                // 获取物品盘查数量，没有则为0
                long thingCount = thingCountMap.getOrDefault(pcrbh, 0L);
                // 计算总数
                long total = personCount + thingCount;
                // 获取盘查人姓名，优先从人员盘查记录中获取，没有则从物品盘查记录中获取
                String pcrxm = personList.stream().filter(p -> pcrbh.equals(p.getPcrbh())).map(PerCheckPersonInfo::getPcrxm).findFirst()
                        .orElseGet(() -> thingList.stream().filter(t -> pcrbh.equals(t.getBpcwprybh())).map(PckZapcWpxx::getBpcwppcrxm).findFirst().orElse(""));
                // 创建排名结果对象，设置相关信息
                RankResultVO vo = new RankResultVO();
                vo.setCode(pcrbh);
                vo.setName(pcrxm);
                vo.setCount(total);
                result.add(vo);
            }
            // 根据数量降序排序
            result.sort((a, b) -> Long.compare(b.getCount(), a.getCount()));
            // 记录日志，包括统计结果数量
            log.info("[个人排名] 统计结果数量: {}", result.size());
            return result;
        } else if (bizTypeEnum == BizTypeEnum.AN_JIAN) {
            // 安检查获个人统计逻辑
            Map<String, Long> handlerCountMap = getSecurityAlarmHandlerCountMap(start, now);
            if (handlerCountMap.isEmpty()) {
                return new ArrayList<>();
            }

            // 3. 查询处置人详细信息
            Map<String, String> empNameMap = getEmpNameMap(handlerCountMap.keySet());

            // 4. 组装排名结果
            List<RankResultVO> result = new ArrayList<>();
            for (Map.Entry<String, Long> entry : handlerCountMap.entrySet()) {
                RankResultVO vo = new RankResultVO();
                vo.setCode(entry.getKey());
                vo.setName(empNameMap.getOrDefault(entry.getKey(), ""));
                vo.setCount(entry.getValue());
                result.add(vo);
            }
            // 按数量降序排序
            result.sort((a, b) -> Long.compare(b.getCount(), a.getCount()));
            return result;
        } else if (bizTypeEnum == BizTypeEnum.JIE_CHU_JING) {
            // 接处警个人统计逻辑
            // 1. 查询指定时间范围内的警情信息
            List<SituationEvent> eventList = getSituationEvents(start, now);
            if (eventList.isEmpty()) {
                return new ArrayList<>();
            }

            // 2. 按处警人编号分组统计数量
            Map<String, Long> personCountMap = eventList.stream()
                    .filter(e -> e.getHandlePersonCode() != null)
                    .collect(Collectors.groupingBy(SituationEvent::getHandlePersonCode, Collectors.counting()));
            if (personCountMap.isEmpty()) {
                return new ArrayList<>();
            }

            // 3. 查询处警人详细信息
            Map<String, String> empNameMap = getEmpNameMap(personCountMap.keySet());

            // 4. 组装结果
            List<RankResultVO> result = buildRankResultList(personCountMap, empNameMap);
            log.info("[接处警个人排名] 统计结果数量: {}", result.size());
            return result;
        } else {
            // 如果业务类型不支持，则抛出异常
            throw new UnsupportedOperationException("暂不支持该业务类型: " + bizType);
        }
    }

    /**
     * 获取指定时间范围内的物品盘查信息列表
     *
     * @param start 开始时间
     * @param end   结束时间
     * @return 物品盘查信息列表
     */
    private List<PckZapcWpxx> getThingCheckList(LocalDateTime start, LocalDateTime end) {
        QueryWrapper<PckZapcWpxx> thingWrapper = new QueryWrapper<>();
        // 设置查询条件，时间在[start, end]区间内
        thingWrapper.ge("bpcwppcsj", start).le("bpcwppcsj", end);
        // 根据查询条件获取物品信息列表
        return pckZapcWpxxMapper.selectList(thingWrapper);
    }

    /**
     * 合并两个Map的键集合
     * 将两个Map的键合并到一个Set中，用于后续处理
     *
     * @param firstMap  第一个Map
     * @param secondMap 第二个Map
     * @param <K>       键的类型
     * @param <V1>      第一个Map的值类型
     * @param <V2>      第二个Map的值类型
     * @return 合并后的键集合
     */
    private <K, V1, V2> Set<K> mergeKeySets(Map<K, V1> firstMap, Map<K, V2> secondMap) {
        // 创建一个新的HashSet来存储合并后的键
        Set<K> mergedKeys = new HashSet<>();
        // 将第一个Map的键添加到合并后的键集合中
        mergedKeys.addAll(firstMap.keySet());
        // 将第二个Map的键添加到合并后的键集合中
        mergedKeys.addAll(secondMap.keySet());
        // 返回合并后的键集合
        return mergedKeys;
    }

    /**
     * 获取指定时间范围内的安检预警处置人统计信息
     *
     * @param start 开始时间
     * @param end   结束时间
     * @return 处置人编号与处置数量的映射
     */
    private Map<String, Long> getSecurityAlarmHandlerCountMap(LocalDateTime start, LocalDateTime end) {
        // 1. 查询指定时间范围内的安检预警主键
        QueryWrapper<SecurityAlarm> alarmWrapper = new QueryWrapper<>();
        alarmWrapper.ge("alarm_time", start).le("alarm_time", end);
        List<SecurityAlarm> alarmList = securityAlarmMapper.selectList(alarmWrapper);
        if (alarmList.isEmpty()) {
            return Collections.emptyMap();
        }
        // 获取所有安检预警的ID
        Set<String> alarmIds = alarmList.stream().map(SecurityAlarm::getId).collect(Collectors.toSet());

        // 2. 查询处置人信息表，统计处置人编号数量
        List<SecurityAlarmHandle> handleList = securityAlarmHandleMapper.selectList(
                new QueryWrapper<SecurityAlarmHandle>().in("alarm_id", alarmIds)
        );
        if (handleList.isEmpty()) {
            return Collections.emptyMap();
        }

        // 3. 统计每个处置人编号的安检处置数量
        return handleList.stream()
                .filter(h -> h.getHandleEmpCode() != null)
                .collect(Collectors.groupingBy(SecurityAlarmHandle::getHandleEmpCode, Collectors.counting()));
    }

    /**
     * 公共方法：根据统计Map和名称Map组装RankResultVO列表并降序排序
     *
     * @param countMap 统计信息的Map，键为标识符，值为统计数值
     * @param nameMap  名称信息的Map，键为标识符，值为名称字符串
     * @return 返回一个RankResultVO对象的列表，每个对象包含标识符、名称和统计数值
     */
    private <K> List<RankResultVO> buildRankResultList(Map<K, Long> countMap, Map<K, String> nameMap) {
        List<RankResultVO> result = new ArrayList<>();
        // 遍历统计信息的Map
        for (Map.Entry<K, Long> entry : countMap.entrySet()) {
            K code = entry.getKey();
            Long count = entry.getValue();
            // 从名称信息的Map中获取对应的名称，如果不存在则默认为空字符串
            String name = nameMap.getOrDefault(code, "");
            // 创建RankResultVO对象并设置属性
            RankResultVO vo = new RankResultVO();
            vo.setCode(String.valueOf(code));
            vo.setName(name);
            vo.setCount(count);
            // 将RankResultVO对象添加到结果列表中
            result.add(vo);
        }
        // 根据统计数值对结果列表进行降序排序
        result.sort((a, b) -> Long.compare(b.getCount(), a.getCount()));
        return result;
    }

    /**
     * 公共方法：根据实体列表和key、value映射函数组装Map
     * 此方法用于将一个实体对象列表转换为一个Map，其中键和值由提供的函数确定
     * 这种方法特别有用，因为它允许以一种通用和可重用的方式，根据对象列表生成查找表
     *
     * @param <T>         泛型参数，表示列表中实体的类型
     * @param <K>         泛型参数，表示Map中键的类型
     * @param <V>         泛型参数，表示Map中值的类型
     * @param list        实体对象列表，作为生成Map的输入
     * @param keyMapper   函数，用于根据实体对象生成Map中的键
     * @param valueMapper 函数，用于根据实体对象生成Map中的值
     * @return 返回一个Map，其键和值由列表中的实体对象通过keyMapper和valueMapper生成
     */
    private <T, K, V> Map<K, V> buildMap(List<T> list, Function<T, K> keyMapper, Function<T, V> valueMapper) {
        // 使用Java 8的Stream API和Collectors.toMap来组装Map
        // 当键存在冲突时，使用二元操作符(a, b) -> a来保留现有值
        return list.stream().collect(Collectors.toMap(keyMapper, valueMapper, (a, b) -> a));
    }

    /**
     * 公共方法：查询指定时间范围内的警情信息
     * 该方法用于从数据库中查询处理时间在指定时间段内的所有警情事件
     * 主要用于获取某个时间段内发生的所有警情，以便进行进一步的处理或分析
     *
     * @param start 开始时间，表示查询范围的起始时间
     * @param end   结束时间，表示查询范围的终止时间
     * @return 返回一个包含指定时间段内所有警情事件的列表
     */
    private List<SituationEvent> getSituationEvents(LocalDateTime start, LocalDateTime end) {
        // 创建查询条件包装器
        QueryWrapper<SituationEvent> wrapper = new QueryWrapper<>();
        // 设置查询条件：处理时间大于等于开始时间且小于等于结束时间
        wrapper.ge("handle_time", start).le("handle_time", end);
        // 执行查询并返回结果
        return situationEventMapper.selectList(wrapper);
    }

    /**
     * 公共方法：根据人员编号集合查询人员详细信息并组装名称Map
     * 此方法的目的是通过人员编号集合来获取对应的人员名称，并以Map的形式返回，便于快速查找和使用
     *
     * @param empCodes 人员编号集合，用于查询人员详细信息
     * @return 返回一个Map，键为人员编号，值为人员名称如果输入为空集合或null，则返回空的Map
     */
    private Map<String, String> getEmpNameMap(Set<String> empCodes) {
        // 检查输入的人员编号集合是否为空或null，如果是，则返回一个空的Map
        if (empCodes == null || empCodes.isEmpty()) {
            return Collections.emptyMap();
        }
        // 使用MyBatis-Plus的查询工具，根据人员编号集合查询人员列表
        List<BasePoliceEmp> empList = basePoliceEmpMapper.selectList(
                new QueryWrapper<BasePoliceEmp>().in("code", empCodes)
        );
        // 将查询到的人员列表转换为Map，其中键为人员编号，值为人员名称
        return buildMap(empList, BasePoliceEmp::getCode, BasePoliceEmp::getName);
    }

    /**
     * 分页查询盘查列表
     *
     * @param queryDTO 查询条件，包含盘查编号、民警名称、盘查时间、盘查单位等
     * @return 分页结果
     */
    @Override
    public CommonPageVO<PatrolRecordVO> getPatrolList(PatrolListQueryDTO queryDTO) {
        log.info("[盘查列表] 查询条件: {}", queryDTO);
        CommonPageVO<PatrolRecordVO> pageVO = new CommonPageVO<>();

        // 检查分页参数
        if (queryDTO.getPageNum() <= 0) {
            queryDTO.setPageNum(1);
        }
        if (queryDTO.getPageSize() <= 0) {
            queryDTO.setPageSize(10);
        }

        try {
            // 使用MyBatis-Plus的Page对象进行分页查询
            Page<PatrolRecordVO> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());

            // 调用优化后的getPatrolRecordList方法
            Page<PatrolRecordVO> result = patrolRecordMapper.getPatrolRecordList(page, queryDTO);

            // 设置分页结果
            pageVO.setCurrent(result.getCurrent());
            pageVO.setSize(result.getSize());
            pageVO.setTotal(result.getTotal());
            pageVO.setRecords(result.getRecords());

            log.info("[盘查列表] 查询成功，总数: {}, 当前页: {}, 每页数量: {}",
                    result.getTotal(), result.getCurrent(), result.getSize());
        } catch (Exception e) {
            log.error("[盘查列表] 查询异常", e);
            throw new ServerException("查询盘查列表失败", e);
        }

        return pageVO;
    }

    /**
     * 统计时间范围内的盘查数据
     *
     * @param queryDTO 查询条件，包含开始时间、结束时间
     * @return 统计结果
     */
    @Override
    public PatrolStatVO getPatrolStats(PatrolStatQueryDTO queryDTO) {
        log.info("[盘查统计] 查询条件: {}", queryDTO);

        try {
            // 创建返回结果对象
            PatrolStatVO statVO = new PatrolStatVO();
            List<PatrolStatVO.OrgPatrolStatItem> items = new ArrayList<>();

            // 查询人员盘查数据，按单位分组
            QueryWrapper<PerCheckPersonInfo> personWrapper = new QueryWrapper<>();
            if (queryDTO.getStartTime() != null) {
                personWrapper.ge("rypcsj", queryDTO.getStartTime());
            }
            if (queryDTO.getEndTime() != null) {
                personWrapper.le("rypcsj", queryDTO.getEndTime());
            }

            // 查询人员盘查数据并按单位分组
            List<PerCheckPersonInfo> personList = perCheckPersonInfoMapper.selectList(personWrapper);

            // 按单位分组统计人员盘查数据
            Map<String, Long> personCountByOrg = personList.stream()
                    .filter(p -> p.getPcdwbh() != null)
                    .collect(Collectors.groupingBy(PerCheckPersonInfo::getPcdwbh, Collectors.counting()));

            // 按单位分组统计不同盘查人数量
            Map<String, Set<String>> policeByOrg = new HashMap<>();
            for (PerCheckPersonInfo person : personList) {
                if (person.getPcdwbh() != null && person.getPcrbh() != null) {
                    policeByOrg.computeIfAbsent(person.getPcdwbh(), k -> new HashSet<>()).add(person.getPcrbh());
                }
            }

            // 查询物品盘查数据
            QueryWrapper<PckZapcWpxx> itemWrapper = new QueryWrapper<>();
            if (queryDTO.getStartTime() != null) {
                itemWrapper.ge("bpcwppcsj", queryDTO.getStartTime());
            }
            if (queryDTO.getEndTime() != null) {
                itemWrapper.le("bpcwppcsj", queryDTO.getEndTime());
            }
            List<PckZapcWpxx> itemList = pckZapcWpxxMapper.selectList(itemWrapper);

            // 按单位分组统计物品盘查数据
            Map<String, Long> itemCountByOrg = itemList.stream()
                    .filter(i -> i.getBpcwppcdw() != null)
                    .collect(Collectors.groupingBy(PckZapcWpxx::getBpcwppcdw, Collectors.counting()));

            // 按单位分组统计不同盘查人数量
            for (PckZapcWpxx item : itemList) {
                if (item.getBpcwppcdw() != null && item.getBpcwprybh() != null) {
                    policeByOrg.computeIfAbsent(item.getBpcwppcdw(), k -> new HashSet<>()).add(item.getBpcwprybh());
                }
            }

            // 合并所有单位编号
            Set<String> allOrgCodes = new HashSet<>();
            allOrgCodes.addAll(personCountByOrg.keySet());
            allOrgCodes.addAll(itemCountByOrg.keySet());

            // 查询单位名称
            Map<String, String> orgNameMap = new HashMap<>();
            for (PerCheckPersonInfo person : personList) {
                if (person.getPcdwbh() != null && person.getPcdwmc() != null) {
                    orgNameMap.put(person.getPcdwbh(), person.getPcdwmc());
                }
            }
            for (PckZapcWpxx item : itemList) {
                if (item.getBpcwppcdw() != null && item.getPcdwmc() != null) {
                    orgNameMap.put(item.getBpcwppcdw(), item.getPcdwmc());
                }
            }

            // 汇总总计数据
            long totalCount = 0;
            long totalPoliceCount = 0;

            // 生成各单位的统计数据
            for (String orgCode : allOrgCodes) {
                PatrolStatVO.OrgPatrolStatItem item = new PatrolStatVO.OrgPatrolStatItem();
                item.setOrgCode(orgCode);
                item.setOrgName(orgNameMap.getOrDefault(orgCode, orgCode)); // 如果没有名称，就用编号

                // 计算单位盘查总数
                long personOrgCount = personCountByOrg.getOrDefault(orgCode, 0L);
                long itemOrgCount = itemCountByOrg.getOrDefault(orgCode, 0L);
                long orgTotalCount = personOrgCount + itemOrgCount;
                item.setTotalCount(orgTotalCount);

                // 计算单位盘查人数
                int orgPoliceCount = policeByOrg.getOrDefault(orgCode, Collections.emptySet()).size();
                item.setPoliceCount((long) orgPoliceCount);

                // 计算单位人均盘查量
                double orgAvgCount = orgPoliceCount > 0 ? (double) orgTotalCount / orgPoliceCount : 0.0;
                item.setAvgCount(orgAvgCount);

                // 添加到结果列表
                items.add(item);

                // 累加总计
                totalCount += orgTotalCount;
                totalPoliceCount += orgPoliceCount;
            }

            // 按盘查总数降序排序
            items.sort((a, b) -> Long.compare(b.getTotalCount(), a.getTotalCount()));

            // 计算总体人均盘查量
            double avgCount = totalPoliceCount > 0 ? (double) totalCount / totalPoliceCount : 0.0;

            // 设置总计数据
            statVO.setItems(items);
            statVO.setTotalCount(totalCount);
            statVO.setPoliceCount(totalPoliceCount);
            statVO.setAvgCount(avgCount);

            log.info("[盘查统计] 统计结果 - 总数: {}, 警员数: {}, 人均: {}, 单位数: {}",
                    totalCount, totalPoliceCount, avgCount, items.size());

            return statVO;
        } catch (Exception e) {
            log.error("[盘查统计] 统计异常", e);
            throw new ServerException("统计盘查数据失败", e);
        }
    }
}
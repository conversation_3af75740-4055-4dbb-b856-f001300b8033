package cn.com.sipsg.module.business.module.supervision.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> lixt
 * @date : 2025-05-26 16:38:32
 * @Description: 地铁安检信息
 */
@Data
@TableName("tbl_security_check_info")
public class SecurityCheckInfo {

    @TableId(type = IdType.AUTO)
    private Long id;  // 主键ID

    @TableField("create_user_id")
    private String createUserId;  // 创建人

    @TableField("create_time")
    private LocalDateTime createTime;  // 创建时间

    @TableField("update_user_id")
    private String updateUserId;  // 更新人

    @TableField("update_time")
    private LocalDateTime updateTime;  // 更新时间

    @TableField("available")
    private Boolean available;  // 有效标识, TRUE:有效, FALSE:无效

    @TableField("station_id")
    private String stationId;  // 所属站点ID

    @TableField("subway_id")
    private String subwayId;  // 所属线路ID

    @TableField("device_id")
    private Long deviceId;  // 所属设备ID

    @TableField("check_img")
    private String checkImg;  // 安检图片

    @TableField("station_entrance_id")
    private Long stationEntranceId;  // 出入口ID

    @TableField("relation_id")
    private String relationId;  // 数据源ID

    @TableField("alarm_type")
    private Integer alarmType;  // 违带品报警标识

    @TableField("cbinfo")
    private String cbinfo;  // 违禁品信息

    @TableField("type")
    private String type;  // 安检图片类型
}
package cn.com.sipsg.module.business.module.supervision.entity;

import cn.com.sipsg.module.business.module.supervision.enums.AuditStatusEnum;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 服务群众上报表
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tbl_civil_service_report")
public class CivilServiceReport {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 标题名称
     */
    @TableField("title")
    private String title;

    /**
     * 上报人警员编号
     */
    @TableField("reporter_id")
    private String reporterId;

    /**
     * 上报人名称
     */
    @TableField("reporter_name")
    private String reporterName;

    /**
     * 上报时间
     */
    @TableField("report_time")
    private LocalDateTime reportTime;

    /**
     * 内容
     */
    @TableField("content")
    private String content;

    /**
    /**
     * 处理状态：待处理、已处理
     */
    @TableField("status")
    private AuditStatusEnum status;

    /**
     * 是否满意：true-满意，false-不满意，null-未评价
     */
    @TableField("is_satisfied")
    private Boolean isSatisfied;

    /**
     * 上报单位编号
     */
    @TableField("report_org_id")
    private String reportOrgId;

    /**
     * 上报单位名称
     */
    @TableField("report_org_name")
    private String reportOrgName;
    
    /**
     * 站点编号
     */
    @TableField("station_code")
    private String stationCode;

    /**
     * 站点名称
     */
    @TableField("station_name")
    private String stationName;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @TableField(value = "create_user_id", fill = FieldFill.INSERT)
    private String createUserId;

    /**
     * 更新人
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;

    /**
     * 是否有效
     */
    @TableField("available")
    private Boolean available;
}
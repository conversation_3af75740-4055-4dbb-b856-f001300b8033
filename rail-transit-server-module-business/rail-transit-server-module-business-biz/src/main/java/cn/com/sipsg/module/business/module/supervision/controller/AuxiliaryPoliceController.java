package cn.com.sipsg.module.business.module.supervision.controller;

import cn.com.sipsg.common.pojo.CommonResult;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.web.core.controller.BaseController;
import cn.com.sipsg.module.business.module.duty.pojo.vo.PoliceListVO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.DateQueryDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.OrgPolicePageDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.PoliceFuzzySearchDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.OnDutyCountVO;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.StationPoliceCountListVO;
import cn.com.sipsg.module.business.module.supervision.service.PoliceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 辅警档案控制器
 * 提供辅警档案的增删改查功能
 *
 * <AUTHOR>
 * @since 2025-05-23
 */
@Tag(name = "辅警档案", description = "辅警档案相关接口")
@RestController
@RequestMapping("/auxiliaryPolice")
@RequiredArgsConstructor
@Slf4j
public class AuxiliaryPoliceController extends BaseController {

    private final PoliceService policeService;
    
    /**
     * （一警一档）辅警分页列表/模糊查询
     * 查询当前用户所在组织下所有辅警信息列表（可带模糊条件）
     *
     * @param searchDTO 分页+模糊参数DTO
     * @return 辅警分页列表
     */
    @Operation(summary = "辅警分页/模糊分页列表", description = "查询当前用户所在组织下所有辅警信息列表（可带模糊条件）")
    @PostMapping("/profile/page")
    public CommonResult<CommonPageVO<PoliceListVO>> getOrgAuxiliaryPoliceList(@RequestBody PoliceFuzzySearchDTO searchDTO) {
        // 如果没有任何查询条件，则走普通分页，否则走模糊分页
        if ((searchDTO.getKeyword() == null || searchDTO.getKeyword().trim().isEmpty())
                && (searchDTO.getDuty() == null || searchDTO.getDuty().trim().isEmpty())) {
            // 只分页
            OrgPolicePageDTO pageDTO = new OrgPolicePageDTO();
            pageDTO.setPageNum(searchDTO.getPageNum());
            pageDTO.setPageSize(searchDTO.getPageSize());
            return handle(() -> CommonResult.data(policeService.getOrgAuxiliaryPoliceList(pageDTO)));
        } else {
            // 模糊分页
            return handle(() -> CommonResult.data(policeService.fuzzySearchOrgAuxiliaryPoliceList(searchDTO)));
        }
    }
    
    /**
     * 查询当前用户所在组织的值班辅警数量
     * 统计当前用户所在组织指定日期在岗的辅警数量
     *
     * @param queryDTO 查询参数，包含查询日期
     * @return 当前组织值班辅警数量
     */
    @Operation(summary = "查询当前组织值班辅警数量", description = "统计当前用户所在组织指定日期在岗的辅警数量")
    @PostMapping("/on-duty/count")
    public CommonResult<OnDutyCountVO> getOnDutyAuxiliaryPoliceCount(@RequestBody @Valid DateQueryDTO queryDTO) {
        return handle(() -> CommonResult.data(policeService.getOnDutyAuxiliaryPoliceCount(queryDTO)));
    }
    
    /**
     * 查询各站点值班辅警数量
     * 按站点统计当前用户所在组织下每个站点当天在岗的辅警数量
     *
     * @return 各站点值班辅警数量列表
     */
    @Operation(summary = "查询各站点值班辅警数量", description = "按站点统计当前用户所在组织下每个站点当天在岗的辅警数量")
    @PostMapping("/station/count")
    public CommonResult<StationPoliceCountListVO> getStationAuxiliaryPoliceCount(@RequestBody DateQueryDTO queryDTO) {
        return handle(() -> CommonResult.data(policeService.getStationAuxiliaryPoliceCount(queryDTO)));
    }
}
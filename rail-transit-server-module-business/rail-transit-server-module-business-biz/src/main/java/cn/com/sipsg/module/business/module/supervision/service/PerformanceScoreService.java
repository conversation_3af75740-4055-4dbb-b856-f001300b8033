package cn.com.sipsg.module.business.module.supervision.service;

import java.util.List;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.DepartmentScoreRankVO;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.PerformanceAssessmentSummaryVO;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.CriminalRecordTypeVO;

public interface PerformanceScoreService {
    /**
     * 查询当前用户所在单位的本月总分
     * @return 总分
     */
    Double getCurrentOrgMonthTotalScore();

    /**
     * 查询当前用户所在单位的部门绩效排名
     * @return 部门排名列表
     */
    List<DepartmentScoreRankVO> getCurrentOrgDepartmentScoreRank();

    /**
     * 查询所有单位的绩效分数排名
     * @return 单位排名列表
     */
    List<DepartmentScoreRankVO> getOrgScoreRank();

    /**
     * 业务考核统计汇总
     * @return PerformanceAssessmentSummaryVO
     */
    PerformanceAssessmentSummaryVO getCurrentOrgAssessmentSummary();

    /**
     * 获取所有前科类型
     * @return 前科类型VO列表
     */
    List<CriminalRecordTypeVO> getAllCriminalRecordTypes();
} 
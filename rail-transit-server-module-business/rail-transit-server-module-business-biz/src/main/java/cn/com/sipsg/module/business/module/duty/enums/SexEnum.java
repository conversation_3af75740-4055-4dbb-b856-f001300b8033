package cn.com.sipsg.module.business.module.duty.enums;

import lombok.Getter;

@Getter
public enum SexEnum {
    MAN("1", "男"),
    WOMAN("2", "女");

    private final String code;
    private final String label;

    SexEnum(String code, String label) {
        this.code = code;
        this.label = label;
    }

    /**
     * 将性别字符串解析为对应的整数代码。
     * 如果输入的性别字符串与枚举中的标签或代码匹配，则返回相应的代码。
     * 否则尝试将字符串解析为整数。
     * 如果解析失败或输入为空，则返回null。
     *
     * @param sex 性别字符串
     * @return 性别对应的整数代码，或null如果无法解析
     */
    public static String parse(String sex) {
        if (sex == null) return null;
        for (SexEnum e : values()) {
            if (e.label.equals(sex) || e.code.equals(sex)) {
                return e.code;
            }
        }
        return null;
    }
} 
package cn.com.sipsg.module.business.module.supervision.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import cn.com.sipsg.module.business.module.supervision.entity.BaseSubwayStation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface BaseSubwayStationMapper extends BaseMapper<BaseSubwayStation> {
    @Select("SELECT name FROM tbl_base_station WHERE station_code = #{stationId} AND available = true")
    String selectStationNameByStationId(@Param("stationId") String stationId);
} 
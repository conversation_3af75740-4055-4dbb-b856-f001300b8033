package cn.com.sipsg.module.business.module.duty.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> lixt
 * @date : 2025-05-16 10:26:24
 * @Description: 人员谈话记录表
 */
@Data
@TableName("tbl_police_emp_talking_record")
public class PoliceEmpTalkingRecord {

    @TableId(type = IdType.ASSIGN_ID)
    private String id;  // 主键ID

    @TableField("create_user_id")
    private String createUserId;  // 创建人

    @TableField("create_time")
    private LocalDateTime createTime;  // 创建时间

    @TableField("update_user_id")
    private String updateUserId;  // 更新人

    @TableField("update_time")
    private LocalDateTime updateTime;  // 更新时间

    @TableField("available")
    private Boolean available;  // 有效标识,TRUE:有效,FALSE:无效

    @TableField("emp_id")
    private String empId;  // 被谈话人ID

    @TableField("police_org_id")
    private String policeOrgId;  // 被谈话人单位ID

    @TableField("talking_category")
    private String talkingCategory;  // 谈话类别

    @TableField("talking_type")
    private String talkingType;  // 谈话类型

    @TableField("talking_address")
    private String talkingAddress;  // 谈话地址

    @TableField("talking_time")
    private LocalDateTime talkingTime;  // 谈话时间

    @TableField("talking_content")
    private String talkingContent;  // 谈话内容

    @TableField("talking_result")
    private String talkingResult;  // 谈话结果

    @TableField("main_suggestion")
    private String mainSuggestion;  // 主要建议

    @TableField("rectification_suggestion")
    private String rectificationSuggestion;  // 整改建议
}

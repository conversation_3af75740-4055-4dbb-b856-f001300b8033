package cn.com.sipsg.module.business.module.supervision.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum HasChangeEnum {
    NO(0, "未反馈"),
    YES(1, "已反馈");

    private final int code;
    private final String desc;

    public static HasChangeEnum fromCode(Integer code) {
        if (code == null) return null;
        for (HasChangeEnum value : values()) {
            if (value.code == code) return value;
        }
        return null;
    }
} 
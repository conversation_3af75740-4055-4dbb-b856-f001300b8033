package cn.com.sipsg.module.business.module.supervision.service;

import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.WorkRemindQueryDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.WorkRemindSaveDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.WorkRemindVO;

/**
 * 工作提醒Service接口
 */
public interface WorkRemindService {

    /**
     * 保存或更新工作提醒
     * 如果DTO中包含ID则为修改操作，否则为新增操作
     *
     * @param dto 工作提醒保存/更新DTO
     * @return 操作是否成功
     */
    Boolean saveOrUpdateWorkRemind(WorkRemindSaveDTO dto);

    /**
     * 获取工作提醒详情
     *
     * @param id 工作提醒ID
     * @return 工作提醒详情DTO
     */
    WorkRemindSaveDTO getWorkRemindDetail(String id);

    /**
     * 删除工作提醒
     *
     * @param id 工作提醒ID
     * @return 操作是否成功
     */
    Boolean deleteWorkRemind(String id);

    /**
     * 分页查询工作提醒列表
     *
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    CommonPageVO<WorkRemindVO> pageWorkRemind(WorkRemindQueryDTO queryDTO);
}
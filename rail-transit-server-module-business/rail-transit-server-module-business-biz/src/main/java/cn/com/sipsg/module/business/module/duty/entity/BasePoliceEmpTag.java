package cn.com.sipsg.module.business.module.duty.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> lixt
 * @date : 2025-04-29 16:14:11
 * @Description: 警员标签关系表
 */
@Data
@TableName("tbl_base_police_emp_tag")
public class BasePoliceEmpTag {

    @TableId(type = IdType.ASSIGN_ID)
    private String id;  // 主键ID

    @TableField("emp_id")
    private String empId;  // 警员id

    @TableField("tag_key")
    private String tagKey;  // 标签

    @TableField("tag_value")
    private String tagValue;  // 标签描述

    @TableField("create_user_id")
    private String createUserId;  // 创建者ID

    @TableField("create_time")
    private LocalDateTime createTime;  // 创建时间

    @TableField("update_user_id")
    private String updateUserId;  // 更新者ID

    @TableField("update_time")
    private LocalDateTime updateTime;  // 更新时间

    @TableField("available")
    private Boolean available;  // 是否有效
}
package cn.com.sipsg.module.business.module.supervision.pojo.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

@Data
@Schema(description = "日期查询DTO")
public class DateQueryDTO {

    @Schema(description = "查询日期，格式：yyyy-MM-dd", example = "2023-01-01")
    @NotNull(message = "查询日期不能为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate queryDate;
}
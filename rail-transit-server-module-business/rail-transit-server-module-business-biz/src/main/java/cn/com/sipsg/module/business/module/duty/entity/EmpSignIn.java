package cn.com.sipsg.module.business.module.duty.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> lixt
 * @date : 2025-04-24 15:16:19
 * @Description: 民警签到表
 */
@Data
@TableName("tbl_sign_in")
public class EmpSignIn {
    @TableId(type = IdType.ASSIGN_ID)  // 主键自增
    private String id;  // 记录唯一标识主键

    @TableField("create_time")
    private LocalDateTime createTime;  // 记录创建时间

    @TableField("update_time")
    private LocalDateTime updateTime;  // 记录最后更新时间

    @TableField("create_user_id")
    private String createUserId;  // 创建者用户ID

    @TableField("update_user_id")
    private String updateUserId;  // 最后更新者用户ID

    @TableField("available")
    private Boolean available;  // 是否有效

    @TableField("police_emp_id")
    private String policeEmpId;  // 警员主键

    @TableField("sign_time")
    private LocalDateTime signTime;  // 签到时间

    @TableField("location_relation_id")
    private String locationRelationId;  // 地点id tbl_sign_in_location_info.relation_id

    @TableField("location_name")
    private String locationName;  // 签到地点名称

    @TableField("sign_type")
    private Integer signType;  // 类型 1：上班 2：下班 3：签到

    @TableField("location_type")
    private Integer locationType;  // 定位类型 1：自动定位 2：手动定位

    @TableField("img_url")
    private String imgUrl;  // 上下班/签到图片

    @TableField("remark")
    private String remark;  // 备注

    @TableField("location_info_type")
    private Integer locationInfoType;  // 地点类型 1:站点 2:单位

    @TableField("location_code")
    private String locationCode;  // 地点编码
}

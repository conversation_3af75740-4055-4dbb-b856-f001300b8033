package cn.com.sipsg.module.business.module.supervision.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Min;
import javax.validation.constraints.Max;

/**
 * 警员模糊查询DTO
 * 包含分页参数和模糊查询条件，用于查询当前用户所在组织下的警员信息
 *
 * <AUTHOR>
 * @since 2025-05-23
 */
@Data
@Schema(description = "警员模糊查询DTO")
public class PoliceFuzzySearchDTO {
    
    @Schema(description = "姓名/警号/身份证号", example = "张三")
    private String keyword;
     
    @Schema(description = "类型", example = "1")
    private Integer type;
    
    @Schema(description = "职务", example = "巡逐员")
    private String duty;
       
    @Schema(description = "页码", example = "1")
    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;
    
    @Schema(description = "每页数量", example = "10")
    @NotNull(message = "每页数量不能为空")
    @Min(value = 1, message = "每页数量必须大于0")
    @Max(value = 100, message = "每页数量不能超过100")
    private Integer pageSize = 10;
}
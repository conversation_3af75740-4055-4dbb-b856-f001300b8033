package cn.com.sipsg.module.business.module.duty.pojo.vo;

import cn.com.sipsg.common.util.BeanUtils;
import cn.com.sipsg.module.business.module.duty.entity.BasePoliceEmp;
import cn.com.sipsg.module.business.module.duty.entity.BasePoliceOrg;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> lixt
 * @date : 2025-05-15
 * @Description: 警员档案详情VO
 */
@Data
@Schema(description = "警员档案详情VO")
public class PoliceProfileVO {
    @Schema(description = "ID")
    private String id;

    @Schema(description = "警号")
    private String code;

    @Schema(description = "姓名")
    private String name;

    @Schema(description = "身份证号")
    private String idcard;

    @Schema(description = "性别，1男，2女")
    private Integer sex;

    @Schema(description = "出生日期")
    private LocalDateTime birthday;

    @Schema(description = "手机号码")
    private String phone;

    @Schema(description = "人员类型，1民警，2辅警，3安保，4警犬")
    private Integer type;

    @Schema(description = "岗位")
    private Integer position;

    @Schema(description = "所属组织ID")
    private String policeOrgId;

    @Schema(description = "所属组织名称")
    private String policeOrgName;

    @Schema(description = "图片链接")
    private String imgUrl;

    @Schema(description = "行政职务")
    private Integer duties;

    @Schema(description = "行政职务名称")
    private String dutiesName;

    @Schema(description = "警衔")
    private Integer policeRank;

    @Schema(description = "警衔名称")
    private String policeRankName;

    @Schema(description = "民族")
    private String nationality;

    @Schema(description = "职级")
    private Integer rankLevel;

    @Schema(description = "职级名称")
    private String rankLevelName;

    @Schema(description = "人员状态（在职、离职、退休）")
    private String status;

    @Schema(description = "辅警编号")
    private String assistPoliceCode;

    @Schema(description = "岗位类别")
    private String jobType;

    @Schema(description = "工作岗位")
    private String assistPoliceJob;

    @Schema(description = "辅警胸牌号")
    private String assistPrethoracicNo;

    @Schema(description = "警用手机")
    private String policePhone;

    @Schema(description = "紧急联系人姓名")
    private String emergencyContactName;

    @Schema(description = "紧急联系人电话")
    private String emergencyContactPhone;

    @Schema(description = "住址——省份")
    private String addressProvince;

    @Schema(description = "住址——城市")
    private String addressCity;

    @Schema(description = "住址——地区")
    private String addressArea;

    @Schema(description = "住址——街道")
    private String addressStreet;

    @Schema(description = "住址——详情")
    private String addressDetail;

    @Schema(description = "标签列表")
    private List<TagVO> tags;

    @Schema(description = "管辖站点ID列表")
    private List<String> stationIds;
}

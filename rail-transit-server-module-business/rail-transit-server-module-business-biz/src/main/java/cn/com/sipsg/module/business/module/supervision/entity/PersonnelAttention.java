package cn.com.sipsg.module.business.module.supervision.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 重点人员处置记录表
 */
@Data
@TableName("tbl_personnel_attention")
@Schema(description = "重点人员处置记录表")
public class PersonnelAttention {
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键ID")
    private String id;

    @TableField("dispose_time")
    @Schema(description = "处置时间")
    private LocalDateTime disposeTime;

    @TableField("attention_type")
    @Schema(description = "关注方式 1(见面核查)、2(监控关注)、3(便衣贴靠)、4(出警跟防)、99(其他)")
    private Integer attentionType;

    @TableField("warning_sources")
    @Schema(description = "预警来源 1(101预警)、2(票卡预警)、3(盘查)、4(其他)")
    private Integer warningSources;

    @TableField("personnel_type")
    @Schema(description = "人员类型 1(在苏新疆人)、2(涉恐人员)、3(精神病肇事肇祸)、4(个人扬言极端)、99(其他)")
    private Integer personnelType;

    @TableField("police_org_id")
    @Schema(description = "处置单位ID")
    private String policeOrgId;

    @TableField("police_org_name")
    @Schema(description = "处置单位名称")
    private String policeOrgName;

    @TableField("attention_num")
    @Schema(description = "关注对象人数")
    private Integer attentionNum;

    @TableField("in_station_code")
    @Schema(description = "进站站点编号")
    private String inStationCode;

    @TableField("in_station_name")
    @Schema(description = "进站站点名称")
    private String inStationName;

    @TableField("out_station_code")
    @Schema(description = "出站站点编号")
    private String outStationCode;

    @TableField("out_station_name")
    @Schema(description = "出站站点名称")
    private String outStationName;

    @TableField("handover_police_org_id")
    @Schema(description = "交接单位ID")
    private String handoverPoliceOrgId;

    @TableField("handover_police_org_name")
    @Schema(description = "交接单位名称")
    private String handoverPoliceOrgName;

    @TableField("handover_station_code")
    @Schema(description = "交接站点编号")
    private String handoverStationCode;

    @TableField("handover_station_name")
    @Schema(description = "交接站点名称")
    private String handoverStationName;

    @TableField("handover_time")
    @Schema(description = "交接时间")
    private LocalDateTime handoverTime;

    @TableField("name")
    @Schema(description = "姓名")
    private String name;

    @TableField("id_no")
    @Schema(description = "身份证")
    private String idNo;

    @TableField("phone")
    @Schema(description = "手机号")
    private String phone;

    @TableField("image_resource")
    @Schema(description = "现场图片")
    private String imageResource;

    @TableField("remark")
    @Schema(description = "备注")
    private String remark;

    @TableField("available")
    @Schema(description = "有效标识,TRUE:有效,FALSE:无效")
    private Boolean available;

    @TableField("create_user_id")
    @Schema(description = "创建人")
    private String createUserId;

    @TableField("create_time")
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @TableField("update_user_id")
    @Schema(description = "更新人")
    private String updateUserId;

    @TableField("update_time")
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @TableField("in_subway_id")
    @Schema(description = "进站线路ID")
    private String inSubwayId;

    @TableField("out_subway_id")
    @Schema(description = "出战线路ID")
    private String outSubwayId;

    @TableField("handover_subway_id")
    @Schema(description = "交接线路ID")
    private String handoverSubwayId;
}
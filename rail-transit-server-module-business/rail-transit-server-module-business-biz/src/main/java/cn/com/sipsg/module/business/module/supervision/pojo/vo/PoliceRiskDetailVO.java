package cn.com.sipsg.module.business.module.supervision.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@Schema(description = "警务风险详情VO")
public class PoliceRiskDetailVO {
    private String id;

    @Schema(description = "检查场所编号")
    private String stationCode;

    @Schema(description = "检查场所")
    private String stationName;

    @Schema(description = "上报人编号")
    private String createUserId;

    @Schema(description = "上报人")
    private String createUserName;

    @Schema(description = "隐患类型，RiskCategoryEnum的code")
    private String type;

    @Schema(description = "隐患类型名称，RiskCategoryEnum的label")
    private String typeName;

    @Schema(description = "上报时间")
    private LocalDateTime createTime;

    @Schema(description = "整改期限")
    private LocalDateTime fixLastTime;

    @Schema(description = "整改结果")
    private String changeResult;

    @Schema(description = "现场图片url（json字符串，前端直接传json数组字符串）")
    private String riskUrl;

    @Schema(description = "整改后图片url（json字符串，前端直接传json数组字符串）")
    private String changeUrl;

    @Schema(description = "是否紧急上报（0否，1是）")
    private Integer hasUrgency;

    @Schema(description = "检查类目，CheckCategoryEnum的code")
    private String category;

    @Schema(description = "检查类目名称，CheckCategoryEnum的label")
    private String categoryName;

    @Schema(description = "隐患描述")
    private String description;

    @Schema(description = "隐患内容")
    private String content;

    @Schema(description = "整改开始时间")
    private LocalDateTime rectifyStartTime;

    @Schema(description = "整改结束时间")
    private LocalDateTime rectifyEndTime;

    @Schema(description = "现场整改，0-否 1-是")
    private Integer hasChange;
    // 可根据实际需要继续补充字段
} 
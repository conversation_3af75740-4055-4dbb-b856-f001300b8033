package cn.com.sipsg.module.business.module.duty.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class PoliceOrgListQueryDTO {
    @Schema(description = "页码", example = "1")
    private Integer pageNum;
    @Schema(description = "每页数量", example = "10")
    private Integer pageSize;
    @Schema(description = "关键字", example = "地铁公安分局")
    private String keyword;
    @Schema(description = "部门名称（优先模糊查询）", example = "分局")
    private String deptName;
} 
package cn.com.sipsg.module.business.module.duty.mapper;

import cn.com.sipsg.module.business.module.duty.entity.BasePoliceEmpStation;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> lixt
 * @date : 2025-01-15
 * @Description: 警员管辖站点关联表Mapper
 */
@Mapper
public interface BasePoliceEmpStationMapper extends BaseMapper<BasePoliceEmpStation> {
    
    /**
     * 根据警员ID查询管辖站点ID列表
     *
     * @param empId 警员ID
     * @return 站点ID列表
     */
    List<String> selectStationIdsByEmpId(@Param("empId") String empId);
    
    /**
     * 根据站点ID查询管辖警员ID列表
     *
     * @param stationId 站点ID
     * @return 警员ID列表
     */
    List<String> selectEmpIdsByStationId(@Param("stationId") String stationId);
    
    /**
     * 批量删除警员的站点关联
     *
     * @param empId 警员ID
     * @return 删除数量
     */
    int deleteByEmpId(@Param("empId") String empId);
    
    /**
     * 批量插入警员站点关联
     *
     * @param relations 关联关系列表
     * @return 插入数量
     */
    int batchInsert(@Param("relations") List<BasePoliceEmpStation> relations);
}
package cn.com.sipsg.module.business.module.supervision.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum QuestionTypeEnum {
    SINGLE_CHOICE("1", "单选题"),
    MULTIPLE_CHOICE("2", "多选题"),
    TRUE_FALSE("3", "判断题"),
    UNKNOWN("", "未知");

    private final String code;
    private final String desc;

    public static QuestionTypeEnum from(String value) {
        for (QuestionTypeEnum type : values()) {
            if (type.getCode().equals(value) || type.getDesc().equals(value)) {
                return type;
            }
        }
        return UNKNOWN;
    }
}
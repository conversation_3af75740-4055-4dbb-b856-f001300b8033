package cn.com.sipsg.module.business.module.duty.pojo.dto;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 新增-修改岗位类别DTO
 */
@Data
public class PostCategoryAddDTO {
    @Schema(description = "岗位类别ID（修改时必填，新增时不填）", example = "1")
    private String id; // 岗位类别ID，修改时必填
    @Schema(description = "类别名称", example = "巡逻岗")
    private String name; // 类别名称
    @Schema(description = "岗位角色 1-民警 2-辅警", example = "1")
    private Integer role; // 岗位角色 1-民警 2-辅警
}
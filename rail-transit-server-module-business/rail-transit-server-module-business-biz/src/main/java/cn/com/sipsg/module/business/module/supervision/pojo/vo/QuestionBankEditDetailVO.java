package cn.com.sipsg.module.business.module.supervision.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 题库编辑详情VO
 * 用于题库编辑页面反显和数据回填
 */
@Data
@Schema(description = "题库编辑详情VO")
public class QuestionBankEditDetailVO {
    /** 题库编号 */
    @Schema(description = "题库编号")
    private String questionBankId;
    /** 题库名称 */
    @Schema(description = "题库名称")
    private String name;
    /** 题库描述 */
    @Schema(description = "题库描述")
    private String description;
    /** 题库原文件下载URL */
    @Schema(description = "题库原文件下载URL")
    private String fileUrl;
} 
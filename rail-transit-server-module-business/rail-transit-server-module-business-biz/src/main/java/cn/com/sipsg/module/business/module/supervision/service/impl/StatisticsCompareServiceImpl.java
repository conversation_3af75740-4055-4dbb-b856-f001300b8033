package cn.com.sipsg.module.business.module.supervision.service.impl;

import cn.com.sipsg.common.exception.BusinessException;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.security.core.util.SecurityUtils;
import cn.com.sipsg.module.business.module.common.util.UserOrgUtils;
import cn.com.sipsg.module.business.module.duty.entity.BasePoliceOrg;
import cn.com.sipsg.module.business.module.duty.entity.BaseStation;
import cn.com.sipsg.module.business.module.duty.mapper.BasePoliceOrgMapper;
import cn.com.sipsg.module.business.module.duty.mapper.BaseStationMapper;
import cn.com.sipsg.module.business.module.supervision.entity.*;
import cn.com.sipsg.module.business.module.supervision.mapper.*;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.*;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.*;
import cn.com.sipsg.module.business.module.supervision.service.StatisticsCompreService;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class StatisticsCompareServiceImpl implements StatisticsCompreService {
    private final BasePoliceOrgMapper basePoliceOrgMapper;
    private final StatisticsCompreMapper statisticsCompreMapper;
    private final StatisticsDailyPatrolMapper statisticsDailyPatrolMapper;
    private final StatisticsDailyPatrolPeriodMapper statisticsDailyPatrolPeriodMapper;
    private final StatisticsDailyReportMapper statisticsDailyReportMapper;
    private final PersonnelCapturedMapper personnelCapturedMapper;
    private final PersonnelAttentionMapper personnelAttentionMapper;
    private final UserOrgUtils userOrgUtils;
    private final BaseStationMapper baseStationMapper;
    private final BaseSubwayStationMapper baseSubwayStationMapper;

    /**
     * 分页查询当前用户所在单位及下级单位的统计对比信息
     *
     * @param pageNum  页码
     * @param pageSize 每页记录数
     * @return 包含统计对比信息的CommonPageVO对象
     */
    @Override
    public CommonPageVO<StatisticsComparePageVO> pageAllStatisticsCompre(int pageNum, int pageSize) {
        // 获取当前用户及下级单位ID集合
        Set<String> orgIds;
        if (SecurityUtils.isSuperAdmin()) {
            // 超级管理员可以查看所有组织的数据
            List<BasePoliceOrg> allOrg = basePoliceOrgMapper.selectList(null);
            orgIds = allOrg.stream().map(BasePoliceOrg::getId).collect(Collectors.toSet());
        } else {
            // 使用工具类方法获取当前用户及下级组织ID
            List<String> orgIdList = userOrgUtils.getCurrentUserAndSubOrgIds();
            orgIds = new HashSet<>(orgIdList);
        }

        if (orgIds.isEmpty()) {
            return CommonPageVO.build(Collections.emptyList(), pageNum, pageSize, 0);
        }

        // 构建组织ID与名称的映射关系，便于后续查询组织名称
        Map<String, String> orgNameMap = buildOrgNameMap();

        // 创建查询条件对象，限制在当前用户及下级单位范围内，并按统计时间降序排序
        LambdaQueryWrapper<StatisticsCompre> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(StatisticsCompre::getPoliceOrgId, orgIds)
                .eq(StatisticsCompre::getAvailable, true)
                .orderByDesc(StatisticsCompre::getStatisticsTime);

        // 分页查询统计对比信息
        IPage<StatisticsCompre> page = statisticsCompreMapper.selectPage(new Page<>(pageNum, pageSize), wrapper);

        // 调用方法组装并返回统计对比信息的分页响应对象
        return getStatisticsComparePageVOCommonPageVO(pageNum, pageSize, orgNameMap, page);
    }

    /**
     * 根据条件分页查询统计对比信息
     *
     * @param dto 查询条件
     * @return 返回包含统计对比信息的CommonPageVO对象
     */
    @Override
    public CommonPageVO<StatisticsComparePageVO> pageStatisticsCompareByCondition(StatisticsComparePageReqDTO dto) {
        // 调用已存在的方法，从DTO中获取参数
        return pageStatisticsCompareByCondition(
                dto.getStartTime(),
                dto.getEndTime(),
                dto.getPoliceOrgId(),
                dto.getPageNum(),
                dto.getPageSize()
        );
    }

    @Override
    public CommonPageVO<StatisticsComparePageVO> pageStatisticsCompareByCondition(LocalDateTime startTime, LocalDateTime endTime, String policeOrgId, int pageNum, int pageSize) {
        // 构建组织ID与名称的映射关系，用于后续创建公安机关名称映射
        Map<String, String> orgNameMap = buildOrgNameMap();
        log.info("[统计比较查询] 总组织数量: {}", orgNameMap.size());

        // 计算可查询的组织ID集合
        Set<String> allowedOrgIds;
        boolean isSuperAdmin = SecurityUtils.isSuperAdmin();
        log.info("[统计比较查询] 是否超级管理员: {}, 指定组织ID: {}", isSuperAdmin, policeOrgId);

        if (isSuperAdmin) {
            // 超级管理员可以查看所有组织的数据
            if (StringUtils.isNotBlank(policeOrgId)) {
                // 只有指定了具体单位编号时才添加组织限制
                allowedOrgIds = Collections.singleton(policeOrgId);
            } else {
                // 管理员默认不添加组织限制，可以查看所有数据
                allowedOrgIds = null;
            }
        } else {
            // 普通用户只能查看自己及下级单位的数据
            Set<String> userOrgIds = new HashSet<>(userOrgUtils.getCurrentUserAndSubOrgIds());
            log.info("[统计比较查询] 普通用户可访问组织ID: {}", userOrgIds);
            if (policeOrgId != null) {
                // 检查指定的组织ID是否在用户权限范围内
                if (userOrgIds.contains(policeOrgId)) {
                    allowedOrgIds = Collections.singleton(policeOrgId);
                } else {
                    // 如果指定的组织ID不在权限范围内，返回空结果
                    log.warn("[统计比较查询] 指定组织ID不在用户权限范围内: {}", policeOrgId);
                    return CommonPageVO.build(Collections.emptyList(), pageNum, pageSize, 0);
                }
            } else {
                allowedOrgIds = userOrgIds;
            }
        }

        log.info("[统计比较查询] 最终查询组织ID集合: {}", allowedOrgIds);

        if (allowedOrgIds != null && allowedOrgIds.isEmpty()) {
            log.warn("[统计比较查询] 允许查询的组织ID集合为空");
            return CommonPageVO.build(Collections.emptyList(), pageNum, pageSize, 0);
        }

        LambdaQueryWrapper<StatisticsCompre> wrapper = new LambdaQueryWrapper<>();
        // 只有当allowedOrgIds不为null时才添加组织ID限制（管理员无组织限制时为null）
        if (allowedOrgIds != null) {
            wrapper.in(StatisticsCompre::getPoliceOrgId, allowedOrgIds);
        }
        wrapper.eq(StatisticsCompre::getAvailable, true);

        IPage<StatisticsCompre> page = getStatisticCompare(startTime, endTime, pageNum, pageSize, wrapper);
        log.info("[统计比较查询] 查询结果总数: {}, 当前页记录数: {}", page.getTotal(), page.getRecords().size());
        return getStatisticsComparePageVOCommonPageVO(pageNum, pageSize, orgNameMap, page);
    }

    /**
     * 根据指定条件获取统计比较数据
     *
     * @param startTime 开始时间，用于筛选统计时间大于等于该时间的数据
     * @param endTime   结束时间，用于筛选统计时间小于等于该时间的数据
     * @param pageNum   页码，用于分页查询
     * @param pageSize  页面大小，用于分页查询
     * @param wrapper   查询条件封装对象，用于设置查询条件
     * @return 返回封装了统计比较数据的分页对象
     */
    private IPage<StatisticsCompre> getStatisticCompare(LocalDateTime startTime, LocalDateTime endTime, int pageNum, int pageSize, LambdaQueryWrapper<StatisticsCompre> wrapper) {
        // 如果开始时间不为空，则添加统计时间大于等于开始时间的查询条件
        if (startTime != null) wrapper.ge(StatisticsCompre::getStatisticsTime, startTime);
        // 如果结束时间不为空，则添加统计时间小于等于结束时间的查询条件
        if (endTime != null) wrapper.le(StatisticsCompre::getStatisticsTime, endTime);
        // 设置按照统计时间降序排列查询结果
        wrapper.orderByDesc(StatisticsCompre::getStatisticsTime);
        // 执行分页查询，并返回查询结果
        return statisticsCompreMapper.selectPage(new Page<>(pageNum, pageSize), wrapper);
    }

    /**
     * 构建统计比较页面视图对象
     *
     * @param e          统计比较数据实体，包含需要展示的统计数据
     * @param orgNameMap 组织ID与名称的映射，用于获取单位名称
     * @return 返回一个填充了数据的统计比较页面视图对象
     */
    private static StatisticsComparePageVO buildStatisticsComparePageVO(StatisticsCompre e, Map<String, String> orgNameMap) {
        // 创建统计比较页面视图对象实例
        StatisticsComparePageVO vo = new StatisticsComparePageVO();
        // 设置视图对象的ID
        vo.setId(e.getId());
        // 设置视图对象的单位ID
        vo.setUnitId(e.getPoliceOrgId());
        // 根据单位ID设置单位名称，如果找不到对应名称，则默认为空字符串
        vo.setUnitName(orgNameMap.getOrDefault(e.getPoliceOrgId(), ""));
        // 设置统计时间
        vo.setStatisticsTime(e.getStatisticsTime());

        // 创建并设置警力信息视图对象
        StatisticsComparePageVO.PoliceForceVO policeForce = new StatisticsComparePageVO.PoliceForceVO();
        policeForce.setPolice(e.getPolice());
        policeForce.setPoliceCount(e.getPolice());
        policeForce.setAuxiliaryPolice(e.getAuxiliaryPolice());
        policeForce.setPreventionTreatment(e.getPreventionTreatment());
        vo.setPoliceForce(policeForce);

        // 创建并设置身份核查视图对象
        StatisticsComparePageVO.IdentityCheckVO identityCheck = getIdentityCheckVO(e);
        vo.setIdentityCheck(identityCheck);

        // 返回填充完毕的视图对象
        return vo;
    }

    /**
     * 根据StatisticsCompare对象构建IdentityCheckVO对象
     * 此方法用于将StatisticsCompare对象中的身份检查相关数据
     * 映射到StatisticsComparePageVO的IdentityCheckVO对象中
     * 主要用于统计比较页面的身份检查数据展示
     *
     * @param e StatisticsCompare对象，包含原始的身份检查数据
     * @return 返回一个填充了来自StatisticsCompare对象数据的IdentityCheckVO对象
     */
    private static StatisticsComparePageVO.IdentityCheckVO getIdentityCheckVO(StatisticsCompre e) {
        // 创建IdentityCheckVO对象实例
        StatisticsComparePageVO.IdentityCheckVO identityCheck = new StatisticsComparePageVO.IdentityCheckVO();

        // 以下代码将StatisticsCompare对象中的数据映射到IdentityCheckVO对象中
        identityCheck.setChecksNum(e.getChecksNum());
        identityCheck.setPoliceCommunication(e.getPoliceCommunication());
        identityCheck.setPda(e.getPda());
        identityCheck.setBlueLetter(e.getBlueLetter());
        identityCheck.setInvestigation(e.getInvestigation());
        identityCheck.setDrugRecord(e.getDrugRecord());
        identityCheck.setOtherViolations(e.getOtherViolations());

        // 返回填充好的IdentityCheckVO对象
        return identityCheck;
    }

    /**
     * 构建CommonPageVO对象，用于统计比较页面展示
     * 此方法的作用是将分页查询结果转换为页面展示所需的格式
     * 它通过将每条记录映射为StatisticsComparePageVO对象，并将其收集到一个列表中，
     * 然后使用这个列表以及其他分页信息来构建CommonPageVO对象
     *
     * @param pageNum    页码，表示当前查询的是第几页数据
     * @param pageSize   每页大小，表示每页包含的记录数
     * @param orgNameMap 组织名称映射，用于将组织ID转换为组织名称
     * @param page       分页查询结果，包含当前页的记录以及总记录数等信息
     * @return 返回一个CommonPageVO对象，其中包含了转换后的记录列表、当前页码、每页大小和总记录数
     */
    private CommonPageVO<StatisticsComparePageVO> getStatisticsComparePageVOCommonPageVO(int pageNum, int pageSize, Map<String, String> orgNameMap, IPage<StatisticsCompre> page) {
        // 将查询结果的每条记录转换为StatisticsComparePageVO对象，并收集到列表中
        List<StatisticsComparePageVO> voList = page.getRecords().stream()
                .map(e -> buildStatisticsComparePageVO(e, orgNameMap))
                .collect(Collectors.toList());
        // 使用转换后的记录列表、当前页码、每页大小和总记录数构建并返回CommonPageVO对象
        return CommonPageVO.build(voList, pageNum, pageSize, page.getTotal());
    }

    /**
     * 查询当前用户所在单位及下级单位的巡逻情况分页列表
     *
     * @param dto 查询参数
     * @return 分页封装的巡逻情况VO列表
     */
    @Override
    public CommonPageVO<PatrolPageVO> pagePatrolForCurrentUser(PatrolPageReqDTO dto) {
        LocalDateTime startTime = dto.getStartTime();
        LocalDateTime endTime = dto.getEndTime();
        int pageNum = dto.getPageNum();
        int pageSize = dto.getPageSize();
        Set<String> orgIds;
        if (SecurityUtils.isSuperAdmin()) {
            // 超级管理员可以查看所有组织的数据
            List<BasePoliceOrg> allOrgs = basePoliceOrgMapper.selectList(null);
            orgIds = allOrgs.stream().map(BasePoliceOrg::getId).collect(Collectors.toSet());
        } else {
            // 普通用户只能查看自己及下级单位的数据
            orgIds = new HashSet<>(userOrgUtils.getCurrentUserAndSubOrgIds());
        }

        if (orgIds.isEmpty()) {
            return CommonPageVO.build(Collections.emptyList(), pageNum, pageSize, 0);
        }
        Map<String, String> orgNameMap = buildOrgNameMap();
        LambdaQueryWrapper<StatisticsDailyPatrol> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(StatisticsDailyPatrol::getPoliceOrgId, orgIds)
                .eq(StatisticsDailyPatrol::getAvailable, true);
        if (startTime != null) wrapper.ge(StatisticsDailyPatrol::getFormTime, startTime);
        if (endTime != null) wrapper.le(StatisticsDailyPatrol::getFormTime, endTime);
        wrapper.orderByDesc(StatisticsDailyPatrol::getFormTime);
        Page<StatisticsDailyPatrol> page = new Page<>(pageNum, pageSize);
        Page<StatisticsDailyPatrol> result = statisticsDailyPatrolMapper.selectPage(page, wrapper);
        List<PatrolPageVO> voList = result.getRecords().stream().map(entity -> {
            PatrolPageVO vo = new PatrolPageVO();
            mapPatrolEntityToPageVO(entity, vo);
            vo.setPoliceOrgName(orgNameMap.getOrDefault(entity.getPoliceOrgId(), ""));
            return vo;
        }).collect(Collectors.toList());
        return CommonPageVO.build(voList, (int) result.getCurrent(), (int) result.getSize(), result.getTotal());
    }

    private String getWarningSourcesText(Short code) {
        if (code == null) return "";
        switch (code) {
            case 1:
                return "101预警";
            case 2:
                return "票卡预警";
            case 3:
                return "盘查";
            case 4:
                return "其他";
            default:
                return String.valueOf(code);
        }
    }

    private String getCaptureTypeText(Short code) {
        if (code == null) return "";
        switch (code) {
            case 1:
                return "全国在逃";
            case 2:
                return "公安临控";
            case 3:
                return "其他";
            default:
                return String.valueOf(code);
        }
    }

    private String getProcessResultText(Short code) {
        if (code == null) return "";
        switch (code) {
            case 1:
                return "拘留";
            case 2:
                return "移交";
            default:
                return String.valueOf(code);
        }
    }

    private String getPostProcessResultText(Short code) {
        if (code == null) return "";
        switch (code) {
            case 1:
                return "行政拘留";
            case 2:
                return "刑事拘留";
            case 3:
                return "取保候审";
            case 4:
                return "排除嫌疑";
            case 5:
                return "放行";
            case 6:
                return "不予处罚";
            case 99:
                return "其他";
            default:
                return String.valueOf(code);
        }
    }

    private String formatDateTime(LocalDateTime dt) {
        return dt == null ? "" : dt.toString().replace("T", " ");
    }

    private PersonnelCapturedPageVO toPageVO(PersonnelCaptured e) {
        PersonnelCapturedPageVO vo = new PersonnelCapturedPageVO();
        vo.setId(e.getId());
        vo.setName(e.getName());
        vo.setIdNo(e.getIdNo());
        vo.setPoliceOrgName(e.getPoliceOrgName());
        vo.setWarningSourcesText(getWarningSourcesText(e.getWarningSources()));
        vo.setCaptureTypeText(getCaptureTypeText(e.getCaptureType()));
        vo.setCaptureDate(formatDateTime(e.getCaptureDate()));
        vo.setCaptureSite(e.getCaptureSite());
        vo.setProcessResultText(getProcessResultText(e.getProcessResult()));
        vo.setTurnOverOrg(e.getTurnOverOrg());
        vo.setPostProcessResultText(getPostProcessResultText(e.getPostProcessResult()));
        return vo;
    }

    @Override
    public CommonPageVO<PersonnelCapturedPageVO> pageAllPersonnelCapturedForCurrentUser(int pageNum, int pageSize) {
        Set<String> orgIds = getCurrentUserAccessibleOrgIds();
        if (orgIds.isEmpty()) {
            return CommonPageVO.build(Collections.emptyList(), pageNum, pageSize, 0);
        }
        LambdaQueryWrapper<PersonnelCaptured> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(PersonnelCaptured::getPoliceOrgId, orgIds).eq(PersonnelCaptured::getAvailable, true);
        return getPersonnelCapturedPageVOCommonPageVO(pageNum, pageSize, wrapper);
    }

    /**
     * 根据查询条件分页获取人员捕获信息页面VO
     *
     * @param pageNum  页码，表示请求的数据位于第几页
     * @param pageSize 页面大小，表示每页包含的数据条数
     * @param wrapper  查询条件封装对象，用于指定查询的具体条件
     * @return 返回一个通用页面VO，其中包含人员捕获信息页面VO的列表以及分页信息
     */
    private CommonPageVO<PersonnelCapturedPageVO> getPersonnelCapturedPageVOCommonPageVO(int pageNum, int pageSize, LambdaQueryWrapper<PersonnelCaptured> wrapper) {
        // 创建一个分页对象，用于数据库查询时指定页码和页面大小
        Page<PersonnelCaptured> page = new Page<>(pageNum, pageSize);
        // 执行分页查询，获取符合条件的人员捕获信息
        Page<PersonnelCaptured> result = personnelCapturedMapper.selectPage(page, wrapper);
        // 将查询结果转换为人员捕获信息页面VO列表
        List<PersonnelCapturedPageVO> voList = result.getRecords().stream().map(this::toPageVO).collect(Collectors.toList());
        // 构建并返回通用页面VO，包含VO列表、当前页码、页面大小和总记录数
        return CommonPageVO.build(voList, (int) result.getCurrent(), (int) result.getSize(), result.getTotal());
    }

    /**
     * 获取当前用户及其子组织的所有人员关注信息分页数据
     *
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @return 返回包含人员关注信息的CommonPageVO对象
     */
    @Override
    public CommonPageVO<PersonnelAttention> pageAllPersonnelAttentionForCurrentUser(int pageNum, int pageSize) {
        // 获取当前用户及其子组织的ID集合
        Set<String> orgIds = getCurrentUserAccessibleOrgIds();

        // 如果orgIds为空，说明没有相关数据，直接返回空的数据页
        if (orgIds.isEmpty()) {
            return CommonPageVO.build(Collections.emptyList(), pageNum, pageSize, 0);
        }

        // 创建查询条件包装器
        LambdaQueryWrapper<PersonnelAttention> wrapper = new LambdaQueryWrapper<>();
        // 在查询条件中加入组织ID的IN条件
        wrapper.in(PersonnelAttention::getPoliceOrgId, orgIds);
        wrapper.eq(PersonnelAttention::getAvailable, true);

        // 创建分页对象
        Page<PersonnelAttention> page = new Page<>(pageNum, pageSize);

        // 执行分页查询
        Page<PersonnelAttention> result = personnelAttentionMapper.selectPage(page, wrapper);

        // 使用查询结果构建并返回分页数据传输对象
        return CommonPageVO.<PersonnelAttention>builder()
                .records(result.getRecords())
                .current(result.getCurrent())
                .size(result.getSize())
                .total(result.getTotal())
                .build();
    }

    /**
     * 根据条件分页查询当前用户的人员关注信息
     *
     * @param warningSource 预警来源，用于筛选查询结果
     * @param personType    人员类型，用于筛选查询结果
     * @param pageNum       当前页码，用于分页查询
     * @param pageSize      每页记录数，用于分页查询
     * @return 返回包含人员关注信息的CommonPageVO对象
     */
    @Override
    public CommonPageVO<PersonnelAttention> pagePersonnelAttentionForCurrentUserByCondition(String orgId, Integer warningSource, Integer focusType, Integer personType, int pageNum, int pageSize) {
        // 获取当前用户可访问的组织ID集合
        Set<String> allowedOrgIds = getCurrentUserAccessibleOrgIds();

        if (allowedOrgIds.isEmpty()) {
            return CommonPageVO.build(Collections.emptyList(), pageNum, pageSize, 0);
        }

        // 创建LambdaQueryWrapper对象，用于条件查询
        LambdaQueryWrapper<PersonnelAttention> wrapper = new LambdaQueryWrapper<>();

        // 添加数据权限控制：只能查询用户权限范围内的组织数据
        wrapper.in(PersonnelAttention::getPoliceOrgId, allowedOrgIds);

        // 根据传入的单位名称进行条件筛选
        if (orgId != null && !orgId.isEmpty()) {
            wrapper.eq(PersonnelAttention::getPoliceOrgId, orgId);
        }

        // 根据传入的预警来源进行条件筛选
        if (warningSource != null) {
            wrapper.eq(PersonnelAttention::getWarningSources, warningSource);
        }

        // 根据传入的关注类型进行条件筛选
        if (focusType != null) {
            wrapper.eq(PersonnelAttention::getAttentionType, focusType);
        }

        // 根据传入的人员类型进行条件筛选
        if (personType != null) {
            wrapper.eq(PersonnelAttention::getPersonnelType, personType);
        }

        // 只查询有效数据
        wrapper.eq(PersonnelAttention::getAvailable, true);
        // 按处理时间降序排列查询结果
        wrapper.orderByDesc(PersonnelAttention::getDisposeTime);

        // 创建Page对象，用于分页查询
        Page<PersonnelAttention> page = new Page<>(pageNum, pageSize);

        // 执行分页查询，获取结果
        Page<PersonnelAttention> result = personnelAttentionMapper.selectPage(page, wrapper);

        // 构建并返回包含查询结果的CommonPageVO对象
        return CommonPageVO.<PersonnelAttention>builder()
                .records(result.getRecords())
                .current(result.getCurrent())
                .size(result.getSize())
                .total(result.getTotal())
                .build();
    }

    /**
     * 分页查询当前用户所在单位及下级单位的抓获统计数据（统一方法）
     * 根据查询条件动态决定查询逻辑，如果所有条件都为空则查询全部数据
     *
     * @param dto 查询条件
     * @return 分页封装的抓获统计VO列表
     */
    @Override
    public CommonPageVO<PersonnelCapturedPageVO> pagePersonnelCapturedForCurrentUser(CapturedPageQueryReqDTO dto) {
        String policeOrgId = dto.getPoliceOrgId();
        String warningSources = dto.getWarningSources();
        String captureType = dto.getCaptureType();
        String captureSite = dto.getCaptureSite();
        int pageNum = dto.getPageNum();
        int pageSize = dto.getPageSize();
        // 判断是否有查询条件
        if ((policeOrgId == null || policeOrgId.isEmpty()) && (warningSources == null || warningSources.isEmpty()) && (captureType == null || captureType.isEmpty()) && (captureSite == null || captureSite.isEmpty())) {
            return pageAllPersonnelCapturedForCurrentUser(pageNum, pageSize);
        } else {
            // 直接在此处实现原有条件查询逻辑，policeOrgId保持String类型
            Short warningSourcesShort = null;
            if (warningSources != null && !warningSources.isEmpty()) {
                try {
                    warningSourcesShort = Short.valueOf(warningSources);
                } catch (Exception ignored) {
                }
            }
            Short captureTypeShort = null;
            if (captureType != null && !captureType.isEmpty()) {
                try {
                    captureTypeShort = Short.valueOf(captureType);
                } catch (Exception ignored) {
                }
            }
            // 直接在此处实现原pagePersonnelCapturedForCurrentUserByCondition的查询逻辑
            Set<String> orgIds = getCurrentUserAccessibleOrgIds();
            if (orgIds.isEmpty()) {
                return CommonPageVO.build(Collections.emptyList(), pageNum, pageSize, 0);
            }
            LambdaQueryWrapper<PersonnelCaptured> wrapper = new LambdaQueryWrapper<>();
            if (policeOrgId != null && !policeOrgId.isEmpty()) {
                if (SecurityUtils.isSuperAdmin() || orgIds.contains(policeOrgId)) {
                    wrapper.eq(PersonnelCaptured::getPoliceOrgId, policeOrgId);
                } else {
                    return CommonPageVO.build(Collections.emptyList(), pageNum, pageSize, 0);
                }
            } else {
                wrapper.in(PersonnelCaptured::getPoliceOrgId, orgIds);
            }
            if (warningSourcesShort != null) {
                wrapper.eq(PersonnelCaptured::getWarningSources, warningSourcesShort);
            }
            if (captureTypeShort != null) {
                wrapper.eq(PersonnelCaptured::getCaptureType, captureTypeShort);
            }
            if (captureSite != null && !captureSite.isEmpty()) {
                wrapper.like(PersonnelCaptured::getCaptureSite, captureSite);
            }
            wrapper.eq(PersonnelCaptured::getAvailable, true);
            wrapper.orderByDesc(PersonnelCaptured::getCaptureDate);
            return getPersonnelCapturedPageVOCommonPageVO(pageNum, pageSize, wrapper);
        }
    }

    /**
     * 分页查询当前用户所在单位及下级单位的人员关注数据（统一方法）
     * 根据查询条件动态决定查询逻辑，如果所有条件都为空则查询全部数据
     *
     * @param dto 筛选条件
     * @return 分页封装的人员关注VO列表
     */
    @Override
    public CommonPageVO<PersonnelAttention> pagePersonnelAttentionForCurrentUser(FocusPageQueryReqDTO dto) {
        String orgId = dto.getOrgId();
        Integer warningSource = dto.getWarningSource();
        Integer focusType = dto.getFocusType();
        Integer personType = dto.getPersonType();
        int pageNum = dto.getPageNum() != null ? dto.getPageNum() : 1;
        int pageSize = dto.getPageSize() != null ? dto.getPageSize() : 10;
        // 判断是否有查询条件
        if (orgId == null && warningSource == null && focusType == null && personType == null) {
            // 无条件查询，返回全部数据
            return pageAllPersonnelAttentionForCurrentUser(pageNum, pageSize);
        } else {
            // 有条件查询，按条件筛选
            return pagePersonnelAttentionForCurrentUserByCondition(orgId, warningSource, focusType, personType, pageNum, pageSize);
        }
    }

    /**
     * 获取每日报备详情
     *
     * @param id 报备ID
     * @return 每日报备详情
     */
    @Override
    public DailyReportDetailVO getDailyReportDetail(String id) {
        try {
            if (StrUtil.isBlank(id)) {
                throw new BusinessException("报备ID不能为空");
            }

            // 根据ID查询每日报备记录
            StatisticsDailyReport report = statisticsDailyReportMapper.selectById(id);

            if (report == null) {
                throw new BusinessException("每日报备记录不存在");
            }

            DailyReportDetailVO vo = new DailyReportDetailVO();
            // 手动映射所有字段
            vo.setId(report.getId());
            vo.setCreateUserId(report.getCreateUserId());
            vo.setCreateTime(report.getCreateTime());
            vo.setUpdateUserId(report.getUpdateUserId());
            vo.setUpdateTime(report.getUpdateTime());
            vo.setAvailable(report.getAvailable());
            // 使用抽取的方法进行字段映射
            mapReportToDetailVO(report, vo);

            return vo;
        } catch (Exception e) {
            log.error("查询每日报备详情失败，ID：{}", id, e);
            throw new BusinessException("查询每日报备详情失败");
        }
    }

    /**
     * 保存或更新每日报备
     *
     * @param dto 每日报备编辑请求DTO
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveOrUpdateDailyReport(DailyReportEditReqDTO dto) {
        try {
            // 参数校验
            if (dto.getFormTime() == null) {
                throw new BusinessException("填报时间不能为空");
            }
            if (StrUtil.isBlank(dto.getPoliceOrgId())) {
                throw new BusinessException("单位ID不能为空");
            }

            StatisticsDailyReport report;
            boolean isUpdate = false;

            if (StrUtil.isNotBlank(dto.getId())) {
                // 编辑模式：根据ID查询现有记录
                report = statisticsDailyReportMapper.selectById(dto.getId());
                if (report == null) {
                    throw new BusinessException("要编辑的记录不存在");
                }
                isUpdate = true;
            } else {
                // 新增模式：检查是否已存在相同时间和单位的记录
                QueryWrapper<StatisticsDailyReport> wrapper = new QueryWrapper<>();
                wrapper.eq("available", true)
                        .eq("form_time", dto.getFormTime())
                        .eq("police_org_id", Long.valueOf(dto.getPoliceOrgId()));

                StatisticsDailyReport existingReport = statisticsDailyReportMapper.selectOne(wrapper);
                if (existingReport != null) {
                    throw new BusinessException("该时间段该单位的报备记录已存在");
                }

                report = new StatisticsDailyReport();
                report.setCreateTime(LocalDateTime.now());
                report.setCreateUserId(SecurityUtils.getLoginUserId());
                report.setAvailable(true);
            }

            // 使用抽取的方法进行字段映射
            mapDTOToReport(dto, report);

            // 设置更新信息
            report.setUpdateTime(LocalDateTime.now());
            report.setUpdateUserId(SecurityUtils.getLoginUserId());

            int result;
            if (isUpdate) {
                result = statisticsDailyReportMapper.updateById(report);
                if (result <= 0) {
                    throw new BusinessException("更新每日报备失败");
                }
                log.info("更新每日报备成功，ID：{}，填报时间：{}，单位ID：{}",
                        report.getId(), dto.getFormTime(), dto.getPoliceOrgId());
            } else {
                result = statisticsDailyReportMapper.insert(report);
                if (result <= 0) {
                    throw new BusinessException("新增每日报备失败");
                }
                log.info("新增每日报备成功，ID：{}，填报时间：{}，单位ID：{}",
                        report.getId(), dto.getFormTime(), dto.getPoliceOrgId());
            }

            return true;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("保存或更新每日报备失败", e);
            throw new BusinessException("保存或更新每日报备失败");
        }
    }

    /**
     * 分页查询每日报备数据
     * 支持按时间范围查询，支持数据权限（管理员看全部，普通用户看当前单位及下级单位）
     *
     * @param dto 分页查询请求DTO
     * @return 分页封装的每日报备VO列表
     */
    @Override
    public CommonPageVO<DailyReportVO> pageDailyReport(DailyReportPageReqDTO dto) {
        try {
            int pageNum = dto.getPageNum() != null ? dto.getPageNum() : 1;
            int pageSize = dto.getPageSize() != null ? dto.getPageSize() : 10;

            // 构建查询条件
            QueryWrapper<StatisticsDailyReport> wrapper = new QueryWrapper<>();
            wrapper.eq("available", true);

            // 时间范围查询
            if (dto.getStartTime() != null) {
                wrapper.ge("form_time", dto.getStartTime());
            }
            if (dto.getEndTime() != null) {
                wrapper.le("form_time", dto.getEndTime());
            }

            // 数据权限控制
            if (SecurityUtils.isSuperAdmin()) {
                // 管理员可以查看全部数据
                log.info("[每日报备分页查询] 管理员查询全部数据");
            } else {
                // 普通用户只能查看当前单位及下级单位数据
                List<String> orgIds = userOrgUtils.getCurrentUserAndSubOrgIds();
                if (orgIds.isEmpty()) {
                    log.warn("[每日报备分页查询] 用户无权限查看任何单位数据");
                    return CommonPageVO.build(Collections.emptyList(), pageNum, pageSize, 0);
                }
                wrapper.in("police_org_id", orgIds);
                log.info("[每日报备分页查询] 普通用户查询单位范围：{}", orgIds);
            }

            // 按时间倒序排列
            wrapper.orderByDesc("form_time");

            // 执行分页查询
            Page<StatisticsDailyReport> page = new Page<>(pageNum, pageSize);
            Page<StatisticsDailyReport> resultPage = statisticsDailyReportMapper.selectPage(page, wrapper);

            // 转换为VO对象
            List<DailyReportVO> voList = new ArrayList<>();
            Map<String, String> orgNameMap = new HashMap<>();

            for (StatisticsDailyReport report : resultPage.getRecords()) {
                DailyReportVO vo = new DailyReportVO();

                // 手动映射字段
                vo.setId(String.valueOf(report.getId()));
                vo.setPoliceOrgId(String.valueOf(report.getPoliceOrgId()));
                vo.setInputTime(report.getFormTime());
                vo.setResponsiblePerson(report.getResponsibleName());
                vo.setDuty(report.getResponsiblePosition());
                vo.setPhone(report.getResponsiblePhone());
                vo.setDutyFrequencyPoint(report.getResponsibleChannel());

                // 获取组织名称（缓存优化）
                if (report.getPoliceOrgId() != null) {
                    String orgName = orgNameMap.get(report.getPoliceOrgId());
                    if (orgName == null) {
                        BasePoliceOrg org = basePoliceOrgMapper.selectById(report.getPoliceOrgId());
                        if (org != null) {
                            orgName = org.getName();
                            orgNameMap.put(report.getPoliceOrgId(), orgName);
                        }
                    }
                    vo.setPoliceOrgName(orgName);
                }

                voList.add(vo);
            }

            log.info("[每日报备分页查询] 查询成功，页码：{}，每页大小：{}，总记录数：{}",
                    pageNum, pageSize, resultPage.getTotal());

            return CommonPageVO.build(voList, pageNum, pageSize, resultPage.getTotal());

        } catch (Exception e) {
            log.error("[每日报备分页查询] 查询失败", e);
            throw new BusinessException("查询每日报备数据失败");
        }
    }

    /**
     * 保存或更新日常数据
     *
     * @param dto 日常数据保存/更新DTO
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveOrUpdateStatisticsCompre(StatisticsCompreSaveDTO dto) {
        try {
            StatisticsCompre entity;
            boolean isUpdate = false;

            if (dto.getId() != null && !dto.getId().trim().isEmpty()) {
                // 更新模式：检查记录是否存在
                entity = statisticsCompreMapper.selectById(dto.getId());
                if (entity == null || !Boolean.TRUE.equals(entity.getAvailable())) {
                    throw new BusinessException("要编辑的记录不存在");
                }
                isUpdate = true;
            } else {
                // 新增模式：检查是否已存在相同时间和单位的记录
                QueryWrapper<StatisticsCompre> wrapper = new QueryWrapper<>();
                wrapper.eq("available", true)
                        .eq("statistics_time", dto.getStatisticsTime())
                        .eq("police_org_id", dto.getPoliceOrgId());

                StatisticsCompre existingEntity = statisticsCompreMapper.selectOne(wrapper);
                if (existingEntity != null) {
                    throw new BusinessException("该时间段该单位的日常数据记录已存在");
                }

                entity = new StatisticsCompre();
                entity.setCreateTime(LocalDateTime.now());
                entity.setCreateUserId(SecurityUtils.getLoginUserId());
                entity.setAvailable(true);
            }

            // 手动映射字段
            mapDTOToEntity(dto, entity);

            // 设置更新信息
            entity.setUpdateTime(LocalDateTime.now());
            entity.setUpdateUserId(SecurityUtils.getLoginUserId());

            int result;
            if (isUpdate) {
                result = statisticsCompreMapper.updateById(entity);
                if (result <= 0) {
                    throw new BusinessException("更新日常数据失败");
                }
                log.info("更新日常数据成功，ID：{}，统计时间：{}，单位ID：{}",
                        entity.getId(), dto.getStatisticsTime(), dto.getPoliceOrgId());
            } else {
                result = statisticsCompreMapper.insert(entity);
                if (result <= 0) {
                    throw new BusinessException("新增日常数据失败");
                }
                log.info("新增日常数据成功，ID：{}，统计时间：{}，单位ID：{}",
                        entity.getId(), dto.getStatisticsTime(), dto.getPoliceOrgId());
            }

            return true;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("保存或更新日常数据失败", e);
            throw new BusinessException("保存或更新日常数据失败");
        }
    }

    /**
     * 获取日常数据详情
     *
     * @param id 日常数据ID
     * @return 日常数据详情VO
     */
    @Override
    public StatisticsCompreSaveDTO getStatisticsCompreDetail(String id) {
        try {
            if (id == null || id.trim().isEmpty()) {
                throw new BusinessException("日常数据ID不能为空");
            }

            StatisticsCompre entity = statisticsCompreMapper.selectById(id);
            if (entity == null || !Boolean.TRUE.equals(entity.getAvailable())) {
                throw new BusinessException("日常数据记录不存在");
            }

            // 映射到DTO
            StatisticsCompreSaveDTO dto = new StatisticsCompreSaveDTO();
            dto.setId(entity.getId());
            mapEntityToDTO(entity, dto);

            log.info("获取日常数据详情成功，ID：{}", id);
            return dto;

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取日常数据详情失败，ID：{}", id, e);
            throw new BusinessException("获取日常数据详情失败");
        }
    }

    /**
     * 保存或更新巡逻信息
     *
     * @param dto 巡逻信息保存/更新DTO
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveOrUpdatePatrol(PatrolSaveDTO dto) {
        try {
            // 验证基础参数
            if (dto.getPoliceOrgId() == null || dto.getPoliceOrgId().trim().isEmpty()) {
                throw new BusinessException("单位ID不能为空");
            }
            if (dto.getFormTime() == null) {
                throw new BusinessException("表单时间不能为空");
            }
            if (dto.getCall() == null || dto.getCall().trim().isEmpty()) {
                throw new BusinessException("巡逻车呼号不能为空");
            }
            if (dto.getVehicle() == null || dto.getVehicle().trim().isEmpty()) {
                throw new BusinessException("车牌号不能为空");
            }
            if (dto.getArea() == null || dto.getArea().trim().isEmpty()) {
                throw new BusinessException("巡逻区域不能为空");
            }
            if (dto.getPatrolInfoList() == null || dto.getPatrolInfoList().isEmpty()) {
                throw new BusinessException("巡逻信息不能为空");
            }

            boolean isUpdate = dto.getId() != null;

            if (isUpdate) {
                // 更新模式：检查记录是否存在
                StatisticsDailyPatrol existingEntity = statisticsDailyPatrolMapper.selectById(dto.getId());
                if (existingEntity == null || !Boolean.TRUE.equals(existingEntity.getAvailable())) {
                    throw new BusinessException("要编辑的巡逻记录不存在");
                }

                // 删除现有的巡逻记录和相关的巡逻时段记录
                existingEntity.setAvailable(false);
                existingEntity.setUpdateTime(LocalDateTime.now());
                existingEntity.setUpdateUserId(SecurityUtils.getLoginUserId());
                statisticsDailyPatrolMapper.updateById(existingEntity);

                // 删除相关的巡逻时段记录
                LambdaQueryWrapper<StatisticsDailyPatrolPeriod> periodWrapper = new LambdaQueryWrapper<>();
                periodWrapper.eq(StatisticsDailyPatrolPeriod::getDailyPatrolId, dto.getId());
                List<StatisticsDailyPatrolPeriod> existingPeriods = statisticsDailyPatrolPeriodMapper.selectList(periodWrapper);
                for (StatisticsDailyPatrolPeriod period : existingPeriods) {
                    period.setAvailable(false);
                    period.setUpdateTime(LocalDateTime.now());
                    period.setUpdateUserId(SecurityUtils.getLoginUserId());
                    statisticsDailyPatrolPeriodMapper.updateById(period);
                }
            } else {
                // 新增模式：检查是否已存在相同时间和单位的记录
                QueryWrapper<StatisticsDailyPatrol> wrapper = new QueryWrapper<>();
                wrapper.eq("available", true)
                        .eq("form_time", dto.getFormTime())
                        .eq("police_org_id", dto.getPoliceOrgId());

                List<StatisticsDailyPatrol> existingRecords = statisticsDailyPatrolMapper.selectList(wrapper);
                if (!existingRecords.isEmpty()) {
                    throw new BusinessException("该时间段该单位的巡逻记录已存在");
                }
            }

            // 创建新的巡逻主记录
            StatisticsDailyPatrol entity = new StatisticsDailyPatrol();
            entity.setPoliceOrgId(dto.getPoliceOrgId());
            entity.setFormTime(dto.getFormTime());
            entity.setCall(dto.getCall());
            entity.setVehicle(dto.getVehicle());
            entity.setArea(dto.getArea());
            entity.setMemos(dto.getRemark());
            entity.setCreateTime(LocalDateTime.now());
            entity.setCreateUserId(SecurityUtils.getLoginUserId());
            entity.setUpdateTime(LocalDateTime.now());
            entity.setUpdateUserId(SecurityUtils.getLoginUserId());
            entity.setAvailable(true);

            int result = statisticsDailyPatrolMapper.insert(entity);
            if (result <= 0) {
                throw new BusinessException("保存巡逻信息失败");
            }

            // 插入巡逻时段记录
            for (PatrolSaveDTO.PatrolInfoDTO patrolInfo : dto.getPatrolInfoList()) {
                StatisticsDailyPatrolPeriod period = new StatisticsDailyPatrolPeriod();
                period.setCreateUserId(SecurityUtils.getLoginUserId());
                period.setCreateTime(LocalDateTime.now());
                period.setDailyPatrolId(entity.getId());
                period.setStartTime(patrolInfo.getStartTime());
                period.setEndTime(patrolInfo.getEndTime());
                period.setPoliceName(patrolInfo.getName());
                period.setPolicePhone(patrolInfo.getPhone());
                period.setPoliceChannel(patrolInfo.getFreq());
                // 转换字符串为整数
                period.setStrengthPolice(patrolInfo.getPoliceNum());
                period.setStrengthArmedPolice(patrolInfo.getFireNum());
                period.setStrengthAuxiliaryPolice(patrolInfo.getAuxNum());
                period.setStrengthOther(patrolInfo.getOtherNum());
                period.setEquipmentGun(patrolInfo.getGunNum());
                period.setEquipmentGunMsg(patrolInfo.getGunSituation());
                period.setEquipmentGpsMsg(patrolInfo.getGps());
                period.setEquipmentImgMsg(patrolInfo.getDevice());
                period.setAvailable(true);

                statisticsDailyPatrolPeriodMapper.insert(period);
            }

            log.info("{}巡逻信息成功，单位ID：{}，表单时间：{}，巡逻记录数：{}",
                    isUpdate ? "更新" : "新增", dto.getPoliceOrgId(), dto.getFormTime(), dto.getPatrolInfoList().size());
            return true;

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("保存或更新巡逻信息失败", e);
            throw new BusinessException("保存或更新巡逻信息失败");
        }
    }

    /**
     * 获取巡逻信息详情
     *
     * @param id 巡逻信息ID
     * @return 巡逻信息详情DTO
     */
    @Override
    public PatrolSaveDTO getPatrolDetail(String id) {
        try {
            if (StringUtils.isBlank(id)) {
                throw new BusinessException("巡逻信息ID不能为空");
            }

            StatisticsDailyPatrol entity = statisticsDailyPatrolMapper.selectById(id);
            if (entity == null || !Boolean.TRUE.equals(entity.getAvailable())) {
                throw new BusinessException("巡逻信息记录不存在");
            }

            // 映射到DTO
            PatrolSaveDTO dto = new PatrolSaveDTO();
            dto.setId(entity.getId());
            dto.setPoliceOrgId(entity.getPoliceOrgId());
            dto.setFormTime(entity.getFormTime());
            dto.setCall(entity.getCall());
            dto.setVehicle(entity.getVehicle());
            dto.setArea(entity.getArea());
            dto.setRemark(entity.getMemos());

            // 查询巡逻时段信息
            LambdaQueryWrapper<StatisticsDailyPatrolPeriod> periodWrapper = new LambdaQueryWrapper<>();
            periodWrapper.eq(StatisticsDailyPatrolPeriod::getDailyPatrolId, entity.getId())
                    .eq(StatisticsDailyPatrolPeriod::getAvailable, true)
                    .orderByAsc(StatisticsDailyPatrolPeriod::getStartTime);

            List<StatisticsDailyPatrolPeriod> periods = statisticsDailyPatrolPeriodMapper.selectList(periodWrapper);

            // 转换巡逻时段信息
            List<PatrolSaveDTO.PatrolInfoDTO> patrolInfoList = new ArrayList<>();
            for (StatisticsDailyPatrolPeriod period : periods) {
                PatrolSaveDTO.PatrolInfoDTO patrolInfo = new PatrolSaveDTO.PatrolInfoDTO();
                patrolInfo.setId(period.getId().toString());
                patrolInfo.setStartTime(period.getStartTime());
                patrolInfo.setEndTime(period.getEndTime());
                patrolInfo.setName(period.getPoliceName());
                patrolInfo.setPhone(period.getPolicePhone());
                patrolInfo.setFreq(period.getPoliceChannel());
                patrolInfo.setPoliceNum(period.getStrengthPolice());
                patrolInfo.setFireNum(period.getStrengthArmedPolice());
                patrolInfo.setAuxNum(period.getStrengthAuxiliaryPolice());
                patrolInfo.setOtherNum(period.getStrengthOther());
                patrolInfo.setGunNum(period.getEquipmentGun());
                patrolInfo.setGunSituation(period.getEquipmentGunMsg());
                patrolInfo.setGps(period.getEquipmentGpsMsg());
                patrolInfo.setDevice(period.getEquipmentImgMsg());
                patrolInfo.setId(period.getId().toString());

                patrolInfoList.add(patrolInfo);
            }

            dto.setPatrolInfoList(patrolInfoList);

            log.info("获取巡逻信息详情成功，ID：{}，巡逻时段数：{}", id, patrolInfoList.size());
            return dto;

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取巡逻信息详情失败，ID：{}", id, e);
            throw new BusinessException("获取巡逻信息详情失败");
        }
    }

    /**
     * 删除日常数据
     *
     * @param id 日常数据ID
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteStatisticsCompre(String id) {
        return commonDelete(id, statisticsCompreMapper, "日常数据");
    }

    /**
     * 删除每日报备
     *
     * @param id 每日报备ID
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteDailyReport(String id) {
        return commonDelete(id, statisticsDailyReportMapper, "每日报备");
    }

    /**
     * 删除巡逻情况
     *
     * @param id 巡逻情况ID
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deletePatrol(String id) {
        return commonDelete(id, statisticsDailyPatrolMapper, "巡逻情况");
    }

    /**
     * 删除抓获统计
     *
     * @param id 抓获统计ID
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteCaptured(String id) {
        return commonDelete(id, personnelCapturedMapper, "抓获统计");
    }

    /**
     * 删除关注统计
     *
     * @param id 关注统计ID
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteFocus(String id) {
        return commonDelete(id, personnelAttentionMapper, "关注统计");
    }

    /**
     * 通用逻辑删除方法
     */
    private <T> Boolean commonDelete(String id, BaseMapper<T> mapper, String entityName) {
        try {
            if (id == null) {
                throw new BusinessException(entityName + "ID不能为空");
            }
            T entity = mapper.selectById(id);
            Boolean available = null;
            try {
                available = (Boolean) entity.getClass().getMethod("getAvailable").invoke(entity);
            } catch (Exception ignore) {}
            if (entity == null || !Boolean.TRUE.equals(available)) {
                throw new BusinessException(entityName + "记录不存在");
            }
            // 逻辑删除
            try { entity.getClass().getMethod("setAvailable", Boolean.class).invoke(entity, false); } catch (Exception ignore) {}
            try { entity.getClass().getMethod("setUpdateTime", LocalDateTime.class).invoke(entity, LocalDateTime.now()); } catch (Exception ignore) {}
            try { entity.getClass().getMethod("setUpdateUserId", String.class).invoke(entity, SecurityUtils.getLoginUserId()); } catch (Exception ignore) {}
            int result = mapper.updateById(entity);
            if (result <= 0) {
                throw new BusinessException("删除" + entityName + "失败");
            }
            log.info("删除{}成功，ID：{}", entityName, id);
            return true;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("删除{}失败，ID：{}", entityName, id, e);
            throw new BusinessException("删除" + entityName + "失败");
        }
    }

    /**
     * 将StatisticsDailyReport实体对象的字段映射到DailyReportDetailVO
     *
     * @param report 每日报备实体对象
     * @param vo     每日报备详情VO对象
     */
    private void mapReportToDetailVO(StatisticsDailyReport report, DailyReportDetailVO vo) {
        mapReportToVO(report, vo);
    }


    /**
     * 获取当前用户可访问的组织ID集合
     * 超级管理员可以查看所有组织的数据，普通用户只能查看自己及下级单位的数据
     *
     * @return 组织ID集合
     */
    private Set<String> getCurrentUserAccessibleOrgIds() {
        Set<String> orgIds;
        if (SecurityUtils.isSuperAdmin()) {
            // 超级管理员可以查看所有组织的数据
            List<BasePoliceOrg> allOrgs = basePoliceOrgMapper.selectList(null);
            orgIds = allOrgs.stream().map(BasePoliceOrg::getId).collect(Collectors.toSet());
        } else {
            // 普通用户只能查看自己及下级单位的数据
            orgIds = new HashSet<>(userOrgUtils.getCurrentUserAndSubOrgIds());
        }
        return orgIds;
    }

    /**
     * 构建组织ID与名称的映射关系
     * 查询所有警务组织信息并构建ID到名称的映射Map
     *
     * @return 组织ID与名称的映射Map
     */
    private Map<String, String> buildOrgNameMap() {
        List<BasePoliceOrg> allOrg = basePoliceOrgMapper.selectList(null);
        return allOrg.stream().collect(Collectors.toMap(BasePoliceOrg::getId, BasePoliceOrg::getName));
    }

    /**
     * DTO 到 Entity 的映射
     */
    private void mapDTOToEntity(StatisticsCompreSaveDTO dto, StatisticsCompre entity) {
        entity.setPoliceOrgId(dto.getPoliceOrgId());
        entity.setStatisticsTime(dto.getStatisticsTime());
        entity.setKeyPersonWarning(dto.getKeyPersonWarning());
        entity.setPassengerFlowWarning(dto.getPassengerFlowWarning());
        entity.setSecurityCheckWarning(dto.getSecurityCheckWarning());
        entity.setPolice(dto.getPoliceCount());
        entity.setAuxiliaryPolice(dto.getAuxiliaryPolice());
        entity.setPreventionTreatment(dto.getPreventionTreatment());
        entity.setPoliceCommunication(dto.getPoliceCommunication());
        entity.setBlueLetter(dto.getBlueLetter());
        entity.setPda(dto.getPda());
        entity.setInvestigation(dto.getInvestigation());
        entity.setDrugRecord(dto.getDrugRecord());
        entity.setOtherViolations(dto.getOtherViolations());
        entity.setPeopleInquiries(dto.getPeopleInquiries());
        entity.setPickUp(dto.getPickUp());
        entity.setMassesOther(dto.getMassesOther());
        entity.setDistribution(dto.getDistribution());
        entity.setChecksNum(dto.getChecksNum());
        entity.setPoliceTempControl(dto.getPoliceTempControl());
        entity.setCourtTempControl(dto.getCourtTempControl());
        entity.setThreeEscapes(dto.getThreeEscapes());
        entity.setDrillTimes(dto.getDrillTimes());
        entity.setClueArrangement(dto.getClueArrangement());
        entity.setHandlingCases(dto.getHandlingCases());
        entity.setMobilization(dto.getMobilization());
        entity.setSupervision(dto.getSupervision());
        entity.setEpidemicCheck(dto.getEpidemicCheck());
        entity.setDissuade(dto.getDissuade());
        entity.setYellowCode(dto.getYellowCode());
        entity.setRedCode(dto.getRedCode());
        entity.setTemperatureCheck(dto.getTemperatureCheck());
        entity.setAbnormal(dto.getAbnormal());
        entity.setStationDisinfection(dto.getStationDisinfection());
        entity.setTrainDisinfection(dto.getTrainDisinfection());
        entity.setVolunteers(dto.getVolunteers());
        entity.setArrival(dto.getArrival());
        entity.setDeparture(dto.getDeparture());
        entity.setTransfer(dto.getTransfer());
        entity.setSecurityCheck(dto.getSecurityCheck());
        entity.setContraband(dto.getContraband());
        entity.setRemark(dto.getRemark());
        entity.setTicketWarningCapture(dto.getTicketWarningCapture());
        entity.setTicketWarningDistribution(dto.getTicketWarningDistribution());
        entity.setTicketWarningChecks(dto.getTicketWarningChecks());
        entity.setCaptureNum(dto.getCaptureNum());
    }

    /**
     * Entity 到 DTO 的映射
     */
    private void mapEntityToDTO(StatisticsCompre entity, StatisticsCompreSaveDTO dto) {
        dto.setId(entity.getId());
        dto.setPoliceOrgId(entity.getPoliceOrgId());
        dto.setStatisticsTime(entity.getStatisticsTime());
        dto.setKeyPersonWarning(entity.getKeyPersonWarning());
        dto.setPassengerFlowWarning(entity.getPassengerFlowWarning());
        dto.setSecurityCheckWarning(entity.getSecurityCheckWarning());
        dto.setPoliceCount(entity.getPolice());
        dto.setAuxiliaryPolice(entity.getAuxiliaryPolice());
        dto.setPreventionTreatment(entity.getPreventionTreatment());
        dto.setPoliceCommunication(entity.getPoliceCommunication());
        dto.setBlueLetter(entity.getBlueLetter());
        dto.setPda(entity.getPda());
        dto.setInvestigation(entity.getInvestigation());
        dto.setDrugRecord(entity.getDrugRecord());
        dto.setOtherViolations(entity.getOtherViolations());
        dto.setPeopleInquiries(entity.getPeopleInquiries());
        dto.setPickUp(entity.getPickUp());
        dto.setMassesOther(entity.getMassesOther());
        dto.setDistribution(entity.getDistribution());
        dto.setChecksNum(entity.getChecksNum());
        dto.setPoliceTempControl(entity.getPoliceTempControl());
        dto.setCourtTempControl(entity.getCourtTempControl());
        dto.setThreeEscapes(entity.getThreeEscapes());
        dto.setDrillTimes(entity.getDrillTimes());
        dto.setClueArrangement(entity.getClueArrangement());
        dto.setHandlingCases(entity.getHandlingCases());
        dto.setMobilization(entity.getMobilization());
        dto.setSupervision(entity.getSupervision());
        dto.setEpidemicCheck(entity.getEpidemicCheck());
        dto.setDissuade(entity.getDissuade());
        dto.setYellowCode(entity.getYellowCode());
        dto.setRedCode(entity.getRedCode());
        dto.setTemperatureCheck(entity.getTemperatureCheck());
        dto.setAbnormal(entity.getAbnormal());
        dto.setStationDisinfection(entity.getStationDisinfection());
        dto.setTrainDisinfection(entity.getTrainDisinfection());
        dto.setVolunteers(entity.getVolunteers());
        dto.setArrival(entity.getArrival());
        dto.setDeparture(entity.getDeparture());
        dto.setTransfer(entity.getTransfer());
        dto.setSecurityCheck(entity.getSecurityCheck());
        dto.setContraband(entity.getContraband());
        dto.setRemark(entity.getRemark());
        dto.setCaptureNum(entity.getCaptureNum());
        dto.setTicketWarningCapture(entity.getTicketWarningCapture());
        dto.setTicketWarningChecks(entity.getTicketWarningChecks());
        dto.setTicketWarningDistribution(entity.getTicketWarningDistribution());
    }

    /**
     * 将日报Entity字段映射到VO
     */
    private void mapReportToVO(StatisticsDailyReport report, DailyReportDetailVO vo) {
        vo.setFormTime(report.getFormTime());
        vo.setPoliceOrgId(report.getPoliceOrgId());
        vo.setResponsibleName(report.getResponsibleName());
        vo.setResponsiblePosition(report.getResponsiblePosition());
        vo.setResponsiblePhone(report.getResponsiblePhone());
        vo.setResponsibleChannel(report.getResponsibleChannel());
        vo.setStrengthPolice(report.getStrengthPolice());
        vo.setStrengthAuxiliaryPolice(report.getStrengthAuxiliaryPolice());
        vo.setStrengthArmedPolice(report.getStrengthArmedPolice());
        vo.setStrengthOther(report.getStrengthOther());
        vo.setEquipmentGun(report.getEquipmentGun());
        vo.setEquipmentGunMsg(report.getEquipmentGunMsg());
        vo.setEquipmentGpsMsg(report.getEquipmentGpsMsg());
        vo.setEquipmentImgMsg(report.getEquipmentImgMsg());
        vo.setVehicleMotorbike(report.getVehicleMotorbike());
        vo.setVehicleElectricCar(report.getVehicleElectricCar());
        vo.setPatrolVehicle(report.getPatrolVehicle());
        vo.setPatrolPoliceCommunication(report.getPatrolPoliceCommunication());
        vo.setPatrolPda(report.getPatrolPda());
        vo.setPatrolQuestionVehicle(report.getPatrolQuestionVehicle());
        vo.setPatrolArrestEmp(report.getPatrolArrestEmp());
        vo.setImportantPart(report.getImportantPart());
        vo.setImportantPolice(report.getImportantPolice());
        vo.setImportantAuxiliaryPolice(report.getImportantAuxiliaryPolice());
        vo.setImportantArmedPolice(report.getImportantArmedPolice());
        vo.setImportantVehicle(report.getImportantVehicle());
        vo.setImportantWeapon(report.getImportantWeapon());
        vo.setMemos(report.getMemos());
    }

    /**
     * 将日报DTO字段映射到Entity
     */
    private void mapDTOToReport(DailyReportEditReqDTO dto, StatisticsDailyReport report) {
        report.setFormTime(dto.getFormTime());
        report.setPoliceOrgId(dto.getPoliceOrgId());
        report.setResponsibleName(dto.getResponsibleName());
        report.setResponsiblePosition(dto.getResponsiblePosition());
        report.setResponsiblePhone(dto.getResponsiblePhone());
        report.setResponsibleChannel(dto.getResponsibleChannel());
        report.setStrengthPolice(dto.getStrengthPolice());
        report.setStrengthAuxiliaryPolice(dto.getStrengthAuxiliaryPolice());
        report.setStrengthArmedPolice(dto.getStrengthArmedPolice());
        report.setStrengthOther(dto.getStrengthOther());
        report.setEquipmentGun(dto.getEquipmentGun());
        report.setEquipmentGunMsg(dto.getEquipmentGunMsg());
        report.setEquipmentGpsMsg(dto.getEquipmentGpsMsg());
        report.setEquipmentImgMsg(dto.getEquipmentImgMsg());
        report.setVehicleMotorbike(dto.getVehicleMotorbike());
        report.setVehicleElectricCar(dto.getVehicleElectricCar());
        report.setPatrolVehicle(dto.getPatrolVehicle());
        report.setPatrolPoliceCommunication(dto.getPatrolPoliceCommunication());
        report.setPatrolPda(dto.getPatrolPda());
        report.setPatrolQuestionVehicle(dto.getPatrolQuestionVehicle());
        report.setPatrolArrestEmp(dto.getPatrolArrestEmp());
        report.setImportantPart(dto.getImportantPart());
        report.setImportantPolice(dto.getImportantPolice());
        report.setImportantAuxiliaryPolice(dto.getImportantAuxiliaryPolice());
        report.setImportantArmedPolice(dto.getImportantArmedPolice());
        report.setImportantVehicle(dto.getImportantVehicle());
        report.setImportantWeapon(dto.getImportantWeapon());
        report.setMemos(dto.getMemos());
    }

    /**
     * 将StatisticsDailyPatrol实体映射到PatrolPageVO
     *
     * @param entity StatisticsDailyPatrol实体
     * @param vo     PatrolPageVO对象
     */
    private void mapPatrolEntityToPageVO(StatisticsDailyPatrol entity, PatrolPageVO vo) {
        vo.setId(entity.getId());
        vo.setPoliceOrgId(entity.getPoliceOrgId());
        vo.setFormTime(entity.getFormTime());
        vo.setCall(entity.getCall());
        vo.setVehicle(entity.getVehicle());
        vo.setArea(entity.getArea());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveOrUpdatePersonnelCaptured(PersonnelCapturedSaveDTO dto) {
        try {
            PersonnelCaptured entity;
            boolean isUpdate = false;

            if (dto.getId() != null && !dto.getId().trim().isEmpty()) {
                // 更新模式：检查记录是否存在
                entity = personnelCapturedMapper.selectById(dto.getId());
                if (entity == null || !Boolean.TRUE.equals(entity.getAvailable())) {
                    throw new BusinessException("要编辑的抓获记录不存在");
                }
                isUpdate = true;
            } else {
                // 新增模式：创建新实体
                entity = new PersonnelCaptured();
                entity.setAvailable(true);
                entity.setCreateTime(LocalDateTime.now());
                entity.setCreateUserId(SecurityUtils.getLoginUserId());
            }

            // 映射DTO到实体
            mapPersonnelCapturedDTOToEntity(dto, entity);

            if (isUpdate) {
                entity.setUpdateTime(LocalDateTime.now());
                entity.setUpdateUserId(SecurityUtils.getLoginUserId());
                personnelCapturedMapper.updateById(entity);
            } else {
                personnelCapturedMapper.insert(entity);
            }

            return true;
        } catch (BusinessException e) {
            log.error("保存或更新抓获记录失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("保存或更新抓获记录时发生异常", e);
            throw new BusinessException("保存或更新抓获记录失败");
        }
    }

    @Override
    public PersonnelCapturedSaveDTO getPersonnelCapturedDetail(String id) {
        try {
            if (id == null || id.trim().isEmpty()) {
                throw new BusinessException("抓获记录ID不能为空");
            }

            PersonnelCaptured entity = personnelCapturedMapper.selectById(id);
            if (entity == null || !Boolean.TRUE.equals(entity.getAvailable())) {
                throw new BusinessException("抓获记录不存在");
            }

            PersonnelCapturedSaveDTO dto = new PersonnelCapturedSaveDTO();
            mapPersonnelCapturedEntityToDTO(entity, dto);
            return dto;
        } catch (BusinessException e) {
            log.error("获取抓获记录详情失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("获取抓获记录详情时发生异常", e);
            throw new BusinessException("获取抓获记录详情失败");
        }
    }

    /**
     * PersonnelCapturedSaveDTO 到 PersonnelCaptured 的映射
     */
    private void mapPersonnelCapturedDTOToEntity(PersonnelCapturedSaveDTO dto, PersonnelCaptured entity) {
        entity.setName(dto.getName());
        entity.setIdNo(dto.getIdNo());
        entity.setPhone(dto.getPhone());
        entity.setPoliceOrgId(dto.getPoliceOrgId());
        entity.setWarningSources(dto.getWarningSources());
        entity.setCaptureType(dto.getCaptureType());
        entity.setCaptureSite(dto.getCaptureSite());
        entity.setCaptureDate(dto.getCaptureDate());
        entity.setProcessResult(dto.getProcessResult());
        entity.setProcessResultDetail(dto.getProcessResultDetail());
        entity.setTurnOverOrg(dto.getTurnOverOrg());
        entity.setPostProcessResult(dto.getPostProcessResult());
        entity.setRemark(dto.getRemark());
        entity.setCoOrganizer(dto.getCoOrganizer());

        // 处理图片URL字符串
        if (dto.getImageUrls() != null && !dto.getImageUrls().trim().isEmpty()) {
            entity.setImageResource(dto.getImageUrls());
        } else {
            entity.setImageResource(null);
        }

        // 设置抓获单位名称
        if (dto.getPoliceOrgId() != null) {
            BasePoliceOrg policeOrg = basePoliceOrgMapper.selectById(dto.getPoliceOrgId());
            if (policeOrg != null) {
                entity.setPoliceOrgName(policeOrg.getName());
            } else {
                dto.setImageUrls(null);
            }
        }
    }

    /**
     * PersonnelCaptured 到 PersonnelCapturedSaveDTO 的映射
     */
    private void mapPersonnelCapturedEntityToDTO(PersonnelCaptured entity, PersonnelCapturedSaveDTO dto) {
        dto.setId(entity.getId());
        dto.setName(entity.getName());
        dto.setIdNo(entity.getIdNo());
        dto.setPhone(entity.getPhone());
        dto.setPoliceOrgId(entity.getPoliceOrgId());
        dto.setWarningSources(entity.getWarningSources());
        dto.setCaptureType(entity.getCaptureType());
        dto.setCaptureSite(entity.getCaptureSite());
        dto.setCaptureDate(entity.getCaptureDate());
        dto.setProcessResult(entity.getProcessResult());
        dto.setProcessResultDetail(entity.getProcessResultDetail());
        dto.setTurnOverOrg(entity.getTurnOverOrg());
        dto.setPostProcessResult(entity.getPostProcessResult());
        dto.setRemark(entity.getRemark());
        dto.setCoOrganizer(entity.getCoOrganizer());
        dto.setImageUrls(entity.getImageResource());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveOrUpdatePersonnelAttention(PersonnelAttentionSaveDTO dto) {
        try {
            PersonnelAttention entity;
            boolean isUpdate = false;

            if (dto.getId() != null && !dto.getId().trim().isEmpty()) {
                // 更新模式：检查记录是否存在
                entity = personnelAttentionMapper.selectById(dto.getId());
                if (entity == null || !Boolean.TRUE.equals(entity.getAvailable())) {
                    throw new BusinessException("要编辑的关注记录不存在");
                }
                isUpdate = true;
            } else {
                // 新增模式：创建新实体
                entity = new PersonnelAttention();
                entity.setAvailable(true);
                entity.setCreateTime(LocalDateTime.now());
                entity.setCreateUserId(SecurityUtils.getLoginUserId());
            }

            // 映射DTO到实体
            mapPersonnelAttentionDTOToEntity(dto, entity);

            if (isUpdate) {
                entity.setUpdateTime(LocalDateTime.now());
                entity.setUpdateUserId(SecurityUtils.getLoginUserId());
                personnelAttentionMapper.updateById(entity);
            } else {
                personnelAttentionMapper.insert(entity);
            }

            return true;
        } catch (BusinessException e) {
            log.error("保存或更新关注记录失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("保存或更新关注记录时发生异常", e);
            throw new BusinessException("保存或更新关注记录失败");
        }
    }

    @Override
    public PersonnelAttentionSaveDTO getPersonnelAttentionDetail(String id) {
        try {
            if (id == null || id.trim().isEmpty()) {
                throw new BusinessException("关注记录ID不能为空");
            }

            PersonnelAttention entity = personnelAttentionMapper.selectById(id);
            if (entity == null || !Boolean.TRUE.equals(entity.getAvailable())) {
                throw new BusinessException("关注记录不存在");
            }

            PersonnelAttentionSaveDTO dto = new PersonnelAttentionSaveDTO();
            mapPersonnelAttentionEntityToDTO(entity, dto);
            return dto;
        } catch (BusinessException e) {
            log.error("获取关注记录详情失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("获取关注记录详情时发生异常", e);
            throw new BusinessException("获取关注记录详情失败");
        }
    }

    /**
     * PersonnelAttentionSaveDTO 到 PersonnelAttention 的映射
     */
    private void mapPersonnelAttentionDTOToEntity(PersonnelAttentionSaveDTO dto, PersonnelAttention entity) {
        entity.setDisposeTime(dto.getDisposeTime());
        entity.setWarningSources(dto.getWarningSources());
        entity.setPersonnelType(dto.getPersonnelType());
        entity.setPoliceOrgId(dto.getPoliceOrgId());
        entity.setAttentionNum(dto.getAttentionNum());
        entity.setInStationCode(dto.getInStationCode());
        entity.setInStationName(dto.getInStationName());
        entity.setOutStationCode(dto.getOutStationCode());
        entity.setOutStationName(dto.getOutStationName());
        entity.setHandoverPoliceOrgId(dto.getHandoverPoliceOrgId());
        entity.setHandoverPoliceOrgName(dto.getHandoverPoliceOrgName());
        entity.setHandoverStationCode(dto.getHandoverStationCode());
        entity.setHandoverStationName(dto.getHandoverStationName());
        entity.setHandoverTime(dto.getHandoverTime());
        entity.setRemark(dto.getRemark());
        entity.setAvailable(true);

        // 处理图片URL字符串
        if (dto.getImageUrls() != null && !dto.getImageUrls().trim().isEmpty()) {
            entity.setImageResource(dto.getImageUrls());
        } else {
            entity.setImageResource(null);
        }

        // 设置处置单位名称
        entity.setPoliceOrgName(dto.getPoliceOrgName());

        entity.setAttentionType(dto.getAttentionType());

        // 通过站点编码查询线路信息
        entity.setInSubwayId(getSubwayIdByStationCode(dto.getInStationCode()));
        entity.setOutSubwayId(getSubwayIdByStationCode(dto.getOutStationCode()));
        entity.setHandoverSubwayId(getSubwayIdByStationCode(dto.getHandoverStationCode()));
    }

    /**
     * 通过站点编码查询线路ID
     *
     * @param stationCode 站点编码
     * @return 线路ID，如果查询不到则返回null
     */
    private String getSubwayIdByStationCode(String stationCode) {
        if (stationCode == null || stationCode.trim().isEmpty()) {
            return null;
        }

        try {
            LambdaQueryWrapper<BaseSubwayStation> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(BaseSubwayStation::getStationCode, stationCode)
                    .eq(BaseSubwayStation::getAvailable, true)
                    .last("LIMIT 1");

            BaseSubwayStation subwayStation = baseSubwayStationMapper.selectOne(wrapper);
            return subwayStation != null ? subwayStation.getSubwayId() : null;
        } catch (Exception e) {
            log.warn("查询站点编码{}对应的线路ID失败: {}", stationCode, e.getMessage());
            return null;
        }
    }

    /**
     * PersonnelAttention 到 PersonnelAttentionSaveDTO 的映射
     */
    private void mapPersonnelAttentionEntityToDTO(PersonnelAttention entity, PersonnelAttentionSaveDTO dto) {
        dto.setId(entity.getId());
        dto.setDisposeTime(entity.getDisposeTime());
        dto.setWarningSources(entity.getWarningSources());
        dto.setPersonnelType(entity.getPersonnelType());
        dto.setPoliceOrgId(entity.getPoliceOrgId());
        dto.setPoliceOrgName(entity.getPoliceOrgName());
        dto.setAttentionType(entity.getAttentionType());
        dto.setAttentionNum(entity.getAttentionNum());
        dto.setInStationCode(entity.getInStationCode());
        dto.setInStationName(entity.getInStationName());
        dto.setOutStationCode(entity.getOutStationCode());
        dto.setOutStationName(entity.getOutStationName());
        dto.setHandoverPoliceOrgId(entity.getHandoverPoliceOrgId());
        dto.setHandoverPoliceOrgName(entity.getHandoverPoliceOrgName());
        dto.setHandoverStationCode(entity.getHandoverStationCode());
        dto.setHandoverStationName(entity.getHandoverStationName());
        dto.setHandoverTime(entity.getHandoverTime());
        dto.setRemark(entity.getRemark());

        // 处理图片字符串
        if (entity.getImageResource() != null && !entity.getImageResource().trim().isEmpty()) {
            dto.setImageUrls(entity.getImageResource());
        } else {
            dto.setImageUrls(null);
        }
    }

    /**
     * 查询所有站点信息
     * 返回站点编号和站点名称
     *
     * @return 站点信息列表
     */
    @Override
    public List<StationInfoVO> getAllStations() {
        try {
            // 查询所有有效的站点信息
            LambdaQueryWrapper<BaseStation> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(BaseStation::getAvailable, true)
                    .orderByAsc(BaseStation::getStationCode);

            List<BaseStation> stations = baseStationMapper.selectList(wrapper);

            // 转换为VO对象
            List<StationInfoVO> stationInfoList = stations.stream()
                    .map(station -> {
                        StationInfoVO vo = new StationInfoVO();
                        vo.setStationCode(station.getStationCode());
                        vo.setStationName(station.getName());
                        return vo;
                    })
                    .collect(Collectors.toList());

            log.info("查询所有站点信息成功，共{}个站点", stationInfoList.size());
            return stationInfoList;

        } catch (Exception e) {
            log.error("查询所有站点信息失败", e);
            throw new BusinessException("查询站点信息失败");
        }
    }
}
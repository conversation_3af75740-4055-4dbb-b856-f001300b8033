package cn.com.sipsg.module.business.module.supervision.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * 站点值班民警数量列表视图对象
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Data
@Schema(description = "站点值班民警数量列表视图对象")
public class StationPoliceCountListVO {

    @Schema(description = "统计日期")
    private LocalDate date;

    @Schema(description = "值班民警总数")
    private Integer totalCount;

    @Schema(description = "各站点值班民警数量列表")
    private List<StationPoliceCountVO> stationList;
}

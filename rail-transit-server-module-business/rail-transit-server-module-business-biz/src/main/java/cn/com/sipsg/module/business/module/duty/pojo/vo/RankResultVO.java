package cn.com.sipsg.module.business.module.duty.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "通用排名统计返回体（单一数量）", example = "{\"code\":\"1001\",\"name\":\"南京南站\",\"count\":15}")
public class RankResultVO {
    @Schema(description = "编号", example = "1001")
    private String code;
    @Schema(description = "名称", example = "南京南站")
    private String name;
    @Schema(description = "数量", example = "15")
    private Long count;
}
package cn.com.sipsg.module.business.module.supervision.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "考试任务详情请求DTO")
public class ExamTaskDetailReqDTO {
    @Schema(description = "考试任务ID", required = true)
    private String taskId;
    @Schema(description = "页码", example = "1")
    private Integer pageNum = 1;
    @Schema(description = "每页数量", example = "10")
    private Integer pageSize = 10;
    @Schema(description = "考试状态", example = "00")
    private String status;
    @Schema(description = "姓名模糊搜索", example = "张三")
    private String keyword;
}
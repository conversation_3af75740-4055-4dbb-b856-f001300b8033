package cn.com.sipsg.module.business.module.duty.pojo.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> lixt
 * @date : 2025-04-30 17:10:16
 * @Description: 标签分组VO
 */
@Data
@Schema(description = "标签分组VO")
public class TagGroupVO {
    @Schema(description = "分组ID")
    private String id;

    @Schema(description = "标签分组名称")
    private String groupName;

    @Schema(description = "标签分组数量")
    private String groupTagCount;

    @Schema(description = "分组内标签信息")
    private List<TagVO> baseTag;
}

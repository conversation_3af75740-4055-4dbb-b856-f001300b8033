package cn.com.sipsg.module.business.module.supervision.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;

/**
 * 综合统计表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("tbl_statistics_compre")
public class StatisticsCompre {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id; // 主键id

    @TableField("create_user_id")
    private String createUserId; // 创建人id

    @TableField("create_time")
    private LocalDateTime createTime; // 创建时间

    @TableField("update_user_id")
    private String updateUserId; // 修改人id

    @TableField("update_time")
    private LocalDateTime updateTime; // 修改时间

    @TableField("available")
    private Boolean available; // 是否有效

    @TableField("police_org_id")
    private String policeOrgId; // 单位ID

    @TableField("key_person_warning")
    private Integer keyPersonWarning; // 重点人员预警次数

    @TableField("passenger_flow_warning")
    private Integer passengerFlowWarning; // 客流预警次数

    @TableField("security_check_warning")
    private Integer securityCheckWarning; // 安检违禁品预警数

    @TableField("police")
    private Integer police; // 民警数

    @TableField("auxiliary_police")
    private Integer auxiliaryPolice; // 辅警数

    @TableField("prevention_treatment")
    private Integer preventionTreatment; // 群防群治

    @TableField("police_communication")
    private Integer policeCommunication; // 警务通

    @TableField("blue_letter")
    private Integer blueLetter; // 蓝信

    @TableField("pda")
    private Integer pda; // PDA

    @TableField("investigation")
    private Integer investigation; // 盘查抓获

    @TableField("drug_record")
    private Integer drugRecord; // 吸毒前科

    @TableField("other_violations")
    private Integer otherViolations; // 其他违法

    @TableField("people_inquiries")
    private Integer peopleInquiries; // 接受群众问询

    @TableField("pick_up")
    private Integer pickUp; // 捡拾物品（个）

    @TableField("masses_other")
    private Integer massesOther; // 群众服务 - 其他

    @TableField("distribution")
    private Integer distribution; // 101情况 - 下发数

    @TableField("checks_num")
    private Integer checksNum; // 101情况 - 核查数

    @TableField("capture_num")
    private Integer captureNum; // 101情况 - 抓获数

    @TableField("ticket_warning_distribution")
    private Integer ticketWarningDistribution; // 票卡预警情况 - 下发数

    @TableField("ticket_warning_checks")
    private Integer ticketWarningChecks; // 票卡预警情况 - 核查数

    @TableField("ticket_warning_capture")
    private Integer ticketWarningCapture; // 票卡预警情况 - 抓获数

    @TableField("police_temp_control")
    private Integer policeTempControl; // 公安临控

    @TableField("court_temp_control")
    private Integer courtTempControl; // 法院临控

    @TableField("three_escapes")
    private Integer threeEscapes; // 三逃

    @TableField("drill_times")
    private Integer drillTimes; // 演练次数

    @TableField("clue_arrangement")
    private String clueArrangement; // 扫黑除恶 - 线索排摸

    @TableField("handling_cases")
    private String handlingCases; // 扫黑除恶 - 案件侦办

    @TableField("mobilization")
    private String mobilization; // 扫黑除恶 - 宣传动员

    @TableField("supervision")
    private String supervision; // 扫黑除恶 - 检查督导

    @TableField("inspections_sum")
    private Integer inspectionsSum; // 核查总数

    @TableField("epidemic_check")
    private Integer epidemicCheck; // 疫区检查数

    @TableField("dissuade")
    private Integer dissuade; // 劝离

    @TableField("yellow_code")
    private Integer yellowCode; // 黄码

    @TableField("red_code")
    private Integer redCode; // 红码

    @TableField("temperature_check")
    private Integer temperatureCheck; // 体测人数

    @TableField("abnormal")
    private Integer abnormal; // 异常人数

    @TableField("station_disinfection")
    private Integer stationDisinfection; // 站点消毒杀菌情况

    @TableField("train_disinfection")
    private Integer trainDisinfection; // 列车消毒杀菌情况

    @TableField("volunteers")
    private Integer volunteers; // 志愿者数

    @TableField("remark")
    private String remark; // 备注

    @TableField("statistics_time")
    private LocalDateTime statisticsTime; // 统计日期

    @TableField("arrival")
    private Integer arrival; // 进站客流

    @TableField("departure")
    private Integer departure; // 出站客流

    @TableField("transfer")
    private Integer transfer; // 换乘客流

    @TableField("security_check")
    private Integer securityCheck; // 安检包裹数

    @TableField("contraband")
    private Integer contraband; // 违禁品数
}
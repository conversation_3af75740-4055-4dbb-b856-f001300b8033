package cn.com.sipsg.module.business.module.supervision.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> lixt
 * @date : 2025-05-21 14:27:14
 * @Description: 考试人员考试批次表
 */
@Data
@TableName("tbl_exam_task_emp_batch")
public class ExamTaskEmpBatch {

    @TableId(type = IdType.ASSIGN_ID)
    private String id;  // 主键ID

    @TableField("create_user_id")
    private String createUserId;  // 创建人

    @TableField("create_time")
    private LocalDateTime createTime;  // 创建时间

    @TableField("update_user_id")
    private String updateUserId;  // 更新人

    @TableField("update_time")
    private LocalDateTime updateTime;  // 更新时间

    @TableField("available")
    private Boolean available;  // 有效标识

    @TableField("task_id")
    private String taskId;  // 考试任务ID

    @TableField("emp_id")
    private String empId;  // 人员ID

    @TableField("start_time")
    private LocalDateTime startTime;  // 考试实际开始时间

    @TableField("end_time")
    private LocalDateTime endTime;  // 考试实际结束时间

    @TableField("score")
    private Integer score;  // 考试分数

    @TableField("status")
    private String status;  // 考试状态，01：进行中，02：确认考试成绩，03：已完成

    @TableField("pass_exam")
    private Boolean passExam;  // 本次考试是否合格
}
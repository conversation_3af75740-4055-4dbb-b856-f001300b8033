package cn.com.sipsg.module.business.module.supervision.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> lixt
 * @date : 2025-05-21 14:24:32
 * @Description: 考试管理-题目表
 */
@Data
@TableName("tbl_exam_question")
public class ExamQuestion {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;  // 主键ID

    @TableField("create_user_id")
    private String createUserId;  // 创建人（警员ID）

    @TableField("create_time")
    private LocalDateTime createTime;  // 创建时间

    @TableField("update_user_id")
    private String updateUserId;  // 更新人（警员ID）

    @TableField("update_time")
    private LocalDateTime updateTime;  // 更新时间

    @TableField("available")
    private Boolean available;  // 有效标识

    @TableField("question_bank_id")
    private String questionBankId;  // 题库ID

    @TableField("question_type")
    private String questionType;  // 题型

    @TableField("question_stem")
    private String questionStem;  // 题干

    @TableField("question_answer")
    private String questionAnswer;  // 答案

    @TableField("question_option")
    private String questionOption;  // 题目选项（JSON字符串）

    @TableField("serial")
    private Integer serial;  // 题序
}

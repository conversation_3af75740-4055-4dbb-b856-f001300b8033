package cn.com.sipsg.module.business.module.duty.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> lixt
 * @date : 2025-01-15
 * @Description: 警员管辖站点关联表
 */
@Data
@TableName("tbl_base_police_emp_station")
public class BasePoliceEmpStation {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    
    /**
     * 警员ID
     */
    private String empId;
    
    /**
     * 站点ID
     */
    private String stationId;
    
    /**
     * 是否可用
     */
    private Boolean available;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 创建用户ID
     */
    private String createUserId;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 更新用户ID
     */
    private String updateUserId;
}
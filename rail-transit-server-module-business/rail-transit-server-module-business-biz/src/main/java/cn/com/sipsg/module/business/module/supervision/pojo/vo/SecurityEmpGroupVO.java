package cn.com.sipsg.module.business.module.supervision.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 安检人员按单位分组统计VO
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "安检人员按单位分组统计VO")
public class SecurityEmpGroupVO {

    @Schema(description = "安检人员总数")
    private Integer totalCount;

    @Schema(description = "按单位分组统计列表")
    private List<SecurityEmpGroupItem> groups;

    /**
     * 安检人员单位分组项
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "安检人员单位分组项")
    public static class SecurityEmpGroupItem {

        @Schema(description = "单位名称")
        private String orgName;

        @Schema(description = "安检人员数量")
        private Integer count;

        @Schema(description = "占比（百分比）")
        private BigDecimal percentage;
    }
}

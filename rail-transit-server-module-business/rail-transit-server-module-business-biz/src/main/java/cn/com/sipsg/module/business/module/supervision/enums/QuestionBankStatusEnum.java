package cn.com.sipsg.module.business.module.supervision.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 题库状态枚举
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Getter
@AllArgsConstructor
public enum QuestionBankStatusEnum {
    
    ENABLED("01", "启用"),
    DISABLED("02", "禁用"),
    UNKNOWN("", "未知");
    
    /**
     * 状态码
     */
    private final String code;
    
    /**
     * 状态描述
     */
    private final String desc;
    
    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return 枚举实例
     */
    public static QuestionBankStatusEnum getByCode(String code) {
        if (code == null || code.isEmpty()) {
            return UNKNOWN;
        }
        
        for (QuestionBankStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        
        return UNKNOWN;
    }
    
    /**
     * 根据状态描述获取枚举
     *
     * @param desc 状态描述
     * @return 枚举实例
     */
    public static QuestionBankStatusEnum getByDesc(String desc) {
        if (desc == null || desc.isEmpty()) {
            return UNKNOWN;
        }
        
        for (QuestionBankStatusEnum status : values()) {
            if (status.getDesc().equals(desc)) {
                return status;
            }
        }
        
        return UNKNOWN;
    }
}
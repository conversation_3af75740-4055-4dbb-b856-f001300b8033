package cn.com.sipsg.module.business.module.supervision.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 预警来源枚举
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
@Getter
@AllArgsConstructor
public enum WarningSourceEnum {
    ONE_ZERO_ONE_WARNING("1", "101预警"),
    TICKET_CARD_WARNING("2", "票卡预警"),
    INTERROGATION("3", "盘查"),
    OTHER("4", "其他");

    private final String code;
    private final String name;

    public static WarningSourceEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (WarningSourceEnum status : values()) {
            if (code.equals(status.getCode())) {
                return status;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        WarningSourceEnum status = getByCode(code);
        return status != null ? status.getName() : "未知状态";
    }
}

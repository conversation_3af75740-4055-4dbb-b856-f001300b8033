package cn.com.sipsg.module.business.module.supervision.service;

import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.TracePageReqDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.StatisticsTracePageVO;

public interface StatisticsTraceService {
    /**
     * 查询当前用户所在单位及下级单位的跟防情况分页列表（统一方法）
     * @param  dto 查询参数
     * @return 跟防情况分页数据
     */
    CommonPageVO<StatisticsTracePageVO> pageTraceForCurrentUser(TracePageReqDTO dto);

    /**
     * 删除跟防情况
     *
     * @param id 跟防情况ID
     * @return 操作结果
     */
    Boolean deleteTrace(String id);
}
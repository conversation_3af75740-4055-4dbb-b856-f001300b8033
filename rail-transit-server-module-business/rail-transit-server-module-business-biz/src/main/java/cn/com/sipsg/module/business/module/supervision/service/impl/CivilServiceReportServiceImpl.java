package cn.com.sipsg.module.business.module.supervision.service.impl;

import cn.com.sipsg.common.exception.BusinessException;
import cn.com.sipsg.common.security.core.util.SecurityUtils;
import cn.com.sipsg.module.business.module.common.util.UserOrgUtils;
import cn.com.sipsg.module.business.module.duty.entity.BasePoliceEmp;
import cn.com.sipsg.module.business.module.supervision.entity.CivilServiceReport;
import cn.com.sipsg.module.business.module.supervision.enums.AuditStatusEnum;
import cn.com.sipsg.module.business.module.supervision.mapper.CivilServiceReportMapper;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.CivilServiceReportDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.CivilServiceReportPageDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.CivilServiceReportPageResultDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.CivilServiceStatisticsDTO;
import cn.com.sipsg.module.business.module.supervision.service.CivilServiceReportService;
import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 服务群众上报表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CivilServiceReportServiceImpl extends ServiceImpl<CivilServiceReportMapper, CivilServiceReport> implements CivilServiceReportService {

    private final UserOrgUtils userOrgUtils;

    @Override
    public Boolean saveCivilServiceReport(CivilServiceReportDTO dto) {
        CivilServiceReport entity;
        BasePoliceEmp currentPoliceEmp = userOrgUtils.getCurrentUserPoliceEmp();
        String empId = currentPoliceEmp != null ? currentPoliceEmp.getId() : null;
        LocalDateTime now = LocalDateTime.now();

        if (StringUtils.isBlank(dto.getId())) {
            // 新增
            entity = new CivilServiceReport();
            BeanUtils.copyProperties(dto, entity);

            // 设置默认处理状态
            if (entity.getStatus() == null) {
                entity.setStatus(AuditStatusEnum.PENDING);
            }

            // 自动设置上报人信息（从当前用户获取）
            try {
                if (currentPoliceEmp != null) {
                    entity.setReporterId(currentPoliceEmp.getCode()); // 警员编号
                    entity.setReporterName(currentPoliceEmp.getName()); // 警员姓名
                }
            } catch (Exception e) {
                log.error("获取当前用户警员信息失败", e);
            }

            // 设置上报单位信息
            try {
                String currentOrgId = userOrgUtils.getCurrentUserOrgId();
                String currentOrgName = userOrgUtils.getCurrentUserOrgName();
                entity.setReportOrgId(currentOrgId);
                entity.setReportOrgName(currentOrgName);
            } catch (Exception e) {
                log.error("获取当前用户组织信息失败", e);
            }

            // 设置创建人和创建时间
            entity.setCreateUserId(empId);
            entity.setCreateTime(now);
            entity.setUpdateUserId(empId);
            entity.setUpdateTime(now);
            entity.setAvailable(true);

            return this.save(entity);
        } else {
            // 修改
            entity = this.getById(dto.getId());
            if (entity == null) {
                throw new IllegalArgumentException("服务群众上报不存在，ID: " + dto.getId());
            }

            // 复制属性，显式忽略关键字段
            BeanUtils.copyProperties(dto, entity, "reporterId", "reporterName", "createUserId", "createTime");

            // 设置更新人和更新时间
            entity.setUpdateUserId(empId);
            entity.setUpdateTime(now);

            return this.updateById(entity);
        }
    }

    @Override
    public IPage<CivilServiceReportPageResultDTO> getCivilServiceReportPage(CivilServiceReportPageDTO pageDTO) {
        // 构建分页对象
        Page<CivilServiceReport> page = new Page<>(pageDTO.getCurrent(), pageDTO.getSize());

        // 构建查询条件
        LambdaQueryWrapper<CivilServiceReport> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CivilServiceReport::getAvailable, true);

        // 标题名称模糊查询
        if (StringUtils.isNotBlank(pageDTO.getTitle())) {
            queryWrapper.like(CivilServiceReport::getTitle, pageDTO.getTitle());
        }

        // 处理状态精确查询
        if (StringUtils.isNotBlank(pageDTO.getStatus())) {
            validateStatus(pageDTO.getStatus());
            AuditStatusEnum statusEnum = AuditStatusEnum.valueOf(pageDTO.getStatus());
            queryWrapper.eq(CivilServiceReport::getStatus, statusEnum);
        }

        // 上报时间范围查询
        if (pageDTO.getReportStartTime() != null) {
            queryWrapper.ge(CivilServiceReport::getReportTime, pageDTO.getReportStartTime());
        }
        if (pageDTO.getReportEndTime() != null) {
            queryWrapper.le(CivilServiceReport::getReportTime, pageDTO.getReportEndTime());
        }

        // 数据权限控制：根据用户角色决定可查看的数据范围
        if (SecurityUtils.isSuperAdmin()) {
            // 超级管理员可以查看所有数据，不添加组织过滤条件
            log.debug("超级管理员查询服务群众上报，无组织限制");
        } else {
            // 普通用户只能查看当前单位及下级单位的数据
            try {
                List<String> orgIds = userOrgUtils.getCurrentUserAndSubOrgIds();
                if (orgIds != null && !orgIds.isEmpty()) {
                    queryWrapper.in(CivilServiceReport::getReportOrgId, orgIds);
                    log.debug("普通用户数据权限过滤，可访问组织ID: {}", orgIds);
                } else {
                    // 如果用户没有可访问的组织，返回空结果
                    queryWrapper.eq(CivilServiceReport::getId, -1);
                    log.warn("普通用户无可访问的组织，返回空结果");
                }
            } catch (Exception e) {
                log.error("获取用户组织权限失败", e);
                // 出现异常时，为安全起见，返回空结果
                queryWrapper.eq(CivilServiceReport::getId, -1);
            }
        }

        // 按上报时间倒序排序
        queryWrapper.orderByDesc(CivilServiceReport::getReportTime);

        // 执行分页查询
        IPage<CivilServiceReport> entityPage = this.page(page, queryWrapper);

        // 转换为DTO分页结果
        Page<CivilServiceReportPageResultDTO> dtoPage = new Page<>(entityPage.getCurrent(), entityPage.getSize(), entityPage.getTotal());
        List<CivilServiceReportPageResultDTO> dtoList = entityPage.getRecords().stream().map(entity -> {
            // 将实体转换为DTO
            CivilServiceReportPageResultDTO dto = new CivilServiceReportPageResultDTO();
            BeanUtils.copyProperties(entity, dto);

            return dto;
        }).collect(Collectors.toList());
        dtoPage.setRecords(dtoList);

        return dtoPage;
    }

    /**
     * 校验状态值合法性
     */
    private void validateStatus(String status) {
        if (StringUtils.isBlank(status)) {
            return;
        }
        
        try {
            AuditStatusEnum.valueOf(status);
        } catch (IllegalArgumentException e) {
            throw new BusinessException("状态值无效，请使用正确的状态值：PENDING, APPROVED, REJECTED");
        }
    }
    
    /**
     * 获取所有有效的状态值
     */
    public List<String> getValidStatusValues() {
        return Arrays.stream(AuditStatusEnum.values())
                .map(Enum::name)
                .collect(Collectors.toList());
    }

    @Override
    public CivilServiceReportDTO getCivilServiceReportDetail(String id) {
        CivilServiceReport entity = this.getById(id);
        if (entity == null) {
            return null;
        }

        CivilServiceReportDTO dto = new CivilServiceReportDTO();
        BeanUtils.copyProperties(entity, dto);

        // 上报人名称
        if (StringUtils.isNotBlank(entity.getCreateUserId())) {
            String createUserName = userOrgUtils.getUserNameByUserId(entity.getCreateUserId());
            dto.setReporterName(createUserName);
        }

        return dto;
    }

    @Override
    public Boolean batchDeleteCivilServiceReport(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return false;
        }

        BasePoliceEmp currentPoliceEmp = userOrgUtils.getCurrentUserPoliceEmp();
        String empId = currentPoliceEmp != null ? currentPoliceEmp.getId() : null;
        LocalDateTime now = LocalDateTime.now();

        List<CivilServiceReport> reports = this.listByIds(ids);
        if (CollectionUtils.isEmpty(reports)) {
            return false;
        }

        reports.forEach(report -> {
            report.setAvailable(false);
            report.setUpdateUserId(empId);
            report.setUpdateTime(now);
        });

        return this.updateBatchById(reports);
    }
    
    @Override
    public CivilServiceStatisticsDTO getWeeklyStatistics() {
        // 计算一周前的时间
        LocalDateTime oneWeekAgo = LocalDateTime.now().minus(7, ChronoUnit.DAYS);
        
        // 构建查询条件
        LambdaQueryWrapper<CivilServiceReport> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CivilServiceReport::getAvailable, true)
                   .ge(CivilServiceReport::getReportTime, oneWeekAgo);
        
        // 查询一周内的所有服务记录
        List<CivilServiceReport> weeklyReports = this.list(queryWrapper);
        
        // 创建统计结果对象
        CivilServiceStatisticsDTO statistics = new CivilServiceStatisticsDTO();
        
        if (weeklyReports.isEmpty()) {
            // 如果没有记录，设置默认值
            statistics.setServiceCount(0);
            statistics.setServiceObjectCount(0);
            statistics.setSatisfactionRate(0.0);
            return statistics;
        }
        
        // 1. 计算服务次数（即记录总数）
        int serviceCount = weeklyReports.size();
        statistics.setServiceCount(serviceCount);
        
        // 2. 计算服务对象数量（去重后的上报人数量）
        Set<String> uniqueReporters = weeklyReports.stream()
                .map(CivilServiceReport::getReporterId)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
        statistics.setServiceObjectCount(uniqueReporters.size());
        
        // 3. 计算满意度
        long satisfiedCount = weeklyReports.stream()
                .filter(report -> Boolean.TRUE.equals(report.getIsSatisfied()))
                .count();
        
        // 计算已评价的记录数
        long ratedCount = weeklyReports.stream()
                .filter(report -> report.getIsSatisfied() != null)
                .count();
        
        // 计算满意度百分比（如果没有评价，则为0）
        double satisfactionRate = ratedCount > 0 ? (satisfiedCount * 100.0 / ratedCount) : 0.0;
        statistics.setSatisfactionRate(Math.round(satisfactionRate * 10) / 10.0); // 保留一位小数
        
        return statistics;
    }
}
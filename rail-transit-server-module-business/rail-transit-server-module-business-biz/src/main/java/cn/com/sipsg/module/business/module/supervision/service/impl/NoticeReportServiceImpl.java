package cn.com.sipsg.module.business.module.supervision.service.impl;

import cn.com.sipsg.common.exception.BusinessException;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.security.core.util.SecurityUtils;
import cn.com.sipsg.module.business.module.common.util.UserOrgUtils;
import cn.com.sipsg.module.business.module.duty.entity.BasePoliceEmp;
import cn.com.sipsg.module.business.module.supervision.entity.NoticeReport;
import cn.com.sipsg.module.business.module.supervision.mapper.NoticeReportMapper;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.NoticeReportPageReqDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.NoticeReportSaveDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.NoticeReportIssueDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.NoticeReportVO;
import cn.com.sipsg.module.business.module.supervision.service.NoticeReportService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 通报信息 Service 实现类
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Service
@RequiredArgsConstructor
public class NoticeReportServiceImpl extends ServiceImpl<NoticeReportMapper, NoticeReport> implements NoticeReportService {

    private final UserOrgUtils userOrgUtils;

    /**
     * 保存或更新通知报告
     *
     * @param dto 通知报告保存数据传输对象，包含报告的相关信息
     * @return Boolean 保存或更新成功返回true，失败返回false
     */
    @Override
    public Boolean saveOrUpdateNoticeReport(NoticeReportSaveDTO dto) {
        NoticeReport noticeReport = new NoticeReport();
        String currentUserId = SecurityUtils.getLoginUserId();
        LocalDateTime now = LocalDateTime.now();

        // 设置基本字段
        noticeReport.setOrgId(dto.getOrgId());
        noticeReport.setOrgName(dto.getOrgName());
        noticeReport.setTitle(dto.getTitle());
        noticeReport.setNonComplianceContent(dto.getNonComplianceContent());
        noticeReport.setReceiveOrgId(dto.getReceiveOrgId());
        noticeReport.setReceiveOrgName(dto.getReceiveOrgName());
        noticeReport.setIssueUserId(dto.getIssueUserId());
        noticeReport.setIssueUserName(dto.getIssueUserName());
        noticeReport.setReceiveUserId(dto.getReceiveUserId());
        noticeReport.setReceiveUserName(dto.getReceiveUserName());

        // 处理签发时间
        if (StringUtils.hasText(dto.getLawTime())) {
            try {
                // 如果传入了签发时间，尝试解析
                noticeReport.setLawTime(LocalDateTime.parse(dto.getLawTime().replace(" ", "T")));
            } catch (Exception e) {
                // 解析失败则使用当前时间
                noticeReport.setLawTime(now);
            }
        } else {
            // 未传入签发时间则使用当前时间
            noticeReport.setLawTime(now);
        }

        if (StringUtil.isBlank(dto.getId())) {
            // 新增
            noticeReport.setCreateTime(now);
            noticeReport.setCreateUserId(currentUserId);
            noticeReport.setUpdateTime(now);
            noticeReport.setUpdateUserId(currentUserId);
            noticeReport.setAvailable(true);
            // 不再需要设置签收状态
            return save(noticeReport);
        } else {
            // 数据存在，执行更新操作
            noticeReport.setId(dto.getId()); // 确保设置id
            noticeReport.setUpdateTime(now);
            noticeReport.setUpdateUserId(currentUserId);
            return updateById(noticeReport);
        }
    }

    /**
     * 分页查询通知公告举报信息
     *
     * @param dto 查询条件封装对象，包含分页参数及查询条件：
     *            - current: 当前页码
     *            - size: 每页大小
     *            - title: 标题模糊查询关键字（可选）
     *            - orgId: 组织ID精确查询（可选）
     *            - startTime: 法律生效时间起始值（可选）
     *            - endTime: 法律生效时间结束值（可选）
     * @return 返回封装的分页结果对象 CommonPageVO<NoticeReportVO>，包含：
     * - records: 当前页数据列表
     * - total: 总记录数
     * - size: 每页大小
     * - current: 当前页码
     */
    @Override
    public CommonPageVO<NoticeReportVO> pageNoticeReport(NoticeReportPageReqDTO dto) {
        Page<NoticeReport> page = new Page<>(dto.getCurrent(), dto.getSize());
        LambdaQueryWrapper<NoticeReport> queryWrapper = new LambdaQueryWrapper<>();

        // 基础查询条件
        queryWrapper.like(StringUtils.hasText(dto.getTitle()), NoticeReport::getTitle, dto.getTitle())
                .eq(StringUtils.hasText(dto.getOrgId()), NoticeReport::getOrgId, dto.getOrgId())
                .ge(Objects.nonNull(dto.getStartTime()), NoticeReport::getLawTime, dto.getStartTime())
                .le(Objects.nonNull(dto.getEndTime()), NoticeReport::getLawTime, dto.getEndTime())
                .eq(NoticeReport::getAvailable, true);

        // 获取当前用户关联的警员信息
        BasePoliceEmp currentPoliceEmp = userOrgUtils.getCurrentUserPoliceEmp();
        if (currentPoliceEmp == null) {
            throw new BusinessException("当前用户未关联警员信息");
        }
        String empId = currentPoliceEmp.getId();

        // 根据筛选类型添加查询条件
        String filterType = StringUtils.hasText(dto.getFilterType()) ? dto.getFilterType() : "ALL";

        if ("ISSUED".equals(filterType)) {
            // 我下发的：当前用户为下发人
            queryWrapper.eq(NoticeReport::getIssueUserId, empId);
        } else if ("RECEIVED".equals(filterType)) {
            // 我接收的：当前用户所在单位为接收单位
            queryWrapper.eq(NoticeReport::getReceiveOrgId, userOrgUtils.getCurrentUserOrgId());
        } else {
            // 全部通报：需要根据用户权限进行数据过滤
            if (!SecurityUtils.isSuperAdmin()) {
                // 普通用户只能查看当前单位及下级单位的数据
                List<String> accessibleOrgIds = userOrgUtils.getCurrentUserAndSubOrgIds();
                if (accessibleOrgIds != null && !accessibleOrgIds.isEmpty()) {
                    // 如果用户没有传递orgId，则使用权限范围限制
                    if (!StringUtils.hasText(dto.getOrgId())) {
                        // 签发单位或接收单位在权限范围内，或者当前用户是下发人/接收人
                        queryWrapper.and(wrapper -> wrapper
                                .in(NoticeReport::getOrgId, accessibleOrgIds)
                                .or()
                                .in(NoticeReport::getReceiveOrgId, accessibleOrgIds)
                                .or()
                                .eq(NoticeReport::getIssueUserId, empId)
                                .or()
                                .eq(NoticeReport::getReceiveUserId, empId));
                    }
                    // 如果用户传递了orgId，需要验证该orgId是否在用户权限范围内
                    else if (!accessibleOrgIds.contains(dto.getOrgId())) {
                        // 用户传递的orgId不在权限范围内，但如果是下发人或接收人仍可查看
                        queryWrapper.and(wrapper -> wrapper
                                .eq(NoticeReport::getIssueUserId, empId)
                                .or()
                                .eq(NoticeReport::getReceiveUserId, empId));
                    }
                } else {
                    // 如果没有可访问的组织，只能查看自己下发或接收的通报
                    queryWrapper.and(wrapper -> wrapper
                            .eq(NoticeReport::getIssueUserId, empId)
                            .or()
                            .eq(NoticeReport::getReceiveUserId, empId));
                }
            }
            // 管理员可以查看全部数据，不需要额外的组织权限限制
        }

        page(page, queryWrapper);
        List<NoticeReportVO> records = page.getRecords().stream().map(noticeReport -> {
            NoticeReportVO vo = new NoticeReportVO();
            BeanUtils.copyProperties(noticeReport, vo);
            
            // 通过创建人ID查询创建人名称
            if (StringUtils.hasText(noticeReport.getCreateUserId())) {
                String createUserName = userOrgUtils.getUserNameByUserId(noticeReport.getCreateUserId());
                vo.setCreateUserName(createUserName);
            }
            
            return vo;
        }).collect(Collectors.toList());
        return new CommonPageVO<>(records, page.getTotal(), page.getSize(), page.getCurrent());
    }

    /**
     * 获取通知报告详情
     *
     * @param id 通知报告ID
     * @return NoticeReportSaveDTO 通知报告保存DTO对象，如果通知报告不存在或不可用则返回null
     */
    @Override
    public NoticeReportSaveDTO getNoticeReportDetail(String id) {
        NoticeReport noticeReport = getById(id);
        if (Objects.isNull(noticeReport) || !noticeReport.getAvailable()) {
            return null;
        }
        NoticeReportSaveDTO vo = new NoticeReportSaveDTO();

        // 设置基本字段
        vo.setId(noticeReport.getId());
        vo.setTitle(noticeReport.getTitle());
        vo.setOrgId(noticeReport.getOrgId());
        vo.setOrgName(noticeReport.getOrgName());
        vo.setNonComplianceContent(noticeReport.getNonComplianceContent());
        vo.setReceiveOrgId(noticeReport.getReceiveOrgId());
        vo.setReceiveOrgName(noticeReport.getReceiveOrgName());
        vo.setIssueUserId(noticeReport.getIssueUserId());
        vo.setIssueUserName(noticeReport.getIssueUserName());
        vo.setReceiveUserId(noticeReport.getReceiveUserId());
        vo.setReceiveUserName(noticeReport.getReceiveUserName());

        // 处理签发时间格式转换
        if (noticeReport.getLawTime() != null) {
            vo.setLawTime(noticeReport.getLawTime().toString().replace("T", " "));
        }

        return vo;
    }

    /**
     * 删除通知报告
     * <p>
     * 该方法通过设置通知报告的可用状态为false来实现逻辑删除，
     * 并更新修改时间和修改用户信息
     *
     * @param id 通知报告ID
     * @return 删除成功返回true，失败返回false
     */
    @Override
    public Boolean deleteNoticeReport(String id) {
        // 根据ID获取通知报告对象
        NoticeReport noticeReport = getById(id);
        if (Objects.isNull(noticeReport)) {
            return false;
        }

        // 设置逻辑删除相关字段
        noticeReport.setAvailable(false);
        noticeReport.setUpdateTime(LocalDateTime.now());
        noticeReport.setUpdateUserId(SecurityUtils.getLoginUserId());

        // 执行更新操作
        return updateById(noticeReport);
    }

    /**
     * 下发通知报告
     * <p>
     * 该方法用于将通报信息下发给指定的单位，
     * 支持同时下发给多个单位
     *
     * @param dto 下发请求参数，包含通报信息ID和下发单位列表
     * @return 下发成功返回true，失败返回false
     */
    @Override
    public Boolean issueNoticeReport(NoticeReportIssueDTO dto) {
        // 根据ID获取通知报告对象
        NoticeReport noticeReport = getById(dto.getId());
        if (Objects.isNull(noticeReport)) {
            throw new BusinessException("通报信息不存在");
        }

        BasePoliceEmp emp = userOrgUtils.getCurrentUserPoliceEmp();

        // 设置下发相关字段
        noticeReport.setUpdateTime(LocalDateTime.now());
        noticeReport.setUpdateUserId(emp.getId());
        noticeReport.setIssueUserName(emp.getName());
        noticeReport.setIssueUserId(emp.getId());

        // 将下发的单位信息保存到数据库，多个单位用逗号分隔
        if (dto.getOrgList() != null && !dto.getOrgList().isEmpty()) {
            List<String> orgIds = dto.getOrgList().stream()
                    .map(NoticeReportIssueDTO.OrgInfo::getOrgId)
                    .collect(Collectors.toList());
            String receiveOrgIds = String.join(",", orgIds);
            noticeReport.setReceiveOrgId(receiveOrgIds);

            List<String> orgNames = dto.getOrgList().stream()
                    .map(NoticeReportIssueDTO.OrgInfo::getOrgName)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            if (!orgNames.isEmpty()) {
                String receiveOrgNames = String.join(",", orgNames);
                noticeReport.setReceiveOrgName(receiveOrgNames);
            }
        }

        // 执行更新操作
        return updateById(noticeReport);
    }
}
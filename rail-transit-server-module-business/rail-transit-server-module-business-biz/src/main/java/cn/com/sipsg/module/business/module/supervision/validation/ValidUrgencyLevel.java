package cn.com.sipsg.module.business.module.supervision.validation;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * 紧急程度验证注解
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = UrgencyLevelValidator.class)
@Documented
public @interface ValidUrgencyLevel {

    String message() default "紧急程度必须是：urgent(紧急)、important(重要)、normal(一般)";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
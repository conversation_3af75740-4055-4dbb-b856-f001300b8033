package cn.com.sipsg.module.business.module.duty.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR> lixt
 * @date : 2025-01-15
 * @Description: 查询值班人员DTO
 */
@Data
@Schema(description = "查询值班人员DTO")
public class DutyPersonQueryDTO {
    
    @Schema(description = "单位编号", required = true)
    private String orgId;
    
    @Schema(description = "岗位类别ID", required = true)
    private String postId;
}
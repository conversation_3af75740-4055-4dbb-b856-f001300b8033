package cn.com.sipsg.module.business.module.duty.service.impl;

import cn.com.sipsg.common.mybatis.core.query.MPJLambdaWrapperX;
import cn.com.sipsg.common.security.core.util.SecurityUtils;
import cn.com.sipsg.module.business.module.duty.entity.BaseTag;
import cn.com.sipsg.module.business.module.duty.entity.BaseTagGroup;
import cn.com.sipsg.module.business.module.duty.entity.BaseTagGroupRelation;
import cn.com.sipsg.common.exception.BusinessException;
import cn.com.sipsg.module.business.module.duty.mapper.BaseTagGroupMapper;
import cn.com.sipsg.module.business.module.duty.mapper.BaseTagGroupRelationMapper;
import cn.com.sipsg.module.business.module.duty.mapper.BaseTagMapper;
import cn.com.sipsg.module.business.module.duty.pojo.dto.GroupCountDTO;
import cn.com.sipsg.module.business.module.duty.pojo.dto.TagAddDTO;
import cn.com.sipsg.module.business.module.duty.pojo.dto.TagDTO;
import cn.com.sipsg.module.business.module.duty.pojo.dto.TagDeleteDTO;
import cn.com.sipsg.module.business.module.duty.pojo.dto.TagGroupAddDTO;
import cn.com.sipsg.module.business.module.duty.pojo.dto.TagGroupBatchUpdateDTO;
import cn.com.sipsg.module.business.module.duty.pojo.dto.TagGroupDeleteDTO;
import cn.com.sipsg.module.business.module.duty.pojo.dto.TagGroupUpdateDTO;
import cn.com.sipsg.module.business.module.duty.pojo.vo.TagGroupVO;
import cn.com.sipsg.module.business.module.duty.pojo.vo.TagVO;
import cn.com.sipsg.module.business.module.duty.service.BaseTagService;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> lixt
 * @date : 2025-04-30 17:06:43
 * @Description: 标签相关接口实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BaseTagServiceImpl implements BaseTagService {
    private final BaseTagMapper baseTagMapper;
    private final BaseTagGroupMapper baseTagGroupMapper;
    private final BaseTagGroupRelationMapper baseTagGroupRelationMapper;

    /**
     * 获取标签分组信息及其包含的标签列表
     *
     * @return 标签分组信息列表
     */
    @Override
    public List<TagGroupVO> tagGroup() {
        // 查询所有可用的标签分组
        LambdaQueryWrapper<BaseTagGroup> groupWrapper = new LambdaQueryWrapper<>();
        groupWrapper.eq(BaseTagGroup::getAvailable, true)
                   .orderBy(true, true, BaseTagGroup::getId);
        List<BaseTagGroup> allGroups = baseTagGroupMapper.selectList(groupWrapper);

        // 如果没有分组，直接返回空
        if (CollUtil.isEmpty(allGroups)) {
            return Collections.emptyList();
        }

        // 提取所有分组id
        List<String> groupIds = allGroups.stream()
                .map(BaseTagGroup::getId)
                .collect(Collectors.toList());

        // 查询每个分组下的标签数量
        MPJLambdaWrapperX<BaseTagGroupRelation> countWrapper = new MPJLambdaWrapperX<>();
        countWrapper
                .select(BaseTagGroupRelation::getGroupId)
                .select("COUNT(*) AS count")
                .eq(BaseTagGroupRelation::getAvailable, true)
                .in(BaseTagGroupRelation::getGroupId, groupIds)
                .groupBy(BaseTagGroupRelation::getGroupId);
        List<GroupCountDTO> groupCountList = baseTagGroupRelationMapper.selectJoinList(GroupCountDTO.class, countWrapper);

        // 查询每个分组下的所有标签信息
        MPJLambdaWrapperX<BaseTagGroupRelation> tagWrapper = new MPJLambdaWrapperX<>();
        tagWrapper
                .select(BaseTagGroupRelation::getGroupId)
                .select(BaseTag::getId, BaseTag::getTagValue, BaseTag::getTagKey, BaseTag::getAvailable, BaseTag::getBusinessType)
                .leftJoin(BaseTag.class, BaseTag::getId, BaseTagGroupRelation::getTagId)
                .eq(BaseTagGroupRelation::getAvailable, true)
                .eq(BaseTag::getAvailable, true)
                .in(BaseTagGroupRelation::getGroupId, groupIds);
        List<TagDTO> tagList = baseTagGroupRelationMapper.selectJoinList(TagDTO.class, tagWrapper);

        // 组装返回数据
        List<TagGroupVO> list = allGroups.stream().map(group -> {
            TagGroupVO vo = new TagGroupVO();
            vo.setId(group.getId());
            vo.setGroupName(group.getGroupName());
            
            // 查找该分组的标签数量
            Long tagCount = groupCountList.stream()
                    .filter(count -> count.getGroupId().equals(group.getId()))
                    .map(GroupCountDTO::getCount)
                    .findFirst()
                    .orElse(0L);
            vo.setGroupTagCount(String.valueOf(tagCount));
            
            // 查找该分组下的所有标签
            List<TagVO> tags = tagList.stream()
                    .filter(tag -> tag.getGroupId().equals(group.getId()))
                    .map(tag -> {
                        TagVO tagVO = new TagVO();
                        tagVO.setId(tag.getId());
                        tagVO.setAvailable(tag.getAvailable());
                        tagVO.setTagKey(tag.getTagKey());
                        tagVO.setTagValue(tag.getTagValue());
                        tagVO.setBusinessType(tag.getBusinessType());
                        // 可补充其它字段
                        return tagVO;
                    }).collect(Collectors.toList());
            vo.setBaseTag(tags);
            return vo;
        }).collect(Collectors.toList());

        return list;
    }

    /**
     * 添加标签分组
     *
     * @param groupName 分组名称，用于创建新的标签分组
     * @throws BusinessException 如果分组名称为空或添加分组过程中发生异常，则抛出此异常
     */
    @Override
    public void addTagGroup(TagGroupAddDTO dto) {
        String groupName = dto.getGroupName();
        // 检查分组名称是否为空，如果为空，则抛出异常
        if (StrUtil.isBlank(groupName)) {
            throw new BusinessException("分组名称不能为空");
        }
        
        // 校验分组名称唯一性
        Long count = baseTagGroupMapper.selectCount(
            new QueryWrapper<BaseTagGroup>()
                .eq("group_name", groupName)
                .eq("available", true)
        );
        if (count != null && count > 0) {
            throw new BusinessException("分组名称已存在，请勿重复添加");
        }
        
        // 获取当前登录用户ID
        String userId = StpUtil.getLoginIdAsString();
        LocalDateTime now = LocalDateTime.now();
        
        // 创建一个新的标签分组对象
        BaseTagGroup baseTagGroup = new BaseTagGroup();
        // 设置分组名称
        baseTagGroup.setGroupName(groupName);
        // 设置创建时间和更新时间为当前时间
        baseTagGroup.setCreateTime(now);
        baseTagGroup.setUpdateTime(now);
        // 设置创建人和更新人为当前用户
        baseTagGroup.setCreateUserId(userId);
        baseTagGroup.setUpdateUserId(userId);
        // 设置分组为可用状态
        baseTagGroup.setAvailable(true);
        // 插入新的标签分组到数据库
        baseTagGroupMapper.insert(baseTagGroup);
    }


    /**
     * 添加标签并关联到指定分组
     *
     * @param tagName 标签名称，不可为空
     * @param groupId 分组ID，不可为空
     *
     * 此方法首先会验证标签名称和分组ID是否为空，然后检查同一分组下是否已存在相同标签名称
     * 如果验证通过，则会创建一个新的标签并将其关联到指定的分组中
     */
    @Override
    public void addTag(TagAddDTO dto) {
        String tagName = dto.getTagName();
        String groupId = dto.getGroupId();
        // 验证标签名称不为空
        if (StrUtil.isBlank(tagName)) {
            throw new BusinessException("标签名称不能为空");
        }
        // 验证分组ID不为空
        if (groupId == null) {
            throw new BusinessException("分组id不能为空");
        }
        // 新增：校验分组是否存在且可用
        BaseTagGroup group = baseTagGroupMapper.selectById(groupId);
        if (group == null || !Boolean.TRUE.equals(group.getAvailable())) {
            throw new BusinessException("标签分组不存在或已被禁用，无法添加标签");
        }
        
        // 获取当前登录用户ID
        String userId = StpUtil.getLoginIdAsString();
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();
        
        // 校验同一分组下标签名唯一性
        Long tagCount = baseTagGroupRelationMapper.selectCount(
            new QueryWrapper<BaseTagGroupRelation>()
                .eq("group_id", groupId)
                .inSql("tag_id", "SELECT id FROM tbl_base_tag WHERE tag_value = '" + tagName + "' AND available = true")
                .eq("available", true)
        );
        
        // 如果存在同名标签，则抛出异常
        if (tagCount != null && tagCount > 0) {
            throw new BusinessException("该分组下已存在同名标签，请勿重复添加");
        }
        
        // 新增标签
        BaseTag tag = new BaseTag();
        tag.setTagValue(tagName);
        tag.setAvailable(true);
        tag.setCreateTime(now);
        tag.setCreateUserId(userId);
        tag.setUpdateTime(now);
        tag.setUpdateUserId(userId);
        baseTagMapper.insert(tag);
        
        // 新增分组关联
        BaseTagGroupRelation relation = new BaseTagGroupRelation();
        relation.setTagId(tag.getId());
        relation.setGroupId(groupId);
        relation.setAvailable(true);
        relation.setCreateTime(now);
        relation.setCreateUserId(userId);
        relation.setUpdateTime(now);
        relation.setUpdateUserId(userId);
        baseTagGroupRelationMapper.insert(relation);
    }

    @Override
    public void updateTagGroup(TagGroupUpdateDTO dto) {
        String tagId = dto.getTagId();
        String groupId = dto.getGroupId();
        if (tagId == null) throw new BusinessException("标签ID不能为空");
        if (groupId == null) throw new BusinessException("分组ID不能为空");

        // 校验标签和分组存在性
        BaseTag tag = baseTagMapper.selectById(tagId);
        if (tag == null || !Boolean.TRUE.equals(tag.getAvailable())) throw new BusinessException("标签不存在或已被禁用");
        BaseTagGroup group = baseTagGroupMapper.selectById(groupId);
        if (group == null || !Boolean.TRUE.equals(group.getAvailable())) throw new BusinessException("分组不存在或已被禁用");

        // 新增：判断该标签是否已在目标分组且可用
        BaseTagGroupRelation exist = baseTagGroupRelationMapper.selectOne(
            new QueryWrapper<BaseTagGroupRelation>()
                .eq("tag_id", tagId)
                .eq("group_id", groupId)
                .eq("available", true)
        );
        if (exist != null) {
            throw new BusinessException("标签已在该分组，无需变更");
        }

        // 1. 先将该标签所有可用的分组关联置为不可用
        BaseTagGroupRelation update = new BaseTagGroupRelation();
        update.setAvailable(false);
        update.setUpdateTime(java.time.LocalDateTime.now());
        update.setUpdateUserId(cn.dev33.satoken.stp.StpUtil.getLoginIdAsString());
        baseTagGroupRelationMapper.update(update,
            new QueryWrapper<BaseTagGroupRelation>()
                .eq("tag_id", tagId)
                .eq("available", true)
        );

        // 2. 处理新的关联关系：优先启用已存在的软删除关联，不存在则新增
        String currentUserId = SecurityUtils.getLoginUser().getUserId();
        LocalDateTime currentTime = LocalDateTime.now();
        
        // 查找是否存在软删除的关联关系
        BaseTagGroupRelation existingRelation = baseTagGroupRelationMapper.selectOne(
            new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<BaseTagGroupRelation>()
                .eq("tag_id", tagId)
                .eq("group_id", groupId)
                .eq("available", false)
        );
        
        if (existingRelation != null) {
            // 如果存在软删除的关联，直接启用
            BaseTagGroupRelation enableRelation = new BaseTagGroupRelation();
            enableRelation.setId(existingRelation.getId());
            enableRelation.setAvailable(true);
            enableRelation.setUpdateTime(currentTime);
            enableRelation.setUpdateUserId(currentUserId);
            baseTagGroupRelationMapper.updateById(enableRelation);
        } else {
            // 如果不存在，则新增关联关系
            BaseTagGroupRelation newRelation = new BaseTagGroupRelation();
            newRelation.setTagId(tagId);
            newRelation.setGroupId(groupId);
            newRelation.setAvailable(true);
            newRelation.setCreateTime(currentTime);
            newRelation.setCreateUserId(currentUserId);
            newRelation.setUpdateTime(currentTime);
            newRelation.setUpdateUserId(currentUserId);
            baseTagGroupRelationMapper.insert(newRelation);
        }
    }

    @Override
    public void batchUpdateTagGroup(List<TagGroupBatchUpdateDTO.TagGroupMapping> tagGroupMappings) {
        if (CollUtil.isEmpty(tagGroupMappings)) {
            throw new BusinessException("标签分组映射列表不能为空");
        }

        // 提取所有标签ID和分组ID
        List<String> tagIds = tagGroupMappings.stream()
            .map(TagGroupBatchUpdateDTO.TagGroupMapping::getTagId)
            .collect(java.util.stream.Collectors.toList());
        List<String> groupIds = tagGroupMappings.stream()
            .map(TagGroupBatchUpdateDTO.TagGroupMapping::getGroupId)
            .distinct()
            .collect(java.util.stream.Collectors.toList());

        // 校验所有标签存在性
        List<BaseTag> tags = baseTagMapper.selectList(new LambdaQueryWrapper<BaseTag>()
                .in(BaseTag::getId, tagIds));
        if (tags.size() != tagIds.size()) {
            throw new BusinessException("部分标签不存在");
        }
        
        // 检查是否有标签已被禁用
        boolean hasDisabledTag = tags.stream().anyMatch(tag -> !Boolean.TRUE.equals(tag.getAvailable()));
        if (hasDisabledTag) {
            throw new BusinessException("部分标签已被禁用");
        }

        // 校验所有分组存在性
        List<BaseTagGroup> groups = baseTagGroupMapper.selectList(new LambdaQueryWrapper<BaseTagGroup>()
                .in(BaseTagGroup::getId, groupIds));
        if (groups.size() != groupIds.size()) {
            throw new BusinessException("部分分组不存在");
        }
        
        // 检查是否有分组已被禁用
        boolean hasDisabledGroup = groups.stream().anyMatch(group -> !Boolean.TRUE.equals(group.getAvailable()));
        if (hasDisabledGroup) {
            throw new BusinessException("部分分组已被禁用");
        }

        // 检查是否有标签已在目标分组中
        List<String> duplicateChecks = new java.util.ArrayList<>();
        for (TagGroupBatchUpdateDTO.TagGroupMapping mapping : tagGroupMappings) {
            BaseTagGroupRelation existRelation = baseTagGroupRelationMapper.selectOne(
                new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<BaseTagGroupRelation>()
                    .eq("tag_id", mapping.getTagId())
                    .eq("group_id", mapping.getGroupId())
                    .eq("available", true)
            );
            if (existRelation != null) {
                duplicateChecks.add("标签ID " + mapping.getTagId() + " 已在分组ID " + mapping.getGroupId() + " 中");
            }
        }
        
        if (!duplicateChecks.isEmpty()) {
            throw new BusinessException("存在重复分配: " + String.join(", ", duplicateChecks));
        }

        String userId = cn.dev33.satoken.stp.StpUtil.getLoginIdAsString();
        LocalDateTime now = LocalDateTime.now();

        // 批量将所有标签的现有分组关联置为不可用
        BaseTagGroupRelation update = new BaseTagGroupRelation();
        update.setAvailable(false);
        update.setUpdateTime(now);
        update.setUpdateUserId(userId);
        baseTagGroupRelationMapper.update(update,
            new QueryWrapper<BaseTagGroupRelation>()
                .in("tag_id", tagIds)
                .eq("available", true)
        );

        // 处理新的关联关系：优先启用已存在的软删除关联，不存在则新增
        for (TagGroupBatchUpdateDTO.TagGroupMapping mapping : tagGroupMappings) {
            // 查找是否存在软删除的关联关系
            BaseTagGroupRelation existingRelation = baseTagGroupRelationMapper.selectOne(
                new QueryWrapper<BaseTagGroupRelation>()
                    .eq("tag_id", mapping.getTagId())
                    .eq("group_id", mapping.getGroupId())
                    .eq("available", false)
            );
            
            if (existingRelation != null) {
                // 如果存在软删除的关联，直接启用
                BaseTagGroupRelation enableRelation = new BaseTagGroupRelation();
                enableRelation.setId(existingRelation.getId());
                enableRelation.setAvailable(true);
                enableRelation.setUpdateTime(now);
                enableRelation.setUpdateUserId(userId);
                baseTagGroupRelationMapper.updateById(enableRelation);
            } else {
                // 如果不存在，则新增关联关系
                BaseTagGroupRelation newRelation = new BaseTagGroupRelation();
                newRelation.setTagId(mapping.getTagId());
                newRelation.setGroupId(mapping.getGroupId());
                newRelation.setAvailable(true);
                newRelation.setCreateTime(now);
                newRelation.setCreateUserId(userId);
                newRelation.setUpdateTime(now);
                newRelation.setUpdateUserId(userId);
                baseTagGroupRelationMapper.insert(newRelation);
            }
        }
    }

    /**
     * 删除标签
     * 
     * @param tagId 标签ID
     */
    @Override
    public void deleteTag(TagDeleteDTO dto) {
        String tagId = dto.getTagId();
        // 验证标签ID不为空
        if (tagId == null) {
            throw new BusinessException("标签ID不能为空");
        }
        
        // 校验标签是否存在且可用
        BaseTag tag = baseTagMapper.selectById(tagId);
        if (tag == null) {
            throw new BusinessException("标签不存在");
        }
        if (!Boolean.TRUE.equals(tag.getAvailable())) {
            throw new BusinessException("标签已被删除");
        }
        
        String userId = StpUtil.getLoginIdAsString();
        LocalDateTime now = LocalDateTime.now();
        
        // 1. 将标签置为不可用（软删除）
        BaseTag updateTag = new BaseTag();
        updateTag.setId(tagId);
        updateTag.setAvailable(false);
        updateTag.setUpdateTime(now);
        updateTag.setUpdateUserId(userId);
        baseTagMapper.updateById(updateTag);
        
        // 2. 将该标签的所有分组关联置为不可用
        BaseTagGroupRelation updateRelation = new BaseTagGroupRelation();
        updateRelation.setAvailable(false);
        updateRelation.setUpdateTime(now);
        updateRelation.setUpdateUserId(userId);
        baseTagGroupRelationMapper.update(updateRelation,
            new QueryWrapper<BaseTagGroupRelation>()
                .eq("tag_id", tagId)
                .eq("available", true)
        );
    }

    /**
     * 删除标签分组
     * 
     * @param groupId 分组ID
     */
    @Override
    public void deleteTagGroup(TagGroupDeleteDTO dto) {
        String groupId = dto.getGroupId();
        // 验证分组ID不为空
        if (groupId == null) {
            throw new BusinessException("分组ID不能为空");
        }
        
        // 校验分组是否存在且可用
        BaseTagGroup group = baseTagGroupMapper.selectById(groupId);
        if (group == null) {
            throw new BusinessException("标签分组不存在");
        }
        if (!Boolean.TRUE.equals(group.getAvailable())) {
            throw new BusinessException("标签分组已被删除");
        }
        
        String userId = StpUtil.getLoginIdAsString();
        LocalDateTime now = LocalDateTime.now();
        
        // 1. 将分组置为不可用（软删除）
        BaseTagGroup updateGroup = new BaseTagGroup();
        updateGroup.setId(groupId);
        updateGroup.setAvailable(false);
        updateGroup.setUpdateTime(now);
        updateGroup.setUpdateUserId(userId);
        baseTagGroupMapper.updateById(updateGroup);
        
        // 2. 将该分组下的所有标签关联置为不可用
        BaseTagGroupRelation updateRelation = new BaseTagGroupRelation();
        updateRelation.setAvailable(false);
        updateRelation.setUpdateTime(now);
        updateRelation.setUpdateUserId(userId);
        baseTagGroupRelationMapper.update(updateRelation,
            new QueryWrapper<BaseTagGroupRelation>()
                .eq("group_id", groupId)
                .eq("available", true)
        );
    }
}

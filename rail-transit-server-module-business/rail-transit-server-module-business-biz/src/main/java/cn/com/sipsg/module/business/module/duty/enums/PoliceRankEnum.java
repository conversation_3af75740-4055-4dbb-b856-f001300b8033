package cn.com.sipsg.module.business.module.duty.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum PoliceRankEnum {
    CHIEF_SUPERINTENDENT("01", "总警监"),
    DEPUTY_CHIEF_SUPERINTENDENT("02", "副总警监"),
    FIRST_CLASS_COMMISSIONER("03", "一级警监"),
    SECOND_CLASS_COMMISSIONER("04", "二级警监"),
    THIRD_CLASS_COMMISSIONER("05", "三级警监"),
    FIRST_CLASS_SUPERINTENDENT("06", "一级警督"),
    SECOND_CLASS_SUPERINTENDENT("07", "二级警督"),
    THIRD_CLASS_SUPERINTENDENT("08", "三级警督"),
    FIRST_CLASS_SERGEANT("09", "一级警司"),
    SECOND_CLASS_SERGEANT("10", "二级警司"),
    THIRD_CLASS_SERGEANT("11", "三级警司"),
    FIRST_CLASS_CONSTABLE("12", "一级警员"),
    SECOND_CLASS_CONSTABLE("13", "二级警员");

    @EnumValue
    private final String code;
    
    @JsonValue
    private final String label;

    PoliceRankEnum(String code, String label) {
        this.code = code;
        this.label = label;
    }

    public static boolean isValid(String value) {
        for (PoliceRankEnum e : values()) {
            if (e.label.equals(value) || e.code.equals(value)) {
                return true;
            }
        }
        return false;
    }

    public static String getLabelByCode(String code) {
        for (PoliceRankEnum e : values()) {
            if (e.code.equals(code)) {
                return e.label;
            }
        }
        return null;
    }

    public static String getCodeByLabel(String label) {
        for (PoliceRankEnum e : values()) {
            if (e.label.equals(label)) {
                return e.code;
            }
        }
        return null;
    }

    public static PoliceRankEnum getByCode(String code) {
        for (PoliceRankEnum e : values()) {
            if (e.code.equals(code)) {
                return e;
            }
        }
        return null;
    }
}
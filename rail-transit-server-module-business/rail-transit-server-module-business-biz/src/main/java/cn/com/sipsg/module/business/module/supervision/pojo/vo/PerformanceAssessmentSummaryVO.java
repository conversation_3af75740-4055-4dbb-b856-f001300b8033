package cn.com.sipsg.module.business.module.supervision.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "业务考核统计汇总VO")
public class PerformanceAssessmentSummaryVO {
    @Schema(description = "本月总分")
    private Double monthTotalScore;
    @Schema(description = "部门排名（如3/28）")
    private String departmentRank;
    @Schema(description = "任务完成率（如95.8%22）")
    private String taskCompletionRate;
    @Schema(description = "出勤率（如98.2%11）")
    private String attendanceRate;
} 
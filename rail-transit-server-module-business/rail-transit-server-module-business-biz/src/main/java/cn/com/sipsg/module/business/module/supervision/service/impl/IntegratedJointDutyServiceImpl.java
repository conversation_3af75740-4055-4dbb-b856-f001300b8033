package cn.com.sipsg.module.business.module.supervision.service.impl;

import cn.com.sipsg.common.security.core.util.SecurityUtils;
import cn.com.sipsg.module.business.module.common.util.UserOrgUtils;
import cn.com.sipsg.module.business.module.duty.entity.BasePoliceEmp;
import cn.com.sipsg.module.business.module.duty.entity.BasePoliceOrg;
import cn.com.sipsg.module.business.module.duty.mapper.BasePoliceEmpMapper;
import cn.com.sipsg.module.business.module.duty.mapper.BasePoliceOrgMapper;
import cn.com.sipsg.module.business.module.supervision.mapper.GpsPoliceMotorcarMapper;
import cn.com.sipsg.module.business.module.supervision.mapper.GpsPoliceCarMapper;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.IntegratedJointDutySummaryVO;
import cn.com.sipsg.module.business.module.supervision.service.IntegratedJointDutyService;
import cn.com.sipsg.module.business.module.supervision.util.StatisticsTraceQueryUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class IntegratedJointDutyServiceImpl implements IntegratedJointDutyService {
    private final BasePoliceEmpMapper basePoliceEmpMapper;
    private final BasePoliceOrgMapper basePoliceOrgMapper;
    private final GpsPoliceCarMapper gpsPoliceCarMapper;
    private final GpsPoliceMotorcarMapper gpsPoliceMotorcarMapper;
    private final UserOrgUtils userOrgUtils;

    @Override
    public IntegratedJointDutySummaryVO getSummary() {
        // 1. 判断是否是超级管理员
        boolean isSuperAdmin = SecurityUtils.isSuperAdmin();
        log.info("[联勤统计] 当前用户是否是超级管理员: {}", isSuperAdmin);
        
        // 获取所有组织信息
        List<BasePoliceOrg> allOrg = basePoliceOrgMapper.selectList(null);
        
        List<String> orgIds;
        if (isSuperAdmin) {
            // 超级管理员获取所有组织ID
            orgIds = allOrg.stream().map(BasePoliceOrg::getId).collect(Collectors.toList());
            log.info("[联勤统计] 超级管理员获取所有组织ID集合: {}", orgIds.size());
        } else {
            // 非超级管理员获取当前用户及下级组织ID
            String orgId = userOrgUtils.getCurrentUserOrgId();
            if (orgId == null) {
                log.warn("[联勤统计] 当前用户未获取到组织ID，返回空统计结果");
                return new IntegratedJointDutySummaryVO();
            }
            log.info("[联勤统计] 当前用户组织ID: {}", orgId);
            // 获取当前用户及下级组织的所有子组织ID
            StatisticsTraceQueryUtils queryUtils = new StatisticsTraceQueryUtils(userOrgUtils, basePoliceOrgMapper);
            orgIds = queryUtils.getOrgAndChildrenIds(orgId);
            log.info("[联勤统计] 当前用户及下级组织ID集合: {}", orgIds);
        }

        // 2. 查询组织类型
        // 过滤出相关组织的信息
        List<BasePoliceOrg> orgList = allOrg.stream().filter(o -> orgIds.contains(o.getId())).collect(Collectors.toList());
        // 分别获取地面和地铁组织的ID列表
        List<String> groundOrgIds = orgList.stream().filter(o -> o.getPlaceType() == null || o.getPlaceType() == 1).map(BasePoliceOrg::getId).collect(Collectors.toList());
        List<String> subwayOrgIds = orgList.stream().filter(o -> o.getPlaceType() != null && o.getPlaceType() == 2).map(BasePoliceOrg::getId).collect(Collectors.toList());
        log.info("[联勤统计] 地面组织ID: {}，地铁组织ID: {}", groundOrgIds, subwayOrgIds);

        // 3. 计算地面警员数量
        long groundPoliceCount = groundOrgIds.isEmpty() ? 0 : basePoliceEmpMapper.selectCount(new LambdaQueryWrapper<BasePoliceEmp>().in(BasePoliceEmp::getPoliceOrgId, groundOrgIds).eq(BasePoliceEmp::getAvailable, true));
        log.info("[联勤统计] 地面警员数量: {}", groundPoliceCount);

        // 4. 计算地铁警员数量
        long subwayPoliceCount = subwayOrgIds.isEmpty() ? 0 : basePoliceEmpMapper.selectCount(new LambdaQueryWrapper<BasePoliceEmp>().in(BasePoliceEmp::getPoliceOrgId, subwayOrgIds).eq(BasePoliceEmp::getAvailable, true));
        log.info("[联勤统计] 地铁警员数量: {}", subwayPoliceCount);

        // 5. 计算警车数量（从GPS表中获取）
        long policeCarCount = gpsPoliceCarMapper.selectCount(null);
        log.info("[联勤统计] 警车数量: {}", policeCarCount);

        // 6. 计算摩托车数量（从GPS表中获取）
        long motorcycleCount = gpsPoliceMotorcarMapper.selectCount(null);
        log.info("[联勤统计] 摩托车数量: {}", motorcycleCount);

        // 7. 封装VO
        IntegratedJointDutySummaryVO vo = new IntegratedJointDutySummaryVO();
        vo.setGroundPoliceCount(groundPoliceCount);
        vo.setSubwayPoliceCount(subwayPoliceCount);
        vo.setPoliceCarCount(policeCarCount);
        vo.setMotorcycleCount(motorcycleCount);
        log.info("[联勤统计] 汇总VO: {}", vo);

        // 返回封装好的VO对象
        return vo;
    }
}
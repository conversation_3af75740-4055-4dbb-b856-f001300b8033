package cn.com.sipsg.module.business.module.supervision.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> lixt
 * @date : 2025-05-21 14:25:18
 * @Description: 考试管理-考试任务表
 */
@Data
@TableName("tbl_exam_task")
public class ExamTask {
    @TableId(type = IdType.ASSIGN_ID)
    private String id;  // 主键ID

    @TableField("create_user_id")
    private String createUserId;  // 创建人

    @TableField("create_time")
    private LocalDateTime createTime;  // 创建时间

    @TableField("update_user_id")
    private String updateUserId;  // 更新人

    @TableField("update_time")
    private LocalDateTime updateTime;  // 更新时间

    @TableField("available")
    private Boolean available;  // 有效标识

    @TableField("task_name")
    private String taskName;  // 考试任务名称

    @TableField("start_time")
    private LocalDateTime startTime;  // 考试开始时间

    @TableField("end_time")
    private LocalDateTime endTime;  // 考试结束时间

    @TableField("exam_duration")
    private Integer examDuration;  // 考试时长

    @TableField("score_sync")
    private Boolean scoreSync;  // 是否同步成绩到档案

    @TableField("question_bank_id")
    private String questionBankId;  // 考试题库ID

    @TableField("total_score")
    private Integer totalScore;  // 总分

    @TableField("passing_score")
    private Integer passingScore;  // 合格分数

    @TableField("single_choice_num")
    private Integer singleChoiceNum;  // 单选题数量

    @TableField("single_choice_score")
    private Integer singleChoiceScore;  // 单选题分数

    @TableField("multiple_choice_score")
    private Integer multipleChoiceScore;  // 多选题分数

    @TableField("multiple_choice_num")
    private Integer multipleChoiceNum;  // 多选题数量

    @TableField("true_false_num")
    private Integer trueFalseNum;  // 判断题数量

    @TableField("true_false_score")
    private Integer trueFalseScore;  // 判断题分数

    @TableField("remark")
    private String remark;  // 备注
}

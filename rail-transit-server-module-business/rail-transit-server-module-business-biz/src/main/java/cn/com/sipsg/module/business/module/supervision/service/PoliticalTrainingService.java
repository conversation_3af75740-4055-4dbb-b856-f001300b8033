package cn.com.sipsg.module.business.module.supervision.service;

import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.pojo.bo.PageBO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.ExamTaskCreateReqDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.ExamTaskUpdateReqDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.ExamTaskUserPageReqDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.MyExamDetailReqDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.QuestionBankPageReqDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.ExamScoreAnalysisVO;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.ExamTaskVO;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.QuestionBankVO;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.ExamTaskDetailVO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.ExamTaskDetailReqDTO;
import org.springframework.web.multipart.MultipartFile;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.ExamTaskPageReqDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.ExamTaskPageVO;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.MyExamDetailVO;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.DeptPoliceTreeVO;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.ExamTaskEditDetailVO;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.ExamTaskEmpQuestionRecordVO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.ExamTaskEmpQuestionRecordPageReqDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.ExamTaskEmpQuestionUploadDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.QuestionBankUpdateReqDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.QuestionBankEditDetailVO;

import java.util.List;
import cn.com.sipsg.module.business.module.supervision.entity.ExamQuestionBank;

/**
 * 政工考核考试培训服务接口
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
public interface PoliticalTrainingService {
    
    /**
     * 获取当前用户的待办考试任务（分页）
     * 查询条件：当前用户关联的考试任务，且当前时间在考试结束时间之前的
     *
     * @param pageBO 分页参数
     * @return 分页后的待办考试任务VO列表
     */
    CommonPageVO<ExamTaskVO> getPendingExamTasksByCurrentUserWithPage(PageBO pageBO);
    
    /**
     * 获取当前用户的最新两条待办考试任务
     * 查询条件：当前用户关联的考试任务，且当前时间在考试结束时间之前的
     * 按截止时间降序排序，只返回最新的两条数据
     *
     * @return 最新的两条待办考试任务VO列表
     */
    List<ExamTaskVO> getLatestTwoPendingExamTasksByCurrentUser();
    
    /**
     * 获取当前用户的考试成绩按月统计分析
     * 统计当前用户所有考试成绩，按月份分组计算平均分
     * 如果一个月内有多个考试成绩，取平均值
     *
     * @return 按月统计的考试成绩分析VO列表
     */
    List<ExamScoreAnalysisVO> getExamScoreAnalysisByMonth();
    
    /**
     * 根据考试任务名称和状态查询当前用户的考试任务（分页）
     * 支持考试任务名称模糊查询，按照考试截止时间倒序排列，支持按状态过滤
     *
     * @param reqDTO 考试任务查询参数，包含分页信息和过滤条件
     * @param taskName 考试任务名称（模糊查询），传null或空字符串表示不按名称过滤
     * @param status 考试状态（全部/未开始/进行中/不合格/合格），传null或空字符串表示查询全部
     * @return 分页后的考试任务VO列表
     */
    CommonPageVO<ExamTaskVO> searchUserExamTasksWithPage(ExamTaskUserPageReqDTO reqDTO, String taskName, String status);

    /**
     * 新增或更新考试任务
     * @param reqDTO 考试任务请求DTO
     * @return 新增或更新的考试任务ID
     */
    String createOrUpdateExamTask(ExamTaskCreateReqDTO reqDTO);

    /**
     * 更新考试任务
     * 修改考试任务信息并保存
     *
     * @param reqDTO 考试任务更新请求DTO
     * @return 更新是否成功
     */
    Boolean updateExamTask(ExamTaskUpdateReqDTO reqDTO);

    /**
     * 题库分页列表查询
     * 支持题库名称模糊查询和状态过滤
     *
     * @param reqDTO 题库查询参数，包含分页信息和过滤条件
     * @return 分页后的题库VO列表
     */
    CommonPageVO<QuestionBankVO> getQuestionBankListWithPage(QuestionBankPageReqDTO reqDTO);

    /**
     * 题库详情
     * @param questionBankId 题库ID
     * @return 题库VO
     */
    QuestionBankVO getQuestionBankDetail(String questionBankId);

    /**
     * 根据考试任务获取题库详情（题目分数根据任务配置设置）
     * @param taskId 考试任务ID
     * @return 题库详情VO
     */
    QuestionBankVO getQuestionBankDetailByTask(String taskId);

    /**
     * 新增题库
     * @param file 上传的Excel文件
     * @param name 题库名称
     * @param description 题库描述
     * @return 导入成功的题目数量
     */
    Integer uploadQuestionBank(MultipartFile file, String name, String description);

    /**
     * 题库导入（修改时用，支持同时改文件、名称、描述）
     * @param file 上传的Excel文件
     * @param questionBankId 题库ID
     * @param name 题库名称
     * @param description 题库描述
     * @return 导入成功的题目数量
     */
    Integer uploadQuestionBank(MultipartFile file, String questionBankId, String name, String description);

    /**
     * 题库停用
     * 将指定题库状态设置为禁用，只有超级管理员才能执行此操作
     *
     * @param questionBankId 题库ID
     * @return 停用是否成功
     */
    Boolean disableQuestionBank(String questionBankId);

    /**
     * 题库启用/停用
     * @param questionBankId 题库ID
     * @param enable true-启用，false-停用
     * @return 操作结果
     */
    Boolean enableOrDisableQuestionBank(String questionBankId, Boolean enable);

    /**
     * 修改题库信息，支持可选文件替换
     * @param reqDTO 修改请求
     * @return 是否成功
     */
    boolean updateQuestionBank(QuestionBankUpdateReqDTO reqDTO);

    /**
     * 考试任务详情（带人员分页）
     * @param reqDTO 请求参数
     * @return 分页详情
     */
    CommonPageVO<ExamTaskDetailVO> getExamTaskDetailWithPersonPage(ExamTaskDetailReqDTO reqDTO);

    /**
     * 考试任务分页列表
     * @param reqDTO 请求参数
     * @return 分页VO
     */
    CommonPageVO<ExamTaskPageVO> pageExamTasks(ExamTaskPageReqDTO reqDTO);

    /**
     * 我的考试详情，查最近一次考试记录
     * @param reqDTO 请求参数
     * @return 个人考试详情VO
     */
    MyExamDetailVO getMyExamDetail(MyExamDetailReqDTO reqDTO);

    /**
     * 获取考试任务用于编辑的全部信息
     * @param taskId 考试任务详情请求DTO
     * @return 编辑详情VO
     */
    ExamTaskEditDetailVO getExamTaskEditDetail(String taskId);

    /**
     * 获取题库用于编辑的详情信息
     * @param questionBankId 题库ID
     * @return 题库编辑详情VO，包含题库编号、名称、描述、原文件url、文件名、状态等
     */
    QuestionBankEditDetailVO getQuestionBankEditDetail(String questionBankId);

    /**
     * 查询所有在职警员信息（按部门分组的树形结构）
     * 管理员可以查看全部警员，普通用户只能查看当前单位及下级单位的警员
     *
     * @return 部门-警员树形结构列表
     */
    List<DeptPoliceTreeVO> getActivePoliceTreeList();

    /**
     * 分页查询考试人员答题记录
     * @param reqDTO 分页请求DTO
     * @return 分页VO
     */
    CommonPageVO<ExamTaskEmpQuestionRecordVO> pageExamTaskEmpQuestionRecords(ExamTaskEmpQuestionRecordPageReqDTO reqDTO);

    /**
     * 答题记录上传
     * @param record 答题记录
     * @return 是否成功
     */
    Boolean uploadExamTaskEmpQuestionRecord(ExamTaskEmpQuestionUploadDTO record);

    /**
     * 根据题库ID获取题库实体
     */
    ExamQuestionBank getExamQuestionBankEntity(String questionBankId);

    /**
     * 获取上传目录物理路径
     */
    String getUploadPath();
}

<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false">
    <!-- 应用名称 -->
    <springProperty name="APP_NAME" scope="context" source="spring.application.name"/>

    <!-- 日志文件路径 -->
    <property name="LOG_HOME" value="./logs" />

    <!-- 定义输出的格式 -->
    <property name="PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n" />

    <!-- 控制台日志 - 使用彩色日志格式 -->
    <property name="CONSOLE_LOG_PATTERN" value="%boldGreen([%d{yyyy-MM-dd HH:mm:ss.SSS}]) %boldYellow([%X{requestId}]) %boldRed(%-5level) %boldBlue(%logger{15}) %boldCyan([%thread]) %boldMagenta(·⊱══>) %boldWhite(%msg%n)"/>

    <!-- 控制台日志 -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <appender name="FILE"  class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${LOG_HOME}/${APP_NAME}.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <FileNamePattern>${LOG_HOME}/${APP_NAME}.log.%d{yyyy-MM-dd}.%i.log</FileNamePattern>
            <MaxHistory>30</MaxHistory>
            <maxFileSize>50MB</maxFileSize>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${PATTERN}</pattern>
        </encoder>
    </appender>

    <!-- 异步输出日志 -->
    <appender name="ASYNC_FILE" class="com.yomahub.tlog.core.enhance.logback.async.AspectLogbackAsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>2048</queueSize>
        <includeCallerData>true</includeCallerData>
        <appender-ref ref="FILE"/>
    </appender>

    <root level="INFO">
        <appender-ref ref="STDOUT" />
        <!--异步输出日志-->
        <appender-ref ref="ASYNC_FILE" />
    </root>

</configuration>
CREATE TABLE IF NOT EXISTS tbl_civil_service_report (
    id VARCHAR(32) PRIMARY KEY, -- 主键ID
    title VARCHAR(100) NOT NULL COMMENT '标题名称',
    reporter_id VARCHAR(50) NOT NULL COMMENT '上报人警员编号',
    reporter_name VARCHAR(50) NOT NULL COMMENT '上报人名称',
    report_time TIMESTAMP NOT NULL COMMENT '上报时间',
    content TEXT COMMENT '内容',
    status VARCHAR(20) NOT NULL DEFAULT '待审核' COMMENT '处理状态：待审核、已审核通过、处理中、已结案',
    is_satisfied BOOLEAN COMMENT '是否满意：true-满意，false-不满意，null-未评价',
    report_org_id VARCHAR(32) COMMENT '上报单位编号',
    report_org_name VARCHAR(100) COMMENT '上报单位名称',
    station_code VARCHAR(50) COMMENT '站点编号',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    create_user_id VARCHAR(32) COMMENT '创建人',
    update_user_id VARCHAR(32) COMMENT '更新人',
    available BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否有效'
);

COMMENT ON TABLE tbl_civil_service_report IS '服务群众上报表';

-- 索引建议保持不变
CREATE INDEX idx_civil_service_report_title ON tbl_civil_service_report (title);
CREATE INDEX idx_civil_service_report_report_time ON tbl_civil_service_report (report_time);
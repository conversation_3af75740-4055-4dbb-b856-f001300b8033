<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.sipsg.module.business.module.duty.mapper.BasePoliceEmpFamilyMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="cn.com.sipsg.module.business.module.duty.entity.BasePoliceEmpFamily">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="police_emp_id" property="policeEmpId" jdbcType="VARCHAR"/>
        <result column="relationship" property="relationship" jdbcType="VARCHAR"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>

        <result column="available" property="available" jdbcType="BOOLEAN"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="create_user_id" property="createUserId" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="update_user_id" property="updateUserId" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, police_emp_id, relationship, name, available, 
        create_time, create_user_id, update_time, update_user_id
    </sql>

    <!-- 根据警员ID查询家庭成员列表 -->
    <select id="selectByPoliceEmpId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tbl_base_police_emp_family
        WHERE police_emp_id = #{policeEmpId}
        AND available = true
        ORDER BY create_time ASC
    </select>

    <!-- 根据警员ID删除家庭成员（逻辑删除） -->
    <update id="deleteByPoliceEmpId">
        UPDATE tbl_base_police_emp_family
        SET available = false,
            update_time = CURRENT_TIMESTAMP
        WHERE police_emp_id = #{policeEmpId}
        AND available = true
    </update>

</mapper>
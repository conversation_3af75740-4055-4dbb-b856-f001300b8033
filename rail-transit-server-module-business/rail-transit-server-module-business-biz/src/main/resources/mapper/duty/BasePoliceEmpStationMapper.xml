<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.sipsg.module.business.module.duty.mapper.BasePoliceEmpStationMapper">

    <!-- 根据警员ID查询管辖站点ID列表 -->
    <select id="selectStationIdsByEmpId" resultType="java.lang.Long">
        SELECT station_id
        FROM base_police_emp_station
        WHERE emp_id = #{empId}
          AND available = 1
    </select>

    <!-- 根据站点ID查询管辖警员ID列表 -->
    <select id="selectEmpIdsByStationId" resultType="java.lang.String">
        SELECT emp_id
        FROM base_police_emp_station
        WHERE station_id = #{stationId}
          AND available = 1
    </select>

    <!-- 批量删除警员的站点关联 -->
    <delete id="deleteByEmpId">
        DELETE FROM base_police_emp_station
        WHERE emp_id = #{empId}
    </delete>

    <!-- 批量插入警员站点关联 -->
    <insert id="batchInsert">
        INSERT INTO base_police_emp_station (
            emp_id,
            station_id,
            available,
            create_time,
            create_user_id,
            update_time,
            update_user_id
        ) VALUES
        <foreach collection="relations" item="item" separator=",">
            (
                #{item.empId},
                #{item.stationId},
                #{item.available},
                #{item.createTime},
                #{item.createUserId},
                #{item.updateTime},
                #{item.updateUserId}
            )
        </foreach>
    </insert>

</mapper>
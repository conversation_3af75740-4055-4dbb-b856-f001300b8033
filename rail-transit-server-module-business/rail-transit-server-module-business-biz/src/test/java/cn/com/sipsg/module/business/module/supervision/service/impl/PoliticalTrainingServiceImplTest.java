package cn.com.sipsg.module.business.module.supervision.service.impl;

import cn.com.sipsg.common.security.core.LoginUser;
import cn.com.sipsg.common.security.core.util.SecurityUtils;
import cn.com.sipsg.module.business.module.duty.entity.BasePoliceEmp;
import cn.com.sipsg.module.business.module.duty.mapper.BasePoliceEmpMapper;
import cn.com.sipsg.module.business.module.supervision.entity.ExamTaskEmp;
import cn.com.sipsg.module.business.module.supervision.mapper.ExamTaskEmpMapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import org.junit.jupiter.api.*;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

@SpringBootTest
class PoliticalTrainingServiceImplTest {

    @Autowired
    private PoliticalTrainingServiceImpl politicalTrainingService;

    @Autowired
    private BasePoliceEmpMapper basePoliceEmpMapper;

    @Autowired
    private ExamTaskEmpMapper examTaskEmpMapper;

    private static MockedStatic<SecurityUtils> securityUtilsMockedStatic;

    @BeforeAll
    static void beforeAll() {
        // Mock静态方法
        securityUtilsMockedStatic = Mockito.mockStatic(SecurityUtils.class);
    }

    @AfterAll
    static void afterAll() {
        securityUtilsMockedStatic.close();
    }

    @BeforeEach
    void setUp() {
        // 清理历史测试数据
        basePoliceEmpMapper.delete(new QueryWrapper<BasePoliceEmp>().eq("auth_user_id", 2001L));
        examTaskEmpMapper.delete(new QueryWrapper<ExamTaskEmp>().eq("police_code", "P0001"));

        // 1. 构造一个普通警员档案
        BasePoliceEmp emp = new BasePoliceEmp();
        emp.setCode("P0001");
        emp.setName("测试警员");
        emp.setAvailable(true);
        emp.setAuthUserId("2001");
        basePoliceEmpMapper.insert(emp);

        System.out.println("插入警员id: " + emp.getId() + ", code: " + emp.getCode());

        // 2. 构造一条考试任务人员数据
        ExamTaskEmp examTaskEmp = new ExamTaskEmp();
        examTaskEmp.setId(String.valueOf(999L));
        examTaskEmp.setPoliceCode("P0001");
        examTaskEmp.setPoliceName("测试警员");
        examTaskEmp.setAvailable(true);
        examTaskEmp.setTaskId("10001");
        examTaskEmpMapper.insert(examTaskEmp);

        System.out.println("插入考试任务人员id: " + examTaskEmp.getId() + ", policeCode: " + examTaskEmp.getPoliceCode());

        // 3. Mock登录用户上下文
        LoginUser loginUser = new LoginUser();
        loginUser.setUserId("1");
        loginUser.setUsername("testuser");
        securityUtilsMockedStatic.when(SecurityUtils::getLoginUser).thenReturn(loginUser);
    }

    @Test
    void testGetCurrentUserRelatedTaskIds() {
        List<String> taskIds = politicalTrainingService.getCurrentUserRelatedTaskIds();
        Assertions.assertFalse(taskIds.isEmpty(), "普通用户应能查到自己的考试任务ID");
        Assertions.assertTrue(taskIds.contains("10001"), "应包含插入的考试任务ID");

        System.out.println("查到的taskIds: " + taskIds);
    }
}
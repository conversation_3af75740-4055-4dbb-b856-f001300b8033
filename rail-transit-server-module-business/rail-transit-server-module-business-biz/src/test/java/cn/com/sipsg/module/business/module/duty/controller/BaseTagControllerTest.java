package cn.com.sipsg.module.business.module.duty.controller;

import cn.com.sipsg.module.business.module.duty.pojo.dto.*;
import cn.com.sipsg.module.business.module.duty.pojo.vo.TagGroupVO;
import cn.com.sipsg.module.business.module.duty.service.BaseTagService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.Collections;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(MockitoExtension.class)
class BaseTagControllerTest {
    @Mock
    private BaseTagService baseTagService;
    @InjectMocks
    private BaseTagController baseTagController;
    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(baseTagController).build();
        objectMapper = new ObjectMapper();
    }

    @Test
    void testTagGroup() throws Exception {
        when(baseTagService.tagGroup()).thenReturn(Collections.singletonList(new TagGroupVO()));
        mockMvc.perform(get("/tag/tag/group"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").exists())
                .andExpect(jsonPath("$.data").isArray());
    }

    @Test
    void testAddTagGroup() throws Exception {
        TagGroupAddDTO dto = new TagGroupAddDTO();
        dto.setGroupName("测试分组");
        doNothing().when(baseTagService).addTagGroup(any(TagGroupAddDTO.class));
        mockMvc.perform(post("/tag/tag/group/add")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(dto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").exists());
    }

    @Test
    void testAddTag() throws Exception {
        TagAddDTO dto = new TagAddDTO();
        dto.setTagName("测试标签");
        dto.setGroupId("1");
        doNothing().when(baseTagService).addTag(any(TagAddDTO.class));
        mockMvc.perform(post("/tag/tag/add")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(dto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").exists());
    }

    @Test
    void testUpdateTagGroup() throws Exception {
        TagGroupUpdateDTO dto = new TagGroupUpdateDTO();
        dto.setTagId("1");
        dto.setGroupId("2");
        doNothing().when(baseTagService).updateTagGroup(any(TagGroupUpdateDTO.class));
        mockMvc.perform(post("/tag/tag/group/update")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(dto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").exists());
    }

    @Test
    void testBatchUpdateTagGroup() throws Exception {
        TagGroupBatchUpdateDTO dto = new TagGroupBatchUpdateDTO();
        dto.setTagGroupMappings(Collections.emptyList());
        doNothing().when(baseTagService).batchUpdateTagGroup(anyList());
        mockMvc.perform(post("/tag/tag/group/batch-update")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(dto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").exists());
    }

    @Test
    void testDeleteTag() throws Exception {
        TagDeleteDTO dto = new TagDeleteDTO();
        dto.setTagId("1");
        doNothing().when(baseTagService).deleteTag(any(TagDeleteDTO.class));
        mockMvc.perform(post("/tag/tag/delete")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(dto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").exists());
    }

    @Test
    void testDeleteTagGroup() throws Exception {
        TagGroupDeleteDTO dto = new TagGroupDeleteDTO();
        dto.setGroupId("1");
        doNothing().when(baseTagService).deleteTagGroup(any(TagGroupDeleteDTO.class));
        mockMvc.perform(post("/tag/tag/group/delete")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(dto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").exists());
    }
}
package cn.com.sipsg.module.business.module.supervision.controller;

import cn.com.sipsg.common.pojo.CommonResult;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.PoliceRiskCreateReqDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.PoliceRiskPageReqDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.PoliceRiskVO;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.RiskCountVO;
import cn.com.sipsg.module.business.module.supervision.service.PoliceRiskService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import cn.com.sipsg.common.web.core.handler.GlobalExceptionHandler;

/**
 * PoliceRiskController 接口测试类
 */
@ExtendWith(MockitoExtension.class)
class PoliceRiskControllerTest {

    @Mock
    private PoliceRiskService policeRiskService;

    @InjectMocks
    private PoliceRiskController policeRiskController;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(policeRiskController)
                .setControllerAdvice(new GlobalExceptionHandler())
                .build();
        objectMapper = new ObjectMapper();
        // 配置Jackson支持Java 8时间类型
        objectMapper.registerModule(new com.fasterxml.jackson.datatype.jsr310.JavaTimeModule());
        objectMapper.disable(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
    }

    @Test
    void testRiskPage_Success() throws Exception {
        // 准备测试数据
        PoliceRiskPageReqDTO reqDTO = new PoliceRiskPageReqDTO();
        reqDTO.setCurrent(1L);
        reqDTO.setSize(10L);
        reqDTO.setScope("all");
        reqDTO.setStatus("all");

        CommonPageVO<PoliceRiskVO> pageResult = new CommonPageVO<>();
        pageResult.setTotal(2L);
        pageResult.setCurrent(1L);
        pageResult.setSize(10L);
        pageResult.setRecords(Arrays.asList(
                createMockPoliceRiskVO("1"),
                createMockPoliceRiskVO("2")
        ));

        when(policeRiskService.pageRiskList(any(PoliceRiskPageReqDTO.class)))
                .thenReturn(pageResult);

        // 执行测试
        mockMvc.perform(post("/policeRisk/risk/page")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(reqDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.total").value(2))
                .andExpect(jsonPath("$.data.records").isArray())
                .andExpect(jsonPath("$.data.records.length()").value(2));

        // 验证Service方法被调用
        verify(policeRiskService).pageRiskList(any(PoliceRiskPageReqDTO.class));
    }

    @Test
    void testRiskPage_WithNullRequest() throws Exception {
        // 准备测试数据
        CommonPageVO<PoliceRiskVO> pageResult = new CommonPageVO<>();
        pageResult.setTotal(0L);
        pageResult.setCurrent(1L);
        pageResult.setSize(10L);
        pageResult.setRecords(Arrays.asList());

        // 允许参数为null的调用
        when(policeRiskService.pageRiskList(isNull())).thenReturn(pageResult);

        // 执行测试 - 不传请求体
        mockMvc.perform(post("/policeRisk/risk/page")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        // 验证Service方法被调用
        verify(policeRiskService).pageRiskList(isNull());
    }

    @Test
    void testRiskPage_WithFilters() throws Exception {
        // 准备测试数据
        PoliceRiskPageReqDTO reqDTO = new PoliceRiskPageReqDTO();
        reqDTO.setCurrent(1L);
        reqDTO.setSize(10L);
        reqDTO.setScope("org");
        reqDTO.setStatus("todo");
        reqDTO.setCreateUserName("张三");
        reqDTO.setCreateUserId("EMP001");
        reqDTO.setOrgId("1");
        reqDTO.setStartTime(LocalDateTime.now().minusDays(7));
        reqDTO.setEndTime(LocalDateTime.now());

        CommonPageVO<PoliceRiskVO> pageResult = new CommonPageVO<>();
        pageResult.setTotal(1L);
        pageResult.setCurrent(1L);
        pageResult.setSize(10L);
        pageResult.setRecords(Collections.singletonList(createMockPoliceRiskVO("1")));

        when(policeRiskService.pageRiskList(any(PoliceRiskPageReqDTO.class)))
                .thenReturn(pageResult);

        // 执行测试
        mockMvc.perform(post("/policeRisk/risk/page")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(reqDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.total").value(1));

        // 验证Service方法被调用
        verify(policeRiskService).pageRiskList(any(PoliceRiskPageReqDTO.class));
    }

    @Test
    void testOrgRiskStats_Success() throws Exception {
        // 准备测试数据
        RiskCountVO riskCountVO = new RiskCountVO();
        riskCountVO.setCheckPointCount(10);
        riskCountVO.setInspectionTaskCount(5);
        riskCountVO.setRiskHandleCount(3);

        when(policeRiskService.getOrgWeeklyRiskCount())
                .thenReturn(riskCountVO);

        // 执行测试
        mockMvc.perform(get("/policeRisk/risk/org-stats"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.checkPointCount").value(10))
                .andExpect(jsonPath("$.data.inspectionTaskCount").value(5))
                .andExpect(jsonPath("$.data.riskHandleCount").value(3));

        // 验证Service方法被调用
        verify(policeRiskService).getOrgWeeklyRiskCount();
    }

    @Test
    void testReportRisk_Success() throws Exception {
        // 准备测试数据
        PoliceRiskCreateReqDTO reqDTO = new PoliceRiskCreateReqDTO();
        reqDTO.setStationId("ST001");
        reqDTO.setContent("发现安全隐患");
        reqDTO.setType("01");
        reqDTO.setDescription("详细描述");
        reqDTO.setRiskUrl("http://example.com/risk.jpg");
        reqDTO.setHasChange(0);

        when(policeRiskService.createRisk(any(PoliceRiskCreateReqDTO.class)))
                .thenReturn(true);

        // 执行测试
        mockMvc.perform(post("/policeRisk/risk/report")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(reqDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").value(true));

        // 验证Service方法被调用
        verify(policeRiskService).createRisk(any(PoliceRiskCreateReqDTO.class));
    }

    @Test
    void testReportRisk_WithChangeInfo() throws Exception {
        // 准备测试数据 - 包含整改信息
        PoliceRiskCreateReqDTO reqDTO = new PoliceRiskCreateReqDTO();
        reqDTO.setStationId("ST002");
        reqDTO.setContent("安全隐患已整改");
        reqDTO.setType("02");
        reqDTO.setDescription("整改完成");
        reqDTO.setRiskUrl("http://example.com/before.jpg");
        reqDTO.setHasChange(1);

        when(policeRiskService.createRisk(any(PoliceRiskCreateReqDTO.class)))
                .thenReturn(true);

        // 执行测试
        mockMvc.perform(post("/policeRisk/risk/report")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(reqDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").value(true));

        // 验证Service方法被调用
        verify(policeRiskService).createRisk(any(PoliceRiskCreateReqDTO.class));
    }

    @Test
    void testRiskPage_ServiceException() throws Exception {
        // 准备测试数据
        PoliceRiskPageReqDTO reqDTO = new PoliceRiskPageReqDTO();
        reqDTO.setCurrent(1L);
        reqDTO.setSize(10L);

        when(policeRiskService.pageRiskList(any(PoliceRiskPageReqDTO.class)))
                .thenThrow(new RuntimeException("数据库连接失败"));

        // 执行测试
        mockMvc.perform(post("/policeRisk/risk/page")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(reqDTO)))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.msg").value("系统异常"));

        // 验证Service方法被调用
        verify(policeRiskService).pageRiskList(any(PoliceRiskPageReqDTO.class));
    }

    @Test
    void testOrgRiskStats_ServiceException() throws Exception {
        when(policeRiskService.getOrgWeeklyRiskCount())
                .thenThrow(new RuntimeException("统计服务异常"));

        // 执行测试
        mockMvc.perform(get("/policeRisk/risk/org-stats"))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.msg").value("系统异常"));

        // 验证Service方法被调用
        verify(policeRiskService).getOrgWeeklyRiskCount();
    }

    @Test
    void testReportRisk_ServiceException() throws Exception {
        // 准备测试数据
        PoliceRiskCreateReqDTO reqDTO = new PoliceRiskCreateReqDTO();
        reqDTO.setStationId("ST001");
        reqDTO.setContent("测试内容");

        when(policeRiskService.createRisk(any(PoliceRiskCreateReqDTO.class)))
                .thenThrow(new RuntimeException("保存失败"));

        // 执行测试
        mockMvc.perform(post("/policeRisk/risk/report")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(reqDTO)))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.msg").value("系统异常"));

        // 验证Service方法被调用
        verify(policeRiskService).createRisk(any(PoliceRiskCreateReqDTO.class));
    }

    @Test
    void testRiskPage_InvalidJson() throws Exception {
        // 执行测试 - 发送无效的JSON
        mockMvc.perform(post("/policeRisk/risk/page")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("\"invalid\" \"json\""))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testReportRisk_InvalidJson() throws Exception {
        // 执行测试 - 发送无效的JSON
        mockMvc.perform(post("/policeRisk/risk/report")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("\"invalid\" \"json\""))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testRiskPage_EmptyPageResult() throws Exception {
        // 准备测试数据
        PoliceRiskPageReqDTO reqDTO = new PoliceRiskPageReqDTO();
        reqDTO.setCurrent(1L);
        reqDTO.setSize(10L);

        CommonPageVO<PoliceRiskVO> pageResult = new CommonPageVO<>();
        pageResult.setTotal(0L);
        pageResult.setCurrent(1L);
        pageResult.setSize(10L);
        pageResult.setRecords(Arrays.asList());

        when(policeRiskService.pageRiskList(any(PoliceRiskPageReqDTO.class)))
                .thenReturn(pageResult);

        // 执行测试
        mockMvc.perform(post("/policeRisk/risk/page")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(reqDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.total").value(0))
                .andExpect(jsonPath("$.data.records").isArray())
                .andExpect(jsonPath("$.data.records.length()").value(0));

        // 验证Service方法被调用
        verify(policeRiskService).pageRiskList(any(PoliceRiskPageReqDTO.class));
    }

    @Test
    void testReportRisk_ReturnFalse() throws Exception {
        // 准备测试数据
        PoliceRiskCreateReqDTO reqDTO = new PoliceRiskCreateReqDTO();
        reqDTO.setStationId("ST001");
        reqDTO.setContent("测试内容");

        when(policeRiskService.createRisk(any(PoliceRiskCreateReqDTO.class)))
                .thenReturn(false);

        // 执行测试
        mockMvc.perform(post("/policeRisk/risk/report")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(reqDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").value(false));

        // 验证Service方法被调用
        verify(policeRiskService).createRisk(any(PoliceRiskCreateReqDTO.class));
    }

    @Test
    void testRiskPage_WithStartEndTime() throws Exception {
        // 准备测试数据 - 使用startTime和endTime
        PoliceRiskPageReqDTO reqDTO = new PoliceRiskPageReqDTO();
        reqDTO.setCurrent(1L);
        reqDTO.setSize(10L);
        reqDTO.setStartTime(LocalDateTime.of(2024, 1, 1, 0, 0, 0));
        reqDTO.setEndTime(LocalDateTime.of(2024, 12, 31, 23, 59, 59));

        CommonPageVO<PoliceRiskVO> pageResult = new CommonPageVO<>();
        pageResult.setTotal(1L);
        pageResult.setCurrent(1L);
        pageResult.setSize(10L);
        pageResult.setRecords(Collections.singletonList(createMockPoliceRiskVO("1")));

        when(policeRiskService.pageRiskList(any(PoliceRiskPageReqDTO.class)))
                .thenReturn(pageResult);

        // 执行测试
        mockMvc.perform(post("/policeRisk/risk/page")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(reqDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.total").value(1));

        verify(policeRiskService).pageRiskList(any(PoliceRiskPageReqDTO.class));
    }

    @Test
    void testRiskPage_WithOnlyStartTime() throws Exception {
        // 测试只设置开始时间
        PoliceRiskPageReqDTO reqDTO = new PoliceRiskPageReqDTO();
        reqDTO.setCurrent(1L);
        reqDTO.setSize(10L);
        reqDTO.setStartTime(LocalDateTime.of(2024, 1, 1, 0, 0, 0));

        CommonPageVO<PoliceRiskVO> pageResult = new CommonPageVO<>();
        pageResult.setTotal(0L);
        pageResult.setCurrent(1L);
        pageResult.setSize(10L);
        pageResult.setRecords(Arrays.asList());

        when(policeRiskService.pageRiskList(any(PoliceRiskPageReqDTO.class)))
                .thenReturn(pageResult);

        mockMvc.perform(post("/policeRisk/risk/page")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(reqDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        verify(policeRiskService).pageRiskList(any(PoliceRiskPageReqDTO.class));
    }

    @Test
    void testRiskPage_WithOnlyEndTime() throws Exception {
        // 测试只设置结束时间
        PoliceRiskPageReqDTO reqDTO = new PoliceRiskPageReqDTO();
        reqDTO.setCurrent(1L);
        reqDTO.setSize(10L);
        reqDTO.setEndTime(LocalDateTime.of(2024, 2, 28, 23, 59, 59));

        CommonPageVO<PoliceRiskVO> pageResult = new CommonPageVO<>();
        pageResult.setTotal(0L);
        pageResult.setCurrent(1L);
        pageResult.setSize(10L);
        pageResult.setRecords(Arrays.asList());

        when(policeRiskService.pageRiskList(any(PoliceRiskPageReqDTO.class)))
                .thenReturn(pageResult);

        mockMvc.perform(post("/policeRisk/risk/page")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(reqDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        verify(policeRiskService).pageRiskList(any(PoliceRiskPageReqDTO.class));
    }

    @Test
    void testRiskPage_WithTimeRangeFiltering() throws Exception {
        // 测试时间范围过滤
        PoliceRiskPageReqDTO reqDTO = new PoliceRiskPageReqDTO();
        reqDTO.setCurrent(1L);
        reqDTO.setSize(10L);
        reqDTO.setStartTime(LocalDateTime.of(2024, 6, 1, 0, 0, 0));
        reqDTO.setEndTime(LocalDateTime.of(2024, 6, 30, 23, 59, 59));

        CommonPageVO<PoliceRiskVO> pageResult = new CommonPageVO<>();
        pageResult.setTotal(0L);
        pageResult.setCurrent(1L);
        pageResult.setSize(10L);
        pageResult.setRecords(Arrays.asList());

        when(policeRiskService.pageRiskList(any(PoliceRiskPageReqDTO.class)))
                .thenReturn(pageResult);

        mockMvc.perform(post("/policeRisk/risk/page")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(reqDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        verify(policeRiskService).pageRiskList(any(PoliceRiskPageReqDTO.class));
    }

    @Test
    void testRiskPage_WithAllQueryConditions() throws Exception {
        // 测试所有查询条件的组合
        PoliceRiskPageReqDTO reqDTO = new PoliceRiskPageReqDTO();
        reqDTO.setCurrent(1L);
        reqDTO.setSize(20L);
        reqDTO.setScope("org");
        reqDTO.setType("SAFETY");
        reqDTO.setCreateUserId("123456");
        reqDTO.setCreateUserName("张三");
        reqDTO.setOrgId("ORG001");
        reqDTO.setStatus("todo");
        reqDTO.setCheckPlace("地铁站");
        reqDTO.setRectifyStartTime(LocalDateTime.of(2024, 1, 15, 9, 0, 0));
        reqDTO.setRectifyEndTime(LocalDateTime.of(2024, 1, 30, 18, 0, 0));
        reqDTO.setStartTime(LocalDateTime.of(2024, 1, 1, 0, 0, 0));
        reqDTO.setEndTime(LocalDateTime.of(2024, 12, 31, 23, 59, 59));

        CommonPageVO<PoliceRiskVO> pageResult = new CommonPageVO<>();
        pageResult.setTotal(5L);
        pageResult.setCurrent(1L);
        pageResult.setSize(20L);
        pageResult.setRecords(Arrays.asList(
                createMockPoliceRiskVO("1"),
                createMockPoliceRiskVO("2")
        ));

        when(policeRiskService.pageRiskList(any(PoliceRiskPageReqDTO.class)))
                .thenReturn(pageResult);

        mockMvc.perform(post("/policeRisk/risk/page")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(reqDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.total").value(5))
                .andExpect(jsonPath("$.data.records.length()").value(2));

        verify(policeRiskService).pageRiskList(any(PoliceRiskPageReqDTO.class));
    }

    @Test
    void testRiskPage_WithEmptyStringFilters() throws Exception {
        // 测试空字符串和空白字符串的处理
        PoliceRiskPageReqDTO reqDTO = new PoliceRiskPageReqDTO();
        reqDTO.setCurrent(1L);
        reqDTO.setSize(10L);
        reqDTO.setType("");
        reqDTO.setCreateUserId("   ");
        reqDTO.setCreateUserName("");
        reqDTO.setCheckPlace("   ");
        reqDTO.setRectifyResult("");

        CommonPageVO<PoliceRiskVO> pageResult = new CommonPageVO<>();
        pageResult.setTotal(0L);
        pageResult.setCurrent(1L);
        pageResult.setSize(10L);
        pageResult.setRecords(Arrays.asList());

        when(policeRiskService.pageRiskList(any(PoliceRiskPageReqDTO.class)))
                .thenReturn(pageResult);

        mockMvc.perform(post("/policeRisk/risk/page")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(reqDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        verify(policeRiskService).pageRiskList(any(PoliceRiskPageReqDTO.class));
    }

    @Test
    void testRiskPage_WithSpecialOrgId() throws Exception {
        // 测试特殊orgId值（"0"应该被忽略）
        PoliceRiskPageReqDTO reqDTO = new PoliceRiskPageReqDTO();
        reqDTO.setCurrent(1L);
        reqDTO.setSize(10L);
        reqDTO.setOrgId("0");

        CommonPageVO<PoliceRiskVO> pageResult = new CommonPageVO<>();
        pageResult.setTotal(0L);
        pageResult.setCurrent(1L);
        pageResult.setSize(10L);
        pageResult.setRecords(Arrays.asList());

        when(policeRiskService.pageRiskList(any(PoliceRiskPageReqDTO.class)))
                .thenReturn(pageResult);

        mockMvc.perform(post("/policeRisk/risk/page")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(reqDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        verify(policeRiskService).pageRiskList(any(PoliceRiskPageReqDTO.class));
    }

    @Test
    void testRiskPage_WithRectifyResultAndStatusConflict() throws Exception {
        // 测试整改结果与状态筛选冲突（整改结果应该被忽略）
        PoliceRiskPageReqDTO reqDTO = new PoliceRiskPageReqDTO();
        reqDTO.setCurrent(1L);
        reqDTO.setSize(10L);
        reqDTO.setRectifyResult("已整改");
        reqDTO.setStatus("todo"); // 与整改结果冲突

        CommonPageVO<PoliceRiskVO> pageResult = new CommonPageVO<>();
        pageResult.setTotal(0L);
        pageResult.setCurrent(1L);
        pageResult.setSize(10L);
        pageResult.setRecords(Arrays.asList());

        when(policeRiskService.pageRiskList(any(PoliceRiskPageReqDTO.class)))
                .thenReturn(pageResult);

        mockMvc.perform(post("/policeRisk/risk/page")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(reqDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        verify(policeRiskService).pageRiskList(any(PoliceRiskPageReqDTO.class));
    }

    @Test
    void testRiskPage_WithRectifyResultOnly() throws Exception {
        // 测试只有整改结果筛选（状态为all或null）
        PoliceRiskPageReqDTO reqDTO = new PoliceRiskPageReqDTO();
        reqDTO.setCurrent(1L);
        reqDTO.setSize(10L);
        reqDTO.setRectifyResult("已整改");
        reqDTO.setStatus("all");

        CommonPageVO<PoliceRiskVO> pageResult = new CommonPageVO<>();
        pageResult.setTotal(0L);
        pageResult.setCurrent(1L);
        pageResult.setSize(10L);
        pageResult.setRecords(Arrays.asList());

        when(policeRiskService.pageRiskList(any(PoliceRiskPageReqDTO.class)))
                .thenReturn(pageResult);

        mockMvc.perform(post("/policeRisk/risk/page")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(reqDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        verify(policeRiskService).pageRiskList(any(PoliceRiskPageReqDTO.class));
    }

    @Test
    void testRiskPage_WithBoundaryPagination() throws Exception {
        // 测试边界分页参数
        PoliceRiskPageReqDTO reqDTO = new PoliceRiskPageReqDTO();
        reqDTO.setCurrent(0L); // 边界值
        reqDTO.setSize(0L); // 边界值

        CommonPageVO<PoliceRiskVO> pageResult = new CommonPageVO<>();
        pageResult.setTotal(0L);
        pageResult.setCurrent(1L);
        pageResult.setSize(10L);
        pageResult.setRecords(Arrays.asList());

        when(policeRiskService.pageRiskList(any(PoliceRiskPageReqDTO.class)))
                .thenReturn(pageResult);

        mockMvc.perform(post("/policeRisk/risk/page")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(reqDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        verify(policeRiskService).pageRiskList(any(PoliceRiskPageReqDTO.class));
    }

    @Test
    void testRiskPage_WithDifferentStatusValues() throws Exception {
        // 测试不同的状态值
        String[] statusValues = {"todo", "done", "all", null};
        
        for (String status : statusValues) {
            PoliceRiskPageReqDTO reqDTO = new PoliceRiskPageReqDTO();
            reqDTO.setCurrent(1L);
            reqDTO.setSize(10L);
            reqDTO.setStatus(status);

            CommonPageVO<PoliceRiskVO> pageResult = new CommonPageVO<>();
            pageResult.setTotal(0L);
            pageResult.setCurrent(1L);
            pageResult.setSize(10L);
            pageResult.setRecords(Arrays.asList());

            when(policeRiskService.pageRiskList(any(PoliceRiskPageReqDTO.class)))
                    .thenReturn(pageResult);

            mockMvc.perform(post("/policeRisk/risk/page")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(reqDTO)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(200));

            verify(policeRiskService, atLeastOnce()).pageRiskList(any(PoliceRiskPageReqDTO.class));
        }
    }

    /**
     * 创建模拟的PoliceRiskVO对象
     */
    private PoliceRiskVO createMockPoliceRiskVO(String id) {
        PoliceRiskVO vo = new PoliceRiskVO();
        vo.setId(id);
        vo.setCode(String.valueOf(id));
        vo.setStationCode("ST" + id);
        vo.setStationName("测试站点" + id);
        vo.setCreateUserId(String.valueOf(id));
        vo.setCreateUserName("测试用户" + id);
        vo.setType("01");
        vo.setTypeName("安全隐患");
        vo.setCreateTime(LocalDateTime.now());
        vo.setFixLastTime(LocalDateTime.now().plusDays(7));
        vo.setChangeResult("未整改");
        return vo;
    }
}
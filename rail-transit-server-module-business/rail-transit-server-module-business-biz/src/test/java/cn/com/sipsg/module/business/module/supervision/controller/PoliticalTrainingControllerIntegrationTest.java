package cn.com.sipsg.module.business.module.supervision.controller;

import cn.com.sipsg.module.business.module.supervision.pojo.vo.QuestionBankVO;
import cn.com.sipsg.module.business.module.supervision.service.PoliticalTrainingService;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.nio.charset.StandardCharsets;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.multipart;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * PoliticalTrainingController 接口测试类
 */
@ExtendWith(MockitoExtension.class)
class PoliticalTrainingControllerTest {

    @Mock
    private PoliticalTrainingService politicalTrainingService;

    @InjectMocks
    private PoliticalTrainingController politicalTrainingController;

    private MockMvc mockMvc;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(politicalTrainingController).build();
    }

    @Test
    void testUploadQuestionBank() throws Exception {
        // 构造Mock文件
        MockMultipartFile file = new MockMultipartFile(
                "file",
                "test.xlsx",
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                "excel-content".getBytes(StandardCharsets.UTF_8)
        );
        String name = "测试题库";
        String description = "测试描述";

        // Mock service 返回导入题目数量
        when(politicalTrainingService.uploadQuestionBank(any(), anyString(), any())).thenReturn(10);

        mockMvc.perform(multipart("/political/training/question-bank/upload")
                        .file(file)
                        .param("name", name)
                        .param("description", description)
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").exists())
                .andExpect(jsonPath("$.data").value(10));
    }

    @Test
    void testGetQuestionBankDetail() throws Exception {
        String questionBankId = "1";
        QuestionBankVO vo = new QuestionBankVO();
        vo.setId(questionBankId);
        vo.setName("测试题库");
        vo.setFileUrl("http://localhost/files/test.xlsx");
        vo.setFileName("test.xlsx");
        // Mock service 返回VO
        when(politicalTrainingService.getQuestionBankDetail(eq(questionBankId))).thenReturn(vo);

        mockMvc.perform(get("/political/training/question-bank/detail")
                        .param("questionBankId", questionBankId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").exists())
                .andExpect(jsonPath("$.data.id").value(questionBankId))
                .andExpect(jsonPath("$.data.fileUrl").value("http://localhost/files/test.xlsx"));
    }

    @Test
    void testUploadQuestionBankWithRealFile() throws Exception {
        // 使用本地真实文件
        String filePath = "C:/Users/<USER>/Desktop/考试模板.xls";
        java.io.FileInputStream fis = new java.io.FileInputStream(filePath);
        MockMultipartFile file = new MockMultipartFile(
                "file",
                "考试模板.xls",
                "application/vnd.ms-excel",
                fis
        );
        String name = "真实文件题库";
        String description = "真实文件测试";

        // Mock service 返回导入题目数量
        when(politicalTrainingService.uploadQuestionBank(any(), anyString(), any())).thenReturn(10);

        mockMvc.perform(multipart("/political/training/question-bank/upload")
                        .file(file)
                        .param("name", name)
                        .param("description", description)
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").exists())
                .andExpect(jsonPath("$.data").value(10));
        // 实际集成测试时，可在此处断言文件是否真实存在于服务器指定目录
        // File savedFile = new File("D:/upload/考试模板.xls");
        // Assertions.assertTrue(savedFile.exists(), "上传的文件应真实存在于服务器指定目录");
    }

    @Test
    void testGetQuestionBankDetailWithUrlCheck() throws Exception {
        String questionBankId = "2";
        QuestionBankVO vo = new QuestionBankVO();
        vo.setId(questionBankId);
        vo.setName("测试题库");
        vo.setFileUrl("http://localhost/files/考试模板.xls");
        vo.setFileName("考试模板.xls");
        // Mock service 返回VO
        when(politicalTrainingService.getQuestionBankDetail(eq(questionBankId))).thenReturn(vo);

        mockMvc.perform(get("/political/training/question-bank/detail")
                        .param("questionBankId", questionBankId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").exists())
                .andExpect(jsonPath("$.data.id").value(questionBankId))
                .andExpect(jsonPath("$.data.fileUrl").isNotEmpty())
                .andExpect(jsonPath("$.data.fileUrl").value(org.hamcrest.Matchers.anyOf(
                        org.hamcrest.Matchers.endsWith(".xls"),
                        org.hamcrest.Matchers.endsWith(".xlsx")
                )))
                .andExpect(jsonPath("$.data.fileUrl").value(org.hamcrest.Matchers.startsWith("http")));
    }
}

@SpringBootTest
@AutoConfigureMockMvc
public class PoliticalTrainingControllerIntegrationTest {
    @Autowired
    private MockMvc mockMvc;

    @Test
    void integrationTestUploadAndDetailWithRealFile() throws Exception {
        // 1. 上传真实文件
        String filePath = "C:/Users/<USER>/Desktop/考试模板.xls";
        MockMultipartFile file = new MockMultipartFile(
                "file",
                "考试模板.xls",
                "application/vnd.ms-excel",
                new java.io.FileInputStream(filePath)
        );
        String name = "集成测试题库";
        String description = "集成测试";

        MvcResult uploadResult = mockMvc.perform(multipart("/political/training/question-bank/upload")
                        .file(file)
                        .param("name", name)
                        .param("description", description))
                .andExpect(status().isOk())
                .andReturn();

        String uploadResp = uploadResult.getResponse().getContentAsString();
        System.out.println("上传返回: " + uploadResp);

        // 2. 解析返回的题库ID或数量（根据实际返回结构调整）
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode uploadJson = objectMapper.readTree(uploadResp);
        // 假设返回data为题库ID或题目数量
        Long questionBankId = uploadJson.path("data").asLong();
        // 如果返回的是题目数量，需要通过其他方式查到最新题库ID
        // TODO: 如有需要可补充数据库查询或接口查询

        // 3. 查询详情接口
        MvcResult detailResult = mockMvc.perform(get("/political/training/question-bank/detail")
                        .param("questionBankId", questionBankId.toString()))
                .andExpect(status().isOk())
                .andReturn();

        String detailResp = detailResult.getResponse().getContentAsString();
        System.out.println("详情返回: " + detailResp);

        JsonNode detailJson = objectMapper.readTree(detailResp);
        String fileUrl = detailJson.path("data").path("fileUrl").asText();
        org.junit.jupiter.api.Assertions.assertNotNull(fileUrl, "fileUrl不能为空");
        org.junit.jupiter.api.Assertions.assertTrue(fileUrl.endsWith(".xls") || fileUrl.endsWith(".xlsx"), "fileUrl应为Excel文件");
        org.junit.jupiter.api.Assertions.assertTrue(fileUrl.startsWith("http"), "fileUrl应为http(s)开头");

        // 4. （可选）根据fileUrl推断本地存储路径，断言文件真实存在（如fileUrl为http://localhost/files/考试模板.xls）
        // String localPath = "D:/upload/考试模板.xls"; // 根据实际配置调整
        // File savedFile = new File(localPath);
        // org.junit.jupiter.api.Assertions.assertTrue(savedFile.exists(), "上传的文件应真实存在于服务器指定目录");
    }
} 
package cn.com.sipsg.module.business.module.supervision.service.impl;

import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.module.business.module.common.util.OrgRecursiveUtils;
import cn.com.sipsg.module.business.module.common.util.UserOrgUtils;
import cn.com.sipsg.module.business.module.duty.entity.BasePoliceEmp;
import cn.com.sipsg.module.business.module.duty.mapper.BasePoliceEmpMapper;
import cn.com.sipsg.module.business.module.duty.mapper.BaseStationMapper;
import cn.com.sipsg.module.business.module.supervision.entity.PoliceRisk;
import cn.com.sipsg.module.business.module.supervision.mapper.PoliceRiskMapper;
import cn.com.sipsg.module.business.module.supervision.mapper.SecretInvestigationTaskMapper;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.PoliceRiskPageReqDTO;
import cn.com.sipsg.module.business.module.supervision.pojo.vo.PoliceRiskVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * PoliceRiskServiceImpl 数据权限管理测试
 */
@ExtendWith(MockitoExtension.class)
class PoliceRiskServiceImplTest {

    @Mock
    private PoliceRiskMapper policeRiskMapper;

    @Mock
    private BasePoliceEmpMapper basePoliceEmpMapper;

    @Mock
    private BaseStationMapper baseStationMapper;

    @Mock
    private UserOrgUtils userOrgUtils;

    @Mock
    private SecretInvestigationTaskMapper secretInvestigationTaskMapper;

    @Mock
    private OrgRecursiveUtils orgRecursiveUtils;

    @InjectMocks
    private PoliceRiskServiceImpl policeRiskService;

    @BeforeEach
    void setUp() {
        // 设置当前用户组织ID
        when(userOrgUtils.getCurrentUserOrgId()).thenReturn("1");
    }

    @Test
    void testPageRiskList_AdminAllScope_ShouldReturnAllRisks() {
        // 模拟管理员权限
        try (MockedStatic<cn.com.sipsg.common.security.core.util.SecurityUtils> securityUtilsMock = 
                mockStatic(cn.com.sipsg.common.security.core.util.SecurityUtils.class)) {
            securityUtilsMock.when(cn.com.sipsg.common.security.core.util.SecurityUtils::isSuperAdmin)
                    .thenReturn(true);

            // 准备测试数据
            PoliceRiskPageReqDTO reqDTO = new PoliceRiskPageReqDTO();
            reqDTO.setScope("all");
            reqDTO.setCurrent(1L);
            reqDTO.setSize(10L);

            // 模拟分页结果
            Page<PoliceRisk> page = new Page<>(1, 10);
            page.setTotal(2);
            page.setRecords(Arrays.asList(
                    createMockPoliceRisk("1", "1"),
                    createMockPoliceRisk("2", "2")
            ));

            when(policeRiskMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
                    .thenReturn(page);

            // 执行测试
            CommonPageVO<PoliceRiskVO> result = policeRiskService.pageRiskList(reqDTO);

            // 验证结果
            assertNotNull(result);
            assertEquals(2L, result.getTotal());
            assertEquals(2, result.getRecords().size());

            // 验证管理员查询时没有添加组织限制条件
            verify(policeRiskMapper).selectPage(any(Page.class), any(LambdaQueryWrapper.class));
        }
    }

    @Test
    void testPageRiskList_NonAdminAllScope_ShouldReturnOrgRisks() {
        // 模拟普通用户权限
        try (MockedStatic<cn.com.sipsg.common.security.core.util.SecurityUtils> securityUtilsMock = 
                mockStatic(cn.com.sipsg.common.security.core.util.SecurityUtils.class)) {
            securityUtilsMock.when(cn.com.sipsg.common.security.core.util.SecurityUtils::isSuperAdmin)
                    .thenReturn(false);

            // 准备测试数据
            PoliceRiskPageReqDTO reqDTO = new PoliceRiskPageReqDTO();
            reqDTO.setScope("all");
            reqDTO.setCurrent(1L);
            reqDTO.setSize(10L);

            // 模拟组织及下级组织ID
            List<String> orgIds = Arrays.asList("1", "2", "3");
            when(orgRecursiveUtils.getOrgAndChildrenIds("1")).thenReturn(orgIds);

            // 模拟警员列表
            List<BasePoliceEmp> emps = Arrays.asList(
                    createMockBasePoliceEmp("1"),
                    createMockBasePoliceEmp("2")
            );
            when(basePoliceEmpMapper.selectList(any(LambdaQueryWrapper.class)))
                    .thenReturn(emps);

            // 模拟分页结果
            Page<PoliceRisk> page = new Page<>(1, 10);
            page.setTotal(1);
            page.setRecords(Arrays.asList(createMockPoliceRisk("1", "1")));

            when(policeRiskMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
                    .thenReturn(page);

            // 执行测试
            CommonPageVO<PoliceRiskVO> result = policeRiskService.pageRiskList(reqDTO);

            // 验证结果
            assertNotNull(result);
            assertEquals(1L, result.getTotal());
            assertEquals(1, result.getRecords().size());

            // 验证普通用户查询时添加了组织限制条件
            verify(basePoliceEmpMapper).selectList(any(LambdaQueryWrapper.class));
            verify(policeRiskMapper).selectPage(any(Page.class), any(LambdaQueryWrapper.class));
        }
    }

    @Test
    void testPageRiskList_OrgScope_ShouldReturnOrgRisks() {
        // 模拟普通用户权限
        try (MockedStatic<cn.com.sipsg.common.security.core.util.SecurityUtils> securityUtilsMock = 
                mockStatic(cn.com.sipsg.common.security.core.util.SecurityUtils.class)) {
            securityUtilsMock.when(cn.com.sipsg.common.security.core.util.SecurityUtils::isSuperAdmin)
                    .thenReturn(false);

            // 准备测试数据
            PoliceRiskPageReqDTO reqDTO = new PoliceRiskPageReqDTO();
            reqDTO.setScope("org");
            reqDTO.setCurrent(1L);
            reqDTO.setSize(10L);

            // 模拟组织及下级组织ID
            List<String> orgIds = Arrays.asList("1", "2", "3");
            when(orgRecursiveUtils.getOrgAndChildrenIds("1")).thenReturn(orgIds);

            // 模拟警员列表
            List<BasePoliceEmp> emps = Arrays.asList(
                    createMockBasePoliceEmp("1"),
                    createMockBasePoliceEmp("2")
            );
            when(basePoliceEmpMapper.selectList(any(LambdaQueryWrapper.class)))
                    .thenReturn(emps);

            // 模拟分页结果
            Page<PoliceRisk> page = new Page<>(1, 10);
            page.setTotal(1);
            page.setRecords(Arrays.asList(createMockPoliceRisk("1", "1")));

            when(policeRiskMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
                    .thenReturn(page);

            // 执行测试
            CommonPageVO<PoliceRiskVO> result = policeRiskService.pageRiskList(reqDTO);

            // 验证结果
            assertNotNull(result);
            assertEquals(1L, result.getTotal());
            assertEquals(1, result.getRecords().size());

            // 验证本单位隐患查询时添加了组织限制条件
            verify(basePoliceEmpMapper).selectList(any(LambdaQueryWrapper.class));
            verify(policeRiskMapper).selectPage(any(Page.class), any(LambdaQueryWrapper.class));
        }
    }

    @Test
    void testPageRiskList_NoEmployees_ShouldReturnEmpty() {
        // 模拟普通用户权限
        try (MockedStatic<cn.com.sipsg.common.security.core.util.SecurityUtils> securityUtilsMock = 
                mockStatic(cn.com.sipsg.common.security.core.util.SecurityUtils.class)) {
            securityUtilsMock.when(cn.com.sipsg.common.security.core.util.SecurityUtils::isSuperAdmin)
                    .thenReturn(false);

            // 准备测试数据
            PoliceRiskPageReqDTO reqDTO = new PoliceRiskPageReqDTO();
            reqDTO.setScope("org");
            reqDTO.setCurrent(1L);
            reqDTO.setSize(10L);

            // 模拟组织及下级组织ID
            List<String> orgIds = Arrays.asList("1", "2", "3");
            when(orgRecursiveUtils.getOrgAndChildrenIds("1")).thenReturn(orgIds);
            // 模拟没有警员
            when(basePoliceEmpMapper.selectList(any(LambdaQueryWrapper.class)))
                    .thenReturn(Collections.emptyList());

            // 执行测试
            CommonPageVO<PoliceRiskVO> result = policeRiskService.pageRiskList(reqDTO);

            // 验证结果
            assertNotNull(result);
            assertEquals(0L, result.getTotal());
            assertEquals(0, result.getRecords().size());

            // 验证没有调用分页查询
            verify(policeRiskMapper, never()).selectPage(any(Page.class), any(LambdaQueryWrapper.class));
        }
    }

    private PoliceRisk createMockPoliceRisk(String id, String createUserId) {
        PoliceRisk risk = new PoliceRisk();
        risk.setId(id);
        risk.setCreateUserId(createUserId);
        risk.setAvailable(true);
        return risk;
    }

    private BasePoliceEmp createMockBasePoliceEmp(String authUserId) {
        BasePoliceEmp emp = new BasePoliceEmp();
        emp.setAuthUserId(authUserId);
        emp.setAvailable(true);
        emp.setName("测试用户" + authUserId);
        return emp;
    }
} 
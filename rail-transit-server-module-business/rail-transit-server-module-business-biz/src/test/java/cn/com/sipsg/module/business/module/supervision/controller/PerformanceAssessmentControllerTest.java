package cn.com.sipsg.module.business.module.supervision.controller;

import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.module.business.module.supervision.entity.PersonnelAttention;
import cn.com.sipsg.module.business.module.supervision.pojo.dto.*;
import cn.com.sipsg.module.business.module.supervision.service.PerformanceScoreService;
import cn.com.sipsg.module.business.module.supervision.service.StatisticsCompreService;
import cn.com.sipsg.module.business.module.supervision.service.StatisticsInvestigationService;
import cn.com.sipsg.module.business.module.supervision.service.StatisticsTraceService;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(MockitoExtension.class)
class PerformanceAssessmentControllerTest {
    @Mock
    private PerformanceScoreService performanceScoreService;
    @Mock
    private StatisticsCompreService statisticsCompreService;
    @Mock
    private StatisticsTraceService statisticsTraceService;
    @Mock
    private StatisticsInvestigationService statisticsInvestigationService;
    @InjectMocks
    private PerformanceAssessmentController controller;
    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(controller).build();
        objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule()); // 注册 Java 8 时间模块，支持 LocalDateTime
    }

    @Test
    void testPageStatisticsCompare() throws Exception {
        when(statisticsCompreService.pageStatisticsCompareByCondition(any(), any(), any(), anyInt(), anyInt()))
                .thenReturn(new CommonPageVO<>());
        StatisticsComparePageReqDTO dto = new StatisticsComparePageReqDTO();
        mockMvc.perform(post("/performance/assessment/statistics-compare/query")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(dto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").exists());
    }

    @Test
    void testPagePatrolForCurrentUser_allNull() throws Exception {
        // startTime 和 endTime 都为 null
        when(statisticsCompreService.pagePatrolForCurrentUser(any(PatrolPageReqDTO.class)))
                .thenReturn(new CommonPageVO<>());
        PatrolPageReqDTO dto = new PatrolPageReqDTO();
        mockMvc.perform(post("/performance/assessment/patrol/page")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(dto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").exists());
    }

    @Test
    void testPagePatrolForCurrentUser_withTime() throws Exception {
        // startTime 不为 null
        when(statisticsCompreService.pagePatrolForCurrentUser(any(PatrolPageReqDTO.class)))
                .thenReturn(new CommonPageVO<>());
        PatrolPageReqDTO dto = new PatrolPageReqDTO();
        dto.setStartTime(java.time.LocalDateTime.now());
        mockMvc.perform(post("/performance/assessment/patrol/page")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(dto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").exists());
    }

    @Test
    void testPageTraceForCurrentUser_allNull() throws Exception {
        when(statisticsTraceService.pageTraceForCurrentUser(any(TracePageReqDTO.class)))
                .thenReturn(new CommonPageVO<>());
        TracePageReqDTO dto = new TracePageReqDTO();
        mockMvc.perform(post("/performance/assessment/trace/page")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(dto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").exists());
    }

    @Test
    void testPageTraceForCurrentUser_withCondition() throws Exception {
        when(statisticsTraceService.pageTraceForCurrentUser(any(TracePageReqDTO.class)))
                .thenReturn(new CommonPageVO<>());
        TracePageReqDTO dto = new TracePageReqDTO();
        dto.setStartTime(java.time.LocalDateTime.now());
        dto.setEndTime(java.time.LocalDateTime.now().plusDays(1));
        mockMvc.perform(post("/performance/assessment/trace/page")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(dto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").exists());
    }

    @Test
    void testPageInvestigationForCurrentUser_allNull() throws Exception {
        when(statisticsInvestigationService.pageInvestigationForCurrentUser(any(InvestigationPageReqDTO.class)))
                .thenReturn(new CommonPageVO<>());
        InvestigationPageReqDTO dto = new InvestigationPageReqDTO();
        mockMvc.perform(post("/performance/assessment/investigation/page")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(dto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").exists());
    }

    @Test
    void testPageInvestigationForCurrentUser_withCondition() throws Exception {
        when(statisticsInvestigationService.pageInvestigationForCurrentUser(any(InvestigationPageReqDTO.class)))
                .thenReturn(new CommonPageVO<>());
        InvestigationPageReqDTO dto = new InvestigationPageReqDTO();
        dto.setPersonKey("张三");
        mockMvc.perform(post("/performance/assessment/investigation/page")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(dto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").exists());
    }

    @Test
    void testPagePersonnelCapturedForCurrentUser() throws Exception {
        when(statisticsCompreService.pagePersonnelCapturedForCurrentUser(any(CapturedPageQueryReqDTO.class)))
                .thenReturn(new CommonPageVO<>());
        CapturedPageQueryReqDTO dto = new CapturedPageQueryReqDTO();
        mockMvc.perform(post("/performance/assessment/captured/page")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(dto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").exists());
    }

    @Test
    void testPagePersonnelCapturedForCurrentUserByCondition() throws Exception {
        when(statisticsCompreService.pagePersonnelCapturedForCurrentUser(any(CapturedPageQueryReqDTO.class)))
                .thenReturn(new CommonPageVO<>());
        CapturedPageQueryReqDTO dto = new CapturedPageQueryReqDTO();
        dto.setCaptureSite("地铁站A");
        mockMvc.perform(post("/performance/assessment/captured/page")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(dto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").exists());
    }

    @Test
    void testPagePersonnelAttentionForCurrentUser() throws Exception {
        when(statisticsCompreService.pagePersonnelAttentionForCurrentUser(any(FocusPageQueryReqDTO.class)))
                .thenReturn(new CommonPageVO<PersonnelAttention>());
        FocusPageQueryReqDTO dto = new FocusPageQueryReqDTO();
        dto.setPageNum(1);
        dto.setPageSize(10);
        mockMvc.perform(post("/performance/assessment/focus/page")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(dto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").exists());
    }

    @Test
    void testPagePersonnelAttentionForCurrentUserByCondition() throws Exception {
        when(statisticsCompreService.pagePersonnelAttentionForCurrentUser(any(FocusPageQueryReqDTO.class)))
                .thenReturn(new CommonPageVO<PersonnelAttention>());
        FocusPageQueryReqDTO dto = new FocusPageQueryReqDTO();
        dto.setOrgId("123");
        mockMvc.perform(post("/performance/assessment/focus/page")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(dto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").exists());
    }
}
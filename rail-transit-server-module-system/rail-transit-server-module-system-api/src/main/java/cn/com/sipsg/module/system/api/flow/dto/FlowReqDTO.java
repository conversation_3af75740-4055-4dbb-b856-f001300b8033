package cn.com.sipsg.module.system.api.flow.dto;

import com.alibaba.fastjson2.JSONObject;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 * 工作流发起，审批等操作DTO对象
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
public class FlowReqDTO {

    /**
     * 流程定义标识
     */
    @NotNull(message = "流程定义标识不能为空")
    private String processDefinitionKey;

    /**
     * 流程定义名 (模糊查询)
     */
    private String processDefinitionName;

    /**
     * 流程实例Id
     */
    private String processInstanceId;

    /**
     * 审批意见
     */
    @NotNull(message = "审批意见对象不能为空")
    private FlowTaskCommentDTO flowTaskCommentDTO;

    /**
     * 流程任务变量数据
     */
    @NotNull(message = "流程任务变量数据对象不能为空")
    private JSONObject taskVariableData;

    /**
     * 要求业务同事传的业务主键id(工单和工作流的表会跟它做个关联)
     */
    @NotNull(message = "业务主键id不能为空")
    private String businessKey;

    /**
     * 流程审批相关的主表数据
     */
    private JSONObject masterData;

    /**
     * 流程审批相关的多个从表数据
     */
    private JSONObject slaveData;

    /**
     * 传阅数据，格式为type和id，type的值参考FlowConstant中的常量值
     */
    private JSONObject copyData;

    /**
     * 流程任务Id
     */
    private String taskId;

    /**
     * 任务名称 (模糊查询)
     */
    private String taskName;

    /**
     * 工单记录表主键id值
     */
    private String workOrderId;

    /**
     * 撤销原因
     */
    private String cancelReason;

    /**
     * 终止原因
     */
    private String stopReason;

    /**
     * 干预备注
     */
    private String taskComment;

    /**
     * 驳回到的目标任务标识
     */
    private String targetTaskKey;

    /**
     * 指派人(多人之间逗号分割)
     */
    private String delegateAssignee;

    /**
     * 老的上级部门id
     */
    private String upDeptIdOldIdString;

}

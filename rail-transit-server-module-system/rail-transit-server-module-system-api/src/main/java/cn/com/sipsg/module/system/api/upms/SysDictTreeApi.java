package cn.com.sipsg.module.system.api.upms;

import cn.com.sipsg.module.system.api.upms.dto.SysDictTreeRespDTO;
import cn.com.sipsg.module.system.enums.ApiConstants;
import cn.hutool.core.lang.tree.Tree;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 字典树 API
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC服务-字典树")
public interface SysDictTreeApi {

    String PREFIX = ApiConstants.PREFIX + "/upms/dictTree";

    /**
     * 根据字典树code查询字典树
     *
     * @param dictTreeCode 字典树code
     * @return 字典树
     */
    @Operation(summary = "根据字典树code查询字典树")
    @GetMapping(PREFIX + "/getDictTreeByDictTreeCode")
    List<SysDictTreeRespDTO> getDictTreeByDictTreeCode(@RequestParam("dictTreeCode") String dictTreeCode);

    /**
     * 根据字典树code查询字典列表
     *
     * @param dictTreeCode 字典树code
     * @return 字典树
     */
    @Operation(summary = "根据字典树code查询字典列表")
    @GetMapping(PREFIX + "/listByDictTreeCode")
    List<SysDictTreeRespDTO> listByDictTreeCode(@RequestParam("dictTreeCode") String dictTreeCode);

}

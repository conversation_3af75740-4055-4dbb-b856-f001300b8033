package cn.com.sipsg.module.system.api.upms;

import cn.com.sipsg.module.system.api.upms.dto.SysFileRespDTO;
import cn.com.sipsg.module.system.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 文件 API
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC服务-文件")
public interface SysFileApi {

    String PREFIX = ApiConstants.PREFIX + "/upms/file";

    /**
     * 根据文件ID查询文件
     *
     * @param fileId 文件ID
     * @return 文件
     */
    @Operation(summary = "根据文件ID查询文件")
    @GetMapping(PREFIX + "/getById")
    SysFileRespDTO getById(@RequestParam("fileId") String fileId);

    /**
     * 根据文件路径查询文件字节数组
     *
     * @param url 文件url
     * @return 文件字节数组
     */
    @Operation(summary = "根据文件路径查询文件字节数组")
    @GetMapping(PREFIX + "/getFileBytesByUrl")
    byte[] getFileBytesByUrl(@RequestParam("url") String url);

    /**
     * 上传文件
     *
     * @param file      源文件
     * @param groupCode 分组编码
     * @return 文件
     */
    @Operation(summary = "上传文件")
    @PostMapping(PREFIX + "/upload")
    SysFileRespDTO upload(@RequestPart("file") MultipartFile file, @RequestParam("groupCode") String groupCode);

    /**
     * 根据分组编码查询文件列表
     *
     * @param groupCode 分组编码
     * @return 文件列表
     */
    @Operation(summary = "根据分组编码查询文件列表")
    @GetMapping(PREFIX + "/getFileListByGroupCode")
    List<SysFileRespDTO> getFileListByGroupCode(@RequestParam("groupCode") String groupCode);

    /**
     * 删除指定文件
     *
     * @param fileId 文件ID
     */
    @Operation(summary = "删除指定文件")
    @PostMapping(PREFIX + "/delete")
    void delete(@RequestParam("fileId") String fileId);

    /**
     * 批量删除文件
     *
     * @param fileIds 文件ID
     */
    @Operation(summary = "批量删除文件")
    @PostMapping(PREFIX + "/deleteBatch")
    void deleteBatch(@RequestParam("fileIds") List<String> fileIds);

}

package cn.com.sipsg.module.system.api.upms;

import cn.com.sipsg.module.system.api.upms.dto.SysDeptPostRespDTO;
import cn.com.sipsg.module.system.api.upms.dto.SysDeptQueryReqDTO;
import cn.com.sipsg.module.system.api.upms.dto.SysDeptRespDTO;
import cn.com.sipsg.module.system.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 部门 API
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC服务-部门")
public interface SysDeptApi {

    String PREFIX = ApiConstants.PREFIX + "/upms/dept";

    /**
     * 根据部门ID列表查询部门列表
     *
     * @param deptIds 部门ID列表
     * @return 部门列表
     */
    @Operation(summary = "根据部门ID列表查询部门列表")
    @GetMapping(PREFIX + "/getDeptListByIds")
    List<SysDeptRespDTO> getDeptListByIds(@RequestParam("deptIds") Collection<String> deptIds);

    /**
     * 根据用户ID查询部门列表
     *
     * @param userId 用户ID
     * @return 部门列表
     */
    @Operation(summary = "根据用户ID查询部门列表")
    @GetMapping(PREFIX + "/getDeptListByUserId")
    List<SysDeptRespDTO> getDeptListByUserId(@RequestParam("userId") String userId);

    /**
     * 根据部门ID查询部门领导岗位ID
     *
     * @param deptId 部门ID
     * @return 部门领导岗位ID
     */
    @Operation(summary = "根据部门ID查询部门领导岗位ID")
    @GetMapping(PREFIX + "/getLeaderDeptPostIdById")
    String getLeaderDeptPostIdById(@RequestParam("deptId") String deptId);

    /**
     * 根据部门ID查询上级部门领导岗位ID
     *
     * @param deptId 部门ID
     * @return 上级部门领导岗位ID
     */
    @Operation(summary = "根据部门ID查询上级部门领导岗位ID")
    @GetMapping(PREFIX + "/getParentLeaderDeptPostIdById")
    String getParentLeaderDeptPostIdById(@RequestParam("deptId") String deptId);

    /**
     * 根据部门ID和岗位ID列表查询部门岗位ID集合
     *
     * @param deptId  部门ID
     * @param postIds 岗位ID列表
     * @return 部门岗位ID集合
     */
    @Operation(summary = "根据部门ID和岗位ID列表查询部门岗位ID集合")
    @GetMapping(PREFIX + "/getDeptPostIdMap")
    Map<String, String> getDeptPostIdMap(@RequestParam("deptId") String deptId, @RequestParam("postIds") Collection<String> postIds);

    /**
     * 根据部门ID和岗位ID列表查询同级部门岗位ID集合
     *
     * @param deptId  部门ID
     * @param postIds 岗位ID列表
     * @return 同级部门岗位ID集合
     */
    @Operation(summary = "根据部门ID和岗位ID列表查询同级部门岗位ID集合")
    @GetMapping(PREFIX + "/getSiblingDeptPostIdMap")
    Map<String, String> getSiblingDeptPostIdMap(@RequestParam("deptId") String deptId, @RequestParam("postIds") Collection<String> postIds);

    /**
     * 根据部门ID和岗位ID列表查询上级部门岗位ID集合
     *
     * @param deptId  部门ID
     * @param postIds 岗位ID列表
     * @return 上级部门岗位ID集合
     */
    @Operation(summary = "根据部门ID和岗位ID列表查询上级部门岗位ID集合")
    @GetMapping(PREFIX + "/getParentDeptPostIdMap")
    Map<String, String> getParentDeptPostIdMap(@RequestParam("deptId") String deptId, @RequestParam("postIds") Collection<String> postIds);

    /**
     * 查询部门列表
     *
     * @return 部门列表
     */
    @Operation(summary = "查询部门列表")
    @GetMapping(PREFIX + "/getDeptList")
    List<SysDeptRespDTO> getDeptList();

    /**
     * 查询部门列表
     *
     * @param reqDTO 请求参数
     * @return 部门列表
     */
    @Operation(summary = "查询部门列表")
    @PostMapping(PREFIX + "/getDeptList")
    List<SysDeptRespDTO> getDeptList(@Valid @RequestBody SysDeptQueryReqDTO reqDTO);

    /**
     * 根据部门id查询部门岗位集合
     *
     * @param deptId 部门id
     * @return 部门岗位集合
     */
    @Operation(summary = "根据部门id查询部门岗位集合")
    @GetMapping(PREFIX + "/getDeptPostListByDeptId")
    List<SysDeptPostRespDTO> getDeptPostListByDeptId(@RequestParam("deptId") String deptId);

    /**
     * 根据部门id查询部门信息
     *
     * @param deptId 部门id
     * @return 部门信息
     */
    @Operation(summary = "根据部门id查询部门信息")
    @GetMapping(PREFIX + "/getDeptByDeptId")
    SysDeptRespDTO getDeptByDeptId(@RequestParam("deptId") String deptId);

    /**
     * 根据岗位id查询部门岗位集合
     *
     * @param deptPostId 部门岗位id
     * @return 部门岗位对象
     */
    @Operation(summary = "根据岗位id查询部门岗位集合")
    @GetMapping(PREFIX + "/getDeptPostInfoByDeptPostId")
    SysDeptPostRespDTO getDeptPostInfoByDeptPostId(@RequestParam("deptPostId") String deptPostId);

    /**
     * 根据岗位名称获取部门集合
     * @param deptName 部门岗位名称
     * @return 部门岗位集合
     */
    @Operation(summary = "根据岗位名称获取部门集合")
    @GetMapping(PREFIX + "/getDeptListByDeptName")
    List<SysDeptRespDTO> getDeptListByDeptName(@RequestParam("deptName") String deptName);

}

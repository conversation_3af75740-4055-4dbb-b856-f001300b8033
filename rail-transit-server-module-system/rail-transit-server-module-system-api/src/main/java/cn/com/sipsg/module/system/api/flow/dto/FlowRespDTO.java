package cn.com.sipsg.module.system.api.flow.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 工作流接口返回对象
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class FlowRespDTO {

    /**
     * 流程实例id
     */
    private String processInstanceId;

    /**
     * 流程定义id
     */
    private String processDefinitionId;

    /**
     * 流程定义key
     */
    private String processDefinitionKey;

    /**
     * 生成的工单id
     */
    private String workOrderId;

}

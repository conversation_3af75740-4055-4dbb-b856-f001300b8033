package cn.com.sipsg.module.system.api.flow.dto;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 流程任务的批注
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
public class FlowTaskCommentDTO {

    /**
     * 流程任务触发按钮类型，内置值可参考FlowTaskButton。
     */
    @NotNull(message = "数据验证失败，任务的审批类型不能为空！")
    private String approvalType;

    /**
     * 流程任务的批注内容。
     */
    private String taskComment;

    /**
     * 委托指定人，比如加签、转办等。
     */
    private String delegateAssignee;

    /**
     * 文件原始名称
     */
    private String fileOriginalName;

    /**
     * 文件上传的groupCode
     */
    private String groupCode;

    /**
     * 发起人指定所属部门id
     */
    @NotBlank(message = "发起人指定的所属部门id不能为空")
    private String deptOfSubmitUser;

    /**
     * 审批人审批时选的所属部门
     */
    private String deptOfApproveUser;

}

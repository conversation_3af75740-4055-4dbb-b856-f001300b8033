package cn.com.sipsg.module.system.enums.flow;

/**
 * 工作流任务触发BUTTON常量
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public interface FlowApprovalTypeConstants {

    /**
     * 保存。
     */
    String SAVE = "save";

    /**
     * 同意。
     */
    String AGREE = "agree";

    /**
     * 拒绝。
     */
    String REFUSE = "refuse";

    /**
     * 驳回。
     */
    String REJECT = "reject";

    /**
     * 撤销。
     */
    String REVOKE = "revoke";

    /**
     * 指派。(转办)
     */
    String TRANSFER = "transfer";

    /**
     * 多实例会签。
     */
    String MULTI_SIGN = "multi_sign";

    /**
     * 会签同意。
     */
    String MULTI_AGREE = "multi_agree";

    /**
     * 会签拒绝。
     */
    String MULTI_REFUSE = "multi_refuse";

    /**
     * 会签弃权。
     */
    String MULTI_ABSTAIN = "multi_abstain";

    /**
     * 多实例加签。
     */
    String MULTI_CONSIGN = "multi_consign";

    /**
     * 多实例减签。
     */
    String MULTI_MINUS_SIGN = "multi_minus_sign";

    /**
     * 中止。
     */
    String STOP = "stop";

    /**
     * 干预。
     */
    String INTERVENE = "intervene";

    /**
     * 自由跳转。
     */
    String FREE_JUMP = "free_jump";

    /**
     * 超时审批自动通过
     */
    String TIMEOUT_AUTO_COMPLETE = "autoComplete";

    /**
     * 超时自动拒绝
     */
    String TIMEOUT_AUTO_REFUSE = "autoRefuse";
    
}

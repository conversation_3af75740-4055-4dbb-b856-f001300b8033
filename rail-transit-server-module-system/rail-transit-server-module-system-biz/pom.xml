<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.com.sipsg</groupId>
        <artifactId>rail-transit-server-module-system</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>rail-transit-server-module-system-biz</artifactId>
    <description>system 模块业务逻辑</description>

    <dependencies>
        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>rail-transit-server-module-system-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- RPC 相关 -->
        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>common-rpc</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>common-env</artifactId>
        </dependency>

        <!-- 注册中心 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- 配置中心 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- 分布式事务 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-seata</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>seata-spring-boot-starter</artifactId>
                    <groupId>io.seata</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.seata</groupId>
            <artifactId>seata-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>io.seata</groupId>
            <artifactId>seata-serializer-kryo</artifactId>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>common-web</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>common-swagger</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>common-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>common-redis</artifactId>
        </dependency>

        <!-- 工具类相关 -->
        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>common-captcha</artifactId>
        </dependency>

        <dependency>
            <groupId>org.dromara.x-file-storage</groupId>
            <artifactId>x-file-storage-spring</artifactId>
        </dependency>

        <!-- 日志相关 -->
        <dependency>
            <groupId>com.yomahub</groupId>
            <artifactId>tlog-web-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>common-operationlog</artifactId>
        </dependency>

        <!-- 认证相关 -->
        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>common-security</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>common-oauth2</artifactId>
        </dependency>

        <!-- 服务保障相关 -->
        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>common-protection</artifactId>
        </dependency>

        <!-- 三方云服务相关 -->
        <dependency>
            <groupId>io.minio</groupId>
            <artifactId>minio</artifactId>
        </dependency>

        <!-- 定时任务相关 -->
        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>common-job</artifactId>
        </dependency>

        <!-- 数据权限相关 -->
        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>common-datapermission</artifactId>
        </dependency>

        <!-- 工作流 -->
        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>common-flow</artifactId>
        </dependency>

        <!-- 工作流-动态表单 -->
        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>common-flow-online</artifactId>
        </dependency>

        <!-- 动态表单 -->
        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>common-online</artifactId>
        </dependency>

        <!-- 动态服务 -->
        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>common-dynamic</artifactId>
        </dependency>

        <!-- 一张图 -->
        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>common-onemap</artifactId>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <!-- 打包 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-install-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
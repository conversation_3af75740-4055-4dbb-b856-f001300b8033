package cn.com.sipsg.module.system.module.uaa.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 登录 VO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "登录 VO")
public class LoginVO {

    /**
     * 令牌名称
     */
    @Schema(description = "令牌名称")
    private String tokenName;

    /**
     * 令牌值
     */
    @Schema(description = "令牌值")
    private String tokenValue;

}

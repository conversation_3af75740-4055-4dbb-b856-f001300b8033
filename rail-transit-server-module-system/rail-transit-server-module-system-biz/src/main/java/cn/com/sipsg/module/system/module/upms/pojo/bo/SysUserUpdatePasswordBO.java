package cn.com.sipsg.module.system.module.upms.pojo.bo;

import cn.com.sipsg.common.validation.group.RawTextLengthGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 用户更新密码 BO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "用户更新密码 BO")
public class SysUserUpdatePasswordBO {

    /**
     * 旧密码
     */
    @Schema(description = "旧密码", requiredMode = Schema.RequiredMode.REQUIRED, example = "123456")
    @NotBlank(message = "旧密码不能为空")
    private String oldPassword;

    /**
     * 新密码
     */
    @Schema(description = "新密码", requiredMode = Schema.RequiredMode.REQUIRED, example = "654321")
    @NotBlank(message = "新密码不能为空")
    @Size(min = 4, max = 16, message = "密码长度为4-16位", groups = RawTextLengthGroup.class)
    private String newPassword;

}

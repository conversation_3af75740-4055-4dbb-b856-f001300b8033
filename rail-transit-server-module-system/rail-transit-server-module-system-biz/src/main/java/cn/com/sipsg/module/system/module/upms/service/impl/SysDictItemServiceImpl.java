package cn.com.sipsg.module.system.module.upms.service.impl;

import cn.com.sipsg.common.enums.CommonStatusEnum;
import cn.com.sipsg.common.exception.enums.ErrorCodeEnum;
import cn.com.sipsg.common.mybatis.core.query.LambdaQueryWrapperX;
import cn.com.sipsg.common.pojo.vo.CommonImportVO;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.util.AssertUtils;
import cn.com.sipsg.common.util.EnumUtils;
import cn.com.sipsg.module.system.module.upms.convert.SysDictItemConvert;
import cn.com.sipsg.module.system.module.upms.entity.SysDict;
import cn.com.sipsg.module.system.module.upms.entity.SysDictItem;
import cn.com.sipsg.module.system.module.upms.mapper.SysDictItemMapper;
import cn.com.sipsg.module.system.module.upms.mapper.SysDictMapper;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysDictItemQueryBO;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysDictItemSaveBO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysDictItemVO;
import cn.com.sipsg.module.system.module.upms.service.SysDictItemService;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 字典项表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Service
@RequiredArgsConstructor
public class SysDictItemServiceImpl extends ServiceImpl<SysDictItemMapper, SysDictItem> implements SysDictItemService {

    private final SysDictMapper dictMapper;

    @Override
    public CommonPageVO<SysDictItemVO> page(SysDictItemQueryBO bo) {
        LambdaQueryWrapperX<SysDictItem> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper
                .eq(SysDictItem::getDictCode, bo.getDictCode())
                .likeIfPresent(SysDictItem::getItemName, bo.getItemName())
                .eqIfPresent(SysDictItem::getStatus, bo.getStatus());
        return this.page(bo, queryWrapper).convert(SysDictItemConvert.INSTANCE::convert);
    }

    @Override
    public String save(SysDictItemSaveBO bo) {
        bo.setItemId(null);
        // 校验字典项
        checkSaveOrUpdate(null, bo.getDictCode(), bo.getItemCode());
        // 保存字典项
        SysDictItem dictItem = SysDictItemConvert.INSTANCE.convert(bo);
        this.save(dictItem);
        return dictItem.getItemId();
    }

    @Override
    public void update(SysDictItemSaveBO bo) {
        // 校验字典项
        checkSaveOrUpdate(bo.getItemId(), bo.getDictCode(), bo.getItemCode());
        // 更新字典项
        SysDictItem dictItem = SysDictItemConvert.INSTANCE.convert(bo);
        this.updateById(dictItem);
    }

    @Override
    public SysDictItemVO detail(String itemId) {
        // 校验是否存在
        SysDictItem dictItem = checkExist(itemId);
        return SysDictItemConvert.INSTANCE.convert(dictItem);
    }

    @Override
    public void delete(String itemId) {
        // 删除字典项
        this.removeById(itemId);
    }

    @Override
    public void updateStatus(String itemId, Integer status) {
        // 校验字典项存在
        checkExist(itemId);
        // 更新字典项状态
        SysDictItem updateDictItem = new SysDictItem();
        updateDictItem.setItemId(itemId);
        updateDictItem.setStatus(SysDictItemConvert.INSTANCE.status2Enum(status));
        this.updateById(updateDictItem);
    }

    @Override
    public CommonImportVO importDictItem(List<Map<String, Object>> dataList) {
        List<Map<String, Object>> errorRecords = new ArrayList<>();
        long successTotal = 0;
        long errorTotal = 0;
        for (Map<String, Object> dataMap : dataList) {
            String dictItemName = StrUtil.trim(MapUtil.getStr(dataMap, "字典项名称"));
            String dictItemCode = StrUtil.trim(MapUtil.getStr(dataMap, "字典项编码"));
            String statusDesc = StrUtil.trim(MapUtil.getStr(dataMap, "状态"));
            String sort = StrUtil.trim(MapUtil.getStr(dataMap, "显示顺序"));
            String dictCode = StrUtil.trim(MapUtil.getStr(dataMap, "所属字典编码"));
            String remark = StrUtil.trim(MapUtil.getStr(dataMap, "备注"));
            StringBuilder sb = new StringBuilder();
            if (StrUtil.isBlank(dictItemName)) {
                sb.append("字典项名称不能为空;");
            }
            if (StrUtil.isBlank(dictItemCode)) {
                sb.append("字典项编码不能为空;");
            } else {
                LambdaQueryWrapperX<SysDictItem> queryWrapper = new LambdaQueryWrapperX<>();
                queryWrapper.eq(SysDictItem::getDictCode, dictCode)
                        .eq(SysDictItem::getItemCode, dictItemCode);
                Long count = baseMapper.selectCount(queryWrapper);
                if (count > 0) {
                    sb.append("字典项编码已存在;");
                }
            }
            CommonStatusEnum status = null;
            if (StrUtil.isBlank(statusDesc)) {
                sb.append("状态不能为空;");
            } else {
                status = EnumUtils.getEnumByDesc(CommonStatusEnum.class, statusDesc);
                if (ObjectUtil.isNull(status)) {
                    sb.append("状态不正确;");
                }
            }
            if (StrUtil.isBlank(sort)) {
                sb.append("显示顺序不能为空;");
            }
            if (StrUtil.isBlank(dictCode)) {
                sb.append("所属字典编码不能为空;");
            } else {
                Long count = dictMapper.selectCount(SysDict::getDictCode, dictCode);
                if (count == 0) {
                    sb.append("所属字典编码不正确;");
                }
            }
            if (sb.length() > 0) {
                errorTotal++;
                dataMap.put("错误提示", sb);
                errorRecords.add(dataMap);
                continue;
            }
            // 保存字典
            SysDictItem sysDictItem = new SysDictItem();
            sysDictItem.setItemName(dictItemName);
            sysDictItem.setItemCode(dictItemCode);
            sysDictItem.setDictCode(dictCode);
            sysDictItem.setStatus(status);
            sysDictItem.setRemark(remark);
            sysDictItem.setSort(Convert.toInt(sort));
            this.save(sysDictItem);
            successTotal++;
        }
        return CommonImportVO.builder().successTotal(successTotal).errorTotal(errorTotal).errorRecords(errorRecords).build();
    }

    private void checkSaveOrUpdate(String itemId, String dictCode, String itemCode) {
        // 校验字典项存在
        checkExist(itemId);
        // 校验字典编码有效
        Long count = dictMapper.selectCount(SysDict::getDictCode, dictCode);
        AssertUtils.isTrue(count == 0, ErrorCodeEnum.INVALID_RELATED_RECORD_ID);
        // 校验字典项编码唯一
        LambdaQueryWrapperX<SysDictItem> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper
                .eq(SysDictItem::getDictCode, dictCode)
                .eq(SysDictItem::getItemCode, itemCode)
                .neIfPresent(SysDictItem::getItemId, itemId);
        AssertUtils.isTrue(this.count(queryWrapper) > 0, String.format("字典项编码【%s】已存在", itemCode));
    }

}

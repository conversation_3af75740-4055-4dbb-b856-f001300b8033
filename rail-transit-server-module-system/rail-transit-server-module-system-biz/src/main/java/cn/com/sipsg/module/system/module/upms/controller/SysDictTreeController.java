package cn.com.sipsg.module.system.module.upms.controller;

import cn.com.sipsg.common.constant.PageConstants;
import cn.com.sipsg.common.operationlog.core.annotation.OperationLog;
import cn.com.sipsg.common.operationlog.core.enums.OperationTypeEnum;
import cn.com.sipsg.common.pojo.CommonResult;
import cn.com.sipsg.common.pojo.vo.CommonImportVO;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.validation.AddValidated;
import cn.com.sipsg.common.validation.UpdateValidated;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysDictTreeBatchUpdateBO;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysDictTreeDragUpdateBO;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysDictTreeQueryBO;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysDictTreeSaveBO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysDictTreeVO;
import cn.com.sipsg.module.system.module.upms.service.SysDictTreeService;
import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.lang.tree.Tree;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import org.springframework.web.multipart.MultipartFile;

/**
 * 字典树管理控制器
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Tag(name = "字典树管理")
@RestController
@RequiredArgsConstructor
@RequestMapping("/system/upms/dictTree")
public class SysDictTreeController {

    private final SysDictTreeService sysDictTreeService;

    /**
     * 查询字典树
     *
     * @return 字典树
     */
    @Operation(summary = "查询字典树")
    @OperationLog(module = "字典树管理", value = "查询字典树", type = OperationTypeEnum.QUERY, saveResponse = false)
    @SaCheckPermission(value = "system:upms:dictTree:list")
    @GetMapping("/tree")
    public CommonResult<List<SysDictTreeVO>> listTree() {
        return CommonResult.data(sysDictTreeService.listTree());
    }

    /**
     * 根据字典树id查询字典树
     *
     * @param dictTreeId 字典树id
     * @return 字典树
     */
    @Operation(summary = "根据字典树id查询字典树")
    @Parameter(name = "dictTreeId", description = "字典树id", required = true, example = "1111")
    @OperationLog(module = "字典树管理", value = "根据字典树id查询字典树", type = OperationTypeEnum.QUERY, saveResponse = false)
    @GetMapping("/listTreeByDictTreeId")
    public CommonResult<List<Tree<String>>> listTreeByDictTreeId(@RequestParam String dictTreeId) {
        return CommonResult.data(sysDictTreeService.listTreeByDictTreeId(dictTreeId));
    }

    /**
     * 根据字典树code查询字典树
     *
     * @param dictTreeCode 字典树code
     * @return 字典树
     */
    @Operation(summary = "根据字典树code查询字典树")
    @Parameter(name = "dictTreeCode", description = "字典树code", required = true, example = "1111")
    @OperationLog(module = "字典树管理", value = "根据字典树code查询字典树", type = OperationTypeEnum.QUERY, saveResponse = false)
    @GetMapping("/listTreeByDictTreeCode")
    public CommonResult<List<Tree<String>>> listTreeByDictTreeCode(@RequestParam String dictTreeCode) {
        return CommonResult.data(sysDictTreeService.listTreeByDictTreeCode(dictTreeCode));
    }

    /**
     * 查询字典树管理分页列表
     *
     * @param bo 参数
     * @return 字典树管理分页列表
     */
    @Operation(summary = "查询字典树管理分页列表")
    @OperationLog(module = "字典树管理", value = "查询字典树管理分页列表", type = OperationTypeEnum.QUERY, saveResponse = false)
    @SaCheckPermission(value = "system:upms:dictTree:list")
    @PostMapping("/page")
    public CommonResult<CommonPageVO<SysDictTreeVO>> page(@RequestBody SysDictTreeQueryBO bo) {
        return CommonResult.data(sysDictTreeService.page(bo));
    }

    /**
     * 查询字典树管理列表
     *
     * @param bo 参数
     * @return 字典树管理列表
     */
    @Operation(summary = "查询字典树管理列表")
    @OperationLog(module = "字典树管理", value = "查询字典树管理列表", type = OperationTypeEnum.QUERY, saveResponse = false)
    @SaCheckPermission(value = "system:upms:dictTree:list")
    @PostMapping("/list")
    public CommonResult<List<SysDictTreeVO>> list(@RequestBody SysDictTreeQueryBO bo) {
        bo.setSize(PageConstants.SIZE_NONE);
        return CommonResult.data(sysDictTreeService.page(bo).getRecords());
    }

    /**
     * 新增字典树管理
     *
     * @param bo 参数
     * @return 字典树管理ID
     */
    @Operation(summary = "新增字典树管理")
    @OperationLog(module = "字典树管理", value = "新增字典树管理", type = OperationTypeEnum.SAVE)
    @SaCheckPermission(value = "system:upms:dictTree:save")
    @PostMapping("/save")
    public CommonResult<String> save(@AddValidated @RequestBody SysDictTreeSaveBO bo) {
        return CommonResult.data(sysDictTreeService.save(bo));
    }

    /**
     * 编辑字典树管理
     *
     * @param bo 参数
     */
    @Operation(summary = "编辑字典树管理")
    @OperationLog(module = "字典树管理", value = "编辑字典树管理", type = OperationTypeEnum.UPDATE)
    @SaCheckPermission(value = "system:upms:dictTree:update")
    @PostMapping("/update")
    public CommonResult<Void> update(@UpdateValidated @RequestBody SysDictTreeSaveBO bo) {
        sysDictTreeService.update(bo);
        return CommonResult.success();
    }

    /**
     * 批量更新字典项
     *
     * @return 成功或失败
     */
    @Operation(summary = "批量更新字典项")
    @OperationLog(module = "字典树管理", value = "批量更新字典项", type = OperationTypeEnum.UPDATE)
    @SaCheckPermission(value = "system:upms:dictTree:update")
    @PostMapping("/updateBatch")
    public CommonResult<Boolean> updateBatch(@RequestBody SysDictTreeBatchUpdateBO bo) {
        return CommonResult.data(sysDictTreeService.updateBatch(bo));
    }

    /**
     * 是否有后代字典
     *
     * @return 成功或失败
     */
    @Operation(summary = "是否有后代字典")
    @OperationLog(module = "字典树管理", value = "是否有后代字典", type = OperationTypeEnum.QUERY, saveResponse = false)
    @GetMapping("/{id}/hasDescendant")
    public CommonResult<Boolean> hasDescendant(@PathVariable String id) {
        return CommonResult.data(sysDictTreeService.hasDescendant(id));
    }

    /**
     * 查询字典树管理详情
     *
     * @param id 主键
     */
    @Operation(summary = "查询字典树管理详情")
    @Parameter(name = "id", description = "主键", required = true, example = "1111")
    @OperationLog(module = "字典树管理", value = "查询字典树管理详情", type = OperationTypeEnum.QUERY, saveResponse = false)
    @SaCheckPermission(value = "system:upms:dictTree:detail")
    @GetMapping("/detail")
    public CommonResult<SysDictTreeVO> detail(@RequestParam String id) {
        return CommonResult.data(sysDictTreeService.detail(id));
    }

    /**
     * 删除字典树管理
     *
     * @param id 主键
     */
    @Operation(summary = "删除字典树管理")
    @Parameter(name = "id", description = "主键", required = true, example = "1111")
    @OperationLog(module = "字典树管理", value = "删除字典树管理", type = OperationTypeEnum.DELETE)
    @SaCheckPermission(value = "system:upms:dictTree:delete")
    @PostMapping("/delete")
    public CommonResult<Void> delete(@RequestParam String id) {
        sysDictTreeService.delete(id);
        return CommonResult.success();
    }

    /**
     * 字典树拖拽更新
     *
     * @param bo 字典树拖拽更新BO
     * @return 成功或失败
     */
    @Operation(summary = "字典树拖拽更新")
    @OperationLog(module = "字典树管理", value = "字典树拖拽更新", type = OperationTypeEnum.UPDATE)
    @SaCheckPermission(value = "system:upms:dictTree:update")
    @PostMapping("/dragUpdate")
    public CommonResult<Boolean> dragUpdate(@RequestBody @Validated SysDictTreeDragUpdateBO bo) {
        return CommonResult.data(sysDictTreeService.dragUpdate(bo));
    }

    /**
     * 导出字典树
     */
    @Operation(summary = "导出字典树")
    @OperationLog(module = "字典树管理", value = "导出字典树", type = OperationTypeEnum.EXPORT)
    @SaCheckPermission(value = "system:upms:dictTree:export")
    @GetMapping("/export")
    public void export() {
        sysDictTreeService.export();
    }

    /**
     * 导入字典树
     *
     * @param file 文件
     * @return 错误信息
     */
    @Operation(summary = "导入字典树")
    @OperationLog(module = "字典树管理", value = "导入字典树", type = OperationTypeEnum.IMPORT)
    @SaCheckPermission(value = "system:upms:dictTree:import")
    @PostMapping("/import")
    public CommonResult<CommonImportVO> importInfo(@RequestParam MultipartFile file) {
        CommonImportVO importVO = sysDictTreeService.importInfo(file);
        return CommonResult.data(importVO);
    }

}
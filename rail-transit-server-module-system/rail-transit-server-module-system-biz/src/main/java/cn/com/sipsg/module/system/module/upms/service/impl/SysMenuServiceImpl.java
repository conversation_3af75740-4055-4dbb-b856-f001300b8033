package cn.com.sipsg.module.system.module.upms.service.impl;

import cn.com.sipsg.common.constant.CommonConstants;
import cn.com.sipsg.common.exception.enums.ErrorCodeEnum;
import cn.com.sipsg.common.mybatis.core.query.LambdaQueryWrapperX;
import cn.com.sipsg.common.mybatis.core.query.MPJLambdaWrapperX;
import cn.com.sipsg.common.util.AssertUtils;
import cn.com.sipsg.common.util.BeanUtils;
import cn.com.sipsg.common.util.CollectionUtils;
import cn.com.sipsg.module.system.module.upms.convert.SysMenuConvert;
import cn.com.sipsg.module.system.module.upms.entity.*;
import cn.com.sipsg.module.system.module.upms.enums.MenuBindTypeEnum;
import cn.com.sipsg.module.system.module.upms.enums.MenuTypeEnum;
import cn.com.sipsg.module.system.module.upms.mapper.*;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysMenuSaveBO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysMenuVO;
import cn.com.sipsg.module.system.module.upms.service.SysMenuService;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 菜单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Service
@RequiredArgsConstructor
public class SysMenuServiceImpl extends ServiceImpl<SysMenuMapper, SysMenu> implements SysMenuService {

    private final SysPermMapper permMapper;

    private final SysMenuPermMapper menuPermMapper;

    private final SysRoleMenuMapper roleMenuMapper;

    private final SysQuickMenuMapper quickMenuMapper;

    @Override
    @Transactional
    public String save(SysMenuSaveBO bo) {
        bo.setMenuId(null);
        if (StrUtil.isBlank(bo.getParentId())) {
            bo.setParentId(CommonConstants.PARENT_ID_ROOT);
        }
        // 校验菜单
        checkSaveOrUpdate(null, bo.getParentId(), SysMenuConvert.INSTANCE.menuType2Enum(bo.getMenuType()), bo.getAppCode(), bo.getPermIds());
        // 保存菜单
        SysMenu menu = SysMenuConvert.INSTANCE.convert(bo);
        this.save(menu);
        // 保存菜单权限关系
        saveMenuPerm(menu.getMenuId(), bo.getPermIds());
        // 判断当前菜单是否为指向动态表单的菜单，并将根据约定，动态插入两个子菜单
        if (StrUtil.isNotBlank(menu.getOnlineFormId()) && MenuBindTypeEnum.ONLINE_FORM == menu.getBindType()) {
            SysMenu viewSubMenu = new SysMenu();
            viewSubMenu.setParentId(menu.getMenuId());
            viewSubMenu.setMenuType(MenuTypeEnum.BUTTON);
            viewSubMenu.setMenuName("查看");
            viewSubMenu.setSort(0);
            viewSubMenu.setOnlineFormId(menu.getOnlineFormId());
            viewSubMenu.setOnlineMenuPermType(CommonConstants.ONLINE_MENU_PERM_VIEW);
            this.save(viewSubMenu);
            SysMenu editSubMenu = new SysMenu();
            editSubMenu.setParentId(menu.getMenuId());
            editSubMenu.setMenuType(MenuTypeEnum.BUTTON);
            editSubMenu.setMenuName("编辑");
            editSubMenu.setSort(1);
            editSubMenu.setOnlineFormId(menu.getOnlineFormId());
            editSubMenu.setOnlineMenuPermType(CommonConstants.ONLINE_MENU_PERM_EDIT);
            this.save(editSubMenu);
        }
        return menu.getMenuId();
    }

    @Override
    @Transactional
    public void update(SysMenuSaveBO bo) {
        if (StrUtil.isBlank(bo.getParentId())) {
            bo.setParentId(CommonConstants.PARENT_ID_ROOT);
        }
        // 校验菜单
        checkSaveOrUpdate(bo.getMenuId(), bo.getParentId(), SysMenuConvert.INSTANCE.menuType2Enum(bo.getMenuType()), bo.getAppCode(), bo.getPermIds());
        // 更新菜单
        SysMenu menu = SysMenuConvert.INSTANCE.convert(bo);
        this.updateById(menu);
        // 先删除原有菜单权限关系
        menuPermMapper.deleteByMenuId(menu.getMenuId());
        // 保存菜单权限关系
        saveMenuPerm(menu.getMenuId(), bo.getPermIds());
        // 如果当前菜单的动态表单Id变化了，就需要同步更新他的内置子菜单也同步更新
        if (ObjectUtil.notEqual(bo.getOnlineFormId(), menu.getOnlineFormId())) {
            SysMenu onlineSubMenu = new SysMenu();
            onlineSubMenu.setOnlineFormId(menu.getOnlineFormId());
            this.update(onlineSubMenu, new LambdaQueryWrapperX<SysMenu>().eq(SysMenu::getParentId, menu.getMenuId()));
        }
    }

    @Override
    @Transactional
    public void delete(String menuId) {
        SysMenu sysMenu = this.getById(menuId);
        if (sysMenu != null) {
            // 校验动态表单内置菜单
            AssertUtils.isTrue(sysMenu.getOnlineMenuPermType() != null, "动态表单内置菜单不允许删除");
            // 删除菜单及子菜单
            this.delete(sysMenu);
        }
    }

    @Override
    public SysMenuVO detail(String menuId) {
        SysMenu menu = getByIdDeep(menuId);
        // 校验是否存在
        AssertUtils.isTrue(Objects.isNull(menu), ErrorCodeEnum.DATA_NOT_EXIST);
        return BeanUtils.copyProperties(menu, SysMenuVO.class);
    }

    @Override
    public List<SysMenu> getMenuList(String userId, String appCode) {
        MPJLambdaWrapperX<SysMenu> queryWrapper = new MPJLambdaWrapperX<>();
        queryWrapper
                .selectAll(SysMenu.class)
                .innerJoin(SysRoleMenu.class, SysRoleMenu::getMenuId, SysMenu::getMenuId)
                .innerJoin(SysUserRole.class, SysUserRole::getRoleId, SysRoleMenu::getRoleId)
                .eq(SysUserRole::getUserId, userId)
                .eq(SysMenu::getAppCode, appCode);
        List<SysMenu> menuList = CollectionUtils.distinct(this.selectJoinList(SysMenu.class, queryWrapper), SysMenu::getMenuId);
        menuList.sort(Comparator.comparing(SysMenu::getSort));
        return menuList;
    }

    private void delete(SysMenu menu) {
        this.removeById(menu);
        // 删除角色菜单关系
        roleMenuMapper.deleteByMenuId(menu.getMenuId());
        // 删除菜单权限关系
        menuPermMapper.deleteByMenuId(menu.getMenuId());
        // 删除快捷菜单
        quickMenuMapper.delete(SysQuickMenu::getMenuId, menu.getMenuId());
        // 删除子菜单
        List<SysMenu> menuList = baseMapper.selectByParentId(menu.getMenuId());
        if (CollUtil.isEmpty(menuList)) {
            return;
        }
        for (SysMenu subMenu : menuList) {
            delete(subMenu);
        }
    }

    private void saveMenuPerm(String menuId, Set<String> permIds) {
        List<SysMenuPerm> menuPermList = permIds.stream().map(permId -> {
            SysMenuPerm menuPerm = new SysMenuPerm();
            menuPerm.setMenuId(menuId);
            menuPerm.setPermId(permId);
            return menuPerm;
        }).collect(Collectors.toList());
        menuPermMapper.insertBatch(menuPermList);
    }

    private void checkSaveOrUpdate(String menuId, String parentId, MenuTypeEnum menuType, String appCode, Set<String> permIds) {
        // 校验菜单存在
        SysMenu menu = checkExist(menuId);
        if (ObjectUtil.isNotNull(menu)) {
            // 校验菜单类型
            AssertUtils.isTrue(menuType != menu.getMenuType(), "菜单类型不允许修改");
            // 校验应用标识
            AssertUtils.isTrue(!StrUtil.equals(appCode, menu.getAppCode()), "应用标识不允许修改");
            // 校验动态表单内置菜单
            AssertUtils.isTrue(menu.getOnlineMenuPermType() != null, "动态表单内置菜单不允许修改");
        }
        // 校验上级菜单的有效性
        checkParentMenu(menuId, parentId, menuType, appCode);
        // 校验权限
        AssertUtils.isTrue(!permMapper.existAllPrimaryKeys(permIds), ErrorCodeEnum.INVALID_RELATED_RECORD_ID);
    }

    private void checkParentMenu(String menuId, String parentId, MenuTypeEnum menuType, String appCode) {
        if (StrUtil.isBlank(parentId) || CommonConstants.PARENT_ID_ROOT.equals(parentId)) {
            return;
        }
        // 1. 不能设置自己为上级菜单
        AssertUtils.isTrue(StrUtil.equals(menuId, parentId), "不能设置自己为上级菜单");
        // 2. 上级菜单不存在
        SysMenu parentMenu = baseMapper.selectById(parentId);
        AssertUtils.isTrue(Objects.isNull(parentMenu), "上级菜单不存在");
        AssertUtils.isTrue(StrUtil.isNotBlank(parentMenu.getOnlineFormId()), "不能为动态表单菜单添加子菜单");
        // 3. 校验应用标识
        AssertUtils.isTrue(!StrUtil.equals(appCode, parentMenu.getAppCode()), "上级菜单应用标识与当前菜单不一致");
        // 4. 逐个判断每种类型的菜单的上级菜单的合法性
        if (menuType == MenuTypeEnum.DIRECTORY || menuType == MenuTypeEnum.MENU) {
            AssertUtils.isTrue(parentMenu.getMenuType() != MenuTypeEnum.DIRECTORY, "当前类型菜单项的上级菜单类型必须为目录");
        }
        // 5. 递归校验上级菜单，如果上级菜单是自己的子菜单，则报错，避免形成环路
        // menuId 为空，说明新增，不需要考虑环路
        if (menuId == null) {
            return;
        }
        for (int i = 0; i < Short.MAX_VALUE; i++) {
            // 4.1 校验环路
            parentId = parentMenu.getParentId();
            AssertUtils.isTrue(StrUtil.equals(menuId, parentId), ErrorCodeEnum.PARENT_IS_CHILD);
            // 4.2 继续递归下一级上级菜单
            if (StrUtil.isBlank(parentId) || CommonConstants.PARENT_ID_ROOT.equals(parentId)) {
                break;
            }
            parentMenu = baseMapper.selectById(parentId);
            if (parentMenu == null) {
                break;
            }
        }
    }

}
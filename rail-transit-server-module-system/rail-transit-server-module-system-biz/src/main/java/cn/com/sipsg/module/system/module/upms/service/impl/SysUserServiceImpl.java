package cn.com.sipsg.module.system.module.upms.service.impl;

import cn.com.sipsg.common.constant.CommonConstants;
import cn.com.sipsg.common.constant.PageConstants;
import cn.com.sipsg.common.constant.RegexConstants;
import cn.com.sipsg.common.datapermission.core.util.DataPermissionUtils;
import cn.com.sipsg.common.enums.CommonStatusEnum;
import cn.com.sipsg.common.exception.BusinessException;
import cn.com.sipsg.common.exception.enums.ErrorCodeEnum;
import cn.com.sipsg.common.mybatis.core.query.LambdaQueryWrapperX;
import cn.com.sipsg.common.mybatis.core.query.MPJLambdaWrapperX;
import cn.com.sipsg.common.pojo.vo.CommonImportVO;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.security.core.util.SecurityUtils;
import cn.com.sipsg.common.translate.core.TranslateUtils;
import cn.com.sipsg.common.util.*;
import cn.com.sipsg.common.validation.group.RawTextLengthGroup;
import cn.com.sipsg.common.web.core.util.ExcelUtils;
import cn.com.sipsg.module.system.module.upms.convert.SysUserConvert;
import cn.com.sipsg.module.system.module.upms.entity.*;
import cn.com.sipsg.module.system.module.upms.mapper.*;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysUserQueryBO;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysUserResetPasswordBO;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysUserSaveBO;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysUserUpdatePasswordBO;
import cn.com.sipsg.module.system.module.upms.pojo.dto.SysUserDeptPostDTO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysDeptPostVO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysUserVO;
import cn.com.sipsg.module.system.module.upms.service.SysDeptService;
import cn.com.sipsg.module.system.module.upms.service.SysUserService;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.RegexPool;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.PhoneUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.BCrypt;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Validator;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 用户服务实现类
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements SysUserService {

    private final SysUserPostMapper userPostMapper;

    private final SysUserDeptMapper userDeptMapper;

    private final SysUserRoleMapper userRoleMapper;

    private final SysDeptPostMapper deptPostMapper;

    private final SysDeptService deptService;

    private final SysRoleMapper roleMapper;

    private final Validator validator;

    private final HttpServletResponse response;

    @Override
    public CommonPageVO<SysUserVO> page(SysUserQueryBO bo) {
        MPJLambdaWrapperX<SysUser> queryWrapper = new MPJLambdaWrapperX<>();
        queryWrapper.selectAll(SysUser.class)
                .likeIfPresent(SysUser::getUsername, bo.getUsername())
                .likeIfPresent(SysUser::getRealName, bo.getRealName())
                .likeIfPresent(SysUser::getMobile, bo.getMobile())
                .eqIfPresent(SysUser::getStatus, bo.getStatus())
                .betweenIfPresent(SysUser::getCreateTime, bo.getCreateTimeStart(), bo.getCreateTimeEnd());
        if (StrUtil.isNotBlank(bo.getDeptId())) {
            Set<String> deptIds = this.getDeptCondition(bo.getDeptId());
            queryWrapper.leftJoin(SysUserDept.class, SysUserDept::getUserId, SysUser::getUserId)
                    .in(SysUserDept::getDeptId, deptIds)
                    .distinct();
        }
        CommonPageVO<SysUserVO> page = this.selectJoinListPage(bo, SysUserVO.class, queryWrapper);
        for (SysUserVO record : page.getRecords()) {
            record.setRoleIds(CollectionUtils.convertSet(userRoleMapper.selectByUserId(record.getUserId()), SysUserRole::getRoleId));
            record.setDeptIds(CollectionUtils.convertSet(userDeptMapper.selectByUserId(record.getUserId()), SysUserDept::getDeptId));
        }
        return page;
    }

    @Override
    @Transactional
    public String save(SysUserSaveBO bo) {
        bo.setUserId(null);
        // 校验用户
        checkSaveOrUpdate(null, bo.getUsername(), bo.getMobile(), bo.getDeptPostList(), bo.getRoleIds());
        // 校验密码格式
        bo.setPassword(checkPasswordFormat(bo.getPassword()));
        ValidationUtils.validate(validator, bo, RawTextLengthGroup.class);
        // 密码加密
        bo.setPassword(encodePassword(bo.getPassword()));
        // 新增用户
        SysUser user = SysUserConvert.INSTANCE.convert(bo);
        this.save(user);
        // 新增用户部门关系
        // 新增用户岗位关系
        if (CollUtil.isNotEmpty(bo.getDeptPostList())) {
            for (SysUserDeptPostDTO deptPostDTO : bo.getDeptPostList()) {
                SysUserDept userDept = new SysUserDept();
                userDept.setUserId(user.getUserId());
                userDept.setDeptId(deptPostDTO.getDeptId());
                userDeptMapper.insert(userDept);
                for (String deptPostId : deptPostDTO.getDeptPostIds()) {
                    SysDeptPost deptPost = deptPostMapper.selectById(deptPostId);
                    SysUserPost userPost = new SysUserPost();
                    userPost.setDeptPostId(deptPostId);
                    userPost.setUserId(user.getUserId());
                    userPost.setPostId(deptPost.getPostId());
                    userPostMapper.insert(userPost);
                }
            }
        }
        // 新增用户角色关系
        if (CollUtil.isNotEmpty(bo.getRoleIds())) {
            for (String roleId : bo.getRoleIds()) {
                SysUserRole userRole = new SysUserRole();
                userRole.setUserId(user.getUserId());
                userRole.setRoleId(roleId);
                userRoleMapper.insert(userRole);
            }
        }
        return user.getUserId();
    }

    @Override
    @Transactional
    public void update(SysUserSaveBO bo) {
        bo.setPassword(null);
        bo.setUsername(null);
        // 超级管理员禁止编辑
        AssertUtils.isTrue(SecurityUtils.isSuperAdmin(bo.getUserId()), ErrorCodeEnum.FORBIDDEN_SUPER_ADMIN_USER);
        // 校验用户
        checkSaveOrUpdate(bo.getUserId(), bo.getUsername(), bo.getMobile(), bo.getDeptPostList(), bo.getRoleIds());
        // 更新用户
        SysUser user = SysUserConvert.INSTANCE.convert(bo);
        this.updateById(user);
        // 先删除原有的用户部门关系，再插入新的关系
        userDeptMapper.deleteByUserId(user.getUserId());
        // 先删除原有的用户岗位关系，再插入新的关系
        userPostMapper.deleteByUserId(user.getUserId());
        if (CollUtil.isNotEmpty(bo.getDeptPostList())) {
            for (SysUserDeptPostDTO deptPostDTO : bo.getDeptPostList()) {
                SysUserDept userDept = new SysUserDept();
                userDept.setUserId(user.getUserId());
                userDept.setDeptId(deptPostDTO.getDeptId());
                userDeptMapper.insert(userDept);
                for (String deptPostId : deptPostDTO.getDeptPostIds()) {
                    SysDeptPost deptPost = deptPostMapper.selectById(deptPostId);
                    SysUserPost userPost = new SysUserPost();
                    userPost.setDeptPostId(deptPostId);
                    userPost.setUserId(user.getUserId());
                    userPost.setPostId(deptPost.getPostId());
                    userPostMapper.insert(userPost);
                }
            }
        }
        // 先删除原有的用户角色关系，再插入新的关系
        userRoleMapper.deleteByUserId(user.getUserId());
        if (CollUtil.isNotEmpty(bo.getRoleIds())) {
            for (String roleId : bo.getRoleIds()) {
                SysUserRole userRole = new SysUserRole();
                userRole.setUserId(user.getUserId());
                userRole.setRoleId(roleId);
                userRoleMapper.insert(userRole);
            }
        }
        // 如果状态为禁用，踢出用户
        if (user.getStatus() == CommonStatusEnum.DISABLE) {
            StpUtil.logout(user.getUserId());
        }
    }

    @Override
    public SysUserVO detail(String userId) {
        SysUser user = getByIdDeep(userId);
        // 校验是否存在
        AssertUtils.isTrue(ObjectUtil.isNull(user), ErrorCodeEnum.DATA_NOT_EXIST);

        SysUserVO userVO = BeanUtils.copyProperties(user, SysUserVO.class);
        // 填充部门岗位ID
        MPJLambdaWrapperX<SysDeptPost> queryWrapper = new MPJLambdaWrapperX<>();
        queryWrapper
                .selectAll(SysDeptPost.class)
                .innerJoin(SysUserPost.class, SysUserPost::getDeptPostId, SysDeptPost::getDeptPostId)
                .eq(SysUserPost::getUserId, user.getUserId());
        List<SysDeptPostVO> deptPostList = deptPostMapper.selectJoinList(SysDeptPostVO.class, queryWrapper);
        List<SysUserDeptPostDTO> userDeptPostDTOList = new ArrayList<>();
        userVO.setDeptPostList(userDeptPostDTOList);
        Map<String, Set<String>> deptPostMap = CollectionUtils.convertMultiMap2(deptPostList, SysDeptPostVO::getDeptId, SysDeptPostVO::getDeptPostId);
        for (String deptId : user.getDeptIds()) {
            SysUserDeptPostDTO userDeptPostDTO = new SysUserDeptPostDTO();
            userDeptPostDTO.setDeptId(deptId);
            userDeptPostDTO.setDeptPostIds(deptPostMap.getOrDefault(deptId, Collections.emptySet()));
            userDeptPostDTOList.add(userDeptPostDTO);
        }
        return userVO;
    }

    @Override
    @Transactional
    public void delete(String userId) {
        SysUser user = this.getById(userId);
        if (ObjectUtil.isNull(user)) {
            return;
        }
        // 超级管理员禁止删除
        AssertUtils.isTrue(SecurityUtils.isSuperAdmin(userId), ErrorCodeEnum.FORBIDDEN_SUPER_ADMIN_USER);
        // 删除用户
        this.removeById(userId);
        // 删除用户岗位关系
        userPostMapper.deleteByUserId(userId);
        // 删除用户部门关系
        userDeptMapper.deleteByUserId(userId);
        // 删除用户角色关系
        userRoleMapper.deleteByUserId(userId);
        // 踢出用户
        StpUtil.logout(userId);
    }

    @Override
    public void resetPassword(SysUserResetPasswordBO bo) {
        // 校验用户存在
        checkExist(bo.getUserId());
        // 只有超级管理员自己才能重置管理员密码
        boolean invalid = !StrUtil.equals(SecurityUtils.getLoginUserId(), bo.getUserId()) && SecurityUtils.isSuperAdmin(bo.getUserId());
        AssertUtils.isTrue(invalid, ErrorCodeEnum.FORBIDDEN_SUPER_ADMIN_USER);
        // 校验密码格式
        bo.setPassword(checkPasswordFormat(bo.getPassword()));
        ValidationUtils.validate(validator, bo, RawTextLengthGroup.class);
        // 更新密码
        SysUser updateObj = new SysUser();
        updateObj.setUserId(bo.getUserId());
        updateObj.setPassword(encodePassword(bo.getPassword()));
        this.updateById(updateObj);
        // 踢出用户
        StpUtil.logout(bo.getUserId());
    }

    @Override
    public void updateStatus(String userId, Integer status) {
        // 校验用户存在
        checkExist(userId);
        // 超级管理员禁止修改状态
        AssertUtils.isTrue(SecurityUtils.isSuperAdmin(userId), ErrorCodeEnum.FORBIDDEN_SUPER_ADMIN_USER);
        // 更新状态
        SysUser updateObj = new SysUser();
        updateObj.setUserId(userId);
        CommonStatusEnum statusEnum = EnumUtils.getEnumByValue(CommonStatusEnum.class, status);
        updateObj.setStatus(statusEnum);
        this.updateById(updateObj);
        // 如果状态为禁用，踢出用户
        if (statusEnum == CommonStatusEnum.DISABLE) {
            StpUtil.logout(userId);
        }
    }

    @Override
    public void updatePassword(String userId, SysUserUpdatePasswordBO bo) {
        // 校验旧密码格式
        String oldPassword = checkPasswordFormat(bo.getOldPassword());
        // 校验旧密码密码
        checkOldPassword(userId, oldPassword);
        // 校验新密码格式
        bo.setNewPassword(checkPasswordFormat(bo.getNewPassword()));
        ValidationUtils.validate(validator, bo, RawTextLengthGroup.class);
        // 执行更新
        SysUser updateObj = new SysUser();
        updateObj.setUserId(userId);
        updateObj.setPassword(encodePassword(bo.getNewPassword()));
        this.updateById(updateObj);
        // 强制下线
        StpUtil.logout(userId);
    }

    @Override
    public void updateAvatar(String userId, String avatar) {
        // 判断文件类型是图片
        AssertUtils.isTrue(!ReUtil.isMatch(RegexConstants.IMAGE, FileUtil.extName(avatar)), "仅支持图片类型文件");
        // 执行更新
        SysUser updateObj = new SysUser();
        updateObj.setUserId(userId);
        updateObj.setAvatar(avatar);
        this.updateById(updateObj);
    }

    @Override
    public boolean isPasswordMatch(String rawPassword, String encodedPassword) {
        return StrUtil.equals(encodePassword(rawPassword), encodedPassword);
    }

    @Override
    public SysUser getByUsername(String username) {
        return this.getOne(new LambdaQueryWrapperX<SysUser>().eq(SysUser::getUsername, username), false);
    }

    @Override
    public void export(SysUserQueryBO bo) {
        bo.setSize(PageConstants.SIZE_NONE);
        List<SysUserVO> userList = this.page(bo).getRecords();
        TranslateUtils.translate(userList);
        ExcelWriter writer = ExcelUtil.getWriter();
        List<Map<String, Object>> rows = new ArrayList<>(userList.size());
        for (SysUserVO userVO : userList) {
            Map<String, Object> row = new LinkedHashMap<>();
            row.put("账号", userVO.getUsername());
            row.put("用户名", userVO.getRealName());
            row.put("手机号码", userVO.getMobile());
            row.put("邮箱", userVO.getEmail());
            row.put("所属部门", userVO.getDeptName());
            row.put("角色", userVO.getRoleName());
            row.put("状态", userVO.getStatus().getDesc());
            rows.add(row);
        }
        writer.write(rows, true);
        ExcelUtils.export(response, writer, "用户导出");
    }

    @Override
    @Transactional
    public CommonImportVO importUser(MultipartFile file) {
        List<Map<String, Object>> dataList = ExcelUtils.readAll(file);
        AssertUtils.isTrue(CollUtil.isEmpty(dataList), ErrorCodeEnum.IMPORT_FILE_ERROR);
        return importUser(dataList);
    }

    @Override
    @Transactional
    public CommonImportVO importUser(List<Map<String, Object>> dataList) {
        AssertUtils.isTrue(CollUtil.isEmpty(dataList), ErrorCodeEnum.IMPORT_FILE_ERROR);
        List<Map<String, Object>> errorRecords = new ArrayList<>();
        AtomicLong successTotal = new AtomicLong();
        AtomicLong errorTotal = new AtomicLong();
        DataPermissionUtils.executeIgnore(() -> {
            for (Map<String, Object> dataMap : dataList) {
                StringBuilder sb = new StringBuilder();
                String username = StrUtil.trim(MapUtil.getStr(dataMap, "账号"));
                String password = StrUtil.trim(MapUtil.getStr(dataMap, "密码"));
                String realName = StrUtil.trim(MapUtil.getStr(dataMap, "用户名"));
                String mobile = StrUtil.trim(MapUtil.getStr(dataMap, "手机号码"));
                String email = StrUtil.trim(MapUtil.getStr(dataMap, "邮箱"));
                String deptName = StrUtil.trim(MapUtil.getStr(dataMap, "所属部门"));
                String roleName = StrUtil.trim(MapUtil.getStr(dataMap, "角色"));
                String statusDesc = StrUtil.trim(MapUtil.getStr(dataMap, "状态"));
                // 校验账号
                if (StrUtil.isBlank(username)) {
                    sb.append("账号不能为空;");
                } else {
                    if (!ReUtil.isMatch(RegexConstants.USERNAME, username)) {
                        sb.append("账号格式不正确;");
                    }
                    Long count = baseMapper.selectCount(SysUser::getUsername, username);
                    if (count > 0) {
                        sb.append("账号已存在;");
                    }
                }
                // 校验密码
                if (StrUtil.isBlank(password)) {
                    password = CommonConstants.DEFAULT_USER_PASSWORD;
                } else {
                    int length = password.length();
                    if (length < 4 || length > 16) {
                        sb.append("密码长度不正确;");
                    }
                }
                // 校验用户名
                if (StrUtil.isBlank(realName)) {
                    sb.append("用户名不能为空;");
                }
                if (StrUtil.isNotBlank(mobile)) {
                    // 校验手机号
                    if (!PhoneUtil.isMobile(mobile)) {
                        sb.append("手机号码格式不正确;");
                    } else {
                        long count = baseMapper.selectCount(SysUser::getMobile, mobile);
                        if (count > 0) {
                            sb.append("手机号码已存在;");
                        }
                    }
                }
                // 校验邮箱
                if (StrUtil.isNotBlank(email) && !ReUtil.isMatch(RegexPool.EMAIL, email)) {
                    sb.append("邮箱格式不正确;");
                }
                // 处理部门
                List<String> deptIds = new ArrayList<>();
                if (StrUtil.isNotBlank(deptName)) {
                    List<String> deptNames = StrUtil.split(deptName, StrUtil.COMMA);
                    boolean deptErrorFlag = false;
                    for (String name : deptNames) {
                        SysDept dept = deptService.getOne(new LambdaQueryWrapperX<SysDept>().eq(SysDept::getDeptName, name), false);
                        if (ObjectUtil.isNull(dept)) {
                            deptErrorFlag = true;
                            break;
                        }
                        deptIds.add(dept.getDeptId());
                    }
                    if (deptErrorFlag) {
                        sb.append("所属部门不存在;");
                    }
                }
                // 校验角色
                List<String> roleIds = new ArrayList<>();
                if (StrUtil.isNotBlank(roleName)) {
                    boolean roleErrorFlag = false;
                    List<String> roleNames = StrUtil.split(roleName, StrUtil.COMMA);
                    for (String name : roleNames) {
                        SysRole role = roleMapper.selectOne(SysRole::getRoleName, name, false);
                        if (ObjectUtil.isNull(role)) {
                            roleErrorFlag = true;
                            break;
                        }
                        roleIds.add(role.getRoleId());
                    }
                    if (roleErrorFlag) {
                        sb.append("角色不存在;");
                    }

                }
                // 校验状态
                CommonStatusEnum status = null;
                if (StrUtil.isBlank(statusDesc)) {
                    sb.append("状态不能为空;");
                } else {
                    status = EnumUtils.getEnumByDesc(CommonStatusEnum.class, statusDesc);
                    if (ObjectUtil.isNull(status)) {
                        sb.append("状态不正确;");
                    }
                }

                if (sb.length() > 0) {
                    errorTotal.getAndIncrement();
                    dataMap.put("错误提示", sb);
                    errorRecords.add(dataMap);
                    continue;
                }
                // 保存用户
                SysUser sysUser = new SysUser();
                sysUser.setUsername(username);
                sysUser.setPassword(encodePassword(password));
                sysUser.setRealName(realName);
                sysUser.setMobile(mobile);
                sysUser.setEmail(email);
                sysUser.setStatus(status);
                this.save(sysUser);

                // 保存用户角色关系
                if (CollUtil.isNotEmpty(roleIds)) {
                    for (String roleId : roleIds) {
                        SysUserRole sysUserRole = new SysUserRole();
                        sysUserRole.setUserId(sysUser.getUserId());
                        sysUserRole.setRoleId(roleId);
                        userRoleMapper.insert(sysUserRole);
                    }
                }

                // 保存用户部门关系
                if (CollUtil.isNotEmpty(deptIds)) {
                    for (String deptId : deptIds) {
                        SysUserDept sysUserDept = new SysUserDept();
                        sysUserDept.setUserId(sysUser.getUserId());
                        sysUserDept.setDeptId(deptId);
                        userDeptMapper.insert(sysUserDept);
                    }
                }
                successTotal.getAndIncrement();
            }
        });
        return CommonImportVO.builder().successTotal(successTotal.get()).errorTotal(errorTotal.get()).errorRecords(errorRecords).build();
    }

    @Override
    public List<SysUser> getUserListByDeptIds(Collection<String> deptIds) {
        if (CollUtil.isEmpty(deptIds)) {
            return Collections.emptyList();
        }
        Set<String> allDeptIds = new HashSet<>();
        for (String deptId : deptIds) {
            allDeptIds.addAll(getDeptCondition(deptId));
        }
        MPJLambdaWrapperX<SysUser> queryWrapperX = new MPJLambdaWrapperX<>();
        queryWrapperX.selectAll(SysUser.class)
                .innerJoin(SysUserDept.class, SysUserDept::getUserId, SysUser::getUserId)
                .in(SysUserDept::getDeptId, allDeptIds)
                .eq(SysUser::getStatus, CommonStatusEnum.ENABLE)
                .distinct();
        return this.selectJoinList(SysUser.class, queryWrapperX);
    }

    @Override
    public List<SysUser> getUserListByRoleIds(Collection<String> roleIds) {
        if (CollUtil.isEmpty(roleIds)) {
            return Collections.emptyList();
        }
        MPJLambdaWrapperX<SysUser> queryWrapperX = new MPJLambdaWrapperX<>();
        queryWrapperX.selectAll(SysUser.class)
                .innerJoin(SysUserRole.class, SysUserRole::getUserId, SysUser::getUserId)
                .in(SysUserRole::getRoleId, roleIds)
                .eq(SysUser::getStatus, CommonStatusEnum.ENABLE)
                .distinct();
        return this.selectJoinList(SysUser.class, queryWrapperX);
    }

    @Override
    public List<SysUser> getUserListByPostIds(Collection<String> postIds) {
        if (CollUtil.isEmpty(postIds)) {
            return Collections.emptyList();
        }
        MPJLambdaWrapperX<SysUser> queryWrapperX = new MPJLambdaWrapperX<>();
        queryWrapperX.selectAll(SysUser.class)
                .innerJoin(SysUserPost.class, SysUserPost::getUserId, SysUser::getUserId)
                .in(SysUserPost::getPostId, postIds)
                .eq(SysUser::getStatus, CommonStatusEnum.ENABLE)
                .distinct();
        return this.selectJoinList(SysUser.class, queryWrapperX);
    }

    @Override
    public List<SysUser> getUserListByDeptPostIds(Collection<String> deptPostIds) {
        if (CollUtil.isEmpty(deptPostIds)) {
            return Collections.emptyList();
        }
        MPJLambdaWrapperX<SysUser> queryWrapperX = new MPJLambdaWrapperX<>();
        queryWrapperX.selectAll(SysUser.class)
                .innerJoin(SysUserPost.class, SysUserPost::getUserId, SysUser::getUserId)
                .in(SysUserPost::getDeptPostId, deptPostIds)
                .eq(SysUser::getStatus, CommonStatusEnum.ENABLE)
                .distinct();
        return this.selectJoinList(SysUser.class, queryWrapperX);
    }

    private void checkSaveOrUpdate(String userId, String username, String mobile, List<SysUserDeptPostDTO> deptPostList, Set<String> roleIds) {
        // 校验用户存在
        checkExist(userId);
        // 校验用户名唯一
        DataPermissionUtils.executeIgnore(() -> checkUsernameUnique(userId, username));
        // 校验手机号唯一
        DataPermissionUtils.executeIgnore(() -> checkPhoneUnique(userId, mobile));
        // 校验部门
        AssertUtils.isTrue(!deptService.existAllPrimaryKeys(CollectionUtils.convertSet(deptPostList, SysUserDeptPostDTO::getDeptId)), ErrorCodeEnum.INVALID_RELATED_RECORD_ID);
        // 校验角色
        AssertUtils.isTrue(!roleMapper.existAllPrimaryKeys(roleIds), ErrorCodeEnum.INVALID_RELATED_RECORD_ID);
        // 校验部门岗位
        if (CollUtil.isNotEmpty(deptPostList)) {
            for (SysUserDeptPostDTO userDeptPostDTO : deptPostList) {
                if (CollUtil.isEmpty(userDeptPostDTO.getDeptPostIds())) {
                    continue;
                }
                LambdaQueryWrapperX<SysDeptPost> queryWrapper = new LambdaQueryWrapperX<>();
                queryWrapper
                        .eq(SysDeptPost::getDeptId, userDeptPostDTO.getDeptId())
                        .in(SysDeptPost::getDeptPostId, userDeptPostDTO.getDeptPostIds());
                AssertUtils.isTrue(deptPostMapper.selectCount(queryWrapper) != userDeptPostDTO.getDeptPostIds().size(), ErrorCodeEnum.INVALID_RELATED_RECORD_ID);
            }
        }
    }

    private void checkUsernameUnique(String userId, String username) {
        if (StrUtil.isBlank(username)) {
            return;
        }
        long count = this.count(new LambdaQueryWrapperX<SysUser>().eq(SysUser::getUsername, username).neIfPresent(SysUser::getUserId, userId));
        AssertUtils.isTrue(count > 0, String.format("用户名【%s】已存在", username));
    }

    private void checkPhoneUnique(String userId, String mobile) {
        if (StrUtil.isBlank(mobile)) {
            return;
        }
        long count = this.count(new LambdaQueryWrapperX<SysUser>().eq(SysUser::getMobile, mobile).neIfPresent(SysUser::getUserId, userId));
        AssertUtils.isTrue(count > 0, String.format("手机号【%s】已存在", mobile));
    }

    /**
     * 校验旧密码
     *
     * @param userId      用户ID
     * @param oldPassword 旧密码
     */
    private void checkOldPassword(String userId, String oldPassword) {
        // 校验用户存在
        SysUser user = checkExist(userId);
        // 校验旧密码
        AssertUtils.isTrue(!BCrypt.checkpw(oldPassword, user.getPassword()), "旧密码校验失败");
    }

    /**
     * 校验密码格式
     *
     * @param password 密码
     * @return 原始密码
     */
    private String checkPasswordFormat(String password) {
        try {
            password = SmUtils.sm2DecryptStr(password);
            return password;
        } catch (Exception e) {
            log.warn("密码解密失败", e);
            throw new BusinessException("密码不合法");
        }
    }

    /**
     * 对密码进行加密
     *
     * @param password 密码
     * @return 加密后的密码
     */
    private String encodePassword(String password) {
        return BCrypt.hashpw(password);
    }

    /**
     * 获取部门条件，查询指定部门的子部门ID，包括自身
     *
     * @param deptId 部门ID
     * @return 部门ID列表
     */
    private Set<String> getDeptCondition(String deptId) {
        if (StrUtil.isBlank(deptId)) {
            return Collections.emptySet();
        }
        Set<String> deptIds = CollectionUtils.convertSet(deptService.getChildDeptList(deptId), SysDept::getDeptId);
        deptIds.add(deptId);
        return deptIds;
    }

}

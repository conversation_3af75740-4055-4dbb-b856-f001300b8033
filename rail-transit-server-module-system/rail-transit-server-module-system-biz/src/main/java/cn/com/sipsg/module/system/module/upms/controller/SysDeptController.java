package cn.com.sipsg.module.system.module.upms.controller;

import cn.com.sipsg.common.constant.PageConstants;
import cn.com.sipsg.common.operationlog.core.annotation.OperationLog;
import cn.com.sipsg.common.operationlog.core.enums.OperationTypeEnum;
import cn.com.sipsg.common.pojo.CommonResult;
import cn.com.sipsg.common.pojo.vo.CommonImportVO;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.util.BeanUtils;
import cn.com.sipsg.common.validation.AddValidated;
import cn.com.sipsg.common.validation.UpdateValidated;
import cn.com.sipsg.module.system.module.upms.entity.SysDept;
import cn.com.sipsg.module.system.module.upms.pojo.bo.*;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysDeptPostVO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysDeptSimpleVO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysDeptVO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysPostVO;
import cn.com.sipsg.module.system.module.upms.service.SysDeptService;
import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 部门控制器
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Tag(name = "部门管理")
@RestController
@RequiredArgsConstructor
@RequestMapping("/system/upms/dept")
public class SysDeptController {

    private final SysDeptService deptService;

    /**
     * 查询部门列表
     *
     * @param bo 参数
     * @return 部门列表
     */
    @Operation(summary = "查询部门列表")
    @OperationLog(module = "部门管理", value = "查询部门列表", type = OperationTypeEnum.QUERY, saveResponse = false)
    @SaCheckPermission(value = "system:upms:dept:list")
    @PostMapping("/list")
    public CommonResult<List<SysDeptVO>> list(@Validated @RequestBody SysDeptQueryBO bo) {
        bo.setSize(PageConstants.SIZE_NONE);
        return CommonResult.data(deptService.page(bo).getRecords());
    }

    /**
     * 查询部门精简信息列表
     *
     * @return 部门列表
     */
    @Operation(summary = "查询部门精简信息列表", description = "主要用于前端的下拉选项")
    @GetMapping("/simpleList")
    public CommonResult<List<SysDeptSimpleVO>> getSimpleList() {
        return CommonResult.data(BeanUtils.copyToList(deptService.list(), SysDeptSimpleVO.class));
    }

    /**
     * 新增部门
     *
     * @param bo 参数
     * @return 部门ID
     */
    @Operation(summary = "新增部门")
    @OperationLog(module = "部门管理", value = "新增部门", type = OperationTypeEnum.SAVE)
    @SaCheckPermission(value = "system:upms:dept:save")
    @PostMapping("/save")
    public CommonResult<String> save(@AddValidated @RequestBody SysDeptSaveBO bo) {
        String deptId = deptService.save(bo);
        return CommonResult.data(deptId);
    }

    /**
     * 编辑部门
     *
     * @param bo 参数
     */
    @Operation(summary = "编辑部门")
    @OperationLog(module = "部门管理", value = "编辑部门", type = OperationTypeEnum.UPDATE)
    @SaCheckPermission(value = "system:upms:dept:update")
    @PostMapping("/update")
    public CommonResult<Void> update(@UpdateValidated @RequestBody SysDeptSaveBO bo) {
        deptService.update(bo);
        return CommonResult.success();
    }

    /**
     * 删除部门
     *
     * @param deptId 部门ID
     */
    @Operation(summary = "删除部门")
    @Parameter(name = "deptId", description = "部门ID", required = true, example = "1111")
    @OperationLog(module = "部门管理", value = "删除部门", type = OperationTypeEnum.DELETE)
    @SaCheckPermission(value = "system:upms:dept:delete")
    @PostMapping("/delete")
    public CommonResult<Void> delete(@RequestParam String deptId) {
        deptService.delete(deptId);
        return CommonResult.success();
    }

    /**
     * 查询部门详情
     *
     * @param deptId 部门ID
     */
    @Operation(summary = "查询部门详情")
    @Parameter(name = "deptId", description = "部门ID", required = true, example = "1111")
    @OperationLog(module = "部门管理", value = "查询部门详情", type = OperationTypeEnum.QUERY, saveResponse = false)
    @SaCheckPermission(value = "system:upms:dept:detail")
    @GetMapping("/detail")
    public CommonResult<SysDeptVO> detail(@RequestParam String deptId) {
        SysDeptVO deptVO = deptService.detail(deptId);
        return CommonResult.data(deptVO);
    }

    /**
     * 查询部门岗位列表
     *
     * @param bo 参数
     * @return 岗位列表
     */
    @Operation(summary = "查询部门岗位列表")
    @OperationLog(module = "部门管理", value = "查询部门岗位列表", type = OperationTypeEnum.QUERY, saveResponse = false)
    @SaCheckPermission(value = "system:upms:dept:post:list")
    @PostMapping("/post/list")
    public CommonResult<List<SysDeptPostVO>> getDeptPostList(@Validated @RequestBody SysDeptPostQueryBO bo) {
        bo.setSize(PageConstants.SIZE_NONE);
        return CommonResult.data(deptService.getDeptPostPage(bo).getRecords());
    }

    /**
     * 查询部门岗位分页列表
     *
     * @param bo 参数
     * @return 岗位分页列表
     */
    @Operation(summary = "查询部门岗位分页列表")
    @OperationLog(module = "部门管理", value = "查询部门岗位分页列表", type = OperationTypeEnum.QUERY, saveResponse = false)
    @SaCheckPermission(value = "system:upms:dept:post:list")
    @PostMapping("/post/page")
    public CommonResult<CommonPageVO<SysDeptPostVO>> getDeptPostPage(@Validated @RequestBody SysDeptPostQueryBO bo) {
        return CommonResult.data(deptService.getDeptPostPage(bo));
    }

    /**
     * 查询不在指定部门下的岗位分页列表
     *
     * @param bo 参数
     * @return 岗位分页列表
     */
    @Operation(summary = "查询不在指定部门下的岗位分页列表")
    @OperationLog(module = "部门管理", value = "查询不在指定部门下的岗位分页列表", type = OperationTypeEnum.QUERY, saveResponse = false)
    @SaCheckPermission(value = "system:upms:dept:post:notInList")
    @PostMapping("/post/notInPage")
    public CommonResult<CommonPageVO<SysPostVO>> getNotInDeptPostPage(@Validated @RequestBody SysDeptPostQueryBO bo) {
        return CommonResult.data(deptService.getNotInDeptPostPage(bo));
    }

    /**
     * 新增部门岗位关系
     *
     * @param bo 参数
     */
    @Operation(summary = "新增部门岗位关系")
    @OperationLog(module = "部门管理", value = "新增部门岗位关系", type = OperationTypeEnum.SAVE)
    @SaCheckPermission(value = "system:upms:dept:post:save")
    @PostMapping("/post/save")
    public CommonResult<Void> saveDeptPost(@Validated @RequestBody SysDeptPostSaveBO bo) {
        deptService.saveDeptPost(bo.getDeptId(), bo.getPostList());
        return CommonResult.success();
    }

    /**
     * 编辑部门岗位关系
     *
     * @param bo 参数
     */
    @Operation(summary = "编辑部门岗位关系")
    @OperationLog(module = "部门管理", value = "编辑部门岗位关系", type = OperationTypeEnum.UPDATE)
    @SaCheckPermission(value = "system:upms:dept:post:update")
    @PostMapping("/post/update")
    public CommonResult<Void> updateDeptPost(@Validated @RequestBody SysDeptPostUpdateBO bo) {
        deptService.updateDeptPost(bo);
        return CommonResult.success();
    }

    /**
     * 删除部门岗位关系
     *
     * @param deptPostId 部门岗位关系ID
     */
    @Operation(summary = "删除部门岗位关系")
    @Parameter(name = "deptPostId", description = "部门岗位关系ID", required = true, example = "1111")
    @OperationLog(module = "部门管理", value = "删除部门岗位关系", type = OperationTypeEnum.DELETE)
    @SaCheckPermission(value = "system:upms:dept:post:delete")
    @PostMapping("/post/delete")
    public CommonResult<Void> deleteDeptPost(@RequestParam String deptPostId) {
        deptService.deleteDeptPost(deptPostId);
        return CommonResult.success();
    }

    /**
     * 导出部门
     */
    @Operation(summary = "导出部门")
    @OperationLog(module = "部门管理", value = "导出部门", type = OperationTypeEnum.EXPORT, saveResponse = false)
    @SaCheckPermission(value = "system:upms:dept:export")
    @GetMapping("/export")
    public void export() {
        deptService.export();
    }

    /**
     * 导入部门
     *
     * @param file 文件
     */
    @Operation(summary = "导入部门")
    @OperationLog(module = "部门管理", value = "导入部门", type = OperationTypeEnum.IMPORT)
    @SaCheckPermission(value = "system:upms:dept:import")
    @PostMapping("/import")
    public CommonResult<CommonImportVO> importDept(@RequestParam MultipartFile file) {
        return CommonResult.data(deptService.importDept(file));
    }

    /**
     * 查询指定用户的部门列表
     *
     * @param userId 用户ID
     * @return 部门列表
     */
    @Operation(summary = "查询指定用户的部门列表")
    @Parameter(name = "userId", description = "用户ID", required = true, example = "1111")
    @GetMapping("/listByUserId")
    public CommonResult<List<SysDeptVO>> getDeptListByUserId(@RequestParam String userId) {
        List<SysDept> deptList = deptService.getDeptListByUserId(userId);
        return CommonResult.data(BeanUtils.copyToList(deptList, SysDeptVO.class));
    }

}

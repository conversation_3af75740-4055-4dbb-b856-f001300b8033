package cn.com.sipsg.module.system.module.upms.service.impl;

import cn.com.sipsg.common.mybatis.core.query.LambdaQueryWrapperX;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.util.BeanUtils;
import cn.com.sipsg.module.system.api.upms.dto.SysOperationLogSaveReqDTO;
import cn.com.sipsg.module.system.module.upms.convert.SysOperationLogConvert;
import cn.com.sipsg.module.system.module.upms.entity.SysOperationLog;
import cn.com.sipsg.module.system.module.upms.mapper.SysOperationLogMapper;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysOperationLogQueryBO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysOperationLogVO;
import cn.com.sipsg.module.system.module.upms.service.SysOperationLogService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 操作日志表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/14
*/
@Service
public class SysOperationLogServiceImpl extends ServiceImpl<SysOperationLogMapper, SysOperationLog> implements SysOperationLogService {

    @Override
    public CommonPageVO<SysOperationLogVO> page(SysOperationLogQueryBO bo) {
        LambdaQueryWrapperX<SysOperationLog> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper
                .likeIfPresent(SysOperationLog::getUsername, bo.getUsername())
                .likeIfPresent(SysOperationLog::getModuleName, bo.getModuleName())
                .eqIfPresent(SysOperationLog::getOperationType, bo.getOperationType())
                .eqIfPresent(SysOperationLog::getSuccess, bo.getSuccess())
                .eqIfPresent(SysOperationLog::getAppCode, bo.getAppCode())
                .betweenIfPresent(SysOperationLog::getDuration, bo.getDurationMin(), bo.getDurationMax());
        if (bo.getOperationDateStart() != null) {
            queryWrapper.ge(SysOperationLog::getOperationTime, bo.getOperationDateStart().atStartOfDay());
        }
        if (bo.getOperationDateEnd() != null) {
            queryWrapper.le(SysOperationLog::getOperationTime, bo.getOperationDateEnd().atStartOfDay().plusDays(1));
        }
        return this.page(bo, queryWrapper).convert(operationLog -> BeanUtils.copyProperties(operationLog, SysOperationLogVO.class));
    }

    @Override
    @Async
    public void saveAsync(SysOperationLogSaveReqDTO reqDTO) {
        this.save(SysOperationLogConvert.INSTANCE.convert(reqDTO));
    }

}
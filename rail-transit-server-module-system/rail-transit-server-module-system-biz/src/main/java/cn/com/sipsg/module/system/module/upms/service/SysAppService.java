package cn.com.sipsg.module.system.module.upms.service;

import cn.com.sipsg.common.mybatis.core.service.BaseServiceX;
import cn.com.sipsg.common.pojo.bo.DeleteBO;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.module.system.module.upms.entity.SysApp;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysAppQueryBO;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysAppSaveBO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysAppVO;

/**
 * 应用服务接口
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public interface SysAppService extends BaseServiceX<SysApp> {

    /**
     * 查询应用分页列表
     *
     * @param bo 参数
     * @return 应用分页列表
     */
    CommonPageVO<SysAppVO> page(SysAppQueryBO bo);

    /**
     * 新增应用
     *
     * @param bo 参数
     * @return 应用ID
     */
    String save(SysAppSaveBO bo);

    /**
     * 编辑应用
     *
     * @param bo 参数
     */
    void update(SysAppSaveBO bo);

    /**
     * 编辑状态
     *
     * @param appId  应用ID
     * @param status 状态
     */
    void updateStatus(String appId, Integer status);

    /**
     * 根据appId查询应用详情
     *
     * @param appId 应用ID
     * @return 应用详情
     */
    SysAppVO detail(String appId);

    /**
     * 删除应用
     *
     * @param appId 应用ID
     */
    void delete(String appId);

    /**
     * 批量删除应用
     *
     * @param bo 参数
     */
    void deleteBatch(DeleteBO bo);

}
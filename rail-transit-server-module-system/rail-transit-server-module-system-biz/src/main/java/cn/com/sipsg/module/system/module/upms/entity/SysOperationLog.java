package cn.com.sipsg.module.system.module.upms.entity;

import cn.com.sipsg.common.operationlog.core.enums.OperationLogLevelEnum;
import cn.com.sipsg.common.operationlog.core.enums.OperationTypeEnum;
import com.baomidou.mybatisplus.annotation.OrderBy;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <p>
 * 操作日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@TableName(value = "medox_sys_operation_log", autoResultMap = true)
public class SysOperationLog {

    /**
     * 日志ID
     */
    @TableId
    private String logId;

    /**
     * 链路追踪ID
     */
    private String traceId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 接口所在服务名称;通常为spring.application.name配置项的值
     */
    private String serviceName;

    /**
     * 调用的controller全类名
     */
    private String apiClass;

    /**
     * 调用的controller中的方法
     */
    private String apiMethod;

    /**
     * 模块名称
     */
    private String moduleName;

    /**
     * 操作名称
     */
    private String operationName;

    /**
     * 操作类型
     */
    private OperationTypeEnum operationType;

    /**
     * 操作时间
     */
    @OrderBy
    private LocalDateTime operationTime;

    /**
     * 执行时长
     */
    private Long duration;

    /**
     * 日志等级
     */
    private OperationLogLevelEnum logLevel;

    /**
     * 请求方法名
     */
    private String requestMethod;

    /**
     * 请求地址
     */
    private String requestUrl;

    /**
     * 请求IP
     */
    private String requestIp;

    /**
     * controller请求参数
     */
    private String requestArguments;

    /**
     * controller应答结果
     */
    private String responseResult;

    /**
     * 浏览器UA
     */
    private String userAgent;

    /**
     * 应答状态
     */
    private Boolean success;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 应用标识
     */
    private String appCode;

}

package cn.com.sipsg.module.system.module.upms.service;

import cn.com.sipsg.common.mybatis.core.service.BaseServiceX;
import cn.com.sipsg.module.system.module.upms.entity.SysFilePart;
import org.dromara.x.file.storage.core.upload.FilePartInfo;

/**
 * <p>
 * 文件分片表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public interface SysFilePartService extends BaseServiceX<SysFilePart> {

    /**
     * 保存文件分片信息
     *
     * @param info 文件分片信息
     */
    void saveFilePart(FilePartInfo info);

    /**
     * 删除文件分片信息
     *
     * @param uploadId 上传ID
     */
    void deleteFilePartByUploadId(String uploadId);

}

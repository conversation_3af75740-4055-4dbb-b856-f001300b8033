package cn.com.sipsg.module.system.module.upms.controller;

import cn.com.sipsg.common.enums.CommonStatusEnum;
import cn.com.sipsg.common.mybatis.core.query.LambdaQueryWrapperX;
import cn.com.sipsg.common.operationlog.core.annotation.OperationLog;
import cn.com.sipsg.common.operationlog.core.enums.OperationTypeEnum;
import cn.com.sipsg.common.pojo.CommonResult;
import cn.com.sipsg.common.pojo.bo.UpdateStatusBO;
import cn.com.sipsg.common.pojo.vo.CommonImportVO;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.util.BeanUtils;
import cn.com.sipsg.common.validation.AddValidated;
import cn.com.sipsg.common.validation.UpdateValidated;
import cn.com.sipsg.module.system.module.upms.entity.SysDict;
import cn.com.sipsg.module.system.module.upms.entity.SysDictItem;
import cn.com.sipsg.module.system.module.upms.enums.DictTypeEnum;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysDictQueryBO;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysDictSaveBO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysDictItemSimpleVO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysDictSimpleVO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysDictVO;
import cn.com.sipsg.module.system.module.upms.service.SysDictService;
import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 字典控制器
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Tag(name = "字典管理")
@RestController
@RequiredArgsConstructor
@RequestMapping("/system/upms/dict")
public class SysDictController {

    private final SysDictService dictService;

    /**
     * 查询字典分页列表
     *
     * @param bo 参数
     * @return 字典分页列表
     */
    @Operation(summary = "查询字典分页列表")
    @OperationLog(module = "字典管理", value = "查询字典分页列表", type = OperationTypeEnum.QUERY, saveResponse = false)
    @SaCheckPermission(value = "system:upms:dict:list")
    @PostMapping("/page")
    public CommonResult<CommonPageVO<SysDictVO>> page(@RequestBody SysDictQueryBO bo) {
        return CommonResult.data(dictService.page(bo));
    }

    /**
     * 查询字典简单信息列表
     *
     * @return 字典简单信息列表
     */
    @Operation(summary = "查询字典简单信息列表")
    @GetMapping("/simpleList")
    public CommonResult<List<SysDictSimpleVO>> getSimpleList() {
        LambdaQueryWrapperX<SysDict> queryWrapper = new LambdaQueryWrapperX<SysDict>()
                .eq(SysDict::getStatus, CommonStatusEnum.ENABLE)
                .eq(SysDict::getDictType, DictTypeEnum.BIZ);
        List<SysDict> dictList = dictService.list(queryWrapper);
        return CommonResult.data(BeanUtils.copyToList(dictList, SysDictSimpleVO.class));
    }

    /**
     * 新增字典
     *
     * @param bo 参数
     * @return 字典ID
     */
    @Operation(summary = "新增字典")
    @OperationLog(module = "字典管理", value = "新增字典", type = OperationTypeEnum.SAVE)
    @SaCheckPermission(value = "system:upms:dict:save")
    @PostMapping("/save")
    public CommonResult<String> save(@AddValidated @RequestBody SysDictSaveBO bo) {
        return CommonResult.data(dictService.save(bo));
    }

    /**
     * 编辑字典
     *
     * @param bo 参数
     */
    @Operation(summary = "编辑字典")
    @OperationLog(module = "字典管理", value = "编辑字典", type = OperationTypeEnum.UPDATE)
    @SaCheckPermission(value = "system:upms:dict:update")
    @PostMapping("/update")
    public CommonResult<Void> update(@UpdateValidated @RequestBody SysDictSaveBO bo) {
        dictService.update(bo);
        return CommonResult.success();
    }

    /**
     * 查询字典详情
     *
     * @param dictId 字典ID
     * @return 字典详情
     */
    @Operation(summary = "查询字典详情")
    @Parameter(name = "dictId", description = "字典ID", required = true)
    @OperationLog(module = "字典管理", value = "查询字典详情", type = OperationTypeEnum.QUERY, saveResponse = false)
    @SaCheckPermission(value = "system:upms:dict:detail")
    @GetMapping("/detail")
    public CommonResult<SysDictVO> detail(@RequestParam String dictId) {
        return CommonResult.data(dictService.detail(dictId));
    }

    /**
     * 删除字典
     *
     * @param dictId 字典ID
     */
    @Operation(summary = "删除字典")
    @Parameter(name = "dictId", description = "字典ID", required = true)
    @OperationLog(module = "字典管理", value = "删除字典", type = OperationTypeEnum.DELETE)
    @SaCheckPermission(value = "system:upms:dict:delete")
    @PostMapping("/delete")
    public CommonResult<Void> delete(@RequestParam String dictId) {
        dictService.delete(dictId);
        return CommonResult.success();
    }

    /**
     * 编辑字典状态
     *
     * @param bo 参数
     */
    @Operation(summary = "编辑字典状态")
    @OperationLog(module = "字典管理", value = "编辑字典状态", type = OperationTypeEnum.UPDATE)
    @SaCheckPermission(value = "system:upms:dict:update")
    @PostMapping("/updateStatus")
    public CommonResult<Void> updateStatus(@Validated @RequestBody UpdateStatusBO bo) {
        dictService.updateStatus(bo.getId(), bo.getStatus());
        return CommonResult.success();
    }

    /**
     * 查询指定字典编码字典项简单信息列表
     *
     * @param dictCode 字典编码
     * @return 字典项简单信息列表
     */
    @Operation(summary = "查询指定字典编码字典项简单信息列表")
    @Parameter(name = "dictCode", description = "字典编码", required = true)
    @GetMapping("/items")
    public CommonResult<List<SysDictItemSimpleVO>> getItemSimpleListByDictCode(@RequestParam String dictCode) {
        List<SysDictItem> itemList = dictService.getItemListByDictCode(dictCode);
        return CommonResult.data(BeanUtils.copyToList(itemList, SysDictItemSimpleVO.class));
    }

    /**
     * 查询指定字典编码字典项简单信息列表（包含禁用）
     *
     * @param dictCode 字典编码
     * @return 字典项简单信息列表（包含禁用）
     */
    @Operation(summary = "查询指定字典编码字典项简单信息列表（包含禁用）")
    @Parameter(name = "dictCode", description = "字典编码", required = true)
    @GetMapping("/allItems")
    public CommonResult<List<SysDictItemSimpleVO>> getItemListIncludeDisableByDictCode(@RequestParam String dictCode) {
        List<SysDictItem> itemList = dictService.getItemListByDictCode(dictCode, Boolean.TRUE);
        return CommonResult.data(BeanUtils.copyToList(itemList, SysDictItemSimpleVO.class));
    }

    /**
     * 导出字典
     *
     * @param bo 参数
     */
    @Operation(summary = "导出字典")
    @OperationLog(module = "字典管理", value = "导出字典", type = OperationTypeEnum.EXPORT, saveResponse = false)
    @SaCheckPermission(value = "system:upms:dict:export")
    @PostMapping("/export")
    public void export(@RequestBody SysDictQueryBO bo) {
        dictService.export(bo);
    }

    /**
     * 导入字典
     *
     * @param file 文件
     */
    @Operation(summary = "导入字典")
    @OperationLog(module = "字典管理", value = "导入字典", type = OperationTypeEnum.IMPORT)
    @SaCheckPermission(value = "system:upms:dict:import")
    @PostMapping("/import")
    public CommonResult<Map<String, CommonImportVO>> importDict(@RequestParam MultipartFile file) {
        return CommonResult.data(dictService.importDict(file));
    }

}

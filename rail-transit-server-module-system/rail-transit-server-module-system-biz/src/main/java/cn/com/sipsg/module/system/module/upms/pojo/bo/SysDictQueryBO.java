package cn.com.sipsg.module.system.module.upms.pojo.bo;

import cn.com.sipsg.common.enums.CommonStatusEnum;
import cn.com.sipsg.common.pojo.bo.SortablePageBO;
import cn.com.sipsg.common.validation.InEnum;
import cn.com.sipsg.module.system.module.upms.enums.DictTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 字典查询 BO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "字典查询 BO")
public class SysDictQueryBO extends SortablePageBO {

    /**
     * 字典名称
     */
    @Schema(description = "字典名称")
    private String dictName;

    /**
     * 字典编码
     */
    @Schema(description = "字典编码")
    private String dictCode;

    /**
     * 字典类型
     */
    @Schema(description = "字典类型")
    @InEnum(enumClass = DictTypeEnum.class, message = "字典类型值不正确")
    private String dictType;

    /**
     * 状态
     */
    @Schema(description = "状态")
    @InEnum(enumClass = CommonStatusEnum.class, message = "状态值不正确")
    private Integer status;

}

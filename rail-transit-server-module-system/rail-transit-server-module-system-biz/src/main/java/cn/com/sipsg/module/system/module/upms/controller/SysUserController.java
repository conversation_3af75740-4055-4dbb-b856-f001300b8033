package cn.com.sipsg.module.system.module.upms.controller;

import cn.com.sipsg.common.datapermission.core.annotation.DataPermission;
import cn.com.sipsg.common.enums.CommonStatusEnum;
import cn.com.sipsg.common.mybatis.core.query.LambdaQueryWrapperX;
import cn.com.sipsg.common.operationlog.core.annotation.OperationLog;
import cn.com.sipsg.common.operationlog.core.enums.OperationTypeEnum;
import cn.com.sipsg.common.pojo.CommonResult;
import cn.com.sipsg.common.pojo.bo.UpdateStatusBO;
import cn.com.sipsg.common.pojo.vo.CommonImportVO;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.security.core.util.SecurityUtils;
import cn.com.sipsg.common.util.BeanUtils;
import cn.com.sipsg.common.validation.AddValidated;
import cn.com.sipsg.common.validation.UpdateValidated;
import cn.com.sipsg.module.system.module.upms.entity.SysUser;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysUserQueryBO;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysUserResetPasswordBO;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysUserSaveBO;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysUserUpdatePasswordBO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysUserSimpleVO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysUserVO;
import cn.com.sipsg.module.system.module.upms.service.SysUserService;
import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.util.StrUtil;
import com.fhs.core.trans.anno.IgnoreTrans;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.Arrays;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 用户控制器
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Tag(name = "用户管理")
@RestController
@RequiredArgsConstructor
@RequestMapping("/system/upms/user")
public class SysUserController {

    private final SysUserService userService;

    /**
     * 查询用户分页列表
     *
     * @param bo 参数
     * @return 用户分页列表
     */
    @Operation(summary = "查询用户分页列表")
    @OperationLog(module = "用户管理", value = "查询用户分页列表", type = OperationTypeEnum.QUERY, saveResponse = false)
    @SaCheckPermission(value = "system:upms:user:list")
    @PostMapping("/page")
    public CommonResult<CommonPageVO<SysUserVO>> page(@Validated @RequestBody SysUserQueryBO bo) {
        CommonPageVO<SysUserVO> page = userService.page(bo);
        return CommonResult.data(page);
    }

    /**
     * 查询用户精简信息列表
     *
     * @return 用户列表
     */
    @Operation(summary = "查询用户精简信息列表", description = "只包含被开启的用户，主要用于前端的下拉选项")
    @GetMapping("/simpleList")
    public CommonResult<List<SysUserSimpleVO>> getSimpleList() {
        LambdaQueryWrapperX<SysUser> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eq(SysUser::getStatus, CommonStatusEnum.ENABLE);
        List<SysUser> userList = userService.list(queryWrapper);
        return CommonResult.data(BeanUtils.copyToList(userList, SysUserSimpleVO.class));
    }

    /**
     * 新增用户
     *
     * @param bo 参数
     * @return 用户ID
     */
    @Operation(summary = "新增用户")
    @OperationLog(module = "用户管理", value = "新增用户", type = OperationTypeEnum.SAVE)
    @SaCheckPermission(value = "system:upms:user:save")
    @PostMapping("/save")
    public CommonResult<String> save(@AddValidated @RequestBody SysUserSaveBO bo) {
        String userId = userService.save(bo);
        return CommonResult.data(userId);
    }

    /**
     * 编辑用户
     *
     * @param bo 参数
     */
    @Operation(summary = "编辑用户")
    @OperationLog(module = "用户管理", value = "编辑用户", type = OperationTypeEnum.UPDATE)
    @SaCheckPermission(value = "system:upms:user:update")
    @PostMapping("/update")
    public CommonResult<Void> update(@UpdateValidated @RequestBody SysUserSaveBO bo) {
        userService.update(bo);
        return CommonResult.success();
    }

    /**
     * 删除用户
     *
     * @param userId 用户ID
     */
    @Operation(summary = "删除用户")
    @Parameter(name = "userId", description = "用户ID", required = true, example = "1111")
    @OperationLog(module = "用户管理", value = "删除用户", type = OperationTypeEnum.DELETE)
    @SaCheckPermission(value = "system:upms:user:delete")
    @PostMapping("/delete")
    public CommonResult<Void> delete(@RequestParam String userId) {
        userService.delete(userId);
        return CommonResult.success();
    }

    /**
     * 重置用户密码
     *
     * @param bo 参数
     */
    @Operation(summary = "重置用户密码")
    @OperationLog(module = "用户管理", value = "重置用户密码", type = OperationTypeEnum.UPDATE)
    @SaCheckPermission(value = "system:upms:user:resetPassword")
    @PostMapping("/resetPassword")
    public CommonResult<Void> resetPassword(@Validated @RequestBody SysUserResetPasswordBO bo) {
        userService.resetPassword(bo);
        return CommonResult.success();
    }

    /**
     * 编辑用户状态
     *
     * @param bo 参数
     */
    @Operation(summary = "编辑用户状态")
    @OperationLog(module = "用户管理", value = "编辑用户状态", type = OperationTypeEnum.UPDATE)
    @SaCheckPermission(value = "system:upms:user:update")
    @PostMapping("/updateStatus")
    public CommonResult<Void> updateStatus(@Validated @RequestBody UpdateStatusBO bo) {
        userService.updateStatus(bo.getId(), bo.getStatus());
        return CommonResult.success();
    }

    /**
     * 修改密码
     *
     * @param bo 参数
     */
    @DataPermission(enable = false)
    @Operation(summary = "修改密码")
    @PostMapping("/updatePassword")
    public CommonResult<Void> updatePassword(@Validated @RequestBody SysUserUpdatePasswordBO bo) {
        userService.updatePassword(SecurityUtils.getLoginUserId(), bo);
        return CommonResult.success();
    }

    /**
     * 修改头像
     *
     * @param avatar 头像
     */
    @DataPermission(enable = false)
    @Operation(summary = "修改头像")
    @Parameter(name = "avatar", description = "头像地址", required = true)
    @PostMapping("/updateAvatar")
    public CommonResult<Void> updateAvatar(@RequestParam String avatar) {
        userService.updateAvatar(SecurityUtils.getLoginUserId(), avatar);
        return CommonResult.success();
    }

    /**
     * 查询用户详情
     *
     * @param userId 用户ID
     * @return 用户详情
     */
    @IgnoreTrans
    @Operation(summary = "查询用户详情")
    @Parameter(name = "userId", description = "用户ID", required = true, example = "1111")
    @OperationLog(module = "用户管理", value = "查询用户详情", type = OperationTypeEnum.QUERY, saveResponse = false)
    @SaCheckPermission(value = "system:upms:user:detail")
    @GetMapping("/detail")
    public CommonResult<SysUserVO> detail(@RequestParam String userId) {
        SysUserVO user = userService.detail(userId);
        return CommonResult.data(user);
    }

    /**
     * 用户id字符串，多个以逗号拼接
     *
     * @param userIds 用户id字符串，多个以逗号拼接
     * @return 用户VO对象
     */
    @Operation(summary = "用户id字符串，多个以逗号拼接")
    @Parameter(name = "userIds", description = "用户id字符串，多个以逗号拼接", required = true)
    @GetMapping("/getUserInfoByUserIds")
    public CommonResult<List<SysUserVO>> getUserInfoByUserIds(@RequestParam String userIds) {
        List<SysUser> sysUsers = userService.listByIdsDeep(StrUtil.split(userIds, StrUtil.COMMA));
        return CommonResult.data(BeanUtils.copyToList(sysUsers, SysUserVO.class));
    }

    /**
     * 导出用户
     *
     * @param bo 参数
     */
    @Operation(summary = "导出用户")
    @OperationLog(module = "用户管理", value = "导出用户", type = OperationTypeEnum.EXPORT, saveResponse = false)
    @SaCheckPermission(value = "system:upms:user:export")
    @PostMapping("/export")
    public void export(@RequestBody SysUserQueryBO bo) {
        userService.export(bo);
    }

    /**
     * 导入用户
     *
     * @param file 文件
     */
    @Operation(summary = "导入用户")
    @OperationLog(module = "用户管理", value = "导入用户", type = OperationTypeEnum.IMPORT)
    @SaCheckPermission(value = "system:upms:user:import")
    @PostMapping("/import")
    public CommonResult<CommonImportVO> importUser(@RequestParam MultipartFile file) {
        return CommonResult.data(userService.importUser(file));
    }

}

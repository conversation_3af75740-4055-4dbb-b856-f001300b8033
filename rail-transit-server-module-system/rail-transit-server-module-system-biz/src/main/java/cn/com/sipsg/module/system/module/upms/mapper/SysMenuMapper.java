package cn.com.sipsg.module.system.module.upms.mapper;

import cn.com.sipsg.common.mybatis.core.mapper.BaseMapperX;
import cn.com.sipsg.module.system.module.upms.entity.SysMenu;
import com.baomidou.dynamic.datasource.annotation.Master;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 菜单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Master
@Mapper
public interface SysMenuMapper extends BaseMapperX<SysMenu> {

    default List<SysMenu> selectByParentIds(Collection<String> parentIds) {
        return selectList(SysMenu::getParentId, parentIds);
    }

    default List<SysMenu> selectByParentId(String parentId) {
        return selectList(SysMenu::getParentId, parentId);
    }

}

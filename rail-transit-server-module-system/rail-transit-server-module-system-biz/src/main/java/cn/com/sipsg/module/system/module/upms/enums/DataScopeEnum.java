package cn.com.sipsg.module.system.module.upms.enums;

import cn.com.sipsg.common.enums.BaseEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 数据范围枚举类
 * 用于实现数据级别的权限
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@RequiredArgsConstructor
public enum DataScopeEnum implements BaseEnum {

    ALL(1, "全部数据权限"),

    DEPT_CUSTOM(2, "指定部门数据权限"),
    DEPT_ONLY(3, "部门数据权限"),
    DEPT_AND_CHILD(4, "部门及以下数据权限"),

    SELF(5, "仅本人数据权限");

    @EnumValue
    @JsonValue
    private final Integer code;

    private final String desc;

}

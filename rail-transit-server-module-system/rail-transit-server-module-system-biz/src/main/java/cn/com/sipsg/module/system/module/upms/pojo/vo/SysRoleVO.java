package cn.com.sipsg.module.system.module.upms.pojo.vo;

import cn.com.sipsg.module.system.module.upms.enums.DataScopeEnum;
import com.baomidou.mybatisplus.annotation.OrderBy;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Set;

/**
 * 角色 VO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "角色 VO")
public class SysRoleVO {

    /**
     * 角色ID
     */
    @Schema(description = "角色ID")
    private String roleId;

    /**
     * 角色名称
     */
    @Schema(description = "角色名称")
    private String roleName;

    /**
     * 角色编码
     */
    @Schema(description = "角色编码")
    private String roleCode;

    /**
     * 数据范围
     */
    @Schema(description = "数据范围")
    private DataScopeEnum dataScope;

    /**
     * 数据范围(指定部门数组)
     */
    @Schema(description = "数据范围(指定部门数组)")
    private Set<String> dataScopeDeptIds;

    /**
     * 菜单ID列表
     */
    @Schema(description = "菜单ID列表")
    private Set<String> menuIds;

}

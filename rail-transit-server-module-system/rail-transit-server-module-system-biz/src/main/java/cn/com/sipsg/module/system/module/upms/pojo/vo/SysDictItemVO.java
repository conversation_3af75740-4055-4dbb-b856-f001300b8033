package cn.com.sipsg.module.system.module.upms.pojo.vo;

import cn.com.sipsg.common.enums.CommonStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 字典项 VO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "字典项 VO")
public class SysDictItemVO {

    /**
     * 字典项 ID
     */
    @Schema(description = "字典项 ID")
    private String itemId;

    /**
     * 字典编码
     */
    @Schema(description = "字典编码")
    private String dictCode;

    /**
     * 字典项名称
     */
    @Schema(description = "字典项名称")
    private String itemName;

    /**
     * 字典项编码
     */
    @Schema(description = "字典项编码")
    private String itemCode;

    /**
     * 排序值
     */
    @Schema(description = "排序值")
    private Integer sort;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private CommonStatusEnum status;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

}

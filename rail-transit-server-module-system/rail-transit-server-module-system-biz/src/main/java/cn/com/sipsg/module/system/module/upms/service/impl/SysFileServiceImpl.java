package cn.com.sipsg.module.system.module.upms.service.impl;

import cn.com.sipsg.common.exception.BusinessException;
import cn.com.sipsg.common.mybatis.core.query.LambdaQueryWrapperX;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.util.AssertUtils;
import cn.com.sipsg.common.util.BeanUtils;
import cn.com.sipsg.common.web.core.util.ServletUtils;
import cn.com.sipsg.module.system.module.upms.convert.SysFileConvert;
import cn.com.sipsg.module.system.module.upms.entity.SysFile;
import cn.com.sipsg.module.system.module.upms.mapper.SysFileMapper;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysFileQueryBO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysFilePartVO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysFileVO;
import cn.com.sipsg.module.system.module.upms.service.SysFilePartService;
import cn.com.sipsg.module.system.module.upms.service.SysFileService;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.dromara.x.file.storage.core.FileInfo;
import org.dromara.x.file.storage.core.FileStorageService;
import org.dromara.x.file.storage.core.recorder.FileRecorder;
import org.dromara.x.file.storage.core.upload.FilePartInfo;
import org.dromara.x.file.storage.core.upload.FilePartInfoList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 文件表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Service
@RequiredArgsConstructor
public class SysFileServiceImpl extends ServiceImpl<SysFileMapper, SysFile> implements SysFileService, FileRecorder {

    private static final String DATE_FORMAT = "yyyy/MM/dd";

    @Autowired
    private FileStorageService fileStorageService;

    @Autowired
    private SysFilePartService filePartService;

    private final HttpServletResponse response;

    /**
     * 保存文件信息到数据库
     */
    @Override
    public boolean save(FileInfo fileInfo) {
        SysFile file = SysFileConvert.INSTANCE.convert(fileInfo);
        boolean b = save(file);
        if (b) {
            fileInfo.setId(file.getId());
        }
        return b;
    }

    /**
     * 更新文件记录，可以根据文件 ID 或 URL 来更新文件记录，
     * 主要用在手动分片上传文件-完成上传，作用是更新文件信息
     */
    @Override
    public void update(FileInfo fileInfo) {
        SysFile file = SysFileConvert.INSTANCE.convert(fileInfo);
        LambdaQueryWrapperX<SysFile> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper
                .eqIfPresent(SysFile::getId, file.getId())
                .eqIfPresent(SysFile::getUrl, file.getUrl());
        update(file, queryWrapper);
    }

    /**
     * 根据 url 查询文件信息
     */
    @Override
    public FileInfo getByUrl(String url) {
        LambdaQueryWrapperX<SysFile> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eq(SysFile::getUrl, url);
        return SysFileConvert.INSTANCE.convert(getOne(queryWrapper));
    }

    /**
     * 根据 url 删除文件信息
     */
    @Override
    public boolean delete(String url) {
        LambdaQueryWrapperX<SysFile> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eq(SysFile::getUrl, url);
        return remove(queryWrapper);
    }

    /**
     * 保存文件分片信息
     */
    @Override
    public void saveFilePart(FilePartInfo filePartInfo) {
        filePartService.saveFilePart(filePartInfo);
    }

    /**
     * 删除文件分片信息
     */
    @Override
    public void deleteFilePartByUploadId(String uploadId) {
        filePartService.deleteFilePartByUploadId(uploadId);
    }

    @Override
    public CommonPageVO<SysFileVO> page(SysFileQueryBO bo) {
        LambdaQueryWrapperX<SysFile> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper
                .likeIfPresent(SysFile::getOriginalFileName, bo.getFileName())
                .eqIfPresent(SysFile::getGroupCode, bo.getGroupCode());
        return page(bo, queryWrapper).convert(file -> BeanUtils.copyProperties(file, SysFileVO.class));
    }

    @Override
    public SysFile upload(MultipartFile file, String groupCode) {
        FileInfo fileInfo = fileStorageService
                .of(file)
                .setHashCalculatorMd5()
                .setPath(getPath())
                .putAttr(StrUtil.isNotEmpty(groupCode), SysFile.Fields.groupCode, groupCode)
                .upload();
        return SysFileConvert.INSTANCE.convert(fileInfo);
    }

    @Override
    public SysFile uploadBase64(String base64, String fileName, String groupCode) {
        // 去除base64的前缀
        int index = base64.indexOf(StrUtil.COMMA);
        if (index > 0) {
            base64 = base64.substring(index + 1);
        }
        FileInfo fileInfo = fileStorageService
                .of(Base64.decode(base64))
                .setHashCalculatorMd5()
                .setOriginalFilename(fileName)
                .setPath(getPath())
                .putAttr(StrUtil.isNotEmpty(groupCode), SysFile.Fields.groupCode, groupCode)
                .upload();
        return SysFileConvert.INSTANCE.convert(fileInfo);
    }

    @Override
    public SysFile upload(InputStream inputStream, String originalFileName, String groupCode) {
        try {
            FileInfo fileInfo = fileStorageService
                    .of(inputStream)
                    .setHashCalculatorMd5()
                    .setOriginalFilename(originalFileName)
                    .setPath(getPath())
                    .putAttr(StrUtil.isNotEmpty(groupCode), SysFile.Fields.groupCode, groupCode)
                    .upload();
            return SysFileConvert.INSTANCE.convert(fileInfo);
        } finally {
            IoUtil.close(inputStream);
        }
    }

    @Override
    public void deleteById(String fileId) {
        SysFile file = this.getById(fileId);
        if (Objects.nonNull(file)) {
            fileStorageService.delete(file.getUrl());
        }
    }

    @Override
    public void deleteBatch(List<String> fileIds) {
        for (String id : fileIds) {
            SysFile file = this.getById(id);
            if (Objects.nonNull(file)) {
                fileStorageService.delete(file.getUrl());
            }
        }
    }

    @Override
    public void download(String fileId) {
        SysFile file = this.checkExist(fileId);
        fileStorageService.download(file.getUrl()).inputStream(in -> {
            try {
                ServletUtils.writeAttachment(response, file.getOriginalFileName(), in, file.getContentType());
            } catch (IOException e) {
                log.error("下载文件失败", e);
                throw new BusinessException("下载文件失败");
            }
        });
    }

    @Override
    public SysFile getFileByHash(String fileHash, String groupCode) {
        return this.getOne(new LambdaQueryWrapperX<SysFile>().eq(SysFile::getFileHash, fileHash).eqIfPresent(SysFile::getGroupCode, groupCode), false);
    }

    @Override
    public String initMultipartUpload(String fileName, String fileHash, String groupCode) {
        FileInfo fileInfo = fileStorageService.initiateMultipartUpload()
                .putAttr(StrUtil.isNotEmpty(groupCode), SysFile.Fields.groupCode, groupCode)
                .putAttr(SysFile.Fields.fileHash, fileHash)
                .setOriginalFilename(fileName)
                .setPath(getPath())
                .init();
        return fileInfo.getUploadId();
    }

    @Override
    public SysFilePartVO uploadPart(String uploadId, Integer partNumber, MultipartFile file) {
        FileInfo fileInfo = getFileInfoByUploadId(uploadId);
        FilePartInfo filePartInfo = fileStorageService.uploadPart(fileInfo, partNumber, file)
                .upload();
        return BeanUtils.copyProperties(filePartInfo, SysFilePartVO.class);
    }

    @Override
    public SysFile completeMultipartUpload(String uploadId) {
        FileInfo fileInfo = getFileInfoByUploadId(uploadId);
        fileInfo = fileStorageService.completeMultipartUpload(fileInfo).complete();
        return SysFileConvert.INSTANCE.convert(fileInfo);
    }

    @Override
    public List<SysFilePartVO> queryUploadedParts(String uploadId) {
        FileInfo fileInfo = getFileInfoByUploadId(uploadId);
        FilePartInfoList partList = fileStorageService.listParts(fileInfo).listParts();
        return BeanUtils.copyToList(partList.getList(), SysFilePartVO.class);
    }

    @Override
    public void abortMultipartUpload(String uploadId) {
        FileInfo fileInfo = getFileInfoByUploadId(uploadId);
        fileStorageService.abortMultipartUpload(fileInfo).abort();
    }

    /**
     * 获取相对路径
     *
     * @return 相对路径
     */
    private String getPath() {
        return LocalDateTimeUtil.format(LocalDate.now(), DATE_FORMAT) + StrUtil.SLASH;
    }

    /**
     * 根据上传ID查询文件信息
     *
     * @param uploadId 上传ID
     * @return 文件信息
     */
    private FileInfo getFileInfoByUploadId(String uploadId) {
        SysFile sysFile = this.getOne(new LambdaQueryWrapperX<SysFile>().eq(SysFile::getUploadId, uploadId), false);
        AssertUtils.isTrue(ObjectUtil.isNull(sysFile), "分片信息不存在");
        return SysFileConvert.INSTANCE.convert(sysFile);
    }

}

package cn.com.sipsg.module.system.module.upms.service;

import cn.com.sipsg.common.mybatis.core.service.BaseServiceX;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.module.system.module.upms.entity.SysPost;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysPostQueryBO;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysPostSaveBO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysPostVO;

/**
 * <p>
 * 岗位表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public interface SysPostService extends BaseServiceX<SysPost> {

    /**
     * 查询岗位分页列表
     *
     * @param bo 参数
     * @return 岗位分页列表
     */
    CommonPageVO<SysPostVO> page(SysPostQueryBO bo);

    /**
     * 新增岗位
     *
     * @param bo 参数
     * @return 岗位ID
     */
    String save(SysPostSaveBO bo);

    /**
     * 编辑岗位
     *
     * @param bo 参数
     */
    void update(SysPostSaveBO bo);

    /**
     * 删除岗位
     *
     * @param postId 岗位ID
     */
    void delete(String postId);

    /**
     * 查询岗位详情
     *
     * @param postId 岗位ID
     * @return 岗位详情
     */
    SysPostVO detail(String postId);

}
package cn.com.sipsg.module.system.module.upms.service;

import cn.com.sipsg.common.mybatis.core.service.BaseServiceX;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.module.system.module.upms.entity.SysPerm;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysPermQueryBO;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysPermSaveBO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysPermVO;

import java.util.Set;

/**
 * <p>
 * 权限表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public interface SysPermService extends BaseServiceX<SysPerm> {

    /**
     * 查询权限分页
     *
     * @param bo 参数
     * @return 权限分页
     */
    CommonPageVO<SysPermVO> page(SysPermQueryBO bo);

    /**
     * 新增权限
     *
     * @param bo 参数
     * @return 权限ID
     */
    String save(SysPermSaveBO bo);

    /**
     * 编辑权限
     *
     * @param bo 参数
     */
    void update(SysPermSaveBO bo);

    /**
     * 删除权限
     *
     * @param permId 权限ID
     */
    void delete(String permId);

    /**
     * 查询权限详情
     *
     * @param permId 权限ID
     * @return 权限详情
     */
    SysPermVO detail(String permId);

    /**
     * 查询指定用户拥有的权限标识
     *
     * @param userId  用户ID
     * @param appCode 应用标识
     * @return 权限标识列表
     */
    Set<String> getPermCodes(String userId, String appCode);

}

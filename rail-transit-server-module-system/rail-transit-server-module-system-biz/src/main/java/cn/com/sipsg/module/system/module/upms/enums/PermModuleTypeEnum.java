package cn.com.sipsg.module.system.module.upms.enums;

import cn.com.sipsg.common.enums.BaseEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldNameConstants;

/**
 * 权限模块类型枚举
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@FieldNameConstants
@RequiredArgsConstructor
public enum PermModuleTypeEnum implements BaseEnum {

    GROUP(0, "分组模块"),

    CONTROLLER(1, "接口模块"),;

    @JsonValue
    @EnumValue
    private final Integer code;

    private final String desc;

}

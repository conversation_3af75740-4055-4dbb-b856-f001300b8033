package cn.com.sipsg.module.system.module.upms.service.impl;

import cn.com.sipsg.common.mybatis.core.query.LambdaQueryWrapperX;
import cn.com.sipsg.common.security.core.util.SecurityUtils;
import cn.com.sipsg.common.util.BeanUtils;
import cn.com.sipsg.module.system.module.upms.entity.SysFilePart;
import cn.com.sipsg.module.system.module.upms.mapper.SysFilePartMapper;
import cn.com.sipsg.module.system.module.upms.service.SysFilePartService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.dromara.x.file.storage.core.upload.FilePartInfo;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <p>
 * 文件分片表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Service
public class SysFilePartServiceImpl extends ServiceImpl<SysFilePartMapper, SysFilePart> implements SysFilePartService {

    @Override
    public void saveFilePart(FilePartInfo info) {
        SysFilePart filePart = BeanUtils.copyProperties(info, SysFilePart.class);
        filePart.setCreateTime(LocalDateTime.now());
        filePart.setCreateBy(SecurityUtils.getLoginUserId());
        if (save(filePart)) {
            info.setId(filePart.getId());
        }
    }

    @Override
    public void deleteFilePartByUploadId(String uploadId) {
        remove(new LambdaQueryWrapperX<SysFilePart>().eq(SysFilePart::getUploadId, uploadId));
    }

}

package cn.com.sipsg.module.system.module.upms.service;

import cn.com.sipsg.common.mybatis.core.service.BaseServiceX;
import cn.com.sipsg.common.pojo.vo.CommonImportVO;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.module.system.module.upms.entity.SysDept;
import cn.com.sipsg.module.system.module.upms.entity.SysDeptPost;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysDeptPostQueryBO;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysDeptPostUpdateBO;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysDeptQueryBO;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysDeptSaveBO;
import cn.com.sipsg.module.system.module.upms.pojo.dto.SysDeptPostDTO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysDeptPostVO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysDeptVO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysPostVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 部门服务类
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public interface SysDeptService extends BaseServiceX<SysDept> {

    /**
     * 查询部门分页列表
     *
     * @param bo 参数
     * @return 部门分页列表
     */
    CommonPageVO<SysDeptVO> page(SysDeptQueryBO bo);

    /**
     * 新增部门
     *
     * @param bo 参数
     * @return 部门ID
     */
    String save(SysDeptSaveBO bo);

    /**
     * 编辑部门
     *
     * @param bo 参数
     */
    void update(SysDeptSaveBO bo);

    /**
     * 删除部门
     *
     * @param deptId 部门ID
     */
    void delete(String deptId);

    /**
     * 根据部门ID查询详情
     *
     * @param deptId 部门ID
     * @return 部门详情
     */
    SysDeptVO detail(String deptId);

    /**
     * 查询部门岗位分页列表
     *
     * @param bo 参数
     * @return 岗位分页列表
     */
    CommonPageVO<SysDeptPostVO> getDeptPostPage(SysDeptPostQueryBO bo);

    /**
     * 查询不在指定部门下的岗位分页列表
     *
     * @param bo 参数
     * @return 岗位分页列表
     */
    CommonPageVO<SysPostVO> getNotInDeptPostPage(SysDeptPostQueryBO bo);

    /**
     * 保存部门岗位
     *
     * @param deptId   部门ID
     * @param postList 岗位列表
     */
    void saveDeptPost(String deptId, List<SysDeptPostDTO> postList);

    /**
     * 更新部门岗位
     *
     * @param bo 参数
     */
    void updateDeptPost(SysDeptPostUpdateBO bo);

    /**
     * 删除部门岗位
     *
     * @param deptPostId 部门岗位ID
     */
    void deleteDeptPost(String deptPostId);

    /**
     * 查询指定部门的所有子部门
     *
     * @param deptId 部门ID
     * @return 子部门列表
     */
    List<SysDept> getChildDeptList(String deptId);

    /**
     * 查询指定部门的所有子部门
     *
     * @param deptIds 部门ID列表
     * @return 子部门列表
     */
    List<SysDept> getChildDeptList(Collection<String> deptIds);

    /**
     * 导出部门
     */
    void export();

    /**
     * 导入部门
     *
     * @param file 文件
     * @return 导入结果
     */
    CommonImportVO importDept(MultipartFile file);

    /**
     * 导入部门
     *
     * @param dataList 数据列表
     * @return 导入结果
     */
    CommonImportVO importDept(List<Map<String, Object>> dataList);

    /**
     * 查询指定部门的领导岗位列表
     *
     * @param deptId 部门ID
     * @return 领导岗位列表
     */
    List<SysDeptPost> getLeaderDeptPostList(String deptId);

    /**
     * 根据用户ID查询部门列表
     *
     * @param userId 用户ID
     * @return 部门列表
     */
    List<SysDept> getDeptListByUserId(String userId);

}

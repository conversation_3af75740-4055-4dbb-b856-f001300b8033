package cn.com.sipsg.module.system.module.upms.pojo.bo;

import cn.com.sipsg.common.constant.CommonConstants;
import cn.com.sipsg.common.validation.group.UpdateGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 部门保存 BO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "部门保存 BO")
public class SysDeptSaveBO {

    /**
     * 部门ID
     */
    @Schema(description = "部门ID", example = "1111")
    @NotBlank(message = "部门ID不能为空", groups = {UpdateGroup.class})
    private String deptId;

    /**
     * 父部门ID
     */
    @Schema(description = "父部门ID", defaultValue = CommonConstants.PARENT_ID_ROOT, example = "1111")
    private String parentId;

    /**
     * 部门名称
     */
    @Schema(description = "部门名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "研发部")
    @NotBlank(message = "部门名称不能为空")
    @Size(max = 30, message = "部门名称长度不能超过30个字符")
    private String deptName;

    /**
     * 排序值
     */
    @Schema(description = "排序值", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "排序值不能为空")
    private Integer sort;

}

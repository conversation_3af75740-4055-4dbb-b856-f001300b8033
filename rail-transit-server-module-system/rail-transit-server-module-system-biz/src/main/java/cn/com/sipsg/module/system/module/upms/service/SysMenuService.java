package cn.com.sipsg.module.system.module.upms.service;

import cn.com.sipsg.common.mybatis.core.service.BaseServiceX;
import cn.com.sipsg.module.system.module.upms.entity.SysMenu;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysMenuSaveBO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysMenuVO;

import java.util.List;

/**
 * <p>
 * 菜单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public interface SysMenuService extends BaseServiceX<SysMenu> {

    /**
     * 新增菜单
     *
     * @param bo 参数
     * @return 菜单ID
     */
    String save(SysMenuSaveBO bo);

    /**
     * 编辑菜单
     *
     * @param bo 参数
     */
    void update(SysMenuSaveBO bo);

    /**
     * 删除菜单
     *
     * @param menuId 菜单ID
     */
    void delete(String menuId);

    /**
     * 查询菜单详情
     *
     * @param menuId 菜单ID
     * @return 菜单详情
     */
    SysMenuVO detail(String menuId);

    /**
     * 查询指定用户拥有的菜单列表
     *
     * @param userId  用户ID
     * @param appCode 应用标识
     * @return 菜单列表
     */
    List<SysMenu> getMenuList(String userId, String appCode);

}
package cn.com.sipsg.module.system.module.upms.entity;

import cn.com.sipsg.common.mybatis.core.dataobject.BaseDO;
import cn.com.sipsg.module.system.module.upms.enums.MenuBindTypeEnum;
import cn.com.sipsg.module.system.module.upms.enums.MenuTypeEnum;
import com.baomidou.mybatisplus.annotation.OrderBy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.github.yulichang.annotation.FieldMapping;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;

import java.util.List;

/**
 * <p>
 * 菜单表
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@FieldNameConstants
@TableName(value = "medox_sys_menu", autoResultMap = true)
public class SysMenu extends BaseDO {

    /**
     * 菜单ID
     */
    @TableId
    private String menuId;

    /**
     * 父菜单ID
     */
    private String parentId;

    /**
     * 菜单名称
     */
    private String menuName;

    /**
     * 菜单类型
     */
    private MenuTypeEnum menuType;

    /**
     * 权限字
     */
    private String permCode;

    /**
     * 绑定类型
     */
    private MenuBindTypeEnum bindType;

    /**
     * 路由地址
     */
    private String routerUrl;

    /**
     * 路由参数
     */
    private String routerParam;

    /**
     * 动态表单ID
     */
    private String onlineFormId;

    /**
     * 动态表单菜单的权限控制类型
     */
    private Integer onlineMenuPermType;

    /**
     * 动态表单的流程ID
     */
    private String onlineFlowEntryId;

    /**
     * 排序值
     */
    @OrderBy(asc = true, sort = Short.MIN_VALUE)
    private Integer sort;

    /**
     * 图标
     */
    private String icon;

    /**
     * 组件路径
     */
    private String componentPath;

    /**
     * 是否显示
     */
    private Boolean showable;

    /**
     * 是否缓存
     */
    private Boolean cacheable;

    /**
     * 路由跳转方式
     */
    private Integer routingMethod;

    /**
     * 链接地址
     */
    private String targetUrl;

    /**
     * 应用标识
     */
    private String appCode;

    /**
     * 权限ID列表
     */
    @TableField(exist = false)
    @FieldMapping(tag = SysMenuPerm.class, thisField = Fields.menuId, joinField = SysMenuPerm.Fields.menuId, select = SysMenuPerm.Fields.permId)
    private List<String> permIds;

}

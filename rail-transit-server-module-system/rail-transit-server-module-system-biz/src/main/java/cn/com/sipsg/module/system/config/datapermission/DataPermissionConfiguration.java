package cn.com.sipsg.module.system.config.datapermission;

import cn.com.sipsg.common.datapermission.core.rule.dept.DeptDataPermissionRuleCustomizer;
import cn.com.sipsg.module.system.module.upms.entity.SysDept;
import cn.com.sipsg.module.system.module.upms.entity.SysUser;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 系统模块的数据权限配置
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Configuration(proxyBeanMethods = false)
public class DataPermissionConfiguration {

    @Bean
    public DeptDataPermissionRuleCustomizer sysDeptDataPermissionRuleCustomizer() {
        return rule -> {
            // dept
            rule.addDeptColumn(SysDept.class);
            rule.addDeptColumn(SysUser.class);
            // user
            rule.addUserColumn(SysUser.class);
        };
    }

}

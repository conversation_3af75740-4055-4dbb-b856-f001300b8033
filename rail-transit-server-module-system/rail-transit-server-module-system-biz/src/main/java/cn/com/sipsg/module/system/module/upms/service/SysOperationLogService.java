package cn.com.sipsg.module.system.module.upms.service;

import cn.com.sipsg.common.mybatis.core.service.BaseServiceX;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.module.system.api.upms.dto.SysOperationLogSaveReqDTO;
import cn.com.sipsg.module.system.module.upms.entity.SysOperationLog;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysOperationLogQueryBO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysOperationLogVO;

/**
 * <p>
 * 操作日志表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public interface SysOperationLogService extends BaseServiceX<SysOperationLog> {

    /**
     * 查询操作日志分页列表
     *
     * @param bo 参数
     * @return 操作日志分页列表
     */
    CommonPageVO<SysOperationLogVO> page(SysOperationLogQueryBO bo);

    /**
     * 异步保存操作日志
     *
     * @param reqDTO 请求 DTO
     */
    void saveAsync(SysOperationLogSaveReqDTO reqDTO);

}
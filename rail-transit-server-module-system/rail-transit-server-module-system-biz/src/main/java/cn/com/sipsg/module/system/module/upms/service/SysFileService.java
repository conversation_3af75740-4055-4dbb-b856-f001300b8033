package cn.com.sipsg.module.system.module.upms.service;

import cn.com.sipsg.common.mybatis.core.service.BaseServiceX;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.module.system.module.upms.entity.SysFile;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysFileQueryBO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysFilePartVO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysFileVO;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.List;

/**
 * <p>
 * 文件表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public interface SysFileService extends BaseServiceX<SysFile> {

    /**
     * 查询文件分页列表
     *
     * @param bo 参数
     * @return 文件分页列表
     */
    CommonPageVO<SysFileVO> page(SysFileQueryBO bo);

    /**
     * 上传文件
     *
     * @param file      文件
     * @param groupCode 分组编码
     * @return 文件
     */
    SysFile upload(MultipartFile file, String groupCode);

    /**
     * 上传文件
     *
     * @param base64    base64字符传
     * @param fileName  文件名
     * @param groupCode 分组编码
     * @return 文件
     */
    SysFile uploadBase64(String base64, String fileName, String groupCode);

    /**
     * 上传文件
     *
     * @param inputStream      文件流
     * @param originalFileName 原始文件名
     * @param groupCode        分组编码
     * @return 文件
     */
    SysFile upload(InputStream inputStream, String originalFileName, String groupCode);

    /**
     * 删除文件
     *
     * @param fileId 文件ID
     */
    void deleteById(String fileId);

    /**
     * 批量删除文件
     *
     * @param fileIds 文件ID
     */
    void deleteBatch(List<String> fileIds);

    /**
     * 下载文件
     *
     * @param fileId 文件ID
     */
    void download(String fileId);

    /**
     * 根据文件哈希获取文件
     *
     * @param fileHash 文件哈希
     * @param groupCode 分组编码
     * @return 文件
     */
    SysFile getFileByHash(String fileHash, String groupCode);

    /**
     * 初始化分片上传
     *
     * @param fileName  文件名
     * @param fileHash  文件哈希
     * @param groupCode 分组编码
     * @return 上传ID
     */
    String initMultipartUpload(String fileName, String fileHash, String groupCode);

    /**
     * 上传分片
     *
     * @param uploadId   上传ID
     * @param partNumber 分片编号
     * @param file       文件
     * @return 分片信息
     */
    SysFilePartVO uploadPart(String uploadId, Integer partNumber, MultipartFile file);

    /**
     * 完成分片上传
     *
     * @param uploadId 上传ID
     * @return 文件
     */
    SysFile completeMultipartUpload(String uploadId);

    /**
     * 查询已上传的分片
     *
     * @param uploadId 上传ID
     * @return 已上传的分片
     */
    List<SysFilePartVO> queryUploadedParts(String uploadId);

    /**
     * 取消分片上传
     *
     * @param uploadId 上传ID
     */
    void abortMultipartUpload(String uploadId);

}
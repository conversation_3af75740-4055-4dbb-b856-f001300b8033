package cn.com.sipsg.module.system.module.upms.controller;

import cn.com.sipsg.common.mybatis.core.query.LambdaQueryWrapperX;
import cn.com.sipsg.common.operationlog.core.annotation.OperationLog;
import cn.com.sipsg.common.operationlog.core.enums.OperationTypeEnum;
import cn.com.sipsg.common.pojo.CommonResult;
import cn.com.sipsg.common.util.BeanUtils;
import cn.com.sipsg.common.validation.AddValidated;
import cn.com.sipsg.common.validation.UpdateValidated;
import cn.com.sipsg.module.system.module.upms.entity.SysMenu;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysMenuQueryBO;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysMenuSaveBO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysMenuSimpleVO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysMenuVO;
import cn.com.sipsg.module.system.module.upms.service.SysMenuService;
import cn.dev33.satoken.annotation.SaCheckPermission;
import com.fhs.core.trans.anno.IgnoreTrans;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 菜单控制器
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Tag(name = "菜单管理")
@RestController
@RequiredArgsConstructor
@RequestMapping("/system/upms/menu")
public class SysMenuController {

    private final SysMenuService menuService;

    /**
     * 查询菜单精简信息列表
     *
     * @return 菜单精简信息列表
     */
    @Operation(summary = "查询菜单精简信息列表", description = "主要用于前端的下拉选项")
    @Parameter(name = "appCode", description = "应用标识", required = true)
    @GetMapping("/simpleList")
    public CommonResult<List<SysMenuSimpleVO>> getSimpleList(@RequestParam String appCode) {
        return CommonResult.data(BeanUtils.copyToList(menuService.list(new LambdaQueryWrapperX<SysMenu>().eq(SysMenu::getAppCode, appCode)), SysMenuSimpleVO.class));
    }

    /**
     * 查询菜单列表
     *
     * @return 菜单列表
     */
    @Operation(summary = "查询菜单列表")
    @OperationLog(module = "菜单管理", value = "查询菜单列表", type = OperationTypeEnum.QUERY, saveResponse = false)
    @SaCheckPermission(value = "system:upms:menu:list")
    @PostMapping("/list")
    public CommonResult<List<SysMenuVO>> list(@RequestBody SysMenuQueryBO bo) {
        List<SysMenu> menuList = menuService.list(new LambdaQueryWrapperX<SysMenu>().eq(SysMenu::getAppCode, bo.getAppCode()));
        return CommonResult.data(BeanUtils.copyToList(menuList, SysMenuVO.class));
    }

    /**
     * 新增菜单
     *
     * @param bo 参数
     * @return 菜单ID
     */
    @Operation(summary = "新增菜单")
    @OperationLog(module = "菜单管理", value = "新增菜单", type = OperationTypeEnum.SAVE)
    @SaCheckPermission(value = "system:upms:menu:save")
    @PostMapping("/save")
    public CommonResult<String> save(@AddValidated @RequestBody SysMenuSaveBO bo) {
        return CommonResult.data(menuService.save(bo));
    }

    /**
     * 编辑菜单
     *
     * @param bo 参数
     */
    @Operation(summary = "编辑菜单")
    @OperationLog(module = "菜单管理", value = "编辑菜单", type = OperationTypeEnum.UPDATE)
    @SaCheckPermission(value = "system:upms:menu:update")
    @PostMapping("/update")
    public CommonResult<Void> update(@UpdateValidated @RequestBody SysMenuSaveBO bo) {
        menuService.update(bo);
        return CommonResult.success();
    }

    /**
     * 删除菜单
     *
     * @param menuId 菜单ID
     */
    @Operation(summary = "删除菜单")
    @Parameter(name = "menuId", required = true, description = "菜单ID")
    @OperationLog(module = "菜单管理", value = "删除菜单", type = OperationTypeEnum.DELETE)
    @SaCheckPermission(value = "system:upms:menu:delete")
    @PostMapping("/delete")
    public CommonResult<Void> delete(@RequestParam String menuId) {
        menuService.delete(menuId);
        return CommonResult.success();
    }

    /**
     * 查询菜单详情
     *
     * @param menuId 菜单ID
     * @return 菜单详情
     */
    @IgnoreTrans
    @Operation(summary = "查询菜单详情")
    @Parameter(name = "menuId", required = true, description = "菜单ID")
    @OperationLog(module = "菜单管理", value = "查询菜单详情", type = OperationTypeEnum.QUERY, saveResponse = false)
    @SaCheckPermission(value = "system:upms:menu:detail")
    @GetMapping("/detail")
    public CommonResult<SysMenuVO> detail(@RequestParam String menuId) {
        return CommonResult.data(menuService.detail(menuId));
    }

}

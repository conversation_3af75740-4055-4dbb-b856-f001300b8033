package cn.com.sipsg.module.system.module.uaa.pojo.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

/**
 * 刷新登录 BO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "刷新登录 BO")
public class RefreshLoginBO {

    /**
     * 应用标识
     */
    @Schema(description = "应用标识", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "应用标识不能为空")
    private String appCode;

    /**
     * 是否使原令牌失效
     */
    @Schema(description = "是否使原令牌失效")
    private Boolean invalidateOriginToken;

}

package cn.com.sipsg.module.system.module.upms.mapper;

import cn.com.sipsg.common.mybatis.core.mapper.BaseMapperX;
import cn.com.sipsg.module.system.module.upms.entity.SysDeptPost;
import com.baomidou.dynamic.datasource.annotation.Master;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 部门岗位关系表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Master
@Mapper
public interface SysDeptPostMapper extends BaseMapperX<SysDeptPost> {

    default List<SysDeptPost> selectByDeptId(String deptId) {
        return selectList(SysDeptPost::getDeptId, deptId);
    }

    default void deleteByPostId(String postId) {
        delete(SysDeptPost::getPostId, postId);
    }

}

package cn.com.sipsg.module.system.module.upms.mapper;

import cn.com.sipsg.common.mybatis.core.mapper.BaseMapperX;
import cn.com.sipsg.module.system.module.upms.entity.SysRoleMenu;
import com.baomidou.dynamic.datasource.annotation.Master;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 角色菜单关系表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Master
@Mapper
public interface SysRoleMenuMapper extends BaseMapperX<SysRoleMenu> {

    default void deleteByRoleId(String roleId) {
        delete(SysRoleMenu::getRoleId, roleId);
    }

    default void deleteByMenuId(String menuId) {
        delete(SysRoleMenu::getMenuId, menuId);
    }

    default List<SysRoleMenu> listByRoleId(String roleId) {
        return selectList(SysRoleMenu::getRoleId, roleId);
    }

}

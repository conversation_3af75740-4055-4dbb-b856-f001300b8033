package cn.com.sipsg.module.system.module.upms.pojo.vo;

import cn.com.sipsg.common.enums.CommonStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 字典项简单信息 VO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "字典项简单信息 VO")
public class SysDictItemSimpleVO {

    /**
     * 标签
     */
    @Schema(description = "标签")
    private String itemName;

    /**
     * 值
     */
    @Schema(description = "值")
    private String itemCode;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private CommonStatusEnum status;

}

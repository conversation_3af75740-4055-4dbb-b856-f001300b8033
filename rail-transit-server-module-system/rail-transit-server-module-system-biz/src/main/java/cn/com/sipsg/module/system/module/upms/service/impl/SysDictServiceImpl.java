package cn.com.sipsg.module.system.module.upms.service.impl;

import cn.com.sipsg.common.constant.PageConstants;
import cn.com.sipsg.common.enums.CommonStatusEnum;
import cn.com.sipsg.common.exception.BusinessException;
import cn.com.sipsg.common.exception.enums.ErrorCodeEnum;
import cn.com.sipsg.common.mybatis.core.query.LambdaQueryWrapperX;
import cn.com.sipsg.common.pojo.vo.CommonImportVO;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.util.AssertUtils;
import cn.com.sipsg.common.util.EnumUtils;
import cn.com.sipsg.common.web.core.util.ExcelUtils;
import cn.com.sipsg.module.system.module.upms.convert.SysDictConvert;
import cn.com.sipsg.module.system.module.upms.entity.SysDict;
import cn.com.sipsg.module.system.module.upms.entity.SysDictItem;
import cn.com.sipsg.module.system.module.upms.enums.DictTypeEnum;
import cn.com.sipsg.module.system.module.upms.mapper.SysDictItemMapper;
import cn.com.sipsg.module.system.module.upms.mapper.SysDictMapper;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysDictQueryBO;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysDictSaveBO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysDictVO;
import cn.com.sipsg.module.system.module.upms.service.SysDictItemService;
import cn.com.sipsg.module.system.module.upms.service.SysDictService;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.*;

/**
 * <p>
 * 字典表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Service
@RequiredArgsConstructor
public class SysDictServiceImpl extends ServiceImpl<SysDictMapper, SysDict> implements SysDictService {

    private final SysDictItemMapper dictItemMapper;

    private final SysDictItemService dictItemService;

    private final HttpServletResponse response;

    @Override
    public CommonPageVO<SysDictVO> page(SysDictQueryBO bo) {
        LambdaQueryWrapperX<SysDict> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper
                .likeIfPresent(SysDict::getDictName, bo.getDictName())
                .likeIfPresent(SysDict::getDictCode, bo.getDictCode())
                .eqIfPresent(SysDict::getStatus, bo.getStatus())
                .eqIfPresent(SysDict::getDictType, bo.getDictType());
        return this.page(bo, queryWrapper).convert(SysDictConvert.INSTANCE::convert);
    }

    @Override
    public String save(SysDictSaveBO bo) {
        bo.setDictId(null);
        // 校验字典
        saveOrUpdate(null, bo.getDictCode());
        // 保存字典
        SysDict dict = SysDictConvert.INSTANCE.convert(bo);
        this.save(dict);
        return dict.getDictId();
    }

    @Override
    @Transactional
    public void update(SysDictSaveBO bo) {
        // 校验字典
        saveOrUpdate(bo.getDictId(), bo.getDictCode());
        // 判断是否更新了字典编码
        SysDict dict = this.getById(bo.getDictId());
        if (!dict.getDictCode().equals(bo.getDictCode())) {
            // 更新字典项中字典编码
            dictItemService.lambdaUpdate()
                    .set(SysDictItem::getDictCode, bo.getDictCode())
                    .eq(SysDictItem::getDictCode, dict.getDictCode())
                    .update();
        }
        // 更新字典
        dict = SysDictConvert.INSTANCE.convert(bo);
        this.updateById(dict);
    }

    @Override
    @Transactional
    public void delete(String dictId) {
        SysDict dict = this.getById(dictId);
        if (Objects.nonNull(dict)) {
            AssertUtils.isTrue(dict.getDictType() == DictTypeEnum.SYSTEM, "系统字典不能删除");
            // 删除字典
            this.removeById(dictId);
            // 删除字典项
            dictItemMapper.delete(SysDictItem::getDictCode, dict.getDictCode());
        }
    }

    @Override
    public SysDictVO detail(String dictId) {
        // 校验是否存在
        SysDict dict = checkExist(dictId);
        return SysDictConvert.INSTANCE.convert(dict);
    }

    @Override
    public void updateStatus(String dictId, Integer status) {
        // 校验字典存在
        checkExist(dictId);
        // 更新字典状态
        SysDict updateDict = new SysDict();
        updateDict.setDictId(dictId);
        updateDict.setStatus(SysDictConvert.INSTANCE.status2Enum(status));
        this.updateById(updateDict);
    }

    @Override
    public List<SysDictItem> getItemListByDictCode(String dictCode) {
        return dictItemMapper.selectList(SysDictItem::getDictCode, dictCode, SysDictItem::getStatus, CommonStatusEnum.ENABLE);
    }

    @Override
    public List<SysDictItem> getItemListByDictCode(String dictCode, boolean includeDisable) {
        LambdaQueryWrapperX<SysDictItem> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eq(SysDictItem::getDictCode, dictCode);
        queryWrapper.eq(!includeDisable, SysDictItem::getStatus, CommonStatusEnum.ENABLE);
        return dictItemMapper.selectList(queryWrapper);
    }

    @Override
    public void export(SysDictQueryBO bo) {
        List<Map<String, Object>> dictRows = new ArrayList<>();
        List<Map<String, Object>> dictItemRows = new ArrayList<>();
        bo.setSize(PageConstants.SIZE_NONE);
        for (SysDictVO dict : this.page(bo).getRecords()) {
            Map<String, Object> dictRow = new LinkedHashMap<>();
            dictRows.add(dictRow);
            dictRow.put("字典名称", dict.getDictName());
            dictRow.put("字典编码", dict.getDictCode());
            dictRow.put("字典类型", dict.getDictType().getDesc());
            dictRow.put("状态", dict.getStatus().getDesc());
            dictRow.put("备注", dict.getRemark());
            List<SysDictItem> dictItemList = dictItemMapper.selectList(SysDictItem::getDictCode, dict.getDictCode());
            for (SysDictItem dictItem : dictItemList) {
                Map<String, Object> dictItemRow = new LinkedHashMap<>();
                dictItemRows.add(dictItemRow);
                dictItemRow.put("字典项名称", dictItem.getItemName());
                dictItemRow.put("字典项编码", dictItem.getItemCode());
                dictItemRow.put("状态", dictItem.getStatus().getDesc());
                dictItemRow.put("显示顺序", dictItem.getSort());
                dictItemRow.put("所属字典编码", dictItem.getDictCode());
                dictItemRow.put("备注", dictItem.getRemark());
            }
        }
        ExcelWriter writer = ExcelUtil.getWriter();
        writer.renameSheet("字典");
        writer.write(dictRows, true);
        writer.setSheet("字典项");
        writer.write(dictItemRows, true);
        ExcelUtils.export(response, writer, "字典导出");
    }

    @Override
    @Transactional
    public Map<String, CommonImportVO> importDict(MultipartFile file) {
        InputStream inputStream = null;
        ExcelReader reader = null;
        List<Map<String, Object>> dictList = null;
        List<Map<String, Object>> dictItemList = null;
        try {
            inputStream = file.getInputStream();
            reader = ExcelUtil.getReader(inputStream);
            reader.setSheet("字典");
            dictList = reader.readAll();
            reader.setSheet("字典项");
            dictItemList = reader.readAll();
        } catch (Exception e) {
            log.error("导入字典异常", e);
        } finally {
            IoUtil.close(inputStream);
            IoUtil.close(reader);
        }
        if (CollUtil.isEmpty(dictList) && CollUtil.isEmpty(dictItemList)) {
            throw new BusinessException(ErrorCodeEnum.IMPORT_FILE_ERROR);
        }
        return MapUtil.builder(new HashMap<String, CommonImportVO>())
                .put("dict", this.importDict(dictList))
                .put("dictItem", dictItemService.importDictItem(dictItemList))
                .build();
    }

    private CommonImportVO importDict(List<Map<String, Object>> dataList) {
        List<Map<String, Object>> errorRecords = new ArrayList<>();
        long successTotal = 0;
        long errorTotal = 0;
        for (Map<String, Object> dataMap : dataList) {
            String dictName = StrUtil.trim(MapUtil.getStr(dataMap, "字典名称"));
            String dictCode = StrUtil.trim(MapUtil.getStr(dataMap, "字典编码"));
            String dictTypeDesc = StrUtil.trim(MapUtil.getStr(dataMap, "字典类型"));
            String statusDesc = StrUtil.trim(MapUtil.getStr(dataMap, "状态"));
            String remark = StrUtil.trim(MapUtil.getStr(dataMap, "备注"));
            StringBuilder sb = new StringBuilder();
            if (StrUtil.isBlank(dictName)) {
                sb.append("字典名称不能为空;");
            }
            if (StrUtil.isBlank(dictCode)) {
                sb.append("字典编码不能为空;");
            } else {
                Long count = baseMapper.selectCount(SysDict::getDictCode, dictCode);
                if (count > 0) {
                    sb.append("字典编码已存在;");
                }
            }
            DictTypeEnum dictType = null;
            if (StrUtil.isBlank(dictTypeDesc)) {
                sb.append("字典类型不能为空;");
            } else {
                dictType = EnumUtils.getEnumByDesc(DictTypeEnum.class, dictTypeDesc);
                if (ObjectUtil.isNull(dictType)) {
                    sb.append("字典类型不正确;");
                }
            }
            CommonStatusEnum status = null;
            if (StrUtil.isBlank(statusDesc)) {
                sb.append("状态不能为空;");
            } else {
                status = EnumUtils.getEnumByDesc(CommonStatusEnum.class, statusDesc);
                if (ObjectUtil.isNull(status)) {
                    sb.append("状态不正确;");
                }
            }
            if (sb.length() > 0) {
                errorTotal++;
                dataMap.put("错误提示", sb);
                errorRecords.add(dataMap);
                continue;
            }
            // 保存字典
            SysDict sysDict = new SysDict();
            sysDict.setDictName(dictName);
            sysDict.setDictCode(dictCode);
            sysDict.setStatus(status);
            sysDict.setDictType(dictType);
            sysDict.setRemark(remark);
            this.save(sysDict);
            successTotal++;
        }
        return CommonImportVO.builder().successTotal(successTotal).errorTotal(errorTotal).errorRecords(errorRecords).build();
    }

    private void saveOrUpdate(String dictId, String dictCode) {
        // 校验字典存在
        checkExist(dictId);
        // 校验字典编码唯一
        checkDictCodeUnique(dictId, dictCode);
    }

    private void checkDictCodeUnique(String dictId, String dictCode) {
        if (StrUtil.isBlank(dictCode)) {
            return;
        }
        long count = this.count(new LambdaQueryWrapperX<SysDict>().eq(SysDict::getDictCode, dictCode).neIfPresent(SysDict::getDictId, dictId));
        AssertUtils.isTrue(count > 0, String.format("字典编码【%s】已存在", dictCode));
    }

}
package cn.com.sipsg.module.system.module.upms.controller;

import cn.com.sipsg.common.mybatis.core.query.MPJLambdaWrapperX;
import cn.com.sipsg.common.pojo.CommonResult;
import cn.com.sipsg.common.security.core.util.SecurityUtils;
import cn.com.sipsg.common.validation.AddValidated;
import cn.com.sipsg.module.system.module.upms.entity.SysMenu;
import cn.com.sipsg.module.system.module.upms.entity.SysQuickMenu;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysQuickMenuSaveBO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysQuickMenuVO;
import cn.com.sipsg.module.system.module.upms.service.SysQuickMenuService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 快捷菜单管理控制器
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Tag(name = "快捷菜单管理")
@RestController
@RequiredArgsConstructor
@RequestMapping("/system/upms/quickMenu")
public class SysQuickMenuController {

    private final SysQuickMenuService quickMenuService;

    /**
     * 查询快捷菜单列表
     *
     * @return 快捷菜单列表
     */
    @Operation(summary = "查询快捷菜单列表")
    @GetMapping("/list")
    public CommonResult<List<SysQuickMenuVO>> list() {
        MPJLambdaWrapperX<SysQuickMenu> lambdaWrapper = new MPJLambdaWrapperX<>();
        lambdaWrapper
                .select(SysQuickMenu::getSort)
                .eq(SysQuickMenu::getUserId, SecurityUtils.getLoginUserId())
                .selectAll(SysMenu.class, SysMenu::getSort)
                .innerJoin(SysMenu.class, SysMenu::getMenuId, SysQuickMenu::getMenuId);
        return CommonResult.data(quickMenuService.selectJoinList(SysQuickMenuVO.class, lambdaWrapper));
    }

    /**
     * 新增快捷菜单
     *
     * @param bo 参数
     * @return 快捷菜单ID
     */
    @Operation(summary = "新增快捷菜单")
    @PostMapping("/save")
    public CommonResult<Void> save(@AddValidated @RequestBody SysQuickMenuSaveBO bo) {
        quickMenuService.save(bo);
        return CommonResult.success();
    }

    /**
     * 删除快捷菜单
     *
     * @param menuId 菜单ID
     */
    @Operation(summary = "删除快捷菜单")
    @Parameter(name = "menuId", description = "菜单ID", required = true, example = "1111")
    @PostMapping("/delete")
    public CommonResult<Void> delete(@RequestParam String menuId) {
        quickMenuService.delete(menuId);
        return CommonResult.success();
    }

    /**
     * 拖拽排序
     *
     * @param menuIds 菜单ID列表
     */
    @Operation(summary = "拖拽排序")
    @PostMapping("/dragSort")
    public CommonResult<Void> dragSort(@RequestBody List<String> menuIds) {
        quickMenuService.dragSort(menuIds);
        return CommonResult.success();
    }

}
package cn.com.sipsg.module.system.module.upms.controller;

import cn.com.sipsg.common.constant.PageConstants;
import cn.com.sipsg.common.mybatis.core.query.LambdaQueryWrapperX;
import cn.com.sipsg.common.operationlog.core.annotation.OperationLog;
import cn.com.sipsg.common.operationlog.core.enums.OperationTypeEnum;
import cn.com.sipsg.common.pojo.CommonResult;
import cn.com.sipsg.common.pojo.bo.DeleteBO;
import cn.com.sipsg.common.pojo.bo.UpdateStatusBO;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.util.BeanUtils;
import cn.com.sipsg.common.validation.AddValidated;
import cn.com.sipsg.common.validation.UpdateValidated;
import cn.com.sipsg.module.system.module.upms.entity.SysApp;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysAppQueryBO;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysAppSaveBO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysAppSimpleVO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysAppVO;
import cn.com.sipsg.module.system.module.upms.service.SysAppService;
import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaIgnore;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

/**
 * 应用管理控制器
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Tag(name = "应用管理")
@RestController
@RequiredArgsConstructor
@RequestMapping("/system/upms/app")
public class SysAppController {

    private final SysAppService appService;

    /**
     * 查询应用分页列表
     *
     * @param bo 参数
     * @return 应用分页列表
     */
    @Operation(summary = "查询应用分页列表")
    @OperationLog(module = "应用管理", value = "查询应用分页列表", type = OperationTypeEnum.QUERY, saveResponse = false)
    @SaCheckPermission(value = "system:upms:app:list")
    @PostMapping("/page")
    public CommonResult<CommonPageVO<SysAppVO>> page(@RequestBody SysAppQueryBO bo) {
        return CommonResult.data(appService.page(bo));
    }

    /**
     * 查询应用列表
     *
     * @param bo 参数
     * @return 应用列表
     */
    @Operation(summary = "查询应用列表")
    @OperationLog(module = "应用管理", value = "查询应用列表", type = OperationTypeEnum.QUERY, saveResponse = false)
    @SaCheckPermission(value = "system:upms:app:list")
    @PostMapping("/list")
    public CommonResult<List<SysAppVO>> list(@RequestBody SysAppQueryBO bo) {
        bo.setSize(PageConstants.SIZE_NONE);
        return CommonResult.data(appService.page(bo).getRecords());
    }

    /**
     * 查询应用精简信息列表
     *
     * @return 应用精简信息列表
     */
    @Operation(summary = "查询应用精简信息列表")
    @GetMapping("/simpleList")
    public CommonResult<List<SysAppSimpleVO>> getSimpleList() {
        return CommonResult.data(BeanUtils.copyToList(appService.list(), SysAppSimpleVO.class));
    }

    /**
     * 新增应用
     *
     * @param bo 参数
     * @return 应用ID
     */
    @Operation(summary = "新增应用")
    @OperationLog(module = "应用管理", value = "新增应用", type = OperationTypeEnum.SAVE)
    @SaCheckPermission(value = "system:upms:app:save")
    @PostMapping("/save")
    public CommonResult<String> save(@AddValidated @RequestBody SysAppSaveBO bo) {
        return CommonResult.data(appService.save(bo));
    }

    /**
     * 编辑应用
     *
     * @param bo 参数
     */
    @Operation(summary = "编辑应用")
    @OperationLog(module = "应用管理", value = "编辑应用", type = OperationTypeEnum.UPDATE)
    @SaCheckPermission(value = "system:upms:app:update")
    @PostMapping("/update")
    public CommonResult<Void> update(@UpdateValidated @RequestBody SysAppSaveBO bo) {
        appService.update(bo);
        return CommonResult.success();
    }

    /**
     * 编辑应用状态
     *
     * @param bo 参数
     */
    @Operation(summary = "编辑应用状态")
    @OperationLog(module = "应用管理", value = "编辑应用状态", type = OperationTypeEnum.UPDATE)
    @SaCheckPermission(value = "system:upms:app:update")
    @PostMapping("/updateStatus")
    public CommonResult<Void> updateStatus(@Validated @RequestBody UpdateStatusBO bo) {
        appService.updateStatus(bo.getId(), bo.getStatus());
        return CommonResult.success();
    }

    /**
     * 查询应用详情
     *
     * @param appId 应用ID
     */
    @Operation(summary = "查询应用详情")
    @Parameter(name = "appId", description = "应用ID", required = true, example = "1111")
    @OperationLog(module = "应用管理", value = "查询应用详情", type = OperationTypeEnum.QUERY, saveResponse = false)
    @SaCheckPermission(value = "system:upms:app:detail")
    @GetMapping("/detail")
    public CommonResult<SysAppVO> detail(@RequestParam String appId) {
        return CommonResult.data(appService.detail(appId));
    }

    /**
     * 根据应用标识查询应用信息
     *
     * @param appCode 应用标识
     */
    @Operation(summary = "根据应用标识查询应用信息")
    @Parameter(name = "appCode", description = "应用标识", required = true, example = "1111")
    @SaIgnore
    @GetMapping("/getByAppCode")
    public CommonResult<SysAppVO> getByAppCode(@RequestParam String appCode) {
        Optional<SysApp> oneOpt = appService.getOneOpt(new LambdaQueryWrapperX<SysApp>().eq(SysApp::getAppCode, appCode), false);
        return CommonResult.data(oneOpt.map(app -> BeanUtils.copyProperties(app, SysAppVO.class, SysApp.Fields.clientSecret)).orElse(null));
    }

    /**
     * 删除应用
     *
     * @param appId 应用ID
     */
    @Operation(summary = "删除应用")
    @Parameter(name = "appId", description = "应用ID", required = true, example = "1111")
    @OperationLog(module = "应用管理", value = "删除应用", type = OperationTypeEnum.DELETE)
    @SaCheckPermission(value = "system:upms:app:delete")
    @PostMapping("/delete")
    public CommonResult<Void> delete(@RequestParam String appId) {
        appService.delete(appId);
        return CommonResult.success();
    }

    /**
     * 批量删除应用
     *
     * @param bo 参数
     */
    @Operation(summary = "批量删除应用")
    @OperationLog(module = "应用管理", value = "批量删除应用", type = OperationTypeEnum.DELETE)
    @SaCheckPermission(value = "system:upms:app:delete")
    @PostMapping("/deleteBatch")
    public CommonResult<Void> deleteBatch(@Validated @RequestBody DeleteBO bo) {
        appService.deleteBatch(bo);
        return CommonResult.success();
    }

}
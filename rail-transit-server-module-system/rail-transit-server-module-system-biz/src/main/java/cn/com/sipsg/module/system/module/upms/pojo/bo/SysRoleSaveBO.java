package cn.com.sipsg.module.system.module.upms.pojo.bo;

import cn.com.sipsg.common.validation.group.UpdateGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.util.Set;

/**
 * 角色保存 BO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "角色保存 BO")
public class SysRoleSaveBO {

    /**
     * 角色ID
     */
    @Schema(description = "角色ID", example = "1111")
    @NotBlank(groups = {UpdateGroup.class}, message = "角色ID不能为空")
    private String roleId;

    /**
     * 角色名称
     */
    @Schema(description = "角色名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "操作人员")
    @NotBlank(message = "角色名称不能为空")
    private String roleName;

    /**
     * 角色编码
     */
    @Schema(description = "角色编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "operator")
    @NotBlank(message = "角色编码不能为空")
    private String roleCode;

    /**
     * 菜单ID列表
     */
    @Schema(description = "菜单ID列表", example = "[\"1111\"]")
    private Set<String> menuIds;

}

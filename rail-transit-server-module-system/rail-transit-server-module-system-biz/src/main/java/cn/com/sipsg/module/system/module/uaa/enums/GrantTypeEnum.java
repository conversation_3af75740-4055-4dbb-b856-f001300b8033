package cn.com.sipsg.module.system.module.uaa.enums;

import cn.com.sipsg.common.enums.BaseStrEnum;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 授权类型枚举
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@RequiredArgsConstructor
public enum GrantTypeEnum implements BaseStrEnum {

    PASSWORD("password", "账号密码登录"),
    
        CLIENT_PASSWORD("client_password", "客户端账号密码登录"),

    ;

    private final String code;

    private final String desc;

}

package cn.com.sipsg.module.system.module.upms.service;

import cn.com.sipsg.common.mybatis.core.service.BaseServiceX;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.module.system.module.upms.entity.SysRole;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysRoleDataScopeUpdateBO;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysRoleQueryBO;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysRoleSaveBO;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysRoleUserQueryBO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysRoleVO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysUserSimpleVO;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 角色表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public interface SysRoleService extends BaseServiceX<SysRole> {

    /**
     * 查询角色分页列表
     *
     * @param bo 参数
     * @return 角色分页列表
     */
    CommonPageVO<SysRoleVO> page(SysRoleQueryBO bo);

    /**
     * 新增角色
     *
     * @param bo 参数
     * @return 角色ID
     */
    String save(SysRoleSaveBO bo);

    /**
     * 编辑角色
     *
     * @param bo 参数
     */
    void update(SysRoleSaveBO bo);

    /**
     * 删除角色
     *
     * @param roleId 角色ID
     */
    void delete(String roleId);

    /**
     * 查询角色详情
     *
     * @param roleId 角色ID
     * @return 角色详情
     */
    SysRoleVO detail(String roleId);

    /**
     * 查询角色用户分页列表
     *
     * @param bo 参数
     * @return 用户分页列表
     */
    CommonPageVO<SysUserSimpleVO> getRoleUserPage(SysRoleUserQueryBO bo);

    /**
     * 查询不在指定角色用户分页列表
     *
     * @param bo 参数
     * @return 用户分页列表
     */
    CommonPageVO<SysUserSimpleVO> getNotInRoleUserPage(SysRoleUserQueryBO bo);

    /**
     * 新增角色用户关系
     *
     * @param roleId  角色ID
     * @param userIds 用户ID列表
     */
    void saveRoleUser(String roleId, Set<String> userIds);

    /**
     * 删除角色用户关系
     *
     * @param roleId 角色ID
     * @param userId 用户ID
     */
    void deleteRoleUser(String roleId, String userId);

    /**
     * 查询指定用户拥有的角色列表
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    List<SysRole> getRoleListByUserId(String userId);

    /**
     * 编辑角色数据权限
     *
     * @param bo 参数
     */
    void updateRoleDataScope(SysRoleDataScopeUpdateBO bo);

}
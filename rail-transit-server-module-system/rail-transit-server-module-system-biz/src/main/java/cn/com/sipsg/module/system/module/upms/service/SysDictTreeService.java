package cn.com.sipsg.module.system.module.upms.service;

import cn.com.sipsg.common.mybatis.core.service.BaseServiceX;
import cn.com.sipsg.common.pojo.vo.CommonImportVO;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.module.system.api.upms.dto.SysDictTreeRespDTO;
import cn.com.sipsg.module.system.module.upms.entity.SysDictTree;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysDictTreeBatchUpdateBO;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysDictTreeDragUpdateBO;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysDictTreeQueryBO;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysDictTreeSaveBO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysDictTreeVO;
import cn.hutool.core.lang.tree.Tree;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 字典树管理服务接口
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public interface SysDictTreeService extends BaseServiceX<SysDictTree> {

    /**
     * 查询字典树管理分页列表
     *
     * @param bo 参数
     * @return 字典树管理分页列表
     */
    CommonPageVO<SysDictTreeVO> page(SysDictTreeQueryBO bo);

    /**
     * 新增字典树管理
     *
     * @param bo 参数
     * @return 字典树管理ID
     */
    String save(SysDictTreeSaveBO bo);

    /**
     * 编辑字典树管理
     *
     * @param bo 参数
     */
    void update(SysDictTreeSaveBO bo);

    /**
     * 根据id查询字典树管理详情
     *
     * @param id 主键
     * @return 字典树管理详情
     */
    SysDictTreeVO detail(String id);

    /**
     * 删除字典树管理
     *
     * @param id 主键
     */
    void delete(String id);

    /**
     * 查询字典树
     *
     * @return 字典树
     */
    List<SysDictTreeVO> listTree();

    /**
     * 根据字典树id查询字典树
     *
     * @param dictTreeId 字典树id
     * @return 字典树
     */
    List<Tree<String>> listTreeByDictTreeId(String dictTreeId);

    /**
     * 根据字典树code查询字典树
     *
     * @param dictTreeCode 字典树code
     * @return 字典树
     */
    List<Tree<String>> listTreeByDictTreeCode(String dictTreeCode);

    /**
     * 根据字典树code查询字典树
     *
     * @param dictTreeCode 字典树code
     * @return 字典树
     */
    List<SysDictTreeRespDTO> getDictTreeByDictTreeCode(String dictTreeCode);

    /**
     * 字典树拖拽更新
     *
     * @param bo 字典树拖拽更新 BO
     * @return 成功或失败
     */
    Boolean dragUpdate(SysDictTreeDragUpdateBO bo);

    /**
     * 导出字典树
     */
    void export();

    /**
     * 导入字典树
     *
     * @param file 文件
     * @return 错误信息
     */
    CommonImportVO importInfo(MultipartFile file);

    /**
     * 字典树批量更新
     *
     * @param bo 字典树批量更新 BO
     * @return 成功或失败
     */
    Boolean updateBatch(SysDictTreeBatchUpdateBO bo);

    /**
     * 判断是否有子节点
     *
     * @param id 主键
     * @return 是否有子节点
     */
    Boolean hasDescendant(String id);
}
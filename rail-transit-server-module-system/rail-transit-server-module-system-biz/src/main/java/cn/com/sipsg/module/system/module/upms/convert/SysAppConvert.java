package cn.com.sipsg.module.system.module.upms.convert;

import cn.com.sipsg.common.enums.CommonStatusEnum;
import cn.com.sipsg.common.util.EnumUtils;
import cn.com.sipsg.module.system.module.upms.entity.SysApp;
import cn.com.sipsg.module.system.module.upms.enums.AppTypeEnum;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysAppSaveBO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 应用转换类
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Mapper
public interface SysAppConvert {

    SysAppConvert INSTANCE = Mappers.getMapper(SysAppConvert.class);

    SysApp convert(SysAppSaveBO bo);

    default CommonStatusEnum status2Enum(Integer status) {
        return EnumUtils.getEnumByValue(CommonStatusEnum.class, status);
    }

    default AppTypeEnum appType2Enum(String appType) {
        return EnumUtils.getEnumByValue(AppTypeEnum.class, appType);
    }

}

package cn.com.sipsg.module.system.module.upms.pojo.bo;

import cn.com.sipsg.common.pojo.bo.SortablePageBO;
import cn.hutool.core.date.DatePattern;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

/**
 * 操作日志查询 BO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "操作日志查询 BO")
public class SysOperationLogQueryBO extends SortablePageBO {

    /**
     * 操作用户名
     */
    @Schema(description = "操作用户名")
    private String username;

    /**
     * 模块名
     */
    @Schema(description = "模块名")
    private String moduleName;

    /**
     * 操作类型
     */
    @Schema(description = "操作类型")
    private Integer operationType;

    /**
     * 是否成功
     */
    @Schema(description = "是否成功")
    private Boolean success;

    /**
     * 最小耗时（毫秒）
     */
    @Schema(description = "耗时（毫秒）")
    private Long durationMin;

    /**
     * 最大耗时（毫秒）
     */
    @Schema(description = "耗时（毫秒）")
    private Long durationMax;

    /**
     * 操作日期开始
     */
    @Schema(description = "操作日期开始")
    @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    private LocalDate operationDateStart;

    /**
     * 操作日期结束
     */
    @Schema(description = "操作日期结束")
    @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    private LocalDate operationDateEnd;

    /**
     * 应用标识
     */
    @Schema(description = "应用标识")
    private String appCode;

}

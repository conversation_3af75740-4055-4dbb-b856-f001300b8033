package cn.com.sipsg.module.system.module.upms.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 字典简单信息 VO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "字典简单信息 VO")
public class SysDictSimpleVO {

    /**
     * 字典 ID
     */
    @Schema(description = "字典 ID")
    private String dictId;

    /**
     * 字典名称
     */
    @Schema(description = "字典名称")
    private String dictName;

    /**
     * 字典编码
     */
    @Schema(description = "字典编码")
    private String dictCode;

}

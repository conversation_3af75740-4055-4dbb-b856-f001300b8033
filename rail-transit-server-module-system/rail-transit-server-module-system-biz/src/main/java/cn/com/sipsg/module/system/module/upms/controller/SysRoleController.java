package cn.com.sipsg.module.system.module.upms.controller;

import cn.com.sipsg.common.operationlog.core.annotation.OperationLog;
import cn.com.sipsg.common.operationlog.core.enums.OperationTypeEnum;
import cn.com.sipsg.common.pojo.CommonResult;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.util.BeanUtils;
import cn.com.sipsg.common.validation.AddValidated;
import cn.com.sipsg.common.validation.UpdateValidated;
import cn.com.sipsg.module.system.module.upms.pojo.bo.*;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysRoleSimpleVO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysRoleVO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysUserSimpleVO;
import cn.com.sipsg.module.system.module.upms.service.SysRoleService;
import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 角色控制器
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Tag(name = "角色管理")
@RestController
@RequiredArgsConstructor
@RequestMapping("/system/upms/role")
public class SysRoleController {

    private final SysRoleService roleService;

    /**
     * 查询角色分页列表
     *
     * @param bo 参数
     * @return 角色分页列表
     */
    @Operation(summary = "查询角色分页列表")
    @OperationLog(module = "角色管理", value = "查询角色分页列表", type = OperationTypeEnum.QUERY, saveResponse = false)
    @SaCheckPermission(value = "system:upms:role:list")
    @PostMapping("/page")
    public CommonResult<CommonPageVO<SysRoleVO>> page(@Validated @RequestBody SysRoleQueryBO bo) {
        CommonPageVO<SysRoleVO> page = roleService.page(bo);
        return CommonResult.data(page);
    }

    /**
     * 查询角色精简信息列表
     *
     * @return 角色精简信息列表
     */
    @Operation(summary = "查询角色精简信息列表", description = "主要用于前端的下拉选项")
    @GetMapping("/simpleList")
    public CommonResult<List<SysRoleSimpleVO>> getSimpleList() {
        return CommonResult.data(BeanUtils.copyToList(roleService.list(), SysRoleSimpleVO.class));
    }

    /**
     * 新增角色
     *
     * @param bo 参数
     * @return 角色ID
     */
    @Operation(summary = "新增角色")
    @OperationLog(module = "角色管理", value = "新增角色", type = OperationTypeEnum.SAVE)
    @SaCheckPermission(value = "system:upms:role:save")
    @PostMapping("/save")
    public CommonResult<String> save(@AddValidated @RequestBody SysRoleSaveBO bo) {
        String roleId = roleService.save(bo);
        return CommonResult.data(roleId);
    }

    /**
     * 编辑角色
     *
     * @param bo 参数
     */
    @Operation(summary = "更新角色")
    @OperationLog(module = "角色管理", value = "编辑角色", type = OperationTypeEnum.UPDATE)
    @SaCheckPermission(value = "system:upms:role:update")
    @PostMapping("/update")
    public CommonResult<Void> update(@UpdateValidated @RequestBody SysRoleSaveBO bo) {
        roleService.update(bo);
        return CommonResult.success();
    }

    /**
     * 删除角色
     *
     * @param roleId 角色ID
     */
    @Operation(summary = "删除角色")
    @Parameter(name = "roleId", description = "角色ID", required = true, example = "1111")
    @OperationLog(module = "角色管理", value = "删除角色", type = OperationTypeEnum.DELETE)
    @SaCheckPermission(value = "system:upms:role:delete")
    @PostMapping("/delete")
    public CommonResult<Void> delete(@RequestParam String roleId) {
        roleService.delete(roleId);
        return CommonResult.success();
    }

    /**
     * 查询角色详情
     *
     * @param roleId 角色ID
     * @return 角色详情
     */
    @Operation(summary = "查询角色详情")
    @Parameter(name = "roleId", description = "角色ID", required = true, example = "1111")
    @OperationLog(module = "角色管理", value = "查询角色详情", type = OperationTypeEnum.QUERY, saveResponse = false)
    @SaCheckPermission(value = "system:upms:role:detail")
    @GetMapping("/detail")
    public CommonResult<SysRoleVO> detail(@RequestParam String roleId) {
        SysRoleVO roleVO = roleService.detail(roleId);
        return CommonResult.data(roleVO);
    }

    /**
     * 查询角色用户分页列表
     *
     * @param bo 参数
     * @return 用户分页列表
     */
    @Operation(summary = "查询角色用户分页列表")
    @OperationLog(module = "角色管理", value = "查询角色用户分页列表", type = OperationTypeEnum.QUERY, saveResponse = false)
    @SaCheckPermission(value = "system:upms:role:user:list")
    @PostMapping("/user/page")
    public CommonResult<CommonPageVO<SysUserSimpleVO>> getRoleUserPage(@Validated @RequestBody SysRoleUserQueryBO bo) {
        CommonPageVO<SysUserSimpleVO> page = roleService.getRoleUserPage(bo);
        return CommonResult.data(page);
    }

    /**
     * 查询不在指定角色用户分页列表
     *
     * @param bo 参数
     * @return 用户分页列表
     */
    @Operation(summary = "查询不在指定角色用户分页列表")
    @OperationLog(module = "角色管理", value = "查询不在指定角色用户分页列表", type = OperationTypeEnum.QUERY, saveResponse = false)
    @SaCheckPermission(value = "system:upms:role:user:notInList")
    @PostMapping("/user/notInPage")
    public CommonResult<CommonPageVO<SysUserSimpleVO>> getNotInRoleUserPage(@Validated @RequestBody SysRoleUserQueryBO bo) {
        CommonPageVO<SysUserSimpleVO> page = roleService.getNotInRoleUserPage(bo);
        return CommonResult.data(page);
    }

    /**
     * 新增角色用户关系
     *
     * @param bo 参数
     */
    @Operation(summary = "新增角色用户关系")
    @OperationLog(module = "角色管理", value = "新增角色用户关系", type = OperationTypeEnum.SAVE)
    @SaCheckPermission(value = "system:upms:role:user:save")
    @PostMapping("/user/save")
    public CommonResult<Void> saveRoleUser(@Validated @RequestBody SysRoleUserSaveBO bo) {
        roleService.saveRoleUser(bo.getRoleId(), bo.getUserIds());
        return CommonResult.success();
    }

    /**
     * 删除角色用户关系
     *
     * @param bo 参数
     */
    @Operation(summary = "删除角色用户关系")
    @OperationLog(module = "角色管理", value = "删除角色用户关系", type = OperationTypeEnum.DELETE)
    @SaCheckPermission(value = "system:upms:role:user:delete")
    @PostMapping("/user/delete")
    public CommonResult<Void> deleteRoleUser(@Validated @RequestBody SysRoleUserDeleteBO bo) {
        roleService.deleteRoleUser(bo.getRoleId(), bo.getUserId());
        return CommonResult.success();
    }

    /**
     * 编辑角色数据权限
     *
     * @param bo 参数
     */
    @Operation(summary = "编辑角色数据权限")
    @OperationLog(module = "角色管理", value = "编辑角色数据权限", type = OperationTypeEnum.UPDATE)
    @SaCheckPermission(value = "system:upms:role:dataScope:update")
    @PostMapping("/dataScope/update")
    public CommonResult<Void> updateRoleDataScope(@Validated @RequestBody SysRoleDataScopeUpdateBO bo) {
        roleService.updateRoleDataScope(bo);
        return CommonResult.success();
    }

}

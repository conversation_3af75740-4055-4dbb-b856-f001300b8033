package cn.com.sipsg.module.system.module.upms.convert;

import cn.com.sipsg.module.system.module.upms.entity.SysDept;
import cn.com.sipsg.module.system.module.upms.pojo.SysDeptTree;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 部门转换类
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Mapper
public interface SysDeptConvert {

    SysDeptConvert INSTANCE = Mappers.getMapper(SysDeptConvert.class);

    default SysDeptTree convert(SysDept dept) {
        SysDeptTree deptTree = new SysDeptTree();
        deptTree.setId(dept.getDeptId());
        deptTree.setDeptName(dept.getDeptName());
        deptTree.setParentId(dept.getParentId());
        deptTree.setSort(dept.getSort());
        return deptTree;
    }

}

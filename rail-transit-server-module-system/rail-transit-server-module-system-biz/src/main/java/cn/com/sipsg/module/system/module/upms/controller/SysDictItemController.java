package cn.com.sipsg.module.system.module.upms.controller;

import cn.com.sipsg.common.operationlog.core.annotation.OperationLog;
import cn.com.sipsg.common.operationlog.core.enums.OperationTypeEnum;
import cn.com.sipsg.common.pojo.CommonResult;
import cn.com.sipsg.common.pojo.bo.UpdateStatusBO;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.validation.AddValidated;
import cn.com.sipsg.common.validation.UpdateValidated;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysDictItemQueryBO;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysDictItemSaveBO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysDictItemVO;
import cn.com.sipsg.module.system.module.upms.service.SysDictItemService;
import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 字典项控制器
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Tag(name = "字典项管理")
@RestController
@RequiredArgsConstructor
@RequestMapping("/system/upms/dictItem")
public class SysDictItemController {

    private final SysDictItemService dictItemService;

    /**
     * 查询字典项分页列表
     *
     * @param bo 参数
     * @return 字典项分页列表
     */
    @Operation(summary = "查询字典项分页列表")
    @OperationLog(module = "字典管理", value = "查询字典项分页列表", type = OperationTypeEnum.QUERY, saveResponse = false)
    @SaCheckPermission(value = "system:upms:dictItem:list")
    @PostMapping("/page")
    public CommonResult<CommonPageVO<SysDictItemVO>> page(@RequestBody SysDictItemQueryBO bo) {
        return CommonResult.data(dictItemService.page(bo));
    }

    /**
     * 新增字典项
     *
     * @param bo 参数
     * @return 字典项ID
     */
    @Operation(summary = "新增字典项")
    @OperationLog(module = "字典管理", value = "新增字典项", type = OperationTypeEnum.SAVE)
    @SaCheckPermission(value = "system:upms:dictItem:save")
    @PostMapping("/save")
    public CommonResult<String> save(@AddValidated @RequestBody SysDictItemSaveBO bo) {
        return CommonResult.data(dictItemService.save(bo));
    }

    /**
     * 编辑字典项
     *
     * @param bo 参数
     */
    @Operation(summary = "编辑字典项")
    @OperationLog(module = "字典管理", value = "编辑字典项", type = OperationTypeEnum.UPDATE)
    @SaCheckPermission(value = "system:upms:dictItem:update")
    @PostMapping("/update")
    public CommonResult<Void> update(@UpdateValidated @RequestBody SysDictItemSaveBO bo) {
        dictItemService.update(bo);
        return CommonResult.success();
    }

    /**
     * 查询字典项详情
     *
     * @param itemId 字典项ID
     * @return 字典项详情
     */
    @Operation(summary = "查询字典项详情")
    @Parameter(name = "itemId", description = "字典项ID", required = true)
    @OperationLog(module = "字典管理", value = "查询字典项详情", type = OperationTypeEnum.QUERY, saveResponse = false)
    @SaCheckPermission(value = "system:upms:dictItem:detail")
    @GetMapping("/detail")
    public CommonResult<SysDictItemVO> detail(@RequestParam String itemId) {
        return CommonResult.data(dictItemService.detail(itemId));
    }

    /**
     * 删除字典项
     *
     * @param itemId 字典项ID
     */
    @Operation(summary = "删除字典项")
    @Parameter(name = "itemId", description = "字典项ID", required = true)
    @OperationLog(module = "字典管理", value = "删除字典项", type = OperationTypeEnum.DELETE)
    @SaCheckPermission(value = "system:upms:dictItem:delete")
    @PostMapping("/delete")
    public CommonResult<Void> delete(@RequestParam String itemId) {
        dictItemService.delete(itemId);
        return CommonResult.success();
    }

    /**
     * 编辑字典项状态
     *
     * @param bo 参数
     */
    @Operation(summary = "编辑字典项状态")
    @OperationLog(module = "字典管理", value = "编辑字典项状态", type = OperationTypeEnum.UPDATE)
    @SaCheckPermission(value = "system:upms:dictItem:update")
    @PostMapping("/updateStatus")
    public CommonResult<Void> updateStatus(@Validated @RequestBody UpdateStatusBO bo) {
        dictItemService.updateStatus(bo.getId(), bo.getStatus());
        return CommonResult.success();
    }

}

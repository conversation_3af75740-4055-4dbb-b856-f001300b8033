package cn.com.sipsg.module.system.module.upms.pojo.bo;

import cn.com.sipsg.common.enums.CommonStatusEnum;
import cn.com.sipsg.common.pojo.bo.SortablePageBO;
import cn.com.sipsg.common.validation.InEnum;
import cn.hutool.core.date.DatePattern;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * 用户查询 BO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "用户查询 BO")
public class SysUserQueryBO extends SortablePageBO {

    /**
     * 用户名
     */
    @Schema(description = "用户名")
    private String username;

    /**
     * 真实姓名
     */
    @Schema(description = "真实姓名")
    private String realName;

    /**
     * 手机号
     */
    @Schema(description = "手机号")
    private String mobile;

    /**
     * 状态
     */
    @Schema(description = "状态")
    @InEnum(enumClass = CommonStatusEnum.class, message = "用户状态不正确")
    private Integer status;

    /**
     * 创建时间起始值
     */
    @Schema(description = "创建时间起始值")
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime createTimeStart;

    /**
     * 创建时间结束值
     */
    @Schema(description = "创建时间结束值")
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime createTimeEnd;

    /**
     * 部门ID
     */
    @Schema(description = "部门ID")
    private String deptId;

}

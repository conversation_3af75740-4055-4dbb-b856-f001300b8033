package cn.com.sipsg.module.system.module.upms.pojo.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

/**
 * 角色用户删除 BO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "角色用户删除 BO")
public class SysRoleUserDeleteBO {

    /**
     * 角色ID
     */
    @Schema(description = "角色ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1111")
    @NotBlank(message = "角色ID不能为空")
    private String roleId;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1111")
    @NotBlank(message = "用户ID不能为空")
    private String userId;

}

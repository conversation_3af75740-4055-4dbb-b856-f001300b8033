package cn.com.sipsg.module.system.module.upms.service;

import cn.com.sipsg.common.mybatis.core.service.BaseServiceX;
import cn.com.sipsg.module.system.module.upms.entity.SysPermModule;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysPermModuleSaveBO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysPermModulePermVO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysPermModuleVO;

import java.util.List;

/**
 * <p>
 * 权限模块表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public interface SysPermModuleService extends BaseServiceX<SysPermModule> {

    /**
     * 新增权限模块
     *
     * @param bo 参数
     * @return 权限模块ID
     */
    String save(SysPermModuleSaveBO bo);

    /**
     * 编辑权限模块
     *
     * @param bo 参数
     */
    void update(SysPermModuleSaveBO bo);

    /**
     * 删除权限模块
     *
     * @param permModuleId 权限模块ID
     */
    void delete(String permModuleId);

    /**
     * 查询权限模块详情
     *
     * @param permModuleId 权限模块ID
     * @return 权限模块详情
     */
    SysPermModuleVO detail(String permModuleId);

    /**
     * 查询权限模块列表及其关联的权限列表
     *
     * @return 权限模块列表
     */
    List<SysPermModulePermVO> listWithPerm();

}

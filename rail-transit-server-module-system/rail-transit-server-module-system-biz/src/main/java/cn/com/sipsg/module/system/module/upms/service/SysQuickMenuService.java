package cn.com.sipsg.module.system.module.upms.service;

import cn.com.sipsg.common.mybatis.core.service.BaseServiceX;
import cn.com.sipsg.module.system.module.upms.entity.SysQuickMenu;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysQuickMenuSaveBO;

import java.util.List;

/**
 * 快捷菜单服务接口
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public interface SysQuickMenuService extends BaseServiceX<SysQuickMenu> {

    /**
     * 新增快捷菜单
     *
     * @param bo 参数
     */
    void save(SysQuickMenuSaveBO bo);

    /**
     * 删除快捷菜单
     *
     * @param menuId 菜单ID
     */
    void delete(String menuId);

    /**
     * 拖拽排序
     *
     * @param menuIds 菜单ID列表
     */
    void dragSort(List<String> menuIds);

}
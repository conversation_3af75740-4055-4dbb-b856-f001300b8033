package cn.com.sipsg.module.system.module.upms.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 用户岗位关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@TableName(value = "medox_sys_user_post", autoResultMap = true)
public class SysUserPost {

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 部门岗位ID
     */
    private String deptPostId;

    /**
     * 岗位ID
     */
    private String postId;

}

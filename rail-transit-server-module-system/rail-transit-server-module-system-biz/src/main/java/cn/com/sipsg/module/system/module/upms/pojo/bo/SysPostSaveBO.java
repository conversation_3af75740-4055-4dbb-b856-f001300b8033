package cn.com.sipsg.module.system.module.upms.pojo.bo;

import cn.com.sipsg.common.validation.group.UpdateGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 岗位保存 BO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "岗位保存 BO")
public class SysPostSaveBO {

    /**
     * 岗位 ID
     */
    @Schema(description = "岗位 ID", example = "1111")
    @NotBlank(message = "岗位 ID 不能为空", groups = {UpdateGroup.class})
    private String postId;

    /**
     * 岗位名称
     */
    @Schema(description = "岗位名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "经理")
    @NotBlank(message = "岗位名称不能为空")
    private String postName;

    /**
     * 岗位等级
     */
    @Schema(description = "岗位等级", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "岗位等级不能为空")
    private Integer postLevel;

    /**
     * 是否是领导岗位
     */
    @Schema(description = "是否是领导岗位", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    @NotNull(message = "是否是领导岗位不能为空")
    private Boolean leaderPost;

}

package cn.com.sipsg.module.system.module.upms.entity;

import cn.com.sipsg.common.mybatis.core.dataobject.BaseDO;
import cn.com.sipsg.common.mybatis.core.type.StringSetTypeHandler;
import cn.com.sipsg.module.system.module.upms.enums.DataScopeEnum;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.github.yulichang.annotation.FieldMapping;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 角色表
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@FieldNameConstants
@TableName(value = "medox_sys_role", autoResultMap = true)
public class SysRole extends BaseDO {

    /**
     * 角色ID
     */
    @TableId
    private String roleId;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色编码
     */
    private String roleCode;

    /**
     * 数据范围
     */
    private DataScopeEnum dataScope;

    /**
     * 数据范围(指定部门数组)
     * 适用于 {@link #dataScope} 的值为 {@link DataScopeEnum#DEPT_CUSTOM} 时
     */
    @TableField(typeHandler = StringSetTypeHandler.class)
    private Set<String> dataScopeDeptIds;

    /**
     * 菜单ID列表
     */
    @TableField(exist = false)
    @FieldMapping(tag = SysRoleMenu.class, thisField = Fields.roleId, joinField = SysRoleMenu.Fields.roleId, select = SysRoleMenu.Fields.menuId)
    private List<String> menuIds;

}

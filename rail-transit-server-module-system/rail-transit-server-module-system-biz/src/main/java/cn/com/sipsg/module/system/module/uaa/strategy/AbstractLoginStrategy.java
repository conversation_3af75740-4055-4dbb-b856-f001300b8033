package cn.com.sipsg.module.system.module.uaa.strategy;

import cn.com.sipsg.common.captcha.config.CaptchaProperties;
import cn.com.sipsg.common.enums.CommonStatusEnum;
import cn.com.sipsg.common.exception.BusinessException;
import cn.com.sipsg.common.exception.enums.ErrorCodeEnum;
import cn.com.sipsg.common.util.AssertUtils;
import cn.com.sipsg.common.util.SmUtils;
import cn.com.sipsg.common.util.ValidationUtils;
import cn.com.sipsg.module.system.module.uaa.pojo.bo.LoginBO;
import cn.com.sipsg.module.system.module.uaa.pojo.vo.LoginVO;
import cn.com.sipsg.module.system.module.uaa.service.AuthService;
import cn.com.sipsg.module.system.module.uaa.service.LoginLimitService;
import cn.com.sipsg.module.system.module.upms.entity.SysUser;
import cn.com.sipsg.module.system.module.upms.service.SysUserService;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.BCrypt;
import com.xingyuv.captcha.model.common.ResponseModel;
import com.xingyuv.captcha.model.vo.CaptchaVO;
import com.xingyuv.captcha.service.CaptchaService;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import javax.validation.Validator;
import java.util.Objects;

/**
 * 抽象登录策略
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Slf4j
public abstract class AbstractLoginStrategy implements LoginStrategy {

    @Resource
    protected SysUserService userService;

    @Resource
    protected CaptchaService captchaService;

    @Resource
    protected CaptchaProperties captchaProperties;

    @Resource
    protected Validator validator;

    @Resource
    protected AuthService authService;

    @Resource
    protected LoginLimitService loginLimitService;

    protected void checkCaptcha(LoginBO bo) {
        if (!captchaProperties.getEnabled()) {
            return;
        }
        // 校验验证码
        ValidationUtils.validate(validator, bo, LoginBO.CaptchaEnableGroup.class);
        CaptchaVO captchaVO = new CaptchaVO();
        captchaVO.setCaptchaVerification(bo.getCaptchaVerification());
        ResponseModel response = captchaService.verification(captchaVO);
        // 验证不通过
        AssertUtils.isTrue(!response.isSuccess(), StrUtil.format("验证码不正确，原因：{}", response.getRepMsg()));
    }

    /**
     * 账号密码登录
     *
     * @param bo 登录参数
     * @return 登录结果
     */
    protected LoginVO passwordLogin(LoginBO bo) {
        // 校验用户名、密码
        ValidationUtils.validate(validator, bo, LoginBO.PasswordLoginGroup.class);
        String username = bo.getUsername();
        if (loginLimitService.isBlocked(username)) {
            throw new BusinessException(ErrorCodeEnum.TOO_MANY_LOGIN_ATTEMPTS.getCode(), StrUtil.format("密码输入错误次数过多，请{}分钟后再试", loginLimitService.getRemainingLockTime(username)));
        }
        String password = bo.getPassword();
        SysUser user = userService.getByUsername(username);
        try {
            password = SmUtils.sm2DecryptStr(password);
        } catch (Exception e) {
            log.warn("密码解密失败", e);
            loginLimitService.loginFailed(username);
            throw new BusinessException(ErrorCodeEnum.INVALID_USERNAME_OR_PASSWORD);
        }
        if (Objects.isNull(user) || !BCrypt.checkpw(password, user.getPassword())) {
            loginLimitService.loginFailed(username);
            throw new BusinessException(ErrorCodeEnum.INVALID_USERNAME_OR_PASSWORD);
        }
        loginLimitService.loginSucceeded(username);
        AssertUtils.isTrue(user.getStatus() == CommonStatusEnum.DISABLE, ErrorCodeEnum.INVALID_USER_STATUS);
        return authService.buildLoginVO(user, bo.getAppCode());
    }

}

package cn.com.sipsg.module.system.module.upms.pojo.bo;

import cn.com.sipsg.common.constant.CommonConstants;
import cn.com.sipsg.common.validation.InEnum;
import cn.com.sipsg.common.validation.group.UpdateGroup;
import cn.com.sipsg.module.system.module.upms.enums.MenuBindTypeEnum;
import cn.com.sipsg.module.system.module.upms.enums.MenuTypeEnum;
import cn.com.sipsg.module.system.module.upms.enums.RoutingMethodEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Set;

/**
 * 菜单保存 BO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "菜单保存 BO")
public class SysMenuSaveBO {

    /**
     * 菜单ID
     */
    @Schema(description = "菜单ID", example = "1111")
    @NotBlank(message = "菜单ID不能为空", groups = {UpdateGroup.class})
    private String menuId;

    /**
     * 父菜单ID
     */
    @Schema(description = "父菜单ID", defaultValue = CommonConstants.PARENT_ID_ROOT, example = "1111")
    private String parentId;

    /**
     * 菜单名称
     */
    @Schema(description = "菜单名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "用户管理")
    @NotBlank(message = "菜单名称不能为空")
    private String menuName;

    /**
     * 菜单类型
     */
    @Schema(description = "菜单类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "菜单类型不能为空")
    @InEnum(enumClass = MenuTypeEnum.class, message = "菜单类型不正确")
    private Integer menuType;

    /**
     * 权限字
     */
    @Schema(description = "权限字", example = "sysUser:add")
    private String permCode;

    /**
     * 绑定类型
     */
    @Schema(description = "绑定类型", example = "0")
    @InEnum(enumClass = MenuBindTypeEnum.class, message = "绑定类型不正确")
    private Integer bindType;

    /**
     * 路由地址
     */
    @Schema(description = "路由地址", example = "/user-manage")
    private String routerUrl;

    /**
     * 路由参数
     */
    @Schema(description = "路由参数", example = "?id=123")
    private String routerParam;

    /**
     * 动态表单ID
     */
    @Schema(description = "动态表单ID", example = "1111")
    private String onlineFormId;

    /**
     * 动态表单的流程ID
     */
    @Schema(description = "动态表单的流程ID")
    private String onlineFlowEntryId;

    /**
     * 排序值
     */
    @Schema(description = "排序值", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "排序值不能为空")
    private Integer sort;

    /**
     * 图标
     */
    @Schema(description = "图标", example = "el-icon-user")
    private String icon;

    /**
     * 组件路径
     */
    @Schema(description = "组件路径", example = "user-manage/index.vue")
    private String componentPath;

    /**
     * 是否显示
     */
    @Schema(description = "是否显示", example = "true")
    private Boolean showable;

    /**
     * 是否缓存
     */
    @Schema(description = "是否缓存", example = "true")
    private Boolean cacheable;

    /**
     * 路由跳转方式
     */
    @Schema(description = "路由跳转方式", example = "0")
    @InEnum(enumClass = RoutingMethodEnum.class, message = "路由跳转方式不正确")
    private Integer routingMethod;

    /**
     * 链接地址
     */
    @Schema(description = "链接地址", example = "https://www.baidu.com")
    private String targetUrl;

    /**
     * 应用标识
     */
    @Schema(description = "应用标识", requiredMode = Schema.RequiredMode.REQUIRED, example = "web")
    @NotBlank(message = "应用标识不能为空")
    private String appCode;

    /**
     * 权限ID列表
     */
    @Schema(description = "权限ID列表", example = "[\"1111\"]")
    private Set<String> permIds;

}

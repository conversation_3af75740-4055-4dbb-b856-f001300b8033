package cn.com.sipsg.module.system.module.upms.pojo.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 字典树批量更新 BO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "SysDictTreeBatchUpdateBO对象")
public class SysDictTreeBatchUpdateBO implements Serializable {

    private static final long serialVersionUID = 7432016877591626450L;

    /**
     * 字典组id
     */
    @Schema(description = "字典组id")
    @NotBlank(message = "字典组id不能为空")
    private String id;

    /**
     * 字典项列表
     */
    @Schema(description = "字典项列表")
    @NotEmpty(message = "字典项列表不能为空")
    private List<DictTreeItemUpdateBO> dictBos;

    /**
     * 字典树项更新BO
     */
    @Getter
    @Setter
    @Schema(description = "DictItemUpdateBO对象")
    public static class DictTreeItemUpdateBO {

        /**
         * 主键
         */
        @Schema(description = "主键")
        private String id;

        /**
         * 父id
         */
        @Schema(description = "父id")
        @NotBlank(message = "父id不能为空")
        private String parentId;

        /**
         * 字典code
         */
        @Schema(description = "字典code")
        @NotBlank(message = "字典code不能为空")
        private String code;

        /**
         * 字典名称
         */
        @Schema(description = "字典名称")
        @NotBlank(message = "字典名称不能为空")
        private String name;

        /**
         * 排序
         */
        @Schema(description = "排序")
        private Integer sort;

        /**
         * 备注
         */
        @Schema(description = "备注")
        private String remark;

        /**
         * 是否启用 0-否，1-是
         */
        @Schema(description = "是否启用 0-否，1-是")
        @NotBlank(message = "是否启用不能为空")
        private Integer status;

    }

}


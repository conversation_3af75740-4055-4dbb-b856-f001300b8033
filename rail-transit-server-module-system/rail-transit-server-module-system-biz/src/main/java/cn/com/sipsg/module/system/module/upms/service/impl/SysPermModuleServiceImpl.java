package cn.com.sipsg.module.system.module.upms.service.impl;

import cn.com.sipsg.common.constant.CommonConstants;
import cn.com.sipsg.common.exception.enums.ErrorCodeEnum;
import cn.com.sipsg.common.mybatis.core.query.MPJLambdaWrapperX;
import cn.com.sipsg.common.util.AssertUtils;
import cn.com.sipsg.common.util.BeanUtils;
import cn.com.sipsg.module.system.module.upms.convert.SysPermModuleConvert;
import cn.com.sipsg.module.system.module.upms.entity.SysPerm;
import cn.com.sipsg.module.system.module.upms.entity.SysPermModule;
import cn.com.sipsg.module.system.module.upms.enums.PermModuleTypeEnum;
import cn.com.sipsg.module.system.module.upms.mapper.SysPermMapper;
import cn.com.sipsg.module.system.module.upms.mapper.SysPermModuleMapper;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysPermModuleSaveBO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysPermModulePermVO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysPermModuleVO;
import cn.com.sipsg.module.system.module.upms.service.SysPermModuleService;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 权限模块表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Service
@RequiredArgsConstructor
public class SysPermModuleServiceImpl extends ServiceImpl<SysPermModuleMapper, SysPermModule> implements SysPermModuleService {

    private final SysPermMapper permMapper;

    @Override
    @Transactional
    public String save(SysPermModuleSaveBO bo) {
        bo.setPermModuleId(null);
        if (StrUtil.isBlank(bo.getParentId())) {
            bo.setParentId(CommonConstants.PARENT_ID_ROOT);
        }
        // 校验权限模块
        checkSaveOrUpdate(null, bo.getParentId(), SysPermModuleConvert.INSTANCE.moduleType2Enum(bo.getModuleType()));
        // 保存权限模块
        SysPermModule permModule = SysPermModuleConvert.INSTANCE.convert(bo);
        this.save(permModule);
        return permModule.getPermModuleId();
    }

    @Override
    @Transactional
    public void update(SysPermModuleSaveBO bo) {
        if (StrUtil.isBlank(bo.getParentId())) {
            bo.setParentId(CommonConstants.PARENT_ID_ROOT);
        }
        // 校验权限模块
        checkSaveOrUpdate(bo.getPermModuleId(), bo.getParentId(), SysPermModuleConvert.INSTANCE.moduleType2Enum(bo.getModuleType()));
        // 更新权限模块
        SysPermModule permModule = SysPermModuleConvert.INSTANCE.convert(bo);
        this.updateById(permModule);
    }

    @Override
    @Transactional
    public void delete(String permModuleId) {
        // 删除权限模块
        this.removeById(permModuleId);
        // 删除权限模块下的权限
        permMapper.deleteByPermModuleId(permModuleId);
        // 删除子权限模块
        List<SysPermModule> permModuleList = this.selectListByParentId(SysPermModule::getParentId, permModuleId);
        if (CollUtil.isEmpty(permModuleList)) {
            return;
        }
        for (SysPermModule permModule : permModuleList) {
            this.delete(permModule.getPermModuleId());
        }
    }

    @Override
    public SysPermModuleVO detail(String permModuleId) {
        // 校验是否存在
        SysPermModule permModule = checkExist(permModuleId);
        return BeanUtils.copyProperties(permModule, SysPermModuleVO.class);
    }

    @Override
    public List<SysPermModulePermVO> listWithPerm() {
        MPJLambdaWrapperX<SysPermModule> queryWrapper = new MPJLambdaWrapperX<>();
        queryWrapper
                .selectAll(SysPermModule.class)
                .leftJoin(SysPerm.class, SysPerm::getPermModuleId, SysPermModule::getPermModuleId)
                .selectCollection(SysPerm.class, SysPermModule::getPerms);
        List<SysPermModule> permModuleList = this.selectJoinList(SysPermModule.class, queryWrapper);
        List<SysPermModulePermVO> permModulePermVOList = new ArrayList<>();
        for (SysPermModule permModule : permModuleList) {
            SysPermModulePermVO permModulePermVO = new SysPermModulePermVO();
            permModulePermVO.setId(permModule.getPermModuleId());
            permModulePermVO.setName(permModule.getModuleName());
            permModulePermVO.setType(permModule.getModuleType());
            permModulePermVO.setPerm(Boolean.FALSE);
            permModulePermVO.setParentId(permModule.getParentId());
            permModulePermVOList.add(permModulePermVO);
            List<SysPerm> perms = permModule.getPerms();
            if (CollUtil.isNotEmpty(perms)) {
                perms.sort(Comparator.comparingInt(SysPerm::getSort));
                for (SysPerm perm : perms) {
                    permModulePermVO = new SysPermModulePermVO();
                    permModulePermVO.setId(perm.getPermId());
                    permModulePermVO.setName(perm.getPermName());
                    permModulePermVO.setPerm(Boolean.TRUE);
                    permModulePermVO.setCode(perm.getPermCode());
                    permModulePermVO.setParentId(permModule.getPermModuleId());
                    permModulePermVOList.add(permModulePermVO);
                }
            }
        }
        return permModulePermVOList;
    }

    private void checkSaveOrUpdate(String permModuleId, String parentId, PermModuleTypeEnum permModuleType) {
        // 校验权限模块存在
        SysPermModule permModule = checkExist(permModuleId);
        // 校验权限模块类型
        if (ObjectUtil.isNotNull(permModule)) {
            AssertUtils.isTrue(permModuleType != permModule.getModuleType(), "权限模块类型不允许修改");
        }
        // 校验上级权限模块有效性
        checkParentPermModule(permModuleId, parentId);
    }

    private void checkParentPermModule(String permModuleId, String parentId) {
        if (StrUtil.isBlank(parentId) || CommonConstants.PARENT_ID_ROOT.equals(parentId)) {
            return;
        }
        // 1. 不能设置自己为上级权限模块
        AssertUtils.isTrue(StrUtil.equals(permModuleId, parentId), "不能设置自己为上级权限模块");
        // 2. 上级权限模块不存在
        SysPermModule parentPermModule = baseMapper.selectById(parentId);
        AssertUtils.isTrue(Objects.isNull(parentPermModule), "上级权限模块不存在");
        // 3. 校验上级权限模块的合法性
        AssertUtils.isTrue(parentPermModule.getModuleType() != PermModuleTypeEnum.GROUP, "当前类型权限模块的上级权限模块类型必须为分组");
        // 4. 递归校验上级权限模块，如果上级权限模块是自己的子模块，则报错，避免形成环路
        // permModuleId 为空，说明新增，不需要考虑环路
        if (permModuleId == null) {
            return;
        }
        for (int i = 0; i < Short.MAX_VALUE; i++) {
            // 4.1 校验环路
            parentId = parentPermModule.getParentId();
            AssertUtils.isTrue(StrUtil.equals(permModuleId, parentId), ErrorCodeEnum.PARENT_IS_CHILD);
            // 4.2 继续递归下一级上级权限模块
            if (StrUtil.isBlank(parentId) || CommonConstants.PARENT_ID_ROOT.equals(parentId)) {
                break;
            }
            parentPermModule = baseMapper.selectById(parentId);
            if (parentPermModule == null) {
                break;
            }
        }
    }

}

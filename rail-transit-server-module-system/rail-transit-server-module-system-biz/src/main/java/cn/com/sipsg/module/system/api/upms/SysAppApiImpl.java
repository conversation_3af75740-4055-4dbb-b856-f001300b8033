package cn.com.sipsg.module.system.api.upms;

import cn.com.sipsg.common.util.BeanUtils;
import cn.com.sipsg.module.system.api.upms.dto.SysAppRespDTO;
import cn.com.sipsg.module.system.module.upms.entity.SysApp;
import cn.com.sipsg.module.system.module.upms.mapper.SysAppMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

/**
 * 应用 API 实现类
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@RestController
@Validated
@RequiredArgsConstructor
public class SysAppApiImpl implements SysAppApi {

    private final SysAppMapper appMapper;

    @Override
    public SysAppRespDTO getAppByAppCode(String appCode) {
        SysApp app = appMapper.selectOne(SysApp::getAppCode, appCode, false);
        return BeanUtils.copyProperties(app, SysAppRespDTO.class);
    }

}

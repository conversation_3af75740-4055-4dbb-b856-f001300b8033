package cn.com.sipsg.module.system.module.upms.service;

import cn.com.sipsg.common.mybatis.core.service.BaseServiceX;
import cn.com.sipsg.common.pojo.vo.CommonImportVO;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.module.system.module.upms.entity.SysUser;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysUserQueryBO;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysUserResetPasswordBO;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysUserSaveBO;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysUserUpdatePasswordBO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysUserVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 用户服务接口
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public interface SysUserService extends BaseServiceX<SysUser> {

    /**
     * 查询用户分页列表
     *
     * @param bo 参数
     * @return 用户分页列表
     */
    CommonPageVO<SysUserVO> page(SysUserQueryBO bo);

    /**
     * 新增用户
     *
     * @param bo 参数
     * @return 用户ID
     */
    String save(SysUserSaveBO bo);

    /**
     * 编辑用户
     *
     * @param bo 参数
     */
    void update(SysUserSaveBO bo);

    /**
     * 查询用户详情
     *
     * @param userId 用户ID
     * @return 用户详情
     */
    SysUserVO detail(String userId);

    /**
     * 删除用户
     *
     * @param userId 用户ID
     */
    void delete(String userId);

    /**
     * 重置密码
     *
     * @param bo 参数
     */
    void resetPassword(SysUserResetPasswordBO bo);

    /**
     * 编辑状态
     *
     * @param userId 用户ID
     * @param status 状态
     */
    void updateStatus(String userId, Integer status);

    /**
     * 修改密码
     *
     * @param userId 用户ID
     * @param bo     参数
     */
    void updatePassword(String userId, SysUserUpdatePasswordBO bo);

    /**
     * 修改头像
     *
     * @param userId 用户ID
     * @param avatar 头像地址
     */
    void updateAvatar(String userId, String avatar);

    /**
     * 判断密码是否匹配
     *
     * @param rawPassword     未加密的密码
     * @param encodedPassword 加密后的密码
     * @return 是否匹配
     */
    boolean isPasswordMatch(String rawPassword, String encodedPassword);

    /**
     * 根据用户名获取用户
     *
     * @param username 用户名
     * @return 用户
     */
    SysUser getByUsername(String username);

    /**
     * 导出用户
     *
     * @param bo 参数
     */
    void export(SysUserQueryBO bo);

    /**
     * 导入用户
     *
     * @param file 文件
     * @return 导入结果
     */
    CommonImportVO importUser(MultipartFile file);

    /**
     * 导入用户
     *
     * @param dataList 数据列表
     * @return 导入结果
     */
    CommonImportVO importUser(List<Map<String, Object>> dataList);

    /**
     * 根据部门ID列表查询用户列表
     *
     * @param deptIds 部门ID列表
     * @return 用户列表
     */
    List<SysUser> getUserListByDeptIds(Collection<String> deptIds);

    /**
     * 根据角色ID列表查询用户列表
     *
     * @param roleIds 角色ID列表
     * @return 用户列表
     */
    List<SysUser> getUserListByRoleIds(Collection<String> roleIds);

    /**
     * 根据岗位ID列表查询用户列表
     *
     * @param postIds 岗位ID列表
     * @return 用户列表
     */
    List<SysUser> getUserListByPostIds(Collection<String> postIds);

    /**
     * 根据部门岗位ID列表查询用户列表
     *
     * @param deptPostIds 部门岗位ID列表
     * @return 用户列表
     */
    List<SysUser> getUserListByDeptPostIds(Collection<String> deptPostIds);

}

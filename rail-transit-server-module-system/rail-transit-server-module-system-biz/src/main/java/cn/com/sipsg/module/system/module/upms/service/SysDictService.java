package cn.com.sipsg.module.system.module.upms.service;

import cn.com.sipsg.common.mybatis.core.service.BaseServiceX;
import cn.com.sipsg.common.pojo.vo.CommonImportVO;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.module.system.module.upms.entity.SysDict;
import cn.com.sipsg.module.system.module.upms.entity.SysDictItem;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysDictQueryBO;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysDictSaveBO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysDictVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 字典表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public interface SysDictService extends BaseServiceX<SysDict> {

    /**
     * 查询字典分页列表
     *
     * @param bo 参数
     * @return 字典分页列表
     */
    CommonPageVO<SysDictVO> page(SysDictQueryBO bo);

    /**
     * 新增字典
     *
     * @param bo 参数
     * @return 字典ID
     */
    String save(SysDictSaveBO bo);

    /**
     * 编辑字典
     *
     * @param bo 参数
     */
    void update(SysDictSaveBO bo);

    /**
     * 删除字典
     *
     * @param dictId 字典ID
     */
    void delete(String dictId);

    /**
     * 查询字典详情
     *
     * @param dictId 字典ID
     * @return 字典详情
     */
    SysDictVO detail(String dictId);

    /**
     * 编辑字典状态
     *
     * @param dictId 字典ID
     * @param status 字典状态
     */
    void updateStatus(String dictId, Integer status);

    /**
     * 查询指定字典编码字典项列表
     *
     * @param dictCode 字典编码
     * @return 字典项列表
     */
    List<SysDictItem> getItemListByDictCode(String dictCode);

    /**
     * 查询指定字典编码字典项列表
     *
     * @param dictCode 字典编码
     * @param includeDisable 是否包含禁用
     * @return 字典项列表
     */
    List<SysDictItem> getItemListByDictCode(String dictCode, boolean includeDisable);

    /**
     * 导出字典
     *
     * @param bo 参数
     */
    void export(SysDictQueryBO bo);

    /**
     * 导入字典
     *
     * @param file 文件
     * @return 导入结果
     */
    Map<String, CommonImportVO> importDict(MultipartFile file);

}
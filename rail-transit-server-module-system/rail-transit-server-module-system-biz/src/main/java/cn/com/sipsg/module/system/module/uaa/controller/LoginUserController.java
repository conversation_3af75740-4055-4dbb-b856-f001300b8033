package cn.com.sipsg.module.system.module.uaa.controller;

import cn.com.sipsg.common.constant.PageConstants;
import cn.com.sipsg.common.operationlog.core.annotation.OperationLog;
import cn.com.sipsg.common.operationlog.core.enums.OperationTypeEnum;
import cn.com.sipsg.common.pojo.CommonResult;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.util.BeanUtils;
import cn.com.sipsg.common.util.SmUtils;
import cn.com.sipsg.module.system.module.uaa.pojo.bo.LoginUserQueryBO;
import cn.com.sipsg.module.system.module.uaa.pojo.vo.LoginUserVO;
import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.session.SaSession;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.PageUtil;
import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 在线用户管理
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Tag(name = "在线用户管理")
@RestController
@RequestMapping("/system/uaa/loginUser")
public class LoginUserController {

    public LoginUserController() {
        PageUtil.setFirstPageNo((int) PageConstants.DEFAULT_CURRENT);
    }

    /**
     * 查询在线用户分页列表
     *
     * @param bo 参数
     * @return 登录用户分页
     */
    @Operation(summary = "查询在线用户分页列表")
    @OperationLog(module = "在线用户管理", value = "查询在线用户分页列表", type = OperationTypeEnum.QUERY, saveResponse = false)
    @SaCheckPermission(value = "system:uaa:loginUser:list")
    @PostMapping("/page")
    public CommonResult<CommonPageVO<LoginUserVO>> page(@Validated @RequestBody LoginUserQueryBO bo) {
        List<String> sessionIds = StpUtil.searchTokenSessionId(StrUtil.EMPTY, 0, -1, false);
        List<LoginUserVO> list = new ArrayList<>(sessionIds.size());
        for (String sessionId : sessionIds) {
            SaSession session = StpUtil.getSessionBySessionId(sessionId);
            if (ObjectUtil.isNull(session)) {
                continue;
            }
            LoginUserVO loginUserVO = BeanUtils.copyProperties(session.get(SaSession.USER), LoginUserVO.class);
            if (ObjectUtil.isNull(loginUserVO) || StrUtil.isNotBlank(bo.getUsername()) && !StrUtil.contains(loginUserVO.getUsername(), bo.getUsername())) {
                continue;
            }
            loginUserVO.setToken(session.getToken());
            list.add(loginUserVO);
        }
        // 根据登录时间倒序
        list.sort((o1, o2) -> o2.getLoginTime().compareTo(o1.getLoginTime()));
        CommonPageVO<LoginUserVO> page = CommonPageVO.build(list, bo);
        page.getRecords().forEach(vo -> vo.setToken(SmUtils.sm2EncryptStr(vo.getToken())));
        return CommonResult.data(page);
    }

    /**
     * 清除会话
     *
     * @param token 令牌
     */
    @Operation(summary = "清除会话")
    @Parameter(name = "token", description = "令牌", required = true)
    @OperationLog(module = "在线用户管理", value = "清除会话", type = OperationTypeEnum.DELETE)
    @SaCheckPermission(value = "system:uaa:loginUser:delete")
    @PostMapping("/delete")
    public CommonResult<Void> delete(@RequestParam String token) {
        StpUtil.logoutByTokenValue(SmUtils.sm2DecryptStr(token));
        return CommonResult.success();
    }

}

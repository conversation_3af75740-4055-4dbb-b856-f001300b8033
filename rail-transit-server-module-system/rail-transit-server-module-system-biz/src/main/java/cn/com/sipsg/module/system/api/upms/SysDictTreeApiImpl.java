package cn.com.sipsg.module.system.api.upms;

import cn.com.sipsg.common.constant.CommonConstants;
import cn.com.sipsg.common.mybatis.core.query.LambdaQueryWrapperX;
import cn.com.sipsg.module.system.api.upms.dto.SysDictTreeRespDTO;
import cn.com.sipsg.module.system.module.upms.entity.SysDictTree;
import cn.com.sipsg.module.system.module.upms.service.SysDictTreeService;
import cn.hutool.core.bean.BeanUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;

/**
 * 字典树 API 实现类
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@RestController
@Validated
@RequiredArgsConstructor
public class SysDictTreeApiImpl implements SysDictTreeApi {

    private final SysDictTreeService sysDictTreeService;

    @Override
    public List<SysDictTreeRespDTO> getDictTreeByDictTreeCode(String dictTreeCode) {
        return sysDictTreeService.getDictTreeByDictTreeCode(dictTreeCode);
    }

    @Override
    public List<SysDictTreeRespDTO> listByDictTreeCode(String dictTreeCode) {
        SysDictTree sysDictTree = sysDictTreeService.getOne(new LambdaQueryWrapperX<SysDictTree>().eq(SysDictTree::getCode, dictTreeCode).eq(SysDictTree::getParentId, CommonConstants.PARENT_ID_ROOT), false);
        if (sysDictTree == null) {
            return Collections.emptyList();
        }
        List<SysDictTree> dictTreeList = sysDictTreeService.list(new LambdaQueryWrapperX<SysDictTree>().eq(SysDictTree::getTreeId, sysDictTree.getTreeId()));
        return BeanUtil.copyToList(dictTreeList, SysDictTreeRespDTO.class);
    }

}

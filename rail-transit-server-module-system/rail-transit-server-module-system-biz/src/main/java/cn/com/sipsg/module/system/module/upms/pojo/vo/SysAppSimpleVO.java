package cn.com.sipsg.module.system.module.upms.pojo.vo;

import cn.com.sipsg.common.enums.CommonStatusEnum;
import cn.com.sipsg.module.system.module.upms.enums.AppTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 应用精简 VO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "应用精简 VO")
public class SysAppSimpleVO {

    /**
     * 应用标识
     */
    @Schema(description = "应用标识")
    private String appCode;

    /**
     * 应用名称
     */
    @Schema(description = "应用名称")
    private String appName;

    /**
     * 应用类型
     */
    @Schema(description = "应用类型")
    private AppTypeEnum appType;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private CommonStatusEnum status;

}

package cn.com.sipsg.module.system.module.uaa.strategy.impl;

import cn.com.sipsg.module.system.module.uaa.enums.GrantTypeEnum;
import cn.com.sipsg.module.system.module.uaa.pojo.bo.LoginBO;
import cn.com.sipsg.module.system.module.uaa.pojo.vo.LoginVO;
import cn.com.sipsg.module.system.module.uaa.strategy.AbstractLoginStrategy;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 密码登录策略
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Service
@RequiredArgsConstructor
public class PasswordLoginStrategy extends AbstractLoginStrategy {

    @Override
    public LoginVO login(LoginBO bo) {
        // 校验验证码
        checkCaptcha(bo);
        return passwordLogin(bo);
    }

    @Override
    public GrantTypeEnum getGrantType() {
        return GrantTypeEnum.PASSWORD;
    }

}

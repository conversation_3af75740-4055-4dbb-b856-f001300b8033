package cn.com.sipsg.module.system.module.upms.controller;

import cn.com.sipsg.common.constant.PageConstants;
import cn.com.sipsg.common.operationlog.core.annotation.OperationLog;
import cn.com.sipsg.common.operationlog.core.enums.OperationTypeEnum;
import cn.com.sipsg.common.pojo.CommonResult;
import cn.com.sipsg.common.pojo.bo.DeleteBO;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.util.BeanUtils;
import cn.com.sipsg.module.system.module.upms.entity.SysFile;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysFileQueryBO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysFilePartVO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysFileVO;
import cn.com.sipsg.module.system.module.upms.service.SysFileService;
import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.collection.CollUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Comparator;
import java.util.List;

/**
 * 文件控制器
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Tag(name = "文件管理")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/system/upms/file")
public class SysFileController {

    private final SysFileService fileService;

    /**
     * 上传文件
     *
     * @param file      文件
     * @param groupCode 分组编码
     */
    @Operation(summary = "上传文件")
    @Parameters({
            @Parameter(name = "file", required = true, schema = @Schema(type = "file"), description = "文件"),
            @Parameter(name = "groupCode", description = "分组编码")
    })
    @OperationLog(module = "文件管理", value = "上传文件", type = OperationTypeEnum.SAVE)
    @PostMapping("/upload")
    public CommonResult<SysFileVO> upload(@RequestParam MultipartFile file, @RequestParam(required = false) String groupCode) {
        SysFile sysFile = fileService.upload(file, groupCode);
        return CommonResult.data(BeanUtils.copyProperties(sysFile, SysFileVO.class));
    }

    /**
     * 上传文件-Base64
     *
     * @param base64    Base64字符串
     * @param fileName  文件名
     * @param groupCode 分组编码
     */
    @Operation(summary = "上传文件-Base64")
    @Parameters({
            @Parameter(name = "base64", required = true, description = "Base64字符串"),
            @Parameter(name = "fileName", required = true, description = "文件名"),
            @Parameter(name = "groupCode", description = "分组编码")
    })
    @OperationLog(module = "文件管理", value = "上传文件-Base64", type = OperationTypeEnum.SAVE)
    @PostMapping("/uploadBase64")
    public CommonResult<SysFileVO> uploadBase64(@RequestParam String base64, @RequestParam String fileName, @RequestParam(required = false) String groupCode) {
        SysFile sysFile = fileService.uploadBase64(base64, fileName, groupCode);
        return CommonResult.data(BeanUtils.copyProperties(sysFile, SysFileVO.class));
    }

    /**
     * 删除文件
     *
     * @param fileId 文件ID
     */
    @Operation(summary = "删除文件")
    @Parameter(name = "fileId", required = true, description = "文件ID")
    @OperationLog(module = "文件管理", value = "删除文件", type = OperationTypeEnum.DELETE)
    @SaCheckPermission(value = "system:upms:file:delete")
    @PostMapping("/delete")
    public CommonResult<Void> delete(@RequestParam String fileId) {
        fileService.deleteById(fileId);
        return CommonResult.success();
    }

    /**
     * 批量删除文件
     *
     * @param bo 批量删除文件
     */
    @Operation(summary = "批量删除文件")
    @OperationLog(module = "文件管理", value = "批量删除文件", type = OperationTypeEnum.DELETE)
    @SaCheckPermission(value = "system:upms:file:delete")
    @PostMapping("/deleteBatch")
    public CommonResult<Void> delete(@Validated @RequestBody DeleteBO bo) {
        fileService.deleteBatch(bo.getIds());
        return CommonResult.success();
    }

    /**
     * 查询文件分页列表
     *
     * @param bo 参数
     * @return 文件分页列表
     */
    @Operation(summary = "查询文件分页列表")
    @OperationLog(module = "文件管理", value = "查询文件分页列表", type = OperationTypeEnum.QUERY, saveResponse = false)
    @SaCheckPermission(value = "system:upms:file:list")
    @PostMapping("/page")
    public CommonResult<CommonPageVO<SysFileVO>> page(@RequestBody SysFileQueryBO bo) {
        return CommonResult.data(fileService.page(bo));
    }

    /**
     * 查询文件列表
     *
     * @param bo 参数
     * @return 文件列表
     */
    @Operation(summary = "查询文件列表")
    @PostMapping("/list")
    public CommonResult<List<SysFileVO>> list(@Validated(SysFileQueryBO.ListGroup.class) @RequestBody SysFileQueryBO bo) {
        bo.setSize(PageConstants.SIZE_NONE);
        List<SysFileVO> list = CollUtil.sort(fileService.page(bo).getRecords(), Comparator.comparing(SysFileVO::getCreateTime));
        return CommonResult.data(list);
    }

    /**
     * 根据分组编码查询文件列表
     *
     * @param groupCode 分组编码
     * @return 文件列表
     */
    @SaIgnore
    @Operation(summary = "根据分组编码查询文件列表")
    @Parameter(name = "groupCode", required = true, description = "分组编码")
    @GetMapping("/listByGroupCode")
    public CommonResult<List<SysFileVO>> listByGroupCode(@RequestParam String groupCode) {
        List<SysFile> fileList = fileService.lambdaQuery().eq(SysFile::getGroupCode, groupCode).orderByAsc(SysFile::getCreateTime).list();
        return CommonResult.data(BeanUtils.copyToList(fileList, SysFileVO.class));
    }

    /**
     * 下载文件
     *
     * @param fileId 文件ID
     */
    @Operation(summary = "下载文件")
    @Parameter(name = "fileId", required = true, description = "文件ID")
    @OperationLog(module = "文件管理", value = "下载文件", type = OperationTypeEnum.QUERY, saveResponse = false)
    @GetMapping("/download")
    public void download(@RequestParam String fileId) {
        fileService.download(fileId);
    }

    /**
     * 根据文件哈希获取文件
     *
     * @param fileHash 文件哈希
     */
    @Operation(summary = "根据文件哈希获取文件")
    @Parameters({
            @Parameter(name = "fileHash", required = true, description = "文件哈希"),
            @Parameter(name = "groupCode", description = "分组编码")
    })
    @GetMapping("/getFileByHash")
    public CommonResult<SysFileVO> getFileByHash(@RequestParam String fileHash, @RequestParam String groupCode) {
        SysFile sysFile = fileService.getFileByHash(fileHash, groupCode);
        return CommonResult.data(BeanUtils.copyProperties(sysFile, SysFileVO.class));
    }

    /**
     * 初始化分片上传
     *
     * @param fileName  文件名
     * @param groupCode 分组编码
     */
    @Operation(summary = "初始化分片上传")
    @Parameters({
            @Parameter(name = "fileName", required = true, description = "文件名"),
            @Parameter(name = "fileHash", required = true, description = "文件哈希"),
            @Parameter(name = "groupCode", description = "分组编码")
    })
    @OperationLog(module = "文件管理", value = "初始化分片上传", type = OperationTypeEnum.SAVE)
    @PostMapping("/initMultipartUpload")
    public CommonResult<String> initMultipartUpload(@RequestParam String fileName, @RequestParam String fileHash, @RequestParam(required = false) String groupCode) {
        return CommonResult.data(fileService.initMultipartUpload(fileName, fileHash, groupCode));
    }

    /**
     * 上传分片
     *
     * @param file       文件
     * @param uploadId   上传ID
     * @param partNumber 分片号
     */
    @Operation(summary = "上传分片")
    @Parameters({
            @Parameter(name = "file", required = true, schema = @Schema(type = "file"), description = "文件"),
            @Parameter(name = "uploadId", required = true, description = "上传ID"),
            @Parameter(name = "partNumber", required = true, description = "分片号")
    })
    @PostMapping("/uploadPart")
    public CommonResult<SysFilePartVO> uploadPart(@RequestParam MultipartFile file, @RequestParam String uploadId, @RequestParam Integer partNumber) {
        SysFilePartVO sysFilePartVO = fileService.uploadPart(uploadId, partNumber, file);
        return CommonResult.data(sysFilePartVO);
    }

    /**
     * 完成分片上传
     *
     * @param uploadId 上传ID
     */
    @Operation(summary = "完成分片上传")
    @Parameter(name = "uploadId", required = true, description = "上传ID")
    @OperationLog(module = "文件管理", value = "完成分片上传", type = OperationTypeEnum.UPDATE)
    @PostMapping("/completeMultipartUpload")
    public CommonResult<SysFileVO> completeMultipartUpload(@RequestParam String uploadId) {
        SysFile sysFile = fileService.completeMultipartUpload(uploadId);
        return CommonResult.data(BeanUtils.copyProperties(sysFile, SysFileVO.class));
    }

    /**
     * 取消分片上传
     *
     * @param uploadId 上传ID
     */
    @Operation(summary = "取消分片上传")
    @Parameter(name = "uploadId", required = true, description = "上传ID")
    @OperationLog(module = "文件管理", value = "取消分片上传", type = OperationTypeEnum.DELETE)
    @PostMapping("/abortMultipartUpload")
    public CommonResult<Void> abortMultipartUpload(@RequestParam String uploadId) {
        fileService.abortMultipartUpload(uploadId);
        return CommonResult.success();
    }

    /**
     * 查询已上传的分片
     *
     * @param uploadId 上传ID
     */
    @Operation(summary = "查询已上传的分片")
    @Parameter(name = "uploadId", required = true, description = "上传ID")
    @GetMapping("/queryUploadedParts")
    public CommonResult<List<SysFilePartVO>> queryUploadedParts(@RequestParam String uploadId) {
        List<SysFilePartVO> list = BeanUtils.copyToList(fileService.queryUploadedParts(uploadId), SysFilePartVO.class);
        return CommonResult.data(list);
    }

}

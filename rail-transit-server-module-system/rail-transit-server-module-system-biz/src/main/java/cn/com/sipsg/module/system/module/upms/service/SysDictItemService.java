package cn.com.sipsg.module.system.module.upms.service;

import cn.com.sipsg.common.mybatis.core.service.BaseServiceX;
import cn.com.sipsg.common.pojo.vo.CommonImportVO;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.module.system.module.upms.entity.SysDictItem;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysDictItemQueryBO;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysDictItemSaveBO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysDictItemVO;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 字典表项 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public interface SysDictItemService extends BaseServiceX<SysDictItem> {

    /**
     * 查询字典项分页列表
     *
     * @param bo 参数
     * @return 字典项分页列表
     */
    CommonPageVO<SysDictItemVO> page(SysDictItemQueryBO bo);

    /**
     * 新增字典项
     *
     * @param bo 参数
     * @return 字典项ID
     */
    String save(SysDictItemSaveBO bo);

    /**
     * 编辑字典项
     *
     * @param bo 参数
     */
    void update(SysDictItemSaveBO bo);

    /**
     * 查询字典项详情
     *
     * @param itemId 字典项ID
     * @return 字典项详情
     */
    SysDictItemVO detail(String itemId);

    /**
     * 删除字典项
     *
     * @param itemId 字典项ID
     */
    void delete(String itemId);

    /**
     * 编辑字典项状态
     *
     * @param itemId 字典项ID
     * @param status 字典项状态
     */
    void updateStatus(String itemId, Integer status);

    /**
     * 导入字典项
     *
     * @param dataList 数据列表
     * @return 导入结果
     */
    CommonImportVO importDictItem(List<Map<String, Object>> dataList);

}

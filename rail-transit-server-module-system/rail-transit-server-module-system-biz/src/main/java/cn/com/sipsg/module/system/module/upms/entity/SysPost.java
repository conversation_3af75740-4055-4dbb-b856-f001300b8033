package cn.com.sipsg.module.system.module.upms.entity;

import cn.com.sipsg.common.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.OrderBy;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;

/**
 * <p>
 * 岗位表
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@FieldNameConstants
@TableName(value = "medox_sys_post", autoResultMap = true)
public class SysPost extends BaseDO {

    /**
     * 岗位ID
     */
    @TableId
    private String postId;

    /**
     * 岗位名称
     */
    private String postName;

    /**
     * 岗位层级;数值越小级别越高
     */
    @OrderBy(asc = true, sort = Short.MIN_VALUE)
    private Integer postLevel;

    /**
     * 是否领导岗位
     */
    private Boolean leaderPost;

}

package cn.com.sipsg.module.system.module.upms.service.impl;

import cn.com.sipsg.common.exception.enums.ErrorCodeEnum;
import cn.com.sipsg.common.mybatis.core.query.LambdaQueryWrapperX;
import cn.com.sipsg.common.mybatis.core.query.MPJLambdaWrapperX;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.security.core.util.SecurityUtils;
import cn.com.sipsg.common.util.AssertUtils;
import cn.com.sipsg.common.util.BeanUtils;
import cn.com.sipsg.common.util.CollectionUtils;
import cn.com.sipsg.common.util.EnumUtils;
import cn.com.sipsg.module.system.module.upms.entity.SysRole;
import cn.com.sipsg.module.system.module.upms.entity.SysRoleMenu;
import cn.com.sipsg.module.system.module.upms.entity.SysUser;
import cn.com.sipsg.module.system.module.upms.entity.SysUserRole;
import cn.com.sipsg.module.system.module.upms.enums.DataScopeEnum;
import cn.com.sipsg.module.system.module.upms.mapper.*;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysRoleDataScopeUpdateBO;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysRoleQueryBO;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysRoleSaveBO;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysRoleUserQueryBO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysRoleVO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysUserSimpleVO;
import cn.com.sipsg.module.system.module.upms.service.SysRoleService;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 角色表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Service
@RequiredArgsConstructor
public class SysRoleServiceImpl extends ServiceImpl<SysRoleMapper, SysRole> implements SysRoleService {

    private final SysMenuMapper menuMapper;

    private final SysRoleMenuMapper roleMenuMapper;

    private final SysUserRoleMapper userRoleMapper;

    private final SysUserMapper userMapper;

    @Override
    public CommonPageVO<SysRoleVO> page(SysRoleQueryBO bo) {
        LambdaQueryWrapperX<SysRole> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper
                .likeIfPresent(SysRole::getRoleName, bo.getRoleName())
                .likeIfPresent(SysRole::getRoleCode, bo.getRoleCode());
        return this.page(bo, queryWrapper).convert(role -> BeanUtils.copyProperties(role, SysRoleVO.class));
    }

    @Override
    @Transactional
    public String save(SysRoleSaveBO bo) {
        bo.setRoleId(null);
        // 校验角色
        checkSaveOrUpdate(null, bo.getRoleCode(), bo.getMenuIds());
        // 保存角色
        SysRole role = BeanUtils.copyProperties(bo, SysRole.class);
        // 设置默认角色数据权限为全部权限
        role.setDataScope(DataScopeEnum.ALL);
        this.save(role);
        // 保存角色菜单关系
        saveRoleMenu(role.getRoleId(), bo.getMenuIds());
        return role.getRoleId();
    }

    @Override
    @Transactional
    public void update(SysRoleSaveBO bo) {
        // 校验角色
        checkSaveOrUpdate(bo.getRoleId(), bo.getRoleCode(), bo.getMenuIds());
        // 更新角色
        SysRole role = BeanUtils.copyProperties(bo, SysRole.class);
        this.updateById(role);
        // 删除原有角色菜单关系
        roleMenuMapper.deleteByRoleId(bo.getRoleId());
        // 保存角色菜单关系
        saveRoleMenu(bo.getRoleId(), bo.getMenuIds());
    }

    @Override
    @Transactional
    public void delete(String roleId) {
        SysRole role = this.getById(roleId);
        if (ObjectUtil.isNull(role)) {
            return;
        }
        // 超级管理员角色禁止删除
        AssertUtils.isTrue(SecurityUtils.isSuperAdmin(Collections.singleton(role.getRoleCode())), ErrorCodeEnum.FORBIDDEN_SUPER_ADMIN_ROLE);
        // 删除角色
        this.removeById(roleId);
        // 删除角色菜单关系
        roleMenuMapper.deleteByRoleId(roleId);
        // 删除角色用户关系
        userRoleMapper.deleteByRoleId(roleId);
    }

    @Override
    public SysRoleVO detail(String roleId) {
        SysRole role = getByIdDeep(roleId);
        // 校验是否存在
        AssertUtils.isTrue(Objects.isNull(role), ErrorCodeEnum.DATA_NOT_EXIST);
        return BeanUtils.copyProperties(role, SysRoleVO.class);
    }

    @Override
    public CommonPageVO<SysUserSimpleVO> getRoleUserPage(SysRoleUserQueryBO bo) {
        MPJLambdaWrapperX<SysUser> queryWrapper = new MPJLambdaWrapperX<>();
        queryWrapper
                .selectAll(SysUser.class)
                .innerJoin(SysUserRole.class, SysUserRole::getUserId, SysUser::getUserId)
                .eq(SysUserRole::getRoleId, bo.getRoleId())
                .likeIfExists(SysUser::getUsername, bo.getUsername())
                .likeIfExists(SysUser::getRealName, bo.getRealName());
        return userMapper.selectJoinPage(bo, SysUserSimpleVO.class, queryWrapper);
    }

    @Override
    public CommonPageVO<SysUserSimpleVO> getNotInRoleUserPage(SysRoleUserQueryBO bo) {
        Set<String> userIds = CollectionUtils.convertSet(userRoleMapper.selectByRoleId(bo.getRoleId()), SysUserRole::getUserId);
        LambdaQueryWrapperX<SysUser> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper
                .notInIfPresent(SysUser::getUserId, userIds)
                .likeIfPresent(SysUser::getUsername, bo.getUsername())
                .likeIfPresent(SysUser::getRealName, bo.getRealName());
        return userMapper.selectPage(bo, queryWrapper).convert(user -> BeanUtils.copyProperties(user, SysUserSimpleVO.class));
    }

    @Override
    public void saveRoleUser(String roleId, Set<String> userIds) {
        // 校验角色存在
        AssertUtils.isTrue(!this.existId(roleId), ErrorCodeEnum.DATA_NOT_EXIST);
        // 校验用户存在
        AssertUtils.isTrue(!userMapper.existAllPrimaryKeys(userIds), ErrorCodeEnum.INVALID_RELATED_RECORD_ID);
        // 保存角色用户关系
        for (String userId : userIds) {
            SysUserRole userRole = new SysUserRole();
            userRole.setRoleId(roleId);
            userRole.setUserId(userId);
            userRoleMapper.insert(userRole);
        }
    }

    @Override
    public void deleteRoleUser(String roleId, String userId) {
        // 超级管理员用户禁止删除
        AssertUtils.isTrue(SecurityUtils.isSuperAdmin(userId), ErrorCodeEnum.FORBIDDEN_SUPER_ADMIN_USER);
        LambdaQueryWrapperX<SysUserRole> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper
                .eq(SysUserRole::getRoleId, roleId)
                .eq(SysUserRole::getUserId, userId);
        userRoleMapper.delete(queryWrapper);
    }

    @Override
    public List<SysRole> getRoleListByUserId(String userId) {
        List<SysUserRole> userRoleList = userRoleMapper.selectByUserId(userId);
        if (CollUtil.isEmpty(userRoleList)) {
            return Collections.emptyList();
        }
        return baseMapper.selectBatchIds(CollectionUtils.convertSet(userRoleList, SysUserRole::getRoleId));
    }

    @Override
    public void updateRoleDataScope(SysRoleDataScopeUpdateBO bo) {
        // 校验角色存在
        SysRole role = checkExist(bo.getRoleId());
        // 超级管理员角色禁止编辑
        AssertUtils.isTrue(SecurityUtils.isSuperAdmin(Collections.singleton(role.getRoleCode())), ErrorCodeEnum.FORBIDDEN_SUPER_ADMIN_ROLE);
        // 更新数据权限
        SysRole updateRole = new SysRole();
        updateRole.setRoleId(bo.getRoleId());
        updateRole.setDataScope(EnumUtils.getEnumByValue(DataScopeEnum.class, bo.getDataScope()));
        updateRole.setDataScopeDeptIds(bo.getDataScopeDeptIds());
        this.updateById(updateRole);
    }

    private void checkSaveOrUpdate(String roleId, String roleCode, Set<String> menuIds) {
        // 校验角色存在
        SysRole role = checkExist(roleId);
        // 校验角色编码
        checkRoleCodeUnique(roleId, roleCode);
        // 超级管理员角色禁止编辑
        if (ObjectUtil.isNotNull(role)) {
            AssertUtils.isTrue(SecurityUtils.isSuperAdmin(Collections.singleton(role.getRoleCode())), ErrorCodeEnum.FORBIDDEN_SUPER_ADMIN_ROLE);
        }
        // 校验菜单ID
        AssertUtils.isTrue(!menuMapper.existAllPrimaryKeys(menuIds), ErrorCodeEnum.INVALID_RELATED_RECORD_ID);
    }

    private void saveRoleMenu(String roleId, Set<String> menuIds) {
        if (CollUtil.isNotEmpty(menuIds)) {
            // 保存角色菜单关系
            List<SysRoleMenu> roleMenuList = menuIds.stream().map(menuId -> {
                SysRoleMenu roleMenu = new SysRoleMenu();
                roleMenu.setRoleId(roleId);
                roleMenu.setMenuId(menuId);
                return roleMenu;
            }).collect(Collectors.toList());
            roleMenuMapper.insertBatch(roleMenuList);
        }
    }

    private void checkRoleCodeUnique(String roleId, String roleCode) {
        if (StrUtil.isBlank(roleCode)) {
            return;
        }
        long count = this.count(new LambdaQueryWrapperX<SysRole>().eq(SysRole::getRoleCode, roleCode).neIfPresent(SysRole::getRoleId, roleId));
        AssertUtils.isTrue(count > 0, String.format("角色编码【%s】已存在", roleCode));
    }

}
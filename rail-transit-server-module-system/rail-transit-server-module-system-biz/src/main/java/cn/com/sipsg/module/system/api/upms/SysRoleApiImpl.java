package cn.com.sipsg.module.system.api.upms;

import cn.com.sipsg.common.mybatis.core.query.LambdaQueryWrapperX;
import cn.com.sipsg.common.util.BeanUtils;
import cn.com.sipsg.module.system.api.upms.dto.SysRoleQueryReqDTO;
import cn.com.sipsg.module.system.api.upms.dto.SysRoleRespDTO;
import cn.com.sipsg.module.system.module.upms.entity.SysRole;
import cn.com.sipsg.module.system.module.upms.service.SysRoleService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 角色 API 实现类
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@RestController
@Validated
@RequiredArgsConstructor
public class SysRoleApiImpl implements SysRoleApi {

    private final SysRoleService roleService;

    @Override
    public List<SysRoleRespDTO> getRoleList() {
        return BeanUtils.copyToList(roleService.list(), SysRoleRespDTO.class);
    }

    @Override
    public List<SysRoleRespDTO> getRoleList(SysRoleQueryReqDTO reqDTO) {
        LambdaQueryWrapperX<SysRole> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.likeIfPresent(SysRole::getRoleName, reqDTO.getRoleName())
                .likeIfPresent(SysRole::getRoleCode, reqDTO.getRoleCode());
        return BeanUtils.copyToList(roleService.list(queryWrapper), SysRoleRespDTO.class);
    }

    @Override
    public SysRoleRespDTO getRoleById(String roleId) {
        return BeanUtils.copyProperties(roleService.getById(roleId), SysRoleRespDTO.class);
    }

}

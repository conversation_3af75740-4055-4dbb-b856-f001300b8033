package cn.com.sipsg.module.system.module.upms.controller;

import cn.com.sipsg.common.operationlog.core.annotation.OperationLog;
import cn.com.sipsg.common.operationlog.core.enums.OperationTypeEnum;
import cn.com.sipsg.common.pojo.CommonResult;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.validation.AddValidated;
import cn.com.sipsg.common.validation.UpdateValidated;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysPostQueryBO;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysPostSaveBO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysPostVO;
import cn.com.sipsg.module.system.module.upms.service.SysPostService;
import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 岗位控制器
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Tag(name = "岗位管理")
@RestController
@RequiredArgsConstructor
@RequestMapping("/system/upms/post")
public class SysPostController {

    private final SysPostService postService;

    /**
     * 查询岗位分页列表
     *
     * @param bo 参数
     * @return 岗位分页列表
     */
    @Operation(summary = "查询岗位分页列表")
    @OperationLog(module = "岗位管理", value = "查询岗位分页列表", type = OperationTypeEnum.QUERY, saveResponse = false)
    @SaCheckPermission(value = "system:upms:post:list")
    @PostMapping("/page")
    public CommonResult<CommonPageVO<SysPostVO>> page(@Validated @RequestBody SysPostQueryBO bo) {
        CommonPageVO<SysPostVO> page = postService.page(bo);
        return CommonResult.data(page);
    }

    /**
     * 新增岗位
     *
     * @param bo 参数
     * @return 岗位ID
     */
    @Operation(summary = "新增岗位")
    @OperationLog(module = "岗位管理", value = "新增岗位", type = OperationTypeEnum.SAVE)
    @SaCheckPermission(value = "system:upms:post:save")
    @PostMapping("/save")
    public CommonResult<String> save(@AddValidated @RequestBody SysPostSaveBO bo) {
        String postId = postService.save(bo);
        return CommonResult.data(postId);
    }

    /**
     * 编辑岗位
     *
     * @param bo 参数
     */
    @Operation(summary = "编辑岗位")
    @OperationLog(module = "岗位管理", value = "编辑岗位", type = OperationTypeEnum.UPDATE)
    @SaCheckPermission(value = "system:upms:post:update")
    @PostMapping("/update")
    public CommonResult<Void> update(@UpdateValidated @RequestBody SysPostSaveBO bo) {
        postService.update(bo);
        return CommonResult.success();
    }

    /**
     * 删除岗位
     *
     * @param postId 岗位ID
     */
    @Operation(summary = "删除岗位")
    @Parameter(name = "postId", description = "岗位ID", required = true)
    @OperationLog(module = "岗位管理", value = "删除岗位", type = OperationTypeEnum.DELETE)
    @SaCheckPermission(value = "system:upms:post:delete")
    @PostMapping("/delete")
    public CommonResult<Void> delete(@RequestParam String postId) {
        postService.delete(postId);
        return CommonResult.success();
    }

    /**
     * 查询岗位详情
     *
     * @param postId 岗位ID
     */
    @Operation(summary = "查询岗位详情")
    @Parameter(name = "postId", description = "岗位ID", required = true)
    @OperationLog(module = "岗位管理", value = "查询岗位详情", type = OperationTypeEnum.QUERY, saveResponse = false)
    @SaCheckPermission(value = "system:upms:post:detail")
    @GetMapping("/detail")
    public CommonResult<SysPostVO> detail(@RequestParam String postId) {
        SysPostVO postVO = postService.detail(postId);
        return CommonResult.data(postVO);
    }

}

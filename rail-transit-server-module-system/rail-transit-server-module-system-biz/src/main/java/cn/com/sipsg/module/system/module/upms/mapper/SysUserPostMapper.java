package cn.com.sipsg.module.system.module.upms.mapper;

import cn.com.sipsg.common.mybatis.core.mapper.BaseMapperX;
import cn.com.sipsg.common.mybatis.core.query.LambdaQueryWrapperX;
import cn.com.sipsg.module.system.module.upms.entity.SysUserPost;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.dynamic.datasource.annotation.Master;
import org.apache.ibatis.annotations.Mapper;

import java.util.Set;

/**
 * <p>
 * 用户岗位关系表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Master
@Mapper
public interface SysUserPostMapper extends BaseMapperX<SysUserPost> {

    default void deleteByUserId(String userId) {
        delete(SysUserPost::getUserId, userId);
    }

    default void deleteByPostId(String postId) {
        delete(SysUserPost::getPostId, postId);
    }

    default void deleteByDeptPostId(String deptPostId) {
        delete(SysUserPost::getDeptPostId, deptPostId);
    }

    default void deleteByDeptPostIds(Set<String> deptPostIds) {
        if (CollUtil.isEmpty(deptPostIds)) {
            return;
        }
        delete(new LambdaQueryWrapperX<SysUserPost>().in(SysUserPost::getDeptPostId, deptPostIds));
    }

}

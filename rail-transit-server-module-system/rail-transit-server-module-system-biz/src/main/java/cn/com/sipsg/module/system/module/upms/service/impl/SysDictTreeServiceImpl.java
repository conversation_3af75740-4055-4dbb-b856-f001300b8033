package cn.com.sipsg.module.system.module.upms.service.impl;

import cn.com.sipsg.common.constant.CommonConstants;
import cn.com.sipsg.common.enums.CommonStatusEnum;
import cn.com.sipsg.common.exception.enums.ErrorCodeEnum;
import cn.com.sipsg.common.mybatis.core.query.LambdaQueryWrapperX;
import cn.com.sipsg.common.pojo.vo.CommonImportVO;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.util.*;
import cn.com.sipsg.common.web.core.util.ExcelUtils;
import cn.com.sipsg.module.system.api.upms.dto.SysDictTreeRespDTO;
import cn.com.sipsg.module.system.module.upms.convert.SysDictTreeConvert;
import cn.com.sipsg.module.system.module.upms.entity.SysDictTree;
import cn.com.sipsg.module.system.module.upms.mapper.SysDictTreeMapper;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysDictTreeBatchUpdateBO;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysDictTreeDragUpdateBO;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysDictTreeQueryBO;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysDictTreeSaveBO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysDictTreeVO;
import cn.com.sipsg.module.system.module.upms.service.SysDictTreeService;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DatePattern.PURE_DATETIME_PATTERN;


/**
 * 字典树管理服务实现类
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Service
@RequiredArgsConstructor
public class SysDictTreeServiceImpl extends ServiceImpl<SysDictTreeMapper, SysDictTree> implements SysDictTreeService {

    private final HttpServletResponse response;

    @Override
    public CommonPageVO<SysDictTreeVO> page(SysDictTreeQueryBO bo) {
        LambdaQueryWrapperX<SysDictTree> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eq(SysDictTree::getParentId, bo.getParentId())
            .likeIfPresent(SysDictTree::getName, bo.getName())
            .likeIfPresent(SysDictTree::getCode, bo.getCode())
            .eqIfPresent(SysDictTree::getParentId, bo.getParentId())
            .orderByAsc(SysDictTree::getSort)
            .orderByDesc(SysDictTree::getCreateTime);
        // 填充查询条件
        return this.page(bo, queryWrapper).convert(entity -> BeanUtils.copyProperties(entity, SysDictTreeVO.class));
    }

    @Override
    @Transactional
    public String save(SysDictTreeSaveBO bo) {
        bo.setId(null);
        // 新增字典树管理
        SysDictTree sysDictTree = SysDictTreeConvert.INSTANCE.convert(bo);
        if (CommonConstants.PARENT_ID_ROOT.equals(bo.getParentId())) {
            sysDictTree.setTreeId(IdUtil.fastSimpleUUID());
        } else {
            SysDictTree parentDict = this.getById(bo.getParentId());
            AssertUtils.isTrue(parentDict == null, "父字典不存在");
            sysDictTree.setTreeId(parentDict.getTreeId());
        }
        createOrUpdateCheck(null, sysDictTree.getCode(), sysDictTree.getTreeId());
        if (bo.getSort() == null) {
            sysDictTree.setSort(Optional.ofNullable(this.getMaxSortByParentId(sysDictTree.getParentId())).orElse(0) + 1);
        }
        this.save(sysDictTree);
        return sysDictTree.getId();
    }

    private void createOrUpdateCheck(String id, String code, String treeId) {
        if (StrUtil.isNotBlank(code)) {
            LambdaQueryWrapperX<SysDictTree> queryWrapperX = new LambdaQueryWrapperX<>();
            queryWrapperX.eq(SysDictTree::getCode, code).neIfPresent(SysDictTree::getId, id)
                .eqIfPresent(SysDictTree::getTreeId, treeId);
            AssertUtils.isTrue(this.count(queryWrapperX) > 0, code + "字典编码已存在");
        }
    }

    @Override
    @Transactional
    public void update(SysDictTreeSaveBO bo) {
        // 编辑字典树管理
        SysDictTree sysDictTree = SysDictTreeConvert.INSTANCE.convert(bo);
        createOrUpdateCheck(sysDictTree.getId(), sysDictTree.getCode(), bo.getTreeId());
        this.updateById(sysDictTree);
    }

    @Override
    public SysDictTreeVO detail(String id) {
        SysDictTree sysDictTree = this.getById(id);
        // 校验是否存在
        AssertUtils.isTrue(sysDictTree == null, ErrorCodeEnum.DATA_NOT_EXIST);
        return BeanUtils.copyProperties(sysDictTree, SysDictTreeVO.class);
    }

    @Override
    @Transactional
    public void delete(String id) {
        SysDictTree sysDictTree = this.getById(id);
        if (sysDictTree != null) {
            // 删除字典树管理
            this.removeById(id);
        }
    }

    @Override
    public List<SysDictTreeVO> listTree() {
        List<SysDictTreeVO> treeVOList = this.lambdaQuery()
            .orderByAsc(SysDictTree::getSort)
            .list().stream()
            .map(SysDictTreeConvert.INSTANCE::convert)
            .collect(Collectors.toList());
        return TreeUtils.build(treeVOList, CommonConstants.PARENT_ID_ROOT);
    }

    @Override
    public List<Tree<String>> listTreeByDictTreeId(String dictTreeId) {
        return genTree(this.getById(dictTreeId));
    }

    @Override
    public List<Tree<String>> listTreeByDictTreeCode(String dictTreeCode) {
        SysDictTree dictTree = this.getOne(new LambdaQueryWrapperX<SysDictTree>().eq(SysDictTree::getCode, dictTreeCode).eq(SysDictTree::getParentId, CommonConstants.PARENT_ID_ROOT), false);
        return genTree(dictTree);
    }

    @Override
    public List<SysDictTreeRespDTO> getDictTreeByDictTreeCode(String dictTreeCode) {
        SysDictTree dictTree = this.getOne(new LambdaQueryWrapperX<SysDictTree>().eq(SysDictTree::getCode, dictTreeCode).eq(SysDictTree::getParentId, CommonConstants.PARENT_ID_ROOT), false);
        return genTreeResp(dictTree);
    }

    @Override
    @Transactional
    public Boolean dragUpdate(SysDictTreeDragUpdateBO bo) {
        String parentId = bo.getParentId();
        String treeId;
        if (CommonConstants.PARENT_ID_ROOT.equals(parentId)) {
            treeId = IdUtil.fastSimpleUUID();
        } else {
            SysDictTree parentDictTree = this.getById(parentId);
            AssertUtils.isTrue(parentDictTree == null, "父字典树不存在");
            treeId = parentDictTree.getTreeId();
        }

        // 更新字典树信息
        SysDictTree dictTree = new SysDictTree();
        dictTree.setId(bo.getDictTreeId());
        dictTree.setParentId(parentId);
        dictTree.setTreeId(treeId);
        this.updateById(dictTree);

        // 更新字典树下字典信息
        this.updateDescendantTreeId(bo.getDictTreeId(), treeId);

        // 更新排序
        List<String> dictTreeIds = bo.getDictTreeIds();
        List<SysDictTree> dictTreeList = new ArrayList<>(dictTreeIds.size());
        int sort = 1;
        for (String id : dictTreeIds) {
            dictTree = new SysDictTree();
            dictTree.setSort(sort);
            dictTree.setId(id);
            dictTreeList.add(dictTree);
            sort++;
        }
        this.updateBatchById(dictTreeList);
        return Boolean.TRUE;
    }

    @Override
    public void export() {
        List<SysDictTree> dictTreeList = this.lambdaQuery().orderByAsc(SysDictTree::getSort).list();
        List<List<Object>> rows = new ArrayList<>(dictTreeList.size());
        if (CollUtil.isNotEmpty(dictTreeList)) {
            Map<String, SysDictTree> dictTreeMap = CollectionUtils.convertMap(dictTreeList, SysDictTree::getId);
            List<SysDictTreeVO> dictTreeVOList = TreeUtils.build(dictTreeList.stream().map(SysDictTreeConvert.INSTANCE::convert).collect(Collectors.toList()), CommonConstants.PARENT_ID_ROOT);
            recursionDictTree(dictTreeVOList, dictTreeMap, rows);
        }
        List<Object> headerRow = CollUtil
            .newArrayList("字典名称", "字典编码", "是否启用", "上级字典编码", "备注");
        ExcelWriter writer = ExcelUtil.getWriter(true);
        writer.writeHeadRow(headerRow);
        writer.write(rows);
        writer.renameSheet("字典树");
        ExcelUtils.export(response, writer, "字典树导出_" + DateUtil.format(LocalDateTime.now(), PURE_DATETIME_PATTERN));
    }

    @Override
    public CommonImportVO importInfo(MultipartFile file) {
        List<Map<String, Object>> dataList = ExcelUtils.readAll(file);
        AssertUtils.isTrue(CollUtil.isEmpty(dataList), ErrorCodeEnum.IMPORT_FILE_ERROR);
        return importInfo(dataList);
    }

    @Override
    @Transactional
    public Boolean updateBatch(SysDictTreeBatchUpdateBO bo) {
        String id = bo.getId();
        SysDictTree parentDict = this.getById(id);
        AssertUtils.isTrue(parentDict == null, "字典组不存在");
        List<SysDictTreeBatchUpdateBO.DictTreeItemUpdateBO> boList = bo.getDictBos();
        Map<String, SysDictTree> existDictMap = this.list(new LambdaQueryWrapperX<SysDictTree>()
                .eq(SysDictTree::getParentId, id))
            .stream()
            .collect(Collectors.toMap(SysDictTree::getId, Function.identity()));
        List<SysDictTree> saveOrUpdateDictList = new ArrayList<>();
        for (SysDictTreeBatchUpdateBO.DictTreeItemUpdateBO dictTreeItemUpdateBO : boList) {
            SysDictTree dict = SysDictTreeConvert.INSTANCE.convert(dictTreeItemUpdateBO);
            String dictItemId = dictTreeItemUpdateBO.getId();
            if (StrUtil.isNotEmpty(dictItemId)) {
                SysDictTree existDict = existDictMap.remove(dictItemId);
                if (existDict == null) {
                    continue;
                }
            } else {
                dict.setTreeId(parentDict.getTreeId());
            }
            createOrUpdateCheck(dictItemId, dict.getCode(), parentDict.getTreeId());
            saveOrUpdateDictList.add(dict);
        }
        if (CollUtil.isNotEmpty(saveOrUpdateDictList)) {
            this.saveOrUpdateBatch(saveOrUpdateDictList);
        }
        if (CollUtil.isNotEmpty(existDictMap)) {
            for (SysDictTree dict : existDictMap.values()) {
                this.remove(dict.getId(), dict.getTreeId());
            }
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean hasDescendant(String id) {
        SysDictTree dict = this.getById(id);
        if (dict != null) {
            return this.count(new LambdaQueryWrapperX<SysDictTree>().eq(SysDictTree::getParentId, id).eq(SysDictTree::getTreeId, dict.getTreeId())) > 0;
        }
        return Boolean.FALSE;
    }

    private void remove(String id, String treeId) {
        this.removeById(id);
        this.remove(new LambdaQueryWrapperX<SysDictTree>().eq(SysDictTree::getParentId, id).eq(SysDictTree::getTreeId, treeId));
    }

    private CommonImportVO importInfo(List<Map<String, Object>> dataList) {
        List<Map<String, Object>> errorRecords = new ArrayList<>();
        long successTotal = 0;
        long errorTotal = 0;
        if (CollUtil.isNotEmpty(dataList)) {
            for (Map<String, Object> dataMap : dataList) {
                String name = StrUtil.blankToDefault(MapUtil.getStr(dataMap, "字典名称(必填)"), MapUtil.getStr(dataMap, "字典名称"));
                String code = StrUtil.blankToDefault(MapUtil.getStr(dataMap, "字典编码(必填)"), MapUtil.getStr(dataMap, "字典编码"));
                String enabled = StrUtil.blankToDefault(MapUtil.getStr(dataMap, "是否启用(必选)"), MapUtil.getStr(dataMap, "是否启用"));
                String parentCode = MapUtil.getStr(dataMap, "上级字典编码");
                String remark = MapUtil.getStr(dataMap, "备注");
                StringBuilder sb = new StringBuilder();
                if (StrUtil.isBlank(name)) {
                    sb.append("字典名称不能为空;");
                }
                if (StrUtil.isBlank(code)) {
                    sb.append("字典编码不能为空;");
                } else {
                    long count = this.count(new LambdaQueryWrapper<SysDictTree>().eq(SysDictTree::getCode, code));
                    if (count > 0) {
                        sb.append("字典编码已被占用;");
                    }
                }
                CommonStatusEnum enumByDesc = null;
                if (StrUtil.isBlank(enabled)) {
                    sb.append("是否启用不能为空;");
                } else {
                    enumByDesc = EnumUtils.getEnumByDesc(CommonStatusEnum.class, enabled);
                    if (enumByDesc == null) {
                        sb.append("是否启用格式不正确;");
                    }
                }
                SysDictTree parentDictTree = null;
                if (StrUtil.isNotBlank(parentCode)) {
                    parentDictTree = this.getOne(new LambdaQueryWrapper<SysDictTree>().eq(SysDictTree::getCode, parentCode), false);
                    if (parentDictTree == null) {
                        sb.append("上级字典不存在;");
                    }
                }
                if (StrUtil.isNotBlank(sb)) {
                    errorTotal++;
                    dataMap.put("错误提示", sb);
                    errorRecords.add(dataMap);
                    continue;
                }
                // 保存字典树
                SysDictTree dictTree = new SysDictTree();
                dictTree.setName(name);
                dictTree.setCode(code);
                dictTree.setRemark(remark);
                if (parentDictTree != null) {
                    dictTree.setParentId(parentDictTree.getId());
                    dictTree.setTreeId(parentDictTree.getTreeId());
                } else {
                    dictTree.setParentId(CommonConstants.PARENT_ID_ROOT);
                    dictTree.setTreeId(IdUtil.fastSimpleUUID());
                }
                dictTree.setSort(Optional.ofNullable(this.getMaxSortByParentId(dictTree.getParentId())).orElse(0) + 1);
                dictTree.setStatus(enumByDesc);
                this.save(dictTree);
                successTotal++;
            }
        }
        return CommonImportVO.builder().successTotal(successTotal).errorTotal(errorTotal).errorRecords(errorRecords).build();
    }

    private Integer getMaxSortByParentId(String parentId) {
        SysDictTree sysDictTree = baseMapper.selectOne(new LambdaQueryWrapper<SysDictTree>().eq(SysDictTree::getParentId, parentId).orderByDesc(SysDictTree::getSort).last("limit 1"), false);
        return sysDictTree == null ? 0 : sysDictTree.getSort();
    }

    private void recursionDictTree(List<SysDictTreeVO> dictTreeVOList, Map<String, SysDictTree> dictTreeMap, List<List<Object>> rows) {
        if (CollUtil.isEmpty(dictTreeVOList)) {
            return;
        }
        for (SysDictTreeVO dictTreeVO : dictTreeVOList) {
            List<Object> data = new ArrayList<>();
            data.add(dictTreeVO.getName());
            data.add(dictTreeVO.getCode());
            data.add(CommonStatusEnum.ENABLE.getCode().equals(dictTreeVO.getStatus().getCode()) ? "是" : "否");
            data.add(dictTreeVO.getParentId().equals(CommonConstants.PARENT_ID_ROOT) ? StrUtil.EMPTY : dictTreeMap.get(dictTreeVO.getParentId()).getCode());
            data.add(dictTreeVO.getRemark());
            rows.add(data);
            recursionDictTree(dictTreeVO.getChildren(), dictTreeMap, rows);
        }

    }

    private void updateDescendantTreeId(String dictTreeId, String treeId) {
        List<SysDictTree> list = this.lambdaQuery().eq(SysDictTree::getParentId, dictTreeId).list();
        if (CollUtil.isNotEmpty(list)) {
            for (SysDictTree dictTree : list) {
                SysDictTree descendantDictTree = new SysDictTree();
                descendantDictTree.setTreeId(treeId);
                descendantDictTree.setId(dictTree.getId());
                this.updateById(descendantDictTree);
                updateDescendantTreeId(dictTree.getId(), treeId);
            }
        }
    }

    private List<Tree<String>> genTree(SysDictTree sysDictTree) {
        if (sysDictTree == null) {
            return Collections.emptyList();
        }
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        treeNodeConfig.setWeightKey("sort");
        List<SysDictTree> dictTreeList = this.list(new LambdaQueryWrapperX<SysDictTree>().eq(SysDictTree::getTreeId, sysDictTree.getTreeId()));
        return TreeUtil.build(dictTreeList, sysDictTree.getParentId(), treeNodeConfig, (treeNode, tree) -> {
            tree.setId(treeNode.getId());
            tree.setParentId(treeNode.getParentId());
            tree.setName(treeNode.getName());
            tree.putExtra("code", treeNode.getCode());
            tree.putExtra("sort", treeNode.getSort());
            tree.putExtra("status", treeNode.getStatus().getCode());
        });
    }

    private List<SysDictTreeRespDTO> genTreeResp(SysDictTree dictTree) {
        if (dictTree == null) {
            return Collections.emptyList();
        }
        List<SysDictTree> dictTreeList = this.list(new LambdaQueryWrapperX<SysDictTree>().eq(SysDictTree::getTreeId, dictTree.getTreeId()));
        List<SysDictTreeRespDTO> sysDictTreeRespDTOS = BeanUtils.copyToList(dictTreeList, SysDictTreeRespDTO.class);
        return TreeUtils.build(sysDictTreeRespDTOS, dictTree.getParentId());
    }

}
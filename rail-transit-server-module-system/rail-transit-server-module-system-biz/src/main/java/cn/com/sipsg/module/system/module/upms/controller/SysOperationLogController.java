package cn.com.sipsg.module.system.module.upms.controller;

import cn.com.sipsg.common.pojo.CommonResult;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysOperationLogQueryBO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysOperationLogVO;
import cn.com.sipsg.module.system.module.upms.service.SysOperationLogService;
import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 操作日志前端控制器
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Tag(name = "操作日志管理")
@RestController
@RequiredArgsConstructor
@RequestMapping("/system/upms/operationLog")
public class SysOperationLogController {

    private final SysOperationLogService operationLogService;

    /**
     * 查询操作日志分页列表
     *
     * @param bo 参数
     * @return 操作日志分页列表
     */
    @Operation(summary = "查询操作日志分页列表")
    @SaCheckPermission(value = "system:upms:operationLog:list")
    @PostMapping("/page")
    public CommonResult<CommonPageVO<SysOperationLogVO>> page(@RequestBody SysOperationLogQueryBO bo) {
        return CommonResult.data(operationLogService.page(bo));
    }

}

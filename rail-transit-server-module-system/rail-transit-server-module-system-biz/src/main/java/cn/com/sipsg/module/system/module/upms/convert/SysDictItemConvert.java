package cn.com.sipsg.module.system.module.upms.convert;

import cn.com.sipsg.common.enums.CommonStatusEnum;
import cn.com.sipsg.common.util.EnumUtils;
import cn.com.sipsg.module.system.module.upms.entity.SysDictItem;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysDictItemSaveBO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysDictItemVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 字典项转换类
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Mapper
public interface SysDictItemConvert {

    SysDictItemConvert INSTANCE = Mappers.getMapper(SysDictItemConvert.class);

    SysDictItem convert(SysDictItemSaveBO bo);

    SysDictItemVO convert(SysDictItem sysDictItem);

    default CommonStatusEnum status2Enum(Integer status) {
        return EnumUtils.getEnumByValue(CommonStatusEnum.class, status);
    }

}

package cn.com.sipsg.module.system.module.upms.controller;

import cn.com.sipsg.common.operationlog.core.annotation.OperationLog;
import cn.com.sipsg.common.operationlog.core.enums.OperationTypeEnum;
import cn.com.sipsg.common.pojo.CommonResult;
import cn.com.sipsg.common.util.BeanUtils;
import cn.com.sipsg.common.validation.AddValidated;
import cn.com.sipsg.common.validation.UpdateValidated;
import cn.com.sipsg.module.system.module.upms.entity.SysPermModule;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysPermModuleSaveBO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysPermModulePermVO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysPermModuleVO;
import cn.com.sipsg.module.system.module.upms.service.SysPermModuleService;
import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 权限模块控制器
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Tag(name = "权限模块管理")
@RestController
@RequiredArgsConstructor
@RequestMapping("/system/upms/permModule")
public class SysPermModuleController {

    private final SysPermModuleService permModuleService;

    /**
     * 查询权限模块列表
     *
     * @return 权限模块列表
     */
    @Operation(summary = "查询权限模块列表")
    @OperationLog(module = "权限管理", value = "查询权限模块列表", type = OperationTypeEnum.QUERY, saveResponse = false)
    @SaCheckPermission(value = "system:upms:permModule:list")
    @PostMapping("/list")
    public CommonResult<List<SysPermModuleVO>> list() {
        List<SysPermModule> list = permModuleService.list();
        return CommonResult.data(BeanUtils.copyToList(list, SysPermModuleVO.class));
    }

    /**
     * 查询权限模块列表及其关联的权限列表
     *
     * @return 权限模块列表
     */
    @Operation(summary = "查询权限模块列表及其关联的权限列表")
    @SaCheckPermission(value = "system:upms:permModule:listWithPerm")
    @PostMapping("/listWithPerm")
    public CommonResult<List<SysPermModulePermVO>> listWithPerm() {
        return CommonResult.data(permModuleService.listWithPerm());
    }

    /**
     * 新增权限模块
     *
     * @param bo 参数
     * @return 权限模块ID
     */
    @Operation(summary = "新增权限模块")
    @OperationLog(module = "权限管理", value = "新增权限模块", type = OperationTypeEnum.SAVE)
    @SaCheckPermission(value = "system:upms:permModule:save")
    @PostMapping("/save")
    public CommonResult<String> save(@AddValidated @RequestBody SysPermModuleSaveBO bo) {
        return CommonResult.data(permModuleService.save(bo));
    }

    /**
     * 编辑权限模块
     *
     * @param bo 参数
     */
    @Operation(summary = "编辑权限模块")
    @OperationLog(module = "权限管理", value = "编辑权限模块", type = OperationTypeEnum.UPDATE)
    @SaCheckPermission(value = "system:upms:permModule:update")
    @PostMapping("/update")
    public CommonResult<Void> update(@UpdateValidated @RequestBody SysPermModuleSaveBO bo) {
        permModuleService.update(bo);
        return CommonResult.success();
    }

    /**
     * 删除权限模块
     *
     * @param permModuleId 权限模块ID
     */
    @Operation(summary = "删除权限模块")
    @Parameter(name = "permModuleId", description = "权限模块ID", required = true)
    @OperationLog(module = "权限管理", value = "删除权限模块", type = OperationTypeEnum.DELETE)
    @SaCheckPermission(value = "system:upms:permModule:delete")
    @PostMapping("/delete")
    public CommonResult<Void> delete(@RequestParam String permModuleId) {
        permModuleService.delete(permModuleId);
        return CommonResult.success();
    }

    /**
     * 查询权限模块详情
     *
     * @param permModuleId 权限模块ID
     * @return 权限模块详情
     */
    @Operation(summary = "查询权限模块详情")
    @Parameter(name = "permModuleId", description = "权限模块ID", required = true)
    @OperationLog(module = "权限管理", value = "查询权限模块详情", type = OperationTypeEnum.QUERY, saveResponse = false)
    @SaCheckPermission(value = "system:upms:permModule:detail")
    @GetMapping("/detail")
    public CommonResult<SysPermModuleVO> detail(@RequestParam String permModuleId) {
        return CommonResult.data(permModuleService.detail(permModuleId));
    }

}

package cn.com.sipsg.module.system.module.upms.controller;

import cn.com.sipsg.common.operationlog.core.annotation.OperationLog;
import cn.com.sipsg.common.operationlog.core.enums.OperationTypeEnum;
import cn.com.sipsg.common.pojo.CommonResult;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.validation.AddValidated;
import cn.com.sipsg.common.validation.UpdateValidated;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysPermQueryBO;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysPermSaveBO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysPermVO;
import cn.com.sipsg.module.system.module.upms.service.SysPermService;
import cn.dev33.satoken.annotation.SaCheckPermission;
import com.fhs.core.trans.anno.IgnoreTrans;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 权限控制器
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Tag(name = "权限管理")
@RestController
@RequiredArgsConstructor
@RequestMapping("/system/upms/perm")
public class SysPermController {

    private final SysPermService permService;

    /**
     * 查询权限分页列表
     *
     * @param bo 参数
     * @return 权限分页列表
     */
    @Operation(summary = "查询权限分页列表")
    @OperationLog(module = "权限管理", value = "查询权限分页列表", type = OperationTypeEnum.QUERY, saveResponse = false)
    @SaCheckPermission(value = "system:upms:perm:list")
    @PostMapping("/page")
    public CommonResult<CommonPageVO<SysPermVO>> page(@Validated @RequestBody SysPermQueryBO bo) {
        return CommonResult.data(permService.page(bo));
    }

    /**
     * 新增权限
     *
     * @param bo 参数
     * @return 权限ID
     */
    @Operation(summary = "新增权限")
    @OperationLog(module = "权限管理", value = "新增权限", type = OperationTypeEnum.SAVE)
    @SaCheckPermission(value = "system:upms:perm:save")
    @PostMapping("/save")
    public CommonResult<String> save(@AddValidated @RequestBody SysPermSaveBO bo) {
        return CommonResult.data(permService.save(bo));
    }

    /**
     * 编辑权限
     *
     * @param bo 参数
     */
    @Operation(summary = "编辑权限")
    @OperationLog(module = "权限管理", value = "编辑权限", type = OperationTypeEnum.UPDATE)
    @SaCheckPermission(value = "system:upms:perm:update")
    @PostMapping("/update")
    public CommonResult<Void> update(@UpdateValidated @RequestBody SysPermSaveBO bo) {
        permService.update(bo);
        return CommonResult.success();
    }

    /**
     * 删除权限
     *
     * @param permId 权限ID
     */
    @Operation(summary = "删除权限")
    @Parameter(name = "permId", description = "权限ID", required = true)
    @OperationLog(module = "权限管理", value = "删除权限", type = OperationTypeEnum.DELETE)
    @SaCheckPermission(value = "system:upms:perm:delete")
    @PostMapping("/delete")
    public CommonResult<Void> delete(@RequestParam String permId) {
        permService.delete(permId);
        return CommonResult.success();
    }

    /**
     * 查询权限详情
     *
     * @param permId 权限ID
     * @return 权限详情
     */
    @IgnoreTrans
    @Operation(summary = "查询权限详情")
    @Parameter(name = "permId", description = "权限ID", required = true)
    @OperationLog(module = "权限管理", value = "查询权限详情", type = OperationTypeEnum.QUERY, saveResponse = false)
    @SaCheckPermission(value = "system:upms:perm:detail")
    @GetMapping("/detail")
    public CommonResult<SysPermVO> detail(@RequestParam String permId) {
        return CommonResult.data(permService.detail(permId));
    }

}

package cn.com.sipsg.module.system.module.upms.service.impl;

import cn.com.sipsg.common.exception.enums.ErrorCodeEnum;
import cn.com.sipsg.common.mybatis.core.query.LambdaQueryWrapperX;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.util.AssertUtils;
import cn.com.sipsg.common.util.BeanUtils;
import cn.com.sipsg.module.system.module.upms.entity.SysPost;
import cn.com.sipsg.module.system.module.upms.mapper.SysDeptPostMapper;
import cn.com.sipsg.module.system.module.upms.mapper.SysPostMapper;
import cn.com.sipsg.module.system.module.upms.mapper.SysUserPostMapper;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysPostQueryBO;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysPostSaveBO;
import cn.com.sipsg.module.system.module.upms.pojo.vo.SysPostVO;
import cn.com.sipsg.module.system.module.upms.service.SysPostService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 岗位表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/14
*/
@Service
@RequiredArgsConstructor
public class SysPostServiceImpl extends ServiceImpl<SysPostMapper, SysPost> implements SysPostService {

    private final SysDeptPostMapper deptPostMapper;

    private final SysUserPostMapper userPostMapper;

    @Override
    public CommonPageVO<SysPostVO> page(SysPostQueryBO bo) {
        LambdaQueryWrapperX<SysPost> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.likeIfPresent(SysPost::getPostName, bo.getPostName());
        return this.page(bo, queryWrapper).convert(sysPost -> BeanUtils.copyProperties(sysPost, SysPostVO.class));
    }

    @Override
    public String save(SysPostSaveBO bo) {
        bo.setPostId(null);
        SysPost post = BeanUtils.copyProperties(bo, SysPost.class);
        // 保存岗位
        this.save(post);
        return post.getPostId();
    }

    @Override
    public void update(SysPostSaveBO bo) {
        AssertUtils.isTrue(!existId(bo.getPostId()), ErrorCodeEnum.DATA_NOT_EXIST);
        SysPost post = BeanUtils.copyProperties(bo, SysPost.class);
        // 更新岗位
        this.updateById(post);
    }

    @Override
    @Transactional
    public void delete(String postId) {
        // 删除岗位
        this.removeById(postId);
        // 删除部门岗位关系
        deptPostMapper.deleteByPostId(postId);
        // 删除用户岗位关系
        userPostMapper.deleteByPostId(postId);
    }

    @Override
    public SysPostVO detail(String postId) {
        // 校验是否存在
        SysPost post = checkExist(postId);
        return BeanUtils.copyProperties(post, SysPostVO.class);
    }

}
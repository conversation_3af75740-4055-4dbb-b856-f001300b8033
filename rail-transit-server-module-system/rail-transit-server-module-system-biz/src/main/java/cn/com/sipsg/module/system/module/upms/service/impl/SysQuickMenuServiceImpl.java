package cn.com.sipsg.module.system.module.upms.service.impl;

import cn.com.sipsg.common.constant.CommonConstants;
import cn.com.sipsg.common.exception.enums.ErrorCodeEnum;
import cn.com.sipsg.common.mybatis.core.query.LambdaQueryWrapperX;
import cn.com.sipsg.common.security.core.util.SecurityUtils;
import cn.com.sipsg.common.util.AssertUtils;
import cn.com.sipsg.common.util.BeanUtils;
import cn.com.sipsg.module.system.module.upms.entity.SysMenu;
import cn.com.sipsg.module.system.module.upms.entity.SysQuickMenu;
import cn.com.sipsg.module.system.module.upms.enums.MenuTypeEnum;
import cn.com.sipsg.module.system.module.upms.mapper.SysMenuMapper;
import cn.com.sipsg.module.system.module.upms.mapper.SysQuickMenuMapper;
import cn.com.sipsg.module.system.module.upms.pojo.bo.SysQuickMenuSaveBO;
import cn.com.sipsg.module.system.module.upms.service.SysQuickMenuService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 快捷菜单服务实现类
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Service
@RequiredArgsConstructor
public class SysQuickMenuServiceImpl extends ServiceImpl<SysQuickMenuMapper, SysQuickMenu> implements SysQuickMenuService {

    private final SysMenuMapper menuMapper;

    @Override
    @Transactional
    public void save(SysQuickMenuSaveBO bo) {
        SysMenu menu = menuMapper.selectById(bo.getMenuId());
        AssertUtils.isTrue(menu == null, ErrorCodeEnum.INVALID_RELATED_RECORD_ID);
        AssertUtils.isTrue(menu.getMenuType() != MenuTypeEnum.MENU, "快捷菜单只能关联菜单");
        String loginUserId = SecurityUtils.getLoginUserId();
        LambdaQueryWrapperX<SysQuickMenu> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eq(SysQuickMenu::getUserId, loginUserId)
                .eq(SysQuickMenu::getMenuId, bo.getMenuId());
        AssertUtils.isTrue(this.count(queryWrapper) > 0, "快捷菜单已存在");
        // 新增快捷菜单
        SysQuickMenu quickMenu = BeanUtils.copyProperties(bo, SysQuickMenu.class);
        quickMenu.setUserId(loginUserId);
        this.save(quickMenu);
    }

    @Override
    @Transactional
    public void delete(String menuId) {
        LambdaQueryWrapperX<SysQuickMenu> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eq(SysQuickMenu::getUserId, SecurityUtils.getLoginUserId())
                .eq(SysQuickMenu::getMenuId, menuId);
        this.remove(queryWrapper);
    }

    @Override
    @Transactional
    public void dragSort(List<String> menuIds) {
        String loginUserId = SecurityUtils.getLoginUserId();
        // 删除原快捷菜单
        baseMapper.delete(SysQuickMenu::getUserId, loginUserId);
        // 添加新快捷菜单
        List<SysQuickMenu> quickMenuList = new ArrayList<>(menuIds.size());
        int sort = CommonConstants.ZERO;
        for (String menuId : menuIds) {
            // 校验菜单是否存在
            SysMenu menu = menuMapper.selectById(menuId);
            if (menu == null) {
                continue;
            }
            SysQuickMenu quickMenu = new SysQuickMenu();
            quickMenu.setUserId(loginUserId);
            quickMenu.setMenuId(menuId);
            quickMenu.setSort(sort++);
            quickMenuList.add(quickMenu);
        }
        this.saveBatch(quickMenuList);
    }

}
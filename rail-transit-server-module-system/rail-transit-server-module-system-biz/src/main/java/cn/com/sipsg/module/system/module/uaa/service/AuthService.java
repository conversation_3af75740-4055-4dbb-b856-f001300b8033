package cn.com.sipsg.module.system.module.uaa.service;

import cn.com.sipsg.module.system.module.uaa.pojo.bo.RefreshLoginBO;
import cn.com.sipsg.module.system.module.uaa.pojo.vo.LoginInfoVO;
import cn.com.sipsg.module.system.module.uaa.pojo.vo.LoginVO;
import cn.com.sipsg.module.system.module.upms.entity.SysUser;

/**
 * 认证 服务类
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public interface AuthService {

    /**
     * 查询登录信息
     *
     * @return 登录信息
     */
    LoginInfoVO getLoginInfo();

    /**
     * 刷新登录
     *
     * @param bo 参数
     * @return 登录结果
     */
    LoginVO refreshLogin(RefreshLoginBO bo);

    /**
     * 生成登录结果
     *
     * @param user    用户
     * @param appCode 应用标识
     * @return 登录结果
     */
    LoginVO buildLoginVO(SysUser user, String appCode);

}

--- #################### 注册中心 + 配置中心相关配置 ####################

spring:
  cloud:
    nacos:
      server-addr: localhost:8848 # Nacos 服务器地址
      username: nacos # Nacos 账号
      password: nacos # Nacos 密码
      discovery: # 【配置中心】配置项
        namespace: dev # 命名空间。这里使用 dev 开发环境
        group: DEFAULT_GROUP # 使用的 Nacos 配置分组，默认为 DEFAULT_GROUP
      config: # 【注册中心】配置项
        namespace: dev # 命名空间。这里使用 dev 开发环境
        group: DEFAULT_GROUP # 使用的 Nacos 配置分组，默认为 DEFAULT_GROUP

# 以下配置可以迁移至 Nacos

server:
  port: 9998

--- #################### 基础配置 ####################

spring:
  # Servlet 配置
  servlet:
    # 文件上传相关配置项
    multipart:
      max-file-size: 50MB # 单个文件大小
      max-request-size: 50MB # 设置总上传的文件大小
    mvc:
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER # 解决 SpringFox 与 SpringBoot 2.6.x 不兼容的问题，参见 SpringFoxHandlerProviderBeanPostProcessor 类

  # Cache 配置项
  cache:
    type: REDIS
    redis:
      time-to-live: 1h # 设置过期时间为 1 小时

  # Spring Data Redis 配置
  data:
    redis:
      repositories:
        enabled: false # 项目未使用到 Spring Data Redis 的 Repository，所以直接禁用，保证启动速度

  # 发送邮件短信配置如：采用qq邮箱服务器发送
  mail:
    # 配置 SMTP 服务器地址
    host: smtp.qq.com
    # 发送者邮箱
    username: <EMAIL>
    # 配置授权码
    password: xxx
    properties:
      mail:
        smtp:
          auth: true

  datasource:
    dynamic:
      primary: master # 设置默认的数据源或者数据源组,默认值即为master
      strict: false # 严格匹配数据源,默认false. true未匹配到指定数据源时抛异常,false使用默认数据源
      seata: true # 开启seata代理，开启后默认每个数据源都代理，如果某个不需要代理可单独关闭
      seata-mode: AT # 支持XA及AT模式，默认AT
      hikari: # Hikari 【连接池】相关的全局配置
        min-idle: 5 # 最小连接数
        max-pool-size: 20 # 最大连接数
        is-auto-commit: true # 此属性控制从池返回的连接的默认自动提交行为,默认值：true
        idle-timeout: 300000 # 空闲连接存活最大时间，默认600000（10分钟）
        max-lifetime: 1800000 # 此属性控制池中连接的最长生命周期，值0表示无限生命周期，默认1800000即30分钟
        connection-timeout: 30000 # 数据库连接超时时间,默认30秒，即30000
        connection-test-query: SELECT 1 # 配置检测连接是否有效
      datasource:
        master:
          type: com.zaxxer.hikari.HikariDataSource
          driver-class-name: org.postgresql.Driver
          url: *************************************************************
          username: postgres
          password: 123456

  # Redis 配置。Redisson 默认的配置足够使用，一般不需要进行调优
  redis:
    host: localhost # 地址
    port: 6378 # 端口
    database: 1 # 数据库索引
    password: 123@abcd # 密码

--- #################### 接口文档配置 ####################

springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  default-flat-param-object: true # 参见 https://doc.xiaominfo.com/docs/faq/v4/knife4j-parameterobject-flat-param 文档
  packages-to-scan: ${medox.info.base-package} # 扫描指定的包
  group-configs:
    - group: 'default'
      paths-to-match: '/**'

knife4j:
  enable: true
  setting:
    language: zh_cn

--- #################### MyBatis Plus 的配置项 ####################

mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true # 虽然默认为 true ，但是还是显示去指定下。
    call-setters-on-nulls: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: ASSIGN_UUID # UUID 模式，不含中划线的 UUID 生成
      #      id-type: AUTO # 自增 ID，适合 MySQL 等直接自增的数据库
      #      id-type: INPUT # 用户输入 ID，适合 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库
      #      id-type: ASSIGN_ID # 分配 ID，默认使用雪花算法。注意，Oracle、PostgreSQL、Kingbase、DB2、H2 数据库时，需要去除实体类上的 @KeySequence 注解
      logic-delete-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)
    banner: true # 是否打印 mybatis plus banner，默认true
  type-aliases-package: ${medox.info.base-package}.module.*.module.**.entity
  mapper-locations: classpath*:cn/com/sipsg/**/module/*/mapper/xml/**Mapper.xml,classpath*:cn/com/sipsg/common/*/core/mapper/xml/**Mapper.xml
  encryptor:
    password: km8GnWhTmv4g+i9y # 加解密的秘钥，可使用 https://www.imaegoo.com/2020/aes-key-generator/ 网站生成

# MyBatis Plus Join 的配置项
mybatis-plus-join:
  banner: true # 是否打印 mybatis plus join banner，默认true
  sub-table-logic: true # 全局启用副表逻辑删除，默认 true。关闭后关联查询不会加副表逻辑删除
  ms-cache: true # 拦截器 MappedStatement 缓存，默认 true
  table-alias: t # 表别名(默认 t)
  logic-del-type: on # 副表逻辑删除条件的位置，支持 WHERE、ON，默认 ON

# VO 转换（数据翻译）相关
easy-trans:
  is-enable-global: true # 启用全局翻译（拦截所有 SpringMVC ResponseBody 进行自动翻译 )。如果对于性能要求很高可关闭此配置，或通过 @IgnoreTrans 忽略某个接口
  is-enable-cloud: false # 禁用 TransType.RPC 微服务模式

--- #################### 文件配置 ####################

dromara:
  x-file-storage:
    default-platform: minio-1 # 默认存储平台
    minio:
      - platform: minio-1 # 存储平台标识
        enable-storage: true  # 启用存储
        access-key: admin
        secret-key: 123@abcd
        end-point: http://localhost:9000
        bucket-name: medox
        domain: http://localhost:9000/medox/ # 访问域名，注意“/”结尾，例如：http://minio.abc.com/abc/

--- #################### 验证码相关配置 ####################

aj:
  captcha:
    jigsaw: classpath:images/jigsaw # 滑动验证，底图路径，不配置将使用默认图片；以 classpath: 开头，取 resource 目录下路径
    pic-click: classpath:images/pic-click # 滑动验证，底图路径，不配置将使用默认图片；以 classpath: 开头，取 resource 目录下路径
    cache-type: redis # 缓存 local/redis...
    cache-number: 1000 # local 缓存的阈值,达到这个值，清除缓存
    timing-clear: 180 # local定时清除过期缓存(单位秒),设置为0代表不执行
    type: blockPuzzle # 验证码类型 default两种都实例化。 blockPuzzle 滑块拼图 clickWord 文字点选
    water-mark: MedoX # 右下角水印文字(我的水印)，可使用 https://tool.chinaz.com/tools/unicode.aspx 中文转 Unicode，Linux 可能需要转 unicode
    interference-options: 0 # 滑动干扰项(0/1/2)
    req-frequency-limit-enable: false # 接口请求次数一分钟限制是否开启 true|false
    req-get-lock-limit: 5 # 验证失败 5 次，get接口锁定
    req-get-lock-seconds: 10 # 验证失败后，锁定时间间隔
    req-get-minute-limit: 30 # get 接口一分钟内请求数限制
    req-check-minute-limit: 60 # check 接口一分钟内请求数限制
    req-verify-minute-limit: 60 # verify 接口一分钟内请求数限制

--- #################### Sa-Token 配置 ####################

sa-token:
  # token 名称（同时也是 cookie 名称）
  token-name: Authorization
  # token 前缀，配置 Token 前缀后，Cookie 鉴权方式将会失效
  #  token-prefix: Bearer
  # token 有效期（单位：秒） 默认30天，-1 代表永久有效
  timeout: 2592000
  # token 最低活跃频率（单位：秒），如果 token 超过此时间没有访问系统就会被冻结，默认-1 代表不限制，永不冻结
  active-timeout: -1
  # 是否允许同一账号多地同时登录 （为 true 时允许一起登录, 为 false 时新登录挤掉旧登录）
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个 token （为 true 时所有登录共用一个 token, 为 false 时每次登录新建一个 token）
  is-share: false
  # token 风格（默认可取值：uuid、simple-uuid、random-32、random-64、random-128、tik）
  token-style: random-64
  # 是否输出操作日志
  is-log: true
  # 是否尝试从 cookie 里读取 Token
  is-read-cookie: false

--- #################### 服务保障相关配置 ####################

# Lock4j 配置项
lock4j:
  acquire-timeout: 3000 # 获取分布式锁超时时间，默认为 3000 毫秒
  expire: 30000 # 分布式锁的超时时间，默认为 30 毫秒

--- #################### 工作流相关配置 ####################

flowable:
  async-executor-activate: false # 关闭一直查询定时任务脚本打印

--- #################### 日志配置 ####################

tlog:
  enable-invoke-time-print: true # 是否打印调用时间，默认为 true

--- #################### 定时任务配置 ####################

xxl:
  job:
    enabled: true
    admin:
      addresses: http://localhost:8900/xxl-job-admin # 调度中心部署跟地址
    executor:
      appname: xxl-job-executor
      port: 8081
      logretentiondays: 30
      logpath: D:/logs
    access-token: 123@abcd

--- #################### 分布式事务配置 ####################

seata:
  enabled: true
  tx-service-group: default_tx_group
  enable-auto-data-source-proxy: false
  client:
    undo:
      log-serialization: kryo
  service:
    grouplist:
      default: localhost:8091
    vgroup-mapping:
      default_tx_group: default

--- #################### MedoX相关配置 ####################

medox:
  info:
    base-package: cn.com.sipsg
  swagger:
    title: 系统模块
    description: 系统模块详情
    author: MedoX
    version: ${medox.info.version}
  captcha:
    enabled: false # 开发环境关闭验证码，方便调试
  flow:
    # 连续的两个节点配的是岗位，岗位是同一个人自动跳过开关
    differentPostAutoSkip:
      enable: true
    # 当前节点是动态指定审批人方式指定的，审批人和上一个节点审批人是同一个人，自动跳过开关，true则自动跳过，默认false
    aynamicAppointApproveUserAutoSkip:
      enable: true
    # 超时发送系统催办消息和自动审批功能的定时调度cron表达式，可根据项目需要修改频率，目前设置是10分钟执行一次
    flowTasktimeoutJob:
      cron: 0 0/5 * * * ?
  amap:
    key: xxx # 替换成实际的key
    url: https://restapi.amap.com/v3
  xss:
    exclude-urls:
      - /flow/flowEntry/save
      - /flow/flowEntry/update
      - /system/upms/menu/save
      - /online/subForm/save
      - /online/subForm/update
      - /dynamic/server/save
      - /dynamic/server/update
      - /dynamic/token/save
      - /dynamic/token/update
      - /dynamic/template/save
      - /dynamic/template/update
      - /onemap/geoMapResource/save
      - /onemap/geoMapResource/update
      - /onemap/geoMapExtent/save
      - /onemap/geoMapExtent/update

# 以上配置可以迁移至 Nacos
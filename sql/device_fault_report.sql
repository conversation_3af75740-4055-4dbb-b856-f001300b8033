-- 设备故障上报表
CREATE TABLE tbl_device_fault_report (
    id BIGINT PRIMARY KEY,
    fault_name VARCHAR(100) NOT NULL COMMENT '故障名称',
    device_fault VARCHAR(200) NOT NULL COMMENT '设备故障',
    occurrence_station VARCHAR(200) NOT NULL COMMENT '发生站点',
    fault_description TEXT COMMENT '故障说明',
    urgency_level VARCHAR(20) NOT NULL COMMENT '紧急程度',
    report_time TIMESTAMP NOT NULL COMMENT '上报时间',
    repair_progress VARCHAR(20) NOT NULL COMMENT '维修进度',
    report_org_id VARCHAR(64) COMMENT '上报单位编号',
    report_org_name VARCHAR(200) COMMENT '上报单位名称',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    creator VARCHAR(64) COMMENT '创建人',
    updater VARCHAR(64) COMMENT '更新人',
    available BOOLEAN DEFAULT TRUE COMMENT '是否有效'
);

-- 添加表注释
-- 添加表注释
COMMENT ON TABLE tbl_device_fault_report IS '设备故障上报表';

-- 创建索引
CREATE INDEX idx_device_fault_report_fault_name ON tbl_device_fault_report(fault_name);
CREATE INDEX idx_device_fault_report_urgency_level ON tbl_device_fault_report(urgency_level);
CREATE INDEX idx_device_fault_report_repair_progress ON tbl_device_fault_report(repair_progress);
CREATE INDEX idx_device_fault_report_report_time ON tbl_device_fault_report(report_time);
CREATE INDEX idx_device_fault_report_create_time ON tbl_device_fault_report(create_time);
CREATE INDEX idx_device_fault_report_occurrence_station ON tbl_device_fault_report(occurrence_station);
CREATE INDEX idx_device_fault_report_available ON tbl_device_fault_report(available);
CREATE INDEX idx_device_fault_report_report_org_id ON tbl_device_fault_report(report_org_id);

-- 创建复合索引
CREATE INDEX idx_device_fault_report_progress_time ON tbl_device_fault_report(repair_progress, report_time);
CREATE INDEX idx_device_fault_report_urgency_time ON tbl_device_fault_report(urgency_level, report_time);

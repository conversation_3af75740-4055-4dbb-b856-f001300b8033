-- 警员管辖站点关联表
CREATE TABLE `base_police_emp_station` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `emp_id` varchar(32) NOT NULL COMMENT '警员ID',
  `station_id` bigint(20) NOT NULL COMMENT '站点ID',
  `available` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否可用：1-可用，0-不可用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user_id` varchar(32) DEFAULT NULL COMMENT '创建用户ID',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user_id` varchar(32) DEFAULT NULL COMMENT '更新用户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_emp_station` (`emp_id`, `station_id`) COMMENT '警员站点唯一索引',
  KEY `idx_emp_id` (`emp_id`) COMMENT '警员ID索引',
  KEY `idx_station_id` (`station_id`) COMMENT '站点ID索引',
  KEY `idx_available` (`available`) COMMENT '可用状态索引',
  KEY `idx_create_time` (`create_time`) COMMENT '创建时间索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='警员管辖站点关联表';

-- 添加外键约束（如果需要的话，根据实际情况决定是否添加）
-- ALTER TABLE `base_police_emp_station` ADD CONSTRAINT `fk_emp_station_emp_id` FOREIGN KEY (`emp_id`) REFERENCES `base_police_emp` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
-- ALTER TABLE `base_police_emp_station` ADD CONSTRAINT `fk_emp_station_station_id` FOREIGN KEY (`station_id`) REFERENCES `base_station` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- 创建复合索引以优化查询性能
CREATE INDEX `idx_emp_available` ON `base_police_emp_station` (`emp_id`, `available`);
CREATE INDEX `idx_station_available` ON `base_police_emp_station` (`station_id`, `available`);
package cn.com.sipsg.module.data.module.Police.entity;

import cn.com.sipsg.common.mybatis.core.dataobject.BaseDO;
import cn.hutool.core.date.DatePattern;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 警情管理
 */
@Getter
@Setter
@Schema(name = "警情管理", description = "警情管理")
@TableName(value = "data_situation_event", autoResultMap = true)
public class SituationEvent extends BaseDO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "id")
    @TableId
    private String id;

    @Schema(description = "警情编号")
    private String eventCode;

    @Schema(description = "报警电话")
    private String alarmPhone;

    @Schema(description = "报警时间")
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date alarmTime;

    @Schema(description = "报警人_姓名")
    private String alarmPersonName;

    @Schema(description = "报警人_联系电话")
    private String alarmPersonPhone;

    @Schema(description = "报警方式")
    private String alarmType;

    @Schema(description = "报警内容")
    private String alarmContent;

    @Schema(description = "警情报警地点(站点名称)")
    private String alarmAddress;

    @Schema(description = "警情报警位置经度")
    private String alarmAddressLng;

    @Schema(description = "警情报警位置纬度")
    private String alarmAddressLat;

    @Schema(description = "警情报警位置坐标系")
    private String alarmAddressCoordinate;

    @Schema(description = "警情类型")
    private String eventType;

    @Schema(description = "警情属性")
    private String eventProperty;

    @Schema(description = "报警地所属辖区编号")
    private String alarmBelongPoliceOrgCode;

    @Schema(description = "报警第所属辖区名称")
    private String alarmBelongPoliceOrgName;

    @Schema(description = "警情案发地点")
    private String happenedAddress;

    @Schema(description = "警情案发位置经度")
    private String happenedAddressLng;

    @Schema(description = "警情案发位置维度")
    private String happenedAddressLat;

    @Schema(description = "警情案发位置坐标系")
    private String happenedAddressCoordinate;

    @Schema(description = "案发地所属辖区编号")
    private String happenedBelongPoliceOrgCode;

    @Schema(description = "案发地所属辖区名称")
    private String happenedBelongPoliceOrgName;

    @Schema(description = "事发时间初值")
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date happenedStartTime;

    @Schema(description = "事发时间终值")
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date happenedEndTime;

    @Schema(description = "事发场所")
    private String happenedPlace;

    @Schema(description = "事发部位")
    private String happenedLocation;

    @Schema(description = "事发原因")
    private String happenedReason;

    @Schema(description = "简要警情")
    private String eventContent;

    @Schema(description = "接警人")
    private String receiveName;

    @Schema(description = "接警时间")
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date receiveTime;

    @Schema(description = "接警单位编号")
    private String receivePoliceOrgCode;

    @Schema(description = "接警单位名称")
    private String receivePoliceOrgName;

    @Schema(description = "接警类型")
    private String receiveType;

    @Schema(description = "处警人")
    private String handlePersonName;

    @Schema(description = "处警时间")
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date handleTime;

    @Schema(description = "处警单位编号")
    private String handlePoliceOrgCode;

    @Schema(description = "处警单位名称")
    private String handlePoliceOrgName;

    @Schema(description = "处警结果")
    private String handleResult;

    @Schema(description = "处理结果")
    private String handleProcessResult;

    @Schema(description = "处警反馈时间")
    private String handleFeedbackTime;

    @Schema(description = "处警详细地点")
    private String handleDetailedAddress;

    @Schema(description = "标注信息")
    private String labelStatus;
}

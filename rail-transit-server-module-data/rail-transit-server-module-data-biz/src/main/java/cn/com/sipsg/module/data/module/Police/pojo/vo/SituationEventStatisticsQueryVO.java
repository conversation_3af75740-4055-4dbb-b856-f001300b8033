package cn.com.sipsg.module.data.module.Police.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * 警情管理_查询
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Schema(name = "警情管理_查询", description = "警情管理_查询")
public class SituationEventStatisticsQueryVO {
    @Schema(description = "警情数量")
    private Integer eventCount;

    @Schema(description = "刑事数量")
    private Integer criminalCount;

    @Schema(description = "统计图表")
    private List<ChartData> chartDataList = new ArrayList<>();

    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    @Schema(name = "统计图表", description = "统计图表")
    public static class ChartData {
        @Schema(description = "统计时间")
        private String value;

        @Schema(description = "统计数量")
        private Integer count;
    }
}

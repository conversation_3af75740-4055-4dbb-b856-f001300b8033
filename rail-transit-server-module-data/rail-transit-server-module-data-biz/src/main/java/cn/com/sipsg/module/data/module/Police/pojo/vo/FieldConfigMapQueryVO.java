package cn.com.sipsg.module.data.module.Police.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 字段配置_查询_分组
 */
@Getter
@Setter
@Schema(name = "字段配置_查询_分组", description = "字段配置_查询_分组")
public class FieldConfigMapQueryVO {
    @Schema(description = "id")
    private String id;

    @Schema(description = "模块表名")
    private String tableName;

    @Schema(description = "字段名")
    private String fieldName;

    @Schema(description = "字段中文名称")
    private String fieldNameCn;

    @Schema(description = "是否启用 0否/1是")
    private Integer enable = 0;

    @Schema(description = "排序")
    private Integer sort = 99;
}

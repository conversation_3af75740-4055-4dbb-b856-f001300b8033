package cn.com.sipsg.module.data.module.Police.mapper;

import cn.com.sipsg.common.mybatis.core.mapper.BaseMapperX;
import cn.com.sipsg.module.data.module.Police.entity.SituationEvent;
import cn.com.sipsg.module.data.module.Police.pojo.vo.SituationEventStatisticsQueryVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 警情管理
 */
public interface SituationEventMapper extends BaseMapperX<SituationEvent> {
    /**
     * 警情统计_半小时为维度
     *
     * @param alarmAddress    警情报警地点(站点名称)
     * @param createStartTime 开始时间 yyyy-MM-dd HH:mm:ss
     * @param createEndTime   结束时间 yyyy-MM-dd HH:mm:ss
     * @return 统计数据
     */
    List<SituationEventStatisticsQueryVO.ChartData> statistics(@Param("alarmAddress") String alarmAddress, @Param("createStartTime") String createStartTime, @Param("createEndTime") String createEndTime);

    /**
     * 警情统计_天为维度
     *
     * @param alarmAddress    警情报警地点(站点名称)
     * @param createStartTime 开始时间 yyyy-MM-dd HH:mm:ss
     * @param createEndTime   结束时间 yyyy-MM-dd HH:mm:ss
     * @return 统计数据
     */
    List<SituationEventStatisticsQueryVO.ChartData> statisticsDay(@Param("alarmAddress") String alarmAddress, @Param("createStartTime") String createStartTime, @Param("createEndTime") String createEndTime);

    /**
     * 警情统计_类型
     *
     * @param alarmAddress    警情报警地点(站点名称)
     * @param createStartTime 开始时间 yyyy-MM-dd HH:mm:ss
     * @param createEndTime   结束时间 yyyy-MM-dd HH:mm:ss
     * @return 统计数据
     */
    List<SituationEventStatisticsQueryVO.ChartData> statisticsType(@Param("alarmAddress") String alarmAddress, @Param("createStartTime") String createStartTime, @Param("createEndTime") String createEndTime);

    /**
     * 刑事数量
     *
     * @param alarmAddress    警情报警地点(站点名称)
     * @param createStartTime 开始时间 yyyy-MM-dd HH:mm:ss
     * @param createEndTime   结束时间 yyyy-MM-dd HH:mm:ss
     * @return 统计数据
     */
    Integer criminalCount(@Param("alarmAddress") String alarmAddress, @Param("createStartTime") String createStartTime, @Param("createEndTime") String createEndTime);
}

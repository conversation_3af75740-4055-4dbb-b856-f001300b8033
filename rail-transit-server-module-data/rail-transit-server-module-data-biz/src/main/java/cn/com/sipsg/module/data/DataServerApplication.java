package cn.com.sipsg.module.data;

import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.context.WebServerInitializedEvent;
import org.springframework.boot.web.server.WebServer;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Component;

import java.net.Inet4Address;
import java.net.UnknownHostException;

/**
 * 系统模块启动类
 *
 * <AUTHOR>
 * @since 2025/04/17
 */
@SuppressWarnings("SpringComponentScan") // 忽略 IDEA 无法识别 ${medox.info.base-package}
@EnableAsync
@SpringBootApplication
@MapperScan("${medox.info.base-package}.module.data.module.*.mapper")
@EnableFeignClients("${medox.info.base-package}.module.**.api")
@Slf4j
public class DataServerApplication {

    public static void main(String[] args) {
        SpringApplication.run(DataServerApplication.class, args);
    }

    /**
     * Web 服务端口监听器
     */
    @Component
    public static class WebServerPortListener implements ApplicationListener<WebServerInitializedEvent> {
        @Override
        public void onApplicationEvent(WebServerInitializedEvent event) {
            try {
                WebServer webServer = event.getWebServer();
                String hostAddress = Inet4Address.getLocalHost().getHostAddress();
                int port = webServer != null ? webServer.getPort() : -1;
                String applicationName = event.getApplicationContext().getApplicationName();
                log.info("swagger 接口文档地址: http://{}:{}{}/doc.html", hostAddress, port, applicationName);
                log.info("swagger 接口导出地址: http://{}:{}{}/v3/api-docs", hostAddress, port, applicationName);
            } catch (UnknownHostException e) {
                log.error(e.getMessage());
            }
        }
    }
}

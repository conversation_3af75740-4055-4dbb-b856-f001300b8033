package cn.com.sipsg.module.data.module.Test.controller;

import cn.com.sipsg.common.operationlog.core.annotation.OperationLog;
import cn.com.sipsg.common.operationlog.core.enums.OperationLogLevelEnum;
import cn.com.sipsg.common.operationlog.core.enums.OperationTypeEnum;
import cn.com.sipsg.common.pojo.CommonResult;
import cn.com.sipsg.module.data.module.Test.service.TestService;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 测试
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/test")
@Tag(name = "测试", description = "测试", extensions = {@Extension(properties = {@ExtensionProperty(name = "x-order", value = "999", parseValue = true)})})
@ApiSupport(order = 999)
public class TestController {
    @Resource
    private TestService testService;

    /**
     * 测试分页
     *
     * @param pageNumber 页码
     * @param pageSize   条数
     * @return 分页数据
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 1)
    @Operation(summary = "测试分页", description = "测试分页")
    @Parameters({
        @Parameter(name = "pageNumber", description = "页码", example = "1", schema = @Schema(description = "页码", type = "int")),
        @Parameter(name = "pageSize", description = "条数", example = "10", schema = @Schema(description = "条数", type = "int"))
    })
    @OperationLog(module = "测试", value = "测试分页", type = OperationTypeEnum.QUERY, level = OperationLogLevelEnum.LOW)
    public CommonResult<PageInfo<JSONObject>> page(@RequestParam(value = "pageNumber", required = false, defaultValue = "1") Integer pageNumber,
                                                   @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize) {
        return CommonResult.data(testService.page(pageNumber, pageSize));
    }
}

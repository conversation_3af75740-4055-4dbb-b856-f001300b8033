package cn.com.sipsg.module.data.module.Police.controller;

import cn.com.sipsg.common.operationlog.core.annotation.OperationLog;
import cn.com.sipsg.common.operationlog.core.enums.OperationLogLevelEnum;
import cn.com.sipsg.common.operationlog.core.enums.OperationTypeEnum;
import cn.com.sipsg.common.pojo.CommonResult;
import cn.com.sipsg.module.data.module.Police.pojo.bo.SituationEventSaveBO;
import cn.com.sipsg.module.data.module.Police.pojo.bo.SituationEventUpdateBO;
import cn.com.sipsg.module.data.module.Police.pojo.vo.SituationEventInfoQueryVO;
import cn.com.sipsg.module.data.module.Police.pojo.vo.SituationEventQueryVO;
import cn.com.sipsg.module.data.module.Police.pojo.vo.SituationEventStatisticsQueryVO;
import cn.com.sipsg.module.data.module.Police.service.SituationEventService;
import com.github.pagehelper.PageInfo;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 警情管理
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/situation/event")
@Tag(name = "警情管理", description = "警情管理", extensions = {@Extension(properties = {@ExtensionProperty(name = "x-order", value = "1", parseValue = true)})})
@ApiSupport(order = 1)
public class SituationEventController {
    @Resource
    private SituationEventService situationEventService;

    /**
     * 新增
     *
     * @param situationEventSaveBO 警情管理_保存
     * @return 状态
     */
    @PostMapping("/add")
    @ApiOperationSupport(order = 1)
    @Operation(summary = "新增", description = "新增")
    @OperationLog(module = "警情管理", value = "新增", type = OperationTypeEnum.SAVE, level = OperationLogLevelEnum.LOW)
    public CommonResult<Object> addData(@Valid @RequestBody SituationEventSaveBO situationEventSaveBO) {
        Boolean status = situationEventService.addData(situationEventSaveBO);
        if (status) {
            return CommonResult.success("新增成功");
        } else {
            return CommonResult.fail("新增失败");
        }
    }

    /**
     * 更新
     *
     * @param situationEventUpdateBO 警情管理_更新
     * @return 状态
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 2)
    @Operation(summary = "更新", description = "更新")
    @OperationLog(module = "警情管理", value = "更新", type = OperationTypeEnum.UPDATE, level = OperationLogLevelEnum.LOW)
    public CommonResult<Object> updateData(@Valid @RequestBody SituationEventUpdateBO situationEventUpdateBO) {
        Boolean status = situationEventService.updateData(situationEventUpdateBO);
        if (status) {
            return CommonResult.success("更新成功");
        } else {
            return CommonResult.fail("更新失败");
        }
    }

    /**
     * 删除
     *
     * @param id id
     * @return 状态
     */
    @GetMapping("/remove")
    @ApiOperationSupport(order = 3)
    @Operation(summary = "删除", description = "删除")
    @OperationLog(module = "警情管理", value = "删除", type = OperationTypeEnum.DELETE, level = OperationLogLevelEnum.HIGH)
    public CommonResult<Object> removeData(@RequestParam(value = "id") String id) {
        Boolean status = situationEventService.removeData(id);
        if (status) {
            return CommonResult.success("删除成功");
        } else {
            return CommonResult.fail("删除失败");
        }
    }

    /**
     * 列表查询
     *
     * @param eventCode                警情编号
     * @param receiveStartTime         接警开始时间 yyyy-MM-dd HH:mm:ss
     * @param receiveEndTime           接警结束时间 yyyy-MM-dd HH:mm:ss
     * @param receiveType              接警类型
     * @param eventCodeSort            接警类型 -1降序/0不排序/1升序
     * @param receivePoliceOrgNameSort 接警类型 -1降序/0不排序/1升序
     * @param pageNum                  页码
     * @param pageSize                 每页条数
     * @return 列表数据
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 4)
    @Operation(summary = "列表查询", description = "列表查询")
    @OperationLog(module = "警情管理", value = "列表查询", type = OperationTypeEnum.QUERY, level = OperationLogLevelEnum.LOW)
    @Parameters({
        @Parameter(name = "eventCode", description = "警情编号", schema = @Schema(description = "警情编号", type = "string")),
        @Parameter(name = "receiveStartTime", description = "接警开始时间 yyyy-MM-dd HH:mm:ss", schema = @Schema(description = "接警开始时间 yyyy-MM-dd HH:mm:ss", type = "string")),
        @Parameter(name = "receiveEndTime", description = "接警结束时间 yyyy-MM-dd HH:mm:ss", schema = @Schema(description = "接警结束时间 yyyy-MM-dd HH:mm:ss", type = "string")),
        @Parameter(name = "receiveType", description = "接警类型", schema = @Schema(description = "接警类型", type = "string")),
        @Parameter(name = "eventCodeSort", description = "警情编号 -1降序/0不排序/1升序", schema = @Schema(description = "警情编号 -1降序/0不排序/1升序", type = "int")),
        @Parameter(name = "receivePoliceOrgNameSort", description = "接警单位名称 -1降序/0不排序/1升序", schema = @Schema(description = "接警单位名称 -1降序/0不排序/1升序", type = "int")),
        @Parameter(name = "pageNum", description = "页码", schema = @Schema(description = "页码", type = "integer", defaultValue = "1")),
        @Parameter(name = "pageSize", description = "每页条数", schema = @Schema(description = "每页条数", type = "integer", defaultValue = "10"))
    })
    public CommonResult<PageInfo<SituationEventQueryVO>> listData(@RequestParam(value = "eventCode", required = false) String eventCode,
                                                                  @RequestParam(value = "receiveStartTime", required = false) String receiveStartTime,
                                                                  @RequestParam(value = "receiveEndTime", required = false) String receiveEndTime,
                                                                  @RequestParam(value = "receiveType", required = false) String receiveType,
                                                                  @RequestParam(value = "eventCodeSort", required = false, defaultValue = "0") Integer eventCodeSort,
                                                                  @RequestParam(value = "receivePoliceOrgNameSort", required = false, defaultValue = "0") Integer receivePoliceOrgNameSort,
                                                                  @RequestParam(value = "pageNum", required = false, defaultValue = "1") Integer pageNum,
                                                                  @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize) {
        return CommonResult.data(situationEventService.listData(eventCode, receiveStartTime, receiveEndTime, receiveType, eventCodeSort, receivePoliceOrgNameSort, pageNum, pageSize));
    }

    /**
     * 详情查询
     *
     * @param id id
     * @return 详情数据
     */
    @GetMapping("/info")
    @ApiOperationSupport(order = 5)
    @Operation(summary = "详情查询", description = "详情查询")
    @OperationLog(module = "警情管理", value = "详情查询", type = OperationTypeEnum.QUERY, level = OperationLogLevelEnum.LOW)
    @Parameters({
        @Parameter(name = "id", description = "id", schema = @Schema(description = "id", type = "string"))
    })
    public CommonResult<SituationEventInfoQueryVO> getInfo(@RequestParam(value = "id") String id) {
        return CommonResult.data(situationEventService.getInfo(id));
    }

    /**
     * 警情统计
     *
     * @param alarmAddress    警情报警地点(站点名称)
     * @param createStartTime 开始时间 yyyy-MM-dd HH:mm:ss
     * @param createEndTime   结束时间 yyyy-MM-dd HH:mm:ss
     * @param type            统计维度 0时间/1类型
     * @return 统计数据
     */
    @GetMapping("/statistics")
    @ApiOperationSupport(order = 6)
    @Operation(summary = "警情统计", description = "警情统计")
    @OperationLog(module = "警情管理", value = "警情统计", type = OperationTypeEnum.QUERY, level = OperationLogLevelEnum.LOW)
    @Parameters({
        @Parameter(name = "alarmAddress", description = "警情报警地点(站点名称)", schema = @Schema(description = "警情报警地点(站点名称)", type = "string")),
        @Parameter(name = "createStartTime", description = "开始时间 yyyy-MM-dd HH:mm:ss", schema = @Schema(description = "开始时间 yyyy-MM-dd HH:mm:ss", type = "string")),
        @Parameter(name = "createEndTime", description = "结束时间 yyyy-MM-dd HH:mm:ss", schema = @Schema(description = "结束时间 yyyy-MM-dd HH:mm:ss", type = "string")),
        @Parameter(name = "type", description = "统计维度 0时间/1类型", schema = @Schema(description = "统计维度 0时间/1类型", type = "int"))
    })
    public CommonResult<SituationEventStatisticsQueryVO> statistics(@RequestParam(value = "alarmAddress", required = false) String alarmAddress,
                                                                    @RequestParam(value = "createStartTime", required = false) String createStartTime,
                                                                    @RequestParam(value = "createEndTime", required = false) String createEndTime,
                                                                    @RequestParam(value = "type", required = false, defaultValue = "0") Integer type) {
        return CommonResult.data(situationEventService.statistics(alarmAddress, createStartTime, createEndTime, type));
    }
}

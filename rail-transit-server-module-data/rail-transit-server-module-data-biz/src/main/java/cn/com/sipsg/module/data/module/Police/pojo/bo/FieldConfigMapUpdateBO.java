package cn.com.sipsg.module.data.module.Police.pojo.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 字段配置_更新_分组
 */
@Getter
@Setter
@Schema(name = "字段配置_更新_分组", description = "字段配置_更新_分组")
public class FieldConfigMapUpdateBO {
    @Schema(description = "模块表名")
    @NotBlank(message = "模块表名不能为空")
    @NotNull(message = "模块表名不能为空")
    @Size(max = 255, message = "模块表名长度不能超过255个字符")
    private String tableName;

    @Schema(description = "启用id列表")
    private List<String> idList;
}

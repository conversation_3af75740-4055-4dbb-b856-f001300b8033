spring:
  application:
    name: data-server

  profiles:
    active: dev

  main:
    allow-circular-references: true # 允许循环依赖，因为项目是三层架构，无法避免这个情况。
    allow-bean-definition-overriding: true # 允许覆盖 Bean，解决重复注册的问题

  config:
    import:
      - optional:classpath:application-${spring.profiles.active}.yml # 加载【本地】配置
      - optional:nacos:${spring.application.name}-${spring.profiles.active}.yml # 加载【Nacos】的配置

--- #################### MedoX相关配置 ####################

medox:
  info:
    version: 1.0.0
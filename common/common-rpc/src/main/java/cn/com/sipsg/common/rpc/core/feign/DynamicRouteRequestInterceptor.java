package cn.com.sipsg.common.rpc.core.feign;

import cn.com.sipsg.common.constant.RpcConstants;
import feign.RequestInterceptor;
import feign.RequestTemplate;

/**
 * 动态服务路由请求拦截器
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public class DynamicRouteRequestInterceptor implements RequestInterceptor {

    @Override
    public void apply(RequestTemplate requestTemplate) {
        if (requestTemplate.queries().containsKey(RpcConstants.SERVICE_NAME_KEY)) {
            requestTemplate.target("http://" + requestTemplate.queries().get(RpcConstants.SERVICE_NAME_KEY).toArray()[0]);
        }
    }

}

package cn.com.sipsg.common.holiday.core.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * 节假日、周末VO
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Data
@Accessors(chain = true)
@Schema(description = "节假日、周末VO")
public class HolidayVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "节假日、法定假日")
    private String holiday;

    @Schema(description = "描述")
    private String describe;

}

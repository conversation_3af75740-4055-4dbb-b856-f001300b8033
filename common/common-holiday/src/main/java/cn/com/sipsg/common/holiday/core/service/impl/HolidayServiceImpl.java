package cn.com.sipsg.common.holiday.core.service.impl;

import cn.com.sipsg.common.holiday.core.entity.Holiday;
import cn.com.sipsg.common.holiday.core.mapper.HolidaysMapper;
import cn.com.sipsg.common.holiday.core.pojo.bo.HolidayQueryBO;
import cn.com.sipsg.common.holiday.core.pojo.bo.HolidaySaveBO;
import cn.com.sipsg.common.holiday.core.pojo.vo.HolidayVO;
import cn.com.sipsg.common.holiday.core.service.HolidayService;
import cn.com.sipsg.common.mybatis.core.query.LambdaQueryWrapperX;
import cn.com.sipsg.common.pojo.bo.DeleteBO;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.util.DateUtils;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.time.LocalDateTime;
import java.util.Date;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 节假日、周末 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Service
public class HolidayServiceImpl extends ServiceImpl<HolidaysMapper, Holiday> implements HolidayService {

    /**
     * 分页查询节假日、周末
     *
     * @param bo   查询条件
     * @return 分页信息
     */
    @Override
    public CommonPageVO<HolidayVO> page(HolidayQueryBO bo) {
        CommonPageVO<Holiday> pageVO = this.page(bo, toWrapper(bo));
        return pageVO.convert(this::toVO);
    }

    /**
     * 获取节假日、周末详情
     *
     * @param id id
     * @return 详情
     */
    @Override
    public HolidayVO detail(String id) {
        Holiday entity = this.getById(id);
        return toVO(entity);
    }

    /**
     * 新增节假日、周末
     *
     * @param bo 新增对象
     * @return 成功或失败
     */
    @Override
    public Boolean save(HolidaySaveBO bo) {
        Holiday entity = Convert.convert(Holiday.class, bo);
        return this.save(entity);
    }

    /**
     * 修改节假日、周末
     *
     * @param bo 修改对象
     * @return 成功或失败
     */
    @Override
    public Boolean update(HolidaySaveBO bo) {
        Holiday entity = Convert.convert(Holiday.class, bo);
        return this.updateById(entity);
    }

    /**
     * 删除节假日、周末
     *
     * @param id id
     * @return 成功或失败
     */
    @Override
    public Boolean remove(String id) {
        return this.removeById(id);
    }

    /**
     * 批量删除节假日、周末
     *
     * @param bo 删除条件
     * @return 成功或失败
     */
    @Override
    @Transactional
    public Boolean removeBatch(DeleteBO bo) {
        return this.removeBatchByIds(bo.getIds());
    }

    /**
     * 根据年份保存节假日
     *
     * @param year 查询条件
     * @return 列表信息
     */
    @Override
    public Boolean saveYear(String year) {
        this.baseMapper.delete(new LambdaQueryWrapperX<Holiday>().likeRight(Holiday::getHoliday, year));
        String url = "https://timor.tech/api/holiday/year/" + year + "?week=Y";
        String result = HttpUtil.get(url, 5000);
        JSONObject jsonObject = new JSONObject(result);
        Integer code = jsonObject.getInt("code");
        if (code != 0) {
            return false;
        }
        JSONObject holiday = jsonObject.getJSONObject("holiday");
        holiday.forEach((k, v) -> {
            if (ObjectUtil.isNotEmpty(v.toString())) {
                JSON parse = JSONUtil.parse(v.toString());
                if (parse.getByPath("holiday") != null && "true".equals(parse.getByPath("holiday").toString())) {
                    HolidaySaveBO bo = new HolidaySaveBO();
                    bo.setHoliday((year + k).replace("-", ""));
                    bo.setDescribe(v.toString());
                    this.save(bo);
                }
            }
        });
        return null;
    }

    /**
     * 查询某个日期(年-月-日)加上指定天数和小时后的日期（跳过节假日）
     *
     * @param date 日期
     * @param days 天数
     * @param hours 小时
     * @return 日期
     */
    @Override
    public String skipHolidays(String date, Integer days, Integer hours) {
        List<Holiday> list = this.list();
        List<String> collect = list.stream().map(Holiday::getHoliday).collect(Collectors.toList());
        DateTime dateTime = DateUtil.parse(date, DatePattern.NORM_DATETIME_PATTERN);
        LocalDateTime localDateTime = LocalDateTimeUtil.of(dateTime);
        LocalDateTime newLocalDateTime = localDateTime;
        if (days != null && days != 0) {
            newLocalDateTime = newLocalDateTime.plusDays(days);
        }
        if (hours != null && hours != 0) {
            newLocalDateTime = newLocalDateTime.plusHours(hours);
        }
        int holidaysBetween = getHolidaysBetween(collect, localDateTime, newLocalDateTime);
        LocalDateTime addLocalDateTime = newLocalDateTime.plusDays(holidaysBetween);
        while (getHolidaysBetween(collect, newLocalDateTime, addLocalDateTime) > 0) {
            int i = getHolidaysBetween(collect, newLocalDateTime, addLocalDateTime);
            newLocalDateTime = addLocalDateTime;
            addLocalDateTime = newLocalDateTime.plusDays(i);
        }
        return DateUtil.format(addLocalDateTime, DatePattern.NORM_DATETIME_PATTERN);
    }

    @Override
    public boolean isExistYear(Integer year) {
        long count = this.count(new LambdaQueryWrapperX<Holiday>()
            .likeRight(Holiday::getHoliday, year.toString()));
        return count > 0;
    }

    //判断2个日期内有多少个节假日
    private int getHolidaysBetween(List<String> collect, LocalDateTime startLocalDateTime, LocalDateTime endLocalDateTime) {
        //只设置年月日
        Date startDate = DateUtils.of(startLocalDateTime.toLocalDate().atStartOfDay());
        Date endDate = DateUtils.of(endLocalDateTime.toLocalDate().atStartOfDay());
        long between = DateUtil.between(startDate, endDate, DateUnit.DAY);
        int count = 0;
        for (long i = 1; i <= between; i++) {
            LocalDateTime minusDays = startLocalDateTime.plusDays(i);
            if (collect.contains(DateUtil.format(minusDays, DatePattern.PURE_DATE_PATTERN))) {
                count += 1;
            }
        }
        return count;
    }


    /**
     * 查询条件转wrapper
     *
     * @param bo 查询条件
     * @return wrapper
     */
    private Wrapper<Holiday> toWrapper(HolidayQueryBO bo) {
        LambdaQueryWrapperX<Holiday> wrapper = new LambdaQueryWrapperX<>();
        // 填充查询条件
        return wrapper;
    }

    /**
     * entity转vo
     *
     * @param entity 实体对象
     * @return vo
     */
    private HolidayVO toVO(Holiday entity) {
        HolidayVO vo = Convert.convert(HolidayVO.class, entity);
        // 填充关联业务属性
        return vo;
    }

}

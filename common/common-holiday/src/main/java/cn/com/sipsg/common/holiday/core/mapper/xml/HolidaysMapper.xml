<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.sipsg.common.holiday.core.mapper.HolidaysMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.com.sipsg.common.holiday.core.entity.Holiday">
        <result column="holiday" property="holiday" />
        <result column="describe" property="describe" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        holiday, describe
    </sql>
    <delete id="deleteByYear">
        delete from sys_holidays where holiday like concat(#{year}::text,'%')
    </delete>


</mapper>

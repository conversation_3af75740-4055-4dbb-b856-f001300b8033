package cn.com.sipsg.common.datapermission.config;

import cn.com.sipsg.common.datapermission.core.rpc.DataPermissionRequestInterceptor;
import cn.com.sipsg.common.datapermission.core.rpc.DataPermissionRpcWebFilter;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;

/**
 * 数据权限RPC自动配置类
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@AutoConfiguration
@ConditionalOnClass(name = "feign.RequestInterceptor")
public class DataPermissionRpcAutoConfiguration {

    @Bean
    public DataPermissionRequestInterceptor dataPermissionRequestInterceptor() {
        return new DataPermissionRequestInterceptor();
    }

    @Bean
    public FilterRegistrationBean<DataPermissionRpcWebFilter> dataPermissionRpcFilter() {
        FilterRegistrationBean<DataPermissionRpcWebFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(new DataPermissionRpcWebFilter());
        return registrationBean;
    }

}

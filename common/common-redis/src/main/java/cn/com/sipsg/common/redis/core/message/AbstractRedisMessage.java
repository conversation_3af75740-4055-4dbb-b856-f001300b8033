package cn.com.sipsg.common.redis.core.message;

import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

/**
 * Redis 消息抽象基类
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
public abstract class AbstractRedisMessage {

    /**
     * 头
     */
    private Map<String, String> headers = new HashMap<>();

    public String getHeader(String key) {
        return headers.get(key);
    }

    public void addHeader(String key, String value) {
        headers.put(key, value);
    }

}

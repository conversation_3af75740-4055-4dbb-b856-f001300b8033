package cn.com.sipsg.common.dynamic.core.strategy.impl;

import cn.com.sipsg.common.constant.PageConstants;
import cn.com.sipsg.common.dynamic.core.aop.DynamicExecuteResult;
import cn.com.sipsg.common.dynamic.core.constant.DynamicConstants;
import cn.com.sipsg.common.dynamic.core.entity.DynamicServer;
import cn.com.sipsg.common.dynamic.core.enums.DynamicServerResultTypeEnums;
import cn.com.sipsg.common.dynamic.core.pojo.dto.ApiDTO;
import cn.com.sipsg.common.dynamic.core.strategy.DynamicExecuteStrategy;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.Method;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 代理动态执行策略
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Service
public class ProxyStrategyImpl implements DynamicExecuteStrategy {

    private static final int REQUEST_TIMEOUT = 120000;

    @Override
    public String dynamicServerType() {
        return DynamicConstants.SERVER_TYPE_PROXY;
    }

    @Override
    @DynamicExecuteResult
    public Object dynamicExecute(ApiDTO apiDTO, JSONObject params) {
        DynamicServer dynamicServer = apiDTO.getDynamicServer();
        JSONObject conditions = params.containsKey(DynamicConstants.CONDITIONS_KEY) ? params.getJSONObject(DynamicConstants.CONDITIONS_KEY) : new JSONObject();
        Map<String, Object> config = new HashMap<>();
        config.put("name", apiDTO.getApiName());
        List<Map<String, Object>> result = new ArrayList<>();
        // 根据资源id查询arcgis资源配置
        String proxyUrl = dynamicServer.getRequestUrl();
        String method = dynamicServer.getRequestMethod();
        String headers = dynamicServer.getRequestHeader();
        if (StrUtil.isNotEmpty(dynamicServer.getRequestBody())) {
            conditions.putAll(JSONUtil.parseObj(dynamicServer.getRequestBody()));
        }
        // String转map<String, String>
        Map<String, String> headerMap = new HashMap<>();
        if (JSONUtil.isTypeJSON(headers)) {
            JSONObject headerJson = JSONUtil.parseObj(headers);
            for (String key : headerJson.keySet()) {
                headerMap.put(key, headerJson.getStr(key));
            }
        }
        HttpRequest httpRequest;
        if (Method.valueOf(method) == Method.GET) {
            httpRequest = HttpRequest.get(proxyUrl).form(conditions);
        } else {
            httpRequest = HttpRequest.post(proxyUrl).body(JSONUtil.toJsonStr(conditions));
        }
        httpRequest.timeout(REQUEST_TIMEOUT).headerMap(headerMap, false);
        String resultStr;
        try (HttpResponse response = httpRequest.execute()) {
            resultStr = response.body();
        }
        config.put(DynamicConstants.RESULT_SET_KEY, resultStr);
        if (JSONUtil.isTypeJSON(resultStr)) {
            if (JSONUtil.isTypeJSONArray(resultStr)) {
                apiDTO.setQueryType(DynamicConstants.SERVER_TYPE_PROXY);
                config.put(DynamicConstants.RESULT_SET_KEY, JSONUtil.parseArray(resultStr));
            } else {
                config.put(DynamicConstants.RESULT_SET_KEY, JSONUtil.parseObj(resultStr));
            }
        }
        if (DynamicServerResultTypeEnums.PAGE == dynamicServer.getResultType()) {
            JSONArray jsonArray = new JSONArray();
            if (StrUtil.isNotEmpty(resultStr)) {
                jsonArray = JSONUtil.parseArray(resultStr);
            }
            config.put(DynamicConstants.RESULT_SET_KEY, jsonArray);
            config.put("count", jsonArray.size());
            config.put(PageConstants.CURRENT, conditions.getLong(PageConstants.CURRENT));
            config.put(PageConstants.SIZE, conditions.getLong(PageConstants.SIZE));
        }
        config.put("api", apiDTO);
        config.put("params", params);
        result.add(config);
        if (StrUtil.isNotEmpty(apiDTO.getQueryType())) {
            return result;
        } else {
            return JSONUtil.parse(resultStr);
        }
    }

}

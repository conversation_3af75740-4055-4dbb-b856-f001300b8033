package cn.com.sipsg.common.dynamic.core.filter;

import cn.com.sipsg.common.dynamic.core.constant.DynamicConstants;
import cn.hutool.core.collection.CollUtil;

import java.util.HashMap;
import java.util.List;

/**
 * Row 过滤器
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public class DynamicRowFilter implements DynamicFilter {

    @Override
    @SuppressWarnings("unchecked")
    public boolean doFilter(List results) {
        HashMap result = (HashMap)results.get(0);
        List<HashMap> dynamicResult = (List<HashMap>)result.get(DynamicConstants.RESULT_SET_KEY);
        if (CollUtil.isEmpty(dynamicResult)) {
            return false;
        }
        dynamicResult = dynamicResult.subList(0, 1);
        result.put(DynamicConstants.RESULT_SET_KEY, dynamicResult);
        return true;
    }

}

package cn.com.sipsg.common.dynamic.core.constant;

/**
 * 服务常量
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public interface DynamicConstants {

    /**
     * 动态参数格式
     */
    String DYNAMIC_PARAM_FORMAT = "#{%s}";

    /**
     * 查询参数键
     */
    String CONDITIONS_KEY = "conditions";

    /**
     * i查询属性查询标志和快查综合查询标志
     */
    String PROJECT_NAME_KEY = "project_name";

    /**
     * 配置
     */
    String PARAM_NULL_KEY = "paramNull";

    /**
     * 结果集键
     */
    String RESULT_SET_KEY = "resultSet";

    /**
     * 服务类型：表查询
     */
    String SERVER_TYPE_TABLE = "TABLE";

    /**
     * 服务类型：代理
     */
    String SERVER_TYPE_PROXY = "PROXY";

    /**
     * 服务类型：ARCGIS
     */
    String SERVER_TYPE_ARCGIS = "ARCGIS";

    /**
     * 查询类型：表
     */
    String QUERY_TYPE_TABLE = "TABLE";

    /**
     * 查询类型：SQL
     */
    String QUERY_TYPE_SQL = "SQL";

    /**
     * 查询功能：i查询
     */
    String API_FUNCTION_I = "I-QUERY";

    /**
     * 查询功能：快查
     */
    String API_FUNCTION_Q = "Q-QUERY";

    /**
     * 查询功能：空间导出查询
     */
    String API_FUNCTION_SHP_EXPORT = "QUERY-EXPORT";

    /**
     * 几何类型：面
     */
    String GEO_TYPE_SURFACE = "面";

    /**
     * 数据查询类型：文本
     */
    String DATA_TYPE_TEXT_BOX = "TextBox";

    /**
     * 数据查询类型：下拉框
     */
    String DATA_TYPE_COMBO_BOX = "ComboBox";

    /**
     * 数据查询类型：日期
     */
    String DATA_TYPE_DATE_PICKER = "DatePicker";

    /**
     * 数据查询类型：空间几何
     */
    String DATA_TYPE_GEOMETRY_BOX = "GeometryBox";

    /**
     * 数据查询类型：日期范围
     */
    String DATA_TYPE_DATE_PICKER_RANGE = "DatePickerRange";

    /**
     * 数据查询类型：数字区间
     */
    String DATA_TYPE_NUMBER_RANGE = "NumberRange";

    /**
     * 令牌类型：Bearer
     */
    String TOKEN_TYPE_BEARER = "BEARER";

    /**
     * 令牌类型：ARCGIS
     */
    String TOKEN_TYPE_ARCGIS = "ARCGIS";

    /**
     * 令牌类型：静态
     */
    String TOKEN_TYPE_STATIC = "STATIC";

    /**
     * 令牌类型：UPI
     */
    String TOKEN_TYPE_UPI = "UPI";

}

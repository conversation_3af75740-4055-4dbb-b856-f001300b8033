package cn.com.sipsg.common.dynamic.core.pojo.bo;

import cn.com.sipsg.common.validation.group.UpdateGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 模板字段配置保存BO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "模板字段配置保存BO")
public class DynamicTemplateFieldSaveBO {

    /**
     * 字段配置ID
     */
    @Schema(description = "字段配置ID")
    @NotBlank(message = "字段配置ID不能为空", groups = {UpdateGroup.class})
    private String id;

    /**
     * 模板ID
     */
    @Schema(description = "模板ID")
    @NotBlank(message = "模板ID不能为空", groups = {UpdateGroup.class})
    private String templateId;

    /**
     * 字段名称
     */
    @Schema(description = "字段名称")
    private String columnName;

    /**
     * 标签名称
     */
    @Schema(description = "标签名称")
    private String labelName;

    /**
     * 单位
     */
    @Schema(description = "单位")
    private String unit;

    /**
     * 精度或格式
     */
    @Schema(description = "精度或格式")
    private String precisionFormat;

    /**
     * 字典编码
     */
    @Schema(description = "字典编码")
    private String dictCode;

    /**
     * 快查是否可见
     */
    @Schema(description = "快查是否可见", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "快查是否可见不能为空")
    private Boolean qQueryVisible;

    /**
     * I查是否可见
     */
    @Schema(description = "I查是否可见", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "I查是否可见不能为空")
    private Boolean iQueryVisible;

    /**
     * 详情是否可见
     */
    @Schema(description = "详情是否可见", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "详情是否可见不能为空")
    private Boolean detailVisible;

    /**
     * 查询是否可见
     */
    @Schema(description = "查询是否可见", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "查询是否可见不能为空")
    private Boolean queryVisible;

    /**
     * 排序值
     */
    @Schema(description = "排序值", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "排序值不能为空")
    private Integer sort;

    /**
     * 数据类型
     */
    @Schema(description = "数据类型")
    private String dataType;

    /**
     * 默认值
     */
    @Schema(description = "默认值")
    private String defaultValue;

    /**
     * 地址前缀
     */
    @Schema(description = "地址前缀")
    private String urlPrefix;

    /**
     * 空值替换值
     */
    @Schema(description = "空值替换")
    private String emptyValueReplacement;

}
package cn.com.sipsg.common.dynamic.core.service.impl;

import cn.com.sipsg.common.datasource.core.entity.Datasource;
import cn.com.sipsg.common.datasource.core.service.DatasourceService;
import cn.com.sipsg.common.dynamic.core.constant.DynamicConstants;
import cn.com.sipsg.common.dynamic.core.entity.DynamicServer;
import cn.com.sipsg.common.dynamic.core.entity.DynamicTemplate;
import cn.com.sipsg.common.dynamic.core.entity.DynamicTemplateField;
import cn.com.sipsg.common.dynamic.core.enums.DynamicServerResultTypeEnums;
import cn.com.sipsg.common.dynamic.core.pojo.dto.ApiDTO;
import cn.com.sipsg.common.dynamic.core.pojo.dto.ApiQueryParamDTO;
import cn.com.sipsg.common.dynamic.core.pojo.dto.TokenDTO;
import cn.com.sipsg.common.dynamic.core.service.DynamicExecuteService;
import cn.com.sipsg.common.dynamic.core.service.DynamicServerService;
import cn.com.sipsg.common.dynamic.core.service.DynamicTemplateService;
import cn.com.sipsg.common.dynamic.core.service.DynamicTokenService;
import cn.com.sipsg.common.dynamic.core.strategy.DynamicExecuteContext;
import cn.com.sipsg.common.mybatis.core.query.LambdaQueryWrapperX;
import cn.com.sipsg.common.util.AssertUtils;
import cn.com.sipsg.module.system.api.upms.SysDictApi;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.db.meta.Column;
import cn.hutool.db.sql.LogicalOperator;
import cn.hutool.json.JSONObject;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 动态执行服务实现类
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Service
@RequiredArgsConstructor
public class DynamicExecuteServiceImpl implements DynamicExecuteService {

    private final DynamicServerService dynamicServerService;

    private final DynamicTemplateService dynamicTemplateService;

    private final DynamicTokenService dynamicTokenService;

    private final DatasourceService datasourceService;

    private final DynamicExecuteContext dynamicExecuteContext;

    @Resource
    private SysDictApi sysDictApi;

    @Override
    public Object dynamicExecute(String serverCode, JSONObject params, boolean shpExportFlag) {
        params = ObjectUtil.defaultIfNull(params, new JSONObject());
        List<DynamicServer> serverList = dynamicServerService.list(new LambdaQueryWrapperX<DynamicServer>().eq(DynamicServer::getServerCode, serverCode));
        AssertUtils.isTrue(CollUtil.isEmpty(serverList), String.format("服务编码：【%s】未找到关联服务", serverCode));
        DynamicServer dynamicServer = serverList.get(0);
        // 组装对应服务配置
        ApiDTO apiDTO = new ApiDTO();
        this.buildServerConfig(apiDTO, dynamicServer, params);
        if (shpExportFlag) {
            // 空间导出
            apiDTO.setApiFunction(DynamicConstants.API_FUNCTION_SHP_EXPORT);
            dynamicServer.setResultType(DynamicServerResultTypeEnums.GEO_JSON_DATA);
            this.buildServerConfig(apiDTO, dynamicServer, params);
            // 查询模板下的id关键字拼接查询
            this.buildIQueryIdParam(apiDTO);
        }
        return dynamicExecuteContext.dynamicExecute(dynamicServer.getServerType(), apiDTO, params);
    }

    /**
     * 构建服务配置
     *
     * @param apiDTO        服务配置
     * @param dynamicServer 动态服务
     * @param params        参数
     */
    @Override
    public void buildServerConfig(ApiDTO apiDTO, DynamicServer dynamicServer, JSONObject params) {
        apiDTO.setDynamicServer(dynamicServer);
        if (dynamicServer.getResultType() == DynamicServerResultTypeEnums.TEMPLATE && ObjectUtil.isNull(apiDTO.getDynamicTemplate())) {
            // 构建模板
            this.buildTemplate(apiDTO, dynamicServer.getTemplateId());
        }
        if (dynamicServer.getResultType() == DynamicServerResultTypeEnums.GEO_JSON_DATA) {
            // 配置查询参数
            List<ApiQueryParamDTO> queryParamList = apiDTO.getQueryParamList();
            if (ObjectUtil.isNull(queryParamList)) {
                queryParamList = new ArrayList<>();
                apiDTO.setQueryParamList(queryParamList);
            }
            if (DynamicConstants.SERVER_TYPE_TABLE.equals(dynamicServer.getServerType()) && DynamicConstants.QUERY_TYPE_TABLE.equalsIgnoreCase(dynamicServer.getQueryType())) {
                JSONObject conditions = params.containsKey(DynamicConstants.CONDITIONS_KEY) ? params.getJSONObject(DynamicConstants.CONDITIONS_KEY) : new JSONObject();
                List<Column> columnList = datasourceService.listTableColumn(dynamicServer.getDbId(), dynamicServer.getQuerySql());
                for (Column column : columnList) {
                    String columnName = column.getName();
                    if (conditions.get(columnName) != null) {
                        ApiQueryParamDTO apiQueryParamDTO = new ApiQueryParamDTO();
                        apiQueryParamDTO.setOperator(LogicalOperator.AND.toString());
                        apiQueryParamDTO.setCondition(columnName + " = #{" + columnName + "}");
                        apiQueryParamDTO.setParamName(columnName);
                        queryParamList.add(apiQueryParamDTO);
                    }
                }
            }
        }
        if (DynamicConstants.SERVER_TYPE_ARCGIS.equals(dynamicServer.getServerType())) {
            // 配置密钥
            this.setToken(dynamicServer.getTokenId(), apiDTO);
        }
        if (DynamicConstants.SERVER_TYPE_TABLE.equals(dynamicServer.getServerType())) {
            // 查询数据源
            Datasource datasource = datasourceService.getById(dynamicServer.getDbId());
            AssertUtils.isTrue(ObjectUtil.isNull(datasource), "关联数据源不存在");
            apiDTO.setDatasource(datasource);
            apiDTO.setQueryType(dynamicServer.getServerType());
        }
    }

    @Override
    public void buildTemplate(ApiDTO apiDTO, String templateId) {
        // 查询模板及关联字段和更多配置
        DynamicTemplate dynamicTemplate = dynamicTemplateService.getByIdDeep(templateId);
        AssertUtils.isTrue(ObjectUtil.isNull(dynamicTemplate), "关联模板不存在");
        apiDTO.setDynamicTemplate(dynamicTemplate);
        List<DynamicTemplateField> fieldList = dynamicTemplate.getFieldList();
        AssertUtils.isTrue(CollUtil.isEmpty(fieldList), "关联模板未配置字段");
        // 查询关联字典
        List<String> dictCodes = fieldList.stream().map(DynamicTemplateField::getDictCode).filter(StrUtil::isNotBlank).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(dictCodes)) {
            apiDTO.setDictMap(sysDictApi.getDictMapByDictCodes(dictCodes));
        }
    }

    /**
     * 获取密钥，配置密钥
     *
     * @param tokenId 密钥ID
     * @param apiDTO  服务DTO
     */
    private void setToken(String tokenId, ApiDTO apiDTO) {
        if (StrUtil.isBlank(tokenId)) {
            return;
        }
        TokenDTO sysTokenDTO = dynamicTokenService.getToken(tokenId);
        if (null != sysTokenDTO) {
            apiDTO.setToken(sysTokenDTO.getToken());
            apiDTO.setTokenType(sysTokenDTO.getTokenType());
        }
    }

    /**
     * 查询模板下关键字拼接查询
     *
     * @param apiDTO 服务配置
     */
    @Override
    public void buildIQueryIdParam(ApiDTO apiDTO) {
        DynamicTemplate dynamicTemplate = apiDTO.getDynamicTemplate();
        if (ObjectUtil.isNotNull(dynamicTemplate)) {
            List<ApiQueryParamDTO> queryParamList = apiDTO.getQueryParamList();
            if (CollUtil.isEmpty(queryParamList)) {
                queryParamList = new ArrayList<>();
            }
            ApiQueryParamDTO queryParamDto = new ApiQueryParamDTO();
            queryParamDto.setParamName(dynamicTemplate.getKeyColumnName());
            queryParamDto.setOperator(LogicalOperator.AND.toString());
            queryParamDto.setCondition(dynamicTemplate.getKeyColumnName() + " = " + String.format(DynamicConstants.DYNAMIC_PARAM_FORMAT, dynamicTemplate.getKeyColumnName()));
            queryParamList.add(queryParamDto);
            apiDTO.setQueryParamList(queryParamList);
        }
    }

}

package cn.com.sipsg.common.dynamic.core.service;

import cn.com.sipsg.common.dynamic.core.entity.DynamicServer;
import cn.com.sipsg.common.dynamic.core.pojo.bo.DynamicServerQueryBO;
import cn.com.sipsg.common.dynamic.core.pojo.bo.DynamicServerSaveBO;
import cn.com.sipsg.common.dynamic.core.pojo.vo.DynamicServerVO;
import cn.com.sipsg.common.dynamic.core.pojo.vo.ServerListResultVO;
import cn.com.sipsg.common.mybatis.core.service.BaseServiceX;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;

import java.util.List;
import java.util.Set;

/**
 * 服务服务接口
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public interface DynamicServerService extends BaseServiceX<DynamicServer> {

    /**
     * 查询服务分页列表
     *
     * @param bo 参数
     * @return 服务分页列表
     */
    CommonPageVO<DynamicServerVO> page(DynamicServerQueryBO bo);

    /**
     * 新增服务
     *
     * @param bo 参数
     * @return 服务ID
     */
    String save(DynamicServerSaveBO bo);

    /**
     * 编辑服务
     *
     * @param bo 参数
     */
    void update(DynamicServerSaveBO bo);

    /**
     * 根据id查询服务详情
     *
     * @param id 主键
     * @return 服务详情
     */
    DynamicServerVO detail(String id);

    /**
     * 删除服务
     *
     * @param id 主键
     */
    void delete(String id);

    /**
     * 编辑服务状态
     *
     * @param id     主键
     * @param status 状态
     */
    void updateStatus(String id, Integer status);

    /**
     * 查询指定服务类型下的分组列表
     *
     * @param serverType 服务类型
     * @return 分组列表
     */
    Set<String> getGroupList(String serverType);

    /**
     * 查询关联服务列表
     *
     * @return 关联服务列表
     */
    List<ServerListResultVO> getServerList();
}
package cn.com.sipsg.common.dynamic.core.filter;

import cn.com.sipsg.common.dynamic.core.constant.DynamicConstants;
import cn.hutool.core.collection.CollUtil;

import cn.hutool.json.JSONObject;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

/**
 * GeoJsonData过滤器
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public class DynamicGeoJsonDataFilter implements DynamicFilter {

    @Override
    @SuppressWarnings("unchecked")
    public boolean doFilter(List results) {
        HashMap result = (HashMap)results.get(0);
        List<HashMap> dynamicResult = (List<HashMap>)result.get(DynamicConstants.RESULT_SET_KEY);
        if (CollUtil.isEmpty(dynamicResult)) {
            dynamicResult = Collections.emptyList();
        }
        List<HashMap> newDynamicResult = new ArrayList<>();
        JSONObject params = (JSONObject)result.get("params");
        HashMap geoJsonData = new HashMap();
        if (params != null) {
            JSONObject conditions = (JSONObject) params.get("conditions");
            if (conditions != null && conditions.containsKey("srid")) {
                geoJsonData.put("srid", conditions.getStr("srid"));
            }
        }
        geoJsonData.put("type", "FeatureCollection");
        geoJsonData.put("features", dynamicResult);
        newDynamicResult.add(geoJsonData);
        result.put(DynamicConstants.RESULT_SET_KEY, newDynamicResult);
        return true;
    }

}

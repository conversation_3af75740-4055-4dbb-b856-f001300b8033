package cn.com.sipsg.common.dynamic.core.entity;

import cn.com.sipsg.common.dynamic.core.enums.DynamicServerResultTypeEnums;
import cn.com.sipsg.common.enums.CommonStatusEnum;
import cn.com.sipsg.common.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;

/**
 * 服务
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@FieldNameConstants
@TableName(value = "medox_dynamic_server", autoResultMap = true)
public class DynamicServer extends BaseDO {

    /**
     * 服务ID
     */
    @TableId
    private String id;

    /**
     * 服务名称
     */
    private String serverName;

    /**
     * 服务编码
     */
    private String serverCode;

    /**
     * 服务类型;TABLE，ARCGIS，PROXY
     */
    private String serverType;

    /**
     * 状态
     */
    private CommonStatusEnum status;

    /**
     * 查询类型;TABLE，SQL
     */
    private String queryType;

    /**
     * 表名或查询语句
     */
    private String querySql;

    /**
     * 分组名称
     */
    private String groupName;

    /**
     * 返回结果类型
     */
    private DynamicServerResultTypeEnums resultType;

    /**
     * 数据源ID
     */
    private String dbId;

    /**
     * 请求地址
     */
    private String requestUrl;

    /**
     * 请求方法;GET，POST
     */
    private String requestMethod;

    /**
     * 请求头
     */
    private String requestHeader;

    /**
     * 请求体
     */
    private String requestBody;

    /**
     * 认证方式
     */
    private String authType;

    /**
     * 令牌ID
     */
    private String tokenId;

    /**
     * 层号
     */
    private Integer layerNo;

    /**
     * 过滤条件
     */
    private String filterCondition;

    /**
     * 模板ID
     */
    private String templateId;

    /**
     * 图表类型
     */
    private String chartType;

    /**
     * 图表标题
     */
    private String chartTitle;

    /**
     * 图表子标题
     */
    private String chartSubTitle;

    /**
     * 图表X坐标单位
     */
    private String chartXUnit;

    /**
     * 图表Y坐标单位
     */
    private String chartYUnit;

    /**
     * 备注
     */
    private String remark;

    /**
     * 空间字段配置
     */
    private String shapeColumn;

}
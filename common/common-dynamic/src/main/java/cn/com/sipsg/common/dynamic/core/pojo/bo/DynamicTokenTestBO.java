package cn.com.sipsg.common.dynamic.core.pojo.bo;

import cn.hutool.core.lang.RegexPool;
import com.alibaba.fastjson2.JSONObject;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * 密钥测试BO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "密钥测试BO")
public class DynamicTokenTestBO {

    /**
     * 密钥类型
     */
    @Schema(description = "密钥类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "密钥类型不能为空")
    private String tokenType;

    /**
     * 请求地址
     */
    @Schema(description = "请求地址", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "请求地址不能为空")
    @Pattern(regexp = RegexPool.URL_HTTP, message = "请求地址不正确")
    private String requestUrl;

    /**
     * 请求入参
     */
    @Schema(description = "请求入参")
    private JSONObject requestBody;

    /**
     * 请求方法
     */
    @Schema(description = "请求方法", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "请求方法不能为空")
    @Pattern(regexp = "(GET|POST)?", message = "请求方法不正确")
    private String requestMethod;

    /**
     * 请求头
     */
    @Schema(description = "请求头")
    private JSONObject requestHeader;

    /**
     * 请求超时时间;单位毫秒
     */
    @Schema(description = "请求超时时间;单位毫秒", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "请求超时时间不能为空")
    private Integer requestTimeout;

    /**
     * 令牌超时时间
     */
    @Schema(description = "令牌超时时间")
    private BigDecimal tokenExpiredMinutes;

}
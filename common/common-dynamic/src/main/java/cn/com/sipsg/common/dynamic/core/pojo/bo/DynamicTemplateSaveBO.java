package cn.com.sipsg.common.dynamic.core.pojo.bo;

import cn.com.sipsg.common.validation.group.UpdateGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.List;

/**
 * 模板保存BO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "模板保存BO")
public class DynamicTemplateSaveBO {

    /**
     * 模板ID
     */
    @Schema(description = "模板ID")
    @NotBlank(message = "模板ID不能为空", groups = {UpdateGroup.class})
    private String id;

    /**
     * 模板名称
     */
    @Schema(description = "模板名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "模板名称不能为空")
    private String templateName;

    /**
     * 关键字id字段名称
     */
    @Schema(description = "关键字id字段名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "关键字id字段名称不能为空")
    private String keyColumnName;

    /**
     * 显示字段名称
     */
    @Schema(description = "显示字段名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "显示字段名称不能为空")
    private String showColumnName;

    /**
     * 查询缓冲区
     */
    @Schema(description = "查询缓冲区")
    @Min(value = 0, message = "查询缓冲区不能小于0")
    private BigDecimal queryBuffer;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 模板字段配置
     */
    @Schema(description = "模板字段配置")
    private List<DynamicTemplateFieldSaveBO> fieldList;

    /**
     * 模板更多配置
     */
    @Schema(description = "模板更多配置")
    private List<DynamicTemplateExtraSaveBO> extraList;

}
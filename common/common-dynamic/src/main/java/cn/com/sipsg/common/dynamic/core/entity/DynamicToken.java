package cn.com.sipsg.common.dynamic.core.entity;

import cn.com.sipsg.common.mybatis.core.dataobject.BaseDO;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;

/**
 * 密钥
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@FieldNameConstants
@TableName(value = "medox_dynamic_token", autoResultMap = true)
public class DynamicToken extends BaseDO {

    /**
     * 密钥ID
     */
    @TableId
    private String id;

    /**
     * 密钥名称
     */
    private String tokenName;

    /**
     * 密钥编码
     */
    private String tokenCode;

    /**
     * 密钥类型
     */
    private String tokenType;

    /**
     * 请求地址
     */
    private String requestUrl;

    /**
     * 请求入参
     */
    @TableField(typeHandler = Fastjson2TypeHandler.class)
    private JSONObject requestBody;

    /**
     * 请求方法
     */
    private String requestMethod;

    /**
     * 请求头
     */
    @TableField(typeHandler = Fastjson2TypeHandler.class)
    private JSONObject requestHeader;

    /**
     * 请求超时时间;单位毫秒
     */
    private Integer requestTimeout;

    /**
     * 备注
     */
    private String remark;

    /**
     * 令牌超时时间
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal tokenExpiredMinutes;

}
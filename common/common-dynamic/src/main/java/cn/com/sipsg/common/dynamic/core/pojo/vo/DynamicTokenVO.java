package cn.com.sipsg.common.dynamic.core.pojo.vo;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fhs.core.trans.vo.VO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 密钥VO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "密钥VO")
public class DynamicTokenVO implements VO {

    /**
     * 密钥ID
     */
    @TableId
    @Schema(description = "密钥ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private String id;

    /**
     * 密钥名称
     */
    @Schema(description = "密钥名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String tokenName;

    /**
     * 密钥编码
     */
    @Schema(description = "密钥编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String tokenCode;

    /**
     * 密钥类型
     */
    @Schema(description = "密钥类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private String tokenType;

    /**
     * 请求地址
     */
    @Schema(description = "请求地址", requiredMode = Schema.RequiredMode.REQUIRED)
    private String requestUrl;

    /**
     * 请求入参
     */
    @Schema(description = "请求入参")
    private JSONObject requestBody;

    /**
     * 请求方法
     */
    @Schema(description = "请求方法", requiredMode = Schema.RequiredMode.REQUIRED)
    private String requestMethod;

    /**
     * 请求头
     */
    @Schema(description = "请求头")
    private JSONObject requestHeader;

    /**
     * 请求超时时间;单位毫秒
     */
    @Schema(description = "请求超时时间;单位毫秒", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer requestTimeout;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

    /**
     * 令牌超时时间
     */
    @Schema(description = "令牌超时时间")
    private BigDecimal tokenExpiredMinutes;

}
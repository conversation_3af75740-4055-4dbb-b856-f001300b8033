package cn.com.sipsg.common.dynamic.core.service.impl;

import cn.com.sipsg.common.dynamic.core.constant.DynamicConstants;
import cn.com.sipsg.common.dynamic.core.entity.DynamicToken;
import cn.com.sipsg.common.dynamic.core.mapper.DynamicTokenMapper;
import cn.com.sipsg.common.dynamic.core.pojo.bo.DynamicTokenQueryBO;
import cn.com.sipsg.common.dynamic.core.pojo.bo.DynamicTokenSaveBO;
import cn.com.sipsg.common.dynamic.core.pojo.bo.DynamicTokenTestBO;
import cn.com.sipsg.common.dynamic.core.pojo.dto.TokenDTO;
import cn.com.sipsg.common.dynamic.core.pojo.dto.UpiTokenDTO;
import cn.com.sipsg.common.dynamic.core.pojo.vo.DynamicTokenVO;
import cn.com.sipsg.common.dynamic.core.service.DynamicTokenService;
import cn.com.sipsg.common.exception.enums.ErrorCodeEnum;
import cn.com.sipsg.common.mybatis.core.query.LambdaQueryWrapperX;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.util.AssertUtils;
import cn.com.sipsg.common.util.BeanUtils;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.*;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;

/**
 * 密钥服务实现类
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Service
@RequiredArgsConstructor
public class DynamicTokenServiceImpl extends ServiceImpl<DynamicTokenMapper, DynamicToken> implements DynamicTokenService {

    private static final String TOKEN = "token";

    private static final String ACCESS_TOKEN = "access_token";

    private static final String STATIC = "static";

    private static final String BEARER = "Bearer ";

    private static final String CONTENT_TYPE = "content-type";

    @Override
    public CommonPageVO<DynamicTokenVO> page(DynamicTokenQueryBO bo) {
        LambdaQueryWrapperX<DynamicToken> queryWrapper = new LambdaQueryWrapperX<>();
        // 填充查询条件
        queryWrapper.likeIfPresent(DynamicToken::getTokenName, bo.getTokenName())
                .likeIfPresent(DynamicToken::getTokenCode, bo.getTokenCode())
                .eqIfPresent(DynamicToken::getTokenType, bo.getTokenType())
                .eqIfPresent(DynamicToken::getRequestMethod, bo.getRequestMethod());
        return this.page(bo, queryWrapper).convert(entity -> BeanUtils.copyProperties(entity, DynamicTokenVO.class));
    }

    @Override
    @Transactional
    public String save(DynamicTokenSaveBO bo) {
        bo.setId(null);
        // 校验密钥
        checkSaveOrUpdate(null, bo.getTokenCode());
        // 新增密钥
        DynamicToken dynamicToken = BeanUtils.copyProperties(bo, DynamicToken.class);
        this.save(dynamicToken);
        return dynamicToken.getId();
    }

    @Override
    @Transactional
    public void update(DynamicTokenSaveBO bo) {
        // 校验密钥
        checkSaveOrUpdate(bo.getId(), bo.getTokenCode());
        // 编辑密钥
        DynamicToken dynamicToken = BeanUtils.copyProperties(bo, DynamicToken.class);
        this.updateById(dynamicToken);
    }

    @Override
    public DynamicTokenVO detail(String id) {
        DynamicToken dynamicToken = this.getById(id);
        // 校验是否存在
        AssertUtils.isTrue(dynamicToken == null, ErrorCodeEnum.DATA_NOT_EXIST);
        return BeanUtils.copyProperties(dynamicToken, DynamicTokenVO.class);
    }

    @Override
    @Transactional
    public void delete(String id) {
        DynamicToken dynamicToken = this.getById(id);
        if (dynamicToken != null) {
            // 删除密钥
            this.removeById(id);
        }
    }

    @Override
    public TokenDTO getToken(String id) {
        DynamicToken dynamicToken = this.getById(id);
        AssertUtils.isTrue(ObjectUtil.isNull(dynamicToken), "密钥不存在");
        return getToken(dynamicToken);
    }

    @Override
    public TokenDTO testToken(DynamicTokenTestBO bo) {
        DynamicToken dynamicToken = BeanUtils.copyProperties(bo, DynamicToken.class);
        TokenDTO tokenDTO = getToken(dynamicToken);
        AssertUtils.isTrue(ObjectUtil.isNull(tokenDTO), "测试密钥失败");
        return tokenDTO;
    }

    @SuppressWarnings("unchecked")
    private TokenDTO getToken(DynamicToken dynamicToken) {
        JSONObject requestBody = dynamicToken.getRequestBody();
        JSONObject requestHeader = dynamicToken.getRequestHeader();
        Map<String, String> headerMap = new HashMap<>();
        if (requestHeader != null) {
            headerMap = Convert.convert(HashMap.class, requestHeader);
        }
        String tokenType = dynamicToken.getTokenType();
        if (DynamicConstants.TOKEN_TYPE_STATIC.equals(tokenType)) {
            if (requestBody != null && requestBody.get(STATIC) != null) {
                TokenDTO tokenDTO = new TokenDTO();
                tokenDTO.setTokenType(tokenType);
                tokenDTO.setToken(requestBody.get(STATIC));
                return tokenDTO;
            }
        }
        String contentType = headerMap.get(CONTENT_TYPE);
        HttpRequest request = HttpUtil.createRequest(Method.valueOf(dynamicToken.getRequestMethod()), dynamicToken.getRequestUrl())
                .timeout(dynamicToken.getRequestTimeout())
                .headerMap(headerMap, false);
        if (ContentType.FORM_URLENCODED.toString().equalsIgnoreCase(contentType)) {
            request.form(requestBody);
        } else {
            request.body(JSON.toJSONString(requestBody));
        }
        try (HttpResponse res = request.execute()) {
            if (res != null && res.getStatus() == HttpStatus.HTTP_OK) {
                String result = res.body();
                if (StrUtil.isNotBlank(result)) {
                    TokenDTO tokenDTO = new TokenDTO();
                    tokenDTO.setTokenType(tokenType);
                    if (DynamicConstants.TOKEN_TYPE_ARCGIS.equals(tokenType)) {
                        tokenDTO.setToken(result);
                        return tokenDTO;
                    } else if (DynamicConstants.TOKEN_TYPE_UPI.equals(tokenType)) {
                        Object o = JSON.parse(result);
                        UpiTokenDTO upiTokenDto = Convert.convert(UpiTokenDTO.class, o);
                        tokenDTO.setToken(upiTokenDto.getToken());
                        return tokenDTO;
                    } else if (DynamicConstants.TOKEN_TYPE_BEARER.equals(tokenType)) {
                        JSONObject jsonObject = JSON.parseObject(result);
                        if (jsonObject.get(TOKEN) != null) {
                            tokenDTO.setToken(BEARER + jsonObject.get(TOKEN).toString());
                        } else if (jsonObject.get(ACCESS_TOKEN) != null) {
                            tokenDTO.setToken(BEARER + jsonObject.get(ACCESS_TOKEN).toString());
                        }
                        return tokenDTO;
                    } else {
                        JSONObject jsonObject = JSON.parseObject(result);
                        if (jsonObject.get(TOKEN) != null) {
                            tokenDTO.setToken(jsonObject.get(TOKEN).toString());
                        } else if (jsonObject.get(ACCESS_TOKEN) != null) {
                            tokenDTO.setToken(jsonObject.get(ACCESS_TOKEN).toString());
                        } else {
                            tokenDTO.setToken(jsonObject);
                        }
                        return tokenDTO;
                    }
                }
            }
        }
        return null;
    }

    private void checkSaveOrUpdate(String id, String tokenCode) {
        // 校验密钥存在
        checkExist(id);
        // 校验密钥编码唯一性
        checkTokenCodeUnique(id, tokenCode);
    }

    private void checkTokenCodeUnique(String id, String tokenCode) {
        long count = this.count(new LambdaQueryWrapperX<DynamicToken>().eq(DynamicToken::getTokenCode, tokenCode).neIfPresent(DynamicToken::getId, id));
        AssertUtils.isTrue(count > 0, String.format("密钥编码【%s】已存在", tokenCode));
    }

}
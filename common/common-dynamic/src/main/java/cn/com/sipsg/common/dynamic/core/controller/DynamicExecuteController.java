package cn.com.sipsg.common.dynamic.core.controller;

import cn.com.sipsg.common.dynamic.core.constant.DynamicConstants;
import cn.com.sipsg.common.dynamic.core.service.DynamicExecuteService;
import cn.com.sipsg.common.operationlog.core.annotation.OperationLog;
import cn.com.sipsg.common.operationlog.core.enums.OperationTypeEnum;
import cn.com.sipsg.common.pojo.CommonResult;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.json.JSONObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 动态执行控制器
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Tag(name = "动态执行")
@RestController
@RequiredArgsConstructor
@RequestMapping("/dynamic")
public class DynamicExecuteController {

    private final DynamicExecuteService dynamicExecuteService;

    private final HttpServletRequest request;

    /**
     * 动态执行
     *
     * @param serverCode 服务编码
     * @param params     参数
     * @return 结果
     */
    @Operation(summary = "动态执行")
    @Parameter(name = "serverCode", description = "服务编码", required = true)
    @OperationLog(module = "动态执行", value = "动态执行", type = OperationTypeEnum.OTHER, saveResponse = false)
    @PostMapping(value = "/execute/{serverCode}")
    public CommonResult<Object> execute(@PathVariable String serverCode, @RequestBody(required = false) JSONObject params) {
        return CommonResult.data(dynamicExecuteService.dynamicExecute(serverCode, params, false));
    }

    /**
     * 动态执行
     *
     * @param serverCode 服务编码
     * @return 结果
     */
    @Operation(summary = "动态执行")
    @Parameter(name = "serverCode", description = "服务编码", required = true)
    @OperationLog(module = "动态执行", value = "动态执行", type = OperationTypeEnum.OTHER, saveResponse = false)
    @GetMapping(value = "/execute/{serverCode}")
    public CommonResult<Object> execute(@PathVariable String serverCode) {
        JSONObject params = new JSONObject();
        params.set(DynamicConstants.CONDITIONS_KEY, ServletUtil.getParamMap(request));
        return CommonResult.data(dynamicExecuteService.dynamicExecute(serverCode, params, false));
    }

}
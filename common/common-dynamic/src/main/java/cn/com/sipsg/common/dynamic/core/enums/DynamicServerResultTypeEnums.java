package cn.com.sipsg.common.dynamic.core.enums;

import cn.com.sipsg.common.enums.BaseStrEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 动态服务结果类型枚举
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@RequiredArgsConstructor
public enum DynamicServerResultTypeEnums implements BaseStrEnum {

    COMMON_JSON("commonJson", "通用对象数组"),

    ROW("row", "单项目数组"),

    GEO_JSON("geoJson", "空间转换对象数组"),

    GEO_JSON_DATA("geoJsonData", "空间转换对象集合"),

    TREE("tree", "树形结构数组"),

    PAGE("page", "分页对象数组"),

    TEMPLATE("template", "模板数组"),

    CHART("chart", "图表数组"),;

    @EnumValue
    @JsonValue
    private final String code;

    private final String desc;

}

package cn.com.sipsg.common.dynamic.core.pojo.dto;

import cn.com.sipsg.common.constant.PageConstants;
import cn.com.sipsg.common.datasource.core.entity.Datasource;
import cn.com.sipsg.common.datasource.core.enums.DbTypeEnum;
import cn.com.sipsg.common.dynamic.core.entity.DynamicServer;
import cn.com.sipsg.common.dynamic.core.entity.DynamicTemplate;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * API DTO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
public class ApiDTO {

    /**
     * 服务编码
     */
    private String apiCode;

    /**
     * 服务id
     */
    private String apiId;

    /**
     * 服务名称
     */
    private String apiName;

    /**
     * 查询方式
     */
    private String queryType;

    /**
     * 服务配置
     */
    private DynamicServer dynamicServer;

    /**
     * 模板配置
     */
    private DynamicTemplate dynamicTemplate;

    /**
     * 数据源
     */
    private Datasource datasource;

    /**
     * 服务功能
     */
    private String apiFunction;

    /**
     * 密钥值
     */
    private Object token;

    /**
     * 密钥类型
     */
    private String tokenType;

    /**
     * 图形要素类型
     */
    private String geoType;

    /**
     * 数据库类型
     */
    private DbTypeEnum dbType;

    /**
     * 页码
     */
    private Long current = PageConstants.DEFAULT_CURRENT;

    /**
     * 每页条数
     */
    private Long size = PageConstants.DEFAULT_SIZE;

    /**
     * 查询参数列表
     */
    private List<ApiQueryParamDTO> queryParamList;

    /**
     * 开启单体化
     */
    private Boolean classifyEnabled;

    /**
     * 图层资源ID
     */
    private String layerResourceId;

    /**
     * 是否详情
     */
    private Boolean detail;

    /**
     * 字典集合
     */
    private Map<String, Map<String, String>> dictMap;

}

package cn.com.sipsg.common.dynamic.core.filter;

import cn.com.sipsg.common.constant.PageConstants;
import cn.com.sipsg.common.dynamic.core.constant.DynamicConstants;
import cn.com.sipsg.common.dynamic.core.pojo.dto.ApiDTO;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;

/**
 * 物理Page过滤器
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public class DynamicPhysicsPageFilter implements DynamicFilter {

    @Override
    @SuppressWarnings("unchecked")
    public boolean doFilter(List results) {
        List<Object> objects = new ArrayList<>();
        HashMap result = (HashMap)results.get(0);
        ApiDTO api = (ApiDTO)result.get("api");
        if (ObjectUtil.isNull(api)) {
            return false;
        }
        List<HashMap> pageList = new ArrayList<>();
        List<HashMap> dynamicResult = (List<HashMap>)result.get(DynamicConstants.RESULT_SET_KEY);
        int current = Optional.ofNullable(result.get(PageConstants.CURRENT)).map(obj -> ((Number)obj).intValue()).orElse(api.getCurrent().intValue());
        int size = Optional.ofNullable(result.get(PageConstants.SIZE)).map(obj -> ((Number)obj).intValue()).orElse(api.getSize().intValue());
        if (CollUtil.isNotEmpty(dynamicResult)) {
            pageList = CollUtil.page(current, size, dynamicResult);
        }
        CommonPageVO<HashMap> commonPageVo = CommonPageVO.<HashMap>builder()
            .current(current)
            .size(size)
            .total(dynamicResult.size())
            .records(pageList)
            .build();
        objects.add(commonPageVo);
        result.put(DynamicConstants.RESULT_SET_KEY, objects);
        return true;
    }
}

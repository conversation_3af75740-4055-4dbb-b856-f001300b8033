package cn.com.sipsg.common.dynamic.core.service;

import cn.com.sipsg.common.dynamic.core.entity.DynamicServer;
import cn.com.sipsg.common.dynamic.core.pojo.dto.ApiDTO;
import cn.hutool.json.JSONObject;

/**
 * 动态执行服务接口
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public interface DynamicExecuteService {

    /**
     * 动态执行
     *
     * @param serverCode    服务编码
     * @param params        参数
     * @param shpExportFlag 是否为shp空间导出
     * @return 执行结果
     */
    Object dynamicExecute(String serverCode, JSONObject params, boolean shpExportFlag);

    /**
     * 构建服务配置
     *
     * @param apiDTO        服务配置
     * @param dynamicServer 动态服务
     * @param params        参数
     */
    void buildServerConfig(ApiDTO apiDTO, DynamicServer dynamicServer, JSONObject params);

    /**
     * 构建模板
     *
     * @param apiDTO     服务配置
     * @param templateId 模板ID
     */
    void buildTemplate(ApiDTO apiDTO, String templateId);

    /**
     * 查询模板下关键字拼接查询
     *
     * @param apiDTO 服务配置
     */
    void buildIQueryIdParam(ApiDTO apiDTO);

}

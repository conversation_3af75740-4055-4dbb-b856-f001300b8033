package cn.com.sipsg.common.dynamic.core.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <p>
 * 关联服务VO
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "关联服务VO")
public class ServerListResultVO {

    /**
     * 主键id
     */
    @Schema(description = "主键id")
    private String id;

    /**
     * 服务名称
     */
    @Schema(description = "服务名称")
    private String serverName;

    /**
     * 服务名称
     */
    @Schema(description = "服务编码")
    private String serverCode;

    /**
     * 服务类型
     */
    @Schema(description = "服务类型")
    private String serverType;

    /**
     * 子数据
     */
    @Schema(description = "子数据")
    private List<ServerListResultVO> children;
}

package cn.com.sipsg.common.dynamic.core.strategy;

import cn.com.sipsg.common.dynamic.core.pojo.dto.ApiDTO;
import cn.com.sipsg.common.util.AssertUtils;
import cn.com.sipsg.common.util.CollectionUtils;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 动态执行上下文
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Service
public class DynamicExecuteContext {

    private final Map<String, DynamicExecuteStrategy> dynamicQueryStrategyMap;

    public DynamicExecuteContext(List<DynamicExecuteStrategy> dynamicExecuteStrategyList) {
        this.dynamicQueryStrategyMap = CollectionUtils.convertMap(dynamicExecuteStrategyList, DynamicExecuteStrategy::dynamicServerType);
    }

    /**
     * 动态执行
     *
     * @param serverType 服务类型
     * @param apiDTO     服务配置
     * @param params     参数
     * @return 执行结果
     */
    public Object dynamicExecute(String serverType, ApiDTO apiDTO, JSONObject params) {
        DynamicExecuteStrategy dynamicExecuteStrategy = dynamicQueryStrategyMap.get(serverType);
        AssertUtils.isTrue(ObjectUtil.isNull(dynamicExecuteStrategy), String.format("未知的服务类型【%s】", serverType));
        if (ObjectUtil.isNull(params)) {
            params = new JSONObject();
        }
        return dynamicExecuteStrategy.dynamicExecute(apiDTO, params);
    }

}

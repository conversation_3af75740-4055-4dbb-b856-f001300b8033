package cn.com.sipsg.common.dynamic.core.ext.impl;

import cn.com.sipsg.common.datasource.core.ext.DatasourceExt;
import cn.com.sipsg.common.dynamic.core.constant.DynamicConstants;
import cn.com.sipsg.common.dynamic.core.entity.DynamicServer;
import cn.com.sipsg.common.dynamic.core.mapper.DynamicServerMapper;
import cn.com.sipsg.common.mybatis.core.query.LambdaQueryWrapperX;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 数据源拓展动态服务实现类
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Component
@RequiredArgsConstructor
public class DatasourceExtDynamicImpl implements DatasourceExt {

    private final DynamicServerMapper dynamicServerMapper;

    @Override
    public boolean inUse(String datasourceId) {
        LambdaQueryWrapperX<DynamicServer> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eq(DynamicServer::getDbId, datasourceId)
                .eq(DynamicServer::getServerType, DynamicConstants.SERVER_TYPE_TABLE);
        return dynamicServerMapper.selectCount(queryWrapper) > 0;
    }

}

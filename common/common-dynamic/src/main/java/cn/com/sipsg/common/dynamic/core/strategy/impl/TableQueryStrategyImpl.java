package cn.com.sipsg.common.dynamic.core.strategy.impl;

import static cn.com.sipsg.common.constant.CommonConstants.DEFAULT_SHAPE_COLUMN;

import cn.com.sipsg.common.constant.PageConstants;
import cn.com.sipsg.common.datasource.core.entity.Datasource;
import cn.com.sipsg.common.datasource.core.enums.DbTypeEnum;
import cn.com.sipsg.common.datasource.core.service.DatasourceService;
import cn.com.sipsg.common.datasource.core.strategy.DatasourceStrategy;
import cn.com.sipsg.common.datasource.core.strategy.JdbcConfig;
import cn.com.sipsg.common.datasource.core.util.DatasourceUtils;
import cn.com.sipsg.common.dynamic.core.aop.DynamicExecuteResult;
import cn.com.sipsg.common.dynamic.core.constant.DynamicConstants;
import cn.com.sipsg.common.dynamic.core.entity.DynamicServer;
import cn.com.sipsg.common.dynamic.core.entity.DynamicTemplate;
import cn.com.sipsg.common.dynamic.core.entity.DynamicTemplateField;
import cn.com.sipsg.common.dynamic.core.enums.DynamicServerResultTypeEnums;
import cn.com.sipsg.common.dynamic.core.pojo.dto.ApiDTO;
import cn.com.sipsg.common.dynamic.core.pojo.dto.ApiQueryParamDTO;
import cn.com.sipsg.common.dynamic.core.strategy.DynamicExecuteStrategy;
import cn.com.sipsg.common.mybatis.core.util.MyBatisUtils;
import cn.com.sipsg.common.util.AssertUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.PageUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.db.meta.Column;
import cn.hutool.json.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.apache.ibatis.jdbc.SQL;
import org.springframework.stereotype.Service;

/**
 * 表查询动态执行策略
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Service
@RequiredArgsConstructor
public class TableQueryStrategyImpl implements DynamicExecuteStrategy {

    /**
     * 表查询最大结果数量
     */
    private static final int TABLE_QUERY_MAX_RESULT_SIZE = 1000;

    private final DatasourceService datasourceService;

    @Override
    public String dynamicServerType() {
        return DynamicConstants.SERVER_TYPE_TABLE;
    }

    @Override
    @DS("#apiDTO.datasource.id")
    @DynamicExecuteResult
    public Object dynamicExecute(ApiDTO apiDTO, JSONObject params) {
        // 查询条件
        JSONObject conditions = params.containsKey(DynamicConstants.CONDITIONS_KEY) ? params.getJSONObject(DynamicConstants.CONDITIONS_KEY) : new JSONObject();
        // 服务
        DynamicServer dynamicServer = apiDTO.getDynamicServer();
        //查询空间字段
        String shapeColumn = dynamicServer.getShapeColumn();
        if (StrUtil.isBlank(shapeColumn)) {
            shapeColumn = DEFAULT_SHAPE_COLUMN;
        }
        // 查询语句
        String querySql = dynamicServer.getQuerySql();
        // 数据源
        Datasource datasource = apiDTO.getDatasource();
        // 查询类型
        String queryType = dynamicServer.getQueryType();
        // 结果类型
        DynamicServerResultTypeEnums resultType = dynamicServer.getResultType();
        // 数据库类型
        DbTypeEnum dbType = datasource.getDbType();
        apiDTO.setDbType(dbType);
        // 数据库模式
        DatasourceStrategy datasourceStrategy = DatasourceUtils.getDatasourceStrategy(dbType);
        com.alibaba.fastjson2.JSONObject datasourceConfiguration = datasource.getConfiguration();
        JdbcConfig jdbcConfig = datasourceStrategy.getJdbcConfig(datasourceConfiguration);
        String schema = Optional.ofNullable(jdbcConfig.getSchema()).map(s -> s + StrUtil.DOT).orElse(StrUtil.EMPTY);
        if (StrUtil.isNotBlank(jdbcConfig.getFuncSchema())) {
            schema = jdbcConfig.getFuncSchema() + StrUtil.DOT;
        }
        Map<String, Object> config = new HashMap<>();
        config.put("name", apiDTO.getApiName());
        config.put("apiId", apiDTO.getApiId());
        List<Map<String, Object>> resultList;
        if (DynamicConstants.API_FUNCTION_I.equals(apiDTO.getApiFunction())) {
            // i查询
            if (DynamicConstants.QUERY_TYPE_SQL.equals(queryType)) {
                querySql = "(" + querySql + ") t";
            }
            // 模板
            DynamicTemplate template = apiDTO.getDynamicTemplate();
            // 模板字段配置
            List<DynamicTemplateField> fieldList = new ArrayList<>();
            if (template != null) {
                fieldList = template.getFieldList();
            }
            boolean spatialQueryFlag = false;
            if (conditions.containsKey(DynamicConstants.PROJECT_NAME_KEY)) {
                if (CollectionUtil.isNotEmpty(apiDTO.getQueryParamList()) && includeProjectName(apiDTO.getQueryParamList())) {
                    spatialQueryFlag = false;
                } else {
                    if (!(conditions.containsKey("x") && conditions.containsKey("y") && conditions.containsKey("buffer"))) {
                        spatialQueryFlag = false;
                    } else {
                        spatialQueryFlag = true;
                    }
                }
            } else {
                spatialQueryFlag = true;
            }
            if (spatialQueryFlag) {
                // i查询情况下空间查询
                // 查询字段
                String finalShapeColumn = shapeColumn;
                List<String> selectFieldList = fieldList.stream().map(DynamicTemplateField::getColumnName).filter(columnName -> !StrUtil.equalsIgnoreCase(columnName, finalShapeColumn)).collect(Collectors.toList());
                // SQL语句构造
                SQL sql = new SQL();
                if (CollUtil.isNotEmpty(selectFieldList)) {
                    sql.SELECT(ArrayUtil.toArray(selectFieldList, String.class));
                } else {
                    sql.SELECT("*");
                }
                // Oracle SDE处理
                if (DbTypeEnum.ORACLE_SDE == dbType) {
                    sql.SELECT("TO_CLOB(SDE.ST_ASTEXT(" + shapeColumn + ")) AS " + shapeColumn);
                } else {
                    sql.SELECT(shapeColumn);
                }
                sql.FROM(querySql);
                if (StrUtil.isNotBlank(apiDTO.getGeoType())) {
                    // 获取srid
                    handleSrid(querySql, schema, dbType, conditions, shapeColumn);
                    // 判断平面和地理坐标
                    Double stx = Optional.ofNullable(getStx(querySql, schema, dbType, shapeColumn)).orElse(0d);
                    // 处理相交
                    String intersectSql = buildIntersectSql(apiDTO, sql, schema, dbType, conditions, stx, shapeColumn);
                    // 执行count
                    String countSql = new SQL()
                        .SELECT("COUNT(1)")
                        .FROM(querySql)
                        .WHERE(intersectSql)
                        .toString();
                    int count = Optional.ofNullable(MyBatisUtils.selectOne(countSql, conditions, Integer.class)).orElse(0);
                    config.put("count", count);
                    if (DynamicServerResultTypeEnums.PAGE == resultType) {
                        String pageSql = buildPageSql(sql.toString(), datasourceStrategy, conditions, config);
                        resultList = MyBatisUtils.selectList(pageSql, conditions);
                    } else {
                        resultList = MyBatisUtils.selectList(sql.toString(), conditions);
                    }
                    if (CollUtil.isNotEmpty(resultList)) {
                        if (DynamicConstants.QUERY_TYPE_TABLE.equals(queryType)) {
                            List<Map<String, Object>> mapList = new ArrayList<>();
                            List<Column> columnList = datasourceService.listTableColumn(datasource.getId(), querySql);
                            for (Map<String, Object> resultMap : resultList) {
                                Map<String, Object> map = new HashMap<>();
                                for (Column column : columnList) {
                                    String columnName = column.getName();
                                    map.put(columnName, resultMap.get(columnName));
                                }
                                mapList.add(map);
                            }
                            resultList = mapList;
                        }
                    }
                    config.put(DynamicConstants.RESULT_SET_KEY, resultList);
                }
            } else {
                // i查情况下的属性查询
                List<String> selectFieldList = fieldList.stream().map(DynamicTemplateField::getColumnName).collect(Collectors.toList());
                // SQL语句构造
                SQL sql = new SQL();
                sql.SELECT(ArrayUtil.toArray(selectFieldList, String.class));
                // 解析参数配置
                StringBuilder spliceParam = new StringBuilder();
                for (ApiQueryParamDTO queryParam : apiDTO.getQueryParamList()) {
                    //操作符 如：AND | OR
                    String operator = queryParam.getOperator();
                    //条件 如：name = :name
                    String sqlStr = queryParam.getCondition();
                    String paramStr = queryParam.getParamName();
                    String[] splitParams = paramStr.split(";");
                    if (containsAllQueryParam(conditions, splitParams, sqlStr) ||
                        (paramStr.equals(DynamicConstants.PARAM_NULL_KEY) && StrUtil.isNotEmpty(paramStr))) {
                        if (ObjectUtil.isNotEmpty(conditions.get(queryParam.getParamName()))) {
                            spliceParam.append(StrUtil.SPACE)
                                .append(operator)
                                .append(StrUtil.SPACE)
                                .append(sqlStr);
                        }
                    }
                }
                sql.FROM(querySql);
                StringBuilder newSpliceParam = null;
                if (StrUtil.isNotBlank(spliceParam)) {
                    newSpliceParam = formateSQL(spliceParam);
                    sql.WHERE("1 = 1" + newSpliceParam);
                }
                // 执行count
                int count = getCount(querySql, conditions, newSpliceParam);
                config.put("count", count);
                if (DynamicServerResultTypeEnums.PAGE == resultType) {
                    querySql = this.buildPageSql(sql.toString(), datasourceStrategy, conditions, config);
                } else if (count > TABLE_QUERY_MAX_RESULT_SIZE && DynamicServerResultTypeEnums.GEO_JSON_DATA != resultType) {
                    conditions.set(PageConstants.SIZE, TABLE_QUERY_MAX_RESULT_SIZE);
                    conditions.set(PageConstants.CURRENT, PageConstants.DEFAULT_CURRENT);
                    querySql = this.buildPageSql(sql.toString(), datasourceStrategy, conditions, config);
                } else {
                    querySql = sql.toString();
                }
                resultList = MyBatisUtils.selectList(querySql, conditions);
                config.put(DynamicConstants.RESULT_SET_KEY, resultList);
            }
        } else if (DynamicConstants.API_FUNCTION_SHP_EXPORT.equals(apiDTO.getApiFunction())) {
            if (DynamicConstants.QUERY_TYPE_SQL.equals(queryType)) {
                querySql = "(" + querySql + ") t";
            }
            // SQL语句构造
            SQL sql = new SQL();
            sql.SELECT("*");
            // Oracle SDE处理
            if (DbTypeEnum.ORACLE_SDE == dbType) {
                sql.SELECT("TO_CLOB(SDE.ST_ASTEXT(" + shapeColumn + ")) AS " + shapeColumn);
            } else {
                sql.SELECT(shapeColumn);
            }
            sql.FROM(querySql);
            // 获取srid
            handleSrid(querySql, schema, dbType, conditions, shapeColumn);
            // 判断平面和地理坐标
            Double stx = Optional.ofNullable(getStx(querySql, schema, dbType, shapeColumn)).orElse(0d);
            // 处理相交
            String intersectSql = " 1=1 ";
            if (StrUtil.isNotBlank(conditions.getStr("geometry"))) {
                intersectSql = buildIntersectSql(apiDTO, sql, schema, dbType, conditions, stx, shapeColumn);
            } else {
                //解析参数配置
                List<ApiQueryParamDTO> queryParamList = apiDTO.getQueryParamList();
                //拼接sql
                StringBuilder spliceParam = new StringBuilder();
                if (CollUtil.isNotEmpty(queryParamList)) {
                    queryParamList.forEach(queryParam -> {
                        //操作符 如 AND | OR
                        String operator = queryParam.getOperator();
                        //配置查询的条件 如 name= #{name}
                        String sqlStr = queryParam.getCondition();
                        String paramStr = queryParam.getParamName();
                        String[] splitParams = paramStr.split(";");
                        if (containsAllQueryParam(conditions, splitParams, sqlStr) || paramStr.equals(DynamicConstants.PARAM_NULL_KEY) && StrUtil.isNotEmpty(sqlStr)) {
                            spliceParam.append(StrUtil.SPACE).append(operator).append(StrUtil.SPACE).append(sqlStr);
                        }
                    });
                }
                if (StrUtil.isNotBlank(spliceParam)) {
                    intersectSql += formateSQL(spliceParam).toString();
                }
            }
            sql.WHERE(intersectSql);
            // 执行count
            String countSql = new SQL()
                .SELECT("COUNT(1)")
                .FROM(querySql)
                .WHERE(intersectSql)
                .toString();
            int count = Optional.ofNullable(MyBatisUtils.selectOne(countSql, conditions, Integer.class)).orElse(0);
            config.put("count", count);
            if (DynamicServerResultTypeEnums.PAGE == resultType) {
                String pageSql = buildPageSql(sql.toString(), datasourceStrategy, conditions, config);
                resultList = MyBatisUtils.selectList(pageSql, conditions);
            } else {
                resultList = MyBatisUtils.selectList(sql.toString(), conditions);
            }
            if (CollUtil.isNotEmpty(resultList)) {
                if (DynamicConstants.QUERY_TYPE_TABLE.equals(queryType)) {
                    List<Map<String, Object>> mapList = new ArrayList<>();
                    List<Column> columnList = datasourceService.listTableColumn(datasource.getId(), querySql);
                    for (Map<String, Object> resultMap : resultList) {
                        Map<String, Object> map = new HashMap<>();
                        for (Column column : columnList) {
                            String columnName = column.getName();
                            map.put(columnName, resultMap.get(columnName));
                        }
                        mapList.add(map);
                    }
                    resultList = mapList;
                }
            }
            config.put(DynamicConstants.RESULT_SET_KEY, resultList);
        } else if (DynamicConstants.QUERY_TYPE_TABLE.equals(queryType)) {
            // 查询类型是表
            if (StrUtil.isNotBlank(apiDTO.getGeoType())) {
                // 处理srid
                handleSrid(querySql, schema, dbType, conditions, shapeColumn);
            }
            // 解析参数配置
            List<ApiQueryParamDTO> queryParamList = apiDTO.getQueryParamList();
            StringBuilder spliceParam = new StringBuilder();
            if (CollectionUtil.isNotEmpty(queryParamList)) {
                queryParamList.forEach(queryParam -> {
                    // 操作符 如：AND | OR
                    String operator = queryParam.getOperator();
                    // 条件 如：name = #{name}
                    String sqlStr = queryParam.getCondition();
                    String paramStr = queryParam.getParamName();
                    String[] splitParams = paramStr.split(";");
                    String dataType = queryParam.getDataType();
                    boolean geometryBoxFlag = false;
                    if (StrUtil.isNotBlank(dataType) && dataType.contains(DynamicConstants.DATA_TYPE_GEOMETRY_BOX)) {
                        Double stx = conditions.getDouble("stx");
                        BigDecimal buffer = conditions.getBigDecimal("buffer");
                        DynamicTemplate template = apiDTO.getDynamicTemplate();
                        if (ObjectUtil.isNotNull(template) && ObjectUtil.isNotNull(template.getQueryBuffer()) && template.getQueryBuffer().compareTo(BigDecimal.ZERO) == 0) {
                            buffer = template.getQueryBuffer();
                            conditions.set("buffer", buffer);
                        }
                        if (StrUtil.containsIgnoreCase(conditions.getStr(paramStr), "point")) {
                            if (buffer.compareTo(BigDecimal.ONE) >= 0) {
                                if (stx > 180 && dataType.equals(DynamicConstants.DATA_TYPE_GEOMETRY_BOX + "_pointPlane")) {
                                    geometryBoxFlag = true;
                                } else if (stx <= 180 && dataType.equals(DynamicConstants.DATA_TYPE_GEOMETRY_BOX + "_pointCoordinate")) {
                                    geometryBoxFlag = true;
                                }
                            } else {
                                if (dataType.equals(DynamicConstants.DATA_TYPE_GEOMETRY_BOX + "_pointPlane")) {
                                    geometryBoxFlag = true;
                                }
                            }
                        } else {
                            if (dataType.equals(DynamicConstants.DATA_TYPE_GEOMETRY_BOX)) {
                                geometryBoxFlag = true;
                            }
                        }
                    } else {
                        geometryBoxFlag = true;
                    }
                    if (geometryBoxFlag && (containsAllQueryParam(conditions, splitParams, sqlStr) || (paramStr.equals(DynamicConstants.PARAM_NULL_KEY) && StrUtil.isNotEmpty(paramStr)))) {
                        spliceParam.append(StrUtil.SPACE).append(operator).append(StrUtil.SPACE).append(sqlStr);
                    }
                });
            }
            // sql语句构造
            SQL sql = new SQL();
            sql.SELECT("*").FROM(querySql);
            StringBuilder newSpliceParam = null;
            if (StrUtil.isNotBlank(spliceParam)) {
                newSpliceParam = formateSQL(spliceParam);
                sql.WHERE("1 = 1" + newSpliceParam);
            }
            // 执行count
            int count = getCount(querySql, conditions, newSpliceParam);
            config.put("count", count);
            if (DynamicServerResultTypeEnums.PAGE == resultType) {
                querySql = this.buildPageSql(sql.toString(), datasourceStrategy, conditions, config);
            } else if (count > TABLE_QUERY_MAX_RESULT_SIZE && DynamicServerResultTypeEnums.GEO_JSON_DATA != resultType) {
                conditions.set(PageConstants.SIZE, TABLE_QUERY_MAX_RESULT_SIZE);
                conditions.set(PageConstants.CURRENT, PageConstants.DEFAULT_CURRENT);
                querySql = this.buildPageSql(sql.toString(), datasourceStrategy, conditions, config);
            } else {
                querySql = sql.toString();
            }

            resultList = MyBatisUtils.selectList(querySql, conditions);
            config.put(DynamicConstants.RESULT_SET_KEY, resultList);
        } else {
            // 解析参数配置
            List<ApiQueryParamDTO> queryParamList = apiDTO.getQueryParamList();
            StringBuilder spliceParam = new StringBuilder();
            if (CollectionUtil.isNotEmpty(queryParamList)) {
                queryParamList.forEach(queryParam -> {
                    // 操作符 如：AND | OR
                    String operator = queryParam.getOperator();
                    // 条件 如：name = #{name}
                    String sqlStr = queryParam.getCondition();
                    String paramStr = queryParam.getParamName();
                    String[] splitParams = paramStr.split(";");
                    String dataType = queryParam.getDataType();
                    boolean geometryBoxFlag = false;
                    if (StrUtil.isNotBlank(dataType) && dataType.contains(DynamicConstants.DATA_TYPE_GEOMETRY_BOX)) {
                        Double stx = conditions.getDouble("stx");
                        BigDecimal buffer = conditions.getBigDecimal("buffer");
                        DynamicTemplate template = apiDTO.getDynamicTemplate();
                        if (ObjectUtil.isNotNull(template) && ObjectUtil.isNotNull(template.getQueryBuffer()) && template.getQueryBuffer().compareTo(BigDecimal.ZERO) == 0) {
                            buffer = template.getQueryBuffer();
                            conditions.set("buffer", buffer);
                        }
                        if (StrUtil.containsIgnoreCase(conditions.getStr(paramStr), "point")) {
                            if (buffer.compareTo(BigDecimal.ONE) >= 0) {
                                if (stx > 180 && dataType.equals(DynamicConstants.DATA_TYPE_GEOMETRY_BOX + "_pointPlane")) {
                                    geometryBoxFlag = true;
                                } else if (stx <= 180 && dataType.equals(DynamicConstants.DATA_TYPE_GEOMETRY_BOX + "_pointCoordinate")) {
                                    geometryBoxFlag = true;
                                }
                            } else {
                                if (dataType.equals(DynamicConstants.DATA_TYPE_GEOMETRY_BOX + "_pointPlane")) {
                                    geometryBoxFlag = true;
                                }
                            }
                        } else {
                            if (dataType.equals(DynamicConstants.DATA_TYPE_GEOMETRY_BOX)) {
                                geometryBoxFlag = true;
                            }
                        }
                    } else {
                        geometryBoxFlag = true;
                    }
                    if (geometryBoxFlag && (containsAllQueryParam(conditions, splitParams, sqlStr) || paramStr.equals(DynamicConstants.PARAM_NULL_KEY) && StrUtil.isNotBlank(sqlStr))) {
                        spliceParam.append(StrUtil.SPACE).append(operator).append(StrUtil.SPACE).append(sqlStr);
                    }
                });
            }

            //构造SQL语句
            SQL sql = new SQL();
            sql.SELECT("*")
                .FROM("(" + querySql + ") t");
            StringBuilder newSpliceParam = null;
            if (StrUtil.isNotBlank(spliceParam)) {
                newSpliceParam = formateSQL(spliceParam);
                sql.WHERE("1 = 1" + newSpliceParam.toString());
            }
            // 查询类型是SQL
            if (DynamicServerResultTypeEnums.PAGE == resultType) {
                // 执行count
                int count = getCount("(" + querySql + ") t", conditions, newSpliceParam);
                config.put("count", count);
                querySql = this.buildPageSql(sql.toString(), datasourceStrategy, conditions, config);
            } else {
                querySql = sql.toString();
            }
            resultList = MyBatisUtils.selectList(querySql, conditions);
            config.put(DynamicConstants.RESULT_SET_KEY, resultList);
        }
        params.put("conditions", conditions);
        config.put("api", apiDTO);
        config.put("params", params);
        List<Map<String, Object>> result = new ArrayList<>();
        result.add(config);
        return result;
    }

    /**
     * 获取总数
     *
     * @param tableName   表名
     * @param conditions  参数
     * @param spliceParam 拼接条件
     * @return 总数
     */
    private int getCount(String tableName, JSONObject conditions, StringBuilder spliceParam) {
        SQL countSql = new SQL();
        countSql.SELECT("COUNT(1)")
            .FROM(tableName);
        if (StrUtil.isNotBlank(spliceParam)) {
            countSql.WHERE("1 = 1" + spliceParam);
        }
        return Optional.ofNullable(MyBatisUtils.selectOne(countSql.toString(), conditions, Integer.class)).orElse(0);
    }

    /**
     * 构建分页SQL
     *
     * @param sql                SQL语句
     * @param datasourceStrategy 数据源策略
     * @param conditions         参数
     * @param config             配置
     * @return 分页SQL
     */
    private String buildPageSql(String sql, DatasourceStrategy datasourceStrategy, JSONObject conditions, Map<String, Object> config) {
        StringBuilder sqlBuilder = new StringBuilder(sql);
        long current = conditions.getLong(PageConstants.CURRENT, PageConstants.DEFAULT_CURRENT);
        long size = conditions.getLong(PageConstants.SIZE, PageConstants.DEFAULT_SIZE);
        config.put(PageConstants.CURRENT, current);
        config.put(PageConstants.SIZE, size);
        datasourceStrategy.handlePageSql(sqlBuilder, size, PageUtil.getStart((int)current, (int)size));
        return sqlBuilder.toString();
    }

    /**
     * 构建相交SQL
     *
     * @param apiDTO     API配置
     * @param sql        SQL语句
     * @param schema     模式
     * @param dbType     数据库类型
     * @param conditions 参数
     */
    private String buildIntersectSql(ApiDTO apiDTO, SQL sql, String schema, DbTypeEnum dbType, JSONObject conditions, Double stx, String shapeColumn) {
        // 查询缓冲区
        BigDecimal queryBuffer = BigDecimal.ZERO;
        if (apiDTO.getDynamicTemplate() != null) {
            queryBuffer = Optional.ofNullable(apiDTO.getDynamicTemplate().getQueryBuffer()).orElse(BigDecimal.ZERO);
        }
        String geometry = conditions.getStr("geometry");
        String intersectSql = StrUtil.EMPTY;
        if (DynamicConstants.GEO_TYPE_SURFACE.equals(apiDTO.getGeoType())) {
            switch (dbType) {
                case POSTGRESQL:
                case KINGBASE:
                    intersectSql = schema + String.format("ST_INTERSECTS(%s, %sST_GEOMFROMTEXT(#{wkt}, #{srid}))", shapeColumn, schema);
                    break;
                case SQLSERVER:
                    intersectSql = String.format("%s.STIntersects(geometry::STGeomFromText(#{wkt}, #{srid})) = 1", shapeColumn);
                    break;
                case MYSQL:
                case DAMENG:
                    intersectSql = String.format("st_intersects(%s, st_geomfromtext(#{wkt}, #{srid}))", shapeColumn);
                    break;
                case ORACLE_SDE:
                    intersectSql = String.format("SDE.ST_INTERSECTS(%s, SDE.ST_GEOMETRY(#{wkt}, #{srid})) = 1", shapeColumn);
                    break;
                case ORACLE_SDO:
                    intersectSql = String.format("SDO_GEOM.RELATE(SDO_UTIL.FROM_WKTGEOMETRY(#{wkt}), 'ANYINTERACT', %s, 0.5) != 'FALSE'", shapeColumn);
                    break;
                default:
                    break;
            }
        } else {
            if (dbType == DbTypeEnum.POSTGRESQL || dbType == DbTypeEnum.KINGBASE) {
                if (queryBuffer.compareTo(BigDecimal.ZERO) == 0) {
                    queryBuffer = conditions.getBigDecimal("buffer");
                }
                if (queryBuffer != null && queryBuffer.compareTo(BigDecimal.ONE) >= 0) {
                    if (StrUtil.isBlank(geometry)) {
                        // 判断平面还是地理坐标，大于180为平面坐标
                        if (stx > 180) {
                            intersectSql = schema + String.format("ST_DWithin(%s, %sST_GeomFromText(#{wkt}, #{srid}), #{buffer})", shapeColumn, schema);
                        } else {
                            intersectSql = schema + String.format("ST_DWithin(%s::geography, %sST_GeomFromText(#{wkt}, #{srid})::geography, #{buffer})", shapeColumn, schema);
                        }
                    } else {
                        intersectSql = schema + String.format("ST_Intersects(%s, %sST_GeomFromText(#{wkt}, #{srid}))", shapeColumn, schema);
                    }
                } else {
                    // 偏离值
                    BigDecimal deviation = conditions.getBigDecimal("deviation");
                    if (deviation != null && queryBuffer != null && queryBuffer.compareTo(deviation) < 0) {
                        conditions.set("buffer", deviation);
                    }
                    if (StrUtil.isBlank(geometry)) {
                        intersectSql = schema + String.format("ST_DWithin(%s, %sST_GeomFromText(#{wkt}, #{srid}), #{buffer})", shapeColumn, schema);
                    } else {
                        intersectSql = schema + String.format("ST_Intersects(%s, %sST_GeomFromText(#{wkt}, #{srid}))", shapeColumn, schema);
                    }
                }
            } else if (dbType == DbTypeEnum.SQLSERVER) {
                if (StrUtil.isBlank(geometry)) {
                    intersectSql = String.format("%s.STIntersects(geometry::STGeomFromText(#{wkt}, #{srid}).STBuffer(#{buffer})) = 1", shapeColumn);
                } else {
                    intersectSql = String.format("%s.STIntersects(geometry::STGeomFromText(#{wkt}, #{srid})) = 1", shapeColumn);
                }
            } else if (dbType == DbTypeEnum.ORACLE_SDE) {
                if (StrUtil.isBlank(geometry)) {
                    intersectSql = String.format("SDE.ST_INTERSECTS(SDE.ST_Buffer(%s, #{buffer}), SDE.ST_GEOMETRY(#{wkt}, #{srid})) = 1", shapeColumn);
                } else {
                    intersectSql = String.format("SDE.ST_INTERSECTS(%s, SDE.ST_GEOMETRY(#{wkt}, #{srid})) = 1", shapeColumn);
                }
            } else if (dbType == DbTypeEnum.ORACLE_SDO) {
                if (StrUtil.isBlank(geometry)) {
                    intersectSql = String.format("sdo_geom.relate(%s, 'ANYINTERACT', sdo_geom.sdo_buffer(sdo_util.from_wktgeometry(#{wkt}), #{buffer}, 0.05), 0.5) != 'FALSE'", shapeColumn);
                } else {
                    intersectSql = String.format("sdo_geom.relate(%s, 'ANYINTERACT', sdo_util.from_wktgeometry(#{wkt}), 0.5) != 'FALSE'", shapeColumn);
                }
            } else {
                if (StrUtil.isBlank(geometry)) {
                    intersectSql = String.format("st_intersects(st_buffer(%s, #{buffer}),st_geomfromtext(#{wkt}, #{srid}))", shapeColumn);
                } else {
                    intersectSql = String.format("st_intersects(%s,st_geomfromtext(#{wkt}, #{srid}))", shapeColumn);
                }
            }
        }
        BigDecimal x = conditions.getBigDecimal("x");
        BigDecimal y = conditions.getBigDecimal("y");
        AssertUtils.isTrue(ObjectUtil.isNull(x) || ObjectUtil.isNull(y), "缺少x或y参数");
        conditions.set("wkt", String.format("Point (%s %s)", x.toPlainString(), y.toPlainString()));
        if (StrUtil.isNotBlank(geometry)) {
            conditions.set("wkt", geometry);
        }
        if (ObjectUtil.isNotNull(queryBuffer) && queryBuffer.compareTo(BigDecimal.ZERO) > 0) {
            conditions.set("buffer", queryBuffer);
        }
        //解析参数配置
        List<ApiQueryParamDTO> queryParamList = apiDTO.getQueryParamList();
        //拼接sql
        StringBuilder spliceParam = new StringBuilder();
        if (containsQueryParam(conditions, queryParamList)) {
            queryParamList.forEach(queryParam -> {
                //操作符 如：AND | OR
                String operator = queryParam.getOperator();
                //条件 如：name = #{name}
                String sqlStr = queryParam.getCondition();
                String paramStr = queryParam.getParamName();
                String[] splitParams = paramStr.split(";");
                if (containsAllQueryParam(conditions, splitParams, sqlStr) || paramStr.equals(DynamicConstants.PARAM_NULL_KEY) && StrUtil.isNotEmpty(sqlStr)) {
                    spliceParam.append(StrUtil.SPACE).append(operator).append(StrUtil.SPACE).append(sqlStr);
                }
            });
        }
        if (StrUtil.isNotBlank(spliceParam)) {
            intersectSql += formateSQL(spliceParam).toString();
        }
        sql.WHERE(intersectSql);
        if (!DynamicConstants.GEO_TYPE_SURFACE.equals(apiDTO.getGeoType())) {
            if (dbType == DbTypeEnum.POSTGRESQL || dbType == DbTypeEnum.KINGBASE) {
                if (ObjectUtil.isNotNull(stx) && stx <= 180) {
                    sql.ORDER_BY(schema + String.format("ST_DistanceSphere(%s, %sST_GeometryFromText(#{wkt}, #{srid}))", shapeColumn, schema));
                } else {
                    sql.ORDER_BY(schema + String.format("ST_Distance(%s, %sST_GeometryFromText(#{wkt}, #{srid}))", shapeColumn, schema));
                }
            } else if (dbType == DbTypeEnum.SQLSERVER) {
                if (StrUtil.isBlank(geometry)) {
                    sql.ORDER_BY(String.format("%s.STIntersects(geometry::STGeomFromText(#{wkt}, #{srid}).STBuffer(#{buffer}))", shapeColumn));
                } else {
                    sql.ORDER_BY(String.format("%s.STIntersects(geometry::STGeomFromText(#{wkt}, #{srid}))", shapeColumn));
                }
            }
        }
        return intersectSql;
    }

    private Double getStx(String tableName, String schema, DbTypeEnum dbType, String shapeColumn) {
        Double stx = null;
        SQL stxSql = new SQL();
        String stxColumn;
        switch (dbType) {
            case POSTGRESQL:
            case KINGBASE:
                stxColumn = schema + String.format("ST_X(%sST_CENTROID(%s))", schema, shapeColumn);
                stxSql.SELECT(stxColumn)
                    .FROM(tableName)
                    .WHERE(stxColumn + " IS NOT NULL")
                    .LIMIT(1);
                break;
            default:
                break;
        }
        String stxSqlStr = stxSql.toString();
        if (StrUtil.isNotBlank(stxSqlStr)) {
            stx = MyBatisUtils.selectOne(stxSqlStr, Double.class);
        }
        return stx;
    }

    private void handleSrid(String tableName, String schema, DbTypeEnum dbType, JSONObject conditions, String shapeColumn) {
        SQL sridSql = new SQL();
        String sridColumn;
        switch (dbType) {
            case POSTGRESQL:
            case KINGBASE:
                sridColumn = schema + String.format("ST_SRID(%s)", shapeColumn);
                sridSql.SELECT(sridColumn)
                    .FROM(tableName)
                    .WHERE(sridColumn + " IS NOT NULL")
                    .LIMIT(1);
                break;
            case SQLSERVER:
                sridColumn = shapeColumn + ".STSrid";
                sridSql.SELECT(sridColumn)
                    .FROM(tableName)
                    .WHERE(sridColumn + " IS NOT NULL")
                    .FETCH_FIRST_ROWS_ONLY(1);
                break;
            case MYSQL:
            case DAMENG:
                sridColumn = String.format("ST_SRID(%s)", shapeColumn);
                sridSql.SELECT(sridColumn)
                    .FROM(tableName)
                    .WHERE(sridColumn + " IS NOT NULL")
                    .LIMIT(1);
                break;
            case ORACLE_SDE:
                sridColumn = String.format("SDE.ST_SRID(%s)", shapeColumn);
                sridSql.SELECT(sridColumn)
                    .FROM(tableName)
                    .WHERE(sridColumn + " IS NOT NULL")
                    .FETCH_FIRST_ROWS_ONLY(1);
                break;
            default:
                break;
        }
        String sridSqlStr = sridSql.toString();
        if (StrUtil.isNotBlank(sridSqlStr)) {
            Integer srid = MyBatisUtils.selectOne(sridSqlStr, Integer.class);
            conditions.set("srid", srid);
        }
    }

    public boolean includeProjectName(List<ApiQueryParamDTO> queryParamList) {
        if (CollectionUtil.isEmpty(queryParamList)) {
            return false;
        }
        for (ApiQueryParamDTO queryParam : queryParamList) {
            String[] splitParamName = queryParam.getParamName().split(";");
            for (String paramName : splitParamName) {
                if (paramName.equals(DynamicConstants.PROJECT_NAME_KEY)) {
                    return true;
                }
            }
        }
        return false;
    }

    public boolean containsAllQueryParam(JSONObject conditions, String[] splitParams, String sqlStr) {
        for (String param : splitParams) {
            if (!conditions.containsKey(param) || ObjectUtil.isEmpty(conditions.get(param)) || ObjectUtil.isEmpty(sqlStr)) {
                return false;
            }
        }
        return true;
    }

    public StringBuilder formateSQL(StringBuilder spliceParam) {
        String formattedSql = spliceParam.toString()
            .replaceAll("'%'\\s*\\|\\|\\s*", "'%' || ")
            .replaceAll("\\s*\\|\\|\\s*'%'", " || '%'");
        return new StringBuilder(formattedSql);
    }

    public boolean containsQueryParam(JSONObject conditions, List<ApiQueryParamDTO> queryParamList) {
        if (ObjectUtil.isEmpty(conditions) || CollUtil.isEmpty(queryParamList)) {
            return false;
        }
        for (ApiQueryParamDTO queryParam : queryParamList) {
            String[] splitParams = queryParam.getParamName().split(";");
            for (String paramName : splitParams) {
                if (paramName.equals(DynamicConstants.PARAM_NULL_KEY) && StrUtil.isNotEmpty(queryParam.getCondition())) {
                    return true;
                }
                if (conditions.containsKey(paramName)) {
                    return true;
                }
            }
        }
        return false;
    }

}

package cn.com.sipsg.common.dynamic.core.entity;

import cn.com.sipsg.common.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.github.yulichang.annotation.EntityMapping;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;

import java.math.BigDecimal;
import java.util.List;

/**
 * 模板
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@FieldNameConstants
@TableName(value = "medox_dynamic_template", autoResultMap = true)
public class DynamicTemplate extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private String id;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 模板编码
     */
    private String templateCode;

    /**
     * 关键字id字段名称
     */
    private String keyColumnName;

    /**
     * 显示字段名称
     */
    private String showColumnName;

    /**
     * 查询缓冲区
     */
    private BigDecimal queryBuffer;

    /**
     * 备注
     */
    private String remark;

    /**
     * 模板字段配置
     */
    @TableField(exist = false)
    @EntityMapping(thisField = Fields.id, joinField = DynamicTemplateField.Fields.templateId)
    private List<DynamicTemplateField> fieldList;

    /**
     * 模板更多配置
     */
    @TableField(exist = false)
    @EntityMapping(thisField = Fields.id, joinField = DynamicTemplateExtra.Fields.templateId)
    private List<DynamicTemplateExtra> extraList;

}
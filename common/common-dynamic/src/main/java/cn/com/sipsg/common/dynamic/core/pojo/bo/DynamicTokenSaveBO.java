package cn.com.sipsg.common.dynamic.core.pojo.bo;

import cn.com.sipsg.common.validation.group.UpdateGroup;
import com.alibaba.fastjson2.JSONObject;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 密钥保存BO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "密钥保存BO")
public class DynamicTokenSaveBO {

    /**
     * 密钥ID
     */
    @Schema(description = "密钥ID")
    @NotBlank(message = "密钥ID不能为空", groups = {UpdateGroup.class})
    private String id;

    /**
     * 密钥名称
     */
    @Schema(description = "密钥名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "密钥名称不能为空")
    private String tokenName;

    /**
     * 密钥编码
     */
    @Schema(description = "密钥编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "密钥编码不能为空")
    private String tokenCode;

    /**
     * 密钥类型
     */
    @Schema(description = "密钥类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "密钥类型不能为空")
    private String tokenType;

    /**
     * 请求地址
     */
    @Schema(description = "请求地址", requiredMode = Schema.RequiredMode.REQUIRED)
    private String requestUrl;

    /**
     * 请求入参
     */
    @Schema(description = "请求入参")
    private JSONObject requestBody;

    /**
     * 请求方法
     */
    @Schema(description = "请求方法", requiredMode = Schema.RequiredMode.REQUIRED)
    @Pattern(regexp = "(GET|POST)?", message = "请求方法不正确")
    private String requestMethod;

    /**
     * 请求头
     */
    @Schema(description = "请求头")
    private JSONObject requestHeader;

    /**
     * 请求超时时间;单位毫秒
     */
    @Schema(description = "请求超时时间;单位毫秒", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer requestTimeout;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 令牌超时时间
     */
    @Schema(description = "令牌超时时间")
    private BigDecimal tokenExpiredMinutes;

}
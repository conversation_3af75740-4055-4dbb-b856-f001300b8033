package cn.com.sipsg.common.dynamic.core.entity;

import cn.com.sipsg.common.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.OrderBy;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;


/**
 * 模板字段配置
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@FieldNameConstants
@TableName(value = "medox_dynamic_template_field", autoResultMap = true)
public class DynamicTemplateField extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private String id;

    /**
     * 模板ID
     */
    private String templateId;

    /**
     * 字段名称
     */
    private String columnName;

    /**
     * 标签名称
     */
    private String labelName;

    /**
     * 单位
     */
    private String unit;

    /**
     * 精度或格式
     */
    private String precisionFormat;

    /**
     * 字典编码
     */
    private String dictCode;

    /**
     * 快查是否可见
     */
    private Boolean qQueryVisible;

    /**
     * I查是否可见
     */
    private Boolean iQueryVisible;

    /**
     * 详情是否可见
     */
    private Boolean detailVisible;

    /**
     * 查询是否可见
     */
    private Boolean queryVisible;

    /**
     * 排序值
     */
    @OrderBy(asc = true, sort = Short.MIN_VALUE)
    private Integer sort;

    /**
     * 数据类型
     */
    private String dataType;

    /**
     * 默认值
     */
    private String defaultValue;

    /**
     * 地址前缀
     */
    private String urlPrefix;

    /**
     * 空值替换值
     */
    private String emptyValueReplacement;

}
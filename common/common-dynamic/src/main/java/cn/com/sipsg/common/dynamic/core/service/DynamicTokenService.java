package cn.com.sipsg.common.dynamic.core.service;

import cn.com.sipsg.common.dynamic.core.entity.DynamicToken;
import cn.com.sipsg.common.dynamic.core.pojo.bo.DynamicTokenQueryBO;
import cn.com.sipsg.common.dynamic.core.pojo.bo.DynamicTokenSaveBO;
import cn.com.sipsg.common.dynamic.core.pojo.bo.DynamicTokenTestBO;
import cn.com.sipsg.common.dynamic.core.pojo.dto.TokenDTO;
import cn.com.sipsg.common.dynamic.core.pojo.vo.DynamicTokenVO;
import cn.com.sipsg.common.mybatis.core.service.BaseServiceX;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;

/**
 * 密钥服务接口
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public interface DynamicTokenService extends BaseServiceX<DynamicToken> {

    /**
     * 查询密钥分页列表
     *
     * @param bo 参数
     * @return 密钥分页列表
     */
    CommonPageVO<DynamicTokenVO> page(DynamicTokenQueryBO bo);

    /**
     * 新增密钥
     *
     * @param bo 参数
     * @return 密钥ID
     */
    String save(DynamicTokenSaveBO bo);

    /**
     * 编辑密钥
     *
     * @param bo 参数
     */
    void update(DynamicTokenSaveBO bo);

    /**
     * 根据id查询密钥详情
     *
     * @param id 主键
     * @return 密钥详情
     */
    DynamicTokenVO detail(String id);

    /**
     * 删除密钥
     *
     * @param id 主键
     */
    void delete(String id);

    /**
     * 根据密钥ID查询密钥
     *
     * @param id 密钥ID
     * @return 密钥
     */
    TokenDTO getToken(String id);

    /**
     * 测试密钥
     *
     * @param bo 参数
     * @return 密钥
     */
    TokenDTO testToken(DynamicTokenTestBO bo);

}
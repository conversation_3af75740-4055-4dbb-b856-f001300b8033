package cn.com.sipsg.common.dynamic.core.strategy.impl;

import cn.com.sipsg.common.constant.PageConstants;
import cn.com.sipsg.common.dynamic.core.aop.DynamicExecuteResult;
import cn.com.sipsg.common.dynamic.core.constant.DynamicConstants;
import cn.com.sipsg.common.dynamic.core.entity.DynamicServer;
import cn.com.sipsg.common.dynamic.core.enums.DynamicServerResultTypeEnums;
import cn.com.sipsg.common.dynamic.core.pojo.dto.ApiDTO;
import cn.com.sipsg.common.dynamic.core.pojo.dto.ApiQueryParamDTO;
import cn.com.sipsg.common.dynamic.core.pojo.dto.ArcgisDTO;
import cn.com.sipsg.common.dynamic.core.strategy.DynamicExecuteStrategy;
import cn.com.sipsg.common.geo.core.util.GeoUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.http.Method;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.operation.buffer.BufferOp;
import org.locationtech.jts.operation.buffer.BufferParameters;
import org.springframework.stereotype.Service;

import java.util.*;

import static cn.com.sipsg.common.constant.CommonConstants.DEFAULT_SHAPE_COLUMN;

/**
 * Arcgis动态执行策略
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Service
public class ArcgisRestStrategyImpl implements DynamicExecuteStrategy {

    private static final int REQUEST_TIMEOUT = 20000;

    @Override
    public String dynamicServerType() {
        return DynamicConstants.SERVER_TYPE_ARCGIS;
    }

    @Override
    @DynamicExecuteResult
    public Object dynamicExecute(ApiDTO apiDTO, JSONObject params) {
        Map<String, Object> config = new HashMap<>();
        config.put("name", apiDTO.getApiName());
        DynamicServer dynamicServer = apiDTO.getDynamicServer();
        List<Map<String, Object>> result = new ArrayList<>();
        JSONObject conditions = params.containsKey(DynamicConstants.CONDITIONS_KEY) ? params.getJSONObject(DynamicConstants.CONDITIONS_KEY) : new JSONObject();
        if (DynamicConstants.SERVER_TYPE_ARCGIS.equals(dynamicServer.getServerType())) {
            if (DynamicConstants.API_FUNCTION_I.equals(apiDTO.getApiFunction())) {
                // 通过x,y,buffer计算缓冲区
                if (conditions.containsKey("geometry")) {
                    calculatedCacheArea(conditions);
                    conditions.put("spatialRel", "esriSpatialRelIntersects");
                    conditions.put("geometryType", "esriGeometryPolygon");
                } else {
                    if (DynamicConstants.GEO_TYPE_SURFACE.equals(apiDTO.getGeoType())) {
                        conditions.set("spatialRel", "esriSpatialRelIntersects");
                        conditions.set("geometryType", "esriGeometryPoint");
                        conditions.set("geometry", conditions.getStr("x") + "," + conditions.getStr("y"));
                    } else {
                        calculatedCacheAreaPoint(conditions);
                        conditions.set("spatialRel", "esriSpatialRelIntersects");
                        conditions.set("geometryType", "esriGeometryPolygon");
                    }
                }
                String where = "1=1";
                if (conditions.containsKey("where") && StrUtil.isNotEmpty(dynamicServer.getRequestBody())) {
                    where += " and " + dynamicServer.getRequestBody();
                }
                if (CollectionUtil.isNotEmpty(apiDTO.getQueryParamList())) {
                    //解析参数配置
                    List<ApiQueryParamDTO> queryParamList = apiDTO.getQueryParamList();
                    //拼接sql
                    StringBuilder spliceParam = new StringBuilder();
                    queryParamList.forEach(queryParam -> {
                        //操作符 如：AND | OR
                        String operator = queryParam.getOperator();
                        //条件 如：name = #{name}
                        String sqlStr = queryParam.getCondition();
                        String paramStr = queryParam.getParamName();
                        String[] splitParams = paramStr.split(";");
                        if (containsAllQueryParam(conditions, splitParams, sqlStr)
                                || paramStr.equals(DynamicConstants.PARAM_NULL_KEY) && StrUtil.isNotEmpty(sqlStr)) {
                            if (paramStr.equals(DynamicConstants.PARAM_NULL_KEY) && StrUtil.isNotEmpty(sqlStr)) {
                                if (getRequestMethod(apiDTO)) {
                                    sqlStr = sqlStr.replace("%", "%25");
                                }
                                spliceParam.append(StrUtil.SPACE).append(operator).append(StrUtil.SPACE).append(sqlStr);
                            } else {
                                for (int i = 0; i < splitParams.length; i++) {
                                    if (String.valueOf(sqlStr.charAt(sqlStr.indexOf("#" + StrPool.DELIM_START + splitParams[i]) - 2)).equals("=")
                                            || String.valueOf(sqlStr.charAt(sqlStr.indexOf("#" + StrPool.DELIM_START + splitParams[i]) - 1)).equals("=")) {
                                        sqlStr = sqlStr.replace("#" + StrPool.DELIM_START + splitParams[i] + StrPool.DELIM_END, "'" + conditions.getStr(splitParams[i]) + "'");
                                    } else {
                                        sqlStr = sqlStr.replace("#" + StrPool.DELIM_START + splitParams[i] + StrPool.DELIM_END, conditions.getStr(splitParams[i]));
                                    }
                                }
                                if (getRequestMethod(apiDTO)) {
                                    sqlStr = sqlStr.replace("%", "%25");
                                }
                                spliceParam.append(StrUtil.SPACE).append(operator).append(StrUtil.SPACE).append(sqlStr);
                            }
                        }
                    });
                    where += spliceParam;
                }
                conditions.set("where", where);
                conditions.put("outFields", "*");
                conditions.put("returnGeometry", "true");
                conditions.put("f", "pjson");
            } else {
                String where = "1=1";
                if (conditions.containsKey("where")) {
                    where += " and " + dynamicServer.getRequestBody();
                }
                conditions.set("where", where);
                conditions.set("outFields", "*");
                conditions.set("returnGeometry", "true");
                conditions.set("f", "pjson");
                List<ApiQueryParamDTO> queryParamList = apiDTO.getQueryParamList();
                // 拼接sql
                if (CollectionUtil.isNotEmpty(queryParamList)) {
                    queryParamList.forEach(queryParam -> {
                        // 操作符 如：AND | OR
                        String operator = queryParam.getOperator();
                        // 条件 如：name = #{name}
                        String sqlStr = queryParam.getCondition();
                        String paramStr = queryParam.getParamName();
                        String[] splitParams = paramStr.split(";");
                        if (conditions.containsKey(DynamicConstants.PROJECT_NAME_KEY) && (containsAllQueryParam(conditions, splitParams, sqlStr)
                                || paramStr.equals(DynamicConstants.PARAM_NULL_KEY) && StrUtil.isNotEmpty(sqlStr))) {
                            if (paramStr.equals(DynamicConstants.PARAM_NULL_KEY) && StrUtil.isNotEmpty(sqlStr)) {
                                if (getRequestMethod(apiDTO)) {
                                    sqlStr = sqlStr.replace("%", "%25");
                                }
                                conditions.set("where", conditions.getStr("where") + StrUtil.SPACE + operator + StrUtil.SPACE + sqlStr);
                            } else {
                                for (int i = 0; i < splitParams.length; i++) {
                                    if (String.valueOf(sqlStr.charAt(sqlStr.indexOf("#" + StrPool.DELIM_START + splitParams[i]) - 2)).equals("=")
                                            || String.valueOf(sqlStr.charAt(sqlStr.indexOf("#" + StrPool.DELIM_START + splitParams[i]) - 1)).equals("=")) {
                                        sqlStr = sqlStr.replace("#" + StrPool.DELIM_START + splitParams[i] + StrPool.DELIM_END, "'" + conditions.getStr(splitParams[i]) + "'");
                                    } else {
                                        sqlStr = sqlStr.replace("#" + StrPool.DELIM_START + splitParams[i] + StrPool.DELIM_END, conditions.getStr(splitParams[i]));
                                    }
                                }
                                if (getRequestMethod(apiDTO)) {
                                    sqlStr = sqlStr.replace("%", "%25");
                                }
                                conditions.set("where", conditions.getStr("where") + StrUtil.SPACE + operator + StrUtil.SPACE + sqlStr);
                            }
                        } else if (!conditions.containsKey(DynamicConstants.PROJECT_NAME_KEY)
                                && (containsAllQueryParam(conditions, splitParams, sqlStr) || paramStr.equals(DynamicConstants.PARAM_NULL_KEY) && StrUtil.isNotEmpty(sqlStr))) {
                            if (paramStr.equals(DynamicConstants.PARAM_NULL_KEY) && StrUtil.isNotEmpty(sqlStr)) {
                                if (getRequestMethod(apiDTO)) {
                                    sqlStr = sqlStr.replace("%", "%25");
                                }
                                conditions.set("where", conditions.getStr("where") + StrUtil.SPACE + operator + StrUtil.SPACE + sqlStr);
                            } else {
                                for (int i = 0; i < splitParams.length; i++) {
                                    sqlStr = sqlStr.replace("' || " + splitParams[i] + " || '", conditions.getStr(splitParams[i]));
                                }
                                if (getRequestMethod(apiDTO)) {
                                    sqlStr = sqlStr.replace("%", "%25");
                                }
                                conditions.set("where", conditions.getStr("where") + StrUtil.SPACE + operator + StrUtil.SPACE + sqlStr);
                            }
                        }
                    });
                }
                if (StrUtil.isNotBlank(conditions.getStr(DEFAULT_SHAPE_COLUMN))) {
                    calculatedCacheArea(conditions);
                    conditions.set("spatialRel", "esriSpatialRelIntersects");
                    conditions.set("geometryType", "esriGeometryPolygon");
                }
            }
        }
        String arcgisUrl = dynamicServer.getRequestUrl();
        String layerNo = conditions.getStr("arcgisLayerNo");
        if (StrUtil.isBlank(layerNo)) {
            layerNo = dynamicServer.getLayerNo().toString();
        }
        arcgisUrl = arcgisUrl + StrUtil.SLASH + layerNo + "/query";
        // 判断有无token如果有token查询 token管理获取token
        String token = Optional.ofNullable(apiDTO.getToken()).map(Object::toString).orElse(StrUtil.EMPTY);
        if (StrUtil.isNotBlank(token) && DynamicConstants.SERVER_TYPE_ARCGIS.equals(apiDTO.getTokenType())) {
            arcgisUrl = arcgisUrl + "?token=" + token;
        }
        String resultStr;
        if (Method.GET.toString().equals(dynamicServer.getRequestMethod())) {
            resultStr = HttpUtil.get(arcgisUrl, conditions, REQUEST_TIMEOUT);
        } else {
            resultStr = HttpUtil.post(arcgisUrl, conditions, REQUEST_TIMEOUT);
        }
        apiDTO.setCurrent(conditions.getLong(PageConstants.CURRENT, PageConstants.DEFAULT_CURRENT));
        apiDTO.setSize(conditions.getLong(PageConstants.SIZE, PageConstants.DEFAULT_SIZE));
        if (StrUtil.isNotEmpty(apiDTO.getQueryType())) {
            // 处理resultStr结果集只取里面的features（attributes，geometry）
            resultStr = handleResultStr(resultStr);
            config.put(DynamicConstants.RESULT_SET_KEY, resultStr);
            if (JSONUtil.isTypeJSON(resultStr)) {
                if (JSONUtil.isTypeJSONArray(resultStr)) {
                    config.put(DynamicConstants.RESULT_SET_KEY, JSONUtil.parseArray(resultStr));
                } else {
                    JSONArray jsonArray = new JSONArray();
                    config.put(DynamicConstants.RESULT_SET_KEY, jsonArray.add(JSONUtil.parseObj(resultStr)));
                }
            }
            DynamicServerResultTypeEnums resultType = dynamicServer.getResultType();
            if (DynamicServerResultTypeEnums.PAGE == resultType) {
                JSONArray jsonArray = new JSONArray();
                if (StrUtil.isNotEmpty(resultStr)) {
                    jsonArray = JSONUtil.parseArray(resultStr);
                }
                config.put(DynamicConstants.RESULT_SET_KEY, jsonArray);
                config.put("count", jsonArray.size());
                config.put(PageConstants.CURRENT, conditions.getLong(PageConstants.CURRENT));
                config.put(PageConstants.SIZE, conditions.getLong(PageConstants.SIZE));
            }
            config.put("api", apiDTO);
            config.put("params", params);
            result.add(config);
        } else {
            apiDTO.setQueryType(DynamicConstants.SERVER_TYPE_ARCGIS);
            ArcgisDTO arcgisDTO = JSON.parseObject(resultStr, ArcgisDTO.class);
            Map<String, Object> map = new HashMap<>();
            map.put(DynamicConstants.RESULT_SET_KEY, arcgisDTO);
            map.put("api", apiDTO);
            result.add(map);
        }
        return result;
    }

    /**
     * wkt计算缓冲区
     *
     * @param conditions 参数
     */
    private void calculatedCacheArea(JSONObject conditions) {
        String shape = conditions.getStr(DEFAULT_SHAPE_COLUMN);
        if (conditions.containsKey("geometry")) {
            shape = conditions.getStr("geometry");
        }
        if (StrUtil.isNotBlank(shape)) {
            String s = GeoUtils.wktToJson(shape);
            JSONObject jsonObject = JSONUtil.parseObj(s);
            JSONArray coordinates = jsonObject.getJSONArray("coordinates");
            JSONObject result = new JSONObject();
            result.set("rings", coordinates);
            conditions.set("geometry", result.toString());
        }
    }

    private String handleResultStr(String resultStr) {
        if (StrUtil.isNotEmpty(resultStr)) {
            JSONArray resultArray = new JSONArray();
            JSONObject jsonObject = JSONUtil.parseObj(resultStr);
            JSONArray features = jsonObject.getJSONArray("features");
            if (CollUtil.isNotEmpty(features)) {
                for (int i = 0; i < features.size(); i++) {
                    JSONObject feature = features.getJSONObject(i);
                    JSONObject attributes = feature.getJSONObject("attributes");
                    JSONObject geometry = feature.getJSONObject("geometry");
                    if (geometry != null) {
                        String wkt = GeoUtils.arcgis2wkt(JSONUtil.toJsonStr(feature));
                        attributes.set(DEFAULT_SHAPE_COLUMN, wkt);
                    }
                    resultArray.add(attributes);
                }
            }
            resultStr = resultArray.toString();
        }
        return resultStr;
    }

    /**
     * x、y、buffer计算缓冲区
     *
     * @param param 参数
     */
    private void calculatedCacheAreaPoint(JSONObject param) {
        Double x = param.getDouble("x");
        Double y = param.getDouble("y");
        Double buffer = param.getDouble("buffer", 10.0);
        Coordinate coordinate = new Coordinate(x, y);
        GeometryFactory gf = new GeometryFactory();
        Geometry gfLineString = gf.createPoint(coordinate);
        // double degree = 100米 / (2 * Math.PI * 6371004) * 360; // 按米转化为度单位
        // double degree = 1.25;
        // 缓冲区建立
        BufferOp bufOp = new BufferOp(gfLineString);
        // 结束端点样式
        bufOp.setEndCapStyle(BufferParameters.CAP_ROUND);
        Geometry bg = bufOp.getResultGeometry(buffer);
        String s = GeoUtils.wktToJson(bg.toText());
        JSONObject jsonObject = JSONUtil.parseObj(s);
        JSONArray coordinates = jsonObject.getJSONArray("coordinates");
        JSONObject result = new JSONObject();
        result.set("rings", coordinates);
        param.set("geometry", result.toString());
    }

    /**
     * 判断参数是否对应
     *
     * @param conditions  请求参数
     * @param splitParams sql参数数组
     * @param sqlStr      sql语句
     * @return
     */
    public boolean containsAllQueryParam(JSONObject conditions, String[] splitParams, String sqlStr) {
        for (String param : splitParams) {
            if (!conditions.containsKey(param) || ObjectUtil.isEmpty(conditions.get(param)) || StrUtil.isBlank(sqlStr)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 获取请求类型
     *
     * @param apiDTO 配置
     * @return
     */
    public boolean getRequestMethod(ApiDTO apiDTO) {
        if (Method.GET.toString().equals(apiDTO.getDynamicServer().getRequestMethod())) {
            return true;
        }
        return false;
    }

}

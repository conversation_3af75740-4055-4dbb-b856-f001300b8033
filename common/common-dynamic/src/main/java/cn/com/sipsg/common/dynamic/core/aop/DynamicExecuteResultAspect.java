package cn.com.sipsg.common.dynamic.core.aop;

import cn.com.sipsg.common.dynamic.core.constant.DynamicConstants;
import cn.com.sipsg.common.dynamic.core.enums.DynamicServerResultTypeEnums;
import cn.com.sipsg.common.dynamic.core.filter.DynamicArcgisFilter;
import cn.com.sipsg.common.dynamic.core.filter.DynamicChartFilter;
import cn.com.sipsg.common.dynamic.core.filter.DynamicFactory;
import cn.com.sipsg.common.dynamic.core.filter.DynamicFilter;
import cn.com.sipsg.common.dynamic.core.filter.DynamicFilterChain;
import cn.com.sipsg.common.dynamic.core.filter.DynamicGeoJsonDataFilter;
import cn.com.sipsg.common.dynamic.core.filter.DynamicGeoJsonFilter;
import cn.com.sipsg.common.dynamic.core.filter.DynamicListFilter;
import cn.com.sipsg.common.dynamic.core.filter.DynamicPageFilter;
import cn.com.sipsg.common.dynamic.core.filter.DynamicPhysicsPageFilter;
import cn.com.sipsg.common.dynamic.core.filter.DynamicRowFilter;
import cn.com.sipsg.common.dynamic.core.filter.DynamicTemplateFilter;
import cn.com.sipsg.common.dynamic.core.filter.DynamicTreeFilter;
import cn.com.sipsg.common.dynamic.core.pojo.dto.ApiDTO;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import java.util.Collection;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

/**
 * 动态执行结果处理切面
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Aspect
@Component
public class DynamicExecuteResultAspect {

    @AfterReturning(returning = "results", pointcut = "@annotation(cn.com.sipsg.common.dynamic.core.aop.DynamicExecuteResult)")
    public void columnAspect(Object results) {
        this.handle(results);
    }

    @SuppressWarnings("unchecked")
    private void handle(Object results) {
        if (ObjectUtil.isEmpty(results)) {
            return;
        }
        // 判断是否是列表
        if (!(results instanceof List)) {
            return;
        }
        List<Map<String, Object>> tmp = (List<Map<String, Object>>)results;
        Map<String, Object> result = tmp.get(0);
        ApiDTO api = (ApiDTO)result.get("api");
        Set<Class<? extends DynamicFilter>> filterList = new LinkedHashSet<>();
        String queryType = StrUtil.nullToEmpty(api.getQueryType());
        switch (queryType) {
            case DynamicConstants.SERVER_TYPE_TABLE:
                filterList.add(DynamicListFilter.class);
                break;
            case DynamicConstants.SERVER_TYPE_ARCGIS:
                filterList.add(DynamicArcgisFilter.class);
                break;
            default:
                break;
        }

        DynamicServerResultTypeEnums resultType = api.getDynamicServer().getResultType();
        switch (resultType) {
            case COMMON_JSON:
                filterList.add(DynamicListFilter.class);
                break;
            case ROW:
                filterList.add(DynamicRowFilter.class);
                break;
            case GEO_JSON:
            case GEO_JSON_DATA:
                filterList.add(DynamicGeoJsonFilter.class);
                break;
            case PAGE:
                break;
            case TREE:
                filterList.add(DynamicTreeFilter.class);
                break;
            case CHART:
                filterList.add(DynamicChartFilter.class);
                break;
            default:
                break;
        }
        // 如果有模版
        if (null != api.getDynamicTemplate()) {
            filterList.add(DynamicTemplateFilter.class);
        }
        if (DynamicServerResultTypeEnums.PAGE.equals(resultType)) {
            switch (queryType) {
                case DynamicConstants.SERVER_TYPE_TABLE:
                    filterList.add(DynamicPageFilter.class);
                    break;
                case DynamicConstants.SERVER_TYPE_ARCGIS:
                    filterList.add(DynamicPhysicsPageFilter.class);
                    break;
                case DynamicConstants.SERVER_TYPE_PROXY:
                    filterList.add(DynamicPhysicsPageFilter.class);
                    break;
                default:
                    break;
            }
        } else if (DynamicServerResultTypeEnums.GEO_JSON_DATA.equals(resultType)) {
            filterList.add(DynamicGeoJsonDataFilter.class);
        }
        DynamicFilterChain chain = new DynamicFilterChain();
        for (Class<? extends DynamicFilter> filterClass : filterList) {
            chain.add(DynamicFactory.getInstance(filterClass));
        }
        chain.doFilter(tmp);
        if (CollUtil.isNotEmpty(tmp)) {
            List<Map<String, Object>> tmpClone = ObjectUtil.clone(tmp);
            ((List<Map<String, Object>>)results).clear();
            ((List<Map<String, Object>>)results).addAll((Collection<? extends Map<String, Object>>)tmpClone.get(0));
        }
    }

}

package cn.com.sipsg.common.dynamic.core.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 密钥简单信息VO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "密钥简单信息VO")
public class DynamicTokenSimpleVO {

    /**
     * 密钥ID
     */
    @Schema(description = "密钥ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private String id;

    /**
     * 密钥名称
     */
    @Schema(description = "密钥名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String tokenName;

    /**
     * 密钥编码
     */
    @Schema(description = "密钥编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String tokenCode;

}
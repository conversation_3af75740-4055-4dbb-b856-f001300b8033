package cn.com.sipsg.common.dynamic.core.filter;

import cn.com.sipsg.common.dynamic.core.constant.DynamicConstants;
import cn.com.sipsg.common.dynamic.core.entity.DynamicServer;
import cn.com.sipsg.common.dynamic.core.pojo.dto.ApiDTO;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 图表过滤器
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Slf4j
public class DynamicChartFilter implements DynamicFilter {

    @Override
    @SuppressWarnings("unchecked")
    public boolean doFilter(List results) {
        List<Object> objects = new ArrayList<>();
        Map result = (HashMap)results.get(0);
        ApiDTO api = (ApiDTO)result.get("api");
        DynamicServer dynamicServer = api.getDynamicServer();
        List<Map<String, Object>> resultSet = (List<Map<String, Object>>)result.get(DynamicConstants.RESULT_SET_KEY);
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("title", dynamicServer.getChartTitle());
        resultMap.put("subTitle", dynamicServer.getChartSubTitle());
        resultMap.put("chartType", dynamicServer.getChartType());
        resultMap.put("xUnit", dynamicServer.getChartXUnit());
        resultMap.put("yUnit", dynamicServer.getChartYUnit());
        if ("pie".equals(dynamicServer.getChartType())) {
            handlePie(resultMap, resultSet);
        } else {
            handleEChart(resultMap, resultSet);
        }
        objects.add(resultMap);
        result.put(DynamicConstants.RESULT_SET_KEY, objects);
        return true;
    }

    private void handlePie(Map<String, Object> result, List<Map<String, Object>> entityList) {
        // 结果集中第一个key对应的value
        List<String> xAxis = new ArrayList<>();
        for (Map<String, Object> entity : entityList) {
            xAxis.add(entity.values().stream().findFirst().map(Convert::toStr).orElse(StrUtil.EMPTY));
        }
        result.put("xAxis", xAxis);
        result.put("data", entityList);
    }

    private void handleEChart(Map<String, Object> result, List<Map<String, Object>> entityList) {
        // 取结果集每个对象第一个value
        List<String> xAxis = new ArrayList<>();
        // 取结果集除第一个key之外的key
        List<String> legend = new ArrayList<>();
        List<String> keys = new ArrayList<>();
        if (CollUtil.isNotEmpty(entityList)) {
            int i = 0;
            Map<String, Object> entity = entityList.get(0);
            for (String key : entity.keySet()) {
                if (i != 0) {
                    legend.add(key);
                }
                keys.add(key);
                i++;
            }
        }
        // 取结果集除第一个key之外每一个key对应的对应的value集合，一个key对应的所有value值为一个集合
        List<List<String>> data = new ArrayList<>();
        for (int i = 0, len = keys.size(); i < len; i++) {
            List<String> d = new ArrayList<>();
            String key = keys.get(i);
            for (Map<String, Object> entity : entityList) {
                if (i == 0) {
                    xAxis.add(MapUtil.getStr(entity, key, StrUtil.EMPTY));
                }
                if (i != 0) {
                    d.add(MapUtil.getStr(entity, key, StrUtil.EMPTY));
                }
            }
            if (!d.isEmpty()) {
                data.add(d);
            }
        }
        result.put("xAxis", xAxis);
        result.put("data", data);
        result.put("legend", legend);
    }

}

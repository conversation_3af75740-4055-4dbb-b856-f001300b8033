package cn.com.sipsg.common.dynamic.core.pojo.vo;

import cn.com.sipsg.common.datasource.core.entity.Datasource;
import cn.com.sipsg.common.dynamic.core.entity.DynamicToken;
import cn.com.sipsg.common.dynamic.core.enums.DynamicServerResultTypeEnums;
import cn.com.sipsg.common.enums.CommonStatusEnum;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;

/**
 * 服务VO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@FieldNameConstants
@Schema(description = "服务VO")
public class DynamicServerVO implements VO {

    /**
     * 服务ID
     */
    @TableId
    @Schema(description = "服务ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private String id;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

    /**
     * 服务名称
     */
    @Schema(description = "服务名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String serverName;

    /**
     * 服务编码
     */
    @Schema(description = "服务编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String serverCode;

    /**
     * 服务类型;TABLE，ARCGIS，PROXY
     */
    @Schema(description = "服务类型;TABLE，ARCGIS，PROXY", requiredMode = Schema.RequiredMode.REQUIRED)
    private String serverType;

    /**
     * 状态;1-启用，0-禁用
     */
    @Schema(description = "状态;1-启用，0-禁用", requiredMode = Schema.RequiredMode.REQUIRED)
    private CommonStatusEnum status;

    /**
     * 查询类型;TABLE，SQL
     */
    @Schema(description = "查询类型;TABLE，SQL")
    private String queryType;

    /**
     * 表名或查询语句
     */
    @Schema(description = "表名或查询语句")
    private String querySql;

    /**
     * 分组名称
     */
    @Schema(description = "分组名称")
    private String groupName;

    /**
     * 返回结果类型
     */
    @Schema(description = "返回结果类型")
    private DynamicServerResultTypeEnums resultType;

    /**
     * 数据源ID
     */
    @Schema(description = "数据源ID")
    @Trans(type = TransType.SIMPLE, target = Datasource.class, fields = Datasource.Fields.name, ref = Fields.dbName)
    private String dbId;

    /**
     * 数据源名称
     */
    @Schema(description = "数据源名称")
    private String dbName;

    /**
     * 请求地址
     */
    @Schema(description = "请求地址")
    private String requestUrl;

    /**
     * 请求方法;GET，POST
     */
    @Schema(description = "请求方法;GET，POST")
    private String requestMethod;

    /**
     * 请求头
     */
    @Schema(description = "请求头")
    private String requestHeader;

    /**
     * 请求体
     */
    @Schema(description = "请求体")
    private String requestBody;

    /**
     * 认证方式
     */
    @Schema(description = "认证方式")
    private String authType;

    /**
     * 令牌ID
     */
    @Schema(description = "令牌ID")
    @Trans(type = TransType.SIMPLE, target = DynamicToken.class, fields = Datasource.Fields.name, ref = Fields.tokenName)
    private String tokenId;

    /**
     * 令牌名称
     */
    @Schema(description = "令牌名称")
    private String tokenName;

    /**
     * 层号
     */
    @Schema(description = "层号")
    private Integer layerNo;

    /**
     * 过滤条件
     */
    @Schema(description = "过滤条件")
    private String filterCondition;

    /**
     * 模板ID
     */
    @Schema(description = "模板ID")
    private String templateId;

    /**
     * 图表类型
     */
    @Schema(description = "图表类型")
    private String chartType;

    /**
     * 图表标题
     */
    @Schema(description = "图表标题")
    private String chartTitle;

    /**
     * 图表子标题
     */
    @Schema(description = "图表子标题")
    private String chartSubTitle;

    /**
     * 图表X坐标单位
     */
    @Schema(description = "图表X坐标单位")
    private String chartXUnit;

    /**
     * 图表Y坐标单位
     */
    @Schema(description = "图表Y坐标单位")
    private String chartYUnit;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 空间字段配置
     */
    @Schema(description = "空间字段配置")
    private String shapeColumn;

}
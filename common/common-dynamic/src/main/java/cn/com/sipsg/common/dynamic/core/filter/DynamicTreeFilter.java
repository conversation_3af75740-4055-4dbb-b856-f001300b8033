package cn.com.sipsg.common.dynamic.core.filter;

import cn.com.sipsg.common.constant.CommonConstants;
import cn.com.sipsg.common.dynamic.core.constant.DynamicConstants;
import cn.com.sipsg.common.util.TreeUtils;

import java.util.HashMap;
import java.util.List;

/**
 * Tree过滤器
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public class DynamicTreeFilter implements DynamicFilter {

    @Override
    @SuppressWarnings("unchecked")
    public boolean doFilter(List results) {
        HashMap result = (HashMap)results.get(0);
        List<HashMap> dynamicResult = (List<HashMap>)result.get(DynamicConstants.RESULT_SET_KEY);
        result.put(DynamicConstants.RESULT_SET_KEY, TreeUtils.buildMap(dynamicResult, CommonConstants.PARENT_ID_ROOT));
        return true;
    }

}

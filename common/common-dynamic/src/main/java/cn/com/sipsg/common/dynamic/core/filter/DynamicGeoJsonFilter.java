package cn.com.sipsg.common.dynamic.core.filter;

import cn.com.sipsg.common.dynamic.core.constant.DynamicConstants;
import cn.com.sipsg.common.dynamic.core.pojo.dto.ApiDTO;
import cn.com.sipsg.common.geo.core.util.GeoUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import static cn.com.sipsg.common.constant.CommonConstants.DEFAULT_SHAPE_COLUMN;

/**
 * GeoJson过滤器
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public class DynamicGeoJsonFilter implements DynamicFilter {

    @Override
    @SuppressWarnings("unchecked")
    public boolean doFilter(List results) {
        HashMap result = (HashMap)results.get(0);
        List<HashMap> dynamicResult = (List<HashMap>)result.get(DynamicConstants.RESULT_SET_KEY);
        ApiDTO api = (ApiDTO)result.get("api");
        String shapeColumn = DEFAULT_SHAPE_COLUMN;
        if (api != null && api.getDynamicServer() != null && StrUtil.isNotBlank(api.getDynamicServer().getShapeColumn())) {
            shapeColumn = api.getDynamicServer().getShapeColumn();
        }
        if (CollUtil.isEmpty(dynamicResult)) {
            return false;
        }
        List<HashMap> newDynamicResult = new ArrayList<>();
        for (HashMap hashMap : dynamicResult) {
            HashMap<String, Object> geoJson = GeoUtils.wktToGeoJson(hashMap, shapeColumn);
            newDynamicResult.add(geoJson);
        }
        result.put(DynamicConstants.RESULT_SET_KEY, newDynamicResult);
        return true;
    }

}

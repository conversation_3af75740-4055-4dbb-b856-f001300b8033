package cn.com.sipsg.common.dynamic.core.pojo.vo;

import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * i查询分页返回对象
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Schema(description = "i查询分页返回对象")
@Getter
@Setter
@Accessors(chain = true)
public class IQueryPageVO<T> extends CommonPageVO<T> {

    /**
     * 查询名称
     */
    @Schema(description = "查询名称")
    private String name;

    /**
     * 查询编码
     */
    @Schema(description = "查询编码")
    private String apiId;

}

package cn.com.sipsg.common.dynamic.core.controller;

import cn.com.sipsg.common.constant.PageConstants;
import cn.com.sipsg.common.dynamic.core.pojo.bo.DynamicServerQueryBO;
import cn.com.sipsg.common.dynamic.core.pojo.bo.DynamicServerSaveBO;
import cn.com.sipsg.common.dynamic.core.pojo.vo.DynamicServerVO;
import cn.com.sipsg.common.dynamic.core.pojo.vo.ServerListResultVO;
import cn.com.sipsg.common.dynamic.core.service.DynamicServerService;
import cn.com.sipsg.common.operationlog.core.annotation.OperationLog;
import cn.com.sipsg.common.operationlog.core.enums.OperationTypeEnum;
import cn.com.sipsg.common.pojo.CommonResult;
import cn.com.sipsg.common.pojo.bo.UpdateStatusBO;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.validation.AddValidated;
import cn.com.sipsg.common.validation.UpdateValidated;
import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

/**
 * 服务管理控制器
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Tag(name = "服务管理")
@RestController
@RequiredArgsConstructor
@RequestMapping("/dynamic/server")
public class DynamicServerController {

    private final DynamicServerService serverService;

    /**
     * 查询服务分页列表
     *
     * @param bo 参数
     * @return 服务分页列表
     */
    @Operation(summary = "查询服务分页列表")
    @OperationLog(module = "服务管理", value = "查询服务分页列表", type = OperationTypeEnum.QUERY, saveResponse = false)
    @SaCheckPermission(value = "dynamic:server:all")
    @PostMapping("/page")
    public CommonResult<CommonPageVO<DynamicServerVO>> page(@RequestBody DynamicServerQueryBO bo) {
        return CommonResult.data(serverService.page(bo));
    }

    /**
     * 查询服务列表
     *
     * @param bo 参数
     * @return 服务列表
     */
    @Operation(summary = "查询服务列表")
    @OperationLog(module = "服务管理", value = "查询服务列表", type = OperationTypeEnum.QUERY, saveResponse = false)
    @SaCheckPermission(value = "dynamic:server:all")
    @PostMapping("/list")
    public CommonResult<List<DynamicServerVO>> list(@RequestBody DynamicServerQueryBO bo) {
        bo.setSize(PageConstants.SIZE_NONE);
        return CommonResult.data(serverService.page(bo).getRecords());
    }

    /**
     * 新增服务
     *
     * @param bo 参数
     * @return 服务ID
     */
    @Operation(summary = "新增服务")
    @OperationLog(module = "服务管理", value = "新增服务", type = OperationTypeEnum.SAVE)
    @SaCheckPermission(value = "dynamic:server:all")
    @PostMapping("/save")
    public CommonResult<String> save(@AddValidated @RequestBody DynamicServerSaveBO bo) {
        return CommonResult.data(serverService.save(bo));
    }

    /**
     * 编辑服务
     *
     * @param bo 参数
     */
    @Operation(summary = "编辑服务")
    @OperationLog(module = "服务管理", value = "编辑服务", type = OperationTypeEnum.UPDATE)
    @SaCheckPermission(value = "dynamic:server:all")
    @PostMapping("/update")
    public CommonResult<Void> update(@UpdateValidated @RequestBody DynamicServerSaveBO bo) {
        serverService.update(bo);
        return CommonResult.success();
    }

    /**
     * 查询服务详情
     *
     * @param id 服务ID
     */
    @Operation(summary = "查询服务详情")
    @Parameter(name = "id", description = "服务ID", required = true, example = "1111")
    @OperationLog(module = "服务管理", value = "查询服务详情", type = OperationTypeEnum.QUERY, saveResponse = false)
    @SaCheckPermission(value = "dynamic:server:all")
    @GetMapping("/detail")
    public CommonResult<DynamicServerVO> detail(@RequestParam String id) {
        return CommonResult.data(serverService.detail(id));
    }

    /**
     * 编辑服务状态
     *
     * @param bo 参数
     */
    @Operation(summary = "编辑服务状态")
    @OperationLog(module = "服务管理", value = "编辑服务状态", type = OperationTypeEnum.UPDATE)
    @SaCheckPermission(value = "dynamic:server:all")
    @PostMapping("/updateStatus")
    public CommonResult<Void> updateStatus(@Validated @RequestBody UpdateStatusBO bo) {
        serverService.updateStatus(bo.getId(), bo.getStatus());
        return CommonResult.success();
    }

    /**
     * 删除服务
     *
     * @param id 服务ID
     */
    @Operation(summary = "删除服务")
    @Parameter(name = "id", description = "服务ID", required = true, example = "1111")
    @OperationLog(module = "服务管理", value = "删除服务", type = OperationTypeEnum.DELETE)
    @SaCheckPermission(value = "dynamic:server:all")
    @PostMapping("/delete")
    public CommonResult<Void> delete(@RequestParam String id) {
        serverService.delete(id);
        return CommonResult.success();
    }

    /**
     * 查询服务分组列表
     *
     * @param serverType 服务类型
     * @return 分组列表
     */
    @Operation(summary = "查询服务分组列表")
    @Parameter(name = "serverType", description = "服务类型", required = true, example = "TABLE")
    @OperationLog(module = "服务管理", value = "查询服务分组列表", type = OperationTypeEnum.QUERY, saveResponse = false)
    @GetMapping("/groupList")
    public CommonResult<Set<String>> getGroupList(@RequestParam String serverType) {
        return CommonResult.data(serverService.getGroupList(serverType));
    }

    /**
     * 查询关联服务列表
     *
     * @return 关联服务列表
     */
    @Operation(summary = "查询关联服务列表")
    @GetMapping("/getServerList")
    public CommonResult<List<ServerListResultVO>> getServerList() {
        return CommonResult.data(serverService.getServerList());
    }

}
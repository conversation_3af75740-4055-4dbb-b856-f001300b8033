package cn.com.sipsg.common.dynamic.core.service.impl;

import cn.com.sipsg.common.datasource.core.entity.Datasource;
import cn.com.sipsg.common.datasource.core.mapper.DatasourceMapper;
import cn.com.sipsg.common.dynamic.core.constant.DynamicConstants;
import cn.com.sipsg.common.dynamic.core.convert.DynamicServerConvert;
import cn.com.sipsg.common.dynamic.core.entity.DynamicServer;
import cn.com.sipsg.common.dynamic.core.entity.DynamicTemplate;
import cn.com.sipsg.common.dynamic.core.enums.DynamicServerResultTypeEnums;
import cn.com.sipsg.common.dynamic.core.mapper.DynamicServerMapper;
import cn.com.sipsg.common.dynamic.core.mapper.DynamicTemplateMapper;
import cn.com.sipsg.common.dynamic.core.pojo.bo.DynamicServerQueryBO;
import cn.com.sipsg.common.dynamic.core.pojo.bo.DynamicServerSaveBO;
import cn.com.sipsg.common.dynamic.core.pojo.vo.DynamicServerVO;
import cn.com.sipsg.common.dynamic.core.pojo.vo.ServerListResultVO;
import cn.com.sipsg.common.dynamic.core.service.DynamicServerService;
import cn.com.sipsg.common.exception.enums.ErrorCodeEnum;
import cn.com.sipsg.common.mybatis.core.query.LambdaQueryWrapperX;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.util.*;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.Validator;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * 服务服务实现类
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Service
@RequiredArgsConstructor
public class DynamicServerServiceImpl extends ServiceImpl<DynamicServerMapper, DynamicServer> implements DynamicServerService {

    private final DatasourceMapper datasourceMapper;

    private final DynamicTemplateMapper dynamicTemplateMapper;

    private final Validator validator;

    @Override
    public CommonPageVO<DynamicServerVO> page(DynamicServerQueryBO bo) {
        LambdaQueryWrapperX<DynamicServer> queryWrapper = new LambdaQueryWrapperX<>();
        // 填充查询条件
        queryWrapper.likeIfPresent(DynamicServer::getServerName, bo.getServerName())
                .likeIfPresent(DynamicServer::getServerCode, bo.getServerCode())
                .eq(DynamicServer::getServerType, bo.getServerType())
                .eqIfPresent(DynamicServer::getStatus, bo.getStatus())
                .eqIfPresent(DynamicServer::getDbId, bo.getDbId())
                .eqIfPresent(DynamicServer::getRequestMethod, bo.getRequestMethod())
                .eqIfPresent(DynamicServer::getGroupName, bo.getGroupName());
        return this.page(bo, queryWrapper).convert(entity -> BeanUtils.copyProperties(entity, DynamicServerVO.class));
    }

    @Override
    @Transactional
    public String save(DynamicServerSaveBO bo) {
        bo.setId(null);
        // 校验服务
        checkSaveOrUpdate(bo);
        // 新增服务
        DynamicServer dynamicServer = DynamicServerConvert.INSTANCE.convert(bo);
        this.save(dynamicServer);
        return dynamicServer.getId();
    }

    @Override
    @Transactional
    public void update(DynamicServerSaveBO bo) {
        // 校验服务
        checkSaveOrUpdate(bo);
        // 编辑服务
        this.updateById(DynamicServerConvert.INSTANCE.convert(bo));
    }

    @Override
    public DynamicServerVO detail(String id) {
        DynamicServer dynamicServer = this.getById(id);
        // 校验是否存在
        AssertUtils.isTrue(dynamicServer == null, ErrorCodeEnum.DATA_NOT_EXIST);
        return BeanUtils.copyProperties(dynamicServer, DynamicServerVO.class);
    }

    @Override
    @Transactional
    public void delete(String id) {
        DynamicServer dynamicServer = this.getById(id);
        if (dynamicServer != null) {
            // 删除服务
            this.removeById(id);
        }
    }

    @Override
    @Transactional
    public void updateStatus(String id, Integer status) {
        // 校验服务
        this.checkExist(id);
        // 更新状态
        DynamicServer updateObj = new DynamicServer();
        updateObj.setId(id);
        updateObj.setStatus(DynamicServerConvert.INSTANCE.status2Enum(status));
        this.updateById(updateObj);
    }

    @Override
    public Set<String> getGroupList(String serverType) {
        LambdaQueryWrapperX<DynamicServer> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.select(DynamicServer::getGroupName)
                .eq(DynamicServer::getServerType, serverType)
                .isNotNull(DynamicServer::getGroupName)
                .ne(DynamicServer::getGroupName, StrUtil.EMPTY);
        return CollectionUtils.convertSet(baseMapper.selectList(queryWrapper), DynamicServer::getGroupName);
    }

    @Override
    public List<ServerListResultVO> getServerList() {
        List<ServerListResultVO> serverListResultList = new ArrayList<>();
        // 添加表查询
        ServerListResultVO tableServerResult = new ServerListResultVO();
        tableServerResult.setId(DynamicConstants.SERVER_TYPE_TABLE);
        tableServerResult.setServerName("表查询");
        List<DynamicServer> tableServerList = this.baseMapper.selectList(DynamicServer::getServerType, DynamicConstants.SERVER_TYPE_TABLE);
        tableServerResult.setChildren(BeanUtils.copyToList(tableServerList, ServerListResultVO.class));
        serverListResultList.add(tableServerResult);
        // 添加arcgis
        ServerListResultVO arcgisServerResult = new ServerListResultVO();
        arcgisServerResult.setId(DynamicConstants.SERVER_TYPE_ARCGIS);
        arcgisServerResult.setServerName("ArcGIS Rest");
        List<DynamicServer> arcgisServerList = this.baseMapper.selectList(DynamicServer::getServerType, DynamicConstants.SERVER_TYPE_ARCGIS);
        arcgisServerResult.setChildren(BeanUtils.copyToList(arcgisServerList, ServerListResultVO.class));
        serverListResultList.add(arcgisServerResult);
        // 添加代理
        ServerListResultVO proxyServerResult = new ServerListResultVO();
        proxyServerResult.setId(DynamicConstants.SERVER_TYPE_PROXY);
        proxyServerResult.setServerName("代理");
        List<DynamicServer> proxyServerList = this.baseMapper.selectList(DynamicServer::getServerType, DynamicConstants.SERVER_TYPE_PROXY);
        proxyServerResult.setChildren(BeanUtils.copyToList(proxyServerList, ServerListResultVO.class));
        serverListResultList.add(proxyServerResult);
        return serverListResultList;
    }

    private void checkSaveOrUpdate(DynamicServerSaveBO bo) {
        // 校验服务类型
        if (DynamicConstants.SERVER_TYPE_TABLE.equals(bo.getServerType())) {
            // 表查询
            ValidationUtils.validate(validator, bo, DynamicServerSaveBO.TableServerTypeGroup.class);
            // 校验数据源存在
            AssertUtils.isTrue(datasourceMapper.selectCount(Datasource::getId, bo.getDbId()) == 0, "数据源不存在");
        } else if (DynamicConstants.SERVER_TYPE_PROXY.equals(bo.getServerType())) {
            // 代理
            ValidationUtils.validate(validator, bo, DynamicServerSaveBO.ProxyServerTypeGroup.class);
        } else {
            // ARCGIS
            ValidationUtils.validate(validator, bo, DynamicServerSaveBO.ArcgisServerTypeGroup.class);
        }
        // 校验返回类型
        if (StrUtil.isNotBlank(bo.getResultType())) {
            DynamicServerResultTypeEnums resultType = EnumUtils.getEnumByValue(DynamicServerResultTypeEnums.class, bo.getResultType());
            if (resultType == DynamicServerResultTypeEnums.TEMPLATE) {
                ValidationUtils.validate(validator, bo, DynamicServerSaveBO.TemplateResultTypeGroup.class);
                AssertUtils.isTrue(dynamicTemplateMapper.selectCount(DynamicTemplate::getId, bo.getTemplateId()) == 0, "模板不存在");
            }
            if (resultType == DynamicServerResultTypeEnums.CHART) {
                ValidationUtils.validate(validator, bo, DynamicServerSaveBO.ChartResultTypeGroup.class);
            }
        }
        // 生成服务编码
        if (ObjectUtil.isNull(bo.getId()) && StrUtil.isBlank(bo.getServerCode())) {
            bo.setServerCode(bo.getServerType() + "CX" + System.currentTimeMillis() + RandomUtil.randomStringUpper(6));
        }
        // 校验服务编码唯一
        Long count = baseMapper.selectCount(new LambdaQueryWrapperX<DynamicServer>().eq(DynamicServer::getServerCode, bo.getServerCode()).neIfPresent(DynamicServer::getId, bo.getId()));
        AssertUtils.isTrue(count > 0, "服务编码已存在");
    }

}
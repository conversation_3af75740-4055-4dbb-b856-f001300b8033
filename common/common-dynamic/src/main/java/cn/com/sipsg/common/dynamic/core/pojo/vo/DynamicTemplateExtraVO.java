package cn.com.sipsg.common.dynamic.core.pojo.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fhs.core.trans.vo.VO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 模板更多配置VO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "模板更多配置VO")
public class DynamicTemplateExtraVO implements VO {

    /**
     * 更多配置ID
     */
    @TableId
    @Schema(description = "更多配置ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private String id;

    /**
     * 模板ID
     */
    @Schema(description = "模板ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private String templateId;

    /**
     * 更多配置名称
     */
    @Schema(description = "更多配置名称")
    private String extraName;

    /**
     * 更多配置编码
     */
    @Schema(description = "更多配置编码")
    private String extraCode;

    /**
     * 更多配置方法
     */
    @Schema(description = "更多配置方法")
    private String extraMethod;

    /**
     * 是否可见
     */
    @Schema(description = "是否可见", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean visible;

}
package cn.com.sipsg.common.dynamic.core.filter;

import cn.com.sipsg.common.constant.PageConstants;
import cn.com.sipsg.common.dynamic.core.constant.DynamicConstants;
import cn.com.sipsg.common.dynamic.core.pojo.vo.IQueryPageVO;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Page过滤器
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public class DynamicPageFilter implements DynamicFilter {

    @Override
    @SuppressWarnings("unchecked")
    public boolean doFilter(List results) {
        List<Object> objects = new ArrayList<>();
        Map result = (Map)results.get(0);
        long total = MapUtil.getLong(result, "count");
        long current = MapUtil.getLong(result, PageConstants.CURRENT);
        long size = MapUtil.getLong(result, PageConstants.SIZE);
        String name = MapUtil.getStr(result, "name");
        String apiId = MapUtil.getStr(result, "apiId");
        List<Map> dynamicResult = (List<Map>)result.get(DynamicConstants.RESULT_SET_KEY);
        CommonPageVO<Map> pageVO;
        if (StrUtil.isNotBlank(name) || StrUtil.isNotBlank(apiId)) {
            pageVO = new IQueryPageVO<Map>().setName(name).setApiId(apiId);
        } else {
            pageVO = new CommonPageVO<>();
        }
        pageVO.setRecords(dynamicResult)
                .setSize(size)
                .setCurrent(current)
                .setTotal(total);
        objects.add(pageVO);
        result.put(DynamicConstants.RESULT_SET_KEY, objects);
        return true;
    }

}

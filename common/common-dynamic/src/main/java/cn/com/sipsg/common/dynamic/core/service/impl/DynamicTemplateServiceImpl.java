package cn.com.sipsg.common.dynamic.core.service.impl;

import cn.com.sipsg.common.dynamic.core.entity.DynamicTemplate;
import cn.com.sipsg.common.dynamic.core.entity.DynamicTemplateExtra;
import cn.com.sipsg.common.dynamic.core.entity.DynamicTemplateField;
import cn.com.sipsg.common.dynamic.core.mapper.DynamicTemplateExtraMapper;
import cn.com.sipsg.common.dynamic.core.mapper.DynamicTemplateFieldMapper;
import cn.com.sipsg.common.dynamic.core.mapper.DynamicTemplateMapper;
import cn.com.sipsg.common.dynamic.core.pojo.bo.DynamicTemplateExtraSaveBO;
import cn.com.sipsg.common.dynamic.core.pojo.bo.DynamicTemplateFieldSaveBO;
import cn.com.sipsg.common.dynamic.core.pojo.bo.DynamicTemplateQueryBO;
import cn.com.sipsg.common.dynamic.core.pojo.bo.DynamicTemplateSaveBO;
import cn.com.sipsg.common.dynamic.core.pojo.vo.DynamicTemplateVO;
import cn.com.sipsg.common.dynamic.core.service.DynamicTemplateService;
import cn.com.sipsg.common.exception.enums.ErrorCodeEnum;
import cn.com.sipsg.common.mybatis.core.query.LambdaQueryWrapperX;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.util.AssertUtils;
import cn.com.sipsg.common.util.BeanUtils;
import cn.com.sipsg.common.util.CollectionUtils;
import cn.com.sipsg.common.util.ValidationUtils;
import cn.com.sipsg.common.validation.group.AddGroup;
import cn.com.sipsg.common.validation.group.UpdateGroup;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.Validator;
import javax.validation.groups.Default;
import java.util.Map;

/**
 * 模板服务实现类
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Service
@RequiredArgsConstructor
public class DynamicTemplateServiceImpl extends ServiceImpl<DynamicTemplateMapper, DynamicTemplate> implements DynamicTemplateService {

    private final DynamicTemplateExtraMapper dynamicTemplateExtraMapper;

    private final DynamicTemplateFieldMapper dynamicTemplateFieldMapper;

    private final Validator validator;

    @Override
    public CommonPageVO<DynamicTemplateVO> page(DynamicTemplateQueryBO bo) {
        LambdaQueryWrapperX<DynamicTemplate> queryWrapper = new LambdaQueryWrapperX<>();
        // 填充查询条件
        queryWrapper.likeIfPresent(DynamicTemplate::getTemplateName, bo.getTemplateName())
                .likeIfPresent(DynamicTemplate::getTemplateCode, bo.getTemplateCode());
        return this.page(bo, queryWrapper).convert(entity -> BeanUtils.copyProperties(entity, DynamicTemplateVO.class));
    }

    @Override
    @Transactional
    public String save(DynamicTemplateSaveBO bo) {
        bo.setId(null);
        // 新增模板
        DynamicTemplate dynamicTemplate = BeanUtils.copyProperties(bo, DynamicTemplate.class);
        // 生成模板编码
        dynamicTemplate.setTemplateCode("SJMB" + System.currentTimeMillis());
        this.save(dynamicTemplate);
        // 新增模板字段配置
        if (CollUtil.isNotEmpty(bo.getFieldList())) {
            for (DynamicTemplateFieldSaveBO r : bo.getFieldList()) {
                r.setTemplateId(dynamicTemplate.getId());
                ValidationUtils.validate(validator, r, AddGroup.class, Default.class);
                dynamicTemplateFieldMapper.insert(BeanUtils.copyProperties(r, DynamicTemplateField.class, DynamicTemplateField.Fields.id));
            }
        }
        // 新增模板更多配置
        if (CollUtil.isNotEmpty(bo.getExtraList())) {
            for (DynamicTemplateExtraSaveBO r : bo.getExtraList()) {
                r.setTemplateId(dynamicTemplate.getId());
                ValidationUtils.validate(validator, r, AddGroup.class, Default.class);
                dynamicTemplateExtraMapper.insert(BeanUtils.copyProperties(r, DynamicTemplateExtra.class, DynamicTemplateExtra.Fields.id));
            }
        }
        return dynamicTemplate.getId();
    }

    @Override
    @Transactional
    public void update(DynamicTemplateSaveBO bo) {
        // 编辑模板
        DynamicTemplate dynamicTemplate = BeanUtils.copyProperties(bo, DynamicTemplate.class);
        this.updateById(dynamicTemplate);
        // 编辑模板字段配置
        // 查询已存在模板字段配置
        Map<String, DynamicTemplateField> existFieldMap = CollectionUtils.convertMap(dynamicTemplateFieldMapper.selectList(DynamicTemplateField::getTemplateId, dynamicTemplate.getId()), DynamicTemplateField::getId);
        if (CollUtil.isNotEmpty(bo.getFieldList())) {
            for (DynamicTemplateFieldSaveBO r : bo.getFieldList()) {
                r.setTemplateId(bo.getId());
                if (ObjectUtil.isNotEmpty(r.getId())) {
                    ValidationUtils.validate(validator, r, UpdateGroup.class, Default.class);
                    dynamicTemplateFieldMapper.updateById(BeanUtils.copyProperties(r, DynamicTemplateField.class));
                    existFieldMap.remove(r.getId());
                } else {
                    ValidationUtils.validate(validator, r, AddGroup.class, Default.class);
                    dynamicTemplateFieldMapper.insert(BeanUtils.copyProperties(r, DynamicTemplateField.class));
                }
            }
        }
        // 删除未匹配的模板字段配置
        if (CollUtil.isNotEmpty(existFieldMap)) {
            dynamicTemplateFieldMapper.deleteBatchIds(existFieldMap.keySet());
        }
        // 编辑模板更多配置
        // 查询已存在模板更多配置
        Map<String, DynamicTemplateExtra> existExtraMap = CollectionUtils.convertMap(dynamicTemplateExtraMapper.selectList(DynamicTemplateExtra::getTemplateId, dynamicTemplate.getId()), DynamicTemplateExtra::getId);
        if (CollUtil.isNotEmpty(bo.getExtraList())) {
            for (DynamicTemplateExtraSaveBO r : bo.getExtraList()) {
                r.setTemplateId(bo.getId());
                if (ObjectUtil.isNotEmpty(r.getId())) {
                    ValidationUtils.validate(validator, r, UpdateGroup.class, Default.class);
                    dynamicTemplateExtraMapper.updateById(BeanUtils.copyProperties(r, DynamicTemplateExtra.class));
                    existExtraMap.remove(r.getId());
                } else {
                    ValidationUtils.validate(validator, r, AddGroup.class, Default.class);
                    dynamicTemplateExtraMapper.insert(BeanUtils.copyProperties(r, DynamicTemplateExtra.class));
                }
            }
        }
        // 删除未匹配的模板更多配置
        if (CollUtil.isNotEmpty(existExtraMap)) {
            dynamicTemplateExtraMapper.deleteBatchIds(existExtraMap.keySet());
        }
    }

    @Override
    public DynamicTemplateVO detail(String id) {
        DynamicTemplate dynamicTemplate = this.getByIdDeep(id);
        // 校验是否存在
        AssertUtils.isTrue(dynamicTemplate == null, ErrorCodeEnum.DATA_NOT_EXIST);
        return BeanUtils.copyProperties(dynamicTemplate, DynamicTemplateVO.class);
    }

    @Override
    @Transactional
    public void delete(String id) {
        DynamicTemplate dynamicTemplate = this.getById(id);
        if (dynamicTemplate != null) {
            // 删除模板
            this.removeById(id);
            // 删除模板字段配置
            dynamicTemplateFieldMapper.delete(DynamicTemplateField::getTemplateId, dynamicTemplate.getId());
            // 删除模板更多配置
            dynamicTemplateExtraMapper.delete(DynamicTemplateExtra::getTemplateId, dynamicTemplate.getId());
        }
    }

}
package cn.com.sipsg.common.dynamic.core.controller;

import cn.com.sipsg.common.dynamic.core.entity.DynamicTemplate;
import cn.com.sipsg.common.dynamic.core.pojo.bo.DynamicTemplateQueryBO;
import cn.com.sipsg.common.dynamic.core.pojo.bo.DynamicTemplateSaveBO;
import cn.com.sipsg.common.dynamic.core.pojo.vo.DynamicTemplateSimpleVO;
import cn.com.sipsg.common.dynamic.core.pojo.vo.DynamicTemplateVO;
import cn.com.sipsg.common.dynamic.core.service.DynamicTemplateService;
import cn.com.sipsg.common.operationlog.core.annotation.OperationLog;
import cn.com.sipsg.common.operationlog.core.enums.OperationTypeEnum;
import cn.com.sipsg.common.pojo.CommonResult;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.util.BeanUtils;
import cn.com.sipsg.common.validation.AddValidated;
import cn.com.sipsg.common.validation.UpdateValidated;
import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 模板管理控制器
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Tag(name = "模板管理")
@RestController
@RequiredArgsConstructor
@RequestMapping("/dynamic/template")
public class DynamicTemplateController {

    private final DynamicTemplateService dynamicTemplateService;

    /**
     * 查询模板分页列表
     *
     * @param bo 参数
     * @return 模板分页列表
     */
    @Operation(summary = "查询模板分页列表")
    @OperationLog(module = "模板管理", value = "查询模板分页列表", type = OperationTypeEnum.QUERY, saveResponse = false)
    @SaCheckPermission(value = "dynamic:template:all")
    @PostMapping("/page")
    public CommonResult<CommonPageVO<DynamicTemplateVO>> page(@RequestBody DynamicTemplateQueryBO bo) {
        return CommonResult.data(dynamicTemplateService.page(bo));
    }

    /**
     * 查询模板简单信息列表
     *
     * @return 模板简单信息列表
     */
    @Operation(summary = "查询模板简单信息列表")
    @GetMapping("/simpleList")
    public CommonResult<List<DynamicTemplateSimpleVO>> getSimpleList() {
        List<DynamicTemplate> list = dynamicTemplateService.list();
        return CommonResult.data(BeanUtils.copyToList(list, DynamicTemplateSimpleVO.class));
    }

    /**
     * 新增模板
     *
     * @param bo 参数
     * @return 模板ID
     */
    @Operation(summary = "新增模板")
    @OperationLog(module = "模板管理", value = "新增模板", type = OperationTypeEnum.SAVE)
    @SaCheckPermission(value = "dynamic:template:all")
    @PostMapping("/save")
    public CommonResult<String> save(@AddValidated @RequestBody DynamicTemplateSaveBO bo) {
        return CommonResult.data(dynamicTemplateService.save(bo));
    }

    /**
     * 编辑模板
     *
     * @param bo 参数
     */
    @Operation(summary = "编辑模板")
    @OperationLog(module = "模板管理", value = "编辑模板", type = OperationTypeEnum.UPDATE)
    @SaCheckPermission(value = "dynamic:template:all")
    @PostMapping("/update")
    public CommonResult<Void> update(@UpdateValidated @RequestBody DynamicTemplateSaveBO bo) {
        dynamicTemplateService.update(bo);
        return CommonResult.success();
    }

    /**
     * 查询模板详情
     *
     * @param id 模板ID
     */
    @Operation(summary = "查询模板详情")
    @Parameter(name = "id", description = "模板ID", required = true, example = "1111")
    @OperationLog(module = "模板管理", value = "查询模板详情", type = OperationTypeEnum.QUERY, saveResponse = false)
    @SaCheckPermission(value = "dynamic:template:all")
    @GetMapping("/detail")
    public CommonResult<DynamicTemplateVO> detail(@RequestParam String id) {
        return CommonResult.data(dynamicTemplateService.detail(id));
    }

    /**
     * 删除模板
     *
     * @param id 模板ID
     */
    @Operation(summary = "删除模板")
    @Parameter(name = "id", description = "模板ID", required = true, example = "1111")
    @OperationLog(module = "模板管理", value = "删除模板", type = OperationTypeEnum.DELETE)
    @SaCheckPermission(value = "dynamic:template:all")
    @PostMapping("/delete")
    public CommonResult<Void> delete(@RequestParam String id) {
        dynamicTemplateService.delete(id);
        return CommonResult.success();
    }

}
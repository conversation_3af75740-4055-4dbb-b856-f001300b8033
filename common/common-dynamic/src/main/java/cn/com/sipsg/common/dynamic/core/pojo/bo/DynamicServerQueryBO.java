package cn.com.sipsg.common.dynamic.core.pojo.bo;

import cn.com.sipsg.common.enums.CommonStatusEnum;
import cn.com.sipsg.common.pojo.bo.SortablePageBO;
import cn.com.sipsg.common.validation.InEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 服务查询BO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "服务查询BO")
public class DynamicServerQueryBO extends SortablePageBO {

    /**
     * 服务名称
     */
    @Schema(description = "服务名称")
    private String serverName;

    /**
     * 服务编码
     */
    @Schema(description = "服务编码")
    private String serverCode;

    /**
     * 数据源ID
     */
    @Schema(description = "数据源ID")
    private String dbId;

    /**
     * 请求方法
     */
    @Schema(description = "请求方法")
    private String requestMethod;

    /**
     * 服务类型
     */
    @Schema(description = "服务类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "服务类型不能为空")
    @Pattern(regexp = "(TABLE|ARCGIS|PROXY)?", message = "服务类型不正确")
    private String serverType;

    /**
     * 状态
     */
    @Schema(description = "状态")
    @InEnum(enumClass = CommonStatusEnum.class, message = "状态不正确")
    private Integer status;

    /**
     * 分组名称
     */
    @Schema(description = "分组名称")
    private String groupName;

}
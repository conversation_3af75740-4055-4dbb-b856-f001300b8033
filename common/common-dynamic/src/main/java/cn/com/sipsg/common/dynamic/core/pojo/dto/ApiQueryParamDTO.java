package cn.com.sipsg.common.dynamic.core.pojo.dto;

import lombok.Getter;
import lombok.Setter;

/**
 * 参数配置 DTO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
public class ApiQueryParamDTO {

    /**
     * 主键id
     */
    private String id;

    /**
     * 查询ID
     */
    private String apiId;

    /**
     * 入参名
     */
    private String paramName;

    /**
     * 操作符
     */
    private String operator;

    /**
     * 条件
     */
    private String condition;

    /**
     * 是否必填
     */
    private Boolean required;

    /**
     * 顺序
     */
    private Integer sort;

    /**
     * 数据类型
     */
    private String dataType;

}

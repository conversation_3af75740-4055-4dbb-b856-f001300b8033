package cn.com.sipsg.common.dynamic.core.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 模板简单信息VO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "模板简单信息VO")
public class DynamicTemplateSimpleVO {

    /**
     * 模板ID
     */
    @Schema(description = "模板ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private String id;

    /**
     * 模板名称
     */
    @Schema(description = "模板名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String templateName;

    /**
     * 模板编码
     */
    @Schema(description = "模板编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String templateCode;

}
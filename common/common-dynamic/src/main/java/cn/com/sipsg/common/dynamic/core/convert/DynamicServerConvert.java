package cn.com.sipsg.common.dynamic.core.convert;

import cn.com.sipsg.common.dynamic.core.entity.DynamicServer;
import cn.com.sipsg.common.dynamic.core.enums.DynamicServerResultTypeEnums;
import cn.com.sipsg.common.dynamic.core.pojo.bo.DynamicServerSaveBO;
import cn.com.sipsg.common.enums.CommonStatusEnum;
import cn.com.sipsg.common.util.EnumUtils;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2025/04/14
 */
@Mapper
public interface DynamicServerConvert {

    DynamicServerConvert INSTANCE = Mappers.getMapper(DynamicServerConvert.class);

    DynamicServer convert(DynamicServerSaveBO bo);

    default CommonStatusEnum status2Enum(Integer status) {
        return EnumUtils.getEnumByValue(CommonStatusEnum.class, status);
    }

    default DynamicServerResultTypeEnums resultType2Enum(String resultType) {
        return EnumUtils.getEnumByValue(DynamicServerResultTypeEnums.class, resultType);
    }

}

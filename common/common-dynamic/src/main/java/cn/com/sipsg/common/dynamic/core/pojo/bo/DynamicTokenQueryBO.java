package cn.com.sipsg.common.dynamic.core.pojo.bo;

import cn.com.sipsg.common.pojo.bo.SortablePageBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 密钥查询BO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "密钥查询BO")
public class DynamicTokenQueryBO extends SortablePageBO {

    /**
     * 密钥名称
     */
    @Schema(description = "密钥名称")
    private String tokenName;

    /**
     * 密钥编码
     */
    @Schema(description = "密钥编码")
    private String tokenCode;

    /**
     * 密钥类型
     */
    @Schema(description = "密钥类型")
    private String tokenType;

    /**
     * 请求方法
     */
    @Schema(description = "请求方法")
    private String requestMethod;

}
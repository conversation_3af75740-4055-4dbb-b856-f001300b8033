package cn.com.sipsg.common.dynamic.core.pojo.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fhs.core.trans.vo.VO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 模板VO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "模板VO")
public class DynamicTemplateVO implements VO {

    /**
     * 模板ID
     */
    @TableId
    @Schema(description = "模板ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private String id;

    /**
     * 模板名称
     */
    @Schema(description = "模板名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String templateName;

    /**
     * 模板编码
     */
    @Schema(description = "模板编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String templateCode;

    /**
     * 关键字id字段名称
     */
    @Schema(description = "关键字id字段名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String keyColumnName;

    /**
     * 显示字段名称
     */
    @Schema(description = "显示字段名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String showColumnName;

    /**
     * 查询缓冲区
     */
    @Schema(description = "查询缓冲区")
    private Integer queryBuffer;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 模板字段配置
     */
    @Schema(description = "模板字段配置")
    private List<DynamicTemplateFieldVO> fieldList;

    /**
     * 模板更多配置
     */
    @Schema(description = "模板更多配置")
    private List<DynamicTemplateExtraVO> extraList;

}
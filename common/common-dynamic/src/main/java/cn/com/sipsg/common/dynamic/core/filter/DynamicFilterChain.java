package cn.com.sipsg.common.dynamic.core.filter;

import cn.com.sipsg.common.dynamic.core.constant.DynamicConstants;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * 责任链
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public class DynamicFilterChain implements DynamicFilter {

    boolean flag = true;
    List<DynamicFilter> filters = new LinkedList<>();

    public DynamicFilterChain add(DynamicFilter f) {
        filters.add(f);
        return this;
    }

    @Override
    @SuppressWarnings("unchecked")
    public boolean doFilter(List results) {
        for (DynamicFilter f : filters) {
            flag = f.doFilter(results);
        }
        HashMap result = (HashMap)results.get(0);
        Object dynamicResult = result.get(DynamicConstants.RESULT_SET_KEY);
        results.clear();
        results.add(dynamicResult);
        return true;
    }
}

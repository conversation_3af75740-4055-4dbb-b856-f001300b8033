package cn.com.sipsg.common.dynamic.core.pojo.bo;

import cn.com.sipsg.common.dynamic.core.enums.DynamicServerResultTypeEnums;
import cn.com.sipsg.common.enums.CommonStatusEnum;
import cn.com.sipsg.common.validation.InEnum;
import cn.com.sipsg.common.validation.InStrEnum;
import cn.com.sipsg.common.validation.group.UpdateGroup;
import cn.hutool.core.lang.RegexPool;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * 服务保存BO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "服务保存BO")
public class DynamicServerSaveBO {

    /**
     * 服务ID
     */
    @Schema(description = "服务ID")
    @NotBlank(message = "服务ID不能为空", groups = {UpdateGroup.class})
    private String id;

    /**
     * 服务名称
     */
    @Schema(description = "服务名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "服务名称不能为空")
    private String serverName;

    /**
     * 服务编码
     */
    @Schema(description = "服务编码")
    @NotBlank(message = "服务编码不能为空", groups = {UpdateGroup.class})
    private String serverCode;

    /**
     * 服务类型;TABLE，ARCGIS，PROXY
     */
    @Schema(description = "服务类型;TABLE，ARCGIS，PROXY", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "服务类型不能为空")
    @Pattern(regexp = "(TABLE|ARCGIS|PROXY)?", message = "服务类型不正确")
    private String serverType;

    /**
     * 状态
     */
    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "状态不能为空")
    @InEnum(enumClass = CommonStatusEnum.class, message = "状态不正确")
    private Integer status;

    /**
     * 查询类型;TABLE，SQL
     */
    @Schema(description = "查询类型;TABLE，SQL")
    @Pattern(regexp = "(TABLE|SQL)?", message = "查询类型不正确")
    @NotBlank(message = "查询类型不能为空", groups = {TableServerTypeGroup.class})
    private String queryType;

    /**
     * 表名或查询语句
     */
    @Schema(description = "表名或查询语句")
    @NotBlank(message = "表名或查询语句不能为空", groups = {TableServerTypeGroup.class})
    private String querySql;

    /**
     * 分组名称
     */
    @Schema(description = "分组名称")
    private String groupName;

    /**
     * 返回结果类型
     */
    @Schema(description = "返回结果类型")
    @InStrEnum(enumClass = DynamicServerResultTypeEnums.class, message = "返回结果类型不正确")
    private String resultType;

    /**
     * 数据源ID
     */
    @Schema(description = "数据源ID")
    @NotBlank(message = "数据源ID不能为空", groups = {TableServerTypeGroup.class})
    private String dbId;

    /**
     * 请求地址
     */
    @Schema(description = "请求地址")
    @NotBlank(message = "请求地址不能为空", groups = {ArcgisServerTypeGroup.class, ProxyServerTypeGroup.class})
    @Pattern(regexp = RegexPool.URL_HTTP, message = "请求地址不正确", groups = {ArcgisServerTypeGroup.class, ProxyServerTypeGroup.class})
    private String requestUrl;

    /**
     * 请求方法;GET，POST
     */
    @Schema(description = "请求方法;GET，POST")
    @NotBlank(message = "请求方法不能为空", groups = {ArcgisServerTypeGroup.class, ProxyServerTypeGroup.class})
    @Pattern(regexp = "(GET|POST)?", message = "请求方法不正确")
    private String requestMethod;

    /**
     * 请求头
     */
    @Schema(description = "请求头")
    private String requestHeader;

    /**
     * 请求体
     */
    @Schema(description = "请求体")
    private String requestBody;

    /**
     * 认证方式
     */
    @Schema(description = "认证方式")
    private String authType;

    /**
     * 令牌ID
     */
    @Schema(description = "令牌ID")
    private String tokenId;

    /**
     * 层号
     */
    @Schema(description = "层号")
    private Integer layerNo;

    /**
     * 过滤条件
     */
    @Schema(description = "过滤条件")
    private String filterCondition;

    /**
     * 模板ID
     */
    @Schema(description = "模板ID")
    @NotBlank(message = "模板ID不能为空", groups = {TemplateResultTypeGroup.class})
    private String templateId;

    /**
     * 图表类型
     */
    @Schema(description = "图表类型")
    @NotBlank(message = "图表类型不能为空", groups = {ChartResultTypeGroup.class})
    @Pattern(regexp = "(LINE|HISTOGRAM|PIE)?", message = "图表类型不正确")
    private String chartType;

    /**
     * 图表标题
     */
    @Schema(description = "图表标题")
    @NotBlank(message = "图表标题不能为空", groups = {ChartResultTypeGroup.class})
    private String chartTitle;

    /**
     * 图表子标题
     */
    @Schema(description = "图表子标题")
    private String chartSubTitle;

    /**
     * 图表X坐标单位
     */
    @Schema(description = "图表X坐标单位")
    private String chartXUnit;

    /**
     * 图表Y坐标单位
     */
    @Schema(description = "图表Y坐标单位")
    private String chartYUnit;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 空间字段配置
     */
    @Schema(description = "空间字段配置")
    private String shapeColumn;

    public interface ChartResultTypeGroup {

    }

    public interface TemplateResultTypeGroup {

    }

    public interface TableServerTypeGroup {

    }

    public interface ArcgisServerTypeGroup {

    }

    public interface ProxyServerTypeGroup {

    }

}
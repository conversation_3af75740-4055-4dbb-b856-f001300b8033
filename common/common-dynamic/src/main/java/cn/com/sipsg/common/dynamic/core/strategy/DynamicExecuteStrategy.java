package cn.com.sipsg.common.dynamic.core.strategy;

import cn.com.sipsg.common.dynamic.core.pojo.dto.ApiDTO;
import cn.hutool.json.JSONObject;

/**
 * 动态执行策略
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public interface DynamicExecuteStrategy {

    /**
     * 动态执行服务类型
     *
     * @return 服务类型
     */
    String dynamicServerType();

    /**
     * 动态执行
     *
     * @param apiDTO 服务配置
     * @param params 参数
     * @return 执行结果
     */
    Object dynamicExecute(ApiDTO apiDTO, JSONObject params);

}

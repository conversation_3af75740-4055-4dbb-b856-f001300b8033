package cn.com.sipsg.common.dynamic.core.filter;

import static cn.com.sipsg.common.constant.CommonConstants.DEFAULT_SHAPE_COLUMN;

import cn.com.sipsg.common.dynamic.core.constant.DynamicConstants;
import cn.com.sipsg.common.dynamic.core.entity.DynamicTemplate;
import cn.com.sipsg.common.dynamic.core.entity.DynamicTemplateExtra;
import cn.com.sipsg.common.dynamic.core.entity.DynamicTemplateField;
import cn.com.sipsg.common.dynamic.core.pojo.dto.ApiDTO;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 模板过滤器
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public class DynamicTemplateFilter implements DynamicFilter {

    @Override
    @SuppressWarnings("unchecked")
    public boolean doFilter(List results) {
        Map result = (Map)results.get(0);
        ApiDTO api = (ApiDTO)result.get("api");
        JSONObject params = (JSONObject)result.get("params");
        String apiCode = StrUtil.blankToDefault(api.getApiCode(), StrUtil.EMPTY);
        List<Map<String, Object>> dynamicResult = (List<Map<String, Object>>)result.get(DynamicConstants.RESULT_SET_KEY);
        DynamicTemplate template = api.getDynamicTemplate();
        if (CollUtil.isEmpty(dynamicResult)) {
            return false;
        }
        String shapeColumn = DEFAULT_SHAPE_COLUMN;
        if (api != null && api.getDynamicServer() != null && StrUtil.isNotBlank(api.getDynamicServer().getShapeColumn())) {
            shapeColumn = api.getDynamicServer().getShapeColumn();
        }
        List<Map<String, Object>> maps = new ArrayList<>();
        for (Map map : dynamicResult) {
            if (map == null) {
                continue;
            }
            Map mapTmp = new HashMap<>();
            List<DynamicTemplateField> fieldList = template.getFieldList();
            String keyColumnName = StrUtil.nullToEmpty(template.getKeyColumnName()); // 模板关键字
            String showColumnName = StrUtil.nullToEmpty(template.getShowColumnName()); //模板名称
            for (DynamicTemplateField field : fieldList) {
                if (keyColumnName.equals(field.getColumnName())) {
                    String id = Optional.ofNullable(map.get(field.getColumnName())).map(Object::toString).orElse(StrUtil.EMPTY);
                    if (StrUtil.isEmpty(id) && StrUtil.isNotBlank(field.getEmptyValueReplacement())) {
                        id = field.getEmptyValueReplacement();
                    }
                    if (StrUtil.isNotBlank(field.getUrlPrefix())) {
                        id = field.getUrlPrefix() + id;
                    }
                    mapTmp.put("id", id); // 模板关键字value
                }
                if (showColumnName.equals(field.getColumnName())) {
                    Object name = Optional.ofNullable(map.get(field.getColumnName())).map(Object::toString).orElse(StrUtil.EMPTY);
                    if (ObjectUtil.isEmpty(name) && StrUtil.isNotBlank(field.getEmptyValueReplacement())) {
                        name = field.getEmptyValueReplacement();
                    }
                    if (StrUtil.isNotBlank(field.getUrlPrefix())) {
                        name = field.getUrlPrefix() + name;
                    }
                    mapTmp.put("name", name); // 模板名称value
                }
            }
            if (BooleanUtil.isTrue(api.getClassifyEnabled())) {
                mapTmp.put("3dtiles", api.getLayerResourceId());
            }
            for (Object key : map.keySet()) {
                if (StrUtil.equalsIgnoreCase(key.toString(), shapeColumn)) {
                    mapTmp.put(DEFAULT_SHAPE_COLUMN, map.get(shapeColumn));
                } else if (StrUtil.equalsIgnoreCase(key.toString(), DEFAULT_SHAPE_COLUMN)) {
                    mapTmp.put(DEFAULT_SHAPE_COLUMN, map.get(key));
                }
            }
            if (CollUtil.isNotEmpty(fieldList)) {
                handleDecimalNum(map, mapTmp, api, apiCode, params);
            }
            maps.add(mapTmp);
        }
        dynamicResult.clear();
        dynamicResult.addAll(maps);
        return true;
    }

    private void handleDecimalNum(Map<String, Object> map, Map<String, Object> mapTmp, ApiDTO api, String apiCode, JSONObject params) {
        LinkedList<Map<String, Object>> detailList = new LinkedList<>();
        LinkedList<Map<String, Object>> qQueryList = new LinkedList<>();
        LinkedList<Map<String, Object>> iQueryList = new LinkedList<>();

        List<DynamicTemplateExtra> extraList = CollectionUtil.isNotEmpty(api.getDynamicTemplate().getExtraList())
            ? api.getDynamicTemplate().getExtraList().stream()
            .map(ObjectUtil::cloneByStream)
            .filter(DynamicTemplateExtra::getVisible)
            .collect(Collectors.toList())
            : new ArrayList<>();

        Map<String, Map<String, String>> dictMap = api.getDictMap();
        List<DynamicTemplateField> fieldList = api.getDynamicTemplate().getFieldList();
        for (DynamicTemplateField field : fieldList) {
            String keyName = field.getColumnName();
            if (CollectionUtil.isNotEmpty(dictMap)) {
                // 先处理字典
                if (StrUtil.isNotBlank(field.getDictCode())) {
                    Map<String, String> dict = dictMap.get(field.getDictCode());
                    if (dict != null) {
                        Optional.ofNullable(map.get(keyName)).map(itemCode -> dict.get(itemCode.toString())).ifPresent(itemName -> map.put(keyName, itemName));
                    }
                }
            }
            // 再处理精度单位
            String value;
            Map<String, Object> tmpMap = new HashMap<>();
            tmpMap.put("key", field.getColumnName());
            tmpMap.put("label", StrUtil.emptyToDefault(field.getLabelName(), field.getColumnName()));
            String precisionFormat = field.getPrecisionFormat();
            if (StrUtil.isNotBlank(precisionFormat) && precisionFormat.matches("^[0-9]*$")) {
                BigDecimal num = BigDecimal.ZERO;
                if (ObjectUtil.isNotEmpty(map.get(keyName))) {
                    String keyValue = map.get(keyName).toString();
                    if (StrUtil.isNotBlank(keyValue)) {
                        num = new BigDecimal(keyValue);
                    }
                }
                num = num.setScale(Integer.parseInt(precisionFormat), RoundingMode.HALF_UP);
                value = num.toEngineeringString();
            } else if (StrUtil.isNotBlank(precisionFormat)) {
                // 判断数据格式转换日期类型
                Object o = map.get(keyName);
                if (o instanceof Long) {
                    value = DateUtil.format(new Date((Long)o), precisionFormat);
                } else if (o instanceof Date) {
                    value = DateUtil.format((Date)o, precisionFormat);
                } else {
                    value = Optional.ofNullable(map.get(keyName)).map(Object::toString).orElse(StrUtil.EMPTY);
                }
            } else {
                value = Optional.ofNullable(map.get(keyName)).map(Object::toString).orElse(StrUtil.EMPTY);
            }
            tmpMap.put("type", field.getDataType());
            if (StrUtil.isBlank(value) && StrUtil.isNotBlank(field.getEmptyValueReplacement())) {
                value = field.getEmptyValueReplacement();
            }
            if (StrUtil.isNotBlank(field.getUrlPrefix())) {
                value = field.getUrlPrefix() + value;
            }
            tmpMap.put("value", value);
            tmpMap.put("unit", field.getUnit());

            if (CollUtil.isNotEmpty(fieldList)) {
                // 详情可见
                if (BooleanUtil.isTrue(field.getDetailVisible())) {
                    detailList.add(tmpMap);
                }
                // 快查可见
                if (BooleanUtil.isTrue(field.getQQueryVisible())) {
                    qQueryList.add(tmpMap);
                }
                // i查可见
                if (BooleanUtil.isTrue(field.getIQueryVisible())) {
                    iQueryList.add(tmpMap);
                }
            }
            if (CollectionUtil.isNotEmpty(extraList)) {
                extraList.forEach(extra -> {
                    String extraJson = extra.getExtraMethod();
                    if (StrUtil.isNotBlank(extraJson)) {
                        extraJson = extraJson.replace(String.format(DynamicConstants.DYNAMIC_PARAM_FORMAT, keyName), Optional.ofNullable(map.get(keyName)).map(Object::toString).orElse(StrUtil.EMPTY))
                            .replace(String.format(DynamicConstants.DYNAMIC_PARAM_FORMAT, "apiCode"), StrUtil.nullToEmpty(apiCode));
                        extra.setExtraMethod(extraJson);
                    }
                });
            }
        }
        if (params != null) {
            JSONObject conditions = params.getJSONObject(DynamicConstants.CONDITIONS_KEY);
            if (ObjectUtil.isNotNull(conditions)) {
                for (String s : conditions.keySet()) {
                    if (CollectionUtil.isNotEmpty(extraList)) {
                        extraList.forEach(extra -> {
                            String extraJson = StrUtil.nullToEmpty(extra.getExtraMethod());
                            extraJson = extraJson.replace(String.format(DynamicConstants.DYNAMIC_PARAM_FORMAT, s), Optional.ofNullable(conditions.get(s)).map(Object::toString).orElse(StrUtil.EMPTY));
                            extra.setExtraMethod(extraJson);
                        });
                    }
                }
            }
        }
        if (!BooleanUtil.isTrue(api.getDetail())) {
            mapTmp.put("extras", extraList);
            mapTmp.put("qQueryList", qQueryList);
            mapTmp.put("iQueryList", iQueryList);
            mapTmp.put("detailList", detailList);
        } else {
            mapTmp.clear();
            mapTmp.put("detailList", detailList);
            mapTmp.put("groupItem", new ArrayList<>());
            mapTmp.put("extras", extraList);
        }
    }

}

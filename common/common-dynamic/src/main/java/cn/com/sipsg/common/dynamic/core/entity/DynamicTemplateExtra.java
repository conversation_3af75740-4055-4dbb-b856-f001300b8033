package cn.com.sipsg.common.dynamic.core.entity;

import cn.com.sipsg.common.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;


/**
 * 模板更多配置
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@FieldNameConstants
@TableName(value = "medox_dynamic_template_extra", autoResultMap = true)
public class DynamicTemplateExtra extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private String id;

    /**
     * 模板ID
     */
    private String templateId;

    /**
     * 更多配置名称
     */
    private String extraName;

    /**
     * 更多配置编码
     */
    private String extraCode;

    /**
     * 更多配置方法
     */
    private String extraMethod;

    /**
     * 是否可见
     */
    private Boolean visible;

}
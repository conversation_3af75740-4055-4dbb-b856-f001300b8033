package cn.com.sipsg.common.dynamic.core.pojo.bo;

import cn.com.sipsg.common.validation.group.UpdateGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 模板更多配置保存BO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "模板更多配置保存BO")
public class DynamicTemplateExtraSaveBO {

    /**
     * 更多配置ID
     */
    @Schema(description = "更多配置ID")
    @NotBlank(message = "更多配置ID不能为空", groups = {UpdateGroup.class})
    private String id;

    /**
     * 模板ID
     */
    @Schema(description = "模板ID")
    @NotBlank(message = "模板ID不能为空", groups = {UpdateGroup.class})
    private String templateId;

    /**
     * 更多配置名称
     */
    @Schema(description = "更多配置名称")
    private String extraName;

    /**
     * 更多配置编码
     */
    @Schema(description = "更多配置编码")
    private String extraCode;

    /**
     * 更多配置方法
     */
    @Schema(description = "更多配置方法")
    private String extraMethod;

    /**
     * 是否可见
     */
    @Schema(description = "是否可见", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "是否可见不能为空")
    private Boolean visible;

}
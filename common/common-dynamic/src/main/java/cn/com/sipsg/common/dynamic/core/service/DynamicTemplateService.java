package cn.com.sipsg.common.dynamic.core.service;

import cn.com.sipsg.common.dynamic.core.entity.DynamicTemplate;
import cn.com.sipsg.common.dynamic.core.pojo.bo.DynamicTemplateQueryBO;
import cn.com.sipsg.common.dynamic.core.pojo.bo.DynamicTemplateSaveBO;
import cn.com.sipsg.common.dynamic.core.pojo.vo.DynamicTemplateVO;
import cn.com.sipsg.common.mybatis.core.service.BaseServiceX;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;

/**
 * 模板服务接口
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public interface DynamicTemplateService extends BaseServiceX<DynamicTemplate> {

    /**
     * 查询模板分页列表
     *
     * @param bo 参数
     * @return 模板分页列表
     */
    CommonPageVO<DynamicTemplateVO> page(DynamicTemplateQueryBO bo);

    /**
     * 新增模板
     *
     * @param bo 参数
     * @return 模板ID
     */
    String save(DynamicTemplateSaveBO bo);

    /**
     * 编辑模板
     *
     * @param bo 参数
     */
    void update(DynamicTemplateSaveBO bo);

    /**
     * 根据id查询模板详情
     *
     * @param id 主键
     * @return 模板详情
     */
    DynamicTemplateVO detail(String id);

    /**
     * 删除模板
     *
     * @param id 主键
     */
    void delete(String id);

}
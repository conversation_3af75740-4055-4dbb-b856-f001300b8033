package cn.com.sipsg.common.dynamic.core.filter;

import cn.com.sipsg.common.dynamic.core.constant.DynamicConstants;
import cn.com.sipsg.common.dynamic.core.pojo.dto.ArcgisDTO;
import cn.com.sipsg.common.dynamic.core.pojo.dto.ArcgisFeatureDTO;
import cn.com.sipsg.common.geo.core.util.GeoUtils;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import static cn.com.sipsg.common.constant.CommonConstants.DEFAULT_SHAPE_COLUMN;

/**
 * Arcgis过滤器
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public class DynamicArcgisFilter implements DynamicFilter {

    @Override
    @SuppressWarnings("unchecked")
    public boolean doFilter(List results) {
        HashMap result = (HashMap)results.get(0);
        if (result.get(DynamicConstants.RESULT_SET_KEY) != null) {
            List<HashMap> maps = new ArrayList<>();
            ArcgisDTO dynamicResult = (ArcgisDTO) result.get(DynamicConstants.RESULT_SET_KEY);
            dealWithArcgisResult(maps, dynamicResult);
            result.put(DynamicConstants.RESULT_SET_KEY, maps);
        }
        return true;
    }

    /**
     * 处理arcgis rest服务返回的数据结构
     */
    private void dealWithArcgisResult(List<HashMap> maps, ArcgisDTO dynamicResult) {
        if (dynamicResult != null) {
            List<ArcgisFeatureDTO> features = dynamicResult.getFeatures();
            if (CollUtil.isNotEmpty(features)) {
                features.forEach(arcgisFeatureDto -> {
                    HashMap attribute = arcgisFeatureDto.getAttributes();
                    HashMap geometry = arcgisFeatureDto.getGeometry();
                    if (geometry != null) {
                        String wkt = GeoUtils.arcgis2wkt(JSON.toJSONString(arcgisFeatureDto));
                        attribute.put(DEFAULT_SHAPE_COLUMN, wkt);
                    }
                    maps.add(attribute);
                });
            }
        }
    }

}

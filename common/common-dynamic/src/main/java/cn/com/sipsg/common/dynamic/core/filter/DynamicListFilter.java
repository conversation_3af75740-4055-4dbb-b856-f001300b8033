package cn.com.sipsg.common.dynamic.core.filter;

import cn.com.sipsg.common.datasource.core.strategy.DatasourceStrategy;
import cn.com.sipsg.common.datasource.core.util.DatasourceUtils;
import cn.com.sipsg.common.dynamic.core.constant.DynamicConstants;
import cn.com.sipsg.common.dynamic.core.pojo.dto.ApiDTO;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import static cn.com.sipsg.common.constant.CommonConstants.DEFAULT_SHAPE_COLUMN;

/**
 * List过滤器
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Slf4j
public class DynamicListFilter implements DynamicFilter {

    @Override
    @SuppressWarnings("unchecked")
    public boolean doFilter(List results) {
        HashMap result = (HashMap) results.get(0);
        Object object = result.get(DynamicConstants.RESULT_SET_KEY);
        if (!(object instanceof List)) {
            result.put(DynamicConstants.RESULT_SET_KEY, object);
            return true;
        }
        List<HashMap> dynamicResult = (List<HashMap>) result.get(DynamicConstants.RESULT_SET_KEY);
        ApiDTO api = (ApiDTO)result.get("api");
        handleShape(dynamicResult, api);
        result.put(DynamicConstants.RESULT_SET_KEY, dynamicResult);
        return true;
    }

    private void handleShape(List<HashMap> dynamicResult, ApiDTO api) {
        DatasourceStrategy datasourceStrategy = DatasourceUtils.getDatasourceStrategy(api.getDbType());
        if (CollUtil.isNotEmpty(dynamicResult) && ObjectUtil.isNotNull(datasourceStrategy)) {
            Iterator<HashMap> iterator = dynamicResult.iterator();
            while (iterator.hasNext()) {
                if (ObjectUtil.isNull(iterator.next())) {
                    iterator.remove();
                }
            }
            if (CollUtil.isEmpty(dynamicResult)) {
                return;
            }
            String shapeColumn = DEFAULT_SHAPE_COLUMN;
            if (api.getDynamicServer() != null && StrUtil.isNotBlank(api.getDynamicServer().getShapeColumn())) {
                shapeColumn = api.getDynamicServer().getShapeColumn();
            }
            String finalShapeColumn = shapeColumn;
            dynamicResult.forEach(record -> {
                for (Object key : record.keySet()) {
                    if (StrUtil.equalsIgnoreCase(key.toString(), finalShapeColumn)) {
                        Object value = record.get(key);
                        if (ObjectUtil.isNotNull(value)) {
                            record.put(finalShapeColumn, datasourceStrategy.geometry2Wkt(value));
                        }
                    } else if (StrUtil.equalsIgnoreCase(key.toString(), DEFAULT_SHAPE_COLUMN)) {
                        Object value = record.get(key);
                        if (ObjectUtil.isNotNull(value)) {
                            record.put(DEFAULT_SHAPE_COLUMN, datasourceStrategy.geometry2Wkt(value));
                        }
                    }
                }
            });
        }
    }

}

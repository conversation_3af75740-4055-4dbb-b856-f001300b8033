package cn.com.sipsg.common.dynamic.core.controller;

import cn.com.sipsg.common.dynamic.core.entity.DynamicToken;
import cn.com.sipsg.common.dynamic.core.pojo.bo.DynamicTokenQueryBO;
import cn.com.sipsg.common.dynamic.core.pojo.bo.DynamicTokenSaveBO;
import cn.com.sipsg.common.dynamic.core.pojo.bo.DynamicTokenTestBO;
import cn.com.sipsg.common.dynamic.core.pojo.dto.TokenDTO;
import cn.com.sipsg.common.dynamic.core.pojo.vo.DynamicTokenSimpleVO;
import cn.com.sipsg.common.dynamic.core.pojo.vo.DynamicTokenVO;
import cn.com.sipsg.common.dynamic.core.service.DynamicTokenService;
import cn.com.sipsg.common.operationlog.core.annotation.OperationLog;
import cn.com.sipsg.common.operationlog.core.enums.OperationTypeEnum;
import cn.com.sipsg.common.pojo.CommonResult;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.util.BeanUtils;
import cn.com.sipsg.common.validation.AddValidated;
import cn.com.sipsg.common.validation.UpdateValidated;
import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 密钥管理控制器
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Tag(name = "密钥管理")
@RestController
@RequiredArgsConstructor
@RequestMapping("/dynamic/token")
public class DynamicTokenController {

    private final DynamicTokenService dynamicTokenService;

    /**
     * 查询密钥分页列表
     *
     * @param bo 参数
     * @return 密钥分页列表
     */
    @Operation(summary = "查询密钥分页列表")
    @OperationLog(module = "密钥管理", value = "查询密钥分页列表", type = OperationTypeEnum.QUERY, saveResponse = false)
    @SaCheckPermission(value = "dynamic:token:all")
    @PostMapping("/page")
    public CommonResult<CommonPageVO<DynamicTokenVO>> page(@RequestBody DynamicTokenQueryBO bo) {
        return CommonResult.data(dynamicTokenService.page(bo));
    }

    /**
     * 查询密钥简单信息列表
     *
     * @return 密钥简单信息列表
     */
    @Operation(summary = "查询密钥简单信息列表")
    @GetMapping("/simpleList")
    public CommonResult<List<DynamicTokenSimpleVO>> getSimpleList() {
        List<DynamicToken> tokenList = dynamicTokenService.list();
        return CommonResult.data(BeanUtils.copyToList(tokenList, DynamicTokenSimpleVO.class));
    }

    /**
     * 新增密钥
     *
     * @param bo 参数
     * @return 密钥ID
     */
    @Operation(summary = "新增密钥")
    @OperationLog(module = "密钥管理", value = "新增密钥", type = OperationTypeEnum.SAVE)
    @SaCheckPermission(value = "dynamic:token:all")
    @PostMapping("/save")
    public CommonResult<String> save(@AddValidated @RequestBody DynamicTokenSaveBO bo) {
        return CommonResult.data(dynamicTokenService.save(bo));
    }

    /**
     * 编辑密钥
     *
     * @param bo 参数
     */
    @Operation(summary = "编辑密钥")
    @OperationLog(module = "密钥管理", value = "编辑密钥", type = OperationTypeEnum.UPDATE)
    @SaCheckPermission(value = "dynamic:token:all")
    @PostMapping("/update")
    public CommonResult<Void> update(@UpdateValidated @RequestBody DynamicTokenSaveBO bo) {
        dynamicTokenService.update(bo);
        return CommonResult.success();
    }

    /**
     * 查询密钥详情
     *
     * @param id 密钥ID
     */
    @Operation(summary = "查询密钥详情")
    @Parameter(name = "id", description = "密钥ID", required = true, example = "1111")
    @OperationLog(module = "密钥管理", value = "查询密钥详情", type = OperationTypeEnum.QUERY, saveResponse = false)
    @SaCheckPermission(value = "dynamic:token:all")
    @GetMapping("/detail")
    public CommonResult<DynamicTokenVO> detail(@RequestParam String id) {
        return CommonResult.data(dynamicTokenService.detail(id));
    }

    /**
     * 删除密钥
     *
     * @param id 密钥ID
     */
    @Operation(summary = "删除密钥")
    @Parameter(name = "id", description = "密钥ID", required = true, example = "1111")
    @OperationLog(module = "密钥管理", value = "删除密钥", type = OperationTypeEnum.DELETE)
    @SaCheckPermission(value = "dynamic:token:all")
    @PostMapping("/delete")
    public CommonResult<Void> delete(@RequestParam String id) {
        dynamicTokenService.delete(id);
        return CommonResult.success();
    }

    /**
     * 测试密钥
     *
     * @param bo 参数
     */
    @Operation(summary = "测试密钥")
    @OperationLog(module = "密钥管理", value = "测试密钥", type = OperationTypeEnum.QUERY)
    @SaCheckPermission(value = "dynamic:token:all")
    @PostMapping("/test")
    public CommonResult<TokenDTO> test(@Validated @RequestBody DynamicTokenTestBO bo) {
        return CommonResult.data(dynamicTokenService.testToken(bo));
    }

}
package cn.com.sipsg.common.amap.config;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 高德api配置
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@ConfigurationProperties(prefix = "medox.amap")
public class AmapProperties {

    /**
     * 关键字
     */
    private String key = StrUtil.EMPTY;

    /**
     * 地址
     */
    private String url = "https://restapi.amap.com/v3";

}

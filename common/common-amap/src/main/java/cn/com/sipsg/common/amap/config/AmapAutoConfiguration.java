package cn.com.sipsg.common.amap.config;

import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;

/**
 * 高德相关api自动装配类
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@AutoConfiguration
@EnableConfigurationProperties({AmapProperties.class})
@ComponentScan("cn.com.sipsg.common.amap")
public class AmapAutoConfiguration {

}

package cn.com.sipsg.common.amap.core.pojo.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotEmpty;
import lombok.Getter;
import lombok.Setter;

/**
 * 获取地理信息查询参数
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "获取地理信息查询参数")
public class GeoParamBO {

    private static final long serialVersionUID = -2834304132550697418L;

    /**
     * 结构化地址信息
     * 规则遵循：国家、省份、城市、区县、城镇、乡村、街道、门牌号码、屋邨、大厦，如：北京市朝阳区阜通东大街6号。
     */
    @NotEmpty
    @Schema(description = "结构化地址信息\n"
        + "规则遵循：国家、省份、城市、区县、城镇、乡村、街道、门牌号码、屋邨、大厦，如：北京市朝阳区阜通东大街6号。",
        required = true)
    private String address;

    /**
     * 城市编码
     */
    @Schema(description = "城市编码")
    private String city;

    /**
     * 返回格式
     * 可选值：JSON,XML
     */
    @Schema(description = "返回格式可选值：JSON,XML")
    private String output;

    /**
     * 坐标转换
     * 4490  国家2000
     * sz2000 苏州2000
     */
    @Schema(description = "* 坐标转换\n"
        + "     * 参数 4490 = 国家2000\n"
        + "     *  sz2000 = 苏州2000")
    private String targetSrid = "4490";



}

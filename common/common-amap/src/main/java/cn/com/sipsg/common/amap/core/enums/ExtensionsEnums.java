package cn.com.sipsg.common.amap.core.enums;

import cn.com.sipsg.common.enums.BaseStrEnum;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 天气查询类型
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@RequiredArgsConstructor
public enum ExtensionsEnums implements BaseStrEnum {

    BASE("base", "返回实况天气"),

    ALL("all", "返回预报天气");

    private final String code;

    @JsonValue
    private final String desc;

}

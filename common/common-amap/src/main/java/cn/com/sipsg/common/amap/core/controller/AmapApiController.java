package cn.com.sipsg.common.amap.core.controller;

import cn.com.sipsg.common.amap.core.pojo.bo.GeoParamBO;
import cn.com.sipsg.common.amap.core.pojo.bo.RegeoParamBO;
import cn.com.sipsg.common.amap.core.pojo.bo.WeatherParamBO;
import cn.com.sipsg.common.amap.core.service.AmapApiService;
import cn.com.sipsg.common.pojo.CommonResult;
import com.alibaba.fastjson2.JSONObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import javax.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 高德相关api管理
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Tag(name = "高德相关api管理")
@RestController
@RequiredArgsConstructor
@RequestMapping("/amap/api/")
public class AmapApiController {

    @Resource
    private AmapApiService ampaApiService;

    /**
     * 天气查询
     *
     * @param bo 天气查询参数
     * @return 天气信息
     */
    @Operation(summary = "天气查询")
    @GetMapping("getWeatherInfo")
    public CommonResult<JSONObject> getWeatherInfo(WeatherParamBO bo) {
        return ampaApiService.getWeatherInfo(bo);
    }

    /**
     * 地理编码
     *
     * @param bo 地理编码参数
     * @return 地理编码
     */
    @Operation(summary = "地理编码")
    @GetMapping("getGeo")
    public CommonResult<JSONObject> getGeo(GeoParamBO bo) {
        return ampaApiService.getGeo(bo);
    }

    /**
     * 逆地理编码
     *
     * @param bo 逆地理编码参数
     * @return 逆地理编码
     */
    @Operation(summary = "逆地理编码")
    @GetMapping("getRegeo")
    public CommonResult<JSONObject> getRegeo(RegeoParamBO bo) {
        return ampaApiService.getRegeo(bo);
    }
}

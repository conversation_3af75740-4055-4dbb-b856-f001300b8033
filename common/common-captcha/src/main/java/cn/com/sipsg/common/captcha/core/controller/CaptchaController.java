package cn.com.sipsg.common.captcha.core.controller;

import cn.com.sipsg.common.pojo.CommonResult;
import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.util.StrUtil;
import com.xingyuv.captcha.model.common.ResponseModel;
import com.xingyuv.captcha.model.vo.CaptchaVO;
import com.xingyuv.captcha.service.CaptchaService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * 验证码控制器
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Tag(name = "验证码")
@RequiredArgsConstructor
@RestController("ajCaptchaController")
@RequestMapping("/captcha")
public class CaptchaController {

    private final CaptchaService captchaService;

    /**
     * 获取验证码
     */
    @SuppressWarnings("rawtypes")
    @Operation(summary = "获得验证码")
    @SaIgnore
    @PostMapping("/get")
    public CommonResult get(@RequestBody CaptchaVO data, HttpServletRequest request) {
        assert request.getRemoteHost() != null;
        data.setBrowserInfo(getRemoteId(request));
        ResponseModel responseModel = captchaService.get(data);
        if (responseModel.isSuccess()) {
            return CommonResult.data(responseModel.getRepData());
        }
        return CommonResult.fail(responseModel.getRepMsg());
    }

    /**
     * 校验验证码
     */
    @SuppressWarnings("rawtypes")
    @Operation(summary = "校验验证码")
    @SaIgnore
    @PostMapping("/check")
    public CommonResult check(@RequestBody CaptchaVO data, HttpServletRequest request) {
        data.setBrowserInfo(getRemoteId(request));
        ResponseModel responseModel = captchaService.check(data);
        if (responseModel.isSuccess()) {
            return CommonResult.data(responseModel.getRepData());
        }
        return CommonResult.fail(responseModel.getRepMsg());
    }

    private static String getRemoteId(HttpServletRequest request) {
        String xfwd = request.getHeader("X-Forwarded-For");
        String ip = getRemoteIpFromXfwd(xfwd);
        String ua = request.getHeader("user-agent");
        if (StrUtil.isNotBlank(ip)) {
            return ip + ua;
        }
        return request.getRemoteAddr() + ua;
    }

    private static String getRemoteIpFromXfwd(String xfwd) {
        if (StrUtil.isNotBlank(xfwd)) {
            String[] ipList = xfwd.split(",");
            return StrUtil.trim(ipList[0]);
        }
        return null;
    }

}

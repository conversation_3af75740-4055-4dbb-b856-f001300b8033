package cn.com.sipsg.common.xss.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import java.util.Collections;
import java.util.List;

/**
 * Xss配置属性
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Validated
@ConfigurationProperties(prefix = "medox.xss")
public class XssProperties {

    /**
     * 是否开启，默认为 true
     */
    private Boolean enabled = Boolean.TRUE;

    /**
     * 排除的 url，默认为空
     */
    private List<String> excludeUrls = Collections.emptyList();

}

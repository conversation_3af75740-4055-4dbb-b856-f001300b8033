package cn.com.sipsg.common.web.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

/**
 * 跨域配置
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@ConfigurationProperties(prefix = "medox.cors")
@Getter
@Setter
@Validated
public class CorsProperties {

    /**
     * 是否开启
     */
    private Boolean enabled = Boolean.TRUE;

}

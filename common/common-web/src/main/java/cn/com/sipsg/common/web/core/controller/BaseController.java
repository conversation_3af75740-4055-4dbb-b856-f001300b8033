package cn.com.sipsg.common.web.core.controller;

import cn.com.sipsg.common.pojo.CommonResult;
import cn.com.sipsg.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;

import java.util.function.Supplier;

/**
 * <AUTHOR> lixt
 * @date : 2025-05-06 14:15:00
 * @Description: 基础控制器，提供统一的异常处理
 */
@Slf4j
public abstract class BaseController {
    /**
     * 处理通用结果的函数
     * 记录异常日志并将异常重新抛出，由GlobalExceptionHandler处理
     * 
     * @param <T> 泛型类型
     * @param supplier 提供CommonResult的供应者
     * @return CommonResult对象，包含成功或失败的信息
     * @throws BusinessException 如果发生业务异常，将重新抛出供全局异常处理器处理
     */
    protected <T> CommonResult<T> handle(Supplier<CommonResult<T>> supplier) {
        try {
            return supplier.get();
        } catch (BusinessException e) {
            // 记录业务异常日志，但重新抛出给GlobalExceptionHandler处理
            log.error("业务异常", e);
            throw e;
        } catch (Exception e) {
            // 记录系统异常日志，但重新抛出给GlobalExceptionHandler处理
            log.error("系统异常", e);
            throw e;
        }
    }
}
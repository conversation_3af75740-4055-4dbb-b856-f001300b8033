package cn.com.sipsg.common.security.config;

import cn.com.sipsg.common.constant.RpcConstants;
import cn.com.sipsg.common.security.core.util.SecurityUtils;
import cn.com.sipsg.common.web.core.util.WebFrameworkUtils;
import cn.dev33.satoken.context.SaHolder;
import cn.dev33.satoken.filter.SaServletFilter;
import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.router.SaRouter;
import cn.dev33.satoken.same.SaSameUtil;
import cn.dev33.satoken.stp.StpUtil;
import cn.dev33.satoken.util.SaResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * SaToken 配置类
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Slf4j
@RequiredArgsConstructor
public class SaTokenConfigure implements WebMvcConfigurer {

    private final SecurityProperties securityProperties;

    /**
     * 注册 Sa-Token 拦截器，打开注解式鉴权功能
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册 Sa-Token 拦截器，定义详细认证规则
        registry.addInterceptor(new SaInterceptor(handler -> {
            // 登录校验 -- 拦截所有路由
            SaRouter.match("/**")
                    .notMatch("/*.html", "/**/*.html", "/**/*.css", "/**/*.js", "/error", "/favicon.ico")
                    .notMatch("/webjars/**", "/swagger-ui.html", "/swagger-config", "/doc.html", "/swagger-resources/**", "/v3/api-docs/**")
                    .notMatch(RpcConstants.RPC_API_PREFIX + "/**")
                    .notMatch(securityProperties.getIgnoreUrls())
                    .check(r -> StpUtil.checkLogin());

            // 根据一个 boolean 条件进行匹配
            SaRouter.match(StpUtil.isLogin()).check(r -> {
                WebFrameworkUtils.setLoginUserId(SecurityUtils.getLoginUserId());
                WebFrameworkUtils.setLoginUsername(SecurityUtils.getLoginUserName());
                WebFrameworkUtils.setLoginRealName(SecurityUtils.getLoginUserRealName());
            });
        })).addPathPatterns("/**");
    }

    /**
     * 注册 Sa-Token 全局过滤器，用于内部服务外网隔离
     */
    @Bean
    @ConditionalOnProperty(value = "medox.security.inner-protection-enabled", havingValue = "true", matchIfMissing = true)
    public SaServletFilter saServletFilter() {
        return new SaServletFilter()
                .addInclude("/**")
                .addExclude("/favicon.ico")
                .setAuth(obj -> {
                    // 校验 Same-Token 身份凭证     —— 以下两句代码可简化为：SaSameUtil.checkCurrentRequestToken();
                    String token = SaHolder.getRequest().getHeader(SaSameUtil.SAME_TOKEN);
                    SaSameUtil.checkToken(token);
                })
                .setError(e -> SaResult.error(e.getMessage()));
    }

}

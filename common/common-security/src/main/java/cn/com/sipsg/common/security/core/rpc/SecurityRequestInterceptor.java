package cn.com.sipsg.common.security.core.rpc;

import cn.dev33.satoken.same.SaSameUtil;
import cn.dev33.satoken.stp.StpUtil;
import feign.RequestInterceptor;
import feign.RequestTemplate;

/**
 * 认证授权请求拦截器
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public class SecurityRequestInterceptor implements RequestInterceptor {

    /**
     * 为 RPC 调用添加请求头 Same-Token 和会话 Token
     */
    @Override
    public void apply(RequestTemplate requestTemplate) {
        // 添加 Same-Token
        requestTemplate.header(SaSameUtil.SAME_TOKEN, SaSameUtil.getToken());
        // 添加会话 Token
        requestTemplate.header(StpUtil.getTokenName(), StpUtil.getTokenValue());
    }

}

package cn.com.sipsg.common.security.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.ArrayList;
import java.util.List;

/**
 * 权限认证配置
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@ConfigurationProperties("medox.security")
@Getter
@Setter
public class SecurityProperties {

    /**
     * 忽略拦截的 url
     */
    private List<String> ignoreUrls = new ArrayList<>();

    /**
     * 是否开启内部服务保护
     */
    private Boolean innerProtectionEnabled = Boolean.TRUE;

}

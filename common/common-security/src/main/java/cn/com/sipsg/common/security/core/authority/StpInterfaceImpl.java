package cn.com.sipsg.common.security.core.authority;

import cn.com.sipsg.common.security.core.util.SaTokenUtils;
import cn.dev33.satoken.session.SaSession;
import cn.dev33.satoken.stp.StpInterface;
import cn.dev33.satoken.stp.StpUtil;

import java.util.List;

/**
 * 自定义权限加载接口实现类
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public class StpInterfaceImpl implements StpInterface {

    /**
     * 返回一个账号所拥有的权限码集合
     */
    @Override
    public List<String> getPermissionList(Object loginId, String loginType) {
        SaSession tokenSession = StpUtil.getTokenSession();
        return SaTokenUtils.getPermissionList(tokenSession);
    }

    /**
     * 返回一个账号所拥有的角色标识集合 (权限与角色可分开校验)
     */
    @Override
    public List<String> getRoleList(Object loginId, String loginType) {
        SaSession tokenSession = StpUtil.getTokenSession();
        return SaTokenUtils.getRoleList(tokenSession);
    }

}

package cn.com.sipsg.common.notify.core.handler.impl.sms;

import cn.com.sipsg.common.notify.core.handler.AbstractNotifyHandler;
import cn.com.sipsg.common.notify.core.handler.dto.BaseNotifyInfo;
import cn.com.sipsg.common.notify.core.handler.dto.sms.DparkSmsNotifyInfo;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;

/**
 * 苏州园区大数据网关短信处理实现类
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Slf4j
public class SuZhouDparkBigDataCenterSmsHandlerImpl extends AbstractNotifyHandler<DparkSmsNotifyInfo> {

    @Override
    public void send(BaseNotifyInfo notice) {
        DparkSmsNotifyInfo notifyInfo = (DparkSmsNotifyInfo)notice;
        // 设置日期格式
        String time = DateUtil.format(new Date(), "yyyyMMddHHmmss");
        // 生成加密签名
        String sign = SecureUtil.md5(
            notifyInfo.getAccount() + notifyInfo.getPassword() + time);

        Map<String, Object> params = new LinkedHashMap<>();
        params.put("userid", notifyInfo.getCompanyId());
        params.put("account", notifyInfo.getAccount());
        params.put("password", notifyInfo.getPassword());
        String info = "【" + notifyInfo.getTitle() + "】" + notifyInfo.getContent();
        params.put("content", info);
        params.put("mobile", StrUtil.join(StrUtil.COMMA, notifyInfo.getSendTos()));
        params.put("timestamp", time);
        params.put("sendTime", "");
        params.put("action", notifyInfo.getAction());
        params.put("extno", "");
        params.put("sign", sign);
        params.put("rt", notifyInfo.getRt());

        String res = HttpUtil.get(notifyInfo.getSmsGateWayUrl(), params);
        if (StrUtil.isNotBlank(res)) {
            JSONObject jsonObject = JSONUtil.parseObj(res);
            Object successResult = jsonObject.get("Message");
            if ("ok".equals(successResult)) {
                log.info("【发送园区大数据中心短信】#成功：{}", info);
                return;
            }
        }
        log.error("【发送园区大数据中心短信】#失败：{}，返回结果：{}", info, res);
    }

    @Override
    public boolean validRequiredField(BaseNotifyInfo notice) {
        if (notice instanceof DparkSmsNotifyInfo) {
            DparkSmsNotifyInfo notifyInfo = (DparkSmsNotifyInfo)notice;
            //1.验证是否有主题
            if (StrUtil.isBlank(notifyInfo.getTitle())) {
                log.error("园区大数据中心短信网关要求包含主题：【xx系统】+内容");
                return false;
            }
            return true;
        }
        return false;
    }
}

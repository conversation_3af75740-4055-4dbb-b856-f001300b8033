package cn.com.sipsg.common.notify.core.handler.impl.wechat;

import cn.com.sipsg.common.notify.core.handler.AbstractNotifyHandler;
import cn.com.sipsg.common.notify.core.handler.dto.BaseNotifyInfo;
import cn.com.sipsg.common.notify.core.handler.dto.wechat.WechatMaSubscribeNotifyInfo;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;

/**
 * 微信小程序订阅消息处理类
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Slf4j
public class WechatMaSubscribeNotifyHandlerImpl extends AbstractNotifyHandler<WechatMaSubscribeNotifyInfo> {

    private static final String GATEWAY_URL = "https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=";

    @Override
    public void send(BaseNotifyInfo notice) {
        WechatMaSubscribeNotifyInfo notifyInfo = (WechatMaSubscribeNotifyInfo)notice;
        String gatewayUrl;
        if (StrUtil.isNotBlank(notifyInfo.getGatewayUrl())) {
            gatewayUrl = notifyInfo.getGatewayUrl();
        } else {
            gatewayUrl = GATEWAY_URL;
        }
        gatewayUrl += notifyInfo.getAccessToken();
        JSONObject params = new JSONObject();
        params.put("template_id", notifyInfo.getTemplateId());
        if (notifyInfo.getPage() != null) {
            params.put("page", notifyInfo.getPage());
        }
        if (notifyInfo.getMiniProgramState() != null) {
            params.put("miniprogram_state", notifyInfo.getMiniProgramState().getState());
        }
        if (notifyInfo.getLang() != null) {
            params.put("lang", notifyInfo.getLang().getLang());
        }
        JSONObject data = new JSONObject();
        params.put("data", data);
        notifyInfo.getData().forEach((k, v) -> data.put(k, MapUtil.builder("value", v).build()));
        String dataJson = JSON.toJSONString(notifyInfo.getData());
        for (String sendTo : notifyInfo.getSendTos()) {
            params.put("touser", sendTo);
            String result = HttpUtil.post(gatewayUrl, JSON.toJSONString(params));
            try {
                JSONObject respObj = JSONObject.parseObject(result);
                if (respObj.getIntValue("errcode") == 0) {
                    log.info("【发送微信小程序订阅消息】#成功，模板ID：{}，模板参数：{}，接收人：{}", notifyInfo.getTemplateId(), dataJson, sendTo);
                } else {
                    log.error("【发送微信小程序订阅消息】#失败，模板ID：{}，模板参数：{}，接收人：{}，失败Code：{}, 失败描述：{}", notifyInfo.getTemplateId(), dataJson, sendTo, respObj.getIntValue("errcode"), respObj.getString("errmsg"));
                }
            } catch (Exception e) {
                log.error("【发送微信小程序订阅消息】#异常，模板ID：{}，模板参数：{}，接收人：{}", notifyInfo.getTemplateId(), dataJson, sendTo, e);
            }
        }
    }

    @Override
    public boolean validRequiredField(BaseNotifyInfo notice) {
        if (notice instanceof WechatMaSubscribeNotifyInfo) {
            WechatMaSubscribeNotifyInfo notifyInfo = (WechatMaSubscribeNotifyInfo)notice;
            // 1.验证是否有接口调用凭证
            if (StrUtil.isBlank(notifyInfo.getAccessToken())) {
                return false;
            }
            // 2.验证是否有模板id
            if (StrUtil.isBlank(notifyInfo.getTemplateId())) {
                return false;
            }
            // 3.验证是否有模板内容
            if (CollUtil.isEmpty(notifyInfo.getData())) {
                return false;
            }
            return true;
        }
        return false;
    }

}

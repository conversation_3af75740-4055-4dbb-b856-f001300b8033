package cn.com.sipsg.common.notify.core.handler.impl.dingtalk;

import cn.com.sipsg.common.notify.core.enums.DingTalkMsgTypeEnum;
import cn.com.sipsg.common.notify.core.handler.AbstractNotifyHandler;
import cn.com.sipsg.common.notify.core.handler.dto.BaseNotifyInfo;
import cn.com.sipsg.common.notify.core.handler.dto.dingtalk.DingTalkWorkNotifyInfo;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiMessageCorpconversationAsyncsendV2Request;
import com.dingtalk.api.request.OapiMessageCorpconversationAsyncsendV2Request.Markdown;
import com.dingtalk.api.request.OapiMessageCorpconversationAsyncsendV2Request.Text;
import com.dingtalk.api.response.OapiMessageCorpconversationAsyncsendV2Response;
import com.taobao.api.ApiException;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;

/**
 * DINGTALK工作消息处理实现类
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Slf4j
public class DingTalkWorkNotifyHandlerImpl extends AbstractNotifyHandler<DingTalkWorkNotifyInfo> {

    private static final String GATEWAY_URL = "https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2";

    @Override
    public boolean validRequiredField(BaseNotifyInfo notice) {
        if (notice instanceof DingTalkWorkNotifyInfo) {
            DingTalkWorkNotifyInfo notifyInfo = (DingTalkWorkNotifyInfo)notice;

            // 1.验证是否有消息类型
            DingTalkMsgTypeEnum msgType = notifyInfo.getMsgType();
            if (msgType == null) {
                return false;
            }

            // 2.1.验证是否有主题
            if (msgType != DingTalkMsgTypeEnum.TEXT && StrUtil.isBlank(notifyInfo.getTitle())) {
                return false;
            }

            // 2.2.验证是否有点击消息跳转的URL和图片URL
            if (msgType == DingTalkMsgTypeEnum.LINK && (StrUtil.isBlank(notifyInfo.getMessageUrl()) || StrUtil.isBlank(notifyInfo.getPicUrl()))) {
                return false;
            }

            // 3.验证是否有内容
            if (StrUtil.isBlank(notifyInfo.getContent())) {
                return false;
            }

            // 4.验证是否有accessToken
            if (StrUtil.isBlank(notifyInfo.getAccessToken())) {
                return false;
            }

            // 5.验证是否有agentId
            if (notifyInfo.getAgentId() == null) {
                return false;
            }

            // 6.验证是否有接收人
            Boolean toAllUser = notifyInfo.getToAllUser();
            if ((toAllUser == null || !toAllUser) && CollUtil.isEmpty(notifyInfo.getSendTos()) && CollUtil
                .isEmpty(notifyInfo.getDeptIds())) {
                return false;
            }
            return true;
        }
        return false;
    }

    @Override
    public void send(BaseNotifyInfo notice) {
        DingTalkWorkNotifyInfo notifyInfo = (DingTalkWorkNotifyInfo)notice;
        String serverUrl;
        if (StrUtil.isNotBlank(notifyInfo.getGatewayUrl())) {
            serverUrl = notifyInfo.getGatewayUrl();
        } else {
            serverUrl = GATEWAY_URL;
        }
        DingTalkClient client = new DefaultDingTalkClient(serverUrl);
        OapiMessageCorpconversationAsyncsendV2Request request = new OapiMessageCorpconversationAsyncsendV2Request();
        request.setAgentId(notifyInfo.getAgentId());
        Set<String> sendTos = notifyInfo.getSendTos();
        if (CollUtil.isNotEmpty(sendTos)) {
            request.setUseridList(String.join(StrUtil.COMMA, sendTos));
        }
        Set<String> deptIds = notifyInfo.getDeptIds();
        if (CollUtil.isNotEmpty(deptIds)) {
            request.setDeptIdList(String.join(StrUtil.COMMA, deptIds));
        }
        request.setToAllUser(notifyInfo.getToAllUser());
        DingTalkMsgTypeEnum msgType = notifyInfo.getMsgType();
        OapiMessageCorpconversationAsyncsendV2Request.Msg msg = new OapiMessageCorpconversationAsyncsendV2Request.Msg();
        msg.setMsgtype(msgType.getMsgType());
        if (msgType == DingTalkMsgTypeEnum.TEXT) {
            Text text = new Text();
            text.setContent(notifyInfo.getContent());
            msg.setText(text);
        }
        if (msgType == DingTalkMsgTypeEnum.MARKDOWN) {
            Markdown markdown = new Markdown();
            markdown.setText(notifyInfo.getContent());
            markdown.setTitle(notifyInfo.getTitle());
            msg.setMarkdown(markdown);
        }
        if (msgType == DingTalkMsgTypeEnum.LINK) {
            msg.setLink(new OapiMessageCorpconversationAsyncsendV2Request.Link());
            msg.getLink().setTitle(notifyInfo.getTitle());
            msg.getLink().setText(notifyInfo.getContent());
            msg.getLink().setMessageUrl(notifyInfo.getMessageUrl());
            msg.getLink().setPicUrl(notifyInfo.getPicUrl());
        }
        request.setMsg(msg);
        try {
            OapiMessageCorpconversationAsyncsendV2Response rsp = client.execute(request, notifyInfo.getAccessToken());
            if (rsp.isSuccess()) {
                log.info("【发送钉钉工作消息】#成功：{}", notifyInfo.getContent());
            } else {
                log.error("【发送钉钉工作消息】#失败：{}, 失败Code：{}, 失败描述：{}", notifyInfo.getContent(), rsp.getErrcode(), rsp.getErrmsg());
            }
        } catch (ApiException e) {
            log.error("【发送钉钉工作消息】#异常：{}", notifyInfo.getContent(), e);
        }
    }

}

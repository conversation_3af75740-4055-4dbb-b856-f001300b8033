package cn.com.sipsg.common.notify.core.handler.impl.sms;

import cn.com.sipsg.common.notify.core.handler.AbstractNotifyHandler;
import cn.com.sipsg.common.notify.core.handler.dto.BaseNotifyInfo;
import cn.com.sipsg.common.notify.core.handler.dto.sms.TencentCloudSmsNotifyInfo;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.sms.v20210111.SmsClient;
import com.tencentcloudapi.sms.v20210111.models.SendSmsRequest;
import com.tencentcloudapi.sms.v20210111.models.SendSmsResponse;
import com.tencentcloudapi.sms.v20210111.models.SendStatus;
import java.util.Arrays;
import lombok.extern.slf4j.Slf4j;

/**
 * 腾讯云短信处理类
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Slf4j
public class TencentCloudSmsNotifyHandlerImpl extends AbstractNotifyHandler<TencentCloudSmsNotifyInfo> {

    @Override
    public void send(BaseNotifyInfo notice) {
        TencentCloudSmsNotifyInfo notifyInfo = (TencentCloudSmsNotifyInfo)notice;
        Credential credential = new Credential(notifyInfo.getSecretId(), notifyInfo.getSecretKey());
        SmsClient client = new SmsClient(credential, notifyInfo.getRegion());
        SendSmsRequest req = new SendSmsRequest();
        req.setPhoneNumberSet(ArrayUtil.toArray(notifyInfo.getSendTos(), String.class));
        req.setSmsSdkAppId(notifyInfo.getSdkAppId());
        req.setSignName(notifyInfo.getSignName());
        req.setTemplateId(notifyInfo.getTemplateId());
        String templateParam;
        if (ArrayUtil.isNotEmpty(notifyInfo.getTemplateParamSet())) {
            req.setTemplateParamSet(notifyInfo.getTemplateParamSet());
            templateParam = Arrays.toString(notifyInfo.getTemplateParamSet());
        } else {
            templateParam = "[]";
        }
        try {
            SendSmsResponse sendSmsResponse = client.SendSms(req);
            SendStatus sendStatus = sendSmsResponse.getSendStatusSet()[0];
            if ("Ok".equals(sendStatus.getCode())) {
                log.info("【发送腾讯云短信】#成功，模板ID：{}，模板参数：{}", notifyInfo.getTemplateId(), templateParam);
            } else {
                log.error("【发送腾讯云短信】#失败，模板ID：{}，模板参数：{}，失败Code：{}, 失败描述：{}",
                    notifyInfo.getTemplateId(), templateParam, sendStatus.getCode(), sendStatus.getMessage());
            }
        } catch (TencentCloudSDKException e) {
            log.error("【发送腾讯云短信】#异常，模板ID：{}，模板参数：{}", notifyInfo.getTemplateId(), templateParam, e);
        }
    }

    @Override
    public boolean validRequiredField(BaseNotifyInfo notice) {
        if (notice instanceof TencentCloudSmsNotifyInfo) {
            TencentCloudSmsNotifyInfo notifyInfo = (TencentCloudSmsNotifyInfo)notice;
            // 1.验证是否有账户
            if (StrUtil.isBlank(notifyInfo.getSecretId())) {
                return false;
            }
            // 2.验证是否有密钥
            if (StrUtil.isBlank(notifyInfo.getSecretKey())) {
                return false;
            }
            // 3.验证是否有地域
            if (StrUtil.isBlank(notifyInfo.getRegion())) {
                return false;
            }
            // 4.验证是否有短信应用ID
            if (StrUtil.isBlank(notifyInfo.getSdkAppId())) {
                return false;
            }
            // 5.验证是否有签名内容
            if (StrUtil.isBlank(notifyInfo.getSignName())) {
                return false;
            }
            // 6.验证是否有模板编码
            if (StrUtil.isBlank(notifyInfo.getTemplateId())) {
                return false;
            }
            return true;
        }
        return false;
    }
}

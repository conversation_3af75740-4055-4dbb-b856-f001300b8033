package cn.com.sipsg.common.notify.core.handler.dto;

import cn.com.sipsg.common.notify.core.handler.dto.dingtalk.DingTalkGroupNotifyInfo;
import cn.com.sipsg.common.notify.core.handler.dto.email.EmailNotifyInfo;
import cn.com.sipsg.common.notify.core.handler.dto.sms.TencentCloudSmsNotifyInfo;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import org.springframework.util.StringUtils;

/**
 * 消息建造者
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public class NotifyBuilder {

    /**
     * @param id          编号
     * @param content     内容，支持MK
     * @param accessToken accessToken
     * @return 钉钉群通知
     */
    public static BaseNotifyInfo prepareDingTalkGroup(String id, String content, String accessToken) {
        if (StringUtils.isEmpty(accessToken)) {
            return null;
        }
        return new DingTalkGroupNotifyInfo(id, content, accessToken);
    }

    /**
     * @param id      编号
     * @param title   主题
     * @param content 内容，支持MK
     * @param sendTos 接收方地址列表
     * @return 邮件通知
     */
    public static BaseNotifyInfo prepareEmail(String id, String title, String content, Set<String> sendTos) {
        if (null == sendTos || sendTos.size() == 0) {
            return null;
        }
        return new EmailNotifyInfo(id, title, content, sendTos);
    }

    /**
     * @param id               编号
     * @param secretId         账户
     * @param secretKey        密钥
     * @param region           地域
     * @param sdkAppId         短信应用ID
     * @param signName         签名内容
     * @param templateId       模板ID
     * @param templateParamSet 模板参数
     * @param sendTos          接收方地址列表
     * @return 腾讯云短信通知
     */
    public static BaseNotifyInfo prepareTencentCloudSms(String id, String secretId, String secretKey, String region,
        String sdkAppId, String signName, String templateId, String[] templateParamSet, Set<String> sendTos) {
        if (null == sendTos || sendTos.size() == 0) {
            return null;
        }
        return new TencentCloudSmsNotifyInfo(id, secretId, secretKey, region, sdkAppId, signName, templateId,
            templateParamSet, sendTos);
    }

    /**
     * @param id          编号
     * @param title       主题
     * @param content     内容，支持MK
     * @param accessToken accessToken
     * @param sendTos     接收方地址列表
     * @return 邮件ding通知list
     */
    public static List<BaseNotifyInfo> prepareDingAndEmail(String id, String title, String content,
        String accessToken, Set<String> sendTos) {
        List<BaseNotifyInfo> list = new ArrayList<>();
        addDingToList(id, content, accessToken, list);
        addEmailToList(id, title, content, sendTos, list);
        return list;
    }

    private static void addEmailToList(String id, String title, String content, Set<String> sendTos,
        List<BaseNotifyInfo> list) {
        BaseNotifyInfo emailNotice = prepareEmail(id, title, content, sendTos);
        if (null != emailNotice) {
            list.add(emailNotice);
        }
    }

    private static void addDingToList(String id, String content, String accessToken,
        List<BaseNotifyInfo> list) {
        BaseNotifyInfo dingNotice = prepareDingTalkGroup(id, content, accessToken);
        if (null != dingNotice) {
            list.add(dingNotice);
        }
    }
}

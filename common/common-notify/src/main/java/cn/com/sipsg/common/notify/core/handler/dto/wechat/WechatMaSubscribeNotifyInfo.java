package cn.com.sipsg.common.notify.core.handler.dto.wechat;

import cn.com.sipsg.common.notify.core.enums.NotifyType;
import cn.com.sipsg.common.notify.core.enums.WechatMaLangEnum;
import cn.com.sipsg.common.notify.core.enums.WechatMaStateEnum;
import cn.com.sipsg.common.notify.core.handler.dto.BaseNotifyInfo;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Map;
import java.util.Set;

/**
 * 微信小程序订阅消息
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Accessors(chain = true)
public class WechatMaSubscribeNotifyInfo extends BaseNotifyInfo {

    /**
     * 网关url
     * <p>
     * 默认值：https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=
     */
    private String gatewayUrl;

    /**
     * 接口调用凭证
     */
    private String accessToken;

    /**
     * 模板id
     */
    private String templateId;

    /**
     * 点击模板卡片后的跳转页面，仅限本小程序内的页面。支持带参数,（示例index?foo=bar）。该字段不填则模板无跳转
     */
    private String page;

    /**
     * 模板内容
     */
    private Map<String, Object> data;

    /**
     * 跳转小程序类型，默认正式版；
     */
    private WechatMaStateEnum miniProgramState;

    /**
     * 语言类型，默认简体中文
     */
    private WechatMaLangEnum lang;

    public WechatMaSubscribeNotifyInfo(String id, String accessToken, String templateId, Map<String, Object> data) {
        this(id, accessToken, templateId, data, null);
    }

    public WechatMaSubscribeNotifyInfo(String id, String accessToken, String templateId, Map<String, Object> data, Set<String> sendTos) {
        this.id = id;
        this.accessToken = accessToken;
        this.templateId = templateId;
        this.data = data;
        this.sendTos = sendTos;
    }

    @Override
    public NotifyType defineNotifyType() {
        return NotifyType.WECHAT_MA_SUBSCRIBE;
    }

    @Override
    public boolean validateCanPutIntoTunnel() {
        return validateReceiverCount();
    }

}

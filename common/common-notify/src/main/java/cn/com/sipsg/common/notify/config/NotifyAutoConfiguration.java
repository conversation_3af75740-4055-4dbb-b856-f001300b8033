package cn.com.sipsg.common.notify.config;

import cn.com.sipsg.common.notify.core.dispatcher.NotifyDispatcher;
import cn.com.sipsg.common.notify.core.disruptor.DisruptorQueue;
import cn.com.sipsg.common.notify.core.handler.ActiveNotifyHandler;
import cn.com.sipsg.common.notify.core.handler.NotifyHandler;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;

import java.util.List;

/**
 * 消息通知自动配置类
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@EnableConfigurationProperties(MailConfig.class)
@AutoConfiguration
public class NotifyAutoConfiguration {

    @Bean
    public DisruptorQueue disruptorQueue() {
        return new DisruptorQueue();
    }

    @Bean
    public NotifyDispatcher notifyDispatcher(DisruptorQueue disruptorQueue) {
        return new NotifyDispatcher(disruptorQueue);
    }

    @Bean
    public ActiveNotifyHandler activeNotifyHandler(List<NotifyHandler> notifyHandlerList) {
        return new ActiveNotifyHandler(notifyHandlerList);
    }

}

package cn.com.sipsg.common.notify.core.handler.dto.sms;

import cn.com.sipsg.common.notify.core.enums.NotifyType;
import com.alibaba.fastjson2.JSONObject;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Set;

/**
 * 阿里云短信消息
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Accessors(chain = true)
public class AliyunSmsNotifyInfo extends BaseSmsNotifyInfo {

    /**
     * 账户
     */
    private String accessKeyId;

    /**
     * 密钥
     */
    private String accessKeySecret;

    /**
     * 域名
     */
    private String endpoint;

    /**
     * 签名
     */
    private String signName;

    /**
     * 模板编码
     */
    private String templateCode;

    /**
     * 模板参数
     */
    private JSONObject templateParams;

    public AliyunSmsNotifyInfo(String id, String accessKeyId, String accessKeySecret, String signName, String templateCode, Set<String> sendTos) {
        this(id, accessKeyId, accessKeySecret, null, signName, templateCode, null, sendTos);
    }

    public AliyunSmsNotifyInfo(String id, String accessKeyId, String accessKeySecret, String signName, String templateCode, JSONObject templateParams, Set<String> sendTos) {
        this(id, accessKeyId, accessKeySecret, null, signName, templateCode, templateParams, sendTos);
    }

    public AliyunSmsNotifyInfo(String id, String accessKeyId, String accessKeySecret, String endpoint, String signName, String templateCode, Set<String> sendTos) {
        this(id, accessKeyId, accessKeySecret, endpoint, signName, templateCode, null, sendTos);
    }

    public AliyunSmsNotifyInfo(String id, String accessKeyId, String accessKeySecret, String endpoint, String signName, String templateCode, JSONObject templateParams, Set<String> sendTos) {
        this.id = id;
        this.accessKeyId = accessKeyId;
        this.accessKeySecret = accessKeySecret;
        this.endpoint = endpoint;
        this.signName = signName;
        this.templateCode = templateCode;
        this.templateParams = templateParams;
        this.sendTos = sendTos;
    }

    @Override
    public boolean validateCanPutIntoTunnel() {
        return validateReceiverCount();
    }

    @Override
    public NotifyType defineNotifyType() {
        return NotifyType.SMS_ALIYUN;
    }

}

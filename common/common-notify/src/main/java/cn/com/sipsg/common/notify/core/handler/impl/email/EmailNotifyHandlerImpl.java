package cn.com.sipsg.common.notify.core.handler.impl.email;

import cn.com.sipsg.common.notify.config.MailConfig;
import cn.com.sipsg.common.notify.core.handler.AbstractNotifyHandler;
import cn.com.sipsg.common.notify.core.handler.dto.BaseNotifyInfo;
import cn.com.sipsg.common.notify.core.handler.dto.email.EmailNotifyInfo;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.io.FileSystemResource;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeUtility;
import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 邮件发送
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Slf4j
public class EmailNotifyHandlerImpl extends AbstractNotifyHandler<BaseNotifyInfo> {

    @Resource
    private MailConfig mailConfig;

    private Map<String, JavaMailSenderImpl> senderMap;

    @Resource
    @Lazy
    private JavaMailSenderImpl javaMailSender;

    @PostConstruct
    public void init() {
        List<MailConfig.MailProperties> mailConfigs = mailConfig.getConfigs();
        if (CollUtil.isEmpty(mailConfigs)) {
            return;
        }
        senderMap = new HashMap<>();
        mailConfigs.forEach(mailProperties -> {
            // 邮件发送者
            JavaMailSenderImpl javaMailSender = new JavaMailSenderImpl();
            javaMailSender.setDefaultEncoding(CharsetUtil.UTF_8);
            javaMailSender.setHost(mailProperties.getHost());
            if (mailProperties.getPort() != null) {
                javaMailSender.setPort(mailProperties.getPort());
            }
            javaMailSender.setUsername(mailProperties.getUsername());
            javaMailSender.setPassword(mailProperties.getPassword());

            // 添加数据
            senderMap.put(mailProperties.getSender(), javaMailSender);
        });
    }

    @Override
    public boolean validRequiredField(BaseNotifyInfo notice) {
        if (notice instanceof EmailNotifyInfo) {
            EmailNotifyInfo notifyInfo = (EmailNotifyInfo)notice;
            //1.验证是否有接收方信息
            //已在BaseNotifyInfo的验证
            /*
            Set<String> sendTos = notifyInfo.getSendTos();
            if(null == sendTos || sendTos.size() == 0){
                return false;
            }
            */

            //2.验证是否有主题
            if (StrUtil.isBlank(notifyInfo.getTitle())) {
                return false;
            }

            return true;
        }
        return false;
    }

    @Override
    public void send(BaseNotifyInfo notice) {
        EmailNotifyInfo notifyInfo = (EmailNotifyInfo) notice;
        JavaMailSenderImpl javaMailSenderImpl;
        if (StrUtil.isNotBlank(notifyInfo.getSender())) {
            if (CollUtil.isEmpty(senderMap)) {
                log.error("JavaMailSender未初始化，请配置MailProperties");
                return;
            }
            javaMailSenderImpl = senderMap.get(notifyInfo.getSender());
            if (javaMailSenderImpl == null) {
                log.error("sender参数错误，未获取到对应的JavaMailSender");
                return;
            }
        } else {
            if (javaMailSender == null) {
                log.error("JavaMailSender未初始化，请配置MailProperties");
                return;
            }
            javaMailSenderImpl = javaMailSender;
        }
        Path dir = null;
        try {
            MimeMessage message = javaMailSenderImpl.createMimeMessage();
            MimeMessageHelper messageHelper = new MimeMessageHelper(message, true, CharsetUtil.UTF_8);
            // 邮件主题
            messageHelper.setSubject(notifyInfo.getTitle());
            // 邮件正文，支持HTML
            messageHelper.setText(notifyInfo.getContent(), true);
            // 邮件发件人
            messageHelper.setFrom(javaMailSenderImpl.getUsername());
            // 邮件发送时间
            messageHelper.setSentDate(DateUtil.date());
            // 邮件收件人
            for (String sendTo : notifyInfo.getSendTos()) {
                messageHelper.addTo(sendTo);
            }
            // 邮件抄送人
            if (CollUtil.isNotEmpty(notifyInfo.getCcTos())) {
                for (String ccTo : notifyInfo.getCcTos()) {
                    messageHelper.addCc(ccTo);
                }
            }
            // 邮件密送人
            if (CollUtil.isNotEmpty(notifyInfo.getBccTos())) {
                for (String bccTo : notifyInfo.getBccTos()) {
                    messageHelper.addBcc(bccTo);
                }
            }

            // 邮件附件
            if (CollUtil.isNotEmpty(notifyInfo.getFiles())) {
                for (String file : notifyInfo.getFiles()) {
                    FileSystemResource fileRes = new FileSystemResource(file);
                    messageHelper.addAttachment(MimeUtility.encodeText(FileUtil.getName(file)), fileRes);
                }
            }

            // 邮件附件
            if (CollUtil.isNotEmpty(notifyInfo.getAttachInfos())) {
                dir = Files.createTempDirectory("email_attach_");
                for (EmailNotifyInfo.EmailAttachInfo attachInfo : notifyInfo.getAttachInfos()) {
                    String fileName = FileUtil.getName(attachInfo.getUrl());
                    String attachmentFilename = StrUtil.isBlank(attachInfo.getName()) ? fileName : attachInfo.getName();
                    File targetFile = new File(dir + File.separator + fileName);
                    HttpUtil.downloadFile(attachInfo.getUrl(), targetFile);
                    messageHelper.addAttachment(MimeUtility.encodeText(attachmentFilename), targetFile);
                }
            }

            javaMailSender.send(message);
            log.info("【发送邮件通知】#成功:" + notifyInfo.getContent());
        } catch (Exception e) {
            log.error("【发送邮件通知】#异常:" + notifyInfo.getContent() + ", 异常描述: " + e);
        } finally {
            if (dir != null) {
                FileUtil.del(dir);
            }
        }
    }

}

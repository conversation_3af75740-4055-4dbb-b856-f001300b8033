package cn.com.sipsg.common.notify.core.disruptor;

import cn.com.sipsg.common.notify.core.handler.dto.BaseNotifyInfo;
import cn.com.sipsg.common.notify.core.handler.dto.NotifyInfoWrapper;
import cn.hutool.core.thread.ThreadFactoryBuilder;
import com.lmax.disruptor.BlockingWaitStrategy;
import com.lmax.disruptor.ExceptionHandler;
import com.lmax.disruptor.RingBuffer;
import com.lmax.disruptor.SequenceBarrier;
import com.lmax.disruptor.WorkerPool;
import com.lmax.disruptor.dsl.ProducerType;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;

/**
 * 无锁内存队列
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Slf4j
public class DisruptorQueue implements InitializingBean {

    private static RingBuffer<NotifyInfoWrapper> notifyInfoRingBuffer;

    /**
     * 分发的线程数
     */
    private static final int DISPATCH_THREADS = 4;

    public boolean addNoticeData(BaseNotifyInfo notice) {
        long sequence = notifyInfoRingBuffer.next();
        try {
            //给Event填充数据
            NotifyInfoWrapper event = notifyInfoRingBuffer.get(sequence);
            event.setNotifyInfo(notice);
        } catch (Exception e) {
            log.error("failed to add event to NotifyDispatcher for : e = {},{}", e, e.getMessage());
        } finally {
            notifyInfoRingBuffer.publish(sequence);
        }
        return true;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        // 启动分发
        initDisruptor(DISPATCH_THREADS);
    }

    private void initDisruptor(int consumerCnt) {
        //1. 构建ringBuffer对象
        notifyInfoRingBuffer = RingBuffer.create(ProducerType.MULTI,
            () -> new NotifyInfoWrapper(),
            1024 * 256,
            new BlockingWaitStrategy());
        //2.设置序号栅栏
        SequenceBarrier sequenceBarrier = notifyInfoRingBuffer.newBarrier();

        NotifyInfoConsumer[] consumers = new NotifyInfoConsumer[consumerCnt];
        for (int i = 0; i < consumerCnt; i++) {
            NotifyInfoConsumer consumer = new NotifyInfoConsumer();
            consumers[i] = consumer;
        }

        //3.设置工作池
        WorkerPool workerPool = new WorkerPool<>(notifyInfoRingBuffer,
            sequenceBarrier,
            new EventExceptionHandler(), consumers);

        notifyInfoRingBuffer.addGatingSequences(workerPool.getWorkerSequences());

        ThreadFactory namedThreadFactory = new ThreadFactoryBuilder()
            .setNamePrefix("NotifyDispatcher-pool-").build();

        ExecutorService executorService = new ThreadPoolExecutor(
            4,
            64,
            30,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(1024 * 4),
            namedThreadFactory,
            new ThreadPoolExecutor.AbortPolicy());

        workerPool.start(executorService);

        addShutDownGracefully(executorService);
    }


    /**
     * 优雅关闭
     */
    private void addShutDownGracefully(ExecutorService executorService) {
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            try {
                executorService.shutdown();
                Thread.sleep(555L);
            } catch (InterruptedException e) {
            }
        }));
    }

    /**
     * 异常静态类
     */
    static class EventExceptionHandler implements ExceptionHandler<NotifyInfoWrapper> {

        @Override
        public void handleEventException(Throwable ex, long sequence, NotifyInfoWrapper event) {
            log.error("NotifyDispatcher disruptor error, sequence：{}, event：{}", sequence, event.getNotifyInfo(), ex);
        }

        @Override
        public void handleOnStartException(Throwable ex) {
            log.error("NotifyDispatcher handleOnStartException", ex);
        }

        @Override
        public void handleOnShutdownException(Throwable ex) {
            log.error("NotifyDispatcher handleOnShutdownException", ex);
        }
    }
}

package cn.com.sipsg.common.notify.core.handler.dto.sms;

import cn.com.sipsg.common.notify.core.enums.NotifyType;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Set;

/**
 * 苏州园区大数据中心短信网关配置信息
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Accessors(chain = true)
public class DparkSmsNotifyInfo extends BaseSmsNotifyInfo {

    /**
     * 短信网关接口地址
     */
    private String smsGateWayUrl;

    /**
     * 企业ID
     */
    private String companyId;

    /**
     * 用户名
     */
    private String account;

    /**
     * 密码
     */
    private String password;

    private String action = "send";

    private String rt = "json";

    public DparkSmsNotifyInfo(String id, String smsGateWayUrl, String companyId, String title, String content,
        String account, String password) {
        this(id, smsGateWayUrl, companyId, title, content, account, password, null);
    }

    public DparkSmsNotifyInfo(String id, String smsGateWayUrl, String companyId, String title, String content,
        String account, String password, Set<String> sendTos) {
        this.setId(id);
        this.setTitle(title);
        this.setContent(content);
        this.setCompanyId(companyId);
        this.setAccount(account);
        this.setPassword(password);
        this.setSmsGateWayUrl(smsGateWayUrl);
        this.setSendTos(sendTos);
    }

    @Override
    public NotifyType defineNotifyType() {
        return NotifyType.SMS_YUANQU_DA_SHU_JU_GATEWAY;
    }
}

package cn.com.sipsg.common.notify.core.handler.dto.email;

import cn.com.sipsg.common.notify.core.enums.NotifyType;
import cn.com.sipsg.common.notify.core.handler.dto.BaseNotifyInfo;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Set;

/**
 * 邮件通知
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Accessors(chain = true)
public class EmailNotifyInfo extends BaseNotifyInfo {

    /**
     * 发送者
     */
    private String sender;

    /**
     * 抄送人
     */
    private Set<String> ccTos;

    /**
     * 密送人
     */
    private Set<String> bccTos;

    /**
     * 附件列表（服务器文件路径）
     */
    private List<String> files;

    /**
     * 附件列表（网络文件路径）
     */
    private List<EmailAttachInfo> attachInfos;

    @Override
    public NotifyType defineNotifyType() {
        return NotifyType.EMAIL;
    }

    /**
     * @param id 编号
     * @param title 主题
     * @param content 内容，支持MK
     * @param sendTos 接收方地址列表
     */
    public EmailNotifyInfo(String id, String title, String content, Set<String> sendTos) {
        this.setId(id);
        this.setTitle(title);
        this.setContent(content);
        this.setSendTos(sendTos);
    }

    /**
     * @param id 编号
     * @param title 主题
     * @param content 内容，支持MK
     * @param sendTos 接收方地址列表
     * @param sender 发送者
     */
    public EmailNotifyInfo(String id, String title, String content, Set<String> sendTos, String sender) {
        this.setId(id);
        this.setTitle(title);
        this.setContent(content);
        this.setSendTos(sendTos);
        this.setSender(sender);
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EmailAttachInfo {

        public EmailAttachInfo(String url) {
            this.url = url;
        }

        /**
         * 附件地址
         */
        private String url;

        /**
         * 附件名称（为null时使用url最后一个/后的名称）
         */
        private String name;

    }

}

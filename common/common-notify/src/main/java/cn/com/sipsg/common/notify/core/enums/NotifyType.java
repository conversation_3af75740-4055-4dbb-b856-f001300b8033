package cn.com.sipsg.common.notify.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 通知类型
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@AllArgsConstructor
public enum NotifyType {

    EMAIL("邮箱", 1, 1),
    DING_TALK_GROUP("钉钉群消息", 2, 0),
    DING_TALK_WORK("钉钉工作消息", 3, 0),
    WECHAT_GONG_ZHONG_HAO("微信公众号", 4, 0),
    WECHAT_ROBOT("微信机器人", 5, 0),
    FLY_BOOK_ROBOT("飞书机器人", 6, 0),
    SMS_YUANQU_DA_SHU_JU_GATEWAY("园区大数据中心短信网关", 7, 1),
    SMS_LEAN_CLOUD("LeanCloud短信", 8, 1),
    SMS_TENCENT_CLOUD("腾讯云短信", 9, 1),
    SMS_ALIYUN("阿里云短信", 11, 1),
    WECHAT_MA_SUBSCRIBE("微信小程序订阅消息", 11, 1),;


    /**
     * 描述
     */
    private String desc;

    /**
     * 类型
     */
    private int type;

    /**
     * 最少接收者的个数
     */
    private int minReceiverCount;
    //    private String implement;

}

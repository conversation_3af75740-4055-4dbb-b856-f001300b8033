package cn.com.sipsg.common.notify.core.handler;


import cn.com.sipsg.common.notify.core.handler.dto.BaseNotifyInfo;

/**
 * 通知处理器接口
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public interface NotifyHandler<T extends BaseNotifyInfo> {


    /**
     * 能否发送
     *
     * @param notice 通知信息
     * @return 是否
     */
    boolean canSend(T notice);

    /**
     * 发送通知
     *
     * @param notice 信息
     */
    void send(T notice);

    /**
     * 是否为本类型的发送通道
     *
     * @param notice 通知信息
     * @return 是否
     */
    boolean canPutSendTunnel(T notice);

    /**
     * 验证通知必填字段是否有值
     *
     * @param notice 通知信息
     * @return 是否
     */
    boolean validRequiredField(T notice);

    //    /** 验证通知内容的合法性
    //     * @param notice 通知信息
    //     * @return
    //     */
    //    boolean validNoticeContent(T notice);
}

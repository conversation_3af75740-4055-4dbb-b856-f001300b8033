package cn.com.sipsg.common.mybatis.core.util;

import cn.com.sipsg.common.pojo.SortingField;
import cn.com.sipsg.common.pojo.bo.PageBO;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ClassUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.InnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.sf.jsqlparser.expression.Alias;
import net.sf.jsqlparser.schema.Column;
import net.sf.jsqlparser.schema.Table;
import org.apache.ibatis.builder.StaticSqlSource;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.ResultMap;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.mapping.SqlSource;
import org.apache.ibatis.scripting.LanguageDriver;
import org.apache.ibatis.session.Configuration;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;

import java.util.*;
import java.util.stream.Collectors;

/**
 * MyBatis 工具类
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public class MyBatisUtils {

    private static final String MYSQL_ESCAPE_CHARACTER = "`";

    private static final SqlSessionFactory sqlSessionFactory;

    static {
        sqlSessionFactory = SpringUtil.getBean(SqlSessionFactory.class);
    }

    /**
     * 构建分页
     *
     * @param pageBO 分页参数
     * @param <T>    泛型
     * @return 分页
     */
    public static <T> Page<T> buildPage(PageBO pageBO) {
        return buildPage(pageBO, null);
    }

    /**
     * 构建分页
     *
     * @param pageBO        分页参数
     * @param sortingFields 排序字段
     * @param <T>           泛型
     * @return 分页
     */
    public static <T> Page<T> buildPage(PageBO pageBO, Collection<SortingField> sortingFields) {
        // 页码 + 数量
        Page<T> page = new Page<>(pageBO.getCurrent(), pageBO.getSize());
        // 排序字段
        if (CollUtil.isNotEmpty(sortingFields)) {
            page.addOrder(sortingFields.stream().filter(Objects::nonNull).map(sortingField -> SortingField.ORDER_ASC.equals(sortingField.getOrder()) ?
                            OrderItem.asc(sortingField.getField()) : OrderItem.desc(sortingField.getField()))
                    .collect(Collectors.toList()));
        }
        return page;
    }

    /**
     * 构建分页
     *
     * @param page 分页
     * @param <T>  泛型
     * @return 通用分页
     */
    public static <T> CommonPageVO<T> buildPage(IPage<T> page) {
        return new CommonPageVO<>(page.getRecords(), page.getTotal(), page.getSize(), page.getCurrent());
    }

    /**
     * 将拦截器添加到链中 由于 MybatisPlusInterceptor 不支持添加拦截器，所以只能全量设置
     *
     * @param interceptor 链
     * @param inner       拦截器
     * @param index       位置
     */
    public static void addInterceptor(MybatisPlusInterceptor interceptor, InnerInterceptor inner, int index) {
        List<InnerInterceptor> inners = new ArrayList<>(interceptor.getInterceptors());
        inners.add(index, inner);
        interceptor.setInterceptors(inners);
    }

    /**
     * 获得 Table 对应的表名
     * <p>
     * 兼容 MySQL 转义表名 `t_xxx`
     *
     * @param table 表
     * @return 去除转移字符后的表名
     */
    public static String getTableName(Table table) {
        String tableName = table.getName();
        if (tableName.startsWith(MYSQL_ESCAPE_CHARACTER) && tableName.endsWith(MYSQL_ESCAPE_CHARACTER)) {
            tableName = tableName.substring(1, tableName.length() - 1);
        }
        return tableName;
    }

    /**
     * 构建 Column 对象
     *
     * @param tableName  表名
     * @param tableAlias 别名
     * @param column     字段名
     * @return Column 对象
     */
    public static Column buildColumn(String tableName, Alias tableAlias, String column) {
        if (tableAlias != null) {
            tableName = tableAlias.getName();
        }
        return new Column(tableName + StringPool.DOT + column);
    }

    /**
     * 查询单条
     *
     * @param sql        SQL语句
     * @param resultType 结果类型
     * @param <T>        泛型
     * @return 数据
     */
    public static <T> T selectOne(String sql, Class<T> resultType) {
        if (!isSelectSql(sql)) {
            return null;
        }
        SqlBuilderStatement sqlBuilderStatement = new SqlBuilderStatement(sqlSessionFactory.getConfiguration());
        String msId;
        if (ObjectUtil.isNotNull(resultType)) {
            msId = sqlBuilderStatement.select(sql, resultType);
        } else {
            msId = sqlBuilderStatement.select(sql);
        }
        try (SqlSession sqlSession = sqlSessionFactory.openSession(false)) {
            return sqlSession.selectOne(msId);
        }
    }

    /**
     * 查询单条
     *
     * @param sql        SQL语句
     * @param param      参数
     * @param resultType 结果类型
     * @param <T>        泛型
     * @return 数据
     */
    public static <T> T selectOne(String sql, Object param, Class<T> resultType) {
        if (!isSelectSql(sql)) {
            return null;
        }
        sql = formatSqlWithScript(sql);
        SqlBuilderStatement sqlBuilderStatement = new SqlBuilderStatement(sqlSessionFactory.getConfiguration());
        Class<?> paramType = ClassUtil.getClass(param);
        String msId;
        if (ObjectUtil.isNotNull(resultType)) {
            msId = sqlBuilderStatement.selectDynamic(sql, paramType, resultType);
        } else {
            msId = sqlBuilderStatement.selectDynamic(sql, paramType);
        }
        try (SqlSession sqlSession = sqlSessionFactory.openSession(false)) {
            return sqlSession.selectOne(msId, param);
        }
    }

    /**
     * 查询列表
     *
     * @param sql SQL语句
     * @return 列表
     */
    public static List<Map<String, Object>> selectList(String sql) {
        if (!isSelectSql(sql)) {
            return Collections.emptyList();
        }
        String msId = new SqlBuilderStatement(sqlSessionFactory.getConfiguration()).select(sql);
        try (SqlSession sqlSession = sqlSessionFactory.openSession(false)) {
            return sqlSession.selectList(msId);
        }
    }

    /**
     * 查询列表
     *
     * @param sql   SQL语句
     * @param param 参数
     * @return 列表
     */
    public static List<Map<String, Object>> selectList(String sql, Object param) {
        if (!isSelectSql(sql)) {
            return Collections.emptyList();
        }
        sql = formatSqlWithScript(sql);
        String msId = new SqlBuilderStatement(sqlSessionFactory.getConfiguration()).selectDynamic(sql, ClassUtil.getClass(param));
        try (SqlSession sqlSession = sqlSessionFactory.openSession(false)) {
            return sqlSession.selectList(msId, param);
        }
    }

    /**
     * 查询列表
     *
     * @param sql        SQL语句
     * @param param      参数
     * @param resultType 结果类型
     * @param <T>        泛型
     * @return 列表
     */
    public static <T> List<T> selectList(String sql, Object param, Class<T> resultType) {
        if (!isSelectSql(sql)) {
            return Collections.emptyList();
        }
        sql = formatSqlWithScript(sql);
        SqlBuilderStatement sqlBuilderStatement = new SqlBuilderStatement(sqlSessionFactory.getConfiguration());
        Class<?> paramType = ClassUtil.getClass(param);
        String msId;
        if (ObjectUtil.isNotNull(resultType)) {
            msId = sqlBuilderStatement.selectDynamic(sql, paramType, resultType);
        } else {
            msId = sqlBuilderStatement.selectDynamic(sql, paramType);
        }
        try (SqlSession sqlSession = sqlSessionFactory.openSession(false)) {
            return sqlSession.selectList(msId, param);
        }
    }

    /**
     * 格式化sql
     *
     * @param sql sql语句
     * @return 格式化后的sql
     */
    private static String formatSqlWithScript(String sql) {
        String formatSql = StrUtil.trim(sql);
        if (!StrUtil.startWithIgnoreCase(formatSql, "<script>")) {
            formatSql = "<script>" + formatSql;
        }
        if (!StrUtil.endWithIgnoreCase(formatSql, "</script>")) {
            formatSql = formatSql + "</script>";
        }
        return formatSql;
    }

    /**
     * 判断是否 SELECT 语句
     *
     * @param sql sql语句
     * @return 是否
     */
    private static boolean isSelectSql(String sql) {
        return StrUtil.startWithIgnoreCase(StrUtil.trimStart(sql), "select");
    }

    private static class SqlBuilderStatement {
        private final Configuration configuration;
        private final LanguageDriver languageDriver;

        private SqlBuilderStatement(Configuration configuration) {
            this.configuration = configuration;
            this.languageDriver = configuration.getDefaultScriptingLanguageInstance();
        }

        private String newMsId(String sql) {
            return SqlCommandType.SELECT + StrUtil.DOT + sql.hashCode();
        }

        private boolean hasMappedStatement(String msId) {
            return this.configuration.hasStatement(msId, false);
        }

        private void newSelectMappedStatement(String msId, SqlSource sqlSource, final Class<?> resultType) {
            MappedStatement ms = (new MappedStatement.Builder(this.configuration, msId, sqlSource, SqlCommandType.SELECT)).resultMaps(new ArrayList<ResultMap>() {{
                this.add((new ResultMap.Builder(SqlBuilderStatement.this.configuration, "defaultResultMap", resultType, Collections.emptyList())).build());
            }}).build();
            this.configuration.addMappedStatement(ms);
        }

        private String select(String sql, Class<?> resultType) {
            String msId = this.newMsId(resultType + sql);
            if (!this.hasMappedStatement(msId)) {
                StaticSqlSource sqlSource = new StaticSqlSource(this.configuration, sql);
                this.newSelectMappedStatement(msId, sqlSource, resultType);
            }
            return msId;
        }

        private String select(String sql) {
            String msId = this.newMsId(sql);
            if (!this.hasMappedStatement(msId)) {
                StaticSqlSource sqlSource = new StaticSqlSource(this.configuration, sql);
                this.newSelectMappedStatement(msId, sqlSource, LinkedHashMap.class);
            }
            return msId;
        }

        private String selectDynamic(String sql, Class<?> parameterType) {
            String msId = this.newMsId(sql + parameterType);
            if (!this.hasMappedStatement(msId)) {
                SqlSource sqlSource = this.languageDriver.createSqlSource(this.configuration, sql, parameterType);
                this.newSelectMappedStatement(msId, sqlSource, LinkedHashMap.class);
            }
            return msId;
        }

        private String selectDynamic(String sql, Class<?> parameterType, Class<?> resultType) {
            String msId = this.newMsId(resultType + sql + parameterType);
            if (!this.hasMappedStatement(msId)) {
                SqlSource sqlSource = this.languageDriver.createSqlSource(this.configuration, sql, parameterType);
                this.newSelectMappedStatement(msId, sqlSource, resultType);
            }
            return msId;
        }
    }

}

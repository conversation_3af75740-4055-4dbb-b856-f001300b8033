package cn.com.sipsg.common.mybatis.core.mapper;

import cn.com.sipsg.common.mybatis.core.util.MyBatisUtils;
import cn.com.sipsg.common.pojo.SortingField;
import cn.com.sipsg.common.pojo.bo.PageBO;
import cn.com.sipsg.common.pojo.bo.SortablePageBO;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.toolkit.Db;
import com.github.yulichang.base.MPJBaseMapper;
import com.github.yulichang.interfaces.MPJBaseJoin;
import org.apache.ibatis.annotations.Param;

import java.io.Serializable;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * 在 MyBatis Plus 的 BaseMapper 的基础上拓展，提供更多的能力
 * <p>
 * 1. {@link BaseMapper} 为 MyBatis Plus 的基础接口，提供基础的 CRUD 能力
 * 2. {@link MPJBaseMapper} 为 MyBatis Plus Join 的基础接口，提供连表 Join 能力
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public interface BaseMapperX<T> extends MPJBaseMapper<T> {

    default CommonPageVO<T> selectPage(SortablePageBO pageBO, @Param("ew") Wrapper<T> queryWrapper) {
        return selectPage(pageBO, pageBO.getSortingFields(), queryWrapper);
    }

    default CommonPageVO<T> selectPage(PageBO pageBO, @Param("ew") Wrapper<T> queryWrapper) {
        return selectPage(pageBO, null, queryWrapper);
    }

    default CommonPageVO<T> selectPage(PageBO pageBO, Collection<SortingField> sortingFields, @Param("ew") Wrapper<T> queryWrapper) {
        // MyBatis Plus 查询
        IPage<T> mpPage = MyBatisUtils.buildPage(pageBO, sortingFields);
        selectPage(mpPage, queryWrapper);
        // 转换返回
        return MyBatisUtils.buildPage(mpPage);
    }

    default <DTO> CommonPageVO<DTO> selectJoinPage(SortablePageBO pageBO, Class<DTO> resultTypeClass, MPJBaseJoin<T> joinQueryWrapper) {
        return selectJoinPage(pageBO, pageBO.getSortingFields(), resultTypeClass, joinQueryWrapper);
    }

    default <DTO> CommonPageVO<DTO> selectJoinPage(PageBO pageBO, Class<DTO> resultTypeClass, MPJBaseJoin<T> joinQueryWrapper) {
        return selectJoinPage(pageBO, null, resultTypeClass, joinQueryWrapper);
    }

    default <DTO> CommonPageVO<DTO> selectJoinPage(PageBO pageBO, Collection<SortingField> sortingFields, Class<DTO> resultTypeClass, MPJBaseJoin<T> joinQueryWrapper) {
        // MyBatis Plus Join 查询
        IPage<DTO> mpPage = MyBatisUtils.buildPage(pageBO, sortingFields);
        selectJoinPage(mpPage, resultTypeClass, joinQueryWrapper);
        // 转换返回
        return MyBatisUtils.buildPage(mpPage);
    }

    default T selectOne(String field, Object value) {
        return selectOne(new QueryWrapper<T>().eq(field, value));
    }

    default T selectOne(String field, Object value, boolean throwEx) {
        return selectOne(new QueryWrapper<T>().eq(field, value), throwEx);
    }

    default T selectOne(SFunction<T, ?> field, Object value) {
        return selectOne(new LambdaQueryWrapper<T>().eq(field, value));
    }

    default T selectOne(SFunction<T, ?> field, Object value, boolean throwEx) {
        return selectOne(new LambdaQueryWrapper<T>().eq(field, value), throwEx);
    }

    default T selectOne(String field1, Object value1, String field2, Object value2) {
        return selectOne(new QueryWrapper<T>().eq(field1, value1).eq(field2, value2));
    }

    default T selectOne(String field1, Object value1, String field2, Object value2, boolean throwEx) {
        return selectOne(new QueryWrapper<T>().eq(field1, value1).eq(field2, value2), throwEx);
    }

    default T selectOne(SFunction<T, ?> field1, Object value1, SFunction<T, ?> field2, Object value2) {
        return selectOne(new LambdaQueryWrapper<T>().eq(field1, value1).eq(field2, value2));
    }

    default T selectOne(SFunction<T, ?> field1, Object value1, SFunction<T, ?> field2, Object value2, boolean throwEx) {
        return selectOne(new LambdaQueryWrapper<T>().eq(field1, value1).eq(field2, value2), throwEx);
    }

    default T selectOne(SFunction<T, ?> field1, Object value1, SFunction<T, ?> field2, Object value2, SFunction<T, ?> field3, Object value3) {
        return selectOne(new LambdaQueryWrapper<T>().eq(field1, value1).eq(field2, value2).eq(field3, value3));
    }

    default T selectOne(SFunction<T, ?> field1, Object value1, SFunction<T, ?> field2, Object value2, SFunction<T, ?> field3, Object value3, boolean throwEx) {
        return selectOne(new LambdaQueryWrapper<T>().eq(field1, value1).eq(field2, value2).eq(field3, value3), throwEx);
    }

    default Long selectCount() {
        return selectCount(new QueryWrapper<>());
    }

    default Long selectCount(String field, Object value) {
        return selectCount(new QueryWrapper<T>().eq(field, value));
    }

    default Long selectCount(SFunction<T, ?> field, Object value) {
        return selectCount(new LambdaQueryWrapper<T>().eq(field, value));
    }

    default List<T> selectList() {
        return selectList(new QueryWrapper<>());
    }

    default List<T> selectList(String field, Object value) {
        return selectList(new QueryWrapper<T>().eq(field, value));
    }

    default List<T> selectList(SFunction<T, ?> field, Object value) {
        return selectList(new LambdaQueryWrapper<T>().eq(field, value));
    }

    default List<T> selectList(String field, Collection<?> values) {
        if (CollUtil.isEmpty(values)) {
            return Collections.emptyList();
        }
        return selectList(new QueryWrapper<T>().in(field, values));
    }

    default List<T> selectList(SFunction<T, ?> field, Collection<?> values) {
        if (CollUtil.isEmpty(values)) {
            return Collections.emptyList();
        }
        return selectList(new LambdaQueryWrapper<T>().in(field, values));
    }

    default List<T> selectList(SFunction<T, ?> leField, SFunction<T, ?> geField, Object value) {
        return selectList(new LambdaQueryWrapper<T>().le(leField, value).ge(geField, value));
    }

    default List<T> selectList(SFunction<T, ?> field1, Object value1, SFunction<T, ?> field2, Object value2) {
        return selectList(new LambdaQueryWrapper<T>().eq(field1, value1).eq(field2, value2));
    }

    /**
     * 批量插入，适合大量数据插入
     *
     * @param entities 实体们
     */
    default Boolean insertBatch(Collection<T> entities) {
        return Db.saveBatch(entities);
    }

    /**
     * 批量插入，适合大量数据插入
     *
     * @param entities 实体们
     * @param size     插入数量 Db.saveBatch 默认为 1000
     */
    default Boolean insertBatch(Collection<T> entities, int size) {
        return Db.saveBatch(entities, size);
    }

    default int updateBatch(T update) {
        return update(update, new QueryWrapper<>());
    }

    default Boolean updateBatch(Collection<T> entities) {
        return Db.updateBatchById(entities);
    }

    default Boolean updateBatch(Collection<T> entities, int size) {
        return Db.updateBatchById(entities, size);
    }

    default Boolean saveOrUpdate(T entity) {
        return Db.saveOrUpdate(entity);
    }

    default Boolean saveOrUpdateBatch(Collection<T> collection) {
        return Db.saveOrUpdateBatch(collection);
    }

    default int delete(String field, String value) {
        return delete(new QueryWrapper<T>().eq(field, value));
    }

    default int delete(SFunction<T, ?> field, Object value) {
        return delete(new LambdaQueryWrapper<T>().eq(field, value));
    }

    /**
     * 判断参数值主键集合中的所有数据，是否全部存在
     *
     * @param idSet 待校验的主键集合。
     * @return 全部存在返回true，否则false。
     */
    default <K extends Serializable> boolean existAllPrimaryKeys(Set<K> idSet) {
        if (CollUtil.isEmpty(idSet)) {
            return true;
        }
        return selectBatchIds(idSet).size() == idSet.size();
    }

}

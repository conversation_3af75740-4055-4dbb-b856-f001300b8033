package cn.com.sipsg.common.mybatis.core.service;

import cn.com.sipsg.common.exception.enums.ErrorCodeEnum;
import cn.com.sipsg.common.mybatis.core.query.LambdaQueryWrapperX;
import cn.com.sipsg.common.mybatis.core.util.MyBatisUtils;
import cn.com.sipsg.common.pojo.SortingField;
import cn.com.sipsg.common.pojo.bo.PageBO;
import cn.com.sipsg.common.pojo.bo.SortablePageBO;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.util.AssertUtils;
import cn.com.sipsg.common.util.BeanUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.service.IService;
import com.github.yulichang.base.MPJBaseService;
import com.github.yulichang.extension.mapping.base.MPJDeepService;
import com.github.yulichang.extension.mapping.config.DeepConfig;
import com.github.yulichang.extension.mapping.relation.Relation;
import com.github.yulichang.interfaces.MPJBaseJoin;

import java.io.Serializable;
import java.util.*;
import java.util.function.Function;

/**
 * 在 MyBatisPlus 的 IService 的基础上拓展，提供更多的能力
 * <p>
 * 1. {@link IService} 为 MyBatis Plus 的基础接口，提供更多的能力
 * 2. {@link MPJBaseService} 为 MyBatis Plus Join 的基础接口，提供更多的能力
 * 3. {@link MPJDeepService} 为 MyBatis Plus Join 的关联查询接口，提供关联查询能力
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public interface BaseServiceX<T> extends MPJBaseService<T>, MPJDeepService<T> {

    default CommonPageVO<T> page(PageBO pageBO) {
        return page(pageBO, Wrappers.emptyWrapper());
    }

    default CommonPageVO<T> page(PageBO pageBO, Wrapper<T> queryWrapper) {
        return page(pageBO, null, queryWrapper);
    }

    default CommonPageVO<T> page(SortablePageBO pageBO) {
        return page(pageBO, Collections.singleton(BeanUtils.copyProperties(pageBO, SortingField.class)), Wrappers.emptyWrapper());
    }

    default CommonPageVO<T> page(SortablePageBO pageBO, Wrapper<T> queryWrapper) {
        return page(pageBO, pageBO.getSortingFields(), queryWrapper);
    }

    default CommonPageVO<T> page(PageBO pageBO, Collection<SortingField> sortingFields, Wrapper<T> queryWrapper) {
        // MyBatis Plus 查询
        IPage<T> mpPage = MyBatisUtils.buildPage(pageBO, sortingFields);
        page(mpPage, queryWrapper);
        // 转换返回
        return MyBatisUtils.buildPage(mpPage);
    }

    default CommonPageVO<T> pageDeep(PageBO pageBO) {
        return pageDeep(pageBO, null, Wrappers.emptyWrapper());
    }

    default CommonPageVO<T> pageDeep(PageBO pageBO, Wrapper<T> queryWrapper) {
        return pageDeep(pageBO, null, queryWrapper);
    }

    default CommonPageVO<T> pageDeep(SortablePageBO pageBO, Wrapper<T> queryWrapper) {
        return pageDeep(pageBO, pageBO.getSortingFields(), queryWrapper);
    }

    default CommonPageVO<T> pageDeep(PageBO pageBO, Collection<SortingField> sortingFields, Wrapper<T> queryWrapper) {
        // MyBatis Plus 查询
        IPage<T> mpPage = MyBatisUtils.buildPage(pageBO, sortingFields);
        pageDeep(mpPage, queryWrapper);
        // MyBatis Plus Join 关联
        Relation.mpjGetRelation(mpPage.getRecords(), DeepConfig.defaultConfig());
        // 转换返回
        return MyBatisUtils.buildPage(mpPage);
    }

    default CommonPageVO<T> pageDeep(PageBO pageBO, Function<DeepConfig.Builder<T>, DeepConfig.Builder<T>> config) {
        return pageDeep(pageBO, null, Wrappers.emptyWrapper(), config);
    }

    default CommonPageVO<T> pageDeep(PageBO pageBO, Wrapper<T> queryWrapper, Function<DeepConfig.Builder<T>, DeepConfig.Builder<T>> config) {
        return pageDeep(pageBO, null, queryWrapper, config);
    }

    default CommonPageVO<T> pageDeep(SortablePageBO pageBO, Wrapper<T> queryWrapper, Function<DeepConfig.Builder<T>, DeepConfig.Builder<T>> config) {
        return pageDeep(pageBO, pageBO.getSortingFields(), queryWrapper, config);
    }

    default CommonPageVO<T> pageDeep(PageBO pageBO, Collection<SortingField> sortingFields, Wrapper<T> queryWrapper, Function<DeepConfig.Builder<T>, DeepConfig.Builder<T>> config) {
        // MyBatis Plus 查询
        IPage<T> mpPage = MyBatisUtils.buildPage(pageBO, sortingFields);
        pageDeep(mpPage, queryWrapper);
        // MyBatis Plus Join 关联
        Relation.mpjGetRelation(mpPage.getRecords(), config.apply(DeepConfig.builder()).build());
        // 转换返回
        return MyBatisUtils.buildPage(mpPage);
    }

    default CommonPageVO<T> pageDeep(PageBO pageBO, DeepConfig<T> config) {
        return pageDeep(pageBO, null, Wrappers.emptyWrapper(), config);
    }

    default CommonPageVO<T> pageDeep(PageBO pageBO, Wrapper<T> queryWrapper, DeepConfig<T> config) {
        return pageDeep(pageBO, null, queryWrapper, config);
    }

    default CommonPageVO<T> pageDeep(SortablePageBO pageBO, Wrapper<T> queryWrapper, DeepConfig<T> config) {
        return pageDeep(pageBO, pageBO.getSortingFields(), queryWrapper, config);
    }

    default CommonPageVO<T> pageDeep(PageBO pageBO, Collection<SortingField> sortingFields, Wrapper<T> queryWrapper, DeepConfig<T> config) {
        // MyBatis Plus 查询
        IPage<T> mpPage = MyBatisUtils.buildPage(pageBO, sortingFields);
        pageDeep(mpPage, queryWrapper);
        // MyBatis Plus Join 关联
        Relation.mpjGetRelation(mpPage.getRecords(), config);
        // 转换返回
        return MyBatisUtils.buildPage(mpPage);
    }

    default <DTO> CommonPageVO<DTO> selectJoinListPage(PageBO pageBO, Class<DTO> clazz, MPJBaseJoin<T> wrapper) {
        return selectJoinListPage(pageBO, null, clazz, wrapper);
    }

    default <DTO> CommonPageVO<DTO> selectJoinListPage(SortablePageBO pageBO, Class<DTO> clazz, MPJBaseJoin<T> wrapper) {
        return selectJoinListPage(pageBO, pageBO.getSortingFields(), clazz, wrapper);
    }

    default <DTO> CommonPageVO<DTO> selectJoinListPage(PageBO pageBO, Collection<SortingField> sortingFields, Class<DTO> clazz, MPJBaseJoin<T> wrapper) {
        // MyBatis Plus Join 查询
        IPage<DTO> mpPage = MyBatisUtils.buildPage(pageBO, sortingFields);
        selectJoinListPage(mpPage, clazz, wrapper);
        return MyBatisUtils.buildPage(mpPage);
    }

    /**
     * 判断参数值列表中的所有数据，是否全部存在。另外，keyName字段在数据表中必须是唯一键值，否则返回结果会出现误判。
     *
     * @param inFilterField  待校验的数据字段，这里使用Java对象中的属性，如courseId，而不是数据字段名course_id
     * @param inFilterValues 数据值列表。
     * @return 全部存在返回true，否则false。
     */
    default <M> boolean existUniqueKeyList(SFunction<T, ?> inFilterField, Set<M> inFilterValues) {
        if (CollUtil.isEmpty(inFilterValues)) {
            return true;
        }
        return getBaseMapper().selectCount(new LambdaQueryWrapperX<T>().in(inFilterField, inFilterValues)) == inFilterValues.size();
    }

    /**
     * 判断主键Id关联的数据是否存在。
     *
     * @param id 主键Id。
     * @return 存在返回true，否则false。
     */
    default boolean existId(Serializable id) {
        return getBaseMapper().selectById(id) != null;
    }

    /**
     * 判断主键Id关联的数据是否存在。
     *
     * @param id 主键Id。
     * @return 存在返回数据，不存在抛出异常
     */
    default T checkExist(Serializable id) {
        if (ObjectUtil.isNotEmpty(id)) {
            T t = getBaseMapper().selectById(id);
            AssertUtils.isTrue(Objects.isNull(t), ErrorCodeEnum.DATA_NOT_EXIST);
            return t;
        }
        return null;
    }

    /**
     * 判断参数值主键集合中的所有数据，是否全部存在
     *
     * @param idSet 待校验的主键集合。
     * @return 全部存在返回true，否则false。
     */
    default <K extends Serializable> boolean existAllPrimaryKeys(Set<K> idSet) {
        if (CollUtil.isEmpty(idSet)) {
            return true;
        }
        return getBaseMapper().selectBatchIds(idSet).size() == idSet.size();
    }

    /**
     * 获取父主键Id下的所有子数据列表。
     *
     * @param parentIdField     父主键字段名字，如"courseId"。
     * @param parentId          父主键的值。
     * @return 父主键Id下的所有子数据列表。
     */
    default <K extends Serializable> List<T> selectListByParentId(SFunction<T, ?> parentIdField, K parentId) {
        if (parentId == null) {
            return Collections.emptyList();
        }
        LambdaQueryWrapperX<T> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eq(parentIdField, parentId);
        return getBaseMapper().selectList(queryWrapper);
    }

}

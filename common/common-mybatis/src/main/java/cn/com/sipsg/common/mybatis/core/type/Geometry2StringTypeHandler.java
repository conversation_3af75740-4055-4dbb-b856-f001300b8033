package cn.com.sipsg.common.mybatis.core.type;

import cn.hutool.core.util.HexUtil;
import lombok.SneakyThrows;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.io.WKBReader;
import org.locationtech.jts.io.WKBWriter;
import org.locationtech.jts.io.WKTReader;
import org.locationtech.jts.io.WKTWriter;
import org.springframework.util.StringUtils;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Geometry与WKT转换类型处理器
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@MappedTypes(value = {String.class})
public class Geometry2StringTypeHand<PERSON> extends BaseTypeHandler<String> {

    private static final int DIMENSION = 3;

    @Override
    @SneakyThrows
    public void setNonNullParameter(PreparedStatement preparedStatement, int i, String shape, JdbcType jdbcType) throws SQLException {
        WKTReader reader = new WKTReader();
        Geometry geometry = reader.read(shape);
        WKBWriter wkbWriter = new WKBWriter(DIMENSION);
        byte[] bytes = wkbWriter.write(geometry);
        preparedStatement.setBytes(i, bytes);
    }

    @Override
    @SneakyThrows
    public String getNullableResult(ResultSet resultSet, String s) {
        String wkt = null;
        String res = resultSet.getString(s);
        if (StringUtils.hasText(res)) {
            byte[] bty = HexUtil.decodeHex(res);
            WKBReader reader = new WKBReader();
            WKTWriter writer = new WKTWriter(DIMENSION);
            wkt = writer.write(reader.read(bty));
        }
        return wkt;
    }

    @Override
    public String getNullableResult(ResultSet resultSet, int i) throws SQLException {
        return resultSet.getString(i);
    }

    @Override
    public String getNullableResult(CallableStatement callableStatement, int i) throws SQLException {
        return callableStatement.getString(i);
    }

}

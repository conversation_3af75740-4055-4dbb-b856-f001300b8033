package cn.com.sipsg.common.operationlog.core.annotation;

import cn.com.sipsg.common.operationlog.core.enums.OperationLogLevelEnum;
import cn.com.sipsg.common.operationlog.core.enums.OperationTypeEnum;

import java.lang.annotation.*;

/**
 * 操作日志记录注解。
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface OperationLog {

    /**
     * 模块名称
     */
    String module() default "";

    /**
     * 操作名称
     */
    String value() default "";

    /**
     * 操作类型
     */
    OperationTypeEnum type() default OperationTypeEnum.OTHER;

    /**
     * 日志等级
     */
    OperationLogLevelEnum level() default OperationLogLevelEnum.LOW;

    /**
     * 是否保存应答结果。
     * 对于类似导出和文件下载之类的接口，该参与应该设置为false。
     */
    boolean saveResponse() default true;

}

package cn.com.sipsg.common.operationlog.core.aop;

import cn.com.sipsg.common.operationlog.config.OperationLogProperties;
import cn.com.sipsg.common.operationlog.core.annotation.OperationLog;
import cn.com.sipsg.common.operationlog.core.enums.OperationTypeEnum;
import cn.com.sipsg.common.pojo.CommonResult;
import cn.com.sipsg.common.security.core.LoginUser;
import cn.com.sipsg.common.security.core.util.SecurityUtils;
import cn.com.sipsg.common.util.TracerUtils;
import cn.com.sipsg.common.web.core.util.ServletUtils;
import cn.com.sipsg.module.system.api.upms.SysOperationLogApi;
import cn.com.sipsg.module.system.api.upms.dto.SysOperationLogSaveReqDTO;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 操作日志记录处理 AOP
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Aspect
@Order(1)
@RequiredArgsConstructor
public class OperationLogAspect {

    @Value("${spring.application.name}")
    private String serviceName;

    private final OperationLogProperties operationLogProperties;

    private final SysOperationLogApi operationLogApi;

    /**
     * 错误信息、请求参数和应答结果字符串的最大长度。
     */
    private static final int MAX_LENGTH = 2000;

    @Around("@annotation(operationLogAnnotation)")
    public Object around(ProceedingJoinPoint joinPoint, OperationLog operationLogAnnotation) throws Throwable {
        // 计时
        LocalDateTime startTime = LocalDateTime.now();
        String traceId = TracerUtils.getTraceId();
        String[] parameterNames = this.getParameterNames(joinPoint);
        Object[] args = joinPoint.getArgs();
        JSONObject jsonArgs = new JSONObject();
        for (int i = 0; i < args.length; i++) {
            Object arg = args[i];
            if (this.isNormalArgs(arg)) {
                String parameterName = parameterNames[i];
                jsonArgs.set(parameterName, arg);
            }
        }
        String params = jsonArgs.toString();
        SysOperationLogSaveReqDTO operationLog = null;
        boolean saveOperationLog = operationLogProperties.isEnabled();
        if (saveOperationLog) {
            operationLog = this.buildSysOperationLog(operationLogAnnotation, joinPoint, params, traceId);
        }
        Object result;
        try {
            result = joinPoint.proceed();
            if (saveOperationLog) {
                String respData = result == null ? "null" : JSONUtil.toJsonStr(result);
                this.operationLogPostProcess(operationLogAnnotation, respData, operationLog, result);
            }
        } catch (Exception e) {
            if (saveOperationLog) {
                operationLog.setSuccess(false);
                operationLog.setErrorMsg(StrUtil.sub(e.getMessage(), 0, MAX_LENGTH));
            }
            throw e;
        } finally {
            if (saveOperationLog) {
                operationLog.setDuration(Duration.between(startTime, LocalDateTime.now()).toMillis());
                operationLogApi.saveAsync(operationLog);
            }
        }
        return result;
    }

    private void operationLogPostProcess(OperationLog operationLogAnnotation, String respData, SysOperationLogSaveReqDTO operationLog, Object result) {
        if (operationLogAnnotation.saveResponse()) {
            if (respData.length() <= MAX_LENGTH) {
                operationLog.setResponseResult(respData);
            } else {
                operationLog.setResponseResult(StrUtil.sub(respData, 0, MAX_LENGTH));
            }
        }
        // 处理不是返回CommonResult的接口。
        if (!(result instanceof CommonResult)) {
            if (ServletUtils.hasRequestContext()) {
                operationLog.setSuccess(ServletUtils.getResponse().getStatus() == HttpServletResponse.SC_OK);
            }
            return;
        }
        CommonResult<?> commonResult = (CommonResult<?>) result;
        operationLog.setSuccess(commonResult.getSuccess());
        if (!commonResult.getSuccess()) {
            operationLog.setErrorMsg(commonResult.getMsg());
        }
        if (operationLog.getOperationType().equals(OperationTypeEnum.LOGIN.getCode())) {
            // 登录操作，在调用登录方法之前无法获取到LoginUser
            if (BooleanUtil.isTrue(operationLog.getSuccess())) {
                LoginUser loginUser = SecurityUtils.getLoginUser();
                operationLog.setUserId(loginUser.getUserId());
                operationLog.setUsername(loginUser.getUsername());
                operationLog.setRealName(loginUser.getRealName());
                operationLog.setAppCode(loginUser.getAppCode());
            }
        }
    }

    private SysOperationLogSaveReqDTO buildSysOperationLog(
            OperationLog operationLogAnnotation,
            ProceedingJoinPoint joinPoint,
            String params,
            String traceId) {
        HttpServletRequest request = ServletUtils.getRequest();
        SysOperationLogSaveReqDTO operationLog = new SysOperationLogSaveReqDTO();
        operationLog.setTraceId(traceId);
        operationLog.setModuleName(operationLogAnnotation.module());
        operationLog.setOperationName(operationLogAnnotation.value());
        operationLog.setOperationType(operationLogAnnotation.type().getCode());
        operationLog.setServiceName(this.serviceName);
        operationLog.setApiClass(joinPoint.getTarget().getClass().getName());
        operationLog.setApiMethod(joinPoint.getTarget().getClass().getName() + StrUtil.DOT + joinPoint.getSignature().getName());
        operationLog.setRequestMethod(request.getMethod());
        operationLog.setRequestUrl(request.getRequestURI());
        operationLog.setUserAgent(ServletUtils.getUserAgent(request));
        operationLog.setLogLevel(operationLogAnnotation.level().getCode());
        operationLog.setOperationTime(LocalDateTime.now());
        boolean isLogin = StpUtil.isLogin();
        if (isLogin) {
            LoginUser loginUser = SecurityUtils.getLoginUser();
            operationLog.setRequestIp(loginUser.getLoginIp());
            operationLog.setUserId(loginUser.getUserId());
            operationLog.setUsername(loginUser.getUsername());
            operationLog.setRealName(loginUser.getRealName());
            operationLog.setAppCode(loginUser.getAppCode());
        } else {
            operationLog.setRequestIp(ServletUtils.getClientIP(request));
        }
        if (params != null) {
            if (params.length() <= MAX_LENGTH) {
                operationLog.setRequestArguments(params);
            } else {
                operationLog.setRequestArguments(StrUtil.sub(params, 0, MAX_LENGTH));
            }
        }
        return operationLog;
    }

    private String[] getParameterNames(ProceedingJoinPoint joinPoint) {
        Signature signature = joinPoint.getSignature();
        MethodSignature methodSignature = (MethodSignature) signature;
        return methodSignature.getParameterNames();
    }

    private boolean isNormalArgs(Object o) {
        if (o instanceof List) {
            List<?> list = (List<?>) o;
            if (CollUtil.isNotEmpty(list)) {
                return !(CollUtil.getFirst(list) instanceof MultipartFile);
            }
        }
        return !(o instanceof HttpServletRequest) && !(o instanceof HttpServletResponse) && !(o instanceof MultipartFile);
    }

}

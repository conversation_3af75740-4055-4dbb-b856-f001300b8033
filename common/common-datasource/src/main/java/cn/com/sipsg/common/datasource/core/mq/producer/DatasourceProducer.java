package cn.com.sipsg.common.datasource.core.mq.producer;

import cn.com.sipsg.common.datasource.core.enums.DatasourceOperationEnum;
import cn.com.sipsg.common.datasource.core.mq.message.DatasourceOperationMessage;
import cn.com.sipsg.common.redis.core.mq.RedisMQTemplate;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 数据源相关的生产者
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Component
@RequiredArgsConstructor
public class DatasourceProducer {

    private final RedisMQTemplate redisMQTemplate;

    /**
     * 发送 {@link DatasourceOperationMessage} 消息
     */
    public void sendDatasourceOperationMessage(String dbId, DatasourceOperationEnum operation) {
        DatasourceOperationMessage message = new DatasourceOperationMessage();
        message.setDbId(dbId);
        message.setOperation(operation);
        redisMQTemplate.send(message);
    }

}

package cn.com.sipsg.common.datasource.core.service;

import cn.com.sipsg.common.datasource.core.entity.Datasource;
import cn.com.sipsg.common.datasource.core.pojo.bo.DatasourceQueryBO;
import cn.com.sipsg.common.datasource.core.pojo.bo.DatasourceSaveBO;
import cn.com.sipsg.common.datasource.core.pojo.bo.DatasourceTestBO;
import cn.com.sipsg.common.datasource.core.pojo.vo.DatasourceVO;
import cn.com.sipsg.common.datasource.core.pojo.bo.TableBO;
import cn.com.sipsg.common.mybatis.core.service.BaseServiceX;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.hutool.db.meta.Column;
import cn.hutool.db.meta.Table;

import java.util.List;

/**
 * 数据源服务接口
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public interface DatasourceService extends BaseServiceX<Datasource> {

    /**
     * 查询数据源分页列表
     *
     * @param bo 参数
     * @return 分页列表
     */
    CommonPageVO<DatasourceVO> page(DatasourceQueryBO bo);

    /**
     * 查询数据源详情
     *
     * @param id 主键
     * @return 详情
     */
    DatasourceVO detail(String id);

    /**
     * 新增数据源
     *
     * @param bo 参数
     * @return 成功或失败
     */
    String save(DatasourceSaveBO bo);

    /**
     * 编辑数据源
     *
     * @param bo 参数
     */
    void update(DatasourceSaveBO bo);

    /**
     * 删除DatasourceSaveBO
     *
     * @param id 主键
     */
    void delete(String id);

    /**
     * 编辑状态
     *
     * @param id     主键
     * @param status 状态
     */
    void changeStatus(String id, Integer status);

    /**
     * 测试连接
     *
     * @param bo 参数
     */
    void testConnection(DatasourceTestBO bo);

    /**
     * 根据数据源ID查询包含的表
     *
     * @param id 数据源ID
     * @return 表列表
     */
    List<String> listTable(String id);

    /**
     * 根据数据源和表名查询表信息
     *
     * @param datasourceId 数据源ID
     * @param tableName    表名
     * @return 表列表
     */
    Table getTable(String datasourceId, String tableName);

    /**
     * 根据数据源ID和表名查询字段列表
     *
     * @param datasourceId 数据源ID
     * @param tableName    表名
     * @return 字段列表
     */
    List<Column> listTableColumn(String datasourceId, String tableName);

    /**
     * 加载所有的数据源
     */
    void loadAllDatasource();

    /**
     * 创建表
     *
     * @param tableBO        表
     */
    void createTable(TableBO tableBO);
}
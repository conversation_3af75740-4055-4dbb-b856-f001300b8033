package cn.com.sipsg.common.datasource.core.convert;

import cn.com.sipsg.common.datasource.core.entity.Datasource;
import cn.com.sipsg.common.datasource.core.enums.DbTypeEnum;
import cn.com.sipsg.common.datasource.core.pojo.bo.DatasourceSaveBO;
import cn.com.sipsg.common.datasource.core.pojo.bo.DatasourceTestBO;
import cn.com.sipsg.common.datasource.core.pojo.vo.ColumnVO;
import cn.com.sipsg.common.enums.CommonStatusEnum;
import cn.com.sipsg.common.util.EnumUtils;
import cn.hutool.db.meta.Column;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 数据源转换器
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Mapper
public interface DatasourceConvert {

    DatasourceConvert INSTANCE = Mappers.getMapper(DatasourceConvert.class);

    Datasource convert(DatasourceSaveBO bo);

    Datasource convert(DatasourceTestBO bo);

    default ColumnVO convert(Column column) {
        ColumnVO columnVO = new ColumnVO();
        columnVO.setTableName(column.getTableName());
        columnVO.setName(column.getName());
        columnVO.setTypeName(columnVO.getTypeName());
        columnVO.setSize(column.getSize());
        columnVO.setDigit(column.getDigit());
        columnVO.setComment(column.getComment());
        columnVO.setNullable(column.isNullable());
        columnVO.setPrimaryKey(column.isPk());
        return columnVO;
    }

    default CommonStatusEnum status2Enum(Integer status) {
        return EnumUtils.getEnumByValue(CommonStatusEnum.class, status);
    }

    default DbTypeEnum dbType2Enum(String dbType) {
        return EnumUtils.getEnumByValue(DbTypeEnum.class, dbType);
    }

}
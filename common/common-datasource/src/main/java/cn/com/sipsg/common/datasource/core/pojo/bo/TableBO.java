package cn.com.sipsg.common.datasource.core.pojo.bo;

import cn.com.sipsg.common.datasource.core.pojo.vo.ColumnVO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

/**
 * 数据表
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "数据表保存BO")
public class TableBO {

    /**
     * 数据源id
     */
    @Schema(description = "数据源id")
    @NotNull(message = "数据源id不能为空")
    private String datasourceId;

    /**
     * 表名
     */
    @Schema(description = "表名")
    @NotNull(message = "表名不能为空")
    private String tableName;

    /**
     * 列
     */
    @Schema(description = "列")
    @NotEmpty(message = "列不能为空")
    private List<ColumnVO> columns;
}

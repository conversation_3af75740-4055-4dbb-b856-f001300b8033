package cn.com.sipsg.common.datasource.core.strategy.impl;

import cn.com.sipsg.common.datasource.core.constant.ObjectFieldTypeConstants;
import cn.com.sipsg.common.datasource.core.enums.DbTypeEnum;
import cn.com.sipsg.common.datasource.core.strategy.JdbcConfig;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import lombok.Getter;
import lombok.Setter;

/**
 * Dameng策略实现类
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public class DamengStrategyImpl extends MySqlStrategyImpl {

    @Override
    public DbTypeEnum getDbType() {
        return DbTypeEnum.DAMENG;
    }

    @Override
    public JdbcConfig getJdbcConfig(JSONObject configuration) {
        return configuration.to(DamengConfig.class);
    }

    @Override
    public String columnType2JavaType(String columnType) {
        if (StrUtil.equalsAny(columnType, "VARCHAR", "LONGVARCHAR", "VARCHAR2", "CHAR", "TEXT", "CLOB")) {
            return ObjectFieldTypeConstants.STRING;
        }
        if (StrUtil.equalsAnyIgnoreCase(columnType, "REAL", "FLOAT", "DOUBLE")) {
            return ObjectFieldTypeConstants.DOUBLE;
        }
        if (StrUtil.equalsIgnoreCase(columnType, "DATE")) {
            return ObjectFieldTypeConstants.LOCAL_DATE;
        }
        if (StrUtil.equalsIgnoreCase(columnType, "TIME")) {
            return ObjectFieldTypeConstants.LOCAL_TIME;
        }
        if (StrUtil.equalsAnyIgnoreCase(columnType, "DATETIME", "TIMESTAMP")) {
            return ObjectFieldTypeConstants.LOCAL_DATE_TIME;
        }
        if (StrUtil.equalsAnyIgnoreCase(columnType, "INT", "SMALLINT", "TINYINT")) {
            return ObjectFieldTypeConstants.INTEGER;
        }
        if (StrUtil.equalsIgnoreCase(columnType, "BIGINT")) {
            return ObjectFieldTypeConstants.LONG;
        }
        if (StrUtil.equalsAnyIgnoreCase(columnType, "DECIMAL", "DEC", "NUMBER")) {
            return ObjectFieldTypeConstants.BIG_DECIMAL;
        }
        if (StrUtil.equalsIgnoreCase(columnType, "BLOB")) {
            return ObjectFieldTypeConstants.BYTE_ARRAY;
        }
        if (StrUtil.equalsIgnoreCase(columnType, "BIT")) {
            return ObjectFieldTypeConstants.BOOLEAN;
        }
        return commonColumnType2JavaType(columnType);
    }

    @Getter
    @Setter
    static class DamengConfig extends JdbcConfig {

        /**
         * 数据库JDBC连接串的扩展部分
         */
        private String extraParams = "&useJDBCCompliantTimezoneShift=true&serverTimezone=Asia/Shanghai&useSSL=true&characterEncoding=UTF-8";

        @Override
        public String getDriver() {
            return "dm.jdbc.driver.DmDriver";
        }

        @Override
        public String getConnectionUrl() {
            return "jdbc:dm://" + getHost() + ":" + getPort() + "?schema=" + getDatabase() + getExtraParams();
        }

    }

}

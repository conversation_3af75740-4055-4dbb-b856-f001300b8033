package cn.com.sipsg.common.datasource.core.pojo.bo;

import cn.com.sipsg.common.pojo.bo.SortablePageBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 数据源查询BO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "数据源查询BO")
public class DatasourceQueryBO extends SortablePageBO {

    /**
     * 数据源名称
     */
    @Schema(description = "数据源名称")
    private String name;

    /**
     * 数据源编码
     */
    @Schema(description = "数据源编码")
    private String code;

    /**
     * 数据库类型
     */
    @Schema(description = "数据库类型")
    private String dbType;

}
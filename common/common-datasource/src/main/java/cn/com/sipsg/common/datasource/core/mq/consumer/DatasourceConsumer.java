package cn.com.sipsg.common.datasource.core.mq.consumer;

import cn.com.sipsg.common.datasource.core.entity.Datasource;
import cn.com.sipsg.common.datasource.core.enums.DatasourceOperationEnum;
import cn.com.sipsg.common.datasource.core.mq.message.DatasourceOperationMessage;
import cn.com.sipsg.common.datasource.core.service.DatasourceService;
import cn.com.sipsg.common.datasource.core.util.DatasourceUtils;
import cn.com.sipsg.common.redis.core.pubsub.AbstractRedisChannelMessageListener;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 针对 {@link DatasourceOperationMessage} 的消费者
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class DatasourceConsumer extends AbstractRedisChannelMessageListener<DatasourceOperationMessage> {

    private final DatasourceService datasourceService;

    @Override
    public void onMessage(@NotNull DatasourceOperationMessage message) {
        log.info("[onMessage][收到 Datasource 操作消息]");
        String dbId = message.getDbId();
        DatasourceOperationEnum operation = message.getOperation();
        if (DatasourceOperationEnum.DELETE == operation) {
            List<String> idList = StrUtil.split(dbId, StrUtil.COMMA);
            if (CollUtil.isNotEmpty(idList)) {
                for (String id : idList) {
                    DatasourceUtils.remove(id);
                }
            }
        }
        if (DatasourceOperationEnum.UPDATE == operation) {
            Datasource datasource = datasourceService.getById(dbId);
            if (datasource != null) {
                DatasourceUtils.update(datasource);
            }
        }
        if (DatasourceOperationEnum.SAVE == operation) {
            Datasource datasource = datasourceService.getById(dbId);
            if (datasource != null) {
                DatasourceUtils.add(datasource);
            }
        }
    }

}

package cn.com.sipsg.common.datasource.core.util;

import cn.com.sipsg.common.datasource.core.entity.Datasource;
import cn.com.sipsg.common.datasource.core.enums.DbTypeEnum;
import cn.com.sipsg.common.datasource.core.strategy.DatasourceStrategy;
import cn.com.sipsg.common.datasource.core.strategy.JdbcConfig;
import cn.com.sipsg.common.datasource.core.strategy.impl.*;
import cn.com.sipsg.common.exception.BusinessException;
import cn.com.sipsg.common.util.AssertUtils;
import cn.com.sipsg.common.util.SmUtils;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.db.DbUtil;
import cn.hutool.db.meta.MetaUtil;
import cn.hutool.db.meta.TableType;
import com.baomidou.dynamic.datasource.DynamicRoutingDataSource;
import com.baomidou.dynamic.datasource.creator.DataSourceProperty;
import com.baomidou.dynamic.datasource.creator.DefaultDataSourceCreator;
import com.baomidou.dynamic.datasource.creator.hikaricp.HikariCpConfig;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.DriverManager;
import java.util.*;

/**
 * 数据源工具类
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Slf4j
@Component
public class DatasourceUtils {

    private static DynamicRoutingDataSource ds;

    private static DefaultDataSourceCreator dataSourceCreator;

    private static DynamicDataSourceProperties dataSourceProperties;

    private static final Map<DbTypeEnum, DatasourceStrategy> DATASOURCE_STRATEGY_MAP = new HashMap<>();

    static {
        DATASOURCE_STRATEGY_MAP.put(DbTypeEnum.MYSQL, new MySqlStrategyImpl());
        DATASOURCE_STRATEGY_MAP.put(DbTypeEnum.POSTGRESQL, new PostgreSqlStrategyImpl());
        DATASOURCE_STRATEGY_MAP.put(DbTypeEnum.DAMENG, new DamengStrategyImpl());
        DATASOURCE_STRATEGY_MAP.put(DbTypeEnum.ORACLE_SDO, new OracleSdoStrategyImpl());
        DATASOURCE_STRATEGY_MAP.put(DbTypeEnum.ORACLE_SDE, new OracleSdeStrategyImpl());
        DATASOURCE_STRATEGY_MAP.put(DbTypeEnum.KINGBASE, new KingbaseStrategyImpl());
        DATASOURCE_STRATEGY_MAP.put(DbTypeEnum.SQLSERVER, new SqlServerStrategyImpl());
    }

    public DatasourceUtils(DataSource dataSource, DefaultDataSourceCreator dataSourceCreator, DynamicDataSourceProperties dataSourceProperties) {
        DatasourceUtils.ds = (DynamicRoutingDataSource) dataSource;
        DatasourceUtils.dataSourceCreator = dataSourceCreator;
        DatasourceUtils.dataSourceProperties = dataSourceProperties;
    }

    /**
     * 测试数据源连接
     *
     * @param datasource 数据源对象
     * @return 是否成功
     */
    public static boolean testConnection(Datasource datasource) {
        DatasourceStrategy datasourceStrategy = DATASOURCE_STRATEGY_MAP.get(datasource.getDbType());
        JdbcConfig jdbcConfig = datasourceStrategy.getJdbcConfig(datasource.getConfiguration());
        Connection conn = null;
        try {
            Class.forName(jdbcConfig.getDriver());
            // 获取连接对象
            DriverManager.setLoginTimeout(3);
            conn = DriverManager.getConnection(jdbcConfig.getConnectionUrl(), jdbcConfig.getUsername(), SmUtils.sm2DecryptStr(jdbcConfig.getPassword()));
            return true;
        } catch (Exception e) {
            log.error("测试数据源连接失败", e);
            return false;
        } finally {
            DbUtil.close(conn);
        }
    }

    /**
     * 获取数据库表
     *
     * @param datasource 数据源
     * @return 数据库表
     */
    public static List<String> listTable(Datasource datasource) {
        DataSource dataSource = getDataSource(datasource.getId());
        if (ObjectUtil.isNull(dataSource)) {
            return Collections.emptyList();
        }
        DatasourceStrategy datasourceStrategy = DATASOURCE_STRATEGY_MAP.get(datasource.getDbType());
        JdbcConfig jdbcConfig = datasourceStrategy.getJdbcConfig(datasource.getConfiguration());
        String schema = jdbcConfig.getSchema();
        if (StrUtil.isNotBlank(schema)) {
            return MetaUtil.getTables(dataSource, schema, TableType.TABLE, TableType.VIEW);
        } else {
            return MetaUtil.getTables(dataSource, TableType.TABLE, TableType.VIEW);
        }
    }

    /**
     * 添加数据源
     *
     * @param datasource 数据源
     * @return 当前数据源列表
     */
    public static Set<String> add(Datasource datasource) {
        // 添加数据源
        try {
            DatasourceStrategy datasourceStrategy = DATASOURCE_STRATEGY_MAP.get(datasource.getDbType());
            JdbcConfig jdbcConfig = datasourceStrategy.getJdbcConfig(datasource.getConfiguration());
            DataSourceProperty property = new DataSourceProperty();
            property.setUsername(jdbcConfig.getUsername());
            property.setPassword(SmUtils.sm2DecryptStr(jdbcConfig.getPassword()));
            property.setUrl(jdbcConfig.getConnectionUrl());
            property.setDriverClassName(jdbcConfig.getDriver());
            property.setPoolName(datasource.getId());
            property.setType(HikariDataSource.class);
            HikariCpConfig hikariCpConfig = new HikariCpConfig();
            hikariCpConfig.setMinimumIdle(jdbcConfig.getMinPoolSize());
            hikariCpConfig.setMaximumPoolSize(jdbcConfig.getMaxPoolSize());
            hikariCpConfig.setIsAutoCommit(true);
            hikariCpConfig.setIdleTimeout(300000L);
            hikariCpConfig.setMaxLifetime(1800000L);
            hikariCpConfig.setConnectionTimeout(30000L);
            hikariCpConfig.setConnectionTestQuery(jdbcConfig.getValidationQuery());
            property.setHikari(hikariCpConfig);
            ds.addDataSource(property.getPoolName(), dataSourceCreator.createDataSource(property));
        } catch (Exception e) {
            log.error("数据源连接失败", e);
            throw new BusinessException(e.getMessage());
        }
        return now();
    }

    /**
     * 编辑数据源
     *
     * @param datasource 数据源
     */
    public static void update(Datasource datasource) {
        // 移除原数据源
        remove(datasource.getId());
        // 添加新数据源
        add(datasource);
    }

    /**
     * 查询当前数据源列表
     *
     * @return 数据源列表
     */
    public static Set<String> now() {
        return ds.getDataSources().keySet();
    }

    /**
     * 移除数据源
     *
     * @param poolName 数据源名称
     */
    public static void remove(String poolName) {
        ds.removeDataSource(poolName);
    }

    /**
     * 根据数据源名称获取数据源
     *
     * @param poolName 数据源名称
     * @return 数据源
     */
    public static DataSource getDataSource(String poolName) {
        if (StrUtil.isEmpty(poolName)) {
            return null;
        }
        verifyPoolName(poolName);
        try {
            return ds.getDataSource(poolName);
        } catch (Exception e) {
            throw new BusinessException("数据源错误");
        }
    }

    /**
     * 校验数据源名称
     *
     * @param poolName 数据源名称
     */
    public static void verifyPoolName(String poolName) {
        AssertUtils.isTrue(dataSourceProperties.getDatasource().containsKey(poolName), "数据源错误");
    }

    /**
     * 获取数据源策略
     *
     * @param dbType 数据库类型
     * @return 数据源策略
     */
    public static DatasourceStrategy getDatasourceStrategy(DbTypeEnum dbType) {
        return DATASOURCE_STRATEGY_MAP.get(dbType);
    }

    /**
     * 获取数据库架构
     *
     * @param datasource 数据源
     * @return 数据库架构
     */
    public static String getSchema(Datasource datasource) {
        String schema = null;
        DatasourceStrategy datasourceStrategy = getDatasourceStrategy(datasource.getDbType());
        if (datasourceStrategy != null) {
            JdbcConfig jdbcConfig = datasourceStrategy.getJdbcConfig(datasource.getConfiguration());
            if (jdbcConfig != null) {
                schema = jdbcConfig.getSchema();
            }
        }
        return StrUtil.nullToEmpty(schema);
    }

}

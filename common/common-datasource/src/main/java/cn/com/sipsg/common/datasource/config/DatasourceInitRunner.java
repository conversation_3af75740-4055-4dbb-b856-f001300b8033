package cn.com.sipsg.common.datasource.config;

import cn.com.sipsg.common.datasource.core.service.DatasourceService;
import javax.annotation.Resource;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * 数据源初始化执行器
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Component
public class DatasourceInitRunner implements CommandLineRunner {

    @Resource
    private DatasourceService datasourceService;

    @Override
    public void run(String... args) throws Exception {
        // 初始化数据源
        datasourceService.loadAllDatasource();
    }

}

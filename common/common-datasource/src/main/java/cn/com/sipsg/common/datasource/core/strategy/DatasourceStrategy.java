package cn.com.sipsg.common.datasource.core.strategy;

import cn.com.sipsg.common.datasource.core.constant.ObjectFieldTypeConstants;
import cn.com.sipsg.common.datasource.core.pojo.bo.TableBO;
import cn.com.sipsg.common.datasource.core.enums.DbTypeEnum;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import java.util.List;

/**
 * 数据源策略接口
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public interface DatasourceStrategy {

    DbTypeEnum getDbType();

    /**
     * 获取jdbc配置对象
     *
     * @param configuration jdbc配置信息
     * @return jdbc配置对象
     */
    JdbcConfig getJdbcConfig(JSONObject configuration);

    /**
     * 字段类型转换成Java类型
     *
     * @param columnType 字段类型
     * @return Java类型
     */
    String columnType2JavaType(String columnType);

    /**
     * 处理分页sql
     *
     * @param sb     sql语句
     * @param limit  限制条目数
     * @param offset 偏移量
     */
    void handlePageSql(StringBuilder sb, long limit, long offset);

    /**
     * geometry转wkt
     *
     * @param geometry geometry
     * @return wkt
     */
    String geometry2Wkt(Object geometry);

    /**
     * 通用字段类型转java类型
     *
     * @param columnType 字段类型
     * @return java类型
     */
    default String commonColumnType2JavaType(String columnType) {
        if (StrUtil.equalsAnyIgnoreCase(columnType, "Geometry")) {
            return ObjectFieldTypeConstants.STRING;
        }
        return null;
    }

    /**
     * wkt转Geometry
     *
     * @param geometry geometry
     * @param srid     坐标系
     * @return geometry
     */
    String wkt2Geometry(Object geometry, Integer srid, String funcSchema);

    /**
     * 获取数据库字段类型
     *
     * @return 数据库类型
     */
    List<String> getDbColumnTypes();

    /**
     * 生成建表语句
     *
     * @param tableBO 表参数
     * @return sql 建表语句
     */
    String generateCreateTableSql(TableBO tableBO);
}

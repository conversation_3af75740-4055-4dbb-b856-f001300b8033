package cn.com.sipsg.common.datasource.core.strategy.impl;

import cn.com.sipsg.common.datasource.core.constant.ObjectFieldTypeConstants;
import cn.com.sipsg.common.datasource.core.pojo.vo.ColumnVO;
import cn.com.sipsg.common.datasource.core.pojo.bo.TableBO;
import cn.com.sipsg.common.datasource.core.enums.DbTypeEnum;
import cn.com.sipsg.common.datasource.core.strategy.DatasourceStrategy;
import cn.com.sipsg.common.datasource.core.strategy.JdbcConfig;
import cn.com.sipsg.common.geo.core.util.GeoUtils;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

/**
 * MySQL策略实现类
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public class MySqlStrategyImpl implements DatasourceStrategy {

    @Override
    public DbTypeEnum getDbType() {
        return DbTypeEnum.MYSQL;
    }

    @Override
    public JdbcConfig getJdbcConfig(JSONObject configuration) {
        return configuration.to(MySqlConfig.class);
    }

    @Override
    public String columnType2JavaType(String columnType) {
        if (StrUtil.equalsAnyIgnoreCase(columnType, "varchar", "char", "text", "longtext", "mediumtext", "tinytext", "enum", "json")) {
            return ObjectFieldTypeConstants.STRING;
        }
        if (StrUtil.equalsAnyIgnoreCase(columnType, "int", "mediumint", "smallint", "tinyint")) {
            return ObjectFieldTypeConstants.INTEGER;
        }
        if (StrUtil.equalsIgnoreCase(columnType, "bit")) {
            return ObjectFieldTypeConstants.BOOLEAN;
        }
        if (StrUtil.equalsIgnoreCase(columnType, "bigint")) {
            return ObjectFieldTypeConstants.LONG;
        }
        if (StrUtil.equalsIgnoreCase(columnType, "decimal")) {
            return ObjectFieldTypeConstants.BIG_DECIMAL;
        }
        if (StrUtil.equalsAnyIgnoreCase(columnType, "float", "double")) {
            return ObjectFieldTypeConstants.DOUBLE;
        }
        if (StrUtil.equalsIgnoreCase(columnType, "date")) {
            return ObjectFieldTypeConstants.LOCAL_DATE;
        }
        if (StrUtil.equalsIgnoreCase(columnType, "time")) {
            return ObjectFieldTypeConstants.LOCAL_TIME;
        }
        if (StrUtil.equalsAnyIgnoreCase(columnType, "datetime", "timestamp")) {
            return ObjectFieldTypeConstants.LOCAL_DATE_TIME;
        }
        if (StrUtil.equalsAnyIgnoreCase(columnType, "longblob", "blob")) {
            return ObjectFieldTypeConstants.BYTE_ARRAY;
        }
        return commonColumnType2JavaType(columnType);
    }

    @Override
    public void handlePageSql(StringBuilder sb, long limit, long offset) {
        sb.append(" limit ").append(limit).append(" offset ").append(offset);
    }

    @Override
    public String geometry2Wkt(Object geometry) {
        if (ObjectUtil.isNull(geometry)) {
            return null;
        }
        if (geometry instanceof String) {
            return String.valueOf(geometry);
        }
        return GeoUtils.mysqlByteToWkt((byte[])geometry);
    }

    @Override
    public String wkt2Geometry(Object geometry, Integer srid, String funcSchema) {
        return "ST_GeomFromText(" + geometry + ", " + srid + ")";
    }

    @Override
    public List<String> getDbColumnTypes() {
        List<String> columnTypes = new ArrayList<>();
        Collections.addAll(columnTypes, "varchar", "char", "text", "longtext", "mediumtext", "tinytext", "enum", "json",
            "int", "mediumint", "smallint", "tinyint", "bit", "bigint", "decimal", "float", "double", "date", "time", "datetime", "timestamp", "longblob", "blob");
        return columnTypes;
    }

    @Override
    public String generateCreateTableSql(TableBO tableBO) {
        StringBuilder sql = new StringBuilder();
        sql.append("CREATE TABLE ").append(tableBO.getTableName()).append(" (\n");
        List<ColumnVO> columns = tableBO.getColumns();
        if (columns == null || columns.isEmpty()) {
            return null;
        }
        for (int i = 0; i < columns.size(); i++) {
            ColumnVO column = columns.get(i);
            sql.append("    ").append(column.getName()).append(" ").append(column.getTypeName());
            if (column.getSize() != null && column.getSize() > 0) {
                sql.append("(").append(column.getSize());
                if (column.getDigit() != null) {
                    sql.append(",").append(column.getDigit());
                }
                sql.append(")");
            }
            if (BooleanUtil.isTrue(column.getPrimaryKey())) {
                sql.append(" PRIMARY KEY");
            }
            if (!BooleanUtil.isTrue(column.getNullable())) {
                sql.append(" NOT NULL");
            }
            if (StrUtil.isNotBlank(column.getComment())) {
                sql.append(" COMMENT '").append(column.getComment()).append("'");
            }
            if (i < columns.size() - 1) {
                sql.append(",\n");
            }
        }

        sql.append("\n);");
        return sql.toString();
    }


    @Getter
    @Setter
    static class MySqlConfig extends JdbcConfig {

        /**
         * 数据库JDBC连接串的扩展部分。
         */
        private String extraParams = "?characterEncoding=utf8&useSSL=true&serverTimezone=Asia/Shanghai";

        @Override
        public String getDriver() {
            return "com.mysql.cj.jdbc.Driver";
        }

        @Override
        public String getConnectionUrl() {
            return "jdbc:mysql://" + getHost() + ":" + getPort() + "/" + getDatabase() + getExtraParams();
        }

    }

}

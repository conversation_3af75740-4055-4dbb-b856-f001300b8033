<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.com.sipsg</groupId>
        <artifactId>common</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>common-datasource</artifactId>
    <description>数据库连接池</description>

    <dependencies>
        <!-- Web 相关 -->
        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>common-web</artifactId>
        </dependency>

        <!-- 空间处理相关 -->
        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>common-geo</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>common-mybatis</artifactId>
        </dependency>

        <!--<dependency>
            <groupId>oracle</groupId>
            <artifactId>sdoapi</artifactId>
            <version>${sdo.version}</version>
        </dependency>
        <dependency>
            <groupId>oracle</groupId>
            <artifactId>sdoutl</artifactId>
            <version>${sdo.version}</version>
        </dependency>-->

        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>common-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId> <!-- 多数据源 -->
        </dependency>

        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>common-operationlog</artifactId>
        </dependency>

    </dependencies>
</project>
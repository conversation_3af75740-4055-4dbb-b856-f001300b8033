package cn.com.sipsg.common.oauth2.core.service.impl;

import cn.com.sipsg.common.enums.CommonStatusEnum;
import cn.com.sipsg.common.exception.BusinessException;
import cn.com.sipsg.common.exception.enums.ErrorCodeEnum;
import cn.com.sipsg.common.oauth2.core.entity.Oauth2Client;
import cn.com.sipsg.common.oauth2.core.mapper.Oauth2ClientMapper;
import cn.com.sipsg.common.oauth2.core.pojo.bo.LoginBO;
import cn.com.sipsg.common.oauth2.core.pojo.vo.AccessTokenVO;
import cn.com.sipsg.common.oauth2.core.pojo.vo.ClientTokenModelVO;
import cn.com.sipsg.common.oauth2.core.pojo.vo.LoginUserVO;
import cn.com.sipsg.common.oauth2.core.pojo.vo.LoginVO;
import cn.com.sipsg.common.oauth2.core.service.Oauth2ClientScopeService;
import cn.com.sipsg.common.oauth2.core.service.Oauth2ServerService;
import cn.com.sipsg.common.oauth2.core.utils.StpOauth2ServerUtils;
import cn.com.sipsg.common.pojo.CommonResult;
import cn.com.sipsg.common.util.AssertUtils;
import cn.com.sipsg.common.util.BeanUtils;
import cn.com.sipsg.common.util.SmUtils;
import cn.com.sipsg.common.util.ValidationUtils;
import cn.com.sipsg.module.system.api.upms.SysUserApi;
import cn.com.sipsg.module.system.api.upms.dto.SysUserRespDTO;
import cn.dev33.satoken.context.SaHolder;
import cn.dev33.satoken.context.model.SaRequest;
import cn.dev33.satoken.oauth2.SaOAuth2Manager;
import cn.dev33.satoken.oauth2.config.SaOAuth2ServerConfig;
import cn.dev33.satoken.oauth2.consts.GrantType;
import cn.dev33.satoken.oauth2.consts.SaOAuth2Consts;
import cn.dev33.satoken.oauth2.data.generate.SaOAuth2DataGenerate;
import cn.dev33.satoken.oauth2.data.model.AccessTokenModel;
import cn.dev33.satoken.oauth2.data.model.ClientTokenModel;
import cn.dev33.satoken.oauth2.data.model.CodeModel;
import cn.dev33.satoken.oauth2.data.model.loader.SaClientModel;
import cn.dev33.satoken.oauth2.data.model.request.ClientIdAndSecretModel;
import cn.dev33.satoken.oauth2.data.model.request.RequestAuthModel;
import cn.dev33.satoken.oauth2.error.SaOAuth2ErrorCode;
import cn.dev33.satoken.oauth2.exception.SaOAuth2Exception;
import cn.dev33.satoken.oauth2.processor.SaOAuth2ServerProcessor;
import cn.dev33.satoken.oauth2.strategy.SaOAuth2Strategy;
import cn.dev33.satoken.oauth2.template.SaOAuth2Template;
import cn.dev33.satoken.oauth2.template.SaOAuth2Util;
import cn.dev33.satoken.router.SaHttpMethod;
import cn.dev33.satoken.stp.SaTokenInfo;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.BCrypt;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.validation.Validator;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/04/14
 **/
@Slf4j
@RequiredArgsConstructor
@Service
public class Oauth2ServerServiceImpl implements Oauth2ServerService {

    private final Validator validator;

    private final Oauth2ClientMapper oauth2ClientMapper;

    private final Oauth2ClientScopeService oauth2ClientScopeService;

    private final SysUserApi sysUserApi;

    @Override
    public void initClientInfo(SaOAuth2ServerConfig oauth2Server) {
        List<Oauth2Client> oauth2Clients = oauth2ClientMapper.selectList(Oauth2Client::getStatus, CommonStatusEnum.ENABLE);
        //置空
        oauth2Server.setClients(MapUtil.newHashMap());
        if (CollectionUtil.isNotEmpty(oauth2Clients)) {
            oauth2Server.setClients(oauth2Clients.stream().map(app -> {
                return new SaClientModel()
                        // client id
                        .setClientId(app.getClientId())
                        // client 秘钥
                        .setClientSecret(app.getClientSecret())
                        // 所有允许授权的 url
                        .addAllowRedirectUris(StrUtil.isBlank(app.getAllowUrls()) ? new String[]{"*"} : ArrayUtil.toArray(StrUtil.split(app.getAllowUrls(), StrPool.COMMA), String.class))
                        // 所有签约的权限
                        .addContractScopes(ArrayUtil.toArray(oauth2ClientScopeService.getScopeCodeByAppId(app.getAppId()), String.class))
                        // 所有允许的授权模式
                        .addAllowGrantTypes(ArrayUtil.toArray(StrUtil.split(app.getAllowGrantTypes(), StrPool.COMMA), String.class))
                        // AccessToken有效期
                        .setAccessTokenTimeout(app.getAccessTokenTimeout())
                        // ClientToken有效期
                        .setClientTokenTimeout(app.getClientTokenTimeout())
                        // RefreshToken有效期
                        .setRefreshTokenTimeout(app.getRefreshTokenTimeout());
            }).collect(Collectors.toMap(SaClientModel::getClientId, Function.identity(), (k1, k2) -> k1)));
        }
    }

    @Override
    public ClientTokenModelVO getClientToken() {
        // 获取变量
        SaRequest req = SaHolder.getRequest();
        SaOAuth2ServerConfig cfg = SaOAuth2Manager.getServerConfig();
        SaOAuth2Template oauth2Template = SaOAuth2Manager.getTemplate();
        String grantType = req.getParamNotNull(SaOAuth2Consts.Param.grant_type);
        if (!grantType.equals(GrantType.client_credentials)) {
            throw new SaOAuth2Exception("无效 grant_type：" + grantType).setCode(SaOAuth2ErrorCode.CODE_30126);
        }
        if (!cfg.enableClientCredentials) {
            SaOAuth2ServerProcessor.instance.throwErrorSystemNotEnableModel();
        }
        if (!SaOAuth2ServerProcessor.instance.currClientModel().getAllowGrantTypes().contains(GrantType.client_credentials)) {
            SaOAuth2ServerProcessor.instance.throwErrorClientNotEnableModel();
        }
        // 获取参数
        ClientIdAndSecretModel clientIdAndSecret = SaOAuth2Manager.getDataResolver().readClientIdAndSecret(req);
        String clientId = clientIdAndSecret.clientId;
        String clientSecret = clientIdAndSecret.clientSecret;
        List<String> scopes = SaOAuth2Manager.getDataConverter().convertScopeStringToList(req.getParam(SaOAuth2Consts.Param.scope));
        //校验 ClientScope
        oauth2Template.checkContractScope(clientId, scopes);
        // 校验 ClientSecret
        oauth2Template.checkClientSecret(clientId, clientSecret);
        // 生成
        ClientTokenModel ct = SaOAuth2Manager.getDataGenerate().generateClientToken(clientId, scopes);
        ClientTokenModelVO clientTokenModelVO = BeanUtils.copyProperties(ct, ClientTokenModelVO.class);
        clientTokenModelVO.setExpiresTime(ct.getExpiresIn());
        return clientTokenModelVO;
    }

    @Override
    public LoginVO login(LoginBO bo) {
        // 校验用户名、密码
        ValidationUtils.validate(validator, bo, LoginBO.PasswordLoginGroup.class);
        String username = bo.getUsername();
        String password = bo.getPassword();
        SysUserRespDTO userDTO = sysUserApi.getOneByUsername(username);
        try {
            password = SmUtils.sm2DecryptStr(password);
        } catch (Exception e) {
            log.error("密码解密失败", e);
            throw new BusinessException(ErrorCodeEnum.INVALID_USERNAME_OR_PASSWORD);
        }

        AssertUtils.isTrue(Objects.isNull(userDTO) || !BCrypt.checkpw(password, userDTO.getPassword()), ErrorCodeEnum.INVALID_USERNAME_OR_PASSWORD);

        AssertUtils.isTrue(ObjectUtil.equals(userDTO.getStatus(), CommonStatusEnum.DISABLE), ErrorCodeEnum.INVALID_USER_STATUS);

        // 1. 登录
        StpOauth2ServerUtils.login(userDTO.getUserId());
        // 2. 获取登录令牌
        SaTokenInfo tokenInfo = StpOauth2ServerUtils.getTokenInfo();
        // 3. 组装登录信息
        LoginVO loginVO = new LoginVO();
        loginVO.setTokenName(tokenInfo.getTokenName());
        loginVO.setTokenValue(tokenInfo.getTokenValue());
        return loginVO;
    }

    @Override
    public Object getRedirectUri() {
        SaRequest req = SaHolder.getRequest();
        SaOAuth2ServerConfig cfg = SaOAuth2Manager.getServerConfig();
        SaOAuth2DataGenerate dataGenerate = SaOAuth2Manager.getDataGenerate();
        SaOAuth2Template oauth2Template = SaOAuth2Manager.getTemplate();
        String responseType = req.getParamNotNull(SaOAuth2Consts.Param.response_type);
        SaOAuth2ServerProcessor.instance.checkAuthorizeResponseType(responseType, req, cfg);
        if (!StpOauth2ServerUtils.getStpLogic().isLogin()) {
            return CommonResult.fail(ErrorCodeEnum.UNAUTHORIZED.getCode(), ErrorCodeEnum.UNAUTHORIZED.getMsg());
        } else {
            RequestAuthModel ra = SaOAuth2Manager.getDataResolver().readRequestAuthModel(req, StpOauth2ServerUtils.getStpLogic().getLoginId());
            oauth2Template.checkRedirectUri(ra.clientId, ra.redirectUri);
            SaOAuth2Manager.getServerConfig();
            oauth2Template.checkContractScope(ra.clientId, ra.scopes);
            boolean isNeedCarefulConfirm = oauth2Template.isNeedCarefulConfirm(ra.loginId, ra.clientId, ra.scopes);
            if (isNeedCarefulConfirm) {
                return CommonResult.fail(ErrorCodeEnum.FORBIDDEN.getCode(), ErrorCodeEnum.FORBIDDEN.getMsg() + ",需要用户确认授权");
            } else {
                String redirectUri;
                if (SaOAuth2Consts.ResponseType.code.equals(ra.responseType)) {
                    CodeModel codeModel = dataGenerate.generateCode(ra);
                    redirectUri = dataGenerate.buildRedirectUri(ra.redirectUri, codeModel.code, ra.state);
                    return CommonResult.data(redirectUri);
                } else if (SaOAuth2Consts.ResponseType.token.equals(ra.responseType)) {
                    AccessTokenModel at = dataGenerate.generateAccessToken(ra, false);
                    redirectUri = dataGenerate.buildImplicitRedirectUri(ra.redirectUri, at.accessToken, ra.state);
                    return CommonResult.data(redirectUri);
                } else {
                    throw (new SaOAuth2Exception("无效 response_type: " + ra.responseType)).setCode(30125);
                }
            }
        }
    }

    @Override
    public Object doConfirm() {
        SaRequest req = SaHolder.getRequest();
        String clientId = req.getParamNotNull(SaOAuth2Consts.Param.client_id);
        Object loginId = StpOauth2ServerUtils.getStpLogic().getLoginId();
        String scope = req.getParamNotNull(SaOAuth2Consts.Param.scope);
        List<String> scopes = SaOAuth2Manager.getDataConverter().convertScopeStringToList(scope);
        SaOAuth2DataGenerate dataGenerate = SaOAuth2Manager.getDataGenerate();
        SaOAuth2Template oauth2Template = SaOAuth2Manager.getTemplate();
        // 此请求只允许 POST 方式
        if (!req.isMethod(SaHttpMethod.POST)) {
            throw new SaOAuth2Exception("无效请求方式：" + req.getMethod()).setCode(SaOAuth2ErrorCode.CODE_30151);
        }
        oauth2Template.saveGrantScope(clientId, loginId, scopes);
        boolean buildRedirectUri = req.isParam(SaOAuth2Consts.Param.build_redirect_uri, "true");
        if (!buildRedirectUri) {
            oauth2Template.saveGrantScope(clientId, loginId, scopes);
            return CommonResult.success();
        }
        RequestAuthModel ra = SaOAuth2Manager.getDataResolver().readRequestAuthModel(req, loginId);
        if (SaOAuth2Consts.ResponseType.code.equals(ra.responseType)) {
            CodeModel codeModel = dataGenerate.generateCode(ra);
            String redirectUri = dataGenerate.buildRedirectUri(ra.redirectUri, codeModel.code, ra.state);
            return CommonResult.data(redirectUri);
        }
        if (SaOAuth2Consts.ResponseType.token.equals(ra.responseType)) {
            AccessTokenModel at = dataGenerate.generateAccessToken(ra, false);
            String redirectUri = dataGenerate.buildImplicitRedirectUri(ra.redirectUri, at.accessToken, ra.state);
            return CommonResult.data(redirectUri);
        }
        throw new SaOAuth2Exception("无效response_type: " + ra.responseType).setCode(SaOAuth2ErrorCode.CODE_30125);
    }

    @Override
    public AccessTokenVO getAccessTokenByCode() {
        AccessTokenModel at = SaOAuth2Strategy.instance.grantTypeAuth.apply(SaHolder.getRequest());
        AccessTokenVO accessTokenVo = new AccessTokenVO();
        accessTokenVo.setAccessToken(at.accessToken);
        accessTokenVo.setRefreshToken(at.refreshToken);
        accessTokenVo.setExpiresTime(at.getExpiresIn());
        accessTokenVo.setRefreshExpiresTime(at.getRefreshExpiresIn());
        accessTokenVo.setClientId(at.clientId);
        accessTokenVo.setScopes(at.scopes);
        accessTokenVo.setTokenType(at.tokenType);
        accessTokenVo.setExtraData(at.extraData);
        accessTokenVo.setLoginId(at.getLoginId());
        return accessTokenVo;
    }

    @Override
    public AccessTokenVO refreshAccessToken() {
        SaRequest req = SaHolder.getRequest();
        String grantType = req.getParamNotNull(SaOAuth2Consts.Param.grant_type);
        SaOAuth2Exception.throwBy(!grantType.equals(GrantType.refresh_token), "无效 grant_type：" + grantType, 30126);
        AccessTokenModel at = SaOAuth2Strategy.instance.grantTypeAuth.apply(req);
        AccessTokenVO accessTokenVo = new AccessTokenVO();
        accessTokenVo.setTokenType(at.tokenType);
        accessTokenVo.setAccessToken(at.accessToken);
        accessTokenVo.setRefreshToken(at.refreshToken);
        accessTokenVo.setExpiresTime(at.getExpiresIn());
        accessTokenVo.setRefreshExpiresTime(at.getRefreshExpiresIn());
        accessTokenVo.setClientId(at.clientId);
        accessTokenVo.setScopes(at.scopes);
        accessTokenVo.setExtraData(at.extraData);
        accessTokenVo.setLoginId(at.getLoginId());
        return accessTokenVo;
    }

    @Override
    public LoginUserVO getUserInfo() {
        String accessToken = SaOAuth2Manager.getDataResolver().readAccessToken(SaHolder.getRequest());
        Object loginId = SaOAuth2Util.getLoginIdByAccessToken(accessToken);
        //获取用户信息
        SysUserRespDTO userRespDTO = sysUserApi.getUserById(loginId.toString());
        AssertUtils.isTrue(ObjectUtil.isNull(userRespDTO), ErrorCodeEnum.DATA_NOT_EXIST);
        LoginUserVO loginUserVO = new LoginUserVO();
        loginUserVO.setUserId(userRespDTO.getUserId());
        loginUserVO.setUsername(userRespDTO.getUsername());
        loginUserVO.setRealName(userRespDTO.getRealName());
        loginUserVO.setAvatar(userRespDTO.getAvatar());
        return loginUserVO;
    }

}

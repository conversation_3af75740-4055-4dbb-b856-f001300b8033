package cn.com.sipsg.common.oauth2.core.pojo.bo;

import cn.com.sipsg.common.pojo.bo.SortablePageBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 客户端范围查询 BO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "客户端范围查询 BO")
public class Oauth2ScopeQueryBO extends SortablePageBO {

    /**
     * 客户端id
     */
    @Schema(description = "客户端id", requiredMode = Schema.RequiredMode.REQUIRED)
    private String appId;

    /**
     * 范围名称
     */
    @Schema(description = "范围名称")
    private String scopeName;

    /**
     * 范围标识
     */
    @Schema(description = "范围标识")
    private String scopeCode;
}

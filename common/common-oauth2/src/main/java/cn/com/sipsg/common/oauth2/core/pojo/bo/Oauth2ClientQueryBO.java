package cn.com.sipsg.common.oauth2.core.pojo.bo;

import cn.com.sipsg.common.pojo.bo.SortablePageBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 客户端管理查询 BO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "客户端管理查询 BO")
public class Oauth2ClientQueryBO extends SortablePageBO {

    /**
     * 应用名称
     */
    @Schema(description = "应用名称")
    private String appName;

}

package cn.com.sipsg.common.oauth2.core.entity;

import cn.com.sipsg.common.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;


/**
 * 客户端范围
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@FieldNameConstants
@TableName(value = "medox_oauth2_scope", autoResultMap = true)
public class Oauth2Scope extends BaseDO {

    /**
     * 范围id
     */
    @TableId("scope_id")
    private String scopeId;

    /**
     * 范围标识
     */
    @TableField("scope_code")
    private String scopeCode;

    /**
     * 范围名称
     */
    @TableField("scope_name")
    private String scopeName;

    /**
     * 范围描述
     */
    @TableField("description")
    private String description;

    /**
     * 范围状态(1=正常,0=禁用)
     */
    @TableField("status")
    private Integer status;

    /**
     * 范围级别
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 是否保留；0-保留,1-不保留
     */
    @TableField("reserved")
    private Integer reserved;

}

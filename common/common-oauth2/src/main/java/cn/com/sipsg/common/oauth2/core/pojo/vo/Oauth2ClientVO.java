package cn.com.sipsg.common.oauth2.core.pojo.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fhs.core.trans.vo.VO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 客户端管理 VO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "客户端管理 VO")
public class Oauth2ClientVO implements VO {

    /**
     * 主键
     */
    @TableId("app_id")
    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED)
    private String appId;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

    /**
     * 应用名称
     */
    @Schema(description = "应用名称")
    private String appName;

    /**
     * 应用状态(1=启用, 0=禁用)
     */
    @Schema(description = "应用状态(1=启用, 0=禁用)")
    private Integer status;

    /**
     * 应用id
     */
    @Schema(description = "应用id")
    private String clientId;

    /**
     * 应用秘钥
     */
    @Schema(description = "应用秘钥")
    private String clientSecret;

    /**
     * 授权类型 authorization_code.授权码 implicit.隐藏式 refresh_token.刷新令牌 password.密码式 client_credentials.客户端模式
     */
    @Schema(description = "授权类型 authorization_code.授权码 implicit.隐藏式 refresh_token.刷新令牌 password.密码式 client_credentials.客户端模式")
    private String allowGrantTypes;

    /**
     * 所有签约的权限
     */
    @Schema(description = "所有签约的权限")
    private String contractScopes;

    /**
     * 应用介绍
     */
    @Schema(description = "应用介绍")
    private String description;

    /**
     * 允许授权的url
     */
    @Schema(description = "允许授权的url")
    private String allowUrls;

    /**
     * 主页地址
     */
    @Schema(description = "主页地址")
    private String homeUrl;

    /**
     * Access-Token 保存的时间(单位秒)
     */
    @Schema(description = "Access-Token 保存的时间(单位秒)")
    private Long accessTokenTimeout;

    /**
     * Refresh-Token 保存的时间(单位秒)
     */
    @Schema(description = "Refresh-Token 保存的时间(单位秒)")
    private String refreshTokenTimeout;

    /**
     * 应用图标
     */
    @Schema(description = "应用图标")
    private String logo;


}

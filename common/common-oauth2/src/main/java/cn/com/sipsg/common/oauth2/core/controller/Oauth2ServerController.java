package cn.com.sipsg.common.oauth2.core.controller;

import cn.com.sipsg.common.oauth2.core.pojo.bo.LoginBO;
import cn.com.sipsg.common.oauth2.core.pojo.vo.AccessTokenVO;
import cn.com.sipsg.common.oauth2.core.pojo.vo.ClientTokenModelVO;
import cn.com.sipsg.common.oauth2.core.pojo.vo.LoginUserVO;
import cn.com.sipsg.common.oauth2.core.pojo.vo.LoginVO;
import cn.com.sipsg.common.oauth2.core.service.Oauth2ServerService;
import cn.com.sipsg.common.oauth2.core.utils.StpOauth2ServerUtils;
import cn.com.sipsg.common.pojo.CommonResult;
import cn.dev33.satoken.annotation.SaIgnore;
import cn.dev33.satoken.oauth2.processor.SaOAuth2ServerProcessor;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * oauth2协议服务接口
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Tag(name = "oauth2协议服务接口")
@RestController
@RequiredArgsConstructor
@RequestMapping("/oauth2/server")
public class Oauth2ServerController {

    private final Oauth2ServerService oauth2ServerService;

    /**
     * 获取clientToken（提供给客户端使用） http://{host}:{port}/oauth2/clientToken?grant_type=client_credentials&client_id={client_id}&client_secret={client_secret}&scope={scope}
     *
     * @return clientToken
     */
    @SaIgnore
    @Operation(summary = "获取clientToken")
    @RequestMapping("/clientToken")
    public CommonResult<ClientTokenModelVO> getClientToken() {
        return CommonResult.data(this.oauth2ServerService.getClientToken());
    }


    /**
     * 授权码模式登录
     *
     * @return 登录信息
     */
    @SaIgnore
    @Operation(summary = "授权码模式登录")
    @RequestMapping("/doLogin")
    public CommonResult<LoginVO> doLogin(@RequestBody LoginBO loginBO) {
        return CommonResult.data(oauth2ServerService.login(loginBO));
    }

    /**
     * 获取最终授权重定向地址
     * 模式一：Code授权码 / 模式二：隐藏式
     *
     * @return 授权码code地址
     */
    @SaIgnore
    @Operation(summary = "获取最终授权重定向地址")
    @RequestMapping("/getRedirectUri")
    public Object getRedirectUri() {
        return oauth2ServerService.getRedirectUri();
    }

    /**
     * 确认授权 http://{host}:{port}/oauth2/doConfirm?client_id={value}&scope={value}&build_redirect_uri=true&response_type={value}&redirect_uri={value}&state={value}
     *
     * @return 确认授权返回Code
     */
    @SaIgnore
    @Operation(summary = "确认授权")
    @PostMapping("/doConfirm")
    public Object doConfirm() {
        return CommonResult.data(oauth2ServerService.doConfirm());
    }

    /**
     * 获取accessToken（提供给客户端使用）
     *
     * @return accessToken
     */
    @SaIgnore
    @Operation(summary = "获取accessToken")
    @RequestMapping("/token")
    public CommonResult<AccessTokenVO> getAccessTokenByCode() {
        return CommonResult.data(oauth2ServerService.getAccessTokenByCode());
    }

    /**
     * 刷新accessToken（提供给客户端使用） 接口请求方式：http://{host}:{port}/oauth2/refresh?grant_type=refresh_token&client_id={client_id}&client_secret={client_secret}&refresh_token={refresh_token}
     *
     * @return 刷新accessToken
     */
    @SaIgnore
    @Operation(summary = "刷新accessToken")
    @RequestMapping("/refresh")
    public CommonResult<AccessTokenVO> refreshAccessToken() {
        return CommonResult.data(oauth2ServerService.refreshAccessToken());
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @SaIgnore
    @Operation(summary = "获取用户信息")
    @RequestMapping("/userinfo")
    public CommonResult<LoginUserVO> getUserInfo() {
        return CommonResult.data(oauth2ServerService.getUserInfo());
    }

    /**
     * 回收AccessToken
     *
     * @return Void
     */
    @SaIgnore
    @Operation(summary = "回收AccessToken")
    @RequestMapping("/revoke")
    public CommonResult<Void> revoke() {
        SaOAuth2ServerProcessor.instance.revoke();
        return CommonResult.success();
    }

    /**
     * 退出登录
     *
     * @return Void
     */
    @Operation(summary = "退出登录")
    @PostMapping("/logout")
    public CommonResult<Void> logout() {
        StpOauth2ServerUtils.logout();
        return CommonResult.success();
    }

}

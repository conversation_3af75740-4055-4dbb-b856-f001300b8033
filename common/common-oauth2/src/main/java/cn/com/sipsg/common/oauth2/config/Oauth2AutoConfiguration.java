package cn.com.sipsg.common.oauth2.config;


import cn.dev33.satoken.oauth2.SaOAuth2Manager;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.ComponentScan;

/**
 * oauth2自动装配类
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@AutoConfiguration
@ComponentScan("cn.com.sipsg.common.oauth2")
@ConditionalOnClass(SaOAuth2Manager.class)
public class Oauth2AutoConfiguration {

}




package cn.com.sipsg.common.oauth2.core.service;

import cn.com.sipsg.common.oauth2.core.pojo.bo.LoginBO;
import cn.com.sipsg.common.oauth2.core.pojo.vo.AccessTokenVO;
import cn.com.sipsg.common.oauth2.core.pojo.vo.ClientTokenModelVO;
import cn.com.sipsg.common.oauth2.core.pojo.vo.LoginUserVO;
import cn.com.sipsg.common.oauth2.core.pojo.vo.LoginVO;
import cn.dev33.satoken.oauth2.config.SaOAuth2ServerConfig;

/**
 * oauth2 相关服务
 *
 * <AUTHOR>
 * @since 2025/04/14
 **/
public interface Oauth2ServerService {

    /**
     * 初始化应用信息
     */
    void initClientInfo(SaOAuth2ServerConfig oauth2Server);

    /**
     * 客户端登陆获取token
     *
     * @return 客户端token
     */
    ClientTokenModelVO getClientToken();

    /**
     * oauth2统一认证授权码登录
     *
     * @return 登录信息
     */
    LoginVO login(LoginBO bo);

    /**
     * 前后端分离——统一认证平台server端授权
     *
     * @return 登录信息
     */
    Object getRedirectUri();

    /**
     * 前后端分离——用户授权获取code
     *
     * @return 用户授权获取code
     */
    Object doConfirm();

    /**
     * 根据code获取accessToken
     *
     * @return 登录信息
     */
    AccessTokenVO getAccessTokenByCode();

    /**
     * 刷新AccessToken
     *
     * @return 登录信息
     */
    AccessTokenVO refreshAccessToken();

    /**
     * 查询用户信息
     *
     * @return 用户信息
     */
    LoginUserVO getUserInfo();

}

package cn.com.sipsg.common.oauth2.core.service;


import cn.com.sipsg.common.mybatis.core.service.BaseServiceX;
import cn.com.sipsg.common.oauth2.core.entity.Oauth2Scope;
import cn.com.sipsg.common.oauth2.core.pojo.bo.Oauth2ScopeQueryBO;
import cn.com.sipsg.common.oauth2.core.pojo.bo.Oauth2ScopeSaveBO;
import cn.com.sipsg.common.oauth2.core.pojo.vo.Oauth2ClientScopeVO;
import cn.com.sipsg.common.oauth2.core.pojo.vo.Oauth2ScopeVO;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;

import java.util.Set;

/**
 * 客户端范围管理服务接口
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public interface Oauth2ScopeService extends BaseServiceX<Oauth2Scope> {

    /**
     * 查询分页列表
     *
     * @param bo 参数
     * @return 分页列表
     */
    CommonPageVO<Oauth2ScopeVO> page(Oauth2ScopeQueryBO bo);

    /**
     * 新增
     *
     * @param bo 参数
     * @return ID
     */
    String save(Oauth2ScopeSaveBO bo);

    /**
     * 编辑
     *
     * @param bo 参数
     */
    void update(Oauth2ScopeSaveBO bo);

    /**
     * 根据scopeId查询详情
     *
     * @param scopeId 范围id
     * @return 详情
     */
    Oauth2ScopeVO detail(String scopeId);

    /**
     * 删除
     *
     * @param scopeId 范围id
     */
    void delete(String scopeId);

    /**
     * 批量删除
     *
     * @param scopeIds 范围id
     */
    void batchDelete(Set<String> scopeIds);

    /**
     * 修改范围状态
     *
     * @param scopeId 主键
     */
    void updateStatus(String scopeId);

    /**
     * 分页查询关联客户端的scope
     *
     * @param bo 参数
     */
    CommonPageVO<Oauth2ClientScopeVO> getClientScopePage(Oauth2ScopeQueryBO bo);

    /**
     * 分页查询未关联客户端的scope
     *
     * @param bo 参数
     */
    CommonPageVO<Oauth2ScopeVO> getNotInClientScopePage(Oauth2ScopeQueryBO bo);

}

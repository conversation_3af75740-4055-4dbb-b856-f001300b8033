package cn.com.sipsg.common.oauth2.core.pojo.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 客户端-范围关系保存BO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "客户端-范围关系保存 BO")
public class Oauth2ClientScopeSaveBO {


    /**
     * 客户端id
     */
    @Schema(description = "应用id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "应用id不能为空")
    private String appId;

    /**
     * 范围id集合
     */
    @Schema(description = "范围id集合", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "范围id集合不能为空")
    private List<String> scopeIds;

}

package cn.com.sipsg.common.oauth2.core.service.impl;

import cn.com.sipsg.common.constant.CommonConstants;
import cn.com.sipsg.common.enums.CommonStatusEnum;
import cn.com.sipsg.common.exception.enums.ErrorCodeEnum;
import cn.com.sipsg.common.mybatis.core.query.LambdaQueryWrapperX;
import cn.com.sipsg.common.mybatis.core.query.MPJLambdaWrapperX;
import cn.com.sipsg.common.oauth2.core.entity.Oauth2ClientScope;
import cn.com.sipsg.common.oauth2.core.entity.Oauth2Scope;
import cn.com.sipsg.common.oauth2.core.enums.ScopeReservedEnum;
import cn.com.sipsg.common.oauth2.core.mapper.Oauth2ClientScopeMapper;
import cn.com.sipsg.common.oauth2.core.mapper.Oauth2ScopeMapper;
import cn.com.sipsg.common.oauth2.core.pojo.bo.Oauth2ScopeQueryBO;
import cn.com.sipsg.common.oauth2.core.pojo.bo.Oauth2ScopeSaveBO;
import cn.com.sipsg.common.oauth2.core.pojo.vo.Oauth2ClientScopeVO;
import cn.com.sipsg.common.oauth2.core.pojo.vo.Oauth2ScopeVO;
import cn.com.sipsg.common.oauth2.core.service.Oauth2ScopeService;
import cn.com.sipsg.common.oauth2.core.service.Oauth2ServerService;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.util.AssertUtils;
import cn.com.sipsg.common.util.BeanUtils;
import cn.dev33.satoken.oauth2.SaOAuth2Manager;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Set;
import java.util.stream.Collectors;

/**
 * 客户端范围管理服务实现类
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Service
@RequiredArgsConstructor
public class Oauth2ScopeServiceImpl extends ServiceImpl<Oauth2ScopeMapper, Oauth2Scope> implements Oauth2ScopeService {

    private final Oauth2ScopeMapper oauth2ScopeMapper;

    private final Oauth2ClientScopeMapper oauth2ClientScopeMapper;

    private final Oauth2ServerService oauth2ServerService;

    @Override
    public CommonPageVO<Oauth2ScopeVO> page(Oauth2ScopeQueryBO bo) {
        LambdaQueryWrapperX<Oauth2Scope> queryWrapper = new LambdaQueryWrapperX<>();
        // 填充查询条件
        queryWrapper.likeIfPresent(Oauth2Scope::getScopeName, bo.getScopeName());
        queryWrapper.likeIfPresent(Oauth2Scope::getScopeCode, bo.getScopeCode());
        queryWrapper.orderByAsc(Oauth2Scope::getReserved);
        queryWrapper.orderByAsc(Oauth2Scope::getSort);
        queryWrapper.orderByDesc(Oauth2Scope::getCreateTime);
        return this.page(bo, queryWrapper).convert(entity -> BeanUtils.copyProperties(entity, Oauth2ScopeVO.class));
    }

    @Override
    public String save(Oauth2ScopeSaveBO bo) {
        bo.setScopeId(null);
        // 新增
        AssertUtils.isTrue(this.baseMapper.selectCount(Oauth2Scope::getScopeCode, bo.getScopeCode()) >= CommonConstants.ONE, "范围标识不能重复！");
        Oauth2Scope oauth2Scope = BeanUtils.copyProperties(bo, Oauth2Scope.class);
        this.save(oauth2Scope);
        return oauth2Scope.getScopeId();
    }

    @Override
    @Transactional
    public void update(Oauth2ScopeSaveBO bo) {
        // 编辑
        Oauth2Scope oauth2Scope = this.baseMapper.selectById(bo.getScopeId());
        AssertUtils.isTrue(ObjectUtil.equals(oauth2Scope.getReserved(), ScopeReservedEnum.RESERVED.getCode()), "保留数据, 不能修改！");
        Long count = this.baseMapper.selectCount(new LambdaQueryWrapperX<Oauth2Scope>().eq(Oauth2Scope::getScopeCode, bo.getScopeCode())
                .ne(Oauth2Scope::getScopeId, bo.getScopeId()));
        AssertUtils.isTrue(count >= CommonConstants.ONE, "范围标识不能重复！");
        Oauth2Scope oauth2ScopeUpdate = BeanUtils.copyProperties(bo, Oauth2Scope.class);
        this.updateById(oauth2ScopeUpdate);
        // 如果范围状态改为禁用，需要调整应用-范围关系
        if (ObjectUtil.equals(oauth2ScopeUpdate.getStatus(), CommonStatusEnum.DISABLE.getCode())) {
            this.oauth2ClientScopeMapper.delete(Oauth2ClientScope::getScopeId, bo.getScopeId());
            // 刷新应用数据
            oauth2ServerService.initClientInfo(SaOAuth2Manager.getServerConfig());
        }
    }

    @Override
    public Oauth2ScopeVO detail(String scopeId) {
        Oauth2Scope oauth2Scope = this.getById(scopeId);
        // 校验是否存在
        AssertUtils.isTrue(oauth2Scope == null, ErrorCodeEnum.DATA_NOT_EXIST);
        return BeanUtils.copyProperties(oauth2Scope, Oauth2ScopeVO.class);
    }

    @Override
    @Transactional
    public void delete(String scopeId) {
        Oauth2Scope oauth2Scope = this.getById(scopeId);
        AssertUtils.isTrue(ObjectUtil.isNull(oauth2Scope), ErrorCodeEnum.DATA_NOT_EXIST);
        AssertUtils.isTrue(ObjectUtil.equals(oauth2Scope.getReserved(), CommonConstants.ZERO), "该状态下，数据不允许删除！");
        this.removeById(scopeId);

        this.oauth2ClientScopeMapper.delete(Oauth2ClientScope::getScopeId, scopeId);
        // 刷新应用数据
        oauth2ServerService.initClientInfo(SaOAuth2Manager.getServerConfig());
    }

    @Override
    @Transactional
    public void batchDelete(Set<String> scopeIds) {
        if (CollUtil.isNotEmpty(scopeIds)) {
            this.baseMapper.deleteByIds(scopeIds);
        }
    }


    @Override
    @Transactional
    public void updateStatus(String scopeId) {
        Oauth2Scope oauth2Scope = this.getById(scopeId);
        AssertUtils.isTrue(ObjectUtil.isNull(oauth2Scope), ErrorCodeEnum.DATA_NOT_EXIST);
        oauth2Scope.setStatus(CommonConstants.ONE - oauth2Scope.getStatus());
        this.updateById(oauth2Scope);

        this.oauth2ClientScopeMapper.delete(Oauth2ClientScope::getScopeId, scopeId);
        // 刷新应用数据
        oauth2ServerService.initClientInfo(SaOAuth2Manager.getServerConfig());
    }

    @Override
    public CommonPageVO<Oauth2ClientScopeVO> getClientScopePage(Oauth2ScopeQueryBO bo) {
        MPJLambdaWrapperX<Oauth2Scope> queryWrapper = new MPJLambdaWrapperX<>();
        queryWrapper.selectAll(Oauth2Scope.class)
                .selectAs(Oauth2ClientScope::getAppId, Oauth2ClientScopeVO::getAppId)
                .innerJoin(Oauth2ClientScope.class, Oauth2ClientScope::getScopeId, Oauth2Scope::getScopeId)
                .eqIfExists(Oauth2ClientScope::getAppId, bo.getAppId())
                .likeIfExists(Oauth2Scope::getScopeName, bo.getScopeName())
                .likeIfExists(Oauth2Scope::getScopeCode, bo.getScopeCode())
                .orderByAsc(Oauth2Scope::getSort)
                .orderByDesc(Oauth2Scope::getCreateTime);
        return this.selectJoinListPage(bo, Oauth2ClientScopeVO.class, queryWrapper);
    }

    @Override
    public CommonPageVO<Oauth2ScopeVO> getNotInClientScopePage(Oauth2ScopeQueryBO bo) {
        Set<String> scopeIds = oauth2ClientScopeMapper.selectList(Oauth2ClientScope::getAppId, bo.getAppId()).stream().map(Oauth2ClientScope::getScopeId).collect(Collectors.toSet());
        MPJLambdaWrapperX<Oauth2Scope> queryWrapper = new MPJLambdaWrapperX<>();
        queryWrapper
                .notInIfPresent(Oauth2Scope::getScopeId, scopeIds)
                .likeIfExists(Oauth2Scope::getScopeName, bo.getScopeName())
                .likeIfExists(Oauth2Scope::getScopeCode, bo.getScopeCode())
                .eqIfExists(Oauth2Scope::getStatus, CommonStatusEnum.ENABLE.getCode())
                .orderByAsc(Oauth2Scope::getSort)
                .orderByDesc(Oauth2Scope::getCreateTime);
        return oauth2ScopeMapper.selectPage(bo, queryWrapper).convert(oauth2Scope -> BeanUtils.copyProperties(oauth2Scope, Oauth2ScopeVO.class));
    }
}

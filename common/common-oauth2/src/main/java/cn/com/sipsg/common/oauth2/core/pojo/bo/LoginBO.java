package cn.com.sipsg.common.oauth2.core.pojo.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

/**
 * 登录 BO
 *
 * <AUTHOR>
 * @since 2025/04/14
 **/
@Getter
@Setter
@Schema(description = "登录 BO")
public class LoginBO {

    /**
     * 用户名
     */
    @Schema(description = "用户名", requiredMode = Schema.RequiredMode.REQUIRED, example = "admin")
    @NotBlank(message = "用户名不能为空", groups = PasswordLoginGroup.class)
    private String username;

    /**
     * 密码
     */
    @Schema(description = "密码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "密码不能为空", groups = PasswordLoginGroup.class)
    private String password;

    /**
     * 验证码key
     */
    @Schema(description = "验证码key")
    private String captchaKey;

    /**
     * 验证码
     */
    @Schema(description = "验证码，验证码开启时，需要传递")
    @NotBlank(message = "验证码不能为空", groups = CaptchaEnableGroup.class)
    private String captchaVerification;

    /**
     * 验证码启用校验分组
     */
    public interface CaptchaEnableGroup {
    }

    /**
     * 账号密码登录校验分组
     */
    public interface PasswordLoginGroup {
    }
}

package cn.com.sipsg.common.oauth2.core.pojo.vo;

import cn.com.sipsg.common.enums.CommonStatusEnum;
import cn.com.sipsg.common.oauth2.core.enums.ScopeReservedEnum;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;


/**
 * 客户端-范围关系 VO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "客户端-范围关系 VO")
public class Oauth2ClientScopeVO implements VO {

    /**
     * 客户端id
     */
    @TableId("app_id")
    @Schema(description = "应用id", requiredMode = Schema.RequiredMode.REQUIRED)
    private String appId;

    /**
     * 范围id
     */
    @Schema(description = "范围id", requiredMode = Schema.RequiredMode.REQUIRED)
    private String scopeId;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

    /**
     * 范围标识
     */
    @Schema(description = "范围标识")
    private String scopeCode;

    /**
     * 范围名称
     */
    @Schema(description = "范围名称")
    private String scopeName;

    /**
     * 范围描述
     */
    @Schema(description = "范围描述")
    private String description;

    /**
     * 范围状态(1=正常,0=禁用)
     */
    @Schema(description = "范围状态(1=正常,0=禁用)", requiredMode = Schema.RequiredMode.REQUIRED)
    @Trans(type = TransType.ENUM, key = CommonStatusEnum.Fields.desc)
    private CommonStatusEnum status;

    /**
     * 范围排序
     */
    @Schema(description = "范围排序")
    private Integer sort;

    /**
     * 是否保留；0-保留,1-不保留
     */
    @Schema(description = "是否保留；0-保留,1-不保留", requiredMode = Schema.RequiredMode.REQUIRED)
    @Trans(type = TransType.ENUM, key = ScopeReservedEnum.Fields.desc)
    private ScopeReservedEnum reserved;

}

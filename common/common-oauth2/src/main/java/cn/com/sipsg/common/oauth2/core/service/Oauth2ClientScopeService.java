package cn.com.sipsg.common.oauth2.core.service;


import cn.com.sipsg.common.mybatis.core.service.BaseServiceX;
import cn.com.sipsg.common.oauth2.core.entity.Oauth2ClientScope;

import java.util.List;

/**
 * 客户端-范围关系服务接口
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public interface Oauth2ClientScopeService extends BaseServiceX<Oauth2ClientScope> {

    /**
     * 根据客户端id获取范围标识集合
     *
     * @param appId 应用id
     * @return 应用范围标识
     */
    List<String> getScopeCodeByAppId(String appId);

    /**
     * 删除客户端-接口范围关联关系
     *
     * @param appId 应用id
     * @param scopeId 范围id
     */
    void deleteAppScope(String appId, String scopeId);

}

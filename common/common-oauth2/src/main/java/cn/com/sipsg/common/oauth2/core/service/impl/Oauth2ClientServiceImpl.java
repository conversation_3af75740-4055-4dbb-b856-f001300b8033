package cn.com.sipsg.common.oauth2.core.service.impl;

import cn.com.sipsg.common.constant.CommonConstants;
import cn.com.sipsg.common.exception.enums.ErrorCodeEnum;
import cn.com.sipsg.common.mybatis.core.query.LambdaQueryWrapperX;
import cn.com.sipsg.common.oauth2.core.entity.Oauth2Client;
import cn.com.sipsg.common.oauth2.core.entity.Oauth2ClientScope;
import cn.com.sipsg.common.oauth2.core.mapper.Oauth2ClientMapper;
import cn.com.sipsg.common.oauth2.core.mapper.Oauth2ClientScopeMapper;
import cn.com.sipsg.common.oauth2.core.mapper.Oauth2ScopeMapper;
import cn.com.sipsg.common.oauth2.core.pojo.bo.Oauth2ClientQueryBO;
import cn.com.sipsg.common.oauth2.core.pojo.bo.Oauth2ClientSaveBO;
import cn.com.sipsg.common.oauth2.core.pojo.vo.Oauth2ClientVO;
import cn.com.sipsg.common.oauth2.core.service.Oauth2ClientService;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.util.AssertUtils;
import cn.com.sipsg.common.util.BeanUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;

/**
 * 客户端管理服务实现类
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Service
@RequiredArgsConstructor
public class Oauth2ClientServiceImpl extends ServiceImpl<Oauth2ClientMapper, Oauth2Client> implements Oauth2ClientService {

    private final Oauth2ScopeMapper oauth2ScopeMapper;

    private final Oauth2ClientScopeMapper oauth2ClientScopeMapper;

    @Override
    public CommonPageVO<Oauth2ClientVO> page(Oauth2ClientQueryBO bo) {
        LambdaQueryWrapperX<Oauth2Client> queryWrapper = new LambdaQueryWrapperX<>();
        // 填充查询条件
        queryWrapper.likeIfPresent(Oauth2Client::getAppName, bo.getAppName());
        return this.page(bo, queryWrapper).convert(entity -> BeanUtils.copyProperties(entity, Oauth2ClientVO.class));
    }

    @Override
    public String save(Oauth2ClientSaveBO bo) {
        bo.setAppId(null);
        // 新增oauth2协议下客户端管理
        Oauth2Client oauth2Client = BeanUtils.copyProperties(bo, Oauth2Client.class);
        oauth2Client.setClientTokenTimeout(oauth2Client.getAccessTokenTimeout());
        AssertUtils.isTrue(this.baseMapper.selectCount(Oauth2Client::getClientId, oauth2Client.getClientId()) >= CommonConstants.ONE, "clientId重复啦");
        //生成clientSecret
        oauth2Client.setClientSecret(RandomUtil.randomString(50));
        this.save(oauth2Client);
        return oauth2Client.getAppId();
    }

    @Override
    public void update(Oauth2ClientSaveBO bo) {
        // 编辑oauth2协议下客户端管理
        Oauth2Client oauth2Client = BeanUtils.copyProperties(bo, Oauth2Client.class);
        oauth2Client.setClientTokenTimeout(oauth2Client.getAccessTokenTimeout());
        this.updateById(oauth2Client);
    }

    @Override
    public Oauth2ClientVO detail(String appId) {
        Oauth2Client oauth2Client = this.getById(appId);
        // 校验是否存在
        AssertUtils.isTrue(oauth2Client == null, ErrorCodeEnum.DATA_NOT_EXIST);
        return BeanUtils.copyProperties(oauth2Client, Oauth2ClientVO.class);
    }

    @Override
    @Transactional
    public void delete(String appId) {
        Oauth2Client oauth2Client = this.getById(appId);
        if (oauth2Client != null) {
            // 删除oauth2协议下客户端管理
            this.removeById(appId);
            //删除应用-授权范围关系
            this.oauth2ClientScopeMapper.delete(Oauth2ClientScope::getAppId, appId);
        }
    }

    @Override
    public void updateStatus(String appId) {
        Oauth2Client oauth2Client = this.getById(appId);
        AssertUtils.isTrue(ObjectUtil.isNull(oauth2Client), ErrorCodeEnum.DATA_NOT_EXIST);
        oauth2Client.setStatus(CommonConstants.ONE - oauth2Client.getStatus());
        this.updateById(oauth2Client);
    }

    @Override
    @Transactional
    public void saveClientScope(String appId, List<String> scopeIds) {
        checkExist(appId);
        AssertUtils.isTrue(!oauth2ScopeMapper.existAllPrimaryKeys(new HashSet<>(scopeIds)), ErrorCodeEnum.INVALID_RELATED_RECORD_ID);
        List<Oauth2ClientScope> appScopeList = CollUtil.newArrayList();
        for (String scopeId : scopeIds) {
            Oauth2ClientScope oauth2ClientScope = new Oauth2ClientScope();
            oauth2ClientScope.setAppId(appId);
            oauth2ClientScope.setScopeId(scopeId);
            appScopeList.add(oauth2ClientScope);
        }
        oauth2ClientScopeMapper.insert(appScopeList);
    }

    @Override
    public String getAppNameByClientId(String clientId) {
        //获取应用信息
        Oauth2Client client = this.baseMapper.selectOne(Oauth2Client::getClientId, clientId);
        AssertUtils.isTrue(Objects.isNull(client), "app不存在或无效的clientId或此app已经禁用");
        return client.getAppName();
    }

    @Override
    public String createClientId() {
        //生成clientId
        return StrUtil.builder().append(DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN)).append(RandomUtil.randomNumbers(2)).toString();
    }

}

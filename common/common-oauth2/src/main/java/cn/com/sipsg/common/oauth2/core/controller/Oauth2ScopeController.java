package cn.com.sipsg.common.oauth2.core.controller;

import cn.com.sipsg.common.constant.PageConstants;
import cn.com.sipsg.common.oauth2.core.pojo.bo.Oauth2ScopeQueryBO;
import cn.com.sipsg.common.oauth2.core.pojo.bo.Oauth2ScopeSaveBO;
import cn.com.sipsg.common.oauth2.core.pojo.vo.Oauth2ScopeVO;
import cn.com.sipsg.common.oauth2.core.service.Oauth2ScopeService;
import cn.com.sipsg.common.operationlog.core.annotation.OperationLog;
import cn.com.sipsg.common.operationlog.core.enums.OperationTypeEnum;
import cn.com.sipsg.common.pojo.CommonResult;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.validation.AddValidated;
import cn.com.sipsg.common.validation.UpdateValidated;
import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

/**
 * 客户端范围管理控制器
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Tag(name = "客户端范围管理")
@RestController
@RequiredArgsConstructor
@RequestMapping("/oauth2/scope")
public class Oauth2ScopeController {

    private final String module = "客户端范围管理";

    private final Oauth2ScopeService oauth2ScopeService;

    /**
     * 查询分页列表
     *
     * @param bo 参数
     * @return 分页列表
     */
    @Operation(summary = "查询分页列表")
    @OperationLog(module = module, value = "查询分页列表", type = OperationTypeEnum.QUERY, saveResponse = false)
    @SaCheckPermission(value = "oauth2:scope:all")
    @PostMapping("/page")
    public CommonResult<CommonPageVO<Oauth2ScopeVO>> page(@RequestBody Oauth2ScopeQueryBO bo) {
        return CommonResult.data(oauth2ScopeService.page(bo));
    }

    /**
     * 查询列表
     *
     * @param bo 参数
     * @return 列表
     */
    @Operation(summary = "查询列表")
    @OperationLog(module = module, value = "查询列表", type = OperationTypeEnum.QUERY, saveResponse = false)
    @SaCheckPermission(value = "oauth2:scope:all")
    @PostMapping("/list")
    public CommonResult<List<Oauth2ScopeVO>> list(@RequestBody Oauth2ScopeQueryBO bo) {
        bo.setSize(PageConstants.SIZE_NONE);
        return CommonResult.data(oauth2ScopeService.page(bo).getRecords());
    }

    /**
     * 新增
     *
     * @param bo 参数
     * @return ID
     */
    @Operation(summary = "新增")
    @OperationLog(module = module, value = "新增", type = OperationTypeEnum.SAVE)
    @SaCheckPermission(value = "oauth2:scope:all")
    @PostMapping("/save")
    public CommonResult<String> save(@AddValidated @RequestBody Oauth2ScopeSaveBO bo) {
        return CommonResult.data(oauth2ScopeService.save(bo));
    }

    /**
     * 编辑
     *
     * @param bo 参数
     */
    @Operation(summary = "编辑")
    @OperationLog(module = module, value = "编辑", type = OperationTypeEnum.UPDATE)
    @SaCheckPermission(value = "oauth2:scope:all")
    @PostMapping("/update")
    public CommonResult<Void> update(@UpdateValidated @RequestBody Oauth2ScopeSaveBO bo) {
        oauth2ScopeService.update(bo);
        return CommonResult.success();
    }

    /**
     * 查询详情
     *
     * @param scopeId 范围id
     */
    @Operation(summary = "查询详情")
    @Parameter(name = "scopeId", description = "范围id", required = true, example = "1111")
    @OperationLog(module = module, value = "查询详情", type = OperationTypeEnum.QUERY, saveResponse = false)
    @SaCheckPermission(value = "oauth2:scope:all")
    @GetMapping("/detail")
    public CommonResult<Oauth2ScopeVO> detail(@RequestParam String scopeId) {
        return CommonResult.data(oauth2ScopeService.detail(scopeId));
    }

    /**
     * 删除
     *
     * @param scopeId 范围id
     */
    @Operation(summary = "删除")
    @Parameter(name = "scopeId", description = "范围id", required = true, example = "1111")
    @OperationLog(module = module, value = "删除", type = OperationTypeEnum.DELETE)
    @SaCheckPermission(value = "oauth2:scope:all")
    @PostMapping("/delete")
    public CommonResult<Void> delete(@RequestParam String scopeId) {
        oauth2ScopeService.delete(scopeId);
        return CommonResult.success();
    }

    /**
     * 批量删除
     *
     * @param scopeIds 范围id
     */
    @Operation(summary = "批量删除")
    @Parameter(name = "scopeId", description = "范围id", required = true, example = "1111")
    @OperationLog(module = module, value = "批量删除", type = OperationTypeEnum.DELETE)
    @SaCheckPermission(value = "oauth2:scope:all")
    @PostMapping("/batchDelete")
    public CommonResult<Void> batchDelete(@RequestParam Set<String> scopeIds) {
        oauth2ScopeService.batchDelete(scopeIds);
        return CommonResult.success();
    }

    /**
     * 修改范围状态
     *
     * @param scopeId 范围id
     */
    @Operation(summary = "修改范围状态")
    @Parameter(name = "scopeId", description = "范围id", required = true, example = "1111")
    @OperationLog(module = module, value = "修改范围状态", type = OperationTypeEnum.DELETE)
    @SaCheckPermission(value = "oauth2:scope:all")
    @PostMapping("/updateStatus")
    public CommonResult<Void> updateStatus(@RequestParam String scopeId) {
        oauth2ScopeService.updateStatus(scopeId);
        return CommonResult.success();
    }

}

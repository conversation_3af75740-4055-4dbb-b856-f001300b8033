package cn.com.sipsg.common.lock4j.core.strategy;

import cn.com.sipsg.common.exception.BusinessException;
import cn.com.sipsg.common.exception.enums.ErrorCodeEnum;
import com.baomidou.lock.LockFailureStrategy;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Method;

/**
 * 自定义获取锁失败策略，抛出 {@link BusinessException} 异常
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Slf4j
public class DefaultLockFailureStrategy implements LockFailureStrategy {

    @Override
    public void onLockFailure(String key, Method method, Object[] arguments) {
        log.debug("[onLockFailure][线程:{} 获取锁失败，key:{} 获取失败:{} ]", Thread.currentThread().getName(), key, arguments);
        throw new BusinessException(ErrorCodeEnum.LOCKED);
    }

}

package cn.com.sipsg.common.idempotent.core.keyresolver.impl;

import cn.com.sipsg.common.idempotent.core.annotation.Idempotent;
import cn.com.sipsg.common.idempotent.core.keyresolver.IdempotentKeyResolver;
import cn.com.sipsg.common.web.core.util.WebFrameworkUtils;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import org.aspectj.lang.JoinPoint;

/**
 * 用户级别的幂等 Key 解析器，使用方法名 + 方法参数 + userId，组装成一个 Key
 * <p>
 * 为了避免 Key 过长，使用 MD5 进行“压缩”
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public class UserIdempotentKeyResolver implements IdempotentKeyResolver {

    @Override
    public String resolver(JoinPoint joinPoint, Idempotent idempotent) {
        String methodName = joinPoint.getSignature().toString();
        String argsStr = StrUtil.join(",", joinPoint.getArgs());
        String userId = WebFrameworkUtils.getLoginUserId();
        return SecureUtil.md5(methodName + argsStr + userId);
    }

}

package cn.com.sipsg.common.websocket.core.sender.local;

import cn.com.sipsg.common.websocket.core.sender.AbstractWebSocketMessageSender;
import cn.com.sipsg.common.websocket.core.sender.WebSocketMessageSender;
import cn.com.sipsg.common.websocket.core.session.WebSocketSessionManager;

/**
 * 本地的 {@link WebSocketMessageSender} 实现类
 * <p>
 * 注意：仅仅适合单机场景！！！
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public class LocalWebSocketMessageSender extends AbstractWebSocketMessageSender {

    public LocalWebSocketMessageSender(WebSocketSessionManager sessionManager) {
        super(sessionManager);
    }

}

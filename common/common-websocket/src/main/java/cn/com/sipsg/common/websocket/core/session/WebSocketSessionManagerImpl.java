package cn.com.sipsg.common.websocket.core.session;

import cn.com.sipsg.common.security.core.LoginUser;
import cn.com.sipsg.common.websocket.core.util.WebSocketFrameworkUtils;
import cn.hutool.core.collection.CollUtil;
import org.springframework.web.socket.WebSocketSession;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 默认的 {@link WebSocketSessionManager} 实现类
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public class WebSocketSessionManagerImpl implements WebSocketSessionManager {

    /**
     * id 与 WebSocketSession 映射
     * <p>
     * key：Session ID
     */
    private final ConcurrentMap<String, WebSocketSession> idSessions = new ConcurrentHashMap<>();

    /**
     * user 与 WebSocketSession 映射
     * <p>
     * key：用户 ID
     */
    private final ConcurrentMap<String, CopyOnWriteArrayList<WebSocketSession>> userSessions = new ConcurrentHashMap<>();

    @Override
    public void addSession(WebSocketSession session) {
        // 添加到 idSessions 中
        idSessions.put(session.getId(), session);
        // 添加到 userSessions 中
        LoginUser user = WebSocketFrameworkUtils.getLoginUser(session);
        if (user == null) {
            return;
        }
        CopyOnWriteArrayList<WebSocketSession> sessions = userSessions.get(user.getUserId());
        if (sessions == null) {
            sessions = new CopyOnWriteArrayList<>();
            if (userSessions.putIfAbsent(user.getUserId(), sessions) != null) {
                sessions = userSessions.get(user.getUserId());
            }
        }
        sessions.add(session);
    }

    @Override
    public void removeSession(WebSocketSession session) {
        // 移除从 idSessions 中
        idSessions.remove(session.getId());
        // 移除从 idSessions 中
        LoginUser user = WebSocketFrameworkUtils.getLoginUser(session);
        if (user == null) {
            return;
        }
        CopyOnWriteArrayList<WebSocketSession> sessions = userSessions.get(user.getUserId());
        sessions.removeIf(session0 -> session0.getId().equals(session.getId()));
        if (CollUtil.isEmpty(sessions)) {
            userSessions.remove(user.getUserId(), sessions);
        }
    }

    @Override
    public WebSocketSession getSession(String id) {
        return idSessions.get(id);
    }

    @Override
    public Collection<WebSocketSession> getSessionList() {
        LinkedList<WebSocketSession> result = new LinkedList<>(); // 避免扩容
        for (List<WebSocketSession> sessions : userSessions.values()) {
            if (CollUtil.isEmpty(sessions)) {
                continue;
            }
            result.addAll(sessions);
        }
        return result;
    }

    @Override
    public Collection<WebSocketSession> getSessionList(String userId) {
        CopyOnWriteArrayList<WebSocketSession> sessions = userSessions.get(userId);
        return CollUtil.isNotEmpty(sessions) ? new ArrayList<>(sessions) : new ArrayList<>();
    }

    @Override
    public Collection<WebSocketSession> getSessionList(Collection<String> userIds) {
        if (CollUtil.isEmpty(userIds)) {
            return Collections.emptyList();
        }
        Set<String> userIdSet = new HashSet<>(userIds);
        Collection<WebSocketSession> sessions = new ArrayList<>();
        for (String userId : userIdSet) {
            sessions.addAll(getSessionList(userId));
        }
        return sessions;
    }

}

package cn.com.sipsg.common.websocket.config;

import cn.com.sipsg.common.websocket.core.enums.WebSocketSenderTypeEnum;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * WebSocket 配置属性
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@ConfigurationProperties(prefix = "medox.websocket")
@Getter
@Setter
public class WebSocketProperties {

    /**
     * 是否开启
     */
    private Boolean enabled = true;

    /**
     * WebSocket 连接路径
     */
    private String path = "/ws";

    /**
     * WebSocket 发送消息类型
     */
    private WebSocketSenderTypeEnum senderType = WebSocketSenderTypeEnum.LOCAL;

}

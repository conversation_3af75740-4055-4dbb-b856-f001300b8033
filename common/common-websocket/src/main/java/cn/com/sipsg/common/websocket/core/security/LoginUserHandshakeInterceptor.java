package cn.com.sipsg.common.websocket.core.security;

import cn.com.sipsg.common.security.core.LoginUser;
import cn.com.sipsg.common.security.core.util.SecurityUtils;
import cn.com.sipsg.common.websocket.core.util.WebSocketFrameworkUtils;
import cn.dev33.satoken.stp.StpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.server.HandshakeInterceptor;

import java.util.Map;

/**
 * 登录用户的 {@link HandshakeInterceptor} 实现类
 * <p>
 * 流程如下：
 * 1. 前端连接 websocket 时，会通过拼接 ?Authorization={token} 到 ws:// 连接后，这样它可以被 Sa-Token 所认证通过
 * 2. {@link LoginUserHandshakeInterceptor} 负责把 {@link LoginUser} 添加到 {@link WebSocketSession} 中
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Slf4j
public class LoginUserHandshakeInterceptor implements HandshakeInterceptor {

    @Override
    public boolean beforeHandshake(ServerHttpRequest request, ServerHttpResponse response,
                                   WebSocketHandler wsHandler, Map<String, Object> attributes) {
        // 未登录情况下拒绝握手
        if (!StpUtil.isLogin()) {
            log.error("[beforeHandshake][未授权客户端，连接失败]");
            return false;
        }
        LoginUser loginUser = SecurityUtils.getLoginUser();
        WebSocketFrameworkUtils.setLoginUser(loginUser, attributes);
        return true;
    }

    @Override
    public void afterHandshake(ServerHttpRequest request, ServerHttpResponse response,
                               WebSocketHandler wsHandler, Exception exception) {
        // do nothing
    }

}

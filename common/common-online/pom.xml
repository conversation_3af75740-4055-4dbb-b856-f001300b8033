<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.com.sipsg</groupId>
        <artifactId>common</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>common-online</artifactId>
    <description>动态表单</description>

    <dependencies>
        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>rail-transit-server-module-system-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- RPC 相关 -->
        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>common-rpc</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>common-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <!-- 认证相关 -->
        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>common-security</artifactId>
        </dependency>

        <!-- 数据权限相关 -->
        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>common-datapermission</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>common-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>common-datasource</artifactId>
        </dependency>

        <!-- 空间处理 -->
        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>common-geo</artifactId>
        </dependency>

        <!-- 代码生成 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-freemarker</artifactId>
        </dependency>

        <!-- 操作日志 -->
        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>common-operationlog</artifactId>
        </dependency>

    </dependencies>

</project>
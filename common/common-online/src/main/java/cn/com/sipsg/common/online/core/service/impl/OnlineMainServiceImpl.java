package cn.com.sipsg.common.online.core.service.impl;

import cn.com.sipsg.common.mybatis.core.query.LambdaQueryWrapperX;
import cn.com.sipsg.common.mybatis.core.query.MPJLambdaWrapperX;
import cn.com.sipsg.common.online.core.convert.OnlineMainConvert;
import cn.com.sipsg.common.online.core.entity.*;
import cn.com.sipsg.common.online.core.enums.OnlineSubFormTypeEnum;
import cn.com.sipsg.common.online.core.mapper.*;
import cn.com.sipsg.common.online.core.pojo.bo.OnlineMainQueryBO;
import cn.com.sipsg.common.online.core.pojo.bo.OnlineMainSaveBO;
import cn.com.sipsg.common.online.core.pojo.vo.OnlineMainSimpleVO;
import cn.com.sipsg.common.online.core.pojo.vo.OnlineMainVO;
import cn.com.sipsg.common.online.core.service.OnlineMainService;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.util.AssertUtils;
import cn.com.sipsg.module.system.api.upms.SysMenuApi;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import javax.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 动态表单-主页面信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Service
@RequiredArgsConstructor
public class OnlineMainServiceImpl extends ServiceImpl<OnlineMainMapper, OnlineMain> implements OnlineMainService {

    private final OnlineDataModelMapper onlineDataModelMapper;

    private final OnlineColumnMapper onlineColumnMapper;

    private final OnlineOperateMapper onlineOperateMapper;

    private final OnlineSubFormMapper onlineSubFormMapper;

    @Resource
    private SysMenuApi sysMenuApi;

    /**
     * 分页查询动态表单-主页面信息
     *
     * @param bo 参数
     * @return 分页信息
     */
    @Override
    public CommonPageVO<OnlineMainVO> page(OnlineMainQueryBO bo) {
        LambdaQueryWrapperX<OnlineMain> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper
                .likeIfPresent(OnlineMain::getName, bo.getName())
                .eqIfPresent(OnlineMain::getFormType, bo.getFormType());
        return this.page(bo, queryWrapper).convert(OnlineMainConvert.INSTANCE::convert);
    }

    /**
     * 获取动态表单-主页面信息详情
     *
     * @param id id
     * @return 详情
     */
    @Override
    public OnlineMainVO detail(String id) {
        OnlineMain onlineMain = this.checkExist(id);
        return OnlineMainConvert.INSTANCE.convert(onlineMain);
    }

    /**
     * 新增动态表单-主页面信息
     *
     * @param bo 参数
     * @return 主页面ID
     */
    @Override
    @Transactional
    public OnlineMain save(OnlineMainSaveBO bo) {
        bo.setId(null);
        OnlineMain onlineMain = OnlineMainConvert.INSTANCE.convert(bo);
        bo.setIfEnable(Boolean.FALSE);
        this.save(onlineMain);
        return onlineMain;
    }

    /**
     * 编辑动态表单-主页面信息
     *
     * @param bo 参数
     */
    @Override
    @Transactional
    public void update(OnlineMainSaveBO bo) {
        OnlineMain onlineMain = OnlineMainConvert.INSTANCE.convert(bo);
        this.updateById(onlineMain);
    }

    /**
     * 删除动态表单-主页面信息
     *
     * @param id id
     */
    @Override
    @Transactional
    public void delete(String id) {
        //判断如果有子表单在菜单中被应用，则不允许被删除
        //判断菜单中是否有引用的子菜单
        List<OnlineMainSimpleVO> onlineMainSimpleVOList = listEnableWithQueryForm(id);
        if (CollUtil.isNotEmpty(onlineMainSimpleVOList)) {
            onlineMainSimpleVOList.forEach(onlineMainSimpleVO -> onlineMainSimpleVO.getFormList().forEach(onlineSubForm -> {
                Long count = sysMenuApi.countMenuByFormId(onlineSubForm.getId());
                AssertUtils.isTrue(count > 0, "该表单已被菜单引用，无法删除");
            }));
        }
        // 删除主页面
        this.removeById(id);
        // 删除数据模型
        onlineDataModelMapper.delete(OnlineDataModel::getMainId, id);
        // 删除数据模型-字段
        onlineColumnMapper.delete(OnlineColumn::getMainId, id);
        // 删除子表单和子表单操作
        List<OnlineSubForm> subFormList = onlineSubFormMapper.selectList(OnlineSubForm::getMainId, id);
        if (CollUtil.isNotEmpty(subFormList)) {
            for (OnlineSubForm onlineSubForm : subFormList) {
                // 删除子表单
                onlineSubFormMapper.deleteById(onlineSubForm);
                // 删除子表单操作
                onlineOperateMapper.delete(OnlineOperate::getSubFormId, onlineSubForm.getId());
            }
        }
    }

    @Override
    @Transactional
    public void updateStatus(String id, Boolean ifEnable) {
        checkExist(id);
        //判断菜单中是否有引用的子菜单
        List<OnlineMainSimpleVO> onlineMainSimpleVOList = listEnableWithQueryForm(id);
        if (CollUtil.isNotEmpty(onlineMainSimpleVOList)) {
            onlineMainSimpleVOList.forEach(onlineMainSimpleVO -> onlineMainSimpleVO.getFormList().forEach(onlineSubForm -> {
                Long count = sysMenuApi.countMenuByFormId(onlineSubForm.getId());
                AssertUtils.isTrue(count > 0 && !ifEnable, 45000, "该表单已被菜单引用，无法禁用");
            }));
        }
        OnlineMain updateObj = new OnlineMain();
        updateObj.setId(id);
        updateObj.setIfEnable(ifEnable);
        this.updateById(updateObj);
    }

    @Override
    public List<OnlineMainSimpleVO> listEnableWithQueryForm(String id) {
        MPJLambdaWrapperX<OnlineMain> queryWrapperX = new MPJLambdaWrapperX<>();
        queryWrapperX
                .selectAll(OnlineMain.class)
                .selectCollection(OnlineSubForm.class, OnlineMainSimpleVO::getFormList)
                .leftJoin(OnlineSubForm.class, OnlineSubForm::getMainId, OnlineMain::getId)
                .eq(OnlineMain::isIfEnable, Boolean.TRUE)
                .in(OnlineSubForm::getType, OnlineSubFormTypeEnum.SEARCH, OnlineSubFormTypeEnum.SEARCH_TREE)
                .eq(StrUtil.isNotBlank(id), OnlineMain::getId, id);
        return this.selectJoinList(OnlineMainSimpleVO.class, queryWrapperX);
    }

}
package cn.com.sipsg.common.online.core.controller;

import cn.com.sipsg.common.constant.PageConstants;
import cn.com.sipsg.common.online.core.pojo.bo.OnlineColumnSearchBO;
import cn.com.sipsg.common.online.core.pojo.bo.OnlineDataModelQueryBO;
import cn.com.sipsg.common.online.core.pojo.bo.OnlineDataModelSaveBO;
import cn.com.sipsg.common.online.core.pojo.vo.OnlineDataModelVO;
import cn.com.sipsg.common.online.core.service.OnlineDataModelService;
import cn.com.sipsg.common.pojo.CommonResult;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.validation.AddValidated;
import cn.com.sipsg.common.validation.UpdateValidated;
import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 动态表单-数据模型 前端控制器
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Tag(name = "动态表单-数据模型管理")
@RestController
@RequiredArgsConstructor
@RequestMapping("/online/dataModel")
public class OnlineDataModelController {

    private final OnlineDataModelService onlineDataModelService;

    /**
     * 分页查询动态表单-数据模型
     *
     * @param bo 参数
     * @return 分页信息
     */
    @Operation(summary = "分页查询动态表单-数据模型")
    @SaCheckPermission(value = "online:main:all")
    @PostMapping("/page")
    public CommonResult<CommonPageVO<OnlineDataModelVO>> page(@Validated @RequestBody OnlineDataModelQueryBO bo) {
        return CommonResult.data(onlineDataModelService.page(bo));
    }

    /**
     * 列表查询动态表单-数据模型
     *
     * @param bo 查询条件
     * @return 列表信息
     */
    @Operation(summary = "列表查询动态表单-数据模型")
    @PostMapping("/list")
    public CommonResult<List<OnlineDataModelVO>> list(@Validated @RequestBody OnlineDataModelQueryBO bo) {
        bo.setSize(PageConstants.SIZE_NONE);
        return CommonResult.data(onlineDataModelService.page(bo).getRecords());
    }

    /**
     * 获取动态表单-数据模型详情
     *
     * @param id id
     * @return 详情
     */
    @Operation(summary = "获取动态表单-数据模型详情")
    @Parameter(name = "id", description = "主键", required = true)
    @SaCheckPermission(value = "online:main:all")
    @GetMapping("/detail")
    public CommonResult<OnlineDataModelVO> detail(@RequestParam String id) {
        return CommonResult.data(onlineDataModelService.detail(id));
    }

    /**
     * 通过模型标识和主页面id查询主键和关联字段
     *
     * @param bo 搜索对象
     * @return 详情
     */
    @Operation(summary = "通过模型标识和主页面id查询主键和关联字段")
    @SaCheckPermission(value = "online:main:all")
    @PostMapping("/getPrimaryKeyAndRelationColumn")
    public CommonResult<Map<String, String>> getPrimaryKeyAndRelationColumn(@RequestBody OnlineColumnSearchBO bo) {
        return CommonResult.data(onlineDataModelService.getPrimaryKeyAndRelationColumn(bo));
    }

    /**
     * 新增动态表单-数据模型
     *
     * @param bo 新增对象
     * @return 成功或失败
     */
    @Operation(summary = "新增动态表单-数据模型")
    @SaCheckPermission(value = "online:main:all")
    @PostMapping("/save")
    public CommonResult<String> save(@AddValidated @RequestBody OnlineDataModelSaveBO bo) {
        return CommonResult.data(onlineDataModelService.save(bo));
    }

    /**
     * 修改动态表单-数据模型
     *
     * @param bo 修改对象
     * @return 成功或失败
     */
    @Operation(summary = "修改动态表单-数据模型")
    @SaCheckPermission(value = "online:main:all")
    @PostMapping("/update")
    public CommonResult<Void> update(@UpdateValidated @RequestBody OnlineDataModelSaveBO bo) {
        onlineDataModelService.update(bo);
        return CommonResult.success();
    }

    /**
     * 删除动态表单-数据模型
     *
     * @param id id
     * @return 成功或失败
     */
    @Operation(summary = "删除动态表单-数据模型", description = "删除动态表单-数据模型")
    @Parameter(name = "id", description = "主键", required = true)
    @SaCheckPermission(value = "online:main:all")
    @PostMapping("/delete")
    public CommonResult<Void> delete(@RequestParam String id) {
        onlineDataModelService.delete(id);
        return CommonResult.success();
    }

    /**
     * 从数据库同步更新字段
     *
     * @param id id
     * @return 成功或失败
     */
    @Operation(summary = "从数据库同步更新字段", description = "从数据库同步更新字段")
    @Parameter(name = "id", description = "主键", required = true)
    @SaCheckPermission(value = "online:main:all")
    @PostMapping("/syncColumn")
    public CommonResult<Void> syncColumn(@RequestParam String id) {
        onlineDataModelService.syncColumn(id);
        return CommonResult.success();
    }

}

package cn.com.sipsg.common.online.core.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 动态表单-主页面信息简单VO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "动态表单-主页面信息简单VO")
public class OnlineMainSimpleVO {

    /**
     * 主键
     */
    @Schema(description = "主键")
    private String id;

    /**
     * 页面类型 1业务 2流程
     */
    @Schema(description = "页面类型 1业务 2流程")
    private Integer formType;

    /**
     * 页面名称
     */
    @Schema(description = "页面名称")
    private String name;

    /**
     * 表单列表
     */
    @Schema(description = "表单列表")
    private List<OnlineSubFormSimpleVO> formList;

}
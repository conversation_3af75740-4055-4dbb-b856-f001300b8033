package cn.com.sipsg.common.online.core.ext.impl;

import cn.com.sipsg.common.datasource.core.ext.DatasourceExt;
import cn.com.sipsg.common.mybatis.core.query.LambdaQueryWrapperX;
import cn.com.sipsg.common.online.core.entity.OnlineMain;
import cn.com.sipsg.common.online.core.service.OnlineMainService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 数据源拓展动态表单实现类
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Component
@RequiredArgsConstructor
public class DatasourceExtOnlineImpl implements DatasourceExt {

    private final OnlineMainService onlineMainService;

    @Override
    public boolean inUse(String datasourceId) {
        LambdaQueryWrapperX<OnlineMain> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eq(OnlineMain::getDbId, datasourceId);
        return onlineMainService.count(queryWrapper) > 0;
    }

}

package cn.com.sipsg.common.online.core.entity;

import com.baomidou.mybatisplus.annotation.OrderBy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.github.yulichang.annotation.EntityMapping;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;


/**
 * 动态表单-子表单
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@FieldNameConstants
@TableName(value = "medox_online_column_form", autoResultMap = true)
public class OnlineColumnForm {

    /**
     * 主键
     */
    private String id;

    /**
     * 子表单id
     */
    private String formId;

    /**
     * 表单模块标识
     */
    private String widgetId;

    /**
     * 表单字段名
     */
    private String formColumnLabel;

    /**
     * 表单字段项类型
     */
    private String formItemType;

    /**
     * 是否显示
     */
    private Boolean showStatus;

    /**
     * 表单字段属性json
     */
    private String formColumnJson;

    /**
     * 关联模型字段id
     */
    private String relateColumnId;

    /**
     * 排序类型
     */
    private String sortType;

    /**
     * 排序值
     */
    @OrderBy(asc = true, sort = Short.MIN_VALUE)
    private Integer sort;

    /**
     * 关联字段
     */
    @TableField(exist = false)
    @EntityMapping(thisField = Fields.relateColumnId, joinField = OnlineColumn.Fields.id)
    private OnlineColumn column;
}
package cn.com.sipsg.common.online.core.pojo.bo;

import cn.com.sipsg.common.pojo.bo.PageBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 动态表单分页参数
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Schema(description = "动态表单分页参数")
@Getter
@Setter
public class OnlineSortablePageBO extends PageBO {

    /**
     * 排序字段列表
     */
    @Schema(description = "排序字段列表")
    private List<OnlineSortingFieldBO> sortingFields;

}

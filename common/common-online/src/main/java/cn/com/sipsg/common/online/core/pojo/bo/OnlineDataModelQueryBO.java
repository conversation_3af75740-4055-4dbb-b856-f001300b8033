package cn.com.sipsg.common.online.core.pojo.bo;

import cn.com.sipsg.common.pojo.bo.SortablePageBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

/**
 * 动态表单-数据模型查询BO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "动态表单-数据模型查询BO")
public class OnlineDataModelQueryBO extends SortablePageBO {

    /**
     * 主页面id
     */
    @Schema(description = "主页面id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "主页面id不能为空")
    private String mainId;

    /**
     * 模型标识
     */
    @Schema(description = "模型标识")
    private String modelCode;

}
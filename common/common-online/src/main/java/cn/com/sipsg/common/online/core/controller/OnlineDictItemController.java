package cn.com.sipsg.common.online.core.controller;

import cn.com.sipsg.common.constant.PageConstants;
import cn.com.sipsg.common.operationlog.core.annotation.OperationLog;
import cn.com.sipsg.common.operationlog.core.enums.OperationTypeEnum;
import cn.com.sipsg.common.pojo.CommonResult;
import cn.com.sipsg.common.online.core.pojo.bo.OnlineDictItemQueryBO;
import cn.com.sipsg.common.online.core.pojo.vo.OnlineDictItemVO;
import cn.com.sipsg.common.online.core.service.OnlineDictItemService;
import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 动态表单-自定义字典项管理控制器
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Tag(name = "动态表单-自定义字典项管理")
@RestController
@RequiredArgsConstructor
@RequestMapping("/online/dictItem")
public class OnlineDictItemController {

    private final OnlineDictItemService onlineDictItemService;

    /**
     * 查询动态表单-自定义字典项列表
     *
     * @param bo 参数
     * @return 动态表单-自定义字典项列表
     */
    @Operation(summary = "查询动态表单-自定义字典项列表")
    @OperationLog(module = "动态表单-自定义字典项管理", value = "查询动态表单-自定义字典项列表", type = OperationTypeEnum.QUERY, saveResponse = false)
    @SaCheckPermission(value = "online:dict:all")
    @PostMapping("/list")
    public CommonResult<List<OnlineDictItemVO>> list(@RequestBody OnlineDictItemQueryBO bo) {
        bo.setSize(PageConstants.SIZE_NONE);
        return CommonResult.data(onlineDictItemService.page(bo).getRecords());
    }
}
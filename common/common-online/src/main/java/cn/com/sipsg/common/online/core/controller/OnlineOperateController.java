package cn.com.sipsg.common.online.core.controller;

import cn.com.sipsg.common.constant.PageConstants;
import cn.com.sipsg.common.online.core.pojo.bo.OnlineOperateQueryBO;
import cn.com.sipsg.common.online.core.pojo.bo.OnlineOperateSaveBO;
import cn.com.sipsg.common.online.core.pojo.vo.OnlineOperateVO;
import cn.com.sipsg.common.online.core.service.OnlineOperateService;
import cn.com.sipsg.common.pojo.CommonResult;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.validation.AddValidated;
import cn.com.sipsg.common.validation.UpdateValidated;
import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 动态表单-子表单操作 前端控制器
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Tag(name = "动态表单-子表单操作管理")
@RestController
@RequiredArgsConstructor
@RequestMapping("/online/operate")
public class OnlineOperateController {

    private final OnlineOperateService onlineOperateService;

    /**
     * 查询动态表单-子表单操作分页列表
     *
     * @param bo 参数
     * @return 动态表单-子表单操作分页列表
     */
    @Operation(summary = "查询动态表单-子表单操作分页列表")
    @SaCheckPermission(value = "online:main:all")
    @PostMapping("/page")
    public CommonResult<CommonPageVO<OnlineOperateVO>> page(@Validated @RequestBody OnlineOperateQueryBO bo) {
        return CommonResult.data(onlineOperateService.page(bo));
    }

    /**
     * 查询动态表单-子表单操作列表
     *
     * @param bo 参数
     * @return 动态表单-子表单操作列表
     */
    @Operation(summary = "查询动态表单-子表单操作列表")
    @SaCheckPermission(value = "online:main:all")
    @PostMapping("/list")
    public CommonResult<List<OnlineOperateVO>> list(@Validated @RequestBody OnlineOperateQueryBO bo) {
        bo.setSize(PageConstants.SIZE_NONE);
        return CommonResult.data(onlineOperateService.page(bo).getRecords());
    }

    /**
     * 新增动态表单-子表单操作
     *
     * @param bo 参数
     * @return 动态表单-子表单操作ID
     */
    @Operation(summary = "新增动态表单-子表单操作")
    @SaCheckPermission(value = "online:main:all")
    @PostMapping("/save")
    public CommonResult<String> save(@AddValidated @RequestBody OnlineOperateSaveBO bo) {
        return CommonResult.data(onlineOperateService.save(bo));
    }

    /**
     * 批量新增动态表单-子表单操作
     *
     * @param list 参数
     * @return 动态表单-子表单操作ID
     */
    @Operation(summary = "批量新增动态表单-子表单操作")
    @SaCheckPermission(value = "online:main:all")
    @PostMapping("/batchSave")
    public CommonResult<Void> save(@AddValidated @RequestBody List<OnlineOperateSaveBO> list) {
        onlineOperateService.batchsSave(list);
        return CommonResult.success();
    }

    /**
     * 编辑动态表单-子表单操作
     *
     * @param bo 参数
     */
    @Operation(summary = "编辑动态表单-子表单操作")
    @SaCheckPermission(value = "online:main:all")
    @PostMapping("/update")
    public CommonResult<Void> update(@UpdateValidated @RequestBody OnlineOperateSaveBO bo) {
        onlineOperateService.update(bo);
        return CommonResult.success();
    }

    /**
     * 查询动态表单-子表单操作详情
     *
     * @param id 主键
     */
    @Operation(summary = "查询动态表单-子表单操作详情")
    @Parameter(name = "id", description = "主键", required = true, example = "1111")
    @SaCheckPermission(value = "online:main:all")
    @GetMapping("/detail")
    public CommonResult<OnlineOperateVO> detail(@RequestParam String id) {
        return CommonResult.data(onlineOperateService.detail(id));
    }

    /**
     * 删除动态表单-子表单操作
     *
     * @param id 主键
     */
    @Operation(summary = "删除动态表单-子表单操作")
    @Parameter(name = "id", description = "主键", required = true, example = "1111")
    @SaCheckPermission(value = "online:main:all")
    @PostMapping("/delete")
    public CommonResult<Void> delete(@RequestParam String id) {
        onlineOperateService.delete(id);
        return CommonResult.success();
    }

}
package cn.com.sipsg.common.online.core.pojo.dto;

import cn.com.sipsg.common.datasource.core.entity.Datasource;
import cn.com.sipsg.common.datasource.core.strategy.DatasourceStrategy;
import cn.com.sipsg.common.online.core.entity.OnlineColumnForm;
import cn.com.sipsg.common.online.core.entity.OnlineDataModel;
import cn.com.sipsg.common.online.core.pojo.vo.TreeWidgetVO;
import cn.com.sipsg.common.online.core.pojo.vo.WidgetParentVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 动态表单-子表单DTO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "动态表单-子表单DTO")
public class OnlineCrudFormDTO {

    /**
     * 主键
     */
    @Schema(description = "主键")
    private String id;

    /**
     * 子表单内部模块id
     */
    @Schema(description = "子表单内部模块id")
    private String tableId;

    /**
     * 主页面id
     */
    @Schema(description = "主页面id")
    private String mainId;

    /**
     * 子表单编码
     */
    @Schema(description = "子表单编码")
    private String code;

    /**
     * 子表单名称
     */
    @Schema(description = "子表单名称")
    private String name;

    /**
     * 子表单类型（list,edit,oneToManyEdit）
     */
    @Schema(description = "子表单类型（list,edit,oneToManyEdit）")
    private String type;

    /**
     * 子表单配置
     */
    @Schema(description = "子表单配置")
    private String propertyJson;

    /**
     * 子表单树结构内容
     */
    private TreeWidgetVO treeWidgetVO;

    /**
     * 子表单内容
     */
    @Schema(description = "子表单内容")
    private WidgetParentVO widgetJson;

    /**
     * 数据模型ids
     */
    @Schema(description = "数据模型ids")
    private List<String> modelIds;

    /**
     * 数据模型列表
     */
    @Schema(description = "数据模型列表")
    private List<OnlineDataModel> dataModelList;

    /**
     * 数据模型列表
     */
    @Schema(description = "数据源")
    private Datasource datasource;

    /**
     * 数据源策略
     */
    @Schema(description = "数据源策略")
    private DatasourceStrategy datasourceStrategy;

    /**
     * 表单字段列表
     */
    @Schema(description = "表单字段列表")
    private List<OnlineColumnForm> columnFormList;

}
package cn.com.sipsg.common.online.core.service.impl;

import cn.com.sipsg.common.exception.enums.ErrorCodeEnum;
import cn.com.sipsg.common.mybatis.core.query.LambdaQueryWrapperX;
import cn.com.sipsg.common.online.core.entity.OnlineOperate;
import cn.com.sipsg.common.online.core.mapper.OnlineOperateMapper;
import cn.com.sipsg.common.online.core.pojo.bo.OnlineOperateQueryBO;
import cn.com.sipsg.common.online.core.pojo.bo.OnlineOperateSaveBO;
import cn.com.sipsg.common.online.core.pojo.vo.OnlineOperateVO;
import cn.com.sipsg.common.online.core.service.OnlineOperateService;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.util.AssertUtils;
import cn.com.sipsg.common.util.BeanUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 动态表单-子表单操作 服务实现类
 *
 * <AUTHOR>
 * @since 2025/04/14
*/
@Service
public class OnlineOperateServiceImpl extends ServiceImpl<OnlineOperateMapper, OnlineOperate> implements OnlineOperateService {

    @Override
    public CommonPageVO<OnlineOperateVO> page(OnlineOperateQueryBO bo) {
        LambdaQueryWrapperX<OnlineOperate> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eq(OnlineOperate::getSubFormId, bo.getSubFormId());
        queryWrapper.eqIfPresent(OnlineOperate::getTableId, bo.getTableId());
        queryWrapper.orderByAsc(OnlineOperate::getCreateTime);
        return this.page(bo, queryWrapper).convert(entity -> BeanUtils.copyProperties(entity, OnlineOperateVO.class));
    }

    @Override
    @Transactional
    public String save(OnlineOperateSaveBO bo) {
        bo.setId(null);
        // 新增动态表单-子表单操作
        OnlineOperate onlineOperate = BeanUtils.copyProperties(bo, OnlineOperate.class);
        this.save(onlineOperate);
        return onlineOperate.getId();
    }

    @Override
    @Transactional
    public void update(OnlineOperateSaveBO bo) {
        // 编辑动态表单-子表单操作
        OnlineOperate onlineOperate = BeanUtils.copyProperties(bo, OnlineOperate.class);
        this.updateById(onlineOperate);
    }

    @Override
    public OnlineOperateVO detail(String id) {
        OnlineOperate onlineOperate = this.getById(id);
        // 校验是否存在
        AssertUtils.isTrue(onlineOperate == null, ErrorCodeEnum.DATA_NOT_EXIST);
        return BeanUtils.copyProperties(onlineOperate, OnlineOperateVO.class);
    }

    @Override
    @Transactional
    public void delete(String id) {
        // 删除动态表单-子表单操作
        this.removeById(id);
    }

    @Override
    public void batchsSave(List<OnlineOperateSaveBO> list) {
        //判断如果当前tableId下已经存在操作，则不继续执行新增操作了
        if (list != null && list.size() > 0) {
            String tableId = list.get(0).getTableId();
            String subFormId = list.get(0).getSubFormId();
            if (StrUtil.isNotEmpty(tableId) && StrUtil.isNotEmpty(subFormId)) {
                List<OnlineOperate> onlineOperateList = this.list(new LambdaQueryWrapperX<OnlineOperate>()
                    .eq(OnlineOperate::getTableId, tableId)
                    .eq(OnlineOperate::getSubFormId, subFormId));
                if (CollUtil.isNotEmpty(onlineOperateList)) {
                    return;
                }
            }
        }
        List<OnlineOperate> onlineOperateList = BeanUtils.copyToList(list, OnlineOperate.class);
        this.saveBatch(onlineOperateList);
    }

}
package cn.com.sipsg.common.online.core.service;

import cn.com.sipsg.common.online.core.entity.OnlineDict;
import cn.com.sipsg.common.online.core.pojo.bo.OnlineCrudPageBO;
import cn.com.sipsg.common.online.core.pojo.bo.OnlineDictPageBO;
import cn.com.sipsg.common.online.core.pojo.dto.OnlineCrudFormDTO;
import cn.com.sipsg.common.online.core.pojo.vo.OnlineDictItemDataVO;
import cn.com.sipsg.common.pojo.bo.DeleteBO;
import cn.com.sipsg.common.pojo.vo.CommonImportVO;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.hutool.core.lang.tree.Tree;
import com.alibaba.fastjson2.JSONObject;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import org.springframework.web.multipart.MultipartFile;

/**
 * 动态表单-CRUD服务接口
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public interface OnlineCrudService {

    /**
     * 查询分页列表
     *
     * @param onlineCrudFormDTO 表单配置
     * @param bo                参数
     * @return 分页列表
     */
    CommonPageVO<Map<String, Object>> page(OnlineCrudFormDTO onlineCrudFormDTO, OnlineCrudPageBO bo);

    /**
     * 新增
     *
     * @param onlineCrudFormDTO 表单配置
     * @param reqJSON           参数
     * @return 主键
     */
    String save(OnlineCrudFormDTO onlineCrudFormDTO, JSONObject reqJSON);

    /**
     * 编辑
     *
     * @param onlineCrudFormDTO 表单配置
     * @param reqJSON           参数
     */
    void update(OnlineCrudFormDTO onlineCrudFormDTO, JSONObject reqJSON);

    /**
     * 详情
     *
     * @param onlineCrudFormDTO 表单配置
     * @param reqJSON           参数
     * @return 详情
     */
    Map<String, Object> detail(OnlineCrudFormDTO onlineCrudFormDTO, JSONObject reqJSON);

    /**
     * 详情
     *
     * @param onlineCrudFormDTO 表单配置
     * @param id                主键
     * @return 详情
     */
    Map<String, Object> detail(OnlineCrudFormDTO onlineCrudFormDTO, String id);

    /**
     * 根据主键列表查询
     *
     * @param onlineCrudFormDTO 表单配置
     * @param ids               主键列表
     * @return 列表
     */
    Map<String, Map<String, Object>> getMapByIds(OnlineCrudFormDTO onlineCrudFormDTO, Collection<String> ids);

    /**
     * 删除
     *
     * @param onlineCrudFormDTO 表单配置
     * @param reqJSON           参数
     */
    void delete(OnlineCrudFormDTO onlineCrudFormDTO, JSONObject reqJSON);

    /**
     * 删除
     *
     * @param onlineCrudFormDTO 表单配置
     * @param id                主键
     */
    void delete(OnlineCrudFormDTO onlineCrudFormDTO, String id);

    /**
     * 批量删除
     *
     * @param onlineCrudFormDTO 表单配置
     * @param deleteBO          参数
     */
    void deleteBatch(OnlineCrudFormDTO onlineCrudFormDTO, DeleteBO deleteBO);

    /**
     * 导出
     *
     * @param onlineCrudFormDTO 表单配置
     * @param bo                参数
     */
    void export(OnlineCrudFormDTO onlineCrudFormDTO, OnlineCrudPageBO bo);

    /**
     * 新增一对多关系
     *
     * @param onlineCrudFormDTO 表单配置
     * @param modelCode         模型编码
     * @param mainDataId        主模型关联属性ID
     * @param reqJSON           参数
     * @return 主键
     */
    String saveOneToManyRelation(OnlineCrudFormDTO onlineCrudFormDTO, String modelCode, String mainDataId, JSONObject reqJSON);

    /**
     * 编辑一对多关系
     *
     * @param onlineCrudFormDTO 表单配置
     * @param modelCode         模型编码
     * @param reqJSON           参数
     */
    void updateOneToManyRelation(OnlineCrudFormDTO onlineCrudFormDTO, String modelCode, JSONObject reqJSON);

    /**
     * 删除一对多关系
     *
     * @param onlineCrudFormDTO 表单配置
     * @param modelCode         模型编码
     * @param dataId            数据主键
     */
    void deleteOneToManyRelation(OnlineCrudFormDTO onlineCrudFormDTO, String modelCode, Object dataId);

    /**
     * 批量删除一对多关系
     *
     * @param onlineCrudFormDTO 表单配置
     * @param modelCode         模型编码
     * @param deleteBO          参数
     */
    void deleteBatchOneToManyRelation(OnlineCrudFormDTO onlineCrudFormDTO, String modelCode, DeleteBO deleteBO);

    /**
     * 查询一对多关系分页列表
     *
     * @param onlineCrudFormDTO 表单配置
     * @param bo                参数
     * @return 分页列表
     */
    CommonPageVO<Map<String, Object>> pageOneToManyRelation(OnlineCrudFormDTO onlineCrudFormDTO, OnlineCrudPageBO bo);

    /**
     * 查询一对多关系详情
     *
     * @param onlineCrudFormDTO 表单配置
     * @param modelCode         模型编码
     * @param dataId            数据主键
     * @return 详情
     */
    Map<String, Object> getOneToManyRelation(OnlineCrudFormDTO onlineCrudFormDTO, String modelCode, Object dataId);

    /**
     * 导出一对多关系
     *
     * @param onlineCrudFormDTO 表单配置
     * @param bo                参数
     */
    void exportOneToManyRelation(OnlineCrudFormDTO onlineCrudFormDTO, OnlineCrudPageBO bo);

    /**
     * 导入Excel
     *
     * @param onlineCrudFormDTO 表单配置
     * @param file              文件
     * @return 导出结果
     */
    CommonImportVO importExcel(OnlineCrudFormDTO onlineCrudFormDTO, MultipartFile file);

    /**
     * 导入一对多excel
     *
     * @param onlineCrudFormDTO 表单配置
     * @param modelCode         模型编码
     * @param dataId            数据主键
     * @param file              文件
     * @return                  导出结果
     */
    CommonImportVO importOneToManyExcel(OnlineCrudFormDTO onlineCrudFormDTO, String modelCode, String dataId, MultipartFile file);

    /**
     * 查询字典数据
     *
     * @param onlineDict 字典配置参数
     * @return 字典数据
     */
    CommonPageVO<Map<String, Object>> getDictData(OnlineDict onlineDict, OnlineDictPageBO bo);

    /**
     * 查询字典数据列表
     *
     * @param onlineDict 字典配置参数
     * @param bo         参数
     * @return 字典数据列表
     */
    List<Map<String, Object>> getDictDataList(OnlineDict onlineDict, OnlineDictPageBO bo);

    /**
     * 查询字典数据树
     *
     * @param onlineDict 字典配置参数
     * @param bo         参数
     * @return 字典数据树
     */
    List<Tree<String>> getDictDataTree(OnlineDict onlineDict, OnlineDictPageBO bo);

    /**
     * 查询字典数据列表
     *
     * @param onlineDict 字典配置参数
     * @param bo         参数
     * @return 字典数据列表
     */
    List<OnlineDictItemDataVO> getOnlineDictDataList(OnlineDict onlineDict, OnlineDictPageBO bo);
}

package cn.com.sipsg.common.online.core.entity;

import cn.com.sipsg.common.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

/**
 * 动态表单-子表单操作
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@TableName(value = "medox_online_operate", autoResultMap = true)
public class OnlineOperate extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private String id;

    /**
     * 子表单id
     */
    private String subFormId;

    /**
     * 操作编码
     */
    private String code;

    /**
     * 操作名称
     */
    private String name;

    /**
     * 操作关联表单id
     */
    private String operateSubFormId;

    /**
     * 操作类型
     */
    private String type;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 操作按钮位置(row 表格行内按钮 top 表格顶部按钮)
     */
    private String buttonPosition;

    /**
     * 前端表格id
     */
    private String tableId;

}

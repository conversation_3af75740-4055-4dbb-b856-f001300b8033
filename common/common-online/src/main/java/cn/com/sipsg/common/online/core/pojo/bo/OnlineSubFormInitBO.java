package cn.com.sipsg.common.online.core.pojo.bo;

import cn.com.sipsg.common.validation.group.AddGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 动态表单-单表初始化BO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "动态表单-单表初始化BO")
public class OnlineSubFormInitBO {

    /**
     * 查询表单widgetJson
     */
    @Schema(description = "查询表单widgetJson")
    @NotBlank(message = "查询表单widgetJson", groups = {AddGroup.class})
    private String searchWidgetJson;

    /**
     * 编辑表单widgetJson
     */
    @Schema(description = "编辑表单widgetJson")
    private String editWidgetJson;

    /**
     * 主页面id
     */
    @Schema(description = "主页面id")
    private String mainId;
}
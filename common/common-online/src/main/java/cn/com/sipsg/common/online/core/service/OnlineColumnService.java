package cn.com.sipsg.common.online.core.service;


import cn.com.sipsg.common.mybatis.core.service.BaseServiceX;
import cn.com.sipsg.common.online.core.entity.OnlineColumn;
import cn.com.sipsg.common.online.core.pojo.bo.OnlineColumnQueryBO;
import cn.com.sipsg.common.online.core.pojo.bo.OnlineColumnSaveBO;
import cn.com.sipsg.common.online.core.pojo.vo.OnlineColumnVO;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;

import java.util.List;

/**
 * 动态表单-模型字段 服务类
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public interface OnlineColumnService extends BaseServiceX<OnlineColumn> {

    /**
     * 分页查询动态表单-模型字段
     *
     * @param bo   查询条件
     * @return 分页信息
     */
    CommonPageVO<OnlineColumnVO> page(OnlineColumnQueryBO bo);

    /**
     * 获取动态表单-模型字段详情
     *
     * @param id id
     * @return 详情
     */
    OnlineColumnVO detail(String id);

    /**
     * 新增动态表单-模型字段
     *
     * @param bo 新增对象
     * @return 主键
     */
    String save(OnlineColumnSaveBO bo);

    /**
     * 编辑动态表单-模型字段
     *
     * @param bo 编辑对象
     */
    void update(OnlineColumnSaveBO bo);

    /**
     * 删除动态表单-模型字段
     *
     * @param id id
     */
    void delete(String id);

    /**
     * 获取主键字段
     *
     * @param columnList 字段列表
     * @return 主键
     */
    OnlineColumn getPrimaryKeyColumn(List<OnlineColumn> columnList);

}
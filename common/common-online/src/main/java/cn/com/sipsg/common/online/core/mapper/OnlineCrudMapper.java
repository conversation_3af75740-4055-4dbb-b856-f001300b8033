package cn.com.sipsg.common.online.core.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * CRUD Mapper 接口
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Mapper
public interface OnlineCrudMapper {

    /**
     * 批量新增
     */
    void insertBatch(@Param("tableName") String tableName, @Param("sb") StringBuilder sb, @Param("sbValue") StringBuilder sbValue, @Param("models") List<HashMap> maps);

    /**
     * 新增
     */
    void insert(@Param("tableName") String tableName, @Param("sb") StringBuilder sb, @Param("sbValue") StringBuilder sbValue, @Param("model") Map map);

    /**
     * 更新
     */
    void update(@Param("tableName") String tableName, @Param("sb") StringBuilder sb, @Param("sbValue") StringBuilder sbValue, @Param("model") Map map, @Param("dataPermissionFilter") String dataPermissionFilter);

    /**
     * 批量更新
     */
    void updateBatch(@Param("tableName") String tableName, @Param("sb") StringBuilder sb, @Param("sbValue") StringBuilder sbValue, @Param("model") Map map, @Param("cascade") boolean cascade,
                     @Param("dataPermissionFilter") String dataPermissionFilter);

    /**
     * 批量删除
     */
    void deleteBatch(@Param("tableName") String tableName, @Param("sbValue") StringBuilder sbValue, @Param("model") Map map, @Param("cascade") boolean cascade, @Param("dataPermissionFilter") String dataPermissionFilter);

    /**
     * 删除
     */
    void delete(@Param("tableName") String tableName, @Param("sbValue") StringBuilder sbValue, @Param("model") Map map, @Param("dataPermissionFilter") String dataPermissionFilter);

    /**
     * 详情
     */
    List<Map<String, Object>> list(@Param("tableName") String tableName, @Param("sb") StringBuilder sb, @Param("sbValue") String sbValue,
        @Param("orderByValue") String orderByValue, @Param("pageValue") String pageValue, @Param("model") Map map, @Param("dataPermissionFilter") String dataPermissionFilter);

    /**
     * 查询数量
     */
    int count(@Param("tableName") String tableName, @Param("sbValue") StringBuilder sbValue, @Param("model") Map map, @Param("dataPermissionFilter") String dataPermissionFilter);

}

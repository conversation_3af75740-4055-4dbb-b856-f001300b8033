package cn.com.sipsg.common.online.core.service.impl;

import cn.com.sipsg.common.datasource.core.service.DatasourceService;
import cn.com.sipsg.common.exception.enums.ErrorCodeEnum;
import cn.com.sipsg.common.mybatis.core.query.LambdaQueryWrapperX;
import cn.com.sipsg.common.online.core.constant.OnlineConstants;
import cn.com.sipsg.common.online.core.entity.OnlineDictItem;
import cn.com.sipsg.common.online.core.pojo.bo.OnlineDictItemSaveBO;
import cn.com.sipsg.common.online.core.pojo.vo.OnlineDictItemVO;
import cn.com.sipsg.common.online.core.service.OnlineDictItemService;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.util.AssertUtils;
import cn.com.sipsg.common.util.BeanUtils;
import cn.com.sipsg.common.online.core.entity.OnlineDict;
import cn.com.sipsg.common.online.core.mapper.OnlineDictMapper;
import cn.com.sipsg.common.online.core.pojo.bo.OnlineDictQueryBO;
import cn.com.sipsg.common.online.core.pojo.bo.OnlineDictSaveBO;
import cn.com.sipsg.common.online.core.pojo.vo.OnlineDictVO;
import cn.com.sipsg.common.online.core.service.OnlineDictService;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.db.meta.Column;
import cn.hutool.db.meta.Table;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * 字典管理服务实现类
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Service
@RequiredArgsConstructor
public class OnlineDictServiceImpl extends ServiceImpl<OnlineDictMapper, OnlineDict> implements OnlineDictService {

    private final DatasourceService datasourceService;

    private final OnlineDictItemService onlineDictItemService;

    @Override
    public CommonPageVO<OnlineDictVO> page(OnlineDictQueryBO bo) {
        LambdaQueryWrapperX<OnlineDict> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.likeIfPresent(OnlineDict::getDictName, bo.getDictName());
        queryWrapper.eqIfPresent(OnlineDict::getDictType, bo.getDictType());
        queryWrapper.eqIfPresent(OnlineDict::getTreeFlag, bo.getTreeFlag());
        // 填充查询条件
        return this.page(bo, queryWrapper).convert(entity -> BeanUtils.copyProperties(entity, OnlineDictVO.class));
    }

    @Override
    @Transactional
    public String save(OnlineDictSaveBO bo) {
        bo.setId(null);
        // 新增字典管理
        OnlineDict onlineDict = BeanUtils.copyProperties(bo, OnlineDict.class);
        this.save(onlineDict);
        //如果是自定义字典，则添加字典项
        if (StrUtil.equalsAny(onlineDict.getDictType(), OnlineConstants.DictType.CUSTOM_DICT, OnlineConstants.DictType.STATIC_DICT)) {
            // 新增字典项
            List<OnlineDictItemSaveBO> items = bo.getItems();
            items.forEach(item -> item.setOnlineDictId(onlineDict.getId()));
            //校验字典项内编码不能重复
            checkUniqueItemCode(items);
            onlineDictItemService.saveBatch(BeanUtils.copyToList(items, OnlineDictItem.class));
        }
        return onlineDict.getId();
    }

    @Override
    @Transactional
    public void update(OnlineDictSaveBO bo) {
        // 编辑字典管理
        OnlineDict onlineDict = BeanUtils.copyProperties(bo, OnlineDict.class);
        //如果是自定义字典，则添加字典项
        if (StrUtil.equalsAny(onlineDict.getDictType(), OnlineConstants.DictType.CUSTOM_DICT, OnlineConstants.DictType.STATIC_DICT)) {
            // 先删除之前的字典项
            onlineDictItemService.remove(new LambdaQueryWrapperX<OnlineDictItem>().eq(OnlineDictItem::getOnlineDictId, onlineDict.getId()));
            // 新增字典项
            List<OnlineDictItemSaveBO> items = bo.getItems();
            items.forEach(item -> {
                item.setOnlineDictId(onlineDict.getId());
                item.setItemId(null);
            });
            //校验字典项内编码不能重复
            checkUniqueItemCode(items);
            onlineDictItemService.saveBatch(BeanUtils.copyToList(items, OnlineDictItem.class));
        }
        this.updateById(onlineDict);
    }

    @Override
    public OnlineDictVO detail(String id) {
        OnlineDict onlineDict = this.getById(id);
        // 校验是否存在
        AssertUtils.isTrue(onlineDict == null, ErrorCodeEnum.DATA_NOT_EXIST);
        OnlineDictVO onlineDictVO = BeanUtils.copyProperties(onlineDict, OnlineDictVO.class);
        if (OnlineConstants.DictType.CUSTOM_DICT.equals(onlineDict.getDictType())) {
            // 查询字典项
            List<OnlineDictItem> items = onlineDictItemService.list(new LambdaQueryWrapperX<OnlineDictItem>().eq(OnlineDictItem::getOnlineDictId, id));
            onlineDictVO.setItems(BeanUtils.copyToList(items, OnlineDictItemVO.class));
        }
        return onlineDictVO;
    }

    @Override
    @Transactional
    public void delete(String id) {
        OnlineDict onlineDict = this.getById(id);
        if (onlineDict != null) {
            // 删除字典管理
            this.removeById(id);
            //删除字典项
            onlineDictItemService.remove(new LambdaQueryWrapperX<OnlineDictItem>().eq(OnlineDictItem::getOnlineDictId, id));
        }
    }

    @Override
    public List<Map<String, String>> getColumns(String id, Boolean isAll) {
        OnlineDict onlineDict = this.getById(id);
        List<Map<String, String>> paramsList = new ArrayList<>();
        String tableName = onlineDict.getTableName();
        AssertUtils.isTrue(tableName == null, "请确认是否配置表名");
        //获取表名下所有字段
        Table table = datasourceService.getTable(onlineDict.getDbId(), tableName);
        String dictDataJson = onlineDict.getDictDataJson();
        if (JSONUtil.isTypeJSON(dictDataJson)) {
            JSONObject parse = JSONObject.parse(dictDataJson);
            List<String> list = parse.getList("paramsList", String.class);
            if (CollUtil.isNotEmpty(list)) {
                for (String s : list) {
                    Column column = table.getColumn(s);
                    if (column != null) {
                        Map<String, String> map = new HashMap<>();
                        map.put("name", column.getName());
                        map.put("comment", column.getComment());
                        paramsList.add(map);
                    }
                }
            }
        }
        if (CollUtil.isEmpty(paramsList) || BooleanUtil.isTrue(isAll)) {
            paramsList = new ArrayList<>();
            Collection<Column> columns = table.getColumns();
            for (Column column : columns) {
                Map<String, String> map = new HashMap<>();
                map.put("name", column.getName());
                map.put("comment", column.getComment());
                paramsList.add(map);
            }
        }
        return paramsList;
    }

    private void checkUniqueItemCode(List<OnlineDictItemSaveBO> items) {
        List<String> codes = new ArrayList<>();
        for (OnlineDictItemSaveBO item : items) {
            AssertUtils.isTrue(codes.contains(item.getItemCode()), "字典项编码不能重复");
            codes.add(item.getItemCode());
        }
    }
}
package cn.com.sipsg.common.online.core.pojo.vo;

import cn.com.sipsg.common.online.core.entity.OnlineDataModel;
import cn.com.sipsg.common.online.core.enums.ColumnTransTypeEnum;
import cn.com.sipsg.common.online.core.enums.ColumnTypeEnum;
import cn.com.sipsg.common.online.core.enums.DataPermissionTypeEnum;
import cn.com.sipsg.common.online.core.enums.OnlineDataModelTypeEnum;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;

/**
 * 动态表单-模型字段VO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@FieldNameConstants
@Schema(description = "动态表单-模型字段VO")
public class OnlineColumnVO implements VO {

    /**
     * 主键
     */
    @TableId("id")
    @Schema(description = "主键")
    private String id;

    /**
     * 主页面id
     */
    @Schema(description = "主页面id")
    private String mainId;

    /**
     * 数据模型id
     */
    @Schema(description = "数据模型id")
    @Trans(type = TransType.SIMPLE, target = OnlineDataModel.class, fields = OnlineDataModel.Fields.modelCode, ref = Fields.modelCode)
    private String modelId;

    /**
     * 数据表名称
     */
    @Schema(description = "数据表名称")
    private String tableName;

    /**
     * 字段名
     */
    @Schema(description = "字段名")
    private String columnName;

    /**
     * 字段类型
     */
    @Schema(description = "字段类型 string，int，float，datetime，boolean，geometry，json")
    private String fieldType;

    /**
     * 检索条件
     */
    @Schema(description = "检索条件 disable，equal，like，between，range")
    private String searchCondition;

    /**
     * 字段标签名
     */
    @Schema(description = "字段标签名")
    private String columnLabel;

    /**
     * 字段别名
     */
    @Schema(description = "字段别名")
    private String columnProperty;

    /**
     * 字段配置
     */
    @Schema(description = "字段配置")
    private String configJson;

    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer sort;

    /**
     * 是否为空
     */
    @Schema(description = "是否为空")
    private Boolean nullable;

    /**
     * 字段长度
     */
    @Schema(description = "字段长度")
    private Integer fieldLength;

    /**
     * 模型标识
     */
    @Schema(description = "模型标识")
    private String modelCode;

    /**
     * 数据表类型
     */
    @Schema(description = "数据表类型")
    private OnlineDataModelTypeEnum tableType;

    /**
     * 数据库类型对应java类型
     */
    @Schema(description = "数据库类型对应java类型")
    private String javaType;

    /**
     * 是否主键
     */
    @Schema(description = "是否主键")
    private Boolean pk;

    /**
     * 主键类型 uuid,auto
     */
    @Schema(description = "主键类型 uuid,auto")
    private String pkType;

    /**
     * 字段类型配置 createId/createName/createTime/deptId/deptName/updateTime/logicDelete
     */
    @Schema(description = "字段类型配置")
    private ColumnTypeEnum columnType;

    /**
     * 字段配置
     */
    @Schema(description = "字段配置")
    private String columnConfig;

    /**
     * 转换类型 dict/serverCode/dept/user
     */
    @Schema(description = "转换类型")
    private ColumnTransTypeEnum transType;

    /**
     * 转换编码
     */
    @Schema(description = "转换编码")
    private String transCode;

    /**
     * 是否字典树
     */
    @Schema(description = "是否字典树")
    private Boolean transTree;

    /**
     * 数据权限类型
     */
    @Schema(description = "数据权限类型")
    private DataPermissionTypeEnum dataPermissionType;

    /**
     * 自定义自动填充值
     */
    @Schema(description = "自定义自动填充值")
    private String autoFillValue;

}
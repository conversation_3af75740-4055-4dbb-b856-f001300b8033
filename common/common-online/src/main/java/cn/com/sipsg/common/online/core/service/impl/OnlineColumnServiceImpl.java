package cn.com.sipsg.common.online.core.service.impl;

import cn.com.sipsg.common.mybatis.core.query.LambdaQueryWrapperX;
import cn.com.sipsg.common.online.core.constant.PkConstants;
import cn.com.sipsg.common.online.core.convert.OnlineColumnConvert;
import cn.com.sipsg.common.online.core.entity.OnlineColumn;
import cn.com.sipsg.common.online.core.mapper.OnlineColumnMapper;
import cn.com.sipsg.common.online.core.pojo.bo.OnlineColumnQueryBO;
import cn.com.sipsg.common.online.core.pojo.bo.OnlineColumnSaveBO;
import cn.com.sipsg.common.online.core.pojo.vo.OnlineColumnVO;
import cn.com.sipsg.common.online.core.service.OnlineColumnService;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.util.CollectionUtils;
import cn.hutool.core.util.BooleanUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 动态表单-模型字段 服务实现类
 *
 * <AUTHOR>
 * @since 2025/04/14
*/
@Service
public class OnlineColumnServiceImpl extends ServiceImpl<OnlineColumnMapper, OnlineColumn> implements OnlineColumnService {

    /**
     * 分页查询动态表单-模型字段
     *
     * @param bo   查询条件
     * @return 分页信息
     */
    @Override
    public CommonPageVO<OnlineColumnVO> page(OnlineColumnQueryBO bo) {
        LambdaQueryWrapperX<OnlineColumn> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eqIfPresent(OnlineColumn::getMainId, bo.getMainId())
                .inIfPresent(OnlineColumn::getModelId, bo.getModelIds())
                .eqIfPresent(OnlineColumn::getTableName, bo.getTableName())
                .eqIfPresent(OnlineColumn::getModelId, bo.getModelId());
        return this.page(bo, queryWrapper).convert(OnlineColumnConvert.INSTANCE::convert);
    }

    /**
     * 获取动态表单-模型字段详情
     *
     * @param id id
     * @return 详情
     */
    @Override
    public OnlineColumnVO detail(String id) {
        OnlineColumn entity = this.checkExist(id);
        return OnlineColumnConvert.INSTANCE.convert(entity);
    }

    /**
     * 新增动态表单-模型字段
     *
     * @param bo 新增对象
     * @return 成功或失败
     */
    @Override
    @Transactional
    public String save(OnlineColumnSaveBO bo) {
        bo.setId(null);
        //如果是主键 先默认设置uuid
        if (BooleanUtil.isTrue(bo.getPk())) {
            bo.setPkType(PkConstants.UUID);
        }
        OnlineColumn entity = OnlineColumnConvert.INSTANCE.convert(bo);
        this.save(entity);
        return entity.getId();
    }

    /**
     * 编辑动态表单-模型字段
     *
     * @param bo 编辑对象
     */
    @Override
    @Transactional
    public void update(OnlineColumnSaveBO bo) {
        //如果是主键 先默认设置uuid
        if (BooleanUtil.isTrue(bo.getPk())) {
            bo.setPkType(PkConstants.UUID);
        }
        OnlineColumn entity = OnlineColumnConvert.INSTANCE.convert(bo);
        this.updateById(entity);
    }

    /**
     * 删除动态表单-模型字段
     *
     * @param id id
     */
    @Override
    @Transactional
    public void delete(String id) {
        this.removeById(id);
    }

    @Override
    public OnlineColumn getPrimaryKeyColumn(List<OnlineColumn> columnList) {
        return CollectionUtils.findFirst(columnList, OnlineColumn::getPk);
    }

}
package cn.com.sipsg.common.online.core.controller;

import cn.com.sipsg.common.constant.PageConstants;
import cn.com.sipsg.common.online.core.pojo.bo.OnlineSubFormInitBO;
import cn.com.sipsg.common.online.core.pojo.bo.OnlineSubFormQueryBO;
import cn.com.sipsg.common.online.core.pojo.bo.OnlineSubFormSaveBO;
import cn.com.sipsg.common.online.core.pojo.vo.OnlineSubFormVO;
import cn.com.sipsg.common.online.core.service.OnlineSubFormService;
import cn.com.sipsg.common.pojo.CommonResult;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.validation.AddValidated;
import cn.com.sipsg.common.validation.UpdateValidated;
import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 动态表单-子表单 前端控制器
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Tag(name = "动态表单-子表单管理")
@RestController
@RequiredArgsConstructor
@RequestMapping("/online/subForm")
public class OnlineSubFormController {

    private final OnlineSubFormService onlineSubFormService;

    /**
     * 查询动态表单-子表单分页列表
     *
     * @param bo 参数
     * @return 动态表单-子表单分页列表
     */
    @Operation(summary = "查询动态表单-子表单分页列表")
    @SaCheckPermission(value = "online:main:all")
    @PostMapping("/page")
    public CommonResult<CommonPageVO<OnlineSubFormVO>> page(@Validated @RequestBody OnlineSubFormQueryBO bo) {
        return CommonResult.data(onlineSubFormService.page(bo));
    }

    /**
     * 查询动态表单-子表单列表
     *
     * @param bo 参数
     * @return 动态表单-子表单列表
     */
    @Operation(summary = "查询动态表单-子表单列表")
    @PostMapping("/list")
    public CommonResult<List<OnlineSubFormVO>> list(@Validated @RequestBody OnlineSubFormQueryBO bo) {
        bo.setSize(PageConstants.SIZE_NONE);
        return CommonResult.data(onlineSubFormService.page(bo).getRecords());
    }

    /**
     * 新增动态表单-子表单
     *
     * @param bo 参数
     * @return 动态表单-子表单ID
     */
    @Operation(summary = "新增动态表单-子表单")
    @SaCheckPermission(value = "online:main:all")
    @PostMapping("/save")
    public CommonResult<String> save(@AddValidated @RequestBody OnlineSubFormSaveBO bo) {
        return CommonResult.data(onlineSubFormService.save(bo));
    }

    /**
     * 编辑动态表单-子表单
     *
     * @param bo 参数
     */
    @Operation(summary = "编辑动态表单-子表单")
    @SaCheckPermission(value = "online:main:all")
    @PostMapping("/update")
    public CommonResult<Void> update(@UpdateValidated @RequestBody OnlineSubFormSaveBO bo) {
        onlineSubFormService.update(bo);
        return CommonResult.success();
    }

    /**
     * 查询动态表单-子表单详情
     *
     * @param id 主键
     */
    @Operation(summary = "查询动态表单-子表单详情")
    @Parameter(name = "id", description = "主键", required = true, example = "1111")
    @SaCheckPermission(value = "online:main:all")
    @GetMapping("/detail")
    public CommonResult<OnlineSubFormVO> detail(@RequestParam String id) {
        return CommonResult.data(onlineSubFormService.detail(id));
    }

    /**
     * 查询动态表单-表单渲染
     *
     * @param id 主键
     */
    @Operation(summary = "查询动态表单-表单渲染")
    @Parameter(name = "id", description = "主键", required = true, example = "1111")
    @GetMapping("/render")
    public CommonResult<OnlineSubFormVO> render(@RequestParam String id) {
        return CommonResult.data(onlineSubFormService.render(id));
    }

    /**
     * 删除动态表单-子表单
     *
     * @param id 主键
     */
    @Operation(summary = "删除动态表单-子表单")
    @Parameter(name = "id", description = "主键", required = true, example = "1111")
    @SaCheckPermission(value = "online:main:all")
    @PostMapping("/delete")
    public CommonResult<Void> delete(@RequestParam String id) {
        onlineSubFormService.delete(id);
        return CommonResult.success();
    }

    /**
     * 动态表单-单表初始化
     *
     * @param bo 参数
     * @return 动态表单-子表单ID
     */
    @Operation(summary = "动态表单-单表初始化")
    @SaCheckPermission(value = "online:main:all")
    @PostMapping("/initSingleTable")
    public CommonResult<String> initSingleTable(@AddValidated @RequestBody OnlineSubFormInitBO bo) {
        onlineSubFormService.initSingleTable(bo);
        return CommonResult.success();
    }

}
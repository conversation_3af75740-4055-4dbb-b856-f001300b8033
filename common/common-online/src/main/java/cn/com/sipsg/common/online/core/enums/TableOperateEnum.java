package cn.com.sipsg.common.online.core.enums;

import cn.com.sipsg.common.enums.BaseStrEnum;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldNameConstants;

/**
 * 表格操作枚举
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@FieldNameConstants
@RequiredArgsConstructor
public enum TableOperateEnum implements BaseStrEnum {

    CREATE("create", "查询"),

    BATCHDELETE("batchDelete", "批量删除"),

    EXPORT("export", "导出"),

    IMPORT("import", "导入"),

    VIEW("view", "查看"),

    EDIT("edit", "编辑"),

    DELETE("delete", "删除"),;

    private final String code;

    private final String desc;
}

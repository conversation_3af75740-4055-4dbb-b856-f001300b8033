package cn.com.sipsg.common.online.core.convert;

import cn.com.sipsg.common.online.core.entity.OnlineMain;
import cn.com.sipsg.common.online.core.pojo.bo.OnlineMainSaveBO;
import cn.com.sipsg.common.online.core.pojo.vo.OnlineMainVO;
import cn.com.sipsg.common.online.core.enums.OnlineMainFormTypeEnum;
import cn.com.sipsg.common.util.EnumUtils;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 动态表单-主页面信息转换器
*
* <AUTHOR>
* @since 2025/04/14
*/
@Mapper
public interface OnlineMainConvert {

    OnlineMainConvert INSTANCE = Mappers.getMapper(OnlineMainConvert.class);

    /**
     * entity转vo
     *
     * @param entity 动态表单-主页面信息实体
     * @return vo
     */
    OnlineMainVO convert(OnlineMain entity);

    /**
     * saveBO转entity
     *
     * @param bo 动态表单-主页面信息SaveBO
     * @return 动态表单-主页面信息实体
     */
    OnlineMain convert(OnlineMainSaveBO bo);

    default OnlineMainFormTypeEnum formType2Enum(Integer formType) {
        return EnumUtils.getEnumByValue(OnlineMainFormTypeEnum.class, formType);
    }

}
package cn.com.sipsg.common.online.core.entity;

import cn.com.sipsg.common.mybatis.core.dataobject.BaseDO;
import cn.com.sipsg.common.mybatis.core.type.StringListTypeHandler;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.github.yulichang.annotation.EntityMapping;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;

import java.util.List;

/**
 * 动态表单-子表单
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@FieldNameConstants
@TableName(value = "medox_online_sub_form", autoResultMap = true)
public class OnlineSubForm extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private String id;

    /**
     * 主页面id
     */
    private String mainId;

    /**
     * 子表单编码
     */
    private String code;

    /**
     * 子表单名称
     */
    private String name;

    /**
     * 子表单类型（0-查询，1-编辑，2-一对一，3-一对多，4-左数右表）
     */
    private Integer type;

    /**
     * 子表单配置
     */
    private String propertyJson;

    /**
     * 子表单内容
     */
    private String widgetJson;

    /**
     * 数据模型id列表
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> modelIds;

    /**
     * 数据模型列表
     */
    @TableField(exist = false)
    private List<OnlineDataModel> dataModelList;

    /**
     * 表单字段列表
     */
    @TableField(exist = false)
    @EntityMapping(thisField = Fields.id, joinField = OnlineColumnForm.Fields.formId)
    private List<OnlineColumnForm> columnFormList;

}

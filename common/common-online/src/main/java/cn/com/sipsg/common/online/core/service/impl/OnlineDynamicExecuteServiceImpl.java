package cn.com.sipsg.common.online.core.service.impl;

import cn.com.sipsg.common.constant.PageConstants;
import cn.com.sipsg.common.exception.enums.ErrorCodeEnum;
import cn.com.sipsg.common.online.core.mapper.OnlineCrudMapper;
import cn.com.sipsg.common.online.core.service.OnlineDynamicExecuteService;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.util.AssertUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.toolkit.sql.SqlInjectionUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 动态执行服务实现类
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Service
@RequiredArgsConstructor
public class OnlineDynamicExecuteServiceImpl implements OnlineDynamicExecuteService {

    private final OnlineCrudMapper onlineCrudMapper;

    @DS("#poolName")
    @Override
    public int count(String tableName, StringBuilder sbValue, Map map, String dataPermissionFilter, String poolName) {
        return onlineCrudMapper.count(tableName, sbValue, map, dataPermissionFilter);
    }

    @DS("#poolName")
    @Override
    public CommonPageVO<Map<String, Object>> page(String tableName, StringBuilder sb, StringBuilder sbValue, StringBuilder orderByValue, StringBuilder pageValue,
        Map map, Map<String, Object> pageInfo, String dataPermissionFilter, String poolName) {
        //sql注入参数校验
        boolean invalid = SqlInjectionUtils.check(orderByValue.toString());
        AssertUtils.isTrue(invalid, ErrorCodeEnum.ILLEGAL_ARGUMENT);
        List<Map<String, Object>> pageList = onlineCrudMapper.list(tableName, sb, sbValue.toString(), orderByValue.toString().trim(), pageValue.toString().trim(), map, dataPermissionFilter);
        return CommonPageVO.<Map<String, Object>>builder()
                .current(MapUtil.getLong(pageInfo, PageConstants.CURRENT))
                .size(MapUtil.getLong(pageInfo, PageConstants.SIZE))
                .total(MapUtil.getLong(pageInfo, PageConstants.TOTAL))
                .records(pageList)
                .build();
    }

    @DS("#poolName")
    @Override
    public List<Map<String, Object>> list(String tableName, StringBuilder sb, StringBuilder sbValue, StringBuilder orderByValue, StringBuilder pageValue,
        Map map, String dataPermissionFilter, String poolName) {
        //sql注入参数校验
        boolean invalid = SqlInjectionUtils.check(orderByValue.toString()) || SqlInjectionUtils.check(pageValue.toString());
        AssertUtils.isTrue(invalid, ErrorCodeEnum.ILLEGAL_ARGUMENT);
        return onlineCrudMapper.list(tableName, sb, sbValue.toString(), orderByValue.toString().trim(), pageValue.toString().trim(), map, dataPermissionFilter);
    }

    @DS("#poolName")
    @Override
    public Map<String, Object> get(String tableName, StringBuilder sb, StringBuilder sbValue, Map map, String dataPermissionFilter, String poolName) {
        List<Map<String, Object>> mapList = onlineCrudMapper.list(tableName, sb, sbValue.toString(), null, null, map, dataPermissionFilter);
        if (CollUtil.isNotEmpty(mapList)) {
            return mapList.get(0);
        }
        return null;
    }

    @DS("#poolName")
    @Override
    public void remove(String tableName, StringBuilder sbValue, Map map, String dataPermissionFilter, String poolName) {
        this.onlineCrudMapper.delete(tableName, sbValue, map, dataPermissionFilter);
    }

    @DS("#poolName")
    @Override
    public void removeBatch(String tableName, StringBuilder sbValue, Map map, boolean relation, String dataPermissionFilter, String poolName) {
        this.onlineCrudMapper.deleteBatch(tableName, sbValue, map, relation, dataPermissionFilter);
    }

    @DS("#poolName")
    @Override
    public void save(String tableName, StringBuilder sb, StringBuilder sbValue, Map map, String poolName) {
        this.onlineCrudMapper.insert(tableName, sb, sbValue, map);
    }

    @DS("#poolName")
    @Override
    public void saveBatch(String tableName, StringBuilder sb, StringBuilder sbValue, List<HashMap> maps, String poolName) {
        this.onlineCrudMapper.insertBatch(tableName, sb, sbValue, maps);
    }

    @DS("#poolName")
    @Override
    public void update(String tableName, StringBuilder sb, StringBuilder sbValue, Map map, String dataPermissionFilter, String poolName) {
        this.onlineCrudMapper.update(tableName, sb, sbValue, map, dataPermissionFilter);
    }

    @DS("#poolName")
    @Override
    public void updateBatch(String tableName, StringBuilder sb, StringBuilder sbValue, Map map, boolean relation, String dataPermissionFilter, String poolName) {
        this.onlineCrudMapper.updateBatch(tableName, sb, sbValue, map, relation, dataPermissionFilter);
    }

}

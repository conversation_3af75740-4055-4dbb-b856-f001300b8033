<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.sipsg.common.online.core.mapper.OnlineCrudMapper">

    <insert id="insertBatch">
        INSERT INTO ${tableName}
        ${sb}
        VALUES
        <foreach collection="models" item="model" separator=",">
            ${sbValue}
        </foreach>
    </insert>

    <insert id="insert">
        INSERT INTO ${tableName}
        ${sb}
        VALUES
        ${sbValue}
    </insert>

    <update id = "update">
        UPDATE ${tableName}
        SET ${sb}
        WHERE
        ${sbValue}
        <if test="dataPermissionFilter != null">
            ${dataPermissionFilter}
        </if>
    </update>

    <update id="updateBatch">
        UPDATE ${tableName}
        SET ${sb}
        WHERE
        ${sbValue} IN (
        <choose>
            <when test="cascade != null and cascade">
                <foreach collection="model.ids" item="id" separator="," close=")">
                    #{id}
                </foreach>
            </when>
            <otherwise>
                <foreach collection="model.ids" item="id" separator=",">
                    #{id}
                </foreach>
            </otherwise>
        </choose>
        )
        <if test="dataPermissionFilter != null">
            ${dataPermissionFilter}
        </if>
    </update>

    <delete id = "deleteBatch">
        DELETE FROM ${tableName}
        WHERE
        ${sbValue} IN (
        <choose>
            <when test="cascade != null and cascade">
                <foreach collection="model.ids" item="id" separator="," close=")">
                    #{id}
                </foreach>
            </when>
            <otherwise>
                <foreach collection="model.ids" item="id" separator=",">
                    #{id}
                </foreach>
            </otherwise>
        </choose>
        )
        <if test="dataPermissionFilter != null">
            ${dataPermissionFilter}
        </if>
    </delete>

    <delete id = "delete">
        DELETE FROM ${tableName}
        WHERE
        ${sbValue}
        <if test="dataPermissionFilter != null">
            ${dataPermissionFilter}
        </if>
    </delete>

    <select id="list" resultType="java.util.Map">
        SELECT ${sb}
        FROM ${tableName}
        WHERE ${sbValue}
        <if test="dataPermissionFilter != null">
            ${dataPermissionFilter}
        </if>
        <if test="orderByValue != null and orderByValue != '' ">
            ORDER BY ${orderByValue}
        </if>
        <if test="pageValue != null and pageValue != '' ">
            ${pageValue}
        </if>
    </select>

    <select id="count" resultType="int">
        SELECT COUNT(1)
        FROM ${tableName}
        WHERE ${sbValue}
        <if test="dataPermissionFilter != null">
            ${dataPermissionFilter}
        </if>
    </select>
</mapper>

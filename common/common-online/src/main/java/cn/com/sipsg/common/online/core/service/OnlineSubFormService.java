package cn.com.sipsg.common.online.core.service;

import cn.com.sipsg.common.mybatis.core.service.BaseServiceX;
import cn.com.sipsg.common.online.core.entity.OnlineSubForm;
import cn.com.sipsg.common.online.core.pojo.bo.OnlineCrudOneToManySaveBO;
import cn.com.sipsg.common.online.core.pojo.bo.OnlineCrudPageBO;
import cn.com.sipsg.common.online.core.pojo.bo.OnlineSubFormInitBO;
import cn.com.sipsg.common.online.core.pojo.bo.OnlineSubFormQueryBO;
import cn.com.sipsg.common.online.core.pojo.bo.OnlineSubFormSaveBO;
import cn.com.sipsg.common.online.core.pojo.dto.OnlineCrudFormDTO;
import cn.com.sipsg.common.online.core.pojo.vo.OnlineSubFormVO;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 动态表单-子表单 服务类
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public interface OnlineSubFormService extends BaseServiceX<OnlineSubForm> {

    /**
     * 查询动态表单-子表单分页列表
     *
     * @param bo 参数
     * @return 动态表单-子表单分页列表
     */
    CommonPageVO<OnlineSubFormVO> page(OnlineSubFormQueryBO bo);

    /**
     * 新增动态表单-子表单
     *
     * @param bo 参数
     * @return 动态表单-子表单ID
     */
    String save(OnlineSubFormSaveBO bo);

    /**
     * 编辑动态表单-子表单
     *
     * @param bo 参数
     */
    void update(OnlineSubFormSaveBO bo);

    /**
     * 根据id查询动态表单-子表单详情
     *
     * @param id 主键
     * @return 动态表单-子表单详情
     */
    OnlineSubFormVO detail(String id);

    /**
     * 删除动态表单-子表单
     *
     * @param id 主键
     */
    void delete(String id);

    /**
     * 查询子表单下所有模型和字段
     *
     * @param id 主键
     * @return 模型和字段
     */
    OnlineCrudFormDTO configList(String id);

    /**
     * 获取主表和子表的各自主键
     *
     * @param id 主键
     * @return 主表和子表主键
     */
    Map getPrimaryKey(String id);

    /**
     * 根据主页面ID列表获取表单列表
     *
     * @param mainIds 主页面ID列表
     * @return 表单列表
     */
    List<OnlineSubForm> getFormListByMainIds(Set<String> mainIds);

    /**
     * 替换子表单中的控件json
     *
     * @param onlineCrudFormDTO 表单信息
     * @param bo 保存信息
     *
     */
    void replaceWidgetJson(OnlineCrudFormDTO onlineCrudFormDTO, OnlineCrudOneToManySaveBO bo);

    /**
     * 隐藏排序字段
     *
     * @param onlineCrudFormDTO 表单信息
     * @param bo 页面信息
     */
    void hiddenSort(OnlineCrudFormDTO onlineCrudFormDTO, OnlineCrudPageBO bo);

    /**
     * 动态表单-单表初始化
     *
     * @param bo 参数
     */
    void initSingleTable(OnlineSubFormInitBO bo);

    /**
     * 查询动态表单-子表单详情（无接口权限）
     *
     * @param id 主键
     * @return 动态表单-子表单详情
     */
    OnlineSubFormVO render(String id);
}
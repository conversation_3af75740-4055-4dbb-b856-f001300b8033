package cn.com.sipsg.common.online.core.service.impl;

import cn.com.sipsg.common.exception.enums.ErrorCodeEnum;
import cn.com.sipsg.common.mybatis.core.query.LambdaQueryWrapperX;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.util.AssertUtils;
import cn.com.sipsg.common.util.BeanUtils;
import cn.com.sipsg.common.online.core.entity.OnlineDictItem;
import cn.com.sipsg.common.online.core.mapper.OnlineDictItemMapper;
import cn.com.sipsg.common.online.core.pojo.bo.OnlineDictItemQueryBO;
import cn.com.sipsg.common.online.core.pojo.bo.OnlineDictItemSaveBO;
import cn.com.sipsg.common.online.core.pojo.vo.OnlineDictItemVO;
import cn.com.sipsg.common.online.core.service.OnlineDictItemService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 动态表单-自定义字典项服务实现类
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Service
@RequiredArgsConstructor
public class OnlineDictItemServiceImpl extends ServiceImpl<OnlineDictItemMapper, OnlineDictItem> implements OnlineDictItemService {

    @Override
    public CommonPageVO<OnlineDictItemVO> page(OnlineDictItemQueryBO bo) {
        LambdaQueryWrapperX<OnlineDictItem> queryWrapper = new LambdaQueryWrapperX<>();
        // 填充查询条件
        queryWrapper.eqIfPresent(OnlineDictItem::getOnlineDictId, bo.getOnlineDictId());
        return this.page(bo, queryWrapper).convert(entity -> BeanUtils.copyProperties(entity, OnlineDictItemVO.class));
    }

    @Override
    @Transactional
    public String save(OnlineDictItemSaveBO bo) {
        bo.setItemId(null);
        // 新增动态表单-自定义字典项
        OnlineDictItem onlineDictItem = BeanUtils.copyProperties(bo, OnlineDictItem.class);
        this.save(onlineDictItem);
        return onlineDictItem.getItemId();
    }

    @Override
    @Transactional
    public void update(OnlineDictItemSaveBO bo) {
        // 编辑动态表单-自定义字典项
        OnlineDictItem onlineDictItem = BeanUtils.copyProperties(bo, OnlineDictItem.class);
        this.updateById(onlineDictItem);
    }

    @Override
    public OnlineDictItemVO detail(String itemId) {
        OnlineDictItem onlineDictItem = this.getById(itemId);
        // 校验是否存在
        AssertUtils.isTrue(onlineDictItem == null, ErrorCodeEnum.DATA_NOT_EXIST);
        return BeanUtils.copyProperties(onlineDictItem, OnlineDictItemVO.class);
    }

    @Override
    @Transactional
    public void delete(String itemId) {
        OnlineDictItem onlineDictItem = this.getById(itemId);
        if (onlineDictItem != null) {
            // 删除动态表单-自定义字典项
            this.removeById(itemId);
        }
    }

}
package cn.com.sipsg.common.online.core.enums;

import cn.com.sipsg.common.enums.BaseEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 子表单模型类型
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@RequiredArgsConstructor
public enum OnlineSubFormTypeEnum implements BaseEnum {

    SEARCH(0, "查询"),

    EDIT(1, "编辑"),

    ONE(2, "一对一详情表单"),

    MANY(3, "一对多详情表单"),

    WORK_FORM(4, "工单列表"),

    ACTIVE_FLOW(5, "流程表单"),

    SEARCH_TREE(6, "左树右表");

    @EnumValue
    @JsonValue
    private final Integer code;

    private final String desc;

}

package cn.com.sipsg.common.online.core.pojo.bo;

import cn.com.sipsg.common.validation.group.UpdateGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 动态表单-自定义字典项保存BO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "动态表单-自定义字典项保存BO")
public class OnlineDictItemSaveBO {

    /**
     * 主键
     */
    @Schema(description = "主键")
    @NotBlank(message = "主键不能为空", groups = {UpdateGroup.class})
    private String itemId;

    /**
     * 动态字典id
     */
    @Schema(description = "动态字典id", requiredMode = Schema.RequiredMode.REQUIRED)
    private String onlineDictId;

    /**
     * 字典项名称
     */
    @Schema(description = "字典项名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "字典项名称不能为空")
    private String itemName;

    /**
     * 字典项编码
     */
    @Schema(description = "字典项编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "字典项编码不能为空")
    private String itemCode;

    /**
     * 排序值
     */
    @Schema(description = "排序值", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer sort;

    /**
     * 状态
     */
    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "状态不能为空")
    private Integer status;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

}
package cn.com.sipsg.common.online.core.pojo.bo;

import cn.com.sipsg.common.pojo.bo.SortablePageBO;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;

/**
 * 动态表单-自定义字典项查询BO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "动态表单-自定义字典项查询BO")
public class OnlineDictItemQueryBO extends SortablePageBO {

    /**
     * 动态字典id
     */
    @Schema(description = "动态字典id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "动态字典id不能为空")
    private String onlineDictId;

}
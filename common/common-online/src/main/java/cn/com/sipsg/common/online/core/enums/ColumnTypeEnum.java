package cn.com.sipsg.common.online.core.enums;

import cn.com.sipsg.common.enums.BaseStrEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldNameConstants;

/**
 * 字段类型配置枚举
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@FieldNameConstants
@RequiredArgsConstructor
public enum ColumnTypeEnum implements BaseStrEnum {

    CREATE_ID("createId", "创建人ID"),

    CREATE_NAME("createName", "创建人姓名"),

    UPDATE_ID("updateId", "更新人ID"),

    UPDATE_NAME("updateName", "更新人姓名"),

    CREATE_TIME("createTime", "创建时间"),

    UPDATE_TIME("updateTime", "更新时间"),

    LOGIC_DELETE("logicDelete", "删除标识"),

    MULTIPLE_SELECT("multipleSelect", "多选字段"),

    AUTO_FILL("autoFill", "自动填充"),;

    @EnumValue
    @JsonValue
    private final String code;

    private final String desc;

}

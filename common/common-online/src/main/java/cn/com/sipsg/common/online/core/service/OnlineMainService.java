package cn.com.sipsg.common.online.core.service;

import cn.com.sipsg.common.mybatis.core.service.BaseServiceX;
import cn.com.sipsg.common.online.core.entity.OnlineMain;
import cn.com.sipsg.common.online.core.pojo.bo.OnlineMainQueryBO;
import cn.com.sipsg.common.online.core.pojo.bo.OnlineMainSaveBO;
import cn.com.sipsg.common.online.core.pojo.vo.OnlineMainSimpleVO;
import cn.com.sipsg.common.online.core.pojo.vo.OnlineMainVO;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;

import java.util.List;

/**
 * 动态表单-主页面信息 服务类
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public interface OnlineMainService extends BaseServiceX<OnlineMain> {

    /**
     * 分页查询动态表单-主页面信息
     *
     * @param bo 参数
     * @return 分页信息
     */
    CommonPageVO<OnlineMainVO> page(OnlineMainQueryBO bo);

    /**
     * 获取动态表单-主页面信息详情
     *
     * @param id id
     * @return 详情
     */
    OnlineMainVO detail(String id);

    /**
     * 新增动态表单-主页面信息
     *
     * @param bo 参数
     * @return 主页面ID
     */
    OnlineMain save(OnlineMainSaveBO bo);

    /**
     * 编辑动态表单-主页面信息
     *
     * @param bo 参数
     */
    void update(OnlineMainSaveBO bo);

    /**
     * 删除动态表单-主页面信息
     *
     * @param id id
     */
    void delete(String id);

    /**
     * 更新状态
     *
     * @param id 主键
     * @param ifEnable 是否发布
     */
    void updateStatus(String id, Boolean ifEnable);

    /**
     * 查询已发布主页面及其关联查询表单列表
     *
     * @param id id
     * @return 主页面及其关联查询表单列表
     */
    List<OnlineMainSimpleVO> listEnableWithQueryForm(String id);

}
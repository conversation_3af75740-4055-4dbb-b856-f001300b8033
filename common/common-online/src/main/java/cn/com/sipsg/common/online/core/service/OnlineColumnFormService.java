package cn.com.sipsg.common.online.core.service;


import cn.com.sipsg.common.mybatis.core.service.BaseServiceX;
import cn.com.sipsg.common.online.core.entity.OnlineColumnForm;
import cn.com.sipsg.common.online.core.pojo.bo.OnlineColumnFormDeleteBO;
import cn.com.sipsg.common.online.core.pojo.bo.OnlineColumnFormQueryBO;
import cn.com.sipsg.common.online.core.pojo.bo.OnlineColumnFormSaveBO;
import cn.com.sipsg.common.online.core.pojo.vo.OnlineColumnFormVO;

import java.util.List;

/**
 * 动态表单-子表单服务接口
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public interface OnlineColumnFormService extends BaseServiceX<OnlineColumnForm> {

    /**
     * 查询动态表单-子表单(关联字段)列表
     *
     * @param bo 参数
     * @return 动态表单-子表单列表
     */
    List<OnlineColumnFormVO> list(OnlineColumnFormQueryBO bo);

    /**
     * 新增动态表单-子表单(关联字段)
     *
     * @param bo 参数
     * @return 动态表单-子表单ID
     */
    String save(OnlineColumnFormSaveBO bo);

    /**
     * 编辑动态表单-子表单(关联字段)
     *
     * @param bo 参数
     */
    void update(OnlineColumnFormSaveBO bo);

    /**
     * 根据id查询动态表单-子表单(关联字段)详情
     *
     * @param id 主键
     * @return 动态表单-子表单(关联字段)详情
     */
    OnlineColumnFormVO detail(String id);

    /**
     * 删除动态表单-子表单(关联字段)
     *
     * @param id 主键
     */
    void delete(String id);

    /**
     * 批量新增动态表单-子表单(关联字段)
     *
     * @param list 参数
     */
    void batchUpdate(List<OnlineColumnFormSaveBO> list);

    /**
     * 批量新增动态表单-子表单(关联字段)
     *
     * @param list 参数
     */
    void batchSave(List<OnlineColumnFormSaveBO> list);

    /**
     * 根据关联字段删除动态表单-子表单(关联字段)
     *
     * @param bo 参数
     */
    void deleteByRelate(OnlineColumnFormDeleteBO bo);
}
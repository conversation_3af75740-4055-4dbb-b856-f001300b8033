<#assign lowerCamelCaseClassName = "${className?uncap_first}">
package ${packageName}.module.${moduleName}.controller;

import cn.com.sipsg.common.constant.PageConstants;
import cn.com.sipsg.common.operationlog.core.annotation.OperationLog;
import cn.com.sipsg.common.operationlog.core.enums.OperationTypeEnum;
import cn.com.sipsg.common.pojo.CommonResult;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.validation.AddValidated;
import cn.com.sipsg.common.validation.UpdateValidated;
import ${packageName}.module.${moduleName}.pojo.bo.${className}QueryBO;
import ${packageName}.module.${moduleName}.pojo.bo.${className}SaveBO;
import ${packageName}.module.${moduleName}.pojo.vo.${className}VO;
import ${packageName}.module.${moduleName}.service.${className}Service;
import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * ${tableComment!}管理控制器
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Tag(name = "${tableComment!}管理")
@RestController
@RequiredArgsConstructor
@RequestMapping("/${moduleName}/${pathName}")
public class ${className}Controller {

    private final ${className}Service ${lowerCamelCaseClassName}Service;

    /**
     * 查询${tableComment!}分页列表
     *
     * @param bo 参数
     * @return ${tableComment!}分页列表
     */
    @Operation(summary = "查询${tableComment!}分页列表")
    @OperationLog(module = "${tableComment!}管理", value = "查询${tableComment!}分页列表", type = OperationTypeEnum.QUERY, saveResponse = false)
    @SaCheckPermission(value = "${moduleName}:${pathName}:list")
    @PostMapping("/page")
    public CommonResult<CommonPageVO<${className}VO>> page(@RequestBody ${className}QueryBO bo) {
        return CommonResult.data(${lowerCamelCaseClassName}Service.page(bo));
    }

    /**
     * 查询${tableComment!}列表
     *
     * @param bo 参数
     * @return ${tableComment!}列表
     */
    @Operation(summary = "查询${tableComment!}列表")
    @OperationLog(module = "${tableComment!}管理", value = "查询${tableComment!}列表", type = OperationTypeEnum.QUERY, saveResponse = false)
    @SaCheckPermission(value = "${moduleName}:${pathName}:list")
    @PostMapping("/list")
    public CommonResult<List<${className}VO>> list(@RequestBody ${className}QueryBO bo) {
        bo.setSize(PageConstants.SIZE_NONE);
        return CommonResult.data(${lowerCamelCaseClassName}Service.page(bo).getRecords());
    }

    /**
     * 新增${tableComment!}
     *
     * @param bo 参数
     * @return ${tableComment!}ID
     */
    @Operation(summary = "新增${tableComment!}")
    @OperationLog(module = "${tableComment!}管理", value = "新增${tableComment!}", type = OperationTypeEnum.SAVE)
    @SaCheckPermission(value = "${moduleName}:${pathName}:save")
    @PostMapping("/save")
    public CommonResult<String> save(@AddValidated @RequestBody ${className}SaveBO bo) {
        return CommonResult.data(${lowerCamelCaseClassName}Service.save(bo));
    }

    /**
     * 编辑${tableComment!}
     *
     * @param bo 参数
     */
    @Operation(summary = "编辑${tableComment!}")
    @OperationLog(module = "${tableComment!}管理", value = "编辑${tableComment!}", type = OperationTypeEnum.UPDATE)
    @SaCheckPermission(value = "${moduleName}:${pathName}:update")
    @PostMapping("/update")
    public CommonResult<Void> update(@UpdateValidated @RequestBody ${className}SaveBO bo) {
        ${lowerCamelCaseClassName}Service.update(bo);
        return CommonResult.success();
    }

    /**
     * 查询${tableComment!}详情
     *
     * @param ${primaryKeyJavaName} ${primaryKeyComment}
     */
    @Operation(summary = "查询${tableComment!}详情")
    @OperationLog(module = "${tableComment!}管理", value = "查询${tableComment!}详情", type = OperationTypeEnum.QUERY, saveResponse = false)
    @SaCheckPermission(value = "${moduleName}:${pathName}:detail")
    @Parameter(name = "${primaryKeyJavaName}", description = "${primaryKeyComment}", required = true, example = "1111")
    @GetMapping("/detail")
    public CommonResult<${className}VO> detail(@RequestParam ${primaryKeyJavaType} ${primaryKeyJavaName}) {
        return CommonResult.data(${lowerCamelCaseClassName}Service.detail(${primaryKeyJavaName}));
    }

    /**
     * 删除${tableComment!}
     *
     * @param ${primaryKeyJavaName} ${primaryKeyComment}
     */
    @Operation(summary = "删除${tableComment!}")
    @OperationLog(module = "${tableComment!}管理", value = "删除${tableComment!}", type = OperationTypeEnum.DELETE)
    @SaCheckPermission(value = "${moduleName}:${pathName}:delete")
    @Parameter(name = "${primaryKeyJavaName}", description = "${primaryKeyComment}", required = true, example = "1111")
    @PostMapping("/delete")
    public CommonResult<Void> delete(@RequestParam ${primaryKeyJavaType} ${primaryKeyJavaName}) {
        ${lowerCamelCaseClassName}Service.delete(id);
        return CommonResult.success();
    }

}
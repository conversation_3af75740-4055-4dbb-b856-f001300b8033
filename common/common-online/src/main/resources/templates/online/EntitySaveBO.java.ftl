<#assign excludeColumns = ["create_time","update_time","create_by","update_by","del_flag"]>
<#assign existBigDecimal = false>
<#assign existLocalDate = false>
<#assign existLocalDateTime = false>
<#assign existBoolean = false>
<#assign existPrimaryKey = false>
<#assign existNotNull = false>
<#assign existNotBlankString = false>
<#list columnList as column>
    <#if !excludeColumns?seq_contains(column.columnName)>
        <#if column.javaType == 'BigDecimal'>
            <#assign existBigDecimal = true>
        </#if>
        <#if column.javaType == 'LocalDate'>
            <#assign existLocalDate = true>
        </#if>
        <#if column.javaType == 'LocalDateTime'>
            <#assign existLocalDateTime = true>
        </#if>
        <#if column.javaType == 'Boolean'>
            <#assign existBoolean = true>
        </#if>
        <#if column.primaryKey>
            <#assign existPrimaryKey = true>
        </#if>
        <#if !(column.nullable)>
            <#if column.javaType == 'String'>
                <#assign existNotBlankString = true>
            <#else>
                <#assign existNotNull = true>
            </#if>
        </#if>
    </#if>
</#list>
<#assign existRelation = relationList??>
<#assign existOneToManyRelation = false>
<#if existRelation>
    <#list relationList as relation>
        <#if relation.modelType == 2>
            <#assign existOneToManyRelation = true>
        </#if>
    </#list>
</#if>
package ${packageName}.module.${moduleName}.pojo.bo;

<#if existPrimaryKey>
import cn.com.sipsg.common.validation.group.UpdateGroup;
</#if>
<#if existLocalDate || existLocalDateTime>
import cn.hutool.core.date.DatePattern;
</#if>
<#if existBoolean>
import com.fasterxml.jackson.annotation.JsonProperty;
</#if>
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
<#if existLocalDate || existLocalDateTime>
import org.springframework.format.annotation.DateTimeFormat;
</#if>

<#if existNotBlankString>
import javax.validation.constraints.NotBlank;
</#if>
<#if existNotNull>
import javax.validation.constraints.NotNull;
</#if>
<#if existBigDecimal>
import java.math.BigDecimal;
</#if>
<#if existLocalDate>
import java.time.LocalDate;
</#if>
<#if existLocalDateTime>
import java.time.LocalDateTime;
</#if>
<#if existOneToManyRelation>
import java.util.List;
</#if>

/**
 * ${tableComment!}保存BO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "${tableComment!}保存BO")
public class ${className}SaveBO {

<#list columnList as column>
    <#if !excludeColumns?seq_contains(column.columnName)>
    /**
     * ${column.columnComment!}
     */
        <#-- 判空 -->
        <#if !(column.nullable)>
            <#if column.primaryKey>
    @Schema(description = "${column.columnComment!}")
            <#else>
    @Schema(description = "${column.columnComment!}", requiredMode = Schema.RequiredMode.REQUIRED)
            </#if>
            <#-- 判断类型 -->
            <#if column.javaType == 'String'>
            <#-- 判断主键 -->
                <#if column.primaryKey>
    @NotBlank(message = "${column.columnComment!}不能为空", groups = {UpdateGroup.class})
                <#else>
    @NotBlank(message = "${column.columnComment!}不能为空")
                </#if>
            <#else>
                <#-- 判断主键 -->
                <#if column.primaryKey>
    @NotNull(message = "${column.columnComment!}不能为空", groups = {UpdateGroup.class})
                <#else>
    @NotNull(message = "${column.columnComment!}不能为空")
                </#if>
            </#if>
        <#else>
    @Schema(description = "${column.columnComment!}")
        </#if>
            <#-- 日期时间注解处理 -->
        <#if column.javaType == 'LocalDate'>
    @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN)
        </#if>
        <#if column.javaType == 'LocalDateTime'>
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
        </#if>
        <#if column.javaType == 'Boolean'>
    @JsonProperty(value = "${column.javaName}")
        </#if>
    private ${column.javaType} ${column.javaName};

    </#if>
</#list>
<#if relationList??>
    <#list relationList as relation>
    /**
     * ${relation.modelName}
     */
    @Schema(description = "${relation.modelName}")
        <#if relation.modelType == 1>
    private ${relation.className}SaveBO ${relation.modelCode};
        <#else>
    private List<${relation.className}SaveBO> ${relation.modelCode};
        </#if>

    </#list>
</#if>
}
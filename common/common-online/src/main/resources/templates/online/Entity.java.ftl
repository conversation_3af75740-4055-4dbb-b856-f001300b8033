<#assign excludeColumns = ["create_time","update_time","create_by","update_by","del_flag"]>
<#assign existBigDecimal = false>
<#assign existLocalDate = false>
<#assign existLocalDateTime = false>
<#assign existGeometry = false>
<#list columnList as column>
    <#if !excludeColumns?seq_contains(column.columnName)>
        <#if column.javaType == 'BigDecimal'>
            <#assign existBigDecimal = true>
        </#if>
        <#if column.javaType == 'LocalDate'>
            <#assign existLocalDate = true>
        </#if>
        <#if column.javaType == 'LocalDateTime'>
            <#assign existLocalDateTime = true>
        </#if>
        <#if column.columnType?lower_case?contains('geometry')>
            <#assign existGeometry = true>
        </#if>
    </#if>
</#list>
<#assign existRelation = relationList??>
<#assign existOneToManyRelation = false>
<#if existRelation>
    <#list relationList as relation>
        <#if relation.modelType == 2>
            <#assign existOneToManyRelation = true>
        </#if>
    </#list>
</#if>
package ${packageName}.module.${moduleName}.entity;

import cn.com.sipsg.common.mybatis.core.dataobject.BaseDO;
<#if existGeometry>
import cn.com.sipsg.common.mybatis.core.type.Geometry2StringTypeHandler;
</#if>
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
<#if existRelation>
import com.github.yulichang.annotation.EntityMapping;
</#if>
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;

<#if existBigDecimal>
import java.math.BigDecimal;
</#if>
<#if existLocalDate>
import java.time.LocalDate;
</#if>
<#if existLocalDateTime>
import java.time.LocalDateTime;
</#if>
<#if existOneToManyRelation>
import java.util.List;
</#if>

/**
 * ${tableComment!}
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@FieldNameConstants
@TableName(value = "${tableName}", autoResultMap = true)
public class ${className} extends BaseDO {

<#list columnList as column>
    <#if !excludeColumns?seq_contains(column.columnName)>
    /**
     * ${column.columnComment!}
     */
        <#if column.primaryKey>
    @TableId("${column.columnName}")
        <#else>
            <#if column.columnType?lower_case?contains('geometry')>
    @TableField(value = "${column.columnName}", typeHandler = Geometry2StringTypeHandler.class)
            <#else>
    @TableField("${column.columnName}")
            </#if>
        </#if>
    private ${column.javaType} ${column.javaName};

    </#if>
</#list>
<#if relationList??>
    <#list relationList as relation>
    /**
     * ${relation.modelName}
     */
    @TableField(exist = false)
    @EntityMapping(thisField = Fields.${relation.parentTableColumnJavaName}, joinField = ${relation.className}.Fields.${relation.childTableColumnJavaName})
        <#if relation.modelType == 1>
    private ${relation.className} ${relation.modelCode};
        <#else>
    private List<${relation.className}> ${relation.modelCode};
        </#if>

    </#list>
</#if>
}
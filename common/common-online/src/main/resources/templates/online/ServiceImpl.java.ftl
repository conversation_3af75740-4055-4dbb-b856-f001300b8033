<#assign existRelation = relationList??>
<#assign lowerCamelCaseClassName = "${className?uncap_first}">
<#assign existOneToOneRelation = false>
<#assign existOneToManyRelation = false>
<#if existRelation>
    <#list relationList as relation>
        <#if relation.modelType == 1>
            <#assign existOneToOneRelation = true>
        </#if>
        <#if relation.modelType == 2>
            <#assign existOneToManyRelation = true>
        </#if>
    </#list>
</#if>
package ${packageName}.module.${moduleName}.service.impl;

import cn.com.sipsg.common.exception.enums.ErrorCodeEnum;
import cn.com.sipsg.common.mybatis.core.query.LambdaQueryWrapperX;
<#if existRelation>
import cn.com.sipsg.common.mybatis.core.query.MPJLambdaWrapperX;
</#if>
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.util.AssertUtils;
import cn.com.sipsg.common.util.BeanUtils;
<#if existRelation>
import cn.com.sipsg.common.util.ValidationUtils;
import cn.com.sipsg.common.validation.group.AddGroup;
import cn.com.sipsg.common.validation.group.UpdateGroup;
</#if>
import ${packageName}.module.${moduleName}.entity.${className};
<#if existRelation>
    <#list relationList as relation>
import ${packageName}.module.${moduleName}.entity.${relation.className};
    </#list>
</#if>
import ${packageName}.module.${moduleName}.mapper.${className}Mapper;
import ${packageName}.module.${moduleName}.pojo.bo.${className}QueryBO;
import ${packageName}.module.${moduleName}.pojo.bo.${className}SaveBO;
<#if existRelation>
    <#list relationList as relation>
import ${packageName}.module.${moduleName}.pojo.bo.${relation.className}SaveBO;
    </#list>
</#if>
import ${packageName}.module.${moduleName}.pojo.vo.${className}VO;
import ${packageName}.module.${moduleName}.service.${className}Service;
<#if existRelation>
    <#list relationList as relation>
import ${packageName}.module.${moduleName}.service.${relation.className}Service;
    </#list>
</#if>
<#if existOneToManyRelation>
import cn.hutool.core.collection.CollUtil;
</#if>
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
<#if existRelation>
import org.springframework.transaction.annotation.Transactional;
</#if>

<#if existRelation>
import javax.validation.Validator;
import javax.validation.groups.Default;
</#if>

/**
 * ${tableComment!}服务实现类
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Service
@RequiredArgsConstructor
public class ${className}ServiceImpl extends ServiceImpl<${className}Mapper, ${className}> implements ${className}Service {

<#if existRelation>
    <#list relationList as relation>
    private final ${relation.className}Service ${relation.className?uncap_first}Service;

    </#list>
    private final Validator validator;

</#if>
    @Override
    public CommonPageVO<${className}VO> page(${className}QueryBO bo) {
        <#if existOneToOneRelation>
        MPJLambdaWrapperX<${className}> queryWrapper = new MPJLambdaWrapperX<>();
        queryWrapper.selectAll(${className}.class);
        // 填充主表查询条件
        <#list relationList as relation>
            <#-- 一对一 -->
            <#if relation.modelType == 1>
        queryWrapper
                .selectAssociation(${relation.className}.class, ${className}::get${relation.modelCode?cap_first})
            <#if relation.leftJoin>
                .leftJoin(${relation.className}.class, ${relation.className}::get${relation.childTableColumnJavaName?cap_first}, ${className}::get${relation.parentTableColumnJavaName?cap_first});
            <#else>
                .innerJoin(${relation.className}.class, ${relation.className}::get${relation.childTableColumnJavaName?cap_first}, ${className}::get${relation.parentTableColumnJavaName?cap_first});
            </#if>
        // 填充一对一查询条件
            </#if>
        </#list>
        return this.selectJoinListPage(bo, ${className}VO.class, queryWrapper);
        <#else>
        LambdaQueryWrapperX<${className}> queryWrapper = new LambdaQueryWrapperX<>();
        // 填充查询条件
            <#if !mainTable>
        queryWrapper.eqIfPresent(${className}::get${childTableColumnJavaName?cap_first}, bo.get${childTableColumnJavaName?cap_first}());
            </#if>
        return this.page(bo, queryWrapper).convert(entity -> BeanUtils.copyProperties(entity, ${className}VO.class));
        </#if>
    }

    @Override
    <#if existRelation>
    @Transactional
    </#if>
    public String save(${className}SaveBO bo) {
        bo.set${primaryKeyJavaName?cap_first}(null);
        // 新增${tableComment!}
        ${className} ${lowerCamelCaseClassName} = BeanUtils.copyProperties(bo, ${className}.class);
        this.save(${lowerCamelCaseClassName});
        <#if existRelation>
            <#list relationList as relation>
        // 新增${relation.modelName}
                <#if relation.modelType == 1>
        ${relation.className}SaveBO ${relation.modelCode} = bo.get${relation.modelCode?cap_first}();
        if (${relation.modelCode} != null) {
            ${relation.modelCode}.set${relation.childTableColumnJavaName?cap_first}(${lowerCamelCaseClassName}.get${relation.parentTableColumnJavaName?cap_first}());
            // 参数校验
            ValidationUtils.validate(validator, ${relation.modelCode}, AddGroup.class, Default.class);
            ${relation.className?uncap_first}Service.save(${relation.modelCode});
        }
                <#else>
        if (CollUtil.isNotEmpty(bo.get${relation.modelCode?cap_first}())) {
            for (${relation.className}SaveBO r : bo.get${relation.modelCode?cap_first}()) {
                r.set${relation.childTableColumnJavaName?cap_first}(${lowerCamelCaseClassName}.get${relation.parentTableColumnJavaName?cap_first}());
                // 参数校验
                ValidationUtils.validate(validator, r, AddGroup.class, Default.class);
                ${relation.className?uncap_first}Service.save(r);
            }
        }
                </#if>
            </#list>
        </#if>
        return ${lowerCamelCaseClassName}.get${primaryKeyJavaName?cap_first}();
    }

    @Override
    <#if existRelation>
    @Transactional
    </#if>
    public void update(${className}SaveBO bo) {
        // 编辑${tableComment!}
        ${className} ${lowerCamelCaseClassName} = BeanUtils.copyProperties(bo, ${className}.class);
        this.updateById(${lowerCamelCaseClassName});
        <#if existRelation>
            <#list relationList as relation>
                <#if relation.modelType == 1>
        // 编辑${relation.modelName}
        ${relation.className}SaveBO ${relation.modelCode} = bo.get${relation.modelCode?cap_first}();
        if (${relation.modelCode} != null) {
            // 参数校验
            ValidationUtils.validate(validator, ${relation.modelCode}, UpdateGroup.class, Default.class);
            ${relation.className?uncap_first}Service.update(${relation.modelCode});
        }
                </#if>
            </#list>
        </#if>
    }

    @Override
    public ${className}VO detail(${primaryKeyJavaType} ${primaryKeyJavaName}) {
        <#if existRelation>
        ${className} ${lowerCamelCaseClassName} = this.getByIdDeep(${primaryKeyJavaName});
        <#else>
        ${className} ${lowerCamelCaseClassName} = this.getById(${primaryKeyJavaName});
        </#if>
        // 校验是否存在
        AssertUtils.isTrue(${lowerCamelCaseClassName} == null, ErrorCodeEnum.DATA_NOT_EXIST);
        return BeanUtils.copyProperties(${lowerCamelCaseClassName}, ${className}VO.class);
    }

    @Override
    <#if existRelation>
    @Transactional
    </#if>
    public void delete(${primaryKeyJavaType} ${primaryKeyJavaName}) {
        ${className} ${lowerCamelCaseClassName} = this.getById(${primaryKeyJavaName});
        if (${lowerCamelCaseClassName} != null) {
            // 删除${tableComment!}
            this.removeById(${primaryKeyJavaName});
            <#if existRelation>
                <#list relationList as relation>
                    <#if relation.cascadeDel>
            // 删除${relation.modelName}
            ${relation.className?uncap_first}Service.remove(new LambdaQueryWrapperX<${relation.className}>().eq(${relation.className}::get${relation.childTableColumnJavaName?cap_first}, ${lowerCamelCaseClassName}.get${relation.parentTableColumnJavaName?cap_first}()));
                    </#if>
                </#list>
            </#if>
        }
    }

}
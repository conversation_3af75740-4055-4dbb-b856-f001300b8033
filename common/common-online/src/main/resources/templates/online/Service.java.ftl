package ${packageName}.module.${moduleName}.service;

import cn.com.sipsg.common.mybatis.core.service.BaseServiceX;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import ${packageName}.module.${moduleName}.entity.${className};
import ${packageName}.module.${moduleName}.pojo.bo.${className}QueryBO;
import ${packageName}.module.${moduleName}.pojo.bo.${className}SaveBO;
import ${packageName}.module.${moduleName}.pojo.vo.${className}VO;

/**
 * ${tableComment!}服务接口
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public interface ${className}Service extends BaseServiceX<${className}> {

    /**
     * 查询${tableComment!}分页列表
     *
     * @param bo 参数
     * @return ${tableComment!}分页列表
     */
    CommonPageVO<${className}VO> page(${className}QueryBO bo);

    /**
     * 新增${tableComment!}
     *
     * @param bo 参数
     * @return ${tableComment!}ID
     */
    String save(${className}SaveBO bo);

    /**
     * 编辑${tableComment!}
     *
     * @param bo 参数
     */
    void update(${className}SaveBO bo);

    /**
     * 根据${primaryKeyJavaName}查询${tableComment!}详情
     *
     * @param ${primaryKeyJavaName} ${primaryKeyComment}
     * @return ${tableComment!}详情
     */
    ${className}VO detail(${primaryKeyJavaType} ${primaryKeyJavaName});

    /**
     * 删除${tableComment!}
     *
     * @param ${primaryKeyJavaName} ${primaryKeyComment}
     */
    void delete(${primaryKeyJavaType} ${primaryKeyJavaName});

}
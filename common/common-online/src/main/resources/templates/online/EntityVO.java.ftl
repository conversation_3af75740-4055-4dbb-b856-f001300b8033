<#assign excludeColumns = ["create_by", "update_time", "update_by", "del_flag"]>
<#assign existBigDecimal = false>
<#assign existLocalDate = false>
<#assign existLocalDateTime = false>
<#assign existBoolean = false>
<#list columnList as column>
    <#if !excludeColumns?seq_contains(column.columnName)>
        <#if column.javaType == 'BigDecimal'>
            <#assign existBigDecimal = true>
        </#if>
        <#if column.javaType == 'LocalDate'>
            <#assign existLocalDate = true>
        </#if>
        <#if column.javaType == 'LocalDateTime'>
            <#assign existLocalDateTime = true>
        </#if>
        <#if column.javaType == 'Boolean'>
            <#assign existBoolean = true>
        </#if>
    </#if>
</#list>
<#assign existRelation = relationList??>
<#assign existOneToManyRelation = false>
<#if existRelation>
    <#list relationList as relation>
        <#if relation.modelType == 2>
            <#assign existOneToManyRelation = true>
        </#if>
    </#list>
</#if>
package ${packageName}.module.${moduleName}.pojo.vo;

import com.baomidou.mybatisplus.annotation.TableId;
<#if existBoolean>
import com.fasterxml.jackson.annotation.JsonProperty;
</#if>
import com.fhs.core.trans.vo.VO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

<#if existBigDecimal>
import java.math.BigDecimal;
</#if>
<#if existLocalDate>
import java.time.LocalDate;
</#if>
<#if existLocalDateTime>
import java.time.LocalDateTime;
</#if>
<#if existOneToManyRelation>
import java.util.List;
</#if>

/**
 * ${tableComment!}VO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "${tableComment!}VO")
public class ${className}VO implements VO {

<#list columnList as column>
    <#if !excludeColumns?seq_contains(column.columnName)>
    /**
     * ${column.columnComment!}
     */
        <#if column.primaryKey>
    @TableId("${column.columnName}")
        </#if>
        <#if column.nullable>
    @Schema(description = "${column.columnComment!}")
        <#else>
    @Schema(description = "${column.columnComment!}", requiredMode = Schema.RequiredMode.REQUIRED)
        </#if>
        <#if column.javaType == 'Boolean'>
    @JsonProperty(value = "${column.javaName}")
        </#if>
    private ${column.javaType} ${column.javaName};

    </#if>
</#list>
<#if relationList??>
    <#list relationList as relation>
    /**
     * ${relation.modelName}
     */
    @Schema(description = "${relation.modelName}")
        <#if relation.modelType == 1>
    private ${relation.className}VO ${relation.modelCode};
        <#else>
    private List<${relation.className}VO> ${relation.modelCode};
        </#if>

    </#list>
</#if>
}
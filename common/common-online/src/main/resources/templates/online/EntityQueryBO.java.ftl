package ${packageName}.module.${moduleName}.pojo.bo;

import cn.com.sipsg.common.pojo.bo.SortablePageBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * ${tableComment!}查询BO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "${tableComment!}查询BO")
public class ${className}QueryBO extends SortablePageBO {

    <#if !mainTable>
    /**
     * ${childTableColumnLabel!}
     */
    @Schema(description = "${childTableColumnLabel!}")
    private ${childTableColumnJavaType} ${childTableColumnJavaName};

    </#if>
}
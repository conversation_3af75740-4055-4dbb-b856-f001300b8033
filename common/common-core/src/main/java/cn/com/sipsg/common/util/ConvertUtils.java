package cn.com.sipsg.common.util;

/**
 * 工具类：ConvertUtils
 * 提供类型转换相关的方法
 * <AUTHOR>
 * @date 2025-07-04
 */
public class ConvertUtils {
    /**
     * 安全地将字符串转为Long，若为空或空字符串则返回null
     */
    public static Long toLong(String str) {
        if (str == null || str.trim().isEmpty()) {
            return null;
        }
        return Long.valueOf(str);
    }

    /**
     * 安全地将字符串转为Integer，若为空或空字符串则返回null
     */
    public static Integer toInteger(String str) {
        if (str == null || str.trim().isEmpty()) {
            return null;
        }
        return Integer.valueOf(str);
    }
} 
package cn.com.sipsg.common.util;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Bean 工具类
 * <p>
 * 1. 默认使用 {@link BeanUtil} 作为实现类，虽然不同 bean 工具的性能有差别，但是对绝大多数同学的项目，不用在意这点性能
 * 2. 针对复杂的对象转换，可以通过 mapstruct + default 配合实现
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public class BeanUtils {

    public static void copyProperties(Object source, Object target) {
        BeanUtil.copyProperties(source, target);
    }

    public static <T> T copyProperties(Object source, Class<T> targetClass) {
        return BeanUtil.toBean(source, targetClass);
    }

    public static <T> T copyProperties(Object source, Class<T> targetClass, boolean ignoreNullValue) {
        if (ignoreNullValue) {
            return BeanUtil.toBean(source, targetClass, CopyOptions.create().ignoreNullValue());
        }
        return copyProperties(source, targetClass);
    }

    public static <T> T copyProperties(Object source, Class<T> targetClass, String... ignoreProperties) {
        return BeanUtil.copyProperties(source, targetClass, ignoreProperties);
    }

    public static <S> List<Map<String, Object>> copyToMapList(Collection<S> source) {
        if (source == null) {
            return null;
        } else if (source.isEmpty()) {
            return Collections.emptyList();
        } else {
            return source.stream().map(BeanUtil::beanToMap).collect(Collectors.toList());
        }
    }

    public static <S, T> List<T> copyToList(Collection<S> source, Class<T> targetType) {
        return BeanUtil.copyToList(source, targetType);
    }

    public static <S, T> List<T> copyToList(Collection<S> source, Class<T> targetType, boolean ignoreNullValue) {
        if (ignoreNullValue) {
            return BeanUtil.copyToList(source, targetType, CopyOptions.create().ignoreNullValue());
        }
        return copyToList(source, targetType);
    }

    public static <S, T> List<T> copyToList(Collection<S> source, Class<T> targetType, String... ignoreProperties) {
        return BeanUtil.copyToList(source, targetType, CopyOptions.create().setIgnoreProperties(ignoreProperties));
    }

}
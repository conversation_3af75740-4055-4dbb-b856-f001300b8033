package cn.com.sipsg.common.validation;

import cn.com.sipsg.common.enums.BaseStrEnum;
import cn.com.sipsg.common.util.EnumUtils;
import cn.hutool.core.util.StrUtil;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Optional;

/**
 * 字符串枚举校验器
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public class InStrEnumValidator implements ConstraintValidator<InStrEnum, String> {

    private Class<? extends BaseStrEnum> enumClass = null;

    @Override
    public void initialize(InStrEnum enumValue) {
        enumClass = enumValue.enumClass();
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext constraintValidatorContext) {
        if (StrUtil.isEmpty(value)) {
            return Boolean.TRUE;
        }
        try {
            return Optional.ofNullable(EnumUtils.getEnumByValue(enumClass, value)).map(enumObj -> Boolean.TRUE).orElse(Boolean.FALSE);
        } catch (Exception e) {
            return Boolean.FALSE;
        }
    }

}

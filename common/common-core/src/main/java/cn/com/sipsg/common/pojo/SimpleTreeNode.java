package cn.com.sipsg.common.pojo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 简单树形节点
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
public class SimpleTreeNode<T> implements Serializable {

    private static final long serialVersionUID = 6858393861449176167L;

    /**
     * 当前节点id
     */
    protected String id;

    /**
     * 父节点id
     */
    protected String parentId;

    /**
     * 子节点列表
     */
    protected List<T> children = new ArrayList<>();

    public void add(T node) {
        children.add(node);
    }

}

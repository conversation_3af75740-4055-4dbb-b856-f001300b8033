package cn.com.sipsg.common.util;

import cn.com.sipsg.common.exception.BusinessException;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * 查询工具类
 * 提供通用的数据库查询方法
 */
@Slf4j
public class QueryUtils {

    /**
     * 通用分组统计方法
     * 该方法用于根据指定字段进行分组统计，可以指定计数字段和是否去重，同时可以应用过滤条件
     * 主要用于简化常见的分组统计查询操作，提高代码复用性和查询效率
     *
     * @param mapper     Mapper对象
     * @param groupField 分组字段名
     * @param countField 计数字段名（可为null，null时用COUNT(1)）
     * @param distinct   是否去重
     * @param filters    过滤条件
     * @param errorMsg   异常信息
     * @return 分组统计结果
     */
    public static <T> List<Map<String, Object>> groupCountByField(
            com.baomidou.mybatisplus.core.mapper.BaseMapper<T> mapper,
            String groupField, 
            String countField, 
            boolean distinct, 
            Map<String, Object> filters, 
            String errorMsg) {
        try {
            // 创建查询条件包装器
            QueryWrapper<T> wrapper = new QueryWrapper<>();
            // 如果有过滤条件，则将过滤条件添加到查询器中
            if (filters != null) {
                filters.forEach(wrapper::eq);
            }

            // 根据是否去重和计数字段是否为空，构建相应的计数表达式
            String countExpr = distinct && countField != null ? "COUNT(DISTINCT " + countField + ") as count" : "COUNT(1) as count";
            // 构建SQL查询语句：选择分组字段和计数表达式，并按分组字段分组
            wrapper.select(groupField, countExpr).groupBy(groupField);
            // 执行查询并返回结果
            return mapper.selectMaps(wrapper);
        } catch (Exception e) {
            // 如果发生异常，记录错误日志并抛出服务异常
            log.error(errorMsg, e);
            throw new BusinessException(errorMsg);
        }
    }
}
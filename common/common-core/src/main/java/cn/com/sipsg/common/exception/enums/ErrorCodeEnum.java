package cn.com.sipsg.common.exception.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 错误码枚举
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@RequiredArgsConstructor
public enum ErrorCodeEnum {

    SUCCESS(200, "请求成功"),

    // ========== 客户端错误段 ==========

    BAD_REQUEST(400, "请求参数不正确"),

    UNAUTHORIZED(401, "账号未登录"),

    FORBIDDEN(403, "没有该操作权限"),

    NOT_FOUND(404, "请求未找到"),

    METHOD_NOT_ALLOWED(405, "请求方法不正确"),

    UNSUPPORTED_MEDIA_TYPE(415, "不支持当前媒体类型"),

    LOCKED(423, "请求失败，请稍后重试"),

    TOO_MANY_REQUESTS(429, "请求过于频繁，请稍后重试"),

    // ========== 服务端错误段 ==========

    INTERNAL_SERVER_ERROR(500, "系统异常"),

    NOT_IMPLEMENTED(501, "功能未实现/未开启"),

    // ========== 自定义错误段 ==========

    BUSINESS_ERROR(40000, "业务异常"),

    REPEATED_REQUESTS(40010, "重复请求，请稍后重试"),

    ILLEGAL_ARGUMENT(40011, "非法参数"),

    INVALID_RELATED_RECORD_ID(40020, "数据验证失败，关联数据并不存在，请刷新后重试"),

    DATA_NOT_EXIST(40030, "数据不存在，请刷新后重试"),

    PARENT_IS_CHILD(40040, "不能设置自己的子节点为上级节点"),

    IMPORT_FILE_ERROR(40050, "导入文件为空或格式不正确"),

    INVALID_USERNAME_OR_PASSWORD(40100, "用户名或密码错误"),

    INVALID_USER_STATUS(40110, "用户账号已被锁定"),

    TOO_MANY_LOGIN_ATTEMPTS(40111, "登录失败次数过多，请稍后重试"),

    INVALID_GRANT_TYPE(40120, "授权类型错误"),

    FORBIDDEN_SUPER_ADMIN_USER(40130, "禁止操作超级管理员用户"),

    FORBIDDEN_SUPER_ADMIN_ROLE(40140, "禁止操作超级管理员角色"),

    LICENSE_EXPIRED(48999, "当前许可证已失效，请核查是否取得授权或重新申请许可证"),

    UIA_ERROR_RESPONSE(50300, "统一认证平台返回结果异常"),

    UIA_ADAPTER_CONFIG_ERROR(50010, "统一认证平台适配配置错误"),

    DEMO_DENY(49000, "演示模式，禁止写操作");

    /**
     * 错误码
     */
    private final Integer code;

    /**
     * 错误提示
     */
    private final String msg;

}

package cn.com.sipsg.common.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 导入通用返回对象
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Builder
@Getter
@Setter
@Schema(description = "导入通用返回对象")
public class CommonImportVO implements Serializable {

    private static final long serialVersionUID = 5233738458893343420L;

    /**
     * 总记录数
     */
    @Schema(description = "总记录数")
    private Long recordTotal;

    /**
     * 成功总数
     */
    @Schema(description = "成功总数")
    private Long successTotal;

    /**
     * 错误总数
     */
    @Schema(description = "错误总数")
    private Long errorTotal;

    /**
     * 错误记录
     */
    @Schema(description = "错误记录")
    private List<Map<String, Object>> errorRecords;

    public Long getRecordTotal() {
        return successTotal + errorTotal;
    }

}

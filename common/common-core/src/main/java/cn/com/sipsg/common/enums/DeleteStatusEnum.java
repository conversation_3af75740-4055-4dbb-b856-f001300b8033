package cn.com.sipsg.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 删除标志枚举
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@RequiredArgsConstructor
public enum DeleteStatusEnum implements BaseStrEnum {

    TRUE("1", "已删除"),

    FALSE("0", "未删除");

    @EnumValue
    @JsonValue
    private final String code;

    private final String desc;

}

<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.com.sipsg</groupId>
        <artifactId>common</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>common-onemap</artifactId>
    <description>一张图</description>

    <dependencies>
        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>common-security</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>common-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>common-web</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>common-geo</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.com.sipsg.geotools</groupId>
            <artifactId>geotools-core</artifactId>
        </dependency>

        <!-- 操作日志 -->
        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>common-operationlog</artifactId>
        </dependency>

        <!-- 动态服务 -->
        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>common-dynamic</artifactId>
        </dependency>

        <!-- 高德地图 -->
        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>common-amap</artifactId>
        </dependency>

        <!-- pdf导出 -->
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itextpdf</artifactId>
            <version>********</version>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itext-asian</artifactId>
            <version>5.2.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>fontbox</artifactId>
            <version>2.0.9</version>
        </dependency>
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
            <version>2.0.9</version>
        </dependency>
    </dependencies>

</project>
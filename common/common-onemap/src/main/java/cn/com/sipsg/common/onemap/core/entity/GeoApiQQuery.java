package cn.com.sipsg.common.onemap.core.entity;

import cn.com.sipsg.common.dynamic.core.entity.DynamicServer;
import cn.com.sipsg.common.enums.CommonStatusEnum;
import cn.com.sipsg.common.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.OrderBy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.github.yulichang.annotation.EntityMapping;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;

import java.util.List;


/**
 * 快查查询表
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@FieldNameConstants
@TableName(value = "medox_onemap_geo_api_q_query", autoResultMap = true)
public class GeoApiQQuery extends BaseDO {

    /**
     * 主键
     */
    @TableId("id")
    private String id;

    /**
     * 名称
     */
    @TableField("api_name")
    private String apiName;

    /**
     * 编码
     */
    @TableField("api_code")
    private String apiCode;

    /**
     * 服务地址
     */
    @TableField("api_url")
    private String apiUrl;

    /**
     * 分组名称
     */
    @TableField("group_name")
    private String groupName;

    /**
     * 消息类型;list，grid,simpleGrid,commonJson,row,geojson,tree,page
     */
    @TableField("result_type")
    private String resultType;

    /**
     * 服务ID
     */
    @TableField("server_id")
    private String serverId;

    /**
     * 服务类型;table,arcgis,upi,proxy,third,zmq
     */
    @TableField("server_type")
    private String serverType;

    /**
     * 模板ID
     */
    @TableField("template_id")
    private String templateId;

    /**
     * 认证方式;无，oauth2，secret
     */
    @TableField("authorize_type")
    private String authorizeType;

    /**
     * 操作类型:默认，自定义结果，自定义查询
     */
    @TableField("operate_type")
    private String operateType;

    /**
     * 扩展操作
     */
    @TableField("expand_operate")
    private String expandOperate;

    /**
     * 排序值
     */
    @TableField("sort")
    @OrderBy(asc = true, sort = Short.MIN_VALUE)
    private Integer sort;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 是否启用;0-否，1-是
     */
    @TableField("status")
    private CommonStatusEnum status;

    /**
     * 默认值
     */
    @TableField("default_value")
    private String defaultValue;

    /**
     * 关联服务
     */
    @TableField(exist = false)
    @EntityMapping(thisField = Fields.serverId, joinField = DynamicServer.Fields.id)
    private DynamicServer dynamicServer;

    /**
     * 关联参数列表
     */
    @TableField(exist = false)
    @EntityMapping(thisField = Fields.id, joinField = GeoApiQueryParam.Fields.apiId)
    private List<GeoApiQueryParam> queryParamList;

}
package cn.com.sipsg.common.onemap.core.controller;

import cn.com.sipsg.common.constant.PageConstants;
import cn.com.sipsg.common.onemap.core.pojo.bo.GeoApiQQuerySaveBO;
import cn.com.sipsg.common.onemap.core.pojo.bo.SearchHistorySaveBO;
import cn.com.sipsg.common.operationlog.core.annotation.OperationLog;
import cn.com.sipsg.common.operationlog.core.enums.OperationTypeEnum;
import cn.com.sipsg.common.pojo.CommonResult;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.validation.AddValidated;
import cn.com.sipsg.common.validation.UpdateValidated;
import cn.com.sipsg.common.onemap.core.pojo.bo.GeoApiQQueryQueryBO;
import cn.com.sipsg.common.onemap.core.pojo.vo.GeoApiQQueryVO;
import cn.com.sipsg.common.onemap.core.service.GeoApiQQueryService;
import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 快查查询表管理控制器
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Tag(name = "快查查询表管理")
@RestController
@RequiredArgsConstructor
@RequestMapping("/onemap/geoApiQQuery")
public class GeoApiQQueryController {

    private final GeoApiQQueryService geoApiQQueryService;

    /**
     * 查询历史搜索记录列表
     *
     * @param serverId 服务id
     * @return 历史搜索列表
     */
    @Operation(summary = "查询历史搜索记录列表")
    @Parameter(name = "serverId", description = "服务id", required = true, example = "1111")
    @OperationLog(module = "一张图搜索历史管理", value = "查询历史搜索记录列表", type = OperationTypeEnum.QUERY, saveResponse = false)
    @GetMapping("/searchHistoryList")
    public CommonResult<List<String>> searchHistoryList(@RequestParam String serverId) {
        return CommonResult.data(geoApiQQueryService.searchHistoryList(serverId));
    }

    /**
     * 保存历史搜索记录
     *
     * @param bo 参数
     * @return 历史搜索列表
     */
    @Operation(summary = "保存历史搜索记录")
    @OperationLog(module = "快查查询表管理", value = "保存历史搜索记录", type = OperationTypeEnum.SAVE)
    @PostMapping("/saveSearchHistory")
    public CommonResult<List<String>> saveSearchHistory(@RequestBody SearchHistorySaveBO bo) {
        return CommonResult.data(geoApiQQueryService.saveSearchHistory(bo));
    }

    /**
     * 查询快查查询表分页列表
     *
     * @param bo 参数
     * @return 快查查询表分页列表
     */
    @Operation(summary = "查询快查查询表分页列表")
    @OperationLog(module = "快查查询表管理", value = "查询快查查询表分页列表", type = OperationTypeEnum.QUERY, saveResponse = false)
    @SaCheckPermission(value = "onemap:geoApiQQuery:all")
    @PostMapping("/page")
    public CommonResult<CommonPageVO<GeoApiQQueryVO>> page(@RequestBody GeoApiQQueryQueryBO bo) {
        return CommonResult.data(geoApiQQueryService.page(bo));
    }

    /**
     * 查询快查查询表列表
     *
     * @param bo 参数
     * @return 快查查询表列表
     */
    @Operation(summary = "查询快查查询表列表")
    @OperationLog(module = "快查查询表管理", value = "查询快查查询表列表", type = OperationTypeEnum.QUERY, saveResponse = false)
    @SaCheckPermission(value = "onemap:geoApiQQuery:all")
    @PostMapping("/list")
    public CommonResult<List<GeoApiQQueryVO>> list(@RequestBody GeoApiQQueryQueryBO bo) {
        bo.setSize(PageConstants.SIZE_NONE);
        return CommonResult.data(geoApiQQueryService.page(bo).getRecords());
    }

    /**
     * 新增快查查询表
     *
     * @param bo 参数
     * @return 快查查询表ID
     */
    @Operation(summary = "新增快查查询表")
    @OperationLog(module = "快查查询表管理", value = "新增快查查询表", type = OperationTypeEnum.SAVE)
    @SaCheckPermission(value = "onemap:geoApiQQuery:all")
    @PostMapping("/save")
    public CommonResult<String> save(@AddValidated @RequestBody GeoApiQQuerySaveBO bo) {
        return CommonResult.data(geoApiQQueryService.save(bo));
    }

    /**
     * 编辑快查查询表
     *
     * @param bo 参数
     */
    @Operation(summary = "编辑快查查询表")
    @OperationLog(module = "快查查询表管理", value = "编辑快查查询表", type = OperationTypeEnum.UPDATE)
    @SaCheckPermission(value = "onemap:geoApiQQuery:all")
    @PostMapping("/update")
    public CommonResult<Void> update(@UpdateValidated @RequestBody GeoApiQQuerySaveBO bo) {
        geoApiQQueryService.update(bo);
        return CommonResult.success();
    }

    /**
     * 查询快查查询表详情
     *
     * @param id 主键
     */
    @Operation(summary = "查询快查查询表详情")
    @Parameter(name = "id", description = "主键", required = true, example = "1111")
    @OperationLog(module = "快查查询表管理", value = "查询快查查询表详情", type = OperationTypeEnum.QUERY, saveResponse = false)
    @SaCheckPermission(value = "onemap:geoApiQQuery:all")
    @GetMapping("/detail")
    public CommonResult<GeoApiQQueryVO> detail(@RequestParam String id) {
        return CommonResult.data(geoApiQQueryService.detail(id));
    }

    /**
     * 删除快查查询表
     *
     * @param id 主键
     */
    @Operation(summary = "删除快查查询表")
    @Parameter(name = "id", description = "主键", required = true, example = "1111")
    @OperationLog(module = "快查查询表管理", value = "删除快查查询表", type = OperationTypeEnum.DELETE)
    @SaCheckPermission(value = "onemap:geoApiQQuery:all")
    @PostMapping("/delete")
    public CommonResult<Void> delete(@RequestParam String id) {
        geoApiQQueryService.delete(id);
        return CommonResult.success();
    }

}
package cn.com.sipsg.common.onemap.core.service;

import cn.com.sipsg.common.mybatis.core.service.BaseServiceX;
import cn.com.sipsg.common.onemap.core.entity.GeoMapTool;
import cn.com.sipsg.common.onemap.core.pojo.bo.GeoMapToolQueryBO;
import cn.com.sipsg.common.onemap.core.pojo.bo.GeoMapToolSaveBO;
import cn.com.sipsg.common.onemap.core.pojo.vo.GeoMapToolTree;
import cn.com.sipsg.common.onemap.core.pojo.vo.GeoMapToolVO;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import java.util.List;

/**
 * 地图工具表服务接口
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public interface GeoMapToolService extends BaseServiceX<GeoMapTool> {

    /**
     * 查询地图工具表分页列表
     *
     * @param bo 参数
     * @return 地图工具表分页列表
     */
    CommonPageVO<GeoMapToolVO> page(GeoMapToolQueryBO bo);

    /**
     * 新增地图工具表
     *
     * @param bo 参数
     * @return 地图工具表ID
     */
    String save(GeoMapToolSaveBO bo);

    /**
     * 编辑地图工具表
     *
     * @param bo 参数
     */
    void update(GeoMapToolSaveBO bo);

    /**
     * 根据id查询地图工具表详情
     *
     * @param id 主键
     * @return 地图工具表详情
     */
    GeoMapToolVO detail(String id);

    /**
     * 删除地图工具表
     *
     * @param id 主键
     */
    void delete(String id);

    /**
     * 查询地图工具表树形列表
     *
     * @param bo 参数
     * @return 地图工具表树形列表
     */
    CommonPageVO<GeoMapToolVO> getTreePageList(GeoMapToolQueryBO bo);

    /**
     * 查询地图工具表树形列表
     *
     * @param bo 参数
     * @return 地图工具表树形列表
     */
    List<GeoMapToolTree> getTreeList(GeoMapToolQueryBO bo);
}
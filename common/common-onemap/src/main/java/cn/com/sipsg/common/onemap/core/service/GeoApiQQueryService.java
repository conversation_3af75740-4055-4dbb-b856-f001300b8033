package cn.com.sipsg.common.onemap.core.service;

import cn.com.sipsg.common.mybatis.core.service.BaseServiceX;
import cn.com.sipsg.common.onemap.core.pojo.bo.GeoApiQQuerySaveBO;
import cn.com.sipsg.common.onemap.core.pojo.bo.SearchHistorySaveBO;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.onemap.core.entity.GeoApiQQuery;
import cn.com.sipsg.common.onemap.core.pojo.bo.GeoApiQQueryQueryBO;
import cn.com.sipsg.common.onemap.core.pojo.vo.GeoApiQQueryVO;
import java.util.List;

/**
 * 快查查询表服务接口
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public interface GeoApiQQueryService extends BaseServiceX<GeoApiQQuery> {

    /**
     * 查询快查查询表分页列表
     *
     * @param bo 参数
     * @return 快查查询表分页列表
     */
    CommonPageVO<GeoApiQQueryVO> page(GeoApiQQueryQueryBO bo);

    /**
     * 新增快查查询表
     *
     * @param bo 参数
     * @return 快查查询表ID
     */
    String save(GeoApiQQuerySaveBO bo);

    /**
     * 编辑快查查询表
     *
     * @param bo 参数
     */
    void update(GeoApiQQuerySaveBO bo);

    /**
     * 根据id查询快查查询表详情
     *
     * @param id 主键
     * @return 快查查询表详情
     */
    GeoApiQQueryVO detail(String id);

    /**
     * 删除快查查询表
     *
     * @param id 主键
     */
    void delete(String id);

    /**
     * 保存历史搜索记录
     *
     * @param bo 参数
     * @return 历史搜索列表
     */
    List<String> saveSearchHistory(SearchHistorySaveBO bo);

    /**
     * 查询历史搜索记录列表
     *
     * @param serverId 服务id
     * @return 历史搜索列表
     */
    List<String> searchHistoryList(String serverId);
}
package cn.com.sipsg.common.onemap.core.service.impl;

import cn.com.sipsg.common.exception.enums.ErrorCodeEnum;
import cn.com.sipsg.common.mybatis.core.query.LambdaQueryWrapperX;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.util.AssertUtils;
import cn.com.sipsg.common.util.BeanUtils;
import cn.com.sipsg.common.onemap.core.entity.GeoMapLayerResourceApi;
import cn.com.sipsg.common.onemap.core.mapper.GeoMapLayerResourceApiMapper;
import cn.com.sipsg.common.onemap.core.pojo.bo.GeoMapLayerResourceApiQueryBO;
import cn.com.sipsg.common.onemap.core.pojo.bo.GeoMapLayerResourceApiSaveBO;
import cn.com.sipsg.common.onemap.core.pojo.vo.GeoMapLayerResourceApiVO;
import cn.com.sipsg.common.onemap.core.service.GeoMapLayerResourceApiService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * 图层关联图层资源/查询服务关系表服务实现类
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Service
@RequiredArgsConstructor
public class GeoMapLayerResourceApiServiceImpl extends ServiceImpl<GeoMapLayerResourceApiMapper, GeoMapLayerResourceApi> implements GeoMapLayerResourceApiService {

    @Override
    public CommonPageVO<GeoMapLayerResourceApiVO> page(GeoMapLayerResourceApiQueryBO bo) {
        LambdaQueryWrapperX<GeoMapLayerResourceApi> queryWrapper = new LambdaQueryWrapperX<>();
        // 填充查询条件
        return this.page(bo, queryWrapper).convert(entity -> BeanUtils.copyProperties(entity, GeoMapLayerResourceApiVO.class));
    }

    @Override
    @Transactional
    public void save(GeoMapLayerResourceApiSaveBO bo) {
        // 新增图层关联图层资源/查询服务关系表
        GeoMapLayerResourceApi geoMapLayerResourceApi = BeanUtils.copyProperties(bo, GeoMapLayerResourceApi.class);
        this.save(geoMapLayerResourceApi);
    }

    @Override
    @Transactional
    public void update(GeoMapLayerResourceApiSaveBO bo) {
        // 编辑图层关联图层资源/查询服务关系表
        GeoMapLayerResourceApi geoMapLayerResourceApi = BeanUtils.copyProperties(bo, GeoMapLayerResourceApi.class);
        this.updateById(geoMapLayerResourceApi);
    }

    @Override
    public GeoMapLayerResourceApiVO detail(String id) {
        GeoMapLayerResourceApi geoMapLayerResourceApi = this.getById(id);
        // 校验是否存在
        AssertUtils.isTrue(geoMapLayerResourceApi == null, ErrorCodeEnum.DATA_NOT_EXIST);
        return BeanUtils.copyProperties(geoMapLayerResourceApi, GeoMapLayerResourceApiVO.class);
    }

    @Override
    @Transactional
    public void delete(String id) {
        GeoMapLayerResourceApi geoMapLayerResourceApi = this.getById(id);
        if (geoMapLayerResourceApi != null) {
            // 删除图层关联图层资源/查询服务关系表
            this.removeById(id);
        }
    }

}
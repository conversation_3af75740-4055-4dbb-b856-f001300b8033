package cn.com.sipsg.common.onemap.core.service;

import cn.com.sipsg.common.mybatis.core.service.BaseServiceX;
import cn.com.sipsg.common.onemap.core.entity.GeoMapLayerResourceApi;
import cn.com.sipsg.common.onemap.core.pojo.bo.GeoMapLayerDragBO;
import cn.com.sipsg.common.onemap.core.pojo.bo.GeoMapLayerFavouriteSaveBO;
import cn.com.sipsg.common.onemap.core.pojo.bo.MapLayerSortBO;
import cn.com.sipsg.common.onemap.core.pojo.vo.MapLayerTreeVO;
import cn.com.sipsg.common.pojo.vo.CommonImportVO;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.onemap.core.entity.GeoMapLayer;
import cn.com.sipsg.common.onemap.core.pojo.bo.GeoMapLayerQueryBO;
import cn.com.sipsg.common.onemap.core.pojo.bo.GeoMapLayerSaveBO;
import cn.com.sipsg.common.onemap.core.pojo.vo.GeoMapLayerVO;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

/**
 * 图层表服务接口
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public interface GeoMapLayerService extends BaseServiceX<GeoMapLayer> {

    /**
     * 查询图层表分页列表
     *
     * @param bo 参数
     * @return 图层表分页列表
     */
    CommonPageVO<GeoMapLayerVO> page(GeoMapLayerQueryBO bo);

    /**
     * 新增图层表
     *
     * @param bo 参数
     * @return 图层表ID
     */
    String save(GeoMapLayerSaveBO bo);

    /**
     * 编辑图层表
     *
     * @param bo 参数
     */
    void update(GeoMapLayerSaveBO bo);

    /**
     * 根据id查询图层表详情
     *
     * @param id 主键
     * @return 图层表详情
     */
    GeoMapLayerVO detail(String id);

    /**
     * 删除图层表
     *
     * @param id 主键
     */
    void delete(String id);

    /**
     * 图层树列表
     *
     * @param bo 参数
     * @return 图层树列表
     */
    List<MapLayerTreeVO> getLayerTree(GeoMapLayerQueryBO bo);

    /**
     * 导入图层表excel数据
     *
     * @param file 导入文件
     * @return 导入结果
     */
    Map<String, CommonImportVO> importExcel(MultipartFile file);

    /**
     * 保存排序
     *
     * @param boList 参数
     */
    void updateMapLayerSort(List<MapLayerSortBO> boList);

    /**
     * 保存排序(跨层级)
     *
     * @param bo 参数
     */
    void updateMapLayerLevelSort(GeoMapLayerDragBO bo);

    /**
     * 删除所有图层表
     */
    void deleteAll();

    /**
     * 复制图层
     * @param id 图层id
     * @param copySource 是否复制图层资源
     */
    void copyMapLayer(String id, int copySource);

    /**
     * 添加图层资源和i查关系
     * @param geoMapLayerResourceApiList 图层资源和i查关系
     */
    void addLayerIQueryRef(List<GeoMapLayerResourceApi> geoMapLayerResourceApiList);

    /**
     * 收藏图层
     * @param geoMapLayerFavouriteSaveBO 收藏图层参数
     */
    void collectMapLayer(GeoMapLayerFavouriteSaveBO geoMapLayerFavouriteSaveBO);

    /**
     * 删除收藏图层
     */
    void delCollectMapLayer();

    /**
     * 导出图层
     * @param response 响应
     * @param bo 参数
     */
    void export(HttpServletResponse response, GeoMapLayerQueryBO bo);

    /**
     * 获取导出shp图层列表
     * @return 导出shp图层列表
     */
    List<GeoMapLayerVO> getExportShpLayerList();
}
package cn.com.sipsg.common.onemap.core.controller;

import cn.com.sipsg.common.constant.PageConstants;
import cn.com.sipsg.common.operationlog.core.annotation.OperationLog;
import cn.com.sipsg.common.operationlog.core.enums.OperationTypeEnum;
import cn.com.sipsg.common.pojo.CommonResult;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.validation.AddValidated;
import cn.com.sipsg.common.validation.UpdateValidated;
import cn.com.sipsg.common.onemap.core.pojo.bo.GeoApiIQueryQueryBO;
import cn.com.sipsg.common.onemap.core.pojo.bo.GeoApiIQuerySaveBO;
import cn.com.sipsg.common.onemap.core.pojo.vo.GeoApiIQueryVO;
import cn.com.sipsg.common.onemap.core.service.GeoApiIQueryService;
import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * i快速查询表管理控制器
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Tag(name = "i快速查询表管理")
@RestController
@RequiredArgsConstructor
@RequestMapping("/onemap/geoApiIQuery")
public class GeoApiIQueryController {

    private final GeoApiIQueryService geoApiIQueryService;

    /**
     * 查询i快速查询表分页列表
     *
     * @param bo 参数
     * @return i快速查询表分页列表
     */
    @Operation(summary = "查询i快速查询表分页列表")
    @OperationLog(module = "i快速查询表管理", value = "查询i快速查询表分页列表", type = OperationTypeEnum.QUERY, saveResponse = false)
    @SaCheckPermission(value = "onemap:geoApiIQuery:all")
    @PostMapping("/page")
    public CommonResult<CommonPageVO<GeoApiIQueryVO>> page(@RequestBody GeoApiIQueryQueryBO bo) {
        return CommonResult.data(geoApiIQueryService.page(bo));
    }

    /**
     * 查询i快速查询表列表
     *
     * @param bo 参数
     * @return i快速查询表列表
     */
    @Operation(summary = "查询i快速查询表列表")
    @OperationLog(module = "i快速查询表管理", value = "查询i快速查询表列表", type = OperationTypeEnum.QUERY, saveResponse = false)
    @SaCheckPermission(value = "onemap:geoApiIQuery:all")
    @PostMapping("/list")
    public CommonResult<List<GeoApiIQueryVO>> list(@RequestBody GeoApiIQueryQueryBO bo) {
        bo.setSize(PageConstants.SIZE_NONE);
        return CommonResult.data(geoApiIQueryService.page(bo).getRecords());
    }

    /**
     * 新增i快速查询表
     *
     * @param bo 参数
     * @return i快速查询表ID
     */
    @Operation(summary = "新增i快速查询表")
    @OperationLog(module = "i快速查询表管理", value = "新增i快速查询表", type = OperationTypeEnum.SAVE)
    @SaCheckPermission(value = "onemap:geoApiIQuery:all")
    @PostMapping("/save")
    public CommonResult<String> save(@AddValidated @RequestBody GeoApiIQuerySaveBO bo) {
        return CommonResult.data(geoApiIQueryService.save(bo));
    }

    /**
     * 编辑i快速查询表
     *
     * @param bo 参数
     */
    @Operation(summary = "编辑i快速查询表")
    @OperationLog(module = "i快速查询表管理", value = "编辑i快速查询表", type = OperationTypeEnum.UPDATE)
    @SaCheckPermission(value = "onemap:geoApiIQuery:all")
    @PostMapping("/update")
    public CommonResult<Void> update(@UpdateValidated @RequestBody GeoApiIQuerySaveBO bo) {
        geoApiIQueryService.update(bo);
        return CommonResult.success();
    }

    /**
     * 查询i快速查询表详情
     *
     * @param id 主键
     */
    @Operation(summary = "查询i快速查询表详情")
    @Parameter(name = "id", description = "主键", required = true, example = "1111")
    @OperationLog(module = "i快速查询表管理", value = "查询i快速查询表详情", type = OperationTypeEnum.QUERY, saveResponse = false)
    @SaCheckPermission(value = "onemap:geoApiIQuery:all")
    @GetMapping("/detail")
    public CommonResult<GeoApiIQueryVO> detail(@RequestParam String id) {
        return CommonResult.data(geoApiIQueryService.detail(id));
    }

    /**
     * 删除i快速查询表
     *
     * @param id 主键
     */
    @Operation(summary = "删除i快速查询表")
    @Parameter(name = "id", description = "主键", required = true, example = "1111")
    @OperationLog(module = "i快速查询表管理", value = "删除i快速查询表", type = OperationTypeEnum.DELETE)
    @SaCheckPermission(value = "onemap:geoApiIQuery:all")
    @PostMapping("/delete")
    public CommonResult<Void> delete(@RequestParam String id) {
        geoApiIQueryService.delete(id);
        return CommonResult.success();
    }

}
package cn.com.sipsg.common.onemap.core.convert;

import cn.com.sipsg.common.enums.CommonStatusEnum;
import cn.com.sipsg.common.onemap.core.entity.GeoApiIQuery;
import cn.com.sipsg.common.onemap.core.pojo.bo.GeoApiIQuerySaveBO;
import cn.com.sipsg.common.util.EnumUtils;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * i快速查询转换器
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Mapper
public interface GeoApiIQueryConvert {

    GeoApiIQueryConvert INSTANCE = Mappers.getMapper(GeoApiIQueryConvert.class);

    /**
     * bo转entity
     *
     * @param entity i快速查询bo
     * @return entity
     */
    GeoApiIQuery convert(GeoApiIQuerySaveBO entity);

    default CommonStatusEnum status2Enum(Integer status) {
        return EnumUtils.getEnumByValue(CommonStatusEnum.class, status);
    }
}

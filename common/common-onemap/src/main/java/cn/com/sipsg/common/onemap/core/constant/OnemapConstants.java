package cn.com.sipsg.common.onemap.core.constant;

/**
 * 一张图常量
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public interface OnemapConstants {

    String IMAGE = "影像";

    String MAP = "地图";

    String COUNTRY_TIANDITU_IMAGE = "国家天地图影像";

    String COUNTRY_TIANDITU_IMAGE_NOTE = "国家天地图注记";

    String COUNTRY_TIANDITU_MAP = "国家天地图地图";

    String COUNTRY_TIANDITU_MAP_NOTE = "国家天地图地图注记";

    /**
     * 图层类型常量 1分组，2图层
     */
    interface MapLayerType {

        String GROUP = "1";

        String LAYER = "2";
    }

    /**
     * 权限类型
     */
    interface ExtentTypeCode {

        /**
         * 中心点和层级
         */
        String CENTER = "1";

        /**
         * 范围
         */
        String RANGE = "2";
    }

    /**
     * 权限类型
     */
    interface PrivilegeTypeCode {

        /**
         * 地图工具
         */
        String PRIVILEGE_TYPE_MAP_TOOL = "MAP_TOOL";

        /**
         * 地图范围
         */
        String PRIVILEGE_TYPE_MAP_EXTENT = "MAP_EXTENT";

        /**
         * 地图图层
         */
        String PRIVILEGE_MAP_LAYER = "MAP_LAYER";

        /**
         * 快速查询
         */
        String QUICK_QUERY = "QUICK_QUERY";

        /**
         * token
         */
        String PRIVILEGE_TYPE_TOKEN = "TOKEN";

        /**
         * 地图底图
         */
        String MAP_BASE_LAYER = "MAP_BASE_LAYER";

        /**
         * 默认地图配置
         */
        String DEFAULT_MAP_LAYER = "DEFAULT_MAP_LAYER";
    }

}

package cn.com.sipsg.common.onemap.core.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 快查询参数
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@ToString
@Schema(description = "快查询查询参数")
public class GeoApiQQueryParamVO implements Serializable {

    private static final long serialVersionUID = -1L;

    /**
     * 标签
     */
    @Schema(description = "标签")
    private String label;

    /**
     * 名称
     */
    @Schema(description = "名称")
    private String value;

    /**
     * 数据类型
     */
    @Schema(description = "数据类型")
    private String dataType;

    /**
     * 默认值
     */
    @Schema(description = "默认值")
    private String defaultValue;

    /**
     * 字典值
     */
    @Schema(description = "字典值")
    private String groupId;


    /**
     * 地址前缀
     */
    @Schema(description = "地址前缀")
    private String urlPrefix;
}

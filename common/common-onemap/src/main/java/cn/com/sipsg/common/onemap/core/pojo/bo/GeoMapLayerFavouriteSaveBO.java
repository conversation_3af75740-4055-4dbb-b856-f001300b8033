package cn.com.sipsg.common.onemap.core.pojo.bo;

import cn.com.sipsg.common.validation.group.UpdateGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

/**
 * 图层收藏表保存BO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "图层收藏表保存BO")
public class GeoMapLayerFavouriteSaveBO {

    /**
     * 主键
     */
    @Schema(description = "主键")
    @NotBlank(message = "主键不能为空", groups = {UpdateGroup.class})
    private String id;

    /**
     * 图层id
     */
    @Schema(description = "图层id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "图层id不能为空")
    private String layerId;

    /**
     * 类型 0-取消收藏 1-收藏
     */
    @Schema(description = "类型 0-取消收藏 1-收藏")
    @NotBlank(message = "类型不能为空")
    private int type;

}
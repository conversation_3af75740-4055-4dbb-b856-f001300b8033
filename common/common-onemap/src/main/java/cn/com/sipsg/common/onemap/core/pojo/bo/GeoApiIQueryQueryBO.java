package cn.com.sipsg.common.onemap.core.pojo.bo;

import cn.com.sipsg.common.onemap.core.entity.GeoApiQueryParam;
import cn.com.sipsg.common.pojo.bo.SortablePageBO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

/**
 * i快速查询表查询BO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "i快速查询表查询BO")
public class GeoApiIQueryQueryBO extends SortablePageBO {

    /**
     * 名称
     */
    @Schema(description = "名称")
    private String apiName;

    /**
     * 编码
     */
    @Schema(description = "编码")
    private String apiCode;

    /**
     * 服务类型：table,arcgis,upi,proxy,third,zmq
     */
    @Schema(description = "服务类型：table,arcgis,upi,proxy,third,zmq")
    private String serverType;

    /**
     * 要素类型：点，线，面
     */
    @Schema(description = "要素类型：点，线，面")
    private String geoType;

    /**
     * 服务ID
     */
    @Schema(description = "服务ID")
    private String serverId;

    /**
     * 模板ID
     */
    @Schema(description = "模板ID")
    private String templateId;

    /**
     * 参数配置集合
     */
    @Schema(description = "参数配置集合")
    private List<GeoApiQueryParam> queryParamList;

}
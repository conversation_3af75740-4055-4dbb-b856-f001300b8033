package cn.com.sipsg.common.onemap.core.service;

import cn.com.sipsg.common.mybatis.core.service.BaseServiceX;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.onemap.core.entity.GeoApiIQuery;
import cn.com.sipsg.common.onemap.core.pojo.bo.GeoApiIQueryQueryBO;
import cn.com.sipsg.common.onemap.core.pojo.bo.GeoApiIQuerySaveBO;
import cn.com.sipsg.common.onemap.core.pojo.vo.GeoApiIQueryVO;

/**
 * i快速查询表服务接口
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public interface GeoApiIQueryService extends BaseServiceX<GeoApiIQuery> {

    /**
     * 查询i快速查询表分页列表
     *
     * @param bo 参数
     * @return i快速查询表分页列表
     */
    CommonPageVO<GeoApiIQueryVO> page(GeoApiIQueryQueryBO bo);

    /**
     * 新增i快速查询表
     *
     * @param bo 参数
     * @return i快速查询表ID
     */
    String save(GeoApiIQuerySaveBO bo);

    /**
     * 编辑i快速查询表
     *
     * @param bo 参数
     */
    void update(GeoApiIQuerySaveBO bo);

    /**
     * 根据id查询i快速查询表详情
     *
     * @param id 主键
     * @return i快速查询表详情
     */
    GeoApiIQueryVO detail(String id);

    /**
     * 删除i快速查询表
     *
     * @param id 主键
     */
    void delete(String id);

}
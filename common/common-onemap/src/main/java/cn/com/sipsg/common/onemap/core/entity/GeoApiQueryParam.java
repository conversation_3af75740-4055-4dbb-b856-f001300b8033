package cn.com.sipsg.common.onemap.core.entity;

import cn.com.sipsg.common.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.OrderBy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;


/**
 * 参数配置表
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@FieldNameConstants
@TableName(value = "medox_onemap_geo_api_query_param", autoResultMap = true)
public class GeoApiQueryParam extends BaseDO {

    /**
     * 主键
     */
    @TableId("id")
    private String id;

    /**
     * 查询ID
     */
    @TableField("api_id")
    private String apiId;

    /**
     * 入参名
     */
    @TableField("param_name")
    private String paramName;

    /**
     * 操作符
     */
    @TableField("operator")
    private String operator;

    /**
     * 条件
     */
    @TableField("condition")
    private String condition;

    /**
     * 排序值
     */
    @TableField("sort")
    @OrderBy(asc = true, sort = Short.MIN_VALUE)
    private Integer sort;

}
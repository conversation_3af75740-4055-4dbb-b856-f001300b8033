package cn.com.sipsg.common.onemap.core.pojo.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fhs.core.trans.vo.VO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 图层收藏表VO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "图层收藏表VO")
public class GeoMapLayerFavouriteVO implements VO {

    /**
     * 主键
     */
    @TableId("id")
    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED)
    private String id;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

    /**
     * 图层id
     */
    @Schema(description = "图层id", requiredMode = Schema.RequiredMode.REQUIRED)
    private String layerId;

}
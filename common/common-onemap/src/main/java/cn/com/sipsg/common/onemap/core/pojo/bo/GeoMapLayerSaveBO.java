package cn.com.sipsg.common.onemap.core.pojo.bo;

import cn.com.sipsg.common.validation.group.UpdateGroup;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
 * 图层表保存BO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "图层表保存BO")
public class GeoMapLayerSaveBO {

    /**
     * 主键
     */
    @Schema(description = "主键")
    @NotBlank(message = "主键不能为空", groups = {UpdateGroup.class})
    private String id;

    /**
     * 父级ID
     */
    @Schema(description = "父级ID")
    private String parentId;

    /**
     * 类型：分组、图层
     */
    @Schema(description = "类型：分组、图层")
    private String type;

    /**
     * 图层编码
     */
    @Schema(description = "图层编码")
    private String mapLayerCode;

    /**
     * 图层名称
     */
    @Schema(description = "图层名称")
    private String mapLayerName;

    /**
     * 排序值
     */
    @Schema(description = "排序值")
    private Integer sort;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 是否启用;0-否，1-是
     */
    @Schema(description = "是否启用;0-否，1-是")
    private Integer status;

    /**
     * 图标
     */
    @Schema(description = "图标")
    private String iconSrc;

    /**
     * 图层范围
     */
    @Schema(description = "图层范围")
    private String layerExtent;

    /**
     * 图例
     */
    @Schema(description = "图例")
    private String legend;

    /**
     * 不透明度
     */
    @Schema(description = "不透明度")
    private BigDecimal opacity;

    /**
     * 是否支持i查询
     */
    @Schema(description = "是否支持i查询")
    @JsonProperty(value = "supportIQuery")
    private Boolean supportIQuery;

    /**
     * 仅置顶显示
     */
    @Schema(description = "仅置顶显示")
    @JsonProperty(value = "onlyShowTop")
    private Boolean onlyShowTop;

    /**
     * 是否可见
     */
    @Schema(description = "是否可见")
    @JsonProperty(value = "visible")
    private Boolean visible;

    /**
     * 是否展开
     */
    @Schema(description = "是否展开")
    @JsonProperty(value = "expand")
    private Boolean expand;

    /**
     * 是否组内可选
     */
    @Schema(description = "是否组内可选")
    @JsonProperty(value = "groupCheck")
    private Boolean groupCheck;

    /**
     * 是否为新数据
     */
    @Schema(description = "是否为新数据")
    private Boolean newData;

    /**
     * 导出服务编码
     */
    @Schema(description = "导出服务编码")
    private String exportServerCode;

    /**
     * 叠置分析服务
     */
    @Schema(description = "叠置分析服务")
    private String overlayServerCode;

    /**
     * 坐标转换
     */
    @Schema(description = "坐标转换")
    private String coordinateTransform;

    /**
     * 投影坐标系
     */
    @Schema(description = "投影坐标系")
    private String projection;

    /**
     * 是否开启影像对比
     */
    @Schema(description = "是否开启影像对比")
    private Boolean imageCompare;

}
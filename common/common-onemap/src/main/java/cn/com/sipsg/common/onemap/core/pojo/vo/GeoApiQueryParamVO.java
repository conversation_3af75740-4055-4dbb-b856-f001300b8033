package cn.com.sipsg.common.onemap.core.pojo.vo;

import cn.com.sipsg.common.onemap.core.entity.GeoApiQueryParam;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fhs.core.trans.vo.VO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 参数配置表VO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "参数配置表VO")
public class GeoApiQueryParamVO implements VO {

    /**
     * 主键
     */
    @TableId("id")
    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED)
    private String id;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

    /**
     * 查询ID
     */
    @Schema(description = "查询ID")
    private String apiId;

    /**
     * 入参名
     */
    @Schema(description = "入参名")
    private String paramName;

    /**
     * 操作符
     */
    @Schema(description = "操作符")
    private String operator;

    /**
     * 条件
     */
    @Schema(description = "条件")
    private String condition;

    /**
     * 排序值
     */
    @Schema(description = "排序值")
    private Integer sort;

    /**
     * 参数配置集合
     */
    @Schema(description = "参数配置集合")
    private List<GeoApiQueryParam> queryParamList;

}
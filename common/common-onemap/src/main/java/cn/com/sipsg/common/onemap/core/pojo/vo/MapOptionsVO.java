package cn.com.sipsg.common.onemap.core.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 地图基础项配置
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@ToString
@Schema(description = "地图基础项配置")
public class MapOptionsVO {

    /**
     * epsg4528配置
     */
    @Schema(description = "epsg4528配置")
    Boolean epsg4528;

    /**
     * 项目四至范围
     */
    @Schema(description = "项目四至范围")
    List<List<Double>> maxBounds;

    /**
     * SZ2000配置
     */
    @Schema(description = "SZ2000配置")
    String projection;

    /**
     * 风格配置
     */
    @Schema(description = "风格配置")
    String styleURL;

    /**
     * 风格配置地址token
     */
    @Schema(description = "风格配置地址token")
    String tokenId;

    /**
     * 关联地图范围id
     */
    @Schema(description = "关联地图范围id")
    String extentId;

    /**
     * 地图授权码
     */
    @Schema(description = "地图授权码")
    String accessToken;

    /**
     * 最小缩放比(0-24)
     */
    @Schema(description = "最小缩放比(0-24)")
    private Integer minZoom = 0;

    /**
     * 最大缩放比(0-24)
     */
    @Schema(description = "最大缩放比(0-24)")
    private Integer maxZoom = 24;

    /**
     * 城市编码
     */
    @Schema(description = "城市编码")
    private String cityCode;
}
package cn.com.sipsg.common.onemap.core.pojo.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 地图导出参数对象
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "地图导出参数对象")
public class MapPdfPropertiesBO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 模板文件路径
     */
    @Schema(description = "模板文件路径")
    private String templatePath;

    /**
     * 布局类型
     */
    @Schema(description = "布局类型")
    private String templatePage;

    /**
     * 大标题
     */
    @Schema(description = "大标题")
    private String title;

    /**
     * 小标题
     */
    @Schema(description = "小标题")
    private String subtitle;

    /**
     * 居中标题
     */
    @Schema(description = "居中标题")
    private String centerTitle;

    /**
     * 图片
     */
    @Schema(description = "图片")
    private String img;

    /**
     * 比例尺图片
     */
    @Schema(description = "比例尺图片")
    private String scaleImg;

    /**
     * 指北针图片
     */
    @Schema(description = "指北针图片")
    private String northImg;

    /**
     * 标尺
     */
    @Schema(description = "标尺")
    private List<String> scaleValue;

    /**
     * 作者
     */
    @Schema(description = "作者")
    private String author;

    /**
     * 版权
     */
    @Schema(description = "版权")
    private String copyRight;

    /**
     * 制图日期
     */
    @Schema(description = "制图日期")
    private String date;

    /**
     * 导出类型
     */
    @Schema(description = "导出类型")
    private String outType;
}

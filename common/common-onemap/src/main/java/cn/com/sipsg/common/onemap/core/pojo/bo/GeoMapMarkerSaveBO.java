package cn.com.sipsg.common.onemap.core.pojo.bo;

import cn.com.sipsg.common.validation.group.UpdateGroup;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import org.springframework.web.multipart.MultipartFile;

/**
 * 图标表保存BO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "图标表保存BO")
public class GeoMapMarkerSaveBO {

    /**
     * 主键
     */
    @Schema(description = "主键")
    @NotBlank(message = "主键不能为空", groups = {UpdateGroup.class})
    private String id;

    /**
     * 图标名称
     */
    @Schema(description = "图标名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "图标名称不能为空")
    private String markerName;

    /**
     * 图标地址
     */
    @Schema(description = "图标地址")
    private String markerUrl;

    /**
     * 分组
     */
    @Schema(description = "分组")
    private String groupBm;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 文件地址
     */
    @Schema(description = "文件地址")
    private String filePath;

    /**
     * 文件
     */
    @Schema(description = "文件")
    private MultipartFile file;

    /**
     * SDF图像
     */
    @Schema(description = "SDF图像")
    private Boolean sdf;

}
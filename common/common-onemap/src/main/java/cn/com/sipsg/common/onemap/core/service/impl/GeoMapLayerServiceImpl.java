package cn.com.sipsg.common.onemap.core.service.impl;

import cn.com.sipsg.common.constant.CommonConstants;
import cn.com.sipsg.common.enums.CommonStatusEnum;
import cn.com.sipsg.common.exception.BusinessException;
import cn.com.sipsg.common.exception.enums.ErrorCodeEnum;
import cn.com.sipsg.common.mybatis.core.query.LambdaQueryWrapperX;
import cn.com.sipsg.common.mybatis.core.query.MPJLambdaWrapperX;
import cn.com.sipsg.common.onemap.core.constant.OnemapConstants;
import cn.com.sipsg.common.onemap.core.constant.OnemapConstants.MapLayerType;
import cn.com.sipsg.common.onemap.core.convert.GeoMapLayerConvert;
import cn.com.sipsg.common.onemap.core.entity.*;
import cn.com.sipsg.common.onemap.core.mapper.*;
import cn.com.sipsg.common.onemap.core.pojo.bo.*;
import cn.com.sipsg.common.onemap.core.pojo.vo.GeoMapLayerVO;
import cn.com.sipsg.common.onemap.core.pojo.vo.GeoMapMarkerVO;
import cn.com.sipsg.common.onemap.core.pojo.vo.GeoMapResourceVO;
import cn.com.sipsg.common.onemap.core.pojo.vo.MapLayerTreeVO;
import cn.com.sipsg.common.onemap.core.service.*;
import cn.com.sipsg.common.pojo.vo.CommonImportVO;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.util.*;
import cn.com.sipsg.common.web.core.util.ExcelUtils;
import cn.com.sipsg.common.web.core.util.WebFrameworkUtils;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DatePattern.PURE_DATETIME_PATTERN;


/**
 * 图层表服务实现类
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Service
@RequiredArgsConstructor
public class GeoMapLayerServiceImpl extends ServiceImpl<GeoMapLayerMapper, GeoMapLayer> implements GeoMapLayerService {

    @Resource
    private GeoMapResourceService geoMapResourceService;

    private final GeoMapMarkerService geoMapMarkerService;

    private final GeoMapLayerResourceApiService  geoMapLayerResourceApiService;

    private final GeoMapLayerResourceMapper geoMapLayerResourceMapper;

    private final GeoMapResourceMarkerService geoMapResourceMarkerService;

    private final GeoMapResourceStyleMapper geoMapResourceStyleMapper;

    private final GeoMapLayerFavouriteMapper geoMapLayerFavouriteMapper;

    private final GeoMapConfigMapper geoMapConfigMapper;

    @Override
    public CommonPageVO<GeoMapLayerVO> page(GeoMapLayerQueryBO bo) {
        LambdaQueryWrapperX<GeoMapLayer> queryWrapper = new LambdaQueryWrapperX<>();
        // 填充查询条件
        return this.page(bo, queryWrapper).convert(entity -> BeanUtils.copyProperties(entity, GeoMapLayerVO.class));
    }

    @Override
    @Transactional
    public String save(GeoMapLayerSaveBO bo) {
        bo.setId(null);
        // 新增图层表
        GeoMapLayer geoMapLayer = GeoMapLayerConvert.INSTANCE.convert(bo);
        String type = bo.getType();
        //判断分组图层是否存在（同一层级）
        if (MapLayerType.GROUP.equals(type)) {
            Long selectCount = baseMapper.selectCount(new LambdaQueryWrapperX<GeoMapLayer>()
                .eq(GeoMapLayer::getMapLayerName, bo.getMapLayerName())
                .eq(GeoMapLayer::getType, type)
                .eqIfPresent(GeoMapLayer::getParentId, bo.getParentId()));
            if (selectCount > 0) {
                throw new BusinessException("图层分组名称已存在");
            }
        }
        geoMapLayer.setParentId(StrUtil.isBlank(bo.getParentId()) ? CommonConstants.PARENT_ID_ROOT : bo.getParentId());
        geoMapLayer.setNewData(true);
        Long aLong = baseMapper.selectCount();
        geoMapLayer.setSort(aLong.intValue() + 1);
        this.save(geoMapLayer);
        return geoMapLayer.getId();
    }

    @Override
    @Transactional
    public void update(GeoMapLayerSaveBO bo) {
        // 编辑图层表
        GeoMapLayer geoMapLayer = GeoMapLayerConvert.INSTANCE.convert(bo);
        String type = bo.getType();
        //判断分组图层是否存在（同一层级）
        if (MapLayerType.GROUP.equals(type)) {
            Long selectCount = baseMapper.selectCount(new LambdaQueryWrapperX<GeoMapLayer>()
                .eq(GeoMapLayer::getMapLayerName, bo.getMapLayerName())
                .eq(GeoMapLayer::getType, type)
                .eqIfPresent(GeoMapLayer::getParentId, bo.getParentId())
                .ne(GeoMapLayer::getId, bo.getId()));
            if (selectCount > 0) {
                throw new BusinessException("图层分组名称已存在");
            }
        }
        this.updateById(geoMapLayer);
    }

    @Override
    public GeoMapLayerVO detail(String id) {
        GeoMapLayer geoMapLayer = this.getById(id);
        // 校验是否存在
        AssertUtils.isTrue(geoMapLayer == null, ErrorCodeEnum.DATA_NOT_EXIST);
        GeoMapLayerVO geoMapLayerVO = BeanUtils.copyProperties(geoMapLayer, GeoMapLayerVO.class);
        MPJLambdaWrapperX<GeoMapLayerResource> queryWrapper = new MPJLambdaWrapperX<>();
        queryWrapper.selectAll(GeoMapResource.class);
        queryWrapper.selectAs(GeoMapLayerResource::getId, GeoMapResourceVO::getLayerResourceId);
        queryWrapper.selectAs(GeoMapLayerResource::getLayerId, GeoMapResourceVO::getLayerId);
        queryWrapper.leftJoin(GeoMapResource.class, GeoMapResource::getId, GeoMapLayerResource::getResourceId);
        queryWrapper.eq(GeoMapLayerResource::getLayerId, id);
        List<GeoMapResourceVO> geoMapResourceVOList = geoMapLayerResourceMapper.selectJoinList(GeoMapResourceVO.class, queryWrapper);
        if (CollUtil.isNotEmpty(geoMapResourceVOList)) {
            for (GeoMapResourceVO geoMapResourceVO : geoMapResourceVOList) {
                //通过图层/图层资源关联id 和图层id 查询 关联服务
                List<GeoMapLayerResourceApi> geoMapLayerResourceApiList = geoMapLayerResourceApiService.list(new LambdaQueryWrapper<GeoMapLayerResourceApi>()
                    .eq(GeoMapLayerResourceApi::getLayerResourceId, geoMapResourceVO.getLayerResourceId())
                    .eq(GeoMapLayerResourceApi::getResourceId, geoMapResourceVO.getId()));
                geoMapResourceVO.setIQueryIdList(CollectionUtils.convertList(geoMapLayerResourceApiList, GeoMapLayerResourceApi::getApiId));

                //根据图层资源id查询对应的图标集合
                List<GeoMapResourceMarker> geoMapResourceMarkerList = geoMapResourceMarkerService.listDeep(new LambdaQueryWrapperX<GeoMapResourceMarker>().eq(GeoMapResourceMarker::getResourceId, geoMapResourceVO.getId()));
                geoMapResourceVO.setGeoMapMarkerList(geoMapResourceMarkerList.stream().map(a -> BeanUtils.copyProperties(a.getGeoMapMarker(), GeoMapMarkerVO.class)).collect(Collectors.toList()));
                geoMapResourceVO.setMarkerIdList(geoMapResourceMarkerList.stream().map(GeoMapResourceMarker::getMarkerId).collect(Collectors.toList()));

                //查询对应的扩展属性
                List<Map<String, Object>> expandProList = new ArrayList<>();
                List<GeoMapResourceStyle> geoMapResourceStyleList = geoMapResourceStyleMapper.selectList(GeoMapResourceStyle::getResourceId, geoMapResourceVO.getId());
                if (CollUtil.isNotEmpty(geoMapResourceStyleList)) {
                    geoMapResourceStyleList.forEach(geoMapResourceStyle -> {
                        Map<String, Object> map = new HashMap<>();
                        map.put("id", geoMapResourceStyle.getId());
                        map.put("expandPro", geoMapResourceStyle.getExpandPro());
                        expandProList.add(map);
                    });
                }
                geoMapResourceVO.setExpandProList(expandProList);
            }
        }
        geoMapLayerVO.setMapResourceList(geoMapResourceVOList);
        return geoMapLayerVO;
    }

    @Override
    @Transactional
    public void delete(String id) {
        GeoMapLayer geoMapLayer = this.getById(id);
        if (geoMapLayer != null) {
            //递归删除图层
            List<String> ids = CollUtil.newArrayList();
            ids.add(id);
            List<GeoMapLayer> list = baseMapper.selectList(new LambdaQueryWrapperX<GeoMapLayer>().eq(GeoMapLayer::getParentId, id));
            deleteByParentId(list, ids);
            baseMapper.deleteByIds(ids);
            //删除图层和图层资源的关联关系
            geoMapLayerResourceMapper.delete(new LambdaQueryWrapperX<GeoMapLayerResource>().in(GeoMapLayerResource::getLayerId, ids));
            //同时删除地图配置表图层数据
            geoMapConfigMapper.delete(new LambdaQueryWrapperX<GeoMapConfig>().in(GeoMapConfig::getPrivilegeId, ids));
        }
    }

    private void deleteByParentId(List<GeoMapLayer> list, List<String> ids) {
        if (CollUtil.isNotEmpty(list)) {
            list.forEach(a -> {
                ids.add(a.getId());
                List<GeoMapLayer> geoMapLayerModelList = baseMapper.selectList(new LambdaQueryWrapperX<GeoMapLayer>().eq(GeoMapLayer::getParentId, a.getId()));
                if (CollUtil.isNotEmpty(geoMapLayerModelList)) {
                    geoMapLayerModelList.forEach(b -> ids.add(b.getId()));
                    deleteByParentId(geoMapLayerModelList, ids);
                }
            });
        }
    }

    @Override
    public List<MapLayerTreeVO> getLayerTree(GeoMapLayerQueryBO bo) {
        List<GeoMapLayer> geoMapLayerList = this.baseMapper.selectList(new LambdaQueryWrapperX<GeoMapLayer>().orderByAsc(GeoMapLayer::getSort).orderByAsc(GeoMapLayer::getCreateTime));
        geoMapLayerList = this.searchLayerTree(geoMapLayerList, bo);
        //根据sort排序
        geoMapLayerList = geoMapLayerList.stream().sorted((a, b) -> {
            if (a.getSort() == null && b.getSort() == null) {
                return CommonConstants.ZERO;
            }
            if (a.getSort() == null) {
                return CommonConstants.ONE;
            }
            if (b.getSort() == null) {
                return CommonConstants.MINUS_ONE;
            }
            return a.getSort().compareTo(b.getSort());
        }).collect(Collectors.toList());
        if (geoMapLayerList.isEmpty()) {
            return new ArrayList<>();
        }
        //获取图层资源数据
        MPJLambdaWrapperX<GeoMapResource> queryWrapper = new MPJLambdaWrapperX<>();
        queryWrapper.selectAll(GeoMapResource.class);
        queryWrapper.selectAs(GeoMapLayerResource::getLayerId, GeoMapResourceVO::getLayerId);
        queryWrapper.selectAs(GeoMapLayerResource::getId, GeoMapResourceVO::getLayerResourceId);
        queryWrapper.rightJoin(GeoMapLayerResource.class, GeoMapLayerResource::getResourceId, GeoMapResource::getId);
        queryWrapper.orderByDesc(GeoMapLayerResource::getCreateTime);
        List<GeoMapResourceVO> geoMapResourceVOList = geoMapResourceService.selectJoinList(GeoMapResourceVO.class, queryWrapper);
        //获取图层资源下所有的API数据
        List<GeoMapLayerResourceApi> geoMapLayerResourceApiList = geoMapLayerResourceApiService.list();
        //查询图层资源下所有的图标资源
        MPJLambdaWrapperX<GeoMapMarker> markerQueryWrapper = new MPJLambdaWrapperX<>();
        markerQueryWrapper.selectAll(GeoMapMarker.class);
        markerQueryWrapper.selectAs(GeoMapResourceMarker::getResourceId, GeoMapMarkerVO::getResourceId);
        markerQueryWrapper.rightJoin(GeoMapResourceMarker.class, GeoMapResourceMarker::getMarkerId, GeoMapMarker::getId);
        List<GeoMapMarkerVO> markerVOList = geoMapMarkerService.selectJoinList(GeoMapMarkerVO.class, markerQueryWrapper);
        List<MapLayerTreeVO> list = geoMapLayerList.stream().map(a -> {
            MapLayerTreeVO mapLayerTreeVO = BeanUtils.copyProperties(a, MapLayerTreeVO.class);
            List<GeoMapResourceVO> source = new ArrayList<>();
            for (GeoMapResourceVO geoMapResourceVO : geoMapResourceVOList) {
                if (StrUtil.equals(geoMapResourceVO.getLayerId(), a.getId())) {
                    List<String> iqueryApis = new ArrayList<>();
                    for (GeoMapLayerResourceApi geoMapLayerResourceApi : geoMapLayerResourceApiList) {
                        if (StrUtil.equals(geoMapLayerResourceApi.getLayerResourceId(), geoMapResourceVO.getLayerResourceId())
                            && StrUtil.equals(geoMapLayerResourceApi.getResourceId(), geoMapResourceVO.getId())) {
                            iqueryApis.add(geoMapLayerResourceApi.getApiId());
                        }
                    }
                    geoMapResourceVO.setIQueryIdList(iqueryApis);

                    List<GeoMapMarkerVO> geoMapMarkerVOList = new ArrayList<>();
                    for (GeoMapMarkerVO geoMapMarkerVO : markerVOList) {
                        if (StrUtil.isNotBlank(geoMapMarkerVO.getResourceId())
                            && geoMapResourceVO.getId().equals(geoMapMarkerVO.getResourceId())) {
                            geoMapMarkerVOList.add(geoMapMarkerVO);
                        }
                    }
                    geoMapResourceVO.setGeoMapMarkerList(geoMapMarkerVOList);

                    source.add(geoMapResourceVO);
                }
            }

            mapLayerTreeVO.setGeoMapResourceVOList(source);
            return mapLayerTreeVO;
        }).collect(Collectors.toList());

        return TreeUtils.build(list, "0");
    }

    @Override
    public Map<String, CommonImportVO> importExcel(MultipartFile file) {
        InputStream inputStream = null;
        ExcelReader reader = null;
        List<Map<String, Object>> layerList = null;
        List<Map<String, Object>> resourceList = null;
        try {
            inputStream = file.getInputStream();
            reader = ExcelUtil.getReader(inputStream);
            reader.setSheet("图层");
            layerList = reader.readAll();
            reader.setSheet("图层资源");
            resourceList = reader.readAll();
        } catch (Exception e) {
            log.error("importInfo error", e);
        } finally {
            IoUtil.close(inputStream);
            IoUtil.close(reader);
        }
        AssertUtils.isTrue(CollUtil.isEmpty(layerList), ErrorCodeEnum.IMPORT_FILE_ERROR);
        CommonImportVO layerImportVO = this.importInfo(layerList);
        CommonImportVO resourceImportVO = geoMapResourceService.importInfo(resourceList);
        return MapUtil.builder(new HashMap<String, CommonImportVO>())
            .put("layer", layerImportVO)
            .put("resource", resourceImportVO)
            .build();
    }

    @Override
    @Transactional
    public void updateMapLayerSort(List<MapLayerSortBO> boList) {
        List<GeoMapLayer> geoMapLayerList = new ArrayList<>(boList.size());
        boList.forEach(bo -> {
            GeoMapLayer geoMapLayer = new GeoMapLayer();
            geoMapLayer.setId(bo.getId());
            geoMapLayer.setSort(bo.getSort());
            geoMapLayerList.add(geoMapLayer);
        });
        this.updateBatchById(geoMapLayerList);
    }

    @Override
    @Transactional
    public void updateMapLayerLevelSort(GeoMapLayerDragBO bo) {
        String layerId = bo.getLayerId();
        String parentId = bo.getParentId();
        GeoMapLayer geoMapLayer = baseMapper.selectById(layerId);
        AssertUtils.isTrue(geoMapLayer == null, "图层不存在");
        // AssertUtils.isTrue(MapLayerType.LAYER.equals(geoMapLayer.getType()) && CommonConstants.PARENT_ID_ROOT.equals(parentId), "图层资源不能移动到根节点");
        this.update(new LambdaUpdateWrapper<GeoMapLayer>().set(GeoMapLayer::getParentId, parentId).eq(GeoMapLayer::getId, layerId));
        //后将所有子节点的sort更新
        List<String> layerIds = bo.getLayerIds();
        if (CollUtil.isNotEmpty(layerIds)) {
            List<GeoMapLayer> geoMapLayerModelList = new ArrayList<>(layerIds.size());
            int index = 1;
            for (String id : layerIds) {
                GeoMapLayer geoMapLayerModelGrag = new GeoMapLayer();
                geoMapLayerModelGrag.setSort(index);
                geoMapLayerModelGrag.setId(id);
                geoMapLayerModelList.add(geoMapLayerModelGrag);
                index++;
            }
            this.updateBatchById(geoMapLayerModelList);
        }
    }

    @Override
    @Transactional
    public void deleteAll() {
        baseMapper.delete(new LambdaQueryWrapperX<>());
        //删除图层和图层资源的关联关系
        geoMapLayerResourceMapper.delete(new LambdaQueryWrapperX<>());
        //删除地图配置表图层数据
        List<GeoMapLayer> geoMapLayers = baseMapper.selectList();
        List<String> ids = geoMapLayers.stream().map(GeoMapLayer::getId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(ids)) {
            geoMapConfigMapper.delete(new LambdaQueryWrapperX<GeoMapConfig>().in(GeoMapConfig::getPrivilegeId, ids));
        }
    }

    @Override
    @Transactional
    public void copyMapLayer(String id, int copySource) {
        GeoMapLayer geoMapLayer = baseMapper.selectById(id);
        AssertUtils.isTrue(geoMapLayer == null, "图层不存在");
        GeoMapLayer newGeoMapLayer = new GeoMapLayer();
        BeanUtil.copyProperties(geoMapLayer, newGeoMapLayer);
        newGeoMapLayer.setId(null);
        newGeoMapLayer.setMapLayerName(geoMapLayer.getMapLayerName() + "-复制");
        newGeoMapLayer.setMapLayerCode(geoMapLayer.getMapLayerCode() + "-copy");
        this.save(newGeoMapLayer);
        if (copySource == 0) {
            //复制图层/图层资源关系
            List<GeoMapLayerResource> geoMapLayerResourceList = geoMapLayerResourceMapper.selectList(new LambdaQueryWrapperX<GeoMapLayerResource>().eq(GeoMapLayerResource::getLayerId, id));
            if (CollUtil.isNotEmpty(geoMapLayerResourceList)) {
                List<GeoMapLayerResource> newGeoMapLayerResourceList = new ArrayList<>(geoMapLayerResourceList.size());
                for (GeoMapLayerResource geoMapLayerResource : geoMapLayerResourceList) {
                    GeoMapLayerResource newGeoMapLayerResource = new GeoMapLayerResource();
                    BeanUtil.copyProperties(geoMapLayerResource, newGeoMapLayerResource);
                    newGeoMapLayerResource.setId(null);
                    newGeoMapLayerResource.setLayerId(newGeoMapLayer.getId());
                    newGeoMapLayerResourceList.add(newGeoMapLayerResource);
                }
                geoMapLayerResourceMapper.insertBatch(newGeoMapLayerResourceList);
            }
        } else {
            //复制图层资源
            MPJLambdaWrapperX<GeoMapResource> queryWrapper = new MPJLambdaWrapperX<>();
            queryWrapper.selectAll(GeoMapResource.class);
            queryWrapper.rightJoin(GeoMapLayerResource.class, GeoMapLayerResource::getResourceId, GeoMapResource::getId);
            queryWrapper.eq(GeoMapLayerResource::getLayerId, id);
            List<GeoMapResource> geoMapResourceList = geoMapResourceService.selectJoinList(GeoMapResource.class, queryWrapper);
            if (CollUtil.isNotEmpty(geoMapResourceList)) {
                for (GeoMapResource geoMapResource : geoMapResourceList) {
                    GeoMapResource newGeoMapResource = new GeoMapResource();
                    BeanUtil.copyProperties(geoMapResource, newGeoMapResource);
                    newGeoMapResource.setId(null);
                    newGeoMapResource.setMapResourceName(geoMapResource.getMapResourceName() + "-复制");
                    newGeoMapResource.setMapResourceCode("TCZY" + System.currentTimeMillis());
                    geoMapResourceService.save(newGeoMapResource);
                    GeoMapLayerResource newGeoMapLayerResource = new GeoMapLayerResource();
                    newGeoMapLayerResource.setLayerId(newGeoMapLayer.getId());
                    newGeoMapLayerResource.setResourceId(newGeoMapResource.getId());
                    geoMapLayerResourceMapper.insert(newGeoMapLayerResource);
                }
            }
        }

        //复制子级
        this.deepCopy(id, newGeoMapLayer.getId(), copySource);
    }

    @Override
    public void addLayerIQueryRef(List<GeoMapLayerResourceApi> geoMapLayerResourceApiList) {
        if (CollUtil.isEmpty(geoMapLayerResourceApiList)) {
            return;
        }
        List<GeoMapLayerResourceApi> list = new ArrayList<>(geoMapLayerResourceApiList.size());
        for (GeoMapLayerResourceApi geoMapLayerResourceApi : geoMapLayerResourceApiList) {
            long count = geoMapLayerResourceApiService.count(new LambdaQueryWrapperX<GeoMapLayerResourceApi>()
                .eq(GeoMapLayerResourceApi::getLayerResourceId, geoMapLayerResourceApi.getLayerResourceId())
                .eq(GeoMapLayerResourceApi::getResourceId, geoMapLayerResourceApi.getResourceId())
                .eq(GeoMapLayerResourceApi::getApiId, geoMapLayerResourceApi.getApiId()));
            if (count > 0) {
                continue;
            }
            list.add(geoMapLayerResourceApi);
        }
        geoMapLayerResourceApiService.saveBatch(list);
    }

    @Override
    public void collectMapLayer(GeoMapLayerFavouriteSaveBO bo) {
        String loginUserId = WebFrameworkUtils.getLoginUserId();
        if (CommonConstants.ONE == bo.getType()) {
            //收藏
            //判断是否已收藏
            long count = geoMapLayerFavouriteMapper.selectCount(new LambdaQueryWrapperX<GeoMapLayerFavourite>().eq(GeoMapLayerFavourite::getId, bo.getLayerId()).eq(GeoMapLayerFavourite::getCreateBy, loginUserId));
            if (count > 0) {
                return;
            }
            GeoMapLayerFavourite geoMapLayerFavourite = new GeoMapLayerFavourite();
            geoMapLayerFavourite.setLayerId(bo.getLayerId());
            geoMapLayerFavouriteMapper.insert(geoMapLayerFavourite);
        } else {
            //取消收藏
            geoMapLayerFavouriteMapper.delete(new LambdaQueryWrapperX<GeoMapLayerFavourite>().eq(GeoMapLayerFavourite::getLayerId, bo.getLayerId()));
        }

    }

    @Override
    public void delCollectMapLayer() {
        String loginUserId = WebFrameworkUtils.getLoginUserId();
        geoMapLayerFavouriteMapper.delete(new LambdaQueryWrapperX<GeoMapLayerFavourite>().eq(GeoMapLayerFavourite::getCreateBy, loginUserId));
    }

    @Override
    public void export(HttpServletResponse response, GeoMapLayerQueryBO bo) {
        String fileName = "图层模板导出_" + DateUtil.format(LocalDateTime.now(), PURE_DATETIME_PATTERN);
        List<List<Object>> layerList = new ArrayList<>();
        List<List<Object>> resourceList = new ArrayList<>();
        if (bo.getFlag()) {
            fileName = "图层数据导出_" + DateUtil.format(LocalDateTime.now(), PURE_DATETIME_PATTERN);
            List<MapLayerTreeVO> layerTree = this.getLayerTree(bo);
            fillExportInfo(layerTree, layerList, resourceList, "");
        }
        List<Object> headerRow = CollUtil
            .newArrayList("图层名称(必填)", "父级图层名称", "类型(1-分组,2-图层 必填)", "透明度(0-1)",
                "是否支持i查询(FALSE-否，TRUE-是)", "是否可见(FALSE-否，TRUE-是 必填)",
                "坐标系", "备注", "是否启用(0-否，1-是)");
        ExcelWriter writer = ExcelUtil.getWriter(true);
        writer.renameSheet("图层");
        writer.writeHeadRow(headerRow);
        for (List<Object> objects : layerList) {
            //移除第一个元素 id值
            objects.remove(0);
        }
        writer.write(layerList);
        List<Object> recourceRow = CollUtil
            .newArrayList("父级图层", "所属图层", "资源名称(必填)",
                "图层类型:group,tile,dynamic,wms,tpk,geojson,heatmap,cluster,wmts_tdt,vector(必填)",
                "要素类型:点,线,面",
                "服务类型:Mapmost,ArcGis", "Token", "资源url", "坐标系", "备注", "图层资源编码", "图层索引", "是否开启缓存 0-否，1-是", "过滤条件", "渲染属性(单个)");
        writer.setSheet("图层资源");
        writer.writeHeadRow(recourceRow);
        writer.write(resourceList);
        ExcelUtils.export(response, writer, fileName);
    }

    @Override
    public List<GeoMapLayerVO> getExportShpLayerList() {
        List<GeoMapLayer> geoMapLayerList = baseMapper.selectList(new LambdaQueryWrapperX<GeoMapLayer>()
            .eq(GeoMapLayer::getType, MapLayerType.LAYER)
            .orderByAsc(GeoMapLayer::getParentId)
            .orderByAsc(GeoMapLayer::getSort)
            .orderByAsc(GeoMapLayer::getCreateTime));
        //图层下，图层资源存在i查询则添加
        List<GeoMapLayerVO> geoMapLayerVOList = new ArrayList<>();
        for (GeoMapLayer geoMapLayer : geoMapLayerList) {
            GeoMapLayerVO geoMapLayerVO = BeanUtils.copyProperties(geoMapLayer, GeoMapLayerVO.class);
            //截取图层名称的最后一个#前面数据
            if (geoMapLayerVO.getMapLayerName().lastIndexOf("#") > 0) {
                geoMapLayerVO.setMapLayerName(geoMapLayerVO.getMapLayerName().substring(0, geoMapLayerVO.getMapLayerName().lastIndexOf("#")));
            }
            MPJLambdaWrapperX<GeoMapResource> queryWrapper = new MPJLambdaWrapperX<>();
            queryWrapper.selectAll(GeoMapResource.class);
            queryWrapper.selectAs(GeoMapLayerResource::getId, GeoMapResourceVO::getLayerResourceId);
            queryWrapper.selectAs(GeoMapLayerResource::getLayerId, GeoMapResourceVO::getLayerId);
            queryWrapper.leftJoin(GeoMapLayerResource.class, GeoMapLayerResource::getResourceId, GeoMapResource::getId);
            queryWrapper.eq(GeoMapLayerResource::getLayerId, geoMapLayer.getId());
            List<GeoMapResourceVO> geoMapResourceVOList = geoMapResourceService.selectJoinList(GeoMapResourceVO.class, queryWrapper);
            boolean flag = false;
            if (CollUtil.isNotEmpty(geoMapResourceVOList)) {
                for (GeoMapResourceVO geoMapResourceVO : geoMapResourceVOList) {
                    //通过图层/图层资源关联id 和图层id 查询 关联服务
                    List<GeoMapLayerResourceApi> geoMapLayerResourceApiList = geoMapLayerResourceApiService.list(new LambdaQueryWrapper<GeoMapLayerResourceApi>()
                        .eq(GeoMapLayerResourceApi::getLayerResourceId, geoMapResourceVO.getLayerResourceId())
                        .eq(GeoMapLayerResourceApi::getResourceId, geoMapResourceVO.getId()));
                    if (CollUtil.isNotEmpty(geoMapLayerResourceApiList)) {
                        flag = true;
                        break;
                    }
                }
            }
            if (flag) {
                geoMapLayerVOList.add(geoMapLayerVO);
            }
        }
        return geoMapLayerVOList;
    }

    private void fillExportInfo(List<MapLayerTreeVO> layerTree, List<List<Object>> layerList, List<List<Object>> resourceList, String parentName) {
        if (CollUtil.isEmpty(layerTree)) {
            return;
        }
        for (MapLayerTreeVO treeNode : layerTree) {
            String mapLayerName = treeNode.getMapLayerName();
            List<Object> maplayer = new ArrayList<>();
            maplayer.add(treeNode.getId());
            maplayer.add(mapLayerName);
            maplayer.add(parentName);
            maplayer.add(treeNode.getType());
            maplayer.add(treeNode.getOpacity());
            maplayer.add(treeNode.getSupportIQuery());
            maplayer.add(treeNode.getVisible());
            maplayer.add(treeNode.getProjection());
            maplayer.add(treeNode.getRemark());
            maplayer.add(Optional.ofNullable(treeNode.getStatus()).map(CommonStatusEnum::getCode).orElse(null));
            layerList.add(maplayer);
            fillExportResourceInfo(treeNode.getGeoMapResourceVOList(), resourceList, mapLayerName, parentName);
            List<MapLayerTreeVO> children = treeNode.getChildren();
            if (StrUtil.isNotEmpty(parentName)) {
                mapLayerName = parentName + "-" + mapLayerName;
            }
            fillExportInfo(children, layerList, resourceList, mapLayerName);
        }
    }

    private void fillExportResourceInfo(List<GeoMapResourceVO> geoMapResourceVOList, List<List<Object>> resourceList, String mapLayerName, String parentName) {
        if (CollUtil.isEmpty(geoMapResourceVOList)) {
            return;
        }
        for (GeoMapResourceVO geoMapResourceVO : geoMapResourceVOList) {
            List<Object> resource = new ArrayList<>();
            resource.add(parentName);
            resource.add(mapLayerName);
            resource.add(geoMapResourceVO.getMapResourceName());
            resource.add(geoMapResourceVO.getMapLayerType());
            resource.add(geoMapResourceVO.getGeometryType());
            resource.add(geoMapResourceVO.getServerType());
            resource.add(geoMapResourceVO.getTokenCode());
            resource.add(geoMapResourceVO.getServerUrl());
            resource.add(geoMapResourceVO.getSridType());
            resource.add(geoMapResourceVO.getRemark());
            resource.add(geoMapResourceVO.getMapResourceCode());
            resource.add(geoMapResourceVO.getLayersIndex());
            resource.add(geoMapResourceVO.getSourceCache());
            resource.add(geoMapResourceVO.getLayerDefs());
            resource.add(geoMapResourceVO.getExpandPro());
            resourceList.add(resource);
        }
    }

    private void deepCopy(String id, String parentId, int copySource) {
        List<GeoMapLayer> childList = baseMapper.selectList(new LambdaQueryWrapperX<GeoMapLayer>().eq(GeoMapLayer::getParentId, id));
        // 递归出口
        if (null == childList || childList.isEmpty()) {
            return;
        }

        for (GeoMapLayer layerModel : childList) {
            GeoMapLayer newGeoMapLayer = new GeoMapLayer();
            BeanUtil.copyProperties(layerModel, newGeoMapLayer);
            newGeoMapLayer.setId(null);
            newGeoMapLayer.setMapLayerName(layerModel.getMapLayerName() + "-复制");
            newGeoMapLayer.setMapLayerCode(layerModel.getMapLayerCode() == null ? "" : layerModel.getMapLayerCode() + "-copy");
            newGeoMapLayer.setParentId(parentId);
            this.save(newGeoMapLayer);
            if (copySource == 0) {
                //复制图层/图层资源关系
                List<GeoMapLayerResource> geoMapLayerResourceList = geoMapLayerResourceMapper.selectList(new LambdaQueryWrapperX<GeoMapLayerResource>().eq(GeoMapLayerResource::getLayerId, layerModel.getId()));
                if (CollUtil.isNotEmpty(geoMapLayerResourceList)) {
                    List<GeoMapLayerResource> newGeoMapLayerResourceList = new ArrayList<>(geoMapLayerResourceList.size());
                    for (GeoMapLayerResource geoMapLayerResource : geoMapLayerResourceList) {
                        GeoMapLayerResource newGeoMapLayerResource = new GeoMapLayerResource();
                        BeanUtil.copyProperties(geoMapLayerResource, newGeoMapLayerResource);
                        newGeoMapLayerResource.setId(null);
                        newGeoMapLayerResource.setLayerId(newGeoMapLayer.getId());
                        newGeoMapLayerResourceList.add(newGeoMapLayerResource);
                    }
                    geoMapLayerResourceMapper.insertBatch(newGeoMapLayerResourceList);
                }
            } else {
                //复制图层资源
                MPJLambdaWrapperX<GeoMapResource> queryWrapper = new MPJLambdaWrapperX<>();
                queryWrapper.selectAll(GeoMapResource.class);
                queryWrapper.rightJoin(GeoMapLayerResource.class, GeoMapLayerResource::getResourceId, GeoMapResource::getId);
                queryWrapper.eq(GeoMapLayerResource::getLayerId, layerModel.getId());
                List<GeoMapResource> geoMapResourceList = geoMapResourceService.selectJoinList(GeoMapResource.class, queryWrapper);
                if (CollUtil.isNotEmpty(geoMapResourceList)) {
                    for (GeoMapResource geoMapResource : geoMapResourceList) {
                        GeoMapResource newGeoMapResource = new GeoMapResource();
                        BeanUtil.copyProperties(geoMapResource, newGeoMapResource);
                        newGeoMapResource.setId(null);
                        newGeoMapResource.setMapResourceName(geoMapResource.getMapResourceName() + "-复制");
                        newGeoMapResource.setMapResourceCode("TCZY" + System.currentTimeMillis());
                        geoMapResourceService.save(newGeoMapResource);
                        GeoMapLayerResource newGeoMapLayerResource = new GeoMapLayerResource();
                        newGeoMapLayerResource.setLayerId(newGeoMapLayer.getId());
                        newGeoMapLayerResource.setResourceId(newGeoMapResource.getId());
                        geoMapLayerResourceMapper.insert(newGeoMapLayerResource);
                    }
                }
            }

            //复制子级
            this.deepCopy(layerModel.getId(), newGeoMapLayer.getId(), copySource);
        }

    }

    private CommonImportVO importInfo(List<Map<String, Object>> layerList) {
        List<Map<String, Object>> errorRecords = new ArrayList<>();
        long successTotal = 0;
        long errorTotal = 0;
        if (CollUtil.isNotEmpty(layerList)) {
            List<MapLayerTreeVO> mapLayerTreeVOS = new ArrayList<>();
            List<MapLayerTreeVO> mapLayerTreeVOList = new ArrayList<>();
            for (Map<String, Object> dataMap : layerList) {
                String mapLayerName = MapUtil.getStr(dataMap, "图层名称(必填)");
                String parentLayerName = MapUtil.getStr(dataMap, "父级图层名称");
                String type = MapUtil.getStr(dataMap, "类型(1-分组,2-图层 必填)");
                Double opacity = MapUtil.getDouble(dataMap, "透明度(0-1)");
                Boolean supportIQuery = MapUtil.getBool(dataMap, "是否支持i查询(FALSE-否，TRUE-是)");
                Boolean visible = MapUtil.getBool(dataMap, "是否可见(FALSE-否，TRUE-是 必填)");
                String projection = MapUtil.getStr(dataMap, "坐标系");
                String remark = MapUtil.getStr(dataMap, "备注");
                Integer inputStatus = MapUtil.getInt(dataMap, "是否启用(0-否，1-是)");
                StringBuilder sb = new StringBuilder();
                if (StrUtil.isBlank(mapLayerName)) {
                    sb.append("图层名称不能为空;");
                }
                if (StrUtil.isBlank(type)) {
                    sb.append("类型不能为空;");
                }
                if (MapLayerType.GROUP.equals(type)) {
                    Long selectCount = baseMapper.selectCount(new LambdaQueryWrapperX<GeoMapLayer>().eq(GeoMapLayer::getMapLayerName, mapLayerName).eq(GeoMapLayer::getType, type));
                    if (selectCount > 0) {
                        sb.append("图层分组名称已存在");
                    }
                }
                if (visible == null) {
                    sb.append("是否可见不能为空;");
                }
                if (StrUtil.isNotBlank(sb)) {
                    errorTotal++;
                    dataMap.put("错误提示", sb);
                    errorRecords.add(dataMap);
                    continue;
                }
                CommonStatusEnum status = null;
                if (ObjectUtil.isNotEmpty(inputStatus)) {
                    status = EnumUtils.getEnumByValue(CommonStatusEnum.class, inputStatus);
                    if (ObjectUtil.isNull(status)) {
                        sb.append("状态不正确;");
                    }
                }
                //转换maplayertree对象后处理
                if (MapLayerType.GROUP.equals(type)) {
                    MapLayerTreeVO mapLayerTreeVO = new MapLayerTreeVO();
                    mapLayerTreeVO.setMapLayerName(mapLayerName);
                    mapLayerTreeVO.setId(mapLayerName);
                    if (StrUtil.isNotBlank(parentLayerName)) {
                        mapLayerTreeVO.setParentId(parentLayerName);
                        mapLayerTreeVO.setId(parentLayerName + "-" + mapLayerName);
                    } else {
                        mapLayerTreeVO.setParentId(CommonConstants.PARENT_ID_ROOT);
                    }
                    mapLayerTreeVO.setType(type);
                    mapLayerTreeVO.setOpacity(opacity);
                    mapLayerTreeVO.setSupportIQuery(supportIQuery);
                    mapLayerTreeVO.setVisible(visible);
                    mapLayerTreeVO.setProjection(projection);
                    mapLayerTreeVO.setRemark(remark);
                    mapLayerTreeVO.setStatus(status);
                    mapLayerTreeVOS.add(mapLayerTreeVO);
                } else {
                    MapLayerTreeVO mapLayerTreeVO = new MapLayerTreeVO();
                    mapLayerTreeVO.setMapLayerName(mapLayerName);
                    mapLayerTreeVO.setId(mapLayerName);
                    if (StrUtil.isNotBlank(parentLayerName)) {
                        mapLayerTreeVO.setParentId(parentLayerName);
                    } else {
                        mapLayerTreeVO.setParentId(CommonConstants.PARENT_ID_ROOT);
                    }
                    mapLayerTreeVO.setType(type);
                    mapLayerTreeVO.setOpacity(opacity);
                    mapLayerTreeVO.setSupportIQuery(supportIQuery);
                    mapLayerTreeVO.setVisible(visible);
                    mapLayerTreeVO.setProjection(projection);
                    mapLayerTreeVO.setRemark(remark);
                    mapLayerTreeVO.setStatus(status);
                    mapLayerTreeVOList.add(mapLayerTreeVO);
                }
                successTotal++;
            }

            if (CollUtil.isNotEmpty(mapLayerTreeVOS)) {
                //判断是否存在重复的图层名称
                List<String> mapLayerNames = mapLayerTreeVOS.stream().map(MapLayerTreeVO::getId)
                    .collect(Collectors.toList());
                List<String> collect = mapLayerNames.stream()
                    .collect(Collectors.groupingBy(Function.identity(), Collectors.counting())).entrySet().stream()
                    .filter(e -> e.getValue() > 1).map(Map.Entry::getKey).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(collect)) {
                    throw new BusinessException("分组名称重复:【" + collect + "】,无法导入!");
                }
                //判断父级图层是否存在
                List<String> parentLayerNames = mapLayerTreeVOS.stream().map(MapLayerTreeVO::getParentId).collect(Collectors.toList());
                mapLayerTreeVOList.stream().map(MapLayerTreeVO::getParentId).forEach(parentLayerNames::add);
                for (String parentLayerName : parentLayerNames) {
                    if (!CommonConstants.PARENT_ID_ROOT.equals(parentLayerName)) {
                        if (!mapLayerNames.contains(parentLayerName)) {
                            throw new BusinessException("父级图层名称:【" + parentLayerName + "】不存在,无法导入!");
                        }
                    }
                }
                List<MapLayerTreeVO> build = TreeUtils.build(mapLayerTreeVOS, CommonConstants.PARENT_ID_ROOT);
                //将图层设置到分组中
                for (MapLayerTreeVO mapLayerTreeVO : build) {
                    if (CollUtil.isNotEmpty(mapLayerTreeVO.getChildren())) {
                        addChildLayer(mapLayerTreeVO.getChildren(), mapLayerTreeVOList);
                    }
                    for (MapLayerTreeVO layerTreeVO : mapLayerTreeVOList) {
                        if (mapLayerTreeVO.getId().equals(layerTreeVO.getParentId())) {
                            mapLayerTreeVO.getChildren().add(layerTreeVO);
                        }
                    }
                }
                //如果图层是首层的则添加
                for (MapLayerTreeVO layerTreeVO : mapLayerTreeVOList) {
                    if (CommonConstants.PARENT_ID_ROOT.equals(layerTreeVO.getParentId())) {
                        build.add(layerTreeVO);
                    }
                }
                int i = 0;
                for (MapLayerTreeVO mapLayerTreeVO : build) {
                    String uuid = IdUtil.fastSimpleUUID();
                    mapLayerTreeVO.setId(uuid);
                    if (CollUtil.isNotEmpty(mapLayerTreeVO.getChildren())) {
                        addChildrenInitLayer(mapLayerTreeVO.getChildren(), uuid, false, new ArrayList<>());
                    }
                    GeoMapLayer convert = GeoMapLayerConvert.INSTANCE.convert(mapLayerTreeVO);
                    convert.setSort(i++);
                    this.save(convert);
                }
            } else {
                successTotal = 0;
                errorTotal = layerList.size();
            }

        }
        return CommonImportVO.builder().successTotal(successTotal).errorTotal(errorTotal).errorRecords(errorRecords)
            .build();
    }

    private void addChildrenInitLayer(List<MapLayerTreeVO> children, String uuid, boolean isAdd, ArrayList<Object> layerIds) {
        int i = 0;
        for (MapLayerTreeVO treeNode : children) {
            String myuuid = IdUtil.fastSimpleUUID();
            treeNode.setId(myuuid);
            layerIds.add(myuuid);
            treeNode.setParentId(uuid);
            if (CollUtil.isNotEmpty(treeNode.getChildren())) {
                addChildrenInitLayer(treeNode.getChildren(), myuuid, isAdd, layerIds);
            }
            GeoMapLayer geoMapLayer = GeoMapLayerConvert.INSTANCE.convert(treeNode);
            geoMapLayer.setSort(i++);
            if (isAdd) {
                if (geoMapLayer.getMapLayerName().equals(OnemapConstants.IMAGE)) {
                    List<GeoMapResource> imagelist = geoMapResourceService.list(new LambdaQueryWrapperX<GeoMapResource>()
                        .in(GeoMapResource::getMapResourceName, OnemapConstants.COUNTRY_TIANDITU_IMAGE, OnemapConstants.COUNTRY_TIANDITU_IMAGE_NOTE));
                    if (CollUtil.isNotEmpty(imagelist)) {
                        for (GeoMapResource geoMapResource : imagelist) {
                            GeoMapLayerResource geoMapLayerResource = new GeoMapLayerResource();
                            geoMapLayerResource.setLayerId(geoMapLayer.getId());
                            geoMapLayerResource.setResourceId(geoMapResource.getId());
                            if (OnemapConstants.COUNTRY_TIANDITU_IMAGE.equals(geoMapResource.getMapResourceName())) {
                                geoMapLayerResource.setSort(1);
                            } else if (OnemapConstants.COUNTRY_TIANDITU_IMAGE_NOTE.equals(geoMapResource.getMapResourceName())) {
                                geoMapLayerResource.setSort(2);
                            }
                            geoMapLayerResourceMapper.insert(geoMapLayerResource);
                        }

                    }
                }
                if (geoMapLayer.getMapLayerName().equals(OnemapConstants.MAP)) {
                    List<GeoMapResource> imagelist = geoMapResourceService.list(new LambdaQueryWrapperX<GeoMapResource>()
                            .in(GeoMapResource::getMapResourceName, OnemapConstants.COUNTRY_TIANDITU_MAP, OnemapConstants.COUNTRY_TIANDITU_MAP_NOTE));
                    if (CollUtil.isNotEmpty(imagelist)) {
                        for (GeoMapResource geoMapResource : imagelist) {
                            GeoMapLayerResource geoMapLayerResource = new GeoMapLayerResource();
                            geoMapLayerResource.setLayerId(geoMapLayer.getId());
                            geoMapLayerResource.setResourceId(geoMapResource.getId());
                            if (OnemapConstants.COUNTRY_TIANDITU_MAP.equals(geoMapResource.getMapResourceName())) {
                                geoMapLayerResource.setSort(1);
                            } else if (OnemapConstants.COUNTRY_TIANDITU_MAP_NOTE.equals(geoMapResource.getMapResourceName())) {
                                geoMapLayerResource.setSort(2);
                            }
                            geoMapLayerResourceMapper.insert(geoMapLayerResource);
                        }

                    }
                }
            }
            baseMapper.insert(geoMapLayer);
        }
    }

    private void addChildLayer(List<MapLayerTreeVO> children, List<MapLayerTreeVO> mapLayerTreeVOList) {
        for (MapLayerTreeVO mapLayerTreeVO : children) {
            if (CollUtil.isNotEmpty(mapLayerTreeVO.getChildren())) {
                addChildLayer(mapLayerTreeVO.getChildren(), mapLayerTreeVOList);
            }
            for (MapLayerTreeVO layerTreeVO : mapLayerTreeVOList) {
                if (mapLayerTreeVO.getId().equals(layerTreeVO.getParentId())) {
                    mapLayerTreeVO.getChildren().add(layerTreeVO);
                }
            }
        }
    }

    private List<GeoMapLayer> searchLayerTree(List<GeoMapLayer> geoMapLayerList, GeoMapLayerQueryBO bo) {
        List<GeoMapLayer> allList = CollUtil.newCopyOnWriteArrayList(geoMapLayerList);
        if (StrUtil.isNotEmpty(bo.getMapLayerName())) {
            geoMapLayerList.removeIf(a -> !a.getMapLayerName().contains(bo.getMapLayerName()));
        }
        if (ObjectUtil.isNotEmpty(bo.getVisible())) {
            geoMapLayerList.removeIf(a -> !StrUtil.toString(a.getVisible()).equals(bo.getVisible().toString()));
        }
        if (ObjectUtil.isNotEmpty(bo.getSupportIQuery())) {
            geoMapLayerList.removeIf(a -> !StrUtil.toString(a.getSupportIQuery()).equals(bo.getSupportIQuery().toString()));
            geoMapLayerList.removeIf(a -> a.getSupportIQuery() == null);
        }
        return findParentLayer(geoMapLayerList, allList);
    }

    private List<GeoMapLayer> findParentLayer(List<GeoMapLayer> geoMapLayerList, List<GeoMapLayer> allList) {
        List<GeoMapLayer> outmodel = CollUtil.newCopyOnWriteArrayList(geoMapLayerList);
        //获取列表都有id集合
        List<String> ids = geoMapLayerList.stream().map(GeoMapLayer::getId).collect(Collectors.toList());
        for (GeoMapLayer layerModel : geoMapLayerList) {
            if (StrUtil.equals(layerModel.getParentId(), "0")) {
                continue;
            }
            if (!ids.contains(layerModel.getParentId())) {
                GeoMapLayer geoMapLayer = allList.stream()
                    .filter(a -> StrUtil.equals(a.getId(), layerModel.getParentId())).findFirst().orElse(null);
                if (geoMapLayer == null) {
                    continue;
                }
                outmodel.add(geoMapLayer);
                findParentLayer(outmodel, allList);
            }
        }
        //数组中id重复的删除
        outmodel = outmodel.stream().collect(
            Collectors.collectingAndThen(
                Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(GeoMapLayer::getId))),
                ArrayList::new));
        geoMapLayerList = outmodel;
        return geoMapLayerList;
    }

}
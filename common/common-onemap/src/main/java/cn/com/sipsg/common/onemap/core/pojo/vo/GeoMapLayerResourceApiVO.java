package cn.com.sipsg.common.onemap.core.pojo.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fhs.core.trans.vo.VO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 图层关联图层资源/查询服务关系表VO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "图层关联图层资源/查询服务关系表VO")
public class GeoMapLayerResourceApiVO implements VO {

    /**
     * 主键
     */
    @TableId("id")
    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED)
    private String id;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

    /**
     * 图层关联图层资源ID
     */
    @Schema(description = "图层关联图层资源ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private String layerResourceId;

    /**
     * 查询服务ID
     */
    @Schema(description = "查询服务ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private String apiId;

    /**
     * 层资源ID
     */
    @Schema(description = "层资源ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private String resourceId;

}
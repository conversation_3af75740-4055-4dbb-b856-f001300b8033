package cn.com.sipsg.common.onemap.core.service.impl;

import cn.com.sipsg.common.exception.enums.ErrorCodeEnum;
import cn.com.sipsg.common.mybatis.core.query.LambdaQueryWrapperX;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.util.AssertUtils;
import cn.com.sipsg.common.util.BeanUtils;
import cn.com.sipsg.common.onemap.core.entity.GeoMapLayerFavourite;
import cn.com.sipsg.common.onemap.core.mapper.GeoMapLayerFavouriteMapper;
import cn.com.sipsg.common.onemap.core.pojo.bo.GeoMapLayerFavouriteQueryBO;
import cn.com.sipsg.common.onemap.core.pojo.bo.GeoMapLayerFavouriteSaveBO;
import cn.com.sipsg.common.onemap.core.pojo.vo.GeoMapLayerFavouriteVO;
import cn.com.sipsg.common.onemap.core.service.GeoMapLayerFavouriteService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * 图层收藏表服务实现类
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Service
@RequiredArgsConstructor
public class GeoMapLayerFavouriteServiceImpl extends ServiceImpl<GeoMapLayerFavouriteMapper, GeoMapLayerFavourite> implements GeoMapLayerFavouriteService {

    @Override
    public CommonPageVO<GeoMapLayerFavouriteVO> page(GeoMapLayerFavouriteQueryBO bo) {
        LambdaQueryWrapperX<GeoMapLayerFavourite> queryWrapper = new LambdaQueryWrapperX<>();
        // 填充查询条件
        return this.page(bo, queryWrapper).convert(entity -> BeanUtils.copyProperties(entity, GeoMapLayerFavouriteVO.class));
    }

    @Override
    @Transactional
    public String save(GeoMapLayerFavouriteSaveBO bo) {
        bo.setId(null);
        // 新增图层收藏表
        GeoMapLayerFavourite geoMapLayerFavourite = BeanUtils.copyProperties(bo, GeoMapLayerFavourite.class);
        this.save(geoMapLayerFavourite);
        return geoMapLayerFavourite.getId();
    }

    @Override
    @Transactional
    public void update(GeoMapLayerFavouriteSaveBO bo) {
        // 编辑图层收藏表
        GeoMapLayerFavourite geoMapLayerFavourite = BeanUtils.copyProperties(bo, GeoMapLayerFavourite.class);
        this.updateById(geoMapLayerFavourite);
    }

    @Override
    public GeoMapLayerFavouriteVO detail(String id) {
        GeoMapLayerFavourite geoMapLayerFavourite = this.getById(id);
        // 校验是否存在
        AssertUtils.isTrue(geoMapLayerFavourite == null, ErrorCodeEnum.DATA_NOT_EXIST);
        return BeanUtils.copyProperties(geoMapLayerFavourite, GeoMapLayerFavouriteVO.class);
    }

    @Override
    @Transactional
    public void delete(String id) {
        GeoMapLayerFavourite geoMapLayerFavourite = this.getById(id);
        if (geoMapLayerFavourite != null) {
            // 删除图层收藏表
            this.removeById(id);
        }
    }

}
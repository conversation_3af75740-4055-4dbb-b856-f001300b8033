package cn.com.sipsg.common.onemap.core.pojo.bo;

import cn.com.sipsg.common.pojo.bo.SortablePageBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 地图范围查询BO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "地图范围查询BO")
public class GeoMapExtentQueryBO extends SortablePageBO {

    /**
     * 地图范围编码
     */
    @Schema(description = "地图范围编码")
    private String mapExtentCode;

    /**
     * 地图范围名称
     */
    @Schema(description = "地图范围名称")
    private String mapExtentName;

    /**
     * 类型：1：中心点和层级，2：范围
     */
    @Schema(description = "类型：1：中心点和层级，2：范围")
    private String type;

}
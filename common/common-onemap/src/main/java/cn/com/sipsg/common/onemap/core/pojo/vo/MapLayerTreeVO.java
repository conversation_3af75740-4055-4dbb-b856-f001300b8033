package cn.com.sipsg.common.onemap.core.pojo.vo;

import cn.com.sipsg.common.enums.CommonStatusEnum;
import cn.com.sipsg.common.pojo.TreeNode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * 图层树
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@Schema(description = "图层树")
public class MapLayerTreeVO extends TreeNode<MapLayerTreeVO> {

    /**
     * 主键id
     */
    @Schema(description = "id")
    private String id;

    /**
     * 父级ID
     */
    @Schema(description = "父级ID")
    private String parentId;

    /**
     * 类型：1-分组；2-图层
     */
    @Schema(description = "类型：1-分组；2-图层")
    private String type;

    /**
     * 图层编码
     */
    @Schema(description = "图层编码")
    private String mapLayerCode;

    /**
     * 图层名称
     */
    @Schema(description = "图层名称")
    private String mapLayerName;

    /**
     * 图标
     */
    @Schema(description = "图标")
    private String iconSrc;

    /**
     * 排序值
     */
    @Schema(description = "排序值")
    private Integer sort;

    /**
     * 是否支持i查询0-否，1-是
     */
    @Schema(description = "是否支持i查询0-否，1-是")
    private Boolean supportIQuery;

    /**
     * 是否支持查询0-否，1-是
     */
    @Schema(description = "仅置顶显示0-否，1-是")
    private Boolean onlyShowTop;

    /**
     * 是否可见 0-否，1-是
     */
    @Schema(description = "是否可见 0-否，1-是")
    private Boolean visible;

    /**
     * 是否展开 0-否，1-是
     */
    @Schema(description = "是否展开 0-否，1-是")
    private Boolean expand;

    /**
     * 是否组内可选0-否，1-是
     */
    @Schema(description = "是否组内可选0-否，1-是")
    private Boolean groupCheck;

    /**
     * 是否为新数据 0-否，1-是
     */
    @Schema(description = "是否为新数据 0-否，1-是")
    private Boolean newData;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 是否启用0-否，1-是
     */
    @Schema(description = "是否启用0-否，1-是")
    private CommonStatusEnum status;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private String createTime;

    /**
     * 修改人
     */
    @Schema(description = "修改人")
    private String updateBy;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    private String updateTime;

    /**
     * 删除标志 0-否，1-是
     */
    @Schema(description = "删除标志 0-否，1-是")
    private Integer delFlag;

    /**
     * 图层范围
     */
    @Schema(description = "图层范围")
    private String layerExtent;

    /**
     * 图例
     */
    @Schema(description = "图例")
    private String legend;

    /**
     * 不透明度
     */
    @Schema(description = "不透明度")
    private Double opacity;

    /**
     * 导出服务编码
     */
    @Schema(description = "导出服务编码")
    private String exportServerCode;

    /**
     * 叠置分析服务
     */
    @Schema(description = "叠置分析服务")
    private String overlayServerCode;

    /**
     * 坐标转换
     */
    @Schema(description = "坐标转换")
    private String coordinateTransform;

    /**
     * 投影坐标
     */
    @Schema(description = "投影坐标")
    private String projection;

    /**
     * 图层资源
     */
    @Schema(description = "图层资源")
    private List<GeoMapResourceVO> geoMapResourceVOList;
}
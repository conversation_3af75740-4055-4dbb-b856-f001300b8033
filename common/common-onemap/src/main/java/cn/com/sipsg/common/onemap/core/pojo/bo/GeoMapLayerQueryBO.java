package cn.com.sipsg.common.onemap.core.pojo.bo;

import cn.com.sipsg.common.pojo.bo.SortablePageBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 图层表查询BO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "图层表查询BO")
public class GeoMapLayerQueryBO extends SortablePageBO {

    /**
     * 图层编码
     */
    @Schema(description = "图层编码")
    private String mapLayerCode;

    /**
     * 图层名称
     */
    @Schema(description = "图层名称")
    private String mapLayerName;

    /**
     * 是否支持i查询
     */
    @Schema(description = "是否支持i查询")
    private Boolean supportIQuery;

    /**
     * 是否可见
     */
    @Schema(description = "是否可见")
    private Boolean visible;

    /**
     * 是否导出数据
     */
    @Schema(description = "是否导出数据")
    private Boolean flag;

    /**
     * 是否开启影像对比
     */
    @Schema(description = "是否开启影像对比")
    private Boolean imageCompare;

}
package cn.com.sipsg.common.onemap.core.entity;

import cn.com.sipsg.common.enums.CommonStatusEnum;
import cn.com.sipsg.common.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.OrderBy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;


/**
 * 地图工具表
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@FieldNameConstants
@TableName(value = "medox_onemap_geo_map_tool", autoResultMap = true)
public class GeoMapTool extends BaseDO {

    /**
     * 主键
     */
    @TableId("id")
    private String id;

    /**
     * 父级ID
     */
    @TableField("parent_id")
    private String parentId;

    /**
     * 地图工具编码
     */
    @TableField("map_tool_code")
    private String mapToolCode;

    /**
     * 地图工具名称
     */
    @TableField("map_tool_name")
    private String mapToolName;

    /**
     * 地图工具类型
     */
    @TableField("map_tool_type")
    private String mapToolType;

    /**
     * 地图工具url
     */
    @TableField("map_tool_url")
    private String mapToolUrl;

    /**
     * 排序值
     */
    @TableField("sort")
    @OrderBy(asc = true, sort = Short.MIN_VALUE)
    private Integer sort;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 是否启用;0-否，1-是
     */
    @TableField("status")
    private CommonStatusEnum status;

    /**
     * 分组
     */
    @TableField("group_name")
    private String groupName;

}
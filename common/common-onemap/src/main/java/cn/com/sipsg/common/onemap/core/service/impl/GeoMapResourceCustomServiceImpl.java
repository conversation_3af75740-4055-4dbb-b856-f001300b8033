package cn.com.sipsg.common.onemap.core.service.impl;

import cn.com.sipsg.common.exception.enums.ErrorCodeEnum;
import cn.com.sipsg.common.mybatis.core.query.LambdaQueryWrapperX;
import cn.com.sipsg.common.mybatis.core.query.MPJLambdaWrapperX;
import cn.com.sipsg.common.onemap.core.entity.GeoMapLayer;
import cn.com.sipsg.common.onemap.core.entity.GeoMapLayerResource;
import cn.com.sipsg.common.onemap.core.entity.GeoMapLayerResourceApi;
import cn.com.sipsg.common.onemap.core.entity.GeoMapMarker;
import cn.com.sipsg.common.onemap.core.entity.GeoMapResource;
import cn.com.sipsg.common.onemap.core.entity.GeoMapResourceCustom;
import cn.com.sipsg.common.onemap.core.entity.GeoMapResourceMarker;
import cn.com.sipsg.common.onemap.core.entity.GeoMapResourceStyle;
import cn.com.sipsg.common.onemap.core.mapper.GeoMapLayerMapper;
import cn.com.sipsg.common.onemap.core.mapper.GeoMapLayerResourceMapper;
import cn.com.sipsg.common.onemap.core.mapper.GeoMapMarkerMapper;
import cn.com.sipsg.common.onemap.core.mapper.GeoMapResourceCustomMapper;
import cn.com.sipsg.common.onemap.core.mapper.GeoMapResourceStyleMapper;
import cn.com.sipsg.common.onemap.core.pojo.bo.GeoMapResourceCustomQueryBO;
import cn.com.sipsg.common.onemap.core.pojo.bo.GeoMapResourceCustomSaveBO;
import cn.com.sipsg.common.onemap.core.pojo.vo.GeoMapLayerVO;
import cn.com.sipsg.common.onemap.core.pojo.vo.GeoMapMarkerVO;
import cn.com.sipsg.common.onemap.core.pojo.vo.GeoMapResourceCustomVO;
import cn.com.sipsg.common.onemap.core.pojo.vo.GeoMapResourceVO;
import cn.com.sipsg.common.onemap.core.service.GeoMapLayerResourceApiService;
import cn.com.sipsg.common.onemap.core.service.GeoMapResourceCustomService;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.util.AssertUtils;
import cn.com.sipsg.common.util.BeanUtils;
import cn.com.sipsg.common.web.core.util.WebFrameworkUtils;
import cn.com.sipsg.module.system.api.upms.SysFileApi;
import cn.com.sipsg.module.system.api.upms.dto.SysFileRespDTO;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * 图层资源表_自定义服务实现类
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Service
@RequiredArgsConstructor
public class GeoMapResourceCustomServiceImpl extends ServiceImpl<GeoMapResourceCustomMapper, GeoMapResourceCustom> implements GeoMapResourceCustomService {

    private static final String GEO_MAP_RESOURCE_CUSTOM = "geoMapResourceCustom";

    private static final String PARENT_ID = "customsipsg123456";

    private final SysFileApi fileApi;

    private final GeoMapResourceStyleMapper geoMapResourceStyleMapper;

    private final GeoMapMarkerMapper geoMapMarkerMapper;

    private final GeoMapLayerMapper geoMapLayerMapper;

    private final GeoMapLayerResourceMapper geoMapLayerResourceMapper;

    private final GeoMapLayerResourceApiService geoMapLayerResourceApiService;

    @Override
    public CommonPageVO<GeoMapResourceCustomVO> page(GeoMapResourceCustomQueryBO bo) {
        LambdaQueryWrapperX<GeoMapResourceCustom> queryWrapper = new LambdaQueryWrapperX<>();
        // 填充查询条件
        return this.page(bo, queryWrapper).convert(entity -> BeanUtils.copyProperties(entity, GeoMapResourceCustomVO.class));
    }

    @Override
    @Transactional
    public GeoMapLayerVO save(GeoMapResourceCustomSaveBO bo) {
        bo.setId(null);
        //判断图层资源编码不重复
        LambdaQueryWrapperX<GeoMapResourceCustom> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eq(GeoMapResourceCustom::getMapResourceCode, bo.getMapResourceCode());
        long count = this.count(queryWrapper);
        AssertUtils.isTrue(count > 0, "图层资源编码已存在!");
        if (StrUtil.isEmpty(bo.getMapResourceCode())) {
            bo.setMapResourceCode("TCZY" + System.currentTimeMillis());
        }
        GeoMapResourceCustom geoMapResourceCustom = BeanUtils.copyProperties(bo, GeoMapResourceCustom.class);
        if (bo.getFile() != null) {
            // 上传文件的保存
            SysFileRespDTO sysFileRespDTO = fileApi.upload(bo.getFile(), GEO_MAP_RESOURCE_CUSTOM);
            geoMapResourceCustom.setFileId(sysFileRespDTO.getId());
            geoMapResourceCustom.setServerUrl(sysFileRespDTO.getUrl());
        }
        // 新增图层资源表_自定义
        this.save(geoMapResourceCustom);
        List<String> expandProApis = new ArrayList<>();
        if (StringUtils.isNotEmpty(bo.getExpandPro())) {
            JSONObject expandProJson = JSONObject.parseObject(bo.getExpandPro());
            String layers = expandProJson.getString("layers");
            if (StringUtils.isNotEmpty(layers)) {
                JSONArray layersJson = JSONUtil.parseArray(layers);
                if (CollUtil.isNotEmpty(layersJson)) {
                    for (Object o : layersJson) {
                        String s = o.toString();
                        expandProApis.add(s);
                    }
                }
            }
        }
        if (CollUtil.isNotEmpty(expandProApis)) {
            expandProApis.forEach(expandProApi -> {
                GeoMapResourceStyle geoMapLayerStyle = new GeoMapResourceStyle();
                geoMapLayerStyle.setResourceId(geoMapResourceCustom.getId());
                geoMapLayerStyle.setExpandPro(expandProApi);
                geoMapResourceStyleMapper.insert(geoMapLayerStyle);
            });
        }
        GeoMapLayerVO geoMapLayerVO = new GeoMapLayerVO();
        List<GeoMapResourceVO> resourceVOList = new ArrayList<>();
        geoMapLayerVO.setId(geoMapResourceCustom.getId());
        geoMapLayerVO.setMapLayerName(geoMapResourceCustom.getMapResourceName());
        GeoMapResourceVO resourceVO = new GeoMapResourceVO();
        BeanUtil.copyProperties(geoMapResourceCustom, resourceVO);
        //通过resource_layer_id查询出对应的扩展属性
        List<Map<String, Object>> expandProList = new ArrayList<>();
        List<GeoMapResourceStyle> geoMapResourceStyleList = geoMapResourceStyleMapper.selectList(new LambdaQueryWrapperX<GeoMapResourceStyle>()
            .eq(GeoMapResourceStyle::getResourceId, geoMapResourceCustom.getId()));
        if (CollUtil.isNotEmpty(geoMapResourceStyleList)) {
            geoMapResourceStyleList.forEach(geoMapLayerStyle -> {
                Map<String, Object> map = new HashMap<>();
                map.put("id", geoMapLayerStyle.getId());
                map.put("expandPro", geoMapLayerStyle.getExpandPro());
                expandProList.add(map);
            });
            resourceVO.setExpandProList(expandProList);
        } else {
            resourceVO.setExpandProList(null);
        }
        resourceVOList.add(resourceVO);
        geoMapLayerVO.setParentId(PARENT_ID);
        geoMapLayerVO.setMapResourceList(resourceVOList);
        return geoMapLayerVO;
    }

    @Override
    @Transactional
    public GeoMapLayerVO update(GeoMapResourceCustomSaveBO bo) {
        // 编辑图层资源表_自定义
        GeoMapResourceCustom geoMapResourceCustom = BeanUtils.copyProperties(bo, GeoMapResourceCustom.class);
        //判断图层资源编码不重复
        LambdaQueryWrapperX<GeoMapResourceCustom> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eq(GeoMapResourceCustom::getMapResourceCode, bo.getMapResourceCode());
        queryWrapper.neIfPresent(GeoMapResourceCustom::getId, bo.getId());
        long count = this.count(queryWrapper);
        AssertUtils.isTrue(count > 0, "图层资源编码已存在!");
        if (StrUtil.isEmpty(bo.getMapResourceCode())) {
            bo.setMapResourceCode("TCZY" + System.currentTimeMillis());
        }
        geoMapResourceStyleMapper.delete(new LambdaQueryWrapperX<GeoMapResourceStyle>().eq(GeoMapResourceStyle::getResourceId, bo.getId()));
        this.updateById(geoMapResourceCustom);
        List<String> expandProApis = new ArrayList<>();
        if (StringUtils.isNotEmpty(bo.getExpandPro())) {
            JSONObject expandProJson = JSONObject.parseObject(bo.getExpandPro());
            String layers = expandProJson.getString("layers");
            if (StringUtils.isNotEmpty(layers)) {
                JSONArray layersJson = JSONUtil.parseArray(layers);
                if (!layersJson.isEmpty()) {
                    for (Object o : layersJson) {
                        String s = o.toString();
                        expandProApis.add(s);
                    }
                }
            }
        }
        if (CollUtil.isNotEmpty(expandProApis)) {
            expandProApis.forEach(expandProApi -> {
                GeoMapResourceStyle geoMapLayerStyle = new GeoMapResourceStyle();
                geoMapLayerStyle.setResourceId(geoMapResourceCustom.getId());
                geoMapLayerStyle.setExpandPro(expandProApi);
                geoMapResourceStyleMapper.insert(geoMapLayerStyle);
            });
        }
        GeoMapLayerVO geoMapLayerVO = new GeoMapLayerVO();
        List<GeoMapResourceVO> resourceVOList = new ArrayList<>();
        geoMapLayerVO.setId(geoMapResourceCustom.getId());
        geoMapLayerVO.setMapLayerName(geoMapResourceCustom.getMapResourceName());
        GeoMapResourceVO resourceVO = new GeoMapResourceVO();
        BeanUtil.copyProperties(geoMapResourceCustom, resourceVO);
        //通过resource_layer_id查询出对应的扩展属性
        List<Map<String, Object>> expandProList = new ArrayList<>();
        List<GeoMapResourceStyle> geoMapResourceStyleList = geoMapResourceStyleMapper.selectList(new LambdaQueryWrapperX<GeoMapResourceStyle>()
            .eq(GeoMapResourceStyle::getResourceId, geoMapResourceCustom.getId())
            .orderByAsc(GeoMapResourceStyle::getCreateTime));
        if (CollUtil.isNotEmpty(geoMapResourceStyleList)) {
            geoMapResourceStyleList.forEach(geoMapLayerStyle -> {
                Map<String, Object> map = new HashMap<>();
                map.put("id", geoMapLayerStyle.getId());
                map.put("expandPro", geoMapLayerStyle.getExpandPro());
                expandProList.add(map);
            });
            resourceVO.setExpandProList(expandProList);
        } else {
            resourceVO.setExpandProList(null);
        }
        resourceVOList.add(resourceVO);
        geoMapLayerVO.setParentId(PARENT_ID);
        geoMapLayerVO.setMapResourceList(resourceVOList);
        return geoMapLayerVO;
    }

    @Override
    public GeoMapResourceCustomVO detail(String id) {
        GeoMapResourceCustom geoMapResourceCustom = this.getById(id);
        // 校验是否存在
        AssertUtils.isTrue(geoMapResourceCustom == null, ErrorCodeEnum.DATA_NOT_EXIST);
        GeoMapResourceCustomVO geoMapResourceCustomVO = BeanUtils.copyProperties(geoMapResourceCustom, GeoMapResourceCustomVO.class);
        List<GeoMapMarkerVO> geoMapMarkerVOList = geoMapMarkerMapper.selectJoinList(GeoMapMarkerVO.class, new MPJLambdaWrapperX<GeoMapMarker>()
            .selectAll(GeoMapMarker.class)
            .innerJoin(GeoMapResourceMarker.class, GeoMapResourceMarker::getMarkerId, GeoMapMarker::getId)
            .eq(GeoMapResourceMarker::getResourceId, id));
        geoMapResourceCustomVO.setGeoMapMarkerList(geoMapMarkerVOList);
        geoMapResourceCustomVO.setMarkerIdList(geoMapMarkerVOList.stream().map(GeoMapMarkerVO::getId).collect(Collectors.toList()));
        List<GeoMapResourceStyle> geoMapResourceStyleList = geoMapResourceStyleMapper.selectList(GeoMapResourceStyle::getResourceId, id);
        if (CollUtil.isNotEmpty(geoMapResourceStyleList)) {
            StringBuilder spend = new StringBuilder("{\"layers\":[");
            StringBuilder join = new StringBuilder();
            for (GeoMapResourceStyle geoMapLayerStyle : geoMapResourceStyleList) {
                join.append(geoMapLayerStyle.getExpandPro()).append(",");
            }
            spend.append(join, 0, join.length() - 1).append("]}");
            geoMapResourceCustomVO.setExpandPro(spend.toString());
        }
        return geoMapResourceCustomVO;
    }

    @Override
    @Transactional
    public void delete(String id) {
        GeoMapResourceCustom geoMapResourceCustom = this.getById(id);
        if (geoMapResourceCustom != null) {
            if (StrUtil.isNotEmpty(geoMapResourceCustom.getFileId())) {
                //文件删除
                fileApi.delete(geoMapResourceCustom.getFileId());
            }
            geoMapResourceStyleMapper.delete(GeoMapResourceStyle::getResourceId, id);
            // 删除图层资源表_自定义
            this.removeById(id);
        }
    }

    @Override
    public GeoMapLayerVO getCurrentUserList() {
        String userId = WebFrameworkUtils.getLoginUserId();
        List<GeoMapResourceCustom> list = this.list(new LambdaQueryWrapper<GeoMapResourceCustom>().eq(GeoMapResourceCustom::getCreateBy, userId));
        GeoMapLayerVO geoMapLayerVO = new GeoMapLayerVO();
        List<GeoMapResourceVO> resourceVOList = new ArrayList<>();
        for (GeoMapResourceCustom geoMapResourceCustom : list) {
            geoMapLayerVO.setId(geoMapResourceCustom.getId());
            geoMapLayerVO.setMapLayerName(geoMapResourceCustom.getMapResourceName());
            GeoMapResourceVO resourceVO = new GeoMapResourceVO();
            BeanUtil.copyProperties(geoMapResourceCustom, resourceVO);
            resourceVOList.add(resourceVO);
        }
        geoMapLayerVO.setParentId("0");
        geoMapLayerVO.setMapResourceList(resourceVOList);
        return geoMapLayerVO;
    }

    @Override
    public GeoMapLayerVO copyIQueryToCustom(GeoMapResourceCustomSaveBO bo) {
        String id = bo.getLayerId();
        GeoMapLayer geoMapLayer = geoMapLayerMapper.selectById(id);
        AssertUtils.isTrue(ObjectUtil.isNull(geoMapLayer), "图层树节点不存在");
        //根据图层树节点查询和图层资源绑定的关系 和i查询
        MPJLambdaWrapperX<GeoMapLayerResource> queryWrapper = new MPJLambdaWrapperX<>();
        queryWrapper.selectAll(GeoMapResource.class);
        queryWrapper.selectAs(GeoMapLayerResource::getId, GeoMapResourceVO::getLayerResourceId);
        queryWrapper.selectAs(GeoMapLayerResource::getLayerId, GeoMapResourceVO::getLayerId);
        queryWrapper.leftJoin(GeoMapResource.class, GeoMapResource::getId, GeoMapLayerResource::getResourceId);
        queryWrapper.eq(GeoMapLayerResource::getLayerId, id);
        List<GeoMapResourceVO> geoMapResourceVOList = geoMapLayerResourceMapper.selectJoinList(GeoMapResourceVO.class, queryWrapper);
        if (CollUtil.isNotEmpty(geoMapResourceVOList)) {
            GeoMapResourceVO geoMapResourceVO = geoMapResourceVOList.get(0);
            //通过图层/图层资源关联id 和图层id 查询 关联服务
            List<GeoMapLayerResourceApi> geoMapLayerResourceApiList = geoMapLayerResourceApiService.list(new LambdaQueryWrapper<GeoMapLayerResourceApi>()
                .eq(GeoMapLayerResourceApi::getLayerResourceId, geoMapResourceVO.getLayerResourceId())
                .eq(GeoMapLayerResourceApi::getResourceId, geoMapResourceVO.getId()));
            if (CollUtil.isNotEmpty(geoMapLayerResourceApiList)) {
                GeoMapResourceCustom geoMapResourceCustom = new GeoMapResourceCustom();
                geoMapResourceCustom.setMapResourceName(geoMapLayer.getMapLayerName());
                geoMapResourceCustom.setMapResourceCode("TCZY" + System.currentTimeMillis());
                geoMapResourceCustom.setServerUrl(geoMapLayerResourceApiList.get(0).getApiId());
                geoMapResourceCustom.setServerType(bo.getServerType());
                geoMapResourceCustom.setMapLayerType(bo.getMapLayerType());
                geoMapResourceCustom.setExpandPro(bo.getExpandPro());
                geoMapResourceCustom.setFileId(geoMapLayerResourceApiList.get(0).getApiId());
                this.save(geoMapResourceCustom);
                List<String> expandProApis = new ArrayList<>();
                if (StringUtils.isNotEmpty(bo.getExpandPro())) {
                    JSONObject expandProJson = JSONObject.parseObject(bo.getExpandPro());
                    String layers = expandProJson.getString("layers");
                    if (StringUtils.isNotEmpty(layers)) {
                        JSONArray layersJson = JSONUtil.parseArray(layers);
                        if (CollUtil.isNotEmpty(layersJson)) {
                            for (Object o : layersJson) {
                                String s = o.toString();
                                expandProApis.add(s);
                            }
                        }
                    }
                }
                if (CollUtil.isNotEmpty(expandProApis)) {
                    expandProApis.forEach(expandProApi -> {
                        GeoMapResourceStyle geoMapLayerStyle = new GeoMapResourceStyle();
                        geoMapLayerStyle.setResourceId(geoMapResourceCustom.getId());
                        geoMapLayerStyle.setExpandPro(expandProApi);
                        geoMapResourceStyleMapper.insert(geoMapLayerStyle);
                    });
                }
                GeoMapLayerVO geoMapLayerVO = new GeoMapLayerVO();
                List<GeoMapResourceVO> resourceVOList = new ArrayList<>();
                geoMapLayerVO.setId(geoMapResourceCustom.getId());
                geoMapLayerVO.setMapLayerName(geoMapResourceCustom.getMapResourceName());
                //截取图层名称的最后一个#前面数据
                if (geoMapLayerVO.getMapLayerName().lastIndexOf("#") > 0) {
                    geoMapLayerVO.setMapLayerName(geoMapLayerVO.getMapLayerName().substring(0, geoMapLayerVO.getMapLayerName().lastIndexOf("#")));
                }
                GeoMapResourceVO resourceVO = new GeoMapResourceVO();
                BeanUtil.copyProperties(geoMapResourceCustom, resourceVO);
                //通过resource_layer_id查询出对应的扩展属性
                List<Map<String, Object>> expandProList = new ArrayList<>();
                List<GeoMapResourceStyle> geoMapResourceStyleList = geoMapResourceStyleMapper.selectList(new LambdaQueryWrapperX<GeoMapResourceStyle>()
                    .eq(GeoMapResourceStyle::getResourceId, geoMapResourceCustom.getId())
                    .orderByAsc(GeoMapResourceStyle::getCreateTime));
                if (CollUtil.isNotEmpty(geoMapResourceStyleList)) {
                    geoMapResourceStyleList.forEach(geoMapLayerStyle -> {
                        Map<String, Object> map = new HashMap<>();
                        map.put("id", geoMapLayerStyle.getId());
                        map.put("expandPro", geoMapLayerStyle.getExpandPro());
                        expandProList.add(map);
                    });
                    resourceVO.setExpandProList(expandProList);
                } else {
                    resourceVO.setExpandProList(null);
                }
                resourceVOList.add(resourceVO);
                geoMapLayerVO.setParentId(PARENT_ID);
                geoMapLayerVO.setMapResourceList(resourceVOList);
                return geoMapLayerVO;
            }
        }
        return null;
    }

}
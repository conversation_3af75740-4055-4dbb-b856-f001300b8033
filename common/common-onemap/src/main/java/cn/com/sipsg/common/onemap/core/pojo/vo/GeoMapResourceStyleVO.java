package cn.com.sipsg.common.onemap.core.pojo.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fhs.core.trans.vo.VO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 图层资源渲染风格表VO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "图层资源渲染风格表VO")
public class GeoMapResourceStyleVO implements VO {

    /**
     * 主键
     */
    @TableId("id")
    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED)
    private String id;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

    /**
     * 图层资源id
     */
    @Schema(description = "图层资源id", requiredMode = Schema.RequiredMode.REQUIRED)
    private String resourceId;

    /**
     * 扩展属性
     */
    @Schema(description = "扩展属性")
    private String expandPro;

    /**
     * 顺序值
     */
    @Schema(description = "顺序值")
    private Integer sort;

    /**
     * 渲染模板id
     */
    @Schema(description = "渲染模板id")
    private String styleTemplateId;

    /**
     * 图层名称
     */
    @Schema(description = "图层名称")
    private String sourceLayer;

}
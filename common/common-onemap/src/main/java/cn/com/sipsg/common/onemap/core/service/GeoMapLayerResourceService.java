package cn.com.sipsg.common.onemap.core.service;

import cn.com.sipsg.common.mybatis.core.service.BaseServiceX;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.onemap.core.entity.GeoMapLayerResource;
import cn.com.sipsg.common.onemap.core.pojo.bo.GeoMapLayerResourceQueryBO;
import cn.com.sipsg.common.onemap.core.pojo.bo.GeoMapLayerResourceSaveBO;
import cn.com.sipsg.common.onemap.core.pojo.vo.GeoMapLayerResourceVO;
import java.util.List;

/**
 * 图层/图层资源关系表服务接口
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public interface GeoMapLayerResourceService extends BaseServiceX<GeoMapLayerResource> {

    /**
     * 查询图层/图层资源关系表分页列表
     *
     * @param bo 参数
     * @return 图层/图层资源关系表分页列表
     */
    CommonPageVO<GeoMapLayerResourceVO> page(GeoMapLayerResourceQueryBO bo);

    /**
     * 新增图层/图层资源关系表
     *
     * @param bo 参数
     * @return 图层/图层资源关系表ID
     */
    String save(GeoMapLayerResourceSaveBO bo);

    /**
     * 编辑图层/图层资源关系表
     *
     * @param bo 参数
     */
    void update(GeoMapLayerResourceSaveBO bo);

    /**
     * 根据id查询图层/图层资源关系表详情
     *
     * @param id 主键
     * @return 图层/图层资源关系表详情
     */
    GeoMapLayerResourceVO detail(String id);

    /**
     * 删除图层/图层资源关系表
     *
     * @param id 主键
     */
    void delete(String id);

    /**
     * 根据图层id查询资源id
     * @param bo 参数
     */
    void addLayerResourceRef(GeoMapLayerResourceSaveBO bo);

    /**
     * 删除图层资源关系
     * @param id 图层id
     */
    void delLayerResourceRef(String id);
}
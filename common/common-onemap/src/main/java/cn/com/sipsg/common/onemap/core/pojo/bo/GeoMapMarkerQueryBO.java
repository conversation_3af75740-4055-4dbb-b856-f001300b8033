package cn.com.sipsg.common.onemap.core.pojo.bo;

import cn.com.sipsg.common.pojo.bo.SortablePageBO;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 图标表查询BO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "图标表查询BO")
public class GeoMapMarkerQueryBO extends SortablePageBO {

    /**
     * 图标名称
     */
    @Schema(description = "图标名称")
    private String markerName;

    /**
     * 分组
     */
    @Schema(description = "分组")
    private String groupBm;

}
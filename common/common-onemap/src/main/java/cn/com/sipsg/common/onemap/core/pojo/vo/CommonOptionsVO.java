package cn.com.sipsg.common.onemap.core.pojo.vo;

import cn.com.sipsg.common.dynamic.core.pojo.vo.DynamicTokenVO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.HashMap;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

/**
 * 中心点、缩放及token配置
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "中心点、缩放及token配置")
public class CommonOptionsVO {

    /**
     * 中心点、缩放
     */
    @Schema(description = "中心点、缩放")
    private OptionsVO options;

    /**
     * 视图模式
     */
    @Schema(description = "视图模式")
    private String viewMode;

    /**
     * token请求信息集合
     */
    @Schema(description = "token请求信息集合")
    private List<DynamicTokenVO> tokens;

    /**
     * token集合
     */
    @Schema(description = "token集合")
    private List<HashMap> tokenMaps;
}

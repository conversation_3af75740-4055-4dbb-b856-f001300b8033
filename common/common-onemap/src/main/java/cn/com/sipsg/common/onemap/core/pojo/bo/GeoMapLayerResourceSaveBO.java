package cn.com.sipsg.common.onemap.core.pojo.bo;

import cn.com.sipsg.common.validation.group.UpdateGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

/**
 * 图层/图层资源关系表保存BO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "图层/图层资源关系表保存BO")
public class GeoMapLayerResourceSaveBO {

    /**
     * 主键
     */
    @Schema(description = "主键")
    @NotBlank(message = "主键不能为空", groups = {UpdateGroup.class})
    private String id;

    /**
     * 图层ID
     */
    @Schema(description = "图层ID")
    private String layerId;

    /**
     * 图层资源ID
     */
    @Schema(description = "图层资源ID")
    private String resourceId;

    /**
     * 过滤字段
     */
    @Schema(description = "过滤字段")
    private String mapLayerFilter;

    /**
     * 排序值
     */
    @Schema(description = "排序值")
    private Integer sort;

}
package cn.com.sipsg.common.onemap.core.pojo.bo;

import cn.com.sipsg.common.validation.group.UpdateGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

/**
 * 图层关联图层资源/查询服务关系表保存BO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "图层关联图层资源/查询服务关系表保存BO")
public class GeoMapLayerResourceApiSaveBO {

    /**
     * 图层关联图层资源ID
     */
    @Schema(description = "图层关联图层资源ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "图层关联图层资源ID不能为空")
    private String layerResourceId;

    /**
     * 查询服务ID
     */
    @Schema(description = "查询服务ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "查询服务ID不能为空")
    private String apiId;

    /**
     * 层资源ID
     */
    @Schema(description = "层资源ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "层资源ID不能为空")
    private String resourceId;

}
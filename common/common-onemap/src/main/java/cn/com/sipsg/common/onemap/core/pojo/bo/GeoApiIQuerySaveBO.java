package cn.com.sipsg.common.onemap.core.pojo.bo;

import cn.com.sipsg.common.onemap.core.entity.GeoApiQueryParam;
import cn.com.sipsg.common.validation.group.UpdateGroup;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

/**
 * i快速查询表保存BO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "i快速查询表保存BO")
public class GeoApiIQuerySaveBO {

    /**
     * 主键
     */
    @Schema(description = "主键")
    @NotBlank(message = "主键不能为空", groups = {UpdateGroup.class})
    private String id;

    /**
     * 名称
     */
    @Schema(description = "名称")
    private String apiName;

    /**
     * 编码
     */
    @Schema(description = "编码")
    private String apiCode;

    /**
     * 服务地址
     */
    @Schema(description = "服务地址")
    private String apiUrl;

    /**
     * 要素类型：点，线，面
     */
    @Schema(description = "要素类型：点，线，面")
    private String geoType;

    /**
     * 服务ID
     */
    @Schema(description = "服务ID")
    private String serverId;

    /**
     * 服务类型：table,arcgis,upi,proxy,third,zmq
     */
    @Schema(description = "服务类型：table,arcgis,upi,proxy,third,zmq")
    private String serverType;

    /**
     * 模板ID
     */
    @Schema(description = "模板ID")
    private String templateId;

    /**
     * 缓存区
     */
    @Schema(description = "缓存区")
    private BigDecimal buffer;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用")
    private Integer status;

    /**
     * 开启单体化
     */
    @Schema(description = "开启单体化")
    @JsonProperty(value = "classifyEnable")
    private Boolean classifyEnable;

    /**
     * 参数配置集合
     */
    @Schema(description = "参数配置集合")
    private List<GeoApiQueryParam> queryParamList;

    /**
     * 图层关联图层资源ID
     */
    @Schema(description = "图层关联图层资源ID")
    private String layerResourceId;


    /**
     * 层资源ID
     */
    @Schema(description = "层资源ID")
    private String resourceId;





}
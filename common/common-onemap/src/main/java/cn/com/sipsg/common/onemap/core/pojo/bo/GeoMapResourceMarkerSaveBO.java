package cn.com.sipsg.common.onemap.core.pojo.bo;

import cn.com.sipsg.common.validation.group.UpdateGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

/**
 * 图层资源/图标关系表保存BO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "图层资源/图标关系表保存BO")
public class GeoMapResourceMarkerSaveBO {

    /**
     * 图层资源ID
     */
    @Schema(description = "图层资源ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "图层资源ID不能为空")
    private String resourceId;

    /**
     * 图标ID
     */
    @Schema(description = "图标ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "图标ID不能为空")
    private String markerId;

}
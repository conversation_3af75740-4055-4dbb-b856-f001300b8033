package cn.com.sipsg.common.onemap.core.service.impl;

import cn.com.sipsg.common.exception.enums.ErrorCodeEnum;
import cn.com.sipsg.common.mybatis.core.query.LambdaQueryWrapperX;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.util.AssertUtils;
import cn.com.sipsg.common.util.BeanUtils;
import cn.com.sipsg.common.onemap.core.entity.GeoApiQueryParam;
import cn.com.sipsg.common.onemap.core.mapper.GeoApiQueryParamMapper;
import cn.com.sipsg.common.onemap.core.pojo.bo.GeoApiQueryParamQueryBO;
import cn.com.sipsg.common.onemap.core.pojo.bo.GeoApiQueryParamSaveBO;
import cn.com.sipsg.common.onemap.core.pojo.vo.GeoApiQueryParamVO;
import cn.com.sipsg.common.onemap.core.service.GeoApiQueryParamService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * 参数配置表服务实现类
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Service
@RequiredArgsConstructor
public class GeoApiQueryParamServiceImpl extends ServiceImpl<GeoApiQueryParamMapper, GeoApiQueryParam> implements GeoApiQueryParamService {

    @Override
    public CommonPageVO<GeoApiQueryParamVO> page(GeoApiQueryParamQueryBO bo) {
        LambdaQueryWrapperX<GeoApiQueryParam> queryWrapper = new LambdaQueryWrapperX<>();
        // 填充查询条件
        return this.page(bo, queryWrapper).convert(entity -> BeanUtils.copyProperties(entity, GeoApiQueryParamVO.class));
    }

    @Override
    @Transactional
    public String save(GeoApiQueryParamSaveBO bo) {
        bo.setId(null);
        // 新增参数配置表
        GeoApiQueryParam geoApiQueryParam = BeanUtils.copyProperties(bo, GeoApiQueryParam.class);
        this.save(geoApiQueryParam);
        return geoApiQueryParam.getId();
    }

    @Override
    @Transactional
    public void update(GeoApiQueryParamSaveBO bo) {
        // 编辑参数配置表
        GeoApiQueryParam geoApiQueryParam = BeanUtils.copyProperties(bo, GeoApiQueryParam.class);
        this.updateById(geoApiQueryParam);
    }

    @Override
    public GeoApiQueryParamVO detail(String id) {
        GeoApiQueryParam geoApiQueryParam = this.getById(id);
        // 校验是否存在
        AssertUtils.isTrue(geoApiQueryParam == null, ErrorCodeEnum.DATA_NOT_EXIST);
        return BeanUtils.copyProperties(geoApiQueryParam, GeoApiQueryParamVO.class);
    }

    @Override
    @Transactional
    public void delete(String id) {
        GeoApiQueryParam geoApiQueryParam = this.getById(id);
        if (geoApiQueryParam != null) {
            // 删除参数配置表
            this.removeById(id);
        }
    }

}
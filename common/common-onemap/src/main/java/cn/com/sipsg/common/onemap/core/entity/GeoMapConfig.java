package cn.com.sipsg.common.onemap.core.entity;

import cn.com.sipsg.common.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;


/**
 * 地图配置表
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@FieldNameConstants
@TableName(value = "medox_onemap_geo_map_config", autoResultMap = true)
public class GeoMapConfig extends BaseDO {

    /**
     * 主键
     */
    @TableId("id")
    private String id;

    /**
     * 关联ID
     */
    @TableField("privilege_id")
    private String privilegeId;

    /**
     * 关联类型
     */
    @TableField("privilege_type")
    private String privilegeType;

    /**
     * 角色id
     */
    @TableField("role_id")
    private String roleId;

    /**
     * 应用标识
     */
    @TableField("app_code")
    private String appCode;

}
package cn.com.sipsg.common.onemap.core.pojo.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fhs.core.trans.vo.VO;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import org.springframework.web.multipart.MultipartFile;

/**
 * 图标表VO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "图标表VO")
public class GeoMapMarkerVO implements VO {

    /**
     * 主键
     */
    @TableId("id")
    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED)
    private String id;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

    /**
     * 图标名称
     */
    @Schema(description = "图标名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String markerName;

    /**
     * 图标地址
     */
    @Schema(description = "图标地址")
    private String markerUrl;

    /**
     * 分组
     */
    @Schema(description = "分组")
    private String groupBm;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 文件地址
     */
    @Schema(description = "文件地址")
    private String filePath;

    /**
     * 文件
     */
    @Schema(description = "文件")
    private MultipartFile file;

    /**
     * 图层资源ID
     */
    @Schema(description = "图层资源ID")
    private String resourceId;

    /**
     * SDF图像
     */
    @Schema(description = "SDF图像")
    private Boolean sdf;

}
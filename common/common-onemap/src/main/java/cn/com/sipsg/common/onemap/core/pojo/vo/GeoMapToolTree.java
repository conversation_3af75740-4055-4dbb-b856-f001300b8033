package cn.com.sipsg.common.onemap.core.pojo.vo;

import cn.com.sipsg.common.pojo.TreeNode;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 地图工具树结构
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@Schema(description = "地图工具树结构")
public class GeoMapToolTree extends TreeNode<GeoMapToolTree> {

    private static final long serialVersionUID = -1L;

    /**
     * 名称
     */
    @Schema(description = "名称")
    private String mapToolName;

    /**
     * 编码
     */
    @Schema(description = "编码")
    private String mapToolCode;

    /**
     * 类型
     */
    @Schema(description = "类型")
    private String mapToolType;

    /**
     * 图标
     */
    @Schema(description = "图标")
    private String mapToolUrl;

    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer sort;

}

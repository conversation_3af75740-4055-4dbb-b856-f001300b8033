package cn.com.sipsg.common.onemap.core.entity;

import cn.com.sipsg.common.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;


/**
 * 图层关联图层资源/查询服务关系表
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@FieldNameConstants
@TableName(value = "medox_onemap_geo_map_layer_resource_api", autoResultMap = true)
public class GeoMapLayerResourceApi {

    /**
     * 图层关联图层资源ID
     */
    @TableField("layer_resource_id")
    private String layerResourceId;

    /**
     * 查询服务ID
     */
    @TableField("api_id")
    private String apiId;

    /**
     * 层资源ID
     */
    @TableField("resource_id")
    private String resourceId;

}
package cn.com.sipsg.common.onemap.core.pojo.bo;

import cn.com.sipsg.common.pojo.bo.SortablePageBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 一张图搜索历史查询BO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "一张图搜索历史查询BO")
public class SearchHistoryQueryBO extends SortablePageBO {

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 服务id
     */
    @Schema(description = "服务id")
    private String serverId;

}
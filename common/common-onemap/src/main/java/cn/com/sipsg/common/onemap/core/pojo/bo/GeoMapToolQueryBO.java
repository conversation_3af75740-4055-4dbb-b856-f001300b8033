package cn.com.sipsg.common.onemap.core.pojo.bo;

import cn.com.sipsg.common.pojo.bo.SortablePageBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 地图工具表查询BO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "地图工具表查询BO")
public class GeoMapToolQueryBO extends SortablePageBO {

    /**
     * 地图工具编码
     */
    @Schema(description = "地图工具编码")
    private String mapToolCode;

    /**
     * 地图工具名称
     */
    @Schema(description = "地图工具名称")
    private String mapToolName;

}
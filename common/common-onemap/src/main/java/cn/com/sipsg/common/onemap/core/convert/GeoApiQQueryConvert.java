package cn.com.sipsg.common.onemap.core.convert;

import cn.com.sipsg.common.enums.CommonStatusEnum;
import cn.com.sipsg.common.onemap.core.entity.GeoApiQQuery;
import cn.com.sipsg.common.onemap.core.pojo.bo.GeoApiQQuerySaveBO;
import cn.com.sipsg.common.util.EnumUtils;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 快查转换器
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Mapper
public interface GeoApiQQueryConvert {

    GeoApiQQueryConvert INSTANCE = Mappers.getMapper(GeoApiQQueryConvert.class);

    /**
     * bo转entity
     *
     * @param entity 快查bo
     * @return entity
     */
    GeoApiQQuery convert(GeoApiQQuerySaveBO entity);

    default CommonStatusEnum status2Enum(Integer status) {
        return EnumUtils.getEnumByValue(CommonStatusEnum.class, status);
    }
}

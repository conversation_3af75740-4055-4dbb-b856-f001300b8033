package cn.com.sipsg.common.onemap.core.entity;

import cn.com.sipsg.common.dynamic.core.entity.DynamicServer;
import cn.com.sipsg.common.enums.CommonStatusEnum;
import cn.com.sipsg.common.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.github.yulichang.annotation.EntityMapping;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;

import java.math.BigDecimal;
import java.util.List;


/**
 * i快速查询表
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@FieldNameConstants
@TableName(value = "medox_onemap_geo_api_i_query", autoResultMap = true)
public class GeoApiIQuery extends BaseDO {

    /**
     * 主键
     */
    @TableId("id")
    private String id;

    /**
     * 名称
     */
    @TableField("api_name")
    private String apiName;

    /**
     * 编码
     */
    @TableField("api_code")
    private String apiCode;

    /**
     * 服务地址
     */
    @TableField("api_url")
    private String apiUrl;

    /**
     * 要素类型：点，线，面
     */
    @TableField("geo_type")
    private String geoType;

    /**
     * 服务ID
     */
    @TableField("server_id")
    private String serverId;

    /**
     * 服务类型：table,arcgis,upi,proxy,third,zmq
     */
    @TableField("server_type")
    private String serverType;

    /**
     * 模板ID
     */
    @TableField("template_id")
    private String templateId;

    /**
     * 缓存区
     */
    @TableField("buffer")
    private BigDecimal buffer;

    /**
     * 是否启用
     */
    @TableField("status")
    private CommonStatusEnum status;

    /**
     * 开启单体化
     */
    @TableField("classify_enable")
    private Boolean classifyEnable;

    /**
     * 关联服务
     */
    @TableField(exist = false)
    @EntityMapping(thisField = GeoApiQQuery.Fields.serverId, joinField = DynamicServer.Fields.id)
    private DynamicServer dynamicServer;

    /**
     * 关联参数列表
     */
    @TableField(exist = false)
    @EntityMapping(thisField = GeoApiQQuery.Fields.id, joinField = GeoApiQueryParam.Fields.apiId)
    private List<GeoApiQueryParam> queryParamList;

}
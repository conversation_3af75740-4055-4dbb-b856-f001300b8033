package cn.com.sipsg.common.onemap.core.entity;

import cn.com.sipsg.common.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.OrderBy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;


/**
 * 图层资源渲染风格表
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@FieldNameConstants
@TableName(value = "medox_onemap_geo_map_resource_style", autoResultMap = true)
public class GeoMapResourceStyle extends BaseDO {

    /**
     * 主键
     */
    @TableId("id")
    private String id;

    /**
     * 图层资源id
     */
    @TableField("resource_id")
    private String resourceId;

    /**
     * 扩展属性
     */
    @TableField("expand_pro")
    private String expandPro;

    /**
     * 顺序值
     */
    @TableField("sort")
    @OrderBy(asc = true, sort = Short.MIN_VALUE)
    private Integer sort;

    /**
     * 渲染模板id
     */
    @TableField("style_template_id")
    private String styleTemplateId;

    /**
     * 图层名称
     */
    @TableField("source_layer")
    private String sourceLayer;

}
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.com.sipsg</groupId>
        <artifactId>common</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>common-flow</artifactId>
    <description>工作流</description>

    <dependencies>
        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>rail-transit-server-module-system-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- RPC 相关 -->
        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>common-rpc</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>common-web</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>common-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>common-redis</artifactId>
        </dependency>

        <!-- 工作流相关 -->
        <dependency>
            <groupId>org.flowable</groupId>
            <artifactId>flowable-spring-boot-starter-basic</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.mybatis</groupId>
                    <artifactId>mybatis</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 操作日志 -->
        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>common-operationlog</artifactId>
        </dependency>

        <!-- 认证授权 -->
        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>common-security</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>common-holiday</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.sipsg</groupId>
            <artifactId>common-notify</artifactId>
        </dependency>
    </dependencies>
</project>
package cn.com.sipsg.common.flow.core.pojo.vo;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * 文件 VO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "文件 VO")
public class SysFileVO {

    /**
     * 文件ID
     */
    @TableId
    @Schema(description = "文件ID")
    private String id;

    /**
     * 文件访问地址
     */
    @Schema(description = "文件访问地址")
    private String url;

    /**
     * 文件大小;单位字节
     */
    @Schema(description = "文件大小;单位字节")
    private Long size;

    /**
     * 文件名称
     */
    @Schema(description = "文件名称")
    private String fileName;

    /**
     * 原始文件名称
     */
    @Schema(description = "原始文件名称")
    private String originalFileName;

    /**
     * 基础存储路径
     */
    @Schema(description = "基础存储路径")
    private String basePath;

    /**
     * 存储路径
     */
    @Schema(description = "存储路径")
    private String path;

    /**
     * 文件扩展名
     */
    @Schema(description = "文件扩展名")
    private String ext;

    /**
     * MIME类型
     */
    @Schema(description = "MIME类型")
    private String contentType;

    /**
     * 存储平台
     */
    @Schema(description = "存储平台")
    private String platform;

    /**
     * 分组编码
     */
    @Schema(description = "分组编码")
    private String groupCode;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 文件相对地址
     */
    @Schema(description = "文件相对地址")
    public String getRelativeUrl() {
        return StrUtil.nullToEmpty(basePath) + path + fileName;
    }

    /**
     * 文件相对地址
     */
    @Schema(description = "文件相对地址")
    private String relativeUrl;

}

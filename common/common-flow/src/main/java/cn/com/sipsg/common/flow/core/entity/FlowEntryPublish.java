package cn.com.sipsg.common.flow.core.entity;

import cn.com.sipsg.common.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;

/**
 * 流程发布数据的实体对象。
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@FieldNameConstants
@TableName(value = "medox_flow_entry_publish", autoResultMap = true)
public class FlowEntryPublish extends BaseDO {

    /**
     * 主键Id。
     */
    @TableId(value = "entry_publish_id")
    private String entryPublishId;

    /**
     * 流程Id。
     */
    private String entryId;

    /**
     * 流程引擎的部署Id。
     */
    private String deployId;

    /**
     * 流程引擎中的流程定义Id。
     */
    private String processDefinitionId;

    /**
     * 发布版本。
     */
    private Integer publishVersion;

    /**
     * 激活状态。
     */
    private Boolean activeStatus;

    /**
     * 是否为主版本。
     */
    private Boolean mainVersion;


    /**
     * 发布时间。
     */
    private LocalDateTime publishTime;

    /**
     * 第一个非开始节点任务的附加信息。
     */
    private String initTaskInfo;

    /**
     * 分析后的节点JSON信息。
     */
    private String analyzedNodeJson;
}

package cn.com.sipsg.common.flow.core.controller;

import cn.com.sipsg.common.constant.PageConstants;
import cn.com.sipsg.common.flow.core.pojo.bo.FlowRuTaskAbnormalRecordQueryBO;
import cn.com.sipsg.common.flow.core.pojo.bo.FlowRuTaskAbnormalRecordSaveBO;
import cn.com.sipsg.common.flow.core.pojo.vo.FlowRuTaskAbnormalRecordVO;
import cn.com.sipsg.common.flow.core.service.FlowRuTaskAbnormalRecordService;
import cn.com.sipsg.common.operationlog.core.annotation.OperationLog;
import cn.com.sipsg.common.operationlog.core.enums.OperationTypeEnum;
import cn.com.sipsg.common.pojo.CommonResult;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.validation.AddValidated;
import cn.com.sipsg.common.validation.UpdateValidated;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 流程异常任务记录控制器
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Tag(name = "流程异常任务记录控制器")
@RestController
@RequiredArgsConstructor
@RequestMapping("/flow/flowRuTaskAbnormalRecord")
public class FlowRuTaskAbnormalRecordController {

    private final FlowRuTaskAbnormalRecordService flowRuTaskAbnormalRecordService;

    /**
     * 查询分页列表
     *
     * @param bo 参数
     * @return 分页列表
     */
    @Operation(summary = "查询分页列表")
    @OperationLog(module = "管理", value = "查询分页列表", type = OperationTypeEnum.QUERY, saveResponse = false)
    @PostMapping("/page")
    public CommonResult<CommonPageVO<FlowRuTaskAbnormalRecordVO>> page(@RequestBody FlowRuTaskAbnormalRecordQueryBO bo) {
        return CommonResult.data(flowRuTaskAbnormalRecordService.page(bo));
    }

    /**
     * 查询列表
     *
     * @param bo 参数
     * @return 列表
     */
    @Operation(summary = "查询列表")
    @OperationLog(module = "管理", value = "查询列表", type = OperationTypeEnum.QUERY, saveResponse = false)
    @PostMapping("/list")
    public CommonResult<List<FlowRuTaskAbnormalRecordVO>> list(@RequestBody FlowRuTaskAbnormalRecordQueryBO bo) {
        bo.setSize(PageConstants.SIZE_NONE);
        return CommonResult.data(flowRuTaskAbnormalRecordService.page(bo).getRecords());
    }

    /**
     * 新增
     *
     * @param bo 参数
     * @return ID
     */
    @Operation(summary = "新增")
    @OperationLog(module = "管理", value = "新增", type = OperationTypeEnum.SAVE)
    @PostMapping("/save")
    public CommonResult<String> save(@AddValidated @RequestBody FlowRuTaskAbnormalRecordSaveBO bo) {
        return CommonResult.data(flowRuTaskAbnormalRecordService.save(bo));
    }

    /**
     * 编辑
     *
     * @param bo 参数
     */
    @Operation(summary = "编辑")
    @OperationLog(module = "管理", value = "编辑", type = OperationTypeEnum.UPDATE)
    @PostMapping("/update")
    public CommonResult<Void> update(@UpdateValidated @RequestBody FlowRuTaskAbnormalRecordSaveBO bo) {
        flowRuTaskAbnormalRecordService.update(bo);
        return CommonResult.success();
    }

    /**
     * 查询详情
     *
     * @param id 主键id
     */
    @Operation(summary = "查询详情")
    @Parameter(name = "id", description = "主键id", required = true, example = "1111")
    @OperationLog(module = "管理", value = "查询详情", type = OperationTypeEnum.QUERY, saveResponse = false)
    @GetMapping("/detail")
    public CommonResult<FlowRuTaskAbnormalRecordVO> detail(@RequestParam String id) {
        return CommonResult.data(flowRuTaskAbnormalRecordService.detail(id));
    }

    /**
     * 删除
     *
     * @param id 主键id
     */
    @Operation(summary = "删除")
    @Parameter(name = "id", description = "主键id", required = true, example = "1111")
    @OperationLog(module = "管理", value = "删除", type = OperationTypeEnum.DELETE)
    @PostMapping("/delete")
    public CommonResult<Void> delete(@RequestParam String id) {
        flowRuTaskAbnormalRecordService.delete(id);
        return CommonResult.success();
    }

}
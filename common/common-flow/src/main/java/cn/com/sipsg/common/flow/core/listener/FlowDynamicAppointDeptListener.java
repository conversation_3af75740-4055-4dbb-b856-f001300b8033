package cn.com.sipsg.common.flow.core.listener;

import cn.com.sipsg.common.flow.core.entity.FlowTaskExt;
import cn.com.sipsg.common.flow.core.service.FlowTaskExtService;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import java.util.Arrays;
import lombok.extern.slf4j.Slf4j;
import org.flowable.common.engine.api.delegate.Expression;
import org.flowable.engine.delegate.TaskListener;
import org.flowable.task.service.delegate.DelegateTask;
import org.springframework.stereotype.Component;

/**
 * 流程动态指定审批部门监听器
 *
 * <AUTHOR>
 * @since 2025/04/14
 **/
@Slf4j
@Component
public class FlowDynamicAppointDeptListener implements TaskListener {

    private final transient FlowTaskExtService flowTaskExtService = SpringUtil.getBean(FlowTaskExtService.class);

    // dynamicApproverDept  是任务监听器注入字段属性对象,通过该对象获取配置的字段
    private Expression dynamicApproverDept;

    @Override
    public void notify(DelegateTask delegateTask) {
        //以下为任务监听器注入属性字段获取方式,此种方式在任务监听器和执行监听器中都可以作为注入属性值获取方式
        Object fieldName = dynamicApproverDept.getValue(delegateTask);
        if (null == fieldName) {
            return;
        }
        Object deptId = delegateTask.getVariable(String.valueOf(fieldName));
        if (ObjectUtil.isNotNull(deptId)) {
            //支持指定多个部门，多个部门id值以逗号拼接
            delegateTask.addCandidateGroups(Arrays.asList(deptId.toString().split(",")));
            //this.recordApproverToFlowTaskExt(delegateTask, o.toString());
        }
    }


    /**
     * 针对动态分配审批人，将节点审批对象参数录入zz_flow_task_ext，便于获取动态分配时获取下一节点的审批对象 注意：对于表zz_flow_task_ext，设置字段process_definition_id和task_id为联合主键，否则seata事务控制下，无法修改数据
     *
     * @param delegateTask    代表任务
     * @param approvalObjects 批准对象
     */
    private void recordApproverToFlowTaskExt(DelegateTask delegateTask, String approvalObjects) {
        // TODO 可能会有问题，flowTaskExt是流程发布过后该版本整体的定义参数，此处是某一个流程实例更新里面参数，可能会有问题
        //流程定义id  postAdjustment:16:83e083e4-77bc-11ee-bc35-00ff0fe3faee
        String processDefinitionId = delegateTask.getProcessDefinitionId();
        //动态分配下一节点id  Activity_160a25c
        String taskDefinitionKey = delegateTask.getTaskDefinitionKey();
        FlowTaskExt flowTaskExt = new FlowTaskExt();
        flowTaskExt.setDeptIds(approvalObjects);
        LambdaQueryWrapper<FlowTaskExt> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(FlowTaskExt::getProcessDefinitionId, processDefinitionId)
            .eq(FlowTaskExt::getTaskId, taskDefinitionKey);
        flowTaskExtService.modifyFlowTaskExt(flowTaskExt, queryWrapper);
    }
}

package cn.com.sipsg.common.flow.core.service.impl;


import cn.com.sipsg.common.exception.BusinessException;
import cn.com.sipsg.common.flow.core.constant.FlowApprovalType;
import cn.com.sipsg.common.flow.core.constant.FlowConstant;
import cn.com.sipsg.common.flow.core.constant.FlowUserTaskExtData;
import cn.com.sipsg.common.flow.core.entity.FlowEntryVariable;
import cn.com.sipsg.common.flow.core.entity.FlowMultiInstanceTrans;
import cn.com.sipsg.common.flow.core.entity.FlowTaskComment;
import cn.com.sipsg.common.flow.core.entity.FlowTaskExt;
import cn.com.sipsg.common.flow.core.mapper.FlowTaskExtMapper;
import cn.com.sipsg.common.flow.core.pojo.dto.FlowElementExtPropertyDTO;
import cn.com.sipsg.common.flow.core.pojo.dto.FlowTaskMultiSignAssignDTO;
import cn.com.sipsg.common.flow.core.pojo.vo.FlowUserInfoVO;
import cn.com.sipsg.common.flow.core.pojo.vo.TaskInfoVO;
import cn.com.sipsg.common.flow.core.service.FlowApiService;
import cn.com.sipsg.common.flow.core.service.FlowEntryVariableService;
import cn.com.sipsg.common.flow.core.service.FlowMultiInstanceTransService;
import cn.com.sipsg.common.flow.core.service.FlowTaskCommentService;
import cn.com.sipsg.common.flow.core.service.FlowTaskExtService;
import cn.com.sipsg.common.pojo.Tuple2;
import cn.com.sipsg.common.util.BeanUtils;
import cn.com.sipsg.common.util.CollectionUtils;
import cn.com.sipsg.module.system.api.upms.SysUserApi;
import cn.com.sipsg.module.system.api.upms.dto.SysUserRespDTO;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.bpmn.model.ExtensionAttribute;
import org.flowable.bpmn.model.ExtensionElement;
import org.flowable.bpmn.model.FieldExtension;
import org.flowable.bpmn.model.FlowElement;
import org.flowable.bpmn.model.FlowableListener;
import org.flowable.bpmn.model.Process;
import org.flowable.bpmn.model.UserTask;
import org.flowable.engine.RuntimeService;
import org.flowable.task.api.TaskInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 流程任务扩展实体对象
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FlowTaskExtServiceImpl extends ServiceImpl<FlowTaskExtMapper, FlowTaskExt> implements FlowTaskExtService {

    @Autowired
    private FlowTaskExtMapper flowTaskExtMapper;

    private final FlowEntryVariableService flowEntryVariableService;

    @Autowired
    private FlowApiService flowApiService;

    private final FlowMultiInstanceTransService flowMultiInstanceTransService;

    @Autowired
    private FlowTaskCommentService flowTaskCommentService;

    @Autowired
    private SysUserApi sysUserApi;

    @Autowired
    private RuntimeService runtimeService;

    private static final String ID = "id";

    private static final String TYPE = "type";

    private static final String LABEL = "label";

    private static final String NAME = "name";

    private static final String VALUE = "value";


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveBatch(List<FlowTaskExt> flowTaskExtList) {
        if (CollUtil.isNotEmpty(flowTaskExtList)) {
            flowTaskExtMapper.insertBatch(flowTaskExtList);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modifyFlowTaskExt(FlowTaskExt flowTaskExt, LambdaQueryWrapper queryWrapper) {
        flowTaskExtMapper.update(flowTaskExt, queryWrapper);
    }

    @Override
    public FlowTaskExt getByProcessDefinitionIdAndTaskId(String processDefinitionId, String taskId) {
        FlowTaskExt filter = new FlowTaskExt();
        filter.setProcessDefinitionId(processDefinitionId);
        filter.setTaskId(taskId);
        return flowTaskExtMapper.selectOne(new QueryWrapper<>(filter));
    }

    @Override
    public List<FlowTaskExt> getByProcessDefinitionId(String processDefinitionId) {
        FlowTaskExt filter = new FlowTaskExt();
        filter.setProcessDefinitionId(processDefinitionId);
        return flowTaskExtMapper.selectList(new QueryWrapper<>(filter));
    }

    @Override
    public List<FlowUserInfoVO> getCandidateUserInfoList(
        String processInstanceId,
        FlowTaskExt flowTaskExt,
        TaskInfo taskInfo,
        boolean isMultiInstanceTask,
        boolean historic) {
        List<FlowUserInfoVO> resultUserMapList = new LinkedList<>();
        if (!isMultiInstanceTask && this.buildTransferUserList(taskInfo, resultUserMapList)) {
            return resultUserMapList;
        }
        Set<String> loginUserIdSet = new HashSet<>();
        if (StrUtil.isNotBlank(flowTaskExt.getDeptIds())) {
            //当前节点最近一次操作是干预操作时，干预可能会将原来的处理用户干预成其他人，则从审批记录里取最新的人
            FlowTaskComment latestFlowTaskComment = flowTaskCommentService.getLatestFlowTaskComment(processInstanceId, taskInfo.getTaskDefinitionKey());
            if (null != latestFlowTaskComment && latestFlowTaskComment.getApprovalType().equals(FlowApprovalType.INTERVENE)) {
                String userId = latestFlowTaskComment.getDelegateAssignee();
                List<SysUserRespDTO> userListByDeptIds = sysUserApi.getUserListByIds(Collections.singletonList(userId));
                List<FlowUserInfoVO> userInfoList = BeanUtils.copyToList(userListByDeptIds, FlowUserInfoVO.class);
                this.buildUserMapList(userInfoList, loginUserIdSet, resultUserMapList);
            } else {
                Set<String> deptIdSet = CollUtil.newHashSet(StrUtil.split(flowTaskExt.getDeptIds(), ','));
                List<SysUserRespDTO> userListByDeptIds = sysUserApi.getUserListByDeptIds(deptIdSet);
                List<FlowUserInfoVO> userInfoList = BeanUtils.copyToList(userListByDeptIds, FlowUserInfoVO.class);
                this.buildUserMapList(userInfoList, loginUserIdSet, resultUserMapList);
            }
        }
        if (StrUtil.isNotBlank(flowTaskExt.getRoleIds())) {
            //当前节点最近一次操作是干预操作时，干预可能会将原来的处理用户干预成其他人，则从审批记录里取最新的人
            FlowTaskComment latestFlowTaskComment = flowTaskCommentService.getLatestFlowTaskComment(processInstanceId, taskInfo.getTaskDefinitionKey());
            if (null != latestFlowTaskComment && latestFlowTaskComment.getApprovalType().equals(FlowApprovalType.INTERVENE)) {
                String userId = latestFlowTaskComment.getDelegateAssignee();
                List<SysUserRespDTO> userListByIds = sysUserApi.getUserListByIds(Collections.singletonList(userId));
                List<FlowUserInfoVO> userInfoList = BeanUtils.copyToList(userListByIds, FlowUserInfoVO.class);
                this.buildUserMapList(userInfoList, loginUserIdSet, resultUserMapList);
            } else {
                Set<String> roleIdSet = CollUtil.newHashSet(StrUtil.split(flowTaskExt.getRoleIds(), ','));
                List<SysUserRespDTO> userListByRoleIds = sysUserApi.getUserListByRoleIds(roleIdSet);
                List<FlowUserInfoVO> userInfoList = BeanUtils.copyToList(userListByRoleIds, FlowUserInfoVO.class);
                this.buildUserMapList(userInfoList, loginUserIdSet, resultUserMapList);
            }
        }
        Set<String> userIdSet = new HashSet<>();
        FlowTaskComment latestFlowTaskComment;
        switch (flowTaskExt.getGroupType()) {
            case FlowConstant.GROUP_TYPE_ASSIGNEE:
                userIdSet.add(taskInfo.getAssignee());
                break;
            case FlowConstant.GROUP_TYPE_DEPT_POST_LEADER:
                //当前节点最近一次操作是干预操作时，干预可能会将原来的处理用户干预成其他人，则从审批记录里取最新的人
                latestFlowTaskComment = flowTaskCommentService.getLatestFlowTaskComment(processInstanceId, taskInfo.getTaskDefinitionKey());
                if (null != latestFlowTaskComment && latestFlowTaskComment.getApprovalType().equals(FlowApprovalType.INTERVENE)) {
                    String userId = latestFlowTaskComment.getDelegateAssignee();
                    List<SysUserRespDTO> userListByIds = sysUserApi.getUserListByIds(Collections.singletonList(userId));
                    List<FlowUserInfoVO> userInfoLists = BeanUtils.copyToList(userListByIds, FlowUserInfoVO.class);
                    this.buildUserMapList(userInfoLists, loginUserIdSet, resultUserMapList);
                    break;
                }
                String deptPostLeaderId = "";
                if (historic) {
                    //查已办，不能使用executionId来获取流程变量
                    deptPostLeaderId = (String)flowApiService.getHistoricProcessInstanceVariable(taskInfo.getProcessInstanceId(), FlowConstant.GROUP_TYPE_DEPT_POST_LEADER_VAR);
                } else {
                    //查待办
                    deptPostLeaderId = flowApiService.getExecutionVariableStringWithSafe(taskInfo.getExecutionId(), FlowConstant.GROUP_TYPE_DEPT_POST_LEADER_VAR);
                }
                List<SysUserRespDTO> respDTOList = sysUserApi.getUserListByDeptPostIds(CollUtil.newHashSet(deptPostLeaderId));
                List<FlowUserInfoVO> userInfoList = BeanUtils.copyToList(respDTOList, FlowUserInfoVO.class);
                this.buildUserMapList(userInfoList, loginUserIdSet, resultUserMapList);
                break;
            case FlowConstant.GROUP_TYPE_UP_DEPT_POST_LEADER:
                //当前节点最近一次操作是干预操作时，干预可能会将原来的处理用户干预成其他人，则从审批记录里取最新的人
                latestFlowTaskComment = flowTaskCommentService.getLatestFlowTaskComment(processInstanceId, taskInfo.getTaskDefinitionKey());
                if (null != latestFlowTaskComment && latestFlowTaskComment.getApprovalType().equals(FlowApprovalType.INTERVENE)) {
                    String userId = latestFlowTaskComment.getDelegateAssignee();
                    List<SysUserRespDTO> userListByIds = sysUserApi.getUserListByIds(Collections.singletonList(userId));
                    List<FlowUserInfoVO> userInfoLists = BeanUtils.copyToList(userListByIds, FlowUserInfoVO.class);
                    this.buildUserMapList(userInfoLists, loginUserIdSet, resultUserMapList);
                    break;
                }
                String upDeptPostLeaderId = "";
                if (historic) {
                    //查已办，不能使用executionId来获取流程变量
                    upDeptPostLeaderId = (String)flowApiService.getHistoricProcessInstanceVariable(taskInfo.getProcessInstanceId(), FlowConstant.GROUP_TYPE_UP_DEPT_POST_LEADER_VAR);
                } else {
                    //查待办
                    upDeptPostLeaderId = flowApiService.getExecutionVariableStringWithSafe(taskInfo.getExecutionId(), FlowConstant.GROUP_TYPE_UP_DEPT_POST_LEADER_VAR);
                }
                List<SysUserRespDTO> respDTOs = sysUserApi.getUserListByDeptPostIds(CollUtil.newHashSet(upDeptPostLeaderId));
                List<FlowUserInfoVO> upUserInfoList = BeanUtils.copyToList(respDTOs, FlowUserInfoVO.class);
                this.buildUserMapList(upUserInfoList, loginUserIdSet, resultUserMapList);
                break;
            case FlowConstant.GROUP_TYPE_DYNAMIC_APPOINT_APPROVE_USER:
                //当前节点最近一次操作是干预操作时，干预可能会将原来的处理用户干预成其他人，则从审批记录里取最新的人
                latestFlowTaskComment = flowTaskCommentService.getLatestFlowTaskComment(processInstanceId, taskInfo.getTaskDefinitionKey());
                if (null != latestFlowTaskComment && latestFlowTaskComment.getApprovalType().equals(FlowApprovalType.INTERVENE)) {
                    String userId = latestFlowTaskComment.getDelegateAssignee();
                    userIdSet.add(userId);
                    break;
                }
                Object userIdJoint;
                if (historic) {
                    //对于历史任务，流程变量需要从act_hi_varinst表里获取参数及对应的值
                    userIdJoint = flowApiService.getHistoricProcessInstanceVariableByName(processInstanceId, flowTaskExt.getDynamicAppointField());
                } else {
                    //对于当前待办任务，如果是从其他节点通过干预操作干预过来的，在获取任务处理人时特殊处理下，从流程变量里获取,而不是从干预生成的task中获取（干预接口在干预成功后将节点对应的任务处理人封装在了流程变量里面了）
                    Object variableValue = runtimeService.getVariable(taskInfo.getProcessInstanceId(), taskInfo.getTaskDefinitionKey());
                    if (null != variableValue) {
                        userIdJoint = variableValue.toString();
                    } else {
                        userIdJoint = flowApiService.getTaskVariableStringWithSafe(taskInfo.getId(), flowTaskExt.getDynamicAppointField());
                    }
                }
                List<String> userIds = Arrays.asList(StringUtils.split(String.valueOf(userIdJoint), StrUtil.COMMA));
                userIdSet.addAll(userIds);
                break;
            case FlowConstant.GROUP_TYPE_DYNAMIC_APPOINT_APPROVE_DEPT:
                //当前节点最近一次操作是干预操作时，干预可能会将原来的处理用户干预成其他人，则从审批记录里取最新的人
                latestFlowTaskComment = flowTaskCommentService.getLatestFlowTaskComment(processInstanceId, taskInfo.getTaskDefinitionKey());
                if (null != latestFlowTaskComment && latestFlowTaskComment.getApprovalType().equals(FlowApprovalType.INTERVENE)) {
                    String userId = latestFlowTaskComment.getDelegateAssignee();
                    List<SysUserRespDTO> userListByIds = sysUserApi.getUserListByIds(Collections.singletonList(userId));
                    List<FlowUserInfoVO> userInfoLists = BeanUtils.copyToList(userListByIds, FlowUserInfoVO.class);
                    this.buildUserMapList(userInfoLists, loginUserIdSet, resultUserMapList);
                    break;
                }
                Object deptId;
                if (historic) {
                    // 对于已办任务需要去历史参数记录表里获取流程变量，虽然act_ru_task表里有该流程变量，但是查不出来
                    deptId = flowApiService.getHistoricProcessInstanceVariableByName(processInstanceId, flowTaskExt.getDynamicAppointField());
                } else {
                    deptId = flowApiService.getTaskVariableStringWithSafe(taskInfo.getId(), flowTaskExt.getDynamicAppointField());
                }
                if (null != deptId) {
                    List<SysUserRespDTO> userListByDeptIds = sysUserApi.getUserListByDeptIds(Arrays.asList(deptId.toString().split(",")));
                    List<FlowUserInfoVO> flowUserInfoVOS = BeanUtils.copyToList(userListByDeptIds, FlowUserInfoVO.class);
                    this.buildUserMapList(flowUserInfoVOS, loginUserIdSet, resultUserMapList);
                }
                break;
            default:
                break;
        }
        List<String> candidateUsernames = flowApiService.getCandidateUsernames(flowTaskExt, taskInfo.getId());
        if (CollUtil.isNotEmpty(candidateUsernames)) {
            userIdSet.addAll(candidateUsernames);
        }
        if (isMultiInstanceTask) {
            List<String> assigneeList = this.getAssigneeList(taskInfo.getExecutionId(), taskInfo.getId());
            if (CollUtil.isNotEmpty(assigneeList)) {
                userIdSet.addAll(assigneeList);
            }
        }
        if (CollUtil.isNotEmpty(userIdSet)) {
            List<SysUserRespDTO> sysUserRespDTOS = sysUserApi.getUserListByIds(userIdSet);
            List<FlowUserInfoVO> userInfoList = BeanUtil.copyToList(sysUserRespDTOS, FlowUserInfoVO.class);
            this.buildUserMapList(userInfoList, loginUserIdSet, resultUserMapList);
        }
        Tuple2<Set<String>, Set<String>> tuple2 = flowApiService.getDeptPostIdAndPostIds(flowTaskExt, processInstanceId, historic);
        Set<String> postIdSet = tuple2.getSecond();
        Set<String> deptPostIdSet = tuple2.getFirst();
        if (CollUtil.isNotEmpty(postIdSet)) {
            List<SysUserRespDTO> userListByPostIds = sysUserApi.getUserListByPostIds(postIdSet);
            List<FlowUserInfoVO> userInfoList = BeanUtils.copyToList(userListByPostIds, FlowUserInfoVO.class);
            this.buildUserMapList(userInfoList, loginUserIdSet, resultUserMapList);
        }
        if (CollUtil.isNotEmpty(deptPostIdSet)) {
            List<SysUserRespDTO> sysUserRespDTOS = sysUserApi.getUserListByDeptPostIds(deptPostIdSet);
            List<FlowUserInfoVO> userInfoList = BeanUtil.copyToList(sysUserRespDTOS, FlowUserInfoVO.class);
            this.buildUserMapList(userInfoList, loginUserIdSet, resultUserMapList);
        }
        return resultUserMapList;
    }

    private void buildUserMapList(
        List<FlowUserInfoVO> userInfoList, Set<String> loginNameSet, List<FlowUserInfoVO> userMapList) {
        if (CollUtil.isEmpty(userInfoList)) {
            return;
        }
        for (FlowUserInfoVO userInfo : userInfoList) {
            if (!loginNameSet.contains(userInfo.getUserId())) {
                loginNameSet.add(userInfo.getUserId());
                userMapList.add(userInfo);
            }
        }
    }

    private void checkDynamicAppointUserOrDept(FlowTaskExt flowTaskExt, UserTask userTask) {
        List<FlowableListener> taskListeners = userTask.getTaskListeners();
        if (CollectionUtils.isAnyEmpty(taskListeners)) {
            throw new BusinessException(userTask.getName() + "节点处理人是动态指定方式，未配置相关监听器");
        }
        String fieldName;
        FlowableListener dynamicAppointUserOrDeptListener = null;
        if (flowTaskExt.getGroupType().equals(FlowConstant.GROUP_TYPE_DYNAMIC_APPOINT_APPROVE_USER)) {
            for (FlowableListener taskListener : taskListeners) {
                if (taskListener.getImplementation().contains("FlowAppointApprovalUser")) {
                    dynamicAppointUserOrDeptListener = taskListener;
                    break;
                }
            }
            if (null == dynamicAppointUserOrDeptListener) {
                throw new BusinessException(userTask.getName() + "节点处理人是动态指定审批人方式，未配置FlowAppointApprovalUser监听器");
            }
            List<FieldExtension> fieldExtensions = dynamicAppointUserOrDeptListener.getFieldExtensions();
            if (CollectionUtils.isAnyEmpty(fieldExtensions)) {
                throw new BusinessException(userTask.getName() + "节点FlowAppointApprovalUser监听器未配置任何注入字段");
            }
            String injectFieldName = fieldExtensions.get(0).getFieldName();
            fieldName = fieldExtensions.get(0).getStringValue();
            if (!injectFieldName.equals(FlowConstant.APPOINTAPPROVALUSER)) {
                throw new BusinessException(userTask.getName() + "节点使用动态指定审批人方式，请使用正确的注入字段：appointApprovalUser");
            }
            if (StringUtils.isBlank(fieldName)) {
                throw new BusinessException(userTask.getName() + "节点使用动态指定审批人方式，请配置该节点指定审批人的传参字段");
            }
        } else {
            for (FlowableListener taskListener : taskListeners) {
                if (taskListener.getImplementation().contains("FlowDynamicAppointDeptListener")) {
                    dynamicAppointUserOrDeptListener = taskListener;
                    break;
                }
            }
            if (null == dynamicAppointUserOrDeptListener) {
                throw new BusinessException(userTask.getName() + "节点处理人是动态指定审批部门方式，未配置FlowDynamicAppointDeptListener监听器");
            }
            List<FieldExtension> fieldExtensions = dynamicAppointUserOrDeptListener.getFieldExtensions();
            if (CollectionUtils.isAnyEmpty(fieldExtensions)) {
                throw new BusinessException(userTask.getName() + "节点FlowDynamicAppointDeptListener监听器未配置任何注入字段");
            }
            String injectFieldName = fieldExtensions.get(0).getFieldName();
            fieldName = fieldExtensions.get(0).getStringValue();
            if (!injectFieldName.equals(FlowConstant.DYNAMIC_APPROVER_DEPT_VAR)) {
                throw new BusinessException(userTask.getName() + "节点使用动态指定部门方式，请使用正确的注入字段：dynamicApproverDept");
            }
            if (StringUtils.isBlank(fieldName)) {
                throw new BusinessException(userTask.getName() + "节点使用动态指定部门方式，请配置该节点指定部门的传参字段");
            }
        }
        flowTaskExt.setDynamicAppointField(fieldName);
    }

    @Override
    public FlowTaskExt buildTaskExtByUserTask(UserTask userTask) {
        FlowTaskExt flowTaskExt = new FlowTaskExt();
        flowTaskExt.setTaskId(userTask.getId());
        String formKey = userTask.getFormKey();
        String assignee = userTask.getAssignee();
        //assignee可能是指定的具体用户如44cd9d3ab18816ce2692d1635fcac755，指定审批人${appointedAssignee}，流程发起人${startUserName}
        if (StrUtil.isNotBlank(assignee)) {
            flowTaskExt.setCandidateUsernames(assignee);
        }
        if (StrUtil.isNotBlank(formKey)) {
            TaskInfoVO taskInfoVo = JSON.parseObject(formKey, TaskInfoVO.class);
            flowTaskExt.setGroupType(taskInfoVo.getGroupType());
            //对动态指定审批人和动态指定审批部门进行必要的校验和封装传参字段
            if (flowTaskExt.getGroupType().equals(FlowConstant.GROUP_TYPE_DYNAMIC_APPOINT_APPROVE_USER) || flowTaskExt.getGroupType().equals(FlowConstant.GROUP_TYPE_DYNAMIC_APPOINT_APPROVE_DEPT)) {
                this.checkDynamicAppointUserOrDept(flowTaskExt, userTask);
            }
        }
        //获取拓展信息，extensionElements数据,如退回设置，扩展属性配置，任务通知
        JSONObject extraDataJson = this.buildFlowTaskExtensionData(userTask);
        if (extraDataJson != null) {
            flowTaskExt.setExtraDataJson(extraDataJson.toJSONString());
        }
        Map<String, List<ExtensionElement>> extensionMap = userTask.getExtensionElements();
        if (MapUtil.isEmpty(extensionMap)) {
            return flowTaskExt;
        }
        //操作按钮数据
        List<JSONObject> operationList = this.buildOperationListExtensionElement(extensionMap);
        if (CollUtil.isNotEmpty(operationList)) {
            flowTaskExt.setOperationListJson(JSON.toJSONString(operationList));
        }
        //节点配置的任务变量数据
        List<JSONObject> variableList = this.buildVariableListExtensionElement(extensionMap);
        if (CollUtil.isNotEmpty(variableList)) {
            flowTaskExt.setVariableListJson(JSON.toJSONString(variableList));
        }
        //会签设置
        JSONObject assigneeListObject = this.buildAssigneeListExtensionElement(extensionMap);
        if (assigneeListObject != null) {
            flowTaskExt.setAssigneeListJson(JSON.toJSONString(assigneeListObject));
        }
        //抄送数据
        List<JSONObject> copyList = this.buildCopyListExtensionElement(extensionMap);
        if (copyList != null) {
            flowTaskExt.setCopyListJson(JSON.toJSONString(copyList));
        }
        //审批对象分配为“岗位”
        List<JSONObject> deptPostList = this.buildDeptPostListExtensionElement(extensionMap);
        if (deptPostList != null) {
            flowTaskExt.setDeptPostListJson(JSON.toJSONString(deptPostList));
        }

        /*
         * 审批对象分配为“部门/候选用户组”
         * userCandidateGroups是部门，候选用户组类型，角色类型（目前角色拿掉了）
         * 候选用户组是：<flowable:userCandidateGroups type="USERS" value="wangjl,admin" />
         * 部门是：<flowable:userCandidateGroups type="DEPT" value="1668912646411259904" />
         */
        JSONObject candidateGroupObject = this.buildUserCandidateGroupsExtensionElement(extensionMap);
        if (candidateGroupObject != null) {
            String type = candidateGroupObject.getString(TYPE);
            String value = candidateGroupObject.getString(VALUE);
            switch (type) {
                case "DEPT":
                    flowTaskExt.setDeptIds(value);
                    break;
                case "ROLE":
                    flowTaskExt.setRoleIds(value);
                    break;
                case "USERS":
                    flowTaskExt.setCandidateUsernames(value);
                    break;
                default:
                    break;
            }
        }
        return flowTaskExt;
    }

    @Override
    public List<FlowTaskExt> buildTaskExtList(BpmnModel bpmnModel) {
        List<Process> processList = bpmnModel.getProcesses();
        List<FlowTaskExt> flowTaskExtList = new LinkedList<>();
        for (Process process : processList) {
            for (FlowElement element : process.getFlowElements()) {
                if (element instanceof UserTask) {
                    FlowTaskExt flowTaskExt = this.buildTaskExtByUserTask((UserTask)element);
                    flowTaskExtList.add(flowTaskExt);
                }
            }
        }
        return flowTaskExtList;
    }

    @Override
    public List<JSONObject> buildOperationListExtensionElement(Map<String, List<ExtensionElement>> extensionMap) {
        List<ExtensionElement> formOperationElements =
            this.getMyExtensionElementList(extensionMap, "operationList", "formOperation");
        if (CollUtil.isEmpty(formOperationElements)) {
            return Collections.emptyList();
        }
        List<JSONObject> resultList = new LinkedList<>();
        //封装操作按钮对应的属性  <flowable:formOperation id="1691647363866" label="提交" type="agree" showOrder="0" latestApprovalStatus="1" />
        for (ExtensionElement e : formOperationElements) {
            JSONObject operationJsonData = new JSONObject();
            operationJsonData.put(ID, e.getAttributeValue(null, ID));
            operationJsonData.put(LABEL, e.getAttributeValue(null, LABEL));
            operationJsonData.put(TYPE, e.getAttributeValue(null, TYPE));
            operationJsonData.put("showOrder", e.getAttributeValue(null, "showOrder"));
            operationJsonData.put("latestApprovalStatus", e.getAttributeValue(null, "latestApprovalStatus"));
            String multiSignAssignee = e.getAttributeValue(null, "multiSignAssignee");
            if (StrUtil.isNotBlank(multiSignAssignee)) {
                operationJsonData.put("multiSignAssignee",
                    JSON.parseObject(multiSignAssignee, FlowTaskMultiSignAssignDTO.class));
            }
            resultList.add(operationJsonData);
        }
        return resultList;
    }

    @Override
    public List<JSONObject> buildVariableListExtensionElement(Map<String, List<ExtensionElement>> extensionMap) {
        List<ExtensionElement> formVariableElements =
            this.getMyExtensionElementList(extensionMap, "variableList", "formVariable");
        if (CollUtil.isEmpty(formVariableElements)) {
            return Collections.emptyList();
        }
        Set<String> variableIdSet = new HashSet<>();
        //封装variableList里的<flowable:formVariable id="1689511212871585792" />属性值
        for (ExtensionElement e : formVariableElements) {
            String id = e.getAttributeValue(null, ID);
            variableIdSet.add(id);
        }
        //FlowEntryVariable根据基础信息里选择的任务变量id去FlowEntryVariable表里查询任务变量对象（例如：请假天数变量，指定审批人变量）
        //List<FlowEntryVariable> variableList = flowEntryVariableService.getInList(variableIdSet);
        List<FlowEntryVariable> variableList = flowEntryVariableService.getFlowEntryVariableListByIdIn(variableIdSet);
        List<JSONObject> resultList = new LinkedList<>();
        for (FlowEntryVariable variable : variableList) {
            resultList.add((JSONObject)JSON.toJSON(variable));
        }
        return resultList;
    }

    @Override
    public FlowElementExtPropertyDTO buildFlowElementExt(FlowElement element) {
        JSONObject propertiesData = this.buildFlowElementExtToJson(element);
        return propertiesData == null ? null : propertiesData.toJavaObject(FlowElementExtPropertyDTO.class);
    }

    @Override
    public JSONObject buildFlowElementExtToJson(FlowElement element) {
        Map<String, List<ExtensionElement>> extensionMap = element.getExtensionElements();
        List<ExtensionElement> propertiesElements = this.getMyExtensionElementList(extensionMap, "properties", "property");
        if (CollUtil.isEmpty(propertiesElements)) {
            return null;
        }
        JSONObject propertiesData = new JSONObject();
        for (ExtensionElement e : propertiesElements) {
            String name = e.getAttributeValue(null, NAME);
            String value = e.getAttributeValue(null, VALUE);
            propertiesData.put(name, value);
        }
        return propertiesData;
    }

    @Override
    public List<FlowTaskExt> getNodeInfoByProcessDefinitioniIdAndTaskKey(String processDefinitioniId, String taskKey) {
        LambdaQueryWrapper<FlowTaskExt> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FlowTaskExt::getProcessDefinitionId, processDefinitioniId);
        if (StringUtils.isNotBlank(taskKey)) {
            queryWrapper.eq(FlowTaskExt::getTaskId, taskKey);
        }
        return this.list(queryWrapper);
    }

    private JSONObject buildFlowTaskExtensionData(UserTask userTask) {
        JSONObject extraDataJson = this.buildFlowElementExtToJson(userTask);
        Map<String, List<ExtensionAttribute>> attributeMap = userTask.getAttributes();
        if (MapUtil.isEmpty(attributeMap)) {
            return extraDataJson;
        }
        if (extraDataJson == null) {
            extraDataJson = new JSONObject();
        }
        this.buildFlowTaskTimeoutExtensionData(attributeMap, extraDataJson);
        List<ExtensionAttribute> rejectTypeAttributes = attributeMap.get(FlowConstant.USER_TASK_REJECT_TYPE_KEY);
        if (CollUtil.isNotEmpty(rejectTypeAttributes)) {
            extraDataJson.put(FlowConstant.USER_TASK_REJECT_TYPE_KEY, rejectTypeAttributes.get(0).getValue());
        }
        List<ExtensionAttribute> sendMsgTypeAttributes = attributeMap.get("sendMessageType");
        if (CollUtil.isNotEmpty(sendMsgTypeAttributes)) {
            ExtensionAttribute attribute = sendMsgTypeAttributes.get(0);
            extraDataJson.put(FlowConstant.USER_TASK_NOTIFY_TYPES_KEY, StrUtil.split(attribute.getValue(), ","));
        }
        return extraDataJson;
    }


    private void buildFlowTaskTimeoutExtensionData(
        Map<String, List<ExtensionAttribute>> attributeMap, JSONObject extraDataJson) {
        //解析并封装超时自动审批相关参数
        List<ExtensionAttribute> timeoutAutoApproveExtensionAttributes = attributeMap.get(FlowConstant.TIME_OUT_AUTO_APPROVE);
        if (CollUtil.isNotEmpty(timeoutAutoApproveExtensionAttributes)) {
            //解析超时自动通过类型
            String timeoutAutoApproveHandleWay = timeoutAutoApproveExtensionAttributes.get(0).getValue();
            extraDataJson.put(FlowConstant.TIME_OUT_AUTO_APPROVE, timeoutAutoApproveHandleWay);
            List<ExtensionAttribute> timeoutAutoApproveHoursAttributes = attributeMap.get(FlowConstant.TIME_OUT_AUTO_APPROVE_HOURS);
            if (CollUtil.isEmpty(timeoutAutoApproveHoursAttributes)) {
                throw new BusinessException("没有设置超时自动审批任务超时小时数！");
            }
            Integer timeoutHours = Integer.valueOf(timeoutAutoApproveHoursAttributes.get(0).getValue());
            extraDataJson.put(FlowConstant.TIME_OUT_AUTO_APPROVE_HOURS, timeoutHours);
            //校验超时自动审批为自动通过时，超时处理人是否传了，超时处理人用来超时任务执行时记录为任务的真正处理人
            if (StrUtil.equalsAny(timeoutAutoApproveHandleWay, FlowUserTaskExtData.TIMEOUT_AUTO_COMPLETE, FlowUserTaskExtData.TIMEOUT_AUTO_REJECT)) {
                List<ExtensionAttribute> defaultAssigneeAttributes = attributeMap.get(FlowConstant.TASK_TIMEOUT_DEFAULT_ASSIGNEE);
                if (CollUtil.isEmpty(defaultAssigneeAttributes)) {
                    throw new BusinessException("没有设置超时任务处理人！");
                }
                extraDataJson.put(FlowConstant.TASK_TIMEOUT_DEFAULT_ASSIGNEE, defaultAssigneeAttributes.get(0).getValue());
                List<ExtensionAttribute> defaultAssigneeDeptAttributes = attributeMap.get(FlowConstant.TASK_TIMEOUT_DEFAULT_ASSIGNEE_DEPT);
                if (CollUtil.isEmpty(defaultAssigneeDeptAttributes)) {
                    throw new BusinessException("默认的超时任务处理人没有选择部门！");
                }
                extraDataJson.put(FlowConstant.TASK_TIMEOUT_DEFAULT_ASSIGNEE_DEPT, defaultAssigneeDeptAttributes.get(0).getValue());
            }
            //封装审批意见
            if (CollUtil.isNotEmpty(attributeMap.get(FlowConstant.TIME_OUT_AUTO_APPROVE_OPINION))) {
                extraDataJson.put(FlowConstant.TIME_OUT_AUTO_APPROVE_OPINION, attributeMap.get(FlowConstant.TIME_OUT_AUTO_APPROVE_OPINION).get(0).getValue());
            }
        }
        //解析封装超时发送通知业务
        List<ExtensionAttribute> timeoutSendMessageAttributes = attributeMap.get(FlowConstant.TIME_OUT_SEND_MESSAGE);
        if (CollUtil.isNotEmpty(timeoutSendMessageAttributes)) {
            String timeoutSendMessageValue = timeoutSendMessageAttributes.get(0).getValue();
            extraDataJson.put(FlowConstant.TIME_OUT_SEND_MESSAGE, timeoutSendMessageValue);
            List<ExtensionAttribute> timeoutSendMessageHoursAttributes = attributeMap.get(FlowConstant.TIME_OUT_SEND_MESSAGE_HOURS);
            if (CollUtil.isEmpty(timeoutSendMessageHoursAttributes)) {
                throw new BusinessException("没有设置超时发送通知任务超时小时数！");
            }
            //封装对应的超时小时数
            Integer timeoutSendMessageHours = Integer.valueOf(timeoutSendMessageHoursAttributes.get(0).getValue());
            extraDataJson.put(FlowConstant.TIME_OUT_SEND_MESSAGE_HOURS, timeoutSendMessageHours);
        }
    }

    private JSONObject buildUserCandidateGroupsExtensionElement(Map<String, List<ExtensionElement>> extensionMap) {
        JSONObject jsonData = null;
        List<ExtensionElement> elementCandidateGroupsList = extensionMap.get("userCandidateGroups");
        if (CollUtil.isEmpty(elementCandidateGroupsList)) {
            return jsonData;
        }
        jsonData = new JSONObject();
        ExtensionElement ee = elementCandidateGroupsList.get(0);
        jsonData.put(TYPE, ee.getAttributeValue(null, TYPE));
        jsonData.put(VALUE, ee.getAttributeValue(null, VALUE));
        return jsonData;
    }

    private JSONObject buildAssigneeListExtensionElement(Map<String, List<ExtensionElement>> extensionMap) {
        JSONObject jsonData = null;
        List<ExtensionElement> elementAssigneeList = extensionMap.get("assigneeList");
        if (CollUtil.isEmpty(elementAssigneeList)) {
            return jsonData;
        }
        ExtensionElement ee = elementAssigneeList.get(0);
        Map<String, List<ExtensionElement>> childExtensionMap = ee.getChildElements();
        if (MapUtil.isEmpty(childExtensionMap)) {
            return jsonData;
        }
        List<ExtensionElement> assigneeElements = childExtensionMap.get("assignee");
        if (CollUtil.isEmpty(assigneeElements)) {
            return jsonData;
        }
        JSONArray assigneeIdArray = new JSONArray();
        for (ExtensionElement e : assigneeElements) {
            assigneeIdArray.add(e.getAttributeValue(null, ID));
        }
        jsonData = new JSONObject();
        String assigneeType = ee.getAttributeValue(null, TYPE);
        jsonData.put("assigneeType", assigneeType);
        jsonData.put("assigneeList", assigneeIdArray);
        return jsonData;
    }

    private List<JSONObject> buildDeptPostListExtensionElement(Map<String, List<ExtensionElement>> extensionMap) {
        List<ExtensionElement> deptPostElements =
            this.getMyExtensionElementList(extensionMap, "deptPostList", "deptPost");
        if (CollUtil.isEmpty(deptPostElements)) {
            return Collections.emptyList();
        }
        List<JSONObject> resultList = new LinkedList<>();
        for (ExtensionElement e : deptPostElements) {
            JSONObject deptPostJsonData = new JSONObject();
            deptPostJsonData.put(ID, e.getAttributeValue(null, ID));
            deptPostJsonData.put(TYPE, e.getAttributeValue(null, TYPE));
            String postId = e.getAttributeValue(null, "postId");
            if (postId != null) {
                deptPostJsonData.put("postId", postId);
            }
            String deptPostId = e.getAttributeValue(null, "deptPostId");
            if (deptPostId != null) {
                deptPostJsonData.put("deptPostId", deptPostId);
            }
            resultList.add(deptPostJsonData);
        }
        return resultList;
    }

    private List<JSONObject> buildCopyListExtensionElement(Map<String, List<ExtensionElement>> extensionMap) {
        List<ExtensionElement> copyElements =
            this.getMyExtensionElementList(extensionMap, "copyItemList", "copyItem");
        if (CollUtil.isEmpty(copyElements)) {
            return Collections.emptyList();
        }
        List<JSONObject> resultList = new LinkedList<>();
        for (ExtensionElement e : copyElements) {
            JSONObject copyJsonData = new JSONObject();
            String type = e.getAttributeValue(null, TYPE);
            copyJsonData.put(TYPE, type);
            if (!StrUtil.equalsAny(type, FlowConstant.GROUP_TYPE_DEPT_POST_LEADER_VAR,
                FlowConstant.GROUP_TYPE_UP_DEPT_POST_LEADER_VAR,
                FlowConstant.GROUP_TYPE_USER_VAR,
                FlowConstant.GROUP_TYPE_ROLE_VAR,
                FlowConstant.GROUP_TYPE_DEPT_VAR,
                FlowConstant.GROUP_TYPE_DEPT_POST_VAR,
                FlowConstant.GROUP_TYPE_ALL_DEPT_POST_VAR,
                FlowConstant.GROUP_TYPE_SIBLING_DEPT_POST_VAR,
                FlowConstant.GROUP_TYPE_SELF_DEPT_POST_VAR,
                FlowConstant.GROUP_TYPE_UP_DEPT_POST_VAR)) {
                throw new BusinessException("Invalid TYPE [" + type + " ] for CopyItenList Extension!");
            }
            String id = e.getAttributeValue(null, ID);
            if (StrUtil.isNotBlank(id)) {
                copyJsonData.put(ID, id);
            }
            resultList.add(copyJsonData);
        }
        return resultList;
    }

    private List<ExtensionElement> getMyExtensionElementList(
        Map<String, List<ExtensionElement>> extensionMap, String rootName, String childName) {
        if (extensionMap == null) {
            return Collections.emptyList();
        }
        List<ExtensionElement> elementList = extensionMap.get(rootName);
        if (CollUtil.isEmpty(elementList)) {
            return Collections.emptyList();
        }
        if (StrUtil.isBlank(childName)) {
            return elementList;
        }
        ExtensionElement ee = elementList.get(0);
        Map<String, List<ExtensionElement>> childExtensionMap = ee.getChildElements();
        if (MapUtil.isEmpty(childExtensionMap)) {
            return Collections.emptyList();
        }
        List<ExtensionElement> childrenElements = childExtensionMap.get(childName);
        if (CollUtil.isEmpty(childrenElements)) {
            return Collections.emptyList();
        }
        return childrenElements;
    }

    private List<String> getAssigneeList(String executionId, String taskId) {
        FlowMultiInstanceTrans flowMultiInstanceTrans = flowMultiInstanceTransService.getByExecutionId(executionId, taskId);
        String multiInstanceExecId;
        if (flowMultiInstanceTrans == null) {
            multiInstanceExecId = flowApiService.getTaskVariableStringWithSafe(taskId, FlowConstant.MULTI_SIGN_TASK_EXECUTION_ID_VAR);
        } else {
            multiInstanceExecId = flowMultiInstanceTrans.getMultiInstanceExecId();
        }
        flowMultiInstanceTrans = flowMultiInstanceTransService.getWithAssigneeListByMultiInstanceExecId(multiInstanceExecId);
        return flowMultiInstanceTrans == null ? null : StrUtil.split(flowMultiInstanceTrans.getAssigneeList(), ",");
    }

    private boolean buildTransferUserList(TaskInfo taskInfo, List<FlowUserInfoVO> resultUserMapList) {
        List<FlowTaskComment> taskCommentList = flowTaskCommentService.getFlowTaskCommentListByExecutionId(taskInfo.getProcessInstanceId(), taskInfo.getId(), taskInfo.getExecutionId());
        if (CollUtil.isEmpty(taskCommentList)) {
            return false;
        }
        FlowTaskComment transferComment = null;
        for (int i = taskCommentList.size() - 1; i >= 0; i--) {
            FlowTaskComment comment = taskCommentList.get(i);
            if (StrUtil.equalsAny(comment.getApprovalType(),
                FlowApprovalType.TRANSFER, FlowApprovalType.INTERVENE)) {
                transferComment = comment;
                break;
            }
        }
        if (transferComment == null || StrUtil.isBlank(transferComment.getDelegateAssignee())) {
            return false;
        }
        Set<String> userIdSet = new HashSet<>(StrUtil.split(transferComment.getDelegateAssignee(), ","));
        List<SysUserRespDTO> userListByIds = sysUserApi.getUserListByIds(userIdSet);
        resultUserMapList.addAll(BeanUtil.copyToList(userListByIds, FlowUserInfoVO.class));
        return true;
    }

    @Override
    public List<FlowUserInfoVO> getDeptPostUserInfo(String processInstanceId, FlowTaskExt flowTaskExt, boolean historic) {
        Set<String> loginNameSet = new HashSet<>();
        List<FlowUserInfoVO> resultUserMapList = new LinkedList<>();
        Tuple2<Set<String>, Set<String>> tuple2 = flowApiService.getDeptPostIdAndPostIds(flowTaskExt, processInstanceId, historic);
        Set<String> postIdSet = tuple2.getSecond();
        Set<String> deptPostIdSet = tuple2.getFirst();
        if (CollUtil.isNotEmpty(postIdSet)) {
            List<SysUserRespDTO> userListByPostIds = sysUserApi.getUserListByPostIds(postIdSet);
            List<FlowUserInfoVO> userInfoList = BeanUtils.copyToList(userListByPostIds, FlowUserInfoVO.class);
            this.buildUserMapList(userInfoList, loginNameSet, resultUserMapList);
        }
        if (CollUtil.isNotEmpty(deptPostIdSet)) {
            List<SysUserRespDTO> sysUserRespDTOS = sysUserApi.getUserListByDeptPostIds(deptPostIdSet);
            List<FlowUserInfoVO> userInfoList = BeanUtils.copyToList(sysUserRespDTOS, FlowUserInfoVO.class);
            this.buildUserMapList(userInfoList, loginNameSet, resultUserMapList);
        }
        return resultUserMapList;
    }

  /*  public List<JSONObject> buildPropertiesListExtensionElement(Map<String, List<ExtensionElement>> extensionMap) {
        List<ExtensionElement> propertiesListElements =
                this.getMyExtensionElementList(extensionMap, "properties", "property");
        if (CollUtil.isEmpty(propertiesListElements)) {
            return Collections.emptyList();
        }
        List<JSONObject> resultList = new LinkedList<>();
        for (ExtensionElement e : propertiesListElements) {
            JSONObject operationJsonData = new JSONObject();
            operationJsonData.put(NAME, e.getAttributeValue(null, NAME));
            operationJsonData.put(VALUE, e.getAttributeValue(null, VALUE));
            resultList.add(operationJsonData);
        }
        return resultList;
    }*/
}

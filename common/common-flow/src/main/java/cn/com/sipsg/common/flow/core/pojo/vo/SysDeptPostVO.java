package cn.com.sipsg.common.flow.core.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 部门岗位VO对象
 * 
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "部门岗位VO对象")
public class SysDeptPostVO {

    /**
     * 部门岗位记录id
     */
    @Schema(description = "部门岗位记录id")
    private String deptPostId;

    /**
     * 部门id
     */
    @Schema(description = "部门id")
    private String deptId;

    /**
     * 岗位id
     */
    @Schema(description = "岗位id")
    private String postId;

    /**
     * 岗位别名
     */
    @Schema(description = "岗位别名")
    private String postAlias;

    /**
     * 部门名称
     */
    @Schema(description = "部门名称")
    private String deptName;

    /**
     * 岗位层级
     */
    @Schema(description = "岗位层级")
    private Integer postLevel;

    /**
     * 是否是领导岗
     */
    @Schema(description = "是否是领导岗")
    private Boolean leaderPost;

}

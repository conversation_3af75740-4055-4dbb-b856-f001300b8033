package cn.com.sipsg.common.flow.core.pojo.vo;

import cn.com.sipsg.common.flow.core.entity.FlowTaskTimeoutJob;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;


/**
 * 超时任务实体VO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "超时任务实体VO")
public class FlowTaskTimeoutJobVO extends FlowTaskTimeoutJob {

    /**
     * 是否有超时任务
     */
    @Schema(description = "是否有超时任务")
    private Boolean isHaveTimeoutJob = Boolean.TRUE;

    /**
     * 任务状态
     */
    @Schema(description = "任务状态")
    private Integer taskStatus;

}

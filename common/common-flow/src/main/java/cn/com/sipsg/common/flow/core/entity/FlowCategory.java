package cn.com.sipsg.common.flow.core.entity;

import cn.com.sipsg.common.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.OrderBy;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;

/**
 * 流程分类的实体对象。
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@FieldNameConstants
@TableName(value = "medox_flow_category", autoResultMap = true)
public class FlowCategory extends BaseDO {

    /**
     * 主键Id。
     */
    @TableId(value = "category_id")
    private String categoryId;

    /**
     * 应用编码。为空时，表示非第三方应用接入。
     */
    private String appCode;

    /**
     * 显示名称。
     */
    private String name;

    /**
     * 分类编码。
     */
    private String code;

    /**
     * 实现顺序。
     */
    @OrderBy(asc = true, sort = Short.MIN_VALUE)
    private Integer showOrder;

    /**
     * 内置mapper
     *//*
    @Mapper
    public interface FlowCategoryModelMapper extends BaseModelMapper<FlowCategoryVo, com.sipsg.center.common.flow.model.FlowCategory> {
    }

    public static final FlowCategoryModelMapper INSTANCE = Mappers.getMapper(FlowCategoryModelMapper.class);*/
}

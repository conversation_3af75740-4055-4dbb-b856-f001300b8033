package cn.com.sipsg.common.flow.core.listener;

import cn.com.sipsg.common.flow.core.constant.FlowApprovalType;
import cn.com.sipsg.common.flow.core.constant.FlowConstant;
import cn.com.sipsg.common.flow.core.entity.FlowTaskComment;
import cn.com.sipsg.common.flow.core.entity.FlowTaskExt;
import cn.com.sipsg.common.flow.core.pojo.dto.FlowApiOptionDTO;
import cn.com.sipsg.common.flow.core.pojo.vo.FlowUserInfoVO;
import cn.com.sipsg.common.flow.core.service.FlowApiService;
import cn.com.sipsg.common.flow.core.service.FlowTaskCommentService;
import cn.com.sipsg.common.flow.core.service.FlowTaskExtService;
import cn.com.sipsg.common.security.core.LoginUser;
import cn.com.sipsg.common.security.core.util.SecurityUtils;
import cn.com.sipsg.common.util.BeanUtils;
import cn.com.sipsg.module.system.api.upms.SysUserApi;
import cn.com.sipsg.module.system.api.upms.dto.SysUserRespDTO;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.text.StrFormatter;
import cn.hutool.extra.spring.SpringUtil;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.TaskService;
import org.flowable.engine.delegate.TaskListener;
import org.flowable.task.api.Task;
import org.flowable.task.service.delegate.DelegateTask;
import org.springframework.util.CollectionUtils;

/**
 * 针对于连续两个节点，任务处理人设置的都是岗位，是不同的岗位，但是这两个不同的岗位里的人都是同一个人，则进行默认通过该节点， 该情况目前只支持连续两个节点是普通节点，暂不支持连续两个节点中有会签任务（会签任务默认需要手动进行审批） （实现思路：上个节点的审批人可以从taskVariableData.get("submitUser")获取，也就是上个节点真正的审批人，当前节点如果设置的是POST类型的任务处理人
 * 则根据设置的岗位id，查询用户集合，用户集合包含上个节点审批人则默认通过该节点）
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Slf4j
public class DifferentPostAutoSkipListener implements TaskListener {

    /**
     * 连续两个节点，后面一个节点任务处理人设置的是岗位，如果拥有该岗位的人中包含上一个节点的审批人，则自动审批通过
     */
    private static final String DifferentPostAutoSkipListener = "3";

    private final transient FlowTaskCommentService flowTaskCommentService = SpringUtil.getBean(FlowTaskCommentService.class);

    private final transient FlowApiService flowApiService = SpringUtil.getBean(FlowApiService.class);

    private final transient FlowTaskExtService flowTaskExtService = SpringUtil.getBean(FlowTaskExtService.class);

    private final transient SysUserApi sysUserApi = SpringUtil.getBean(SysUserApi.class);

    private final transient TaskService taskService = SpringUtil.getBean(TaskService.class);

    private Boolean isAutoSkip = Convert.toBool(SpringUtil.getProperty("medox.flow.differentPostAutoSkip.enable"), false);

    @Override
    public void notify(DelegateTask delegateTask) {
        if (isAutoSkip) {
            FlowTaskComment latestFlowTask = flowTaskCommentService.lambdaQuery()
                .eq(FlowTaskComment::getProcessInstanceId, delegateTask.getProcessInstanceId()).orderByDesc(FlowTaskComment::getCreateTime).last("limit 1").one();
            //对于撤销按钮效果是又回到了当前节点，此时不让它跳过，需手动审批
            if (null != latestFlowTask && FlowApprovalType.REVOKE.equals(latestFlowTask.getApprovalType())) {
                return;
            }
            String processDefinitionId = delegateTask.getProcessDefinitionId();
            FlowTaskExt flowTaskExt = flowTaskExtService.getByProcessDefinitionIdAndTaskId(delegateTask.getProcessDefinitionId(), delegateTask.getTaskDefinitionKey());
            if (flowTaskExt.getGroupType().equals(FlowConstant.GROUP_TYPE_POST) && !flowApiService.isMultiInstanceTask(processDefinitionId, delegateTask.getTaskDefinitionKey())) {
                Set<String> postIdSet = new HashSet<>();
                Set<String> deptPostIdSet = new HashSet<>();
                Set<String> loginUserIdSet = new HashSet<>();
                flowApiService.buildDeptPostIdAndPostIdsForPost(flowTaskExt, delegateTask.getProcessInstanceId(), false, postIdSet, deptPostIdSet);
                if (CollUtil.isNotEmpty(postIdSet)) {
                    List<SysUserRespDTO> userListByPostIds = sysUserApi.getUserListByPostIds(postIdSet);
                    List<FlowUserInfoVO> userInfoList = BeanUtils.copyToList(userListByPostIds, FlowUserInfoVO.class);
                    this.buildUserLoginIdList(userInfoList, loginUserIdSet);
                }
                if (CollUtil.isNotEmpty(deptPostIdSet)) {
                    List<SysUserRespDTO> respDTOList = sysUserApi.getUserListByDeptPostIds(deptPostIdSet);
                    List<FlowUserInfoVO> userInfoList = BeanUtils.copyToList(respDTOList, FlowUserInfoVO.class);
                    this.buildUserLoginIdList(userInfoList, loginUserIdSet);
                }
                log.info("流程的processDefinitionId为：{}，流程实例processInstanceId为:{},节点为：{}，开启了上一个节点审批人和当前节点设置的岗位，岗位里包含的人员有：{},上个节点的审批人为：{}",
                    processDefinitionId, delegateTask.getProcessInstanceId(), delegateTask.getTaskDefinitionKey(), loginUserIdSet.toString(), delegateTask.getVariable(FlowConstant.SUBMIT_USER_VAR));
                if (!CollectionUtils.isEmpty(loginUserIdSet)) {
                    String submitUser = (String)delegateTask.getVariable(FlowConstant.SUBMIT_USER_VAR);
                    //上一步审批人审批时所属的部门
                    Object deptOfApproveUser = delegateTask.getVariable(FlowConstant.DEPT_OF_APPROVE_USER);
                    if (loginUserIdSet.contains(submitUser)) {
                        log.info("开始真正执行自动审批操作");
                        //执行自动审批同意操作
                        LoginUser loginUser = SecurityUtils.getLoginUser();
                        FlowTaskComment comment = new FlowTaskComment();
                        FlowApiOptionDTO option = null;
                        if (null != loginUser) {
                            comment.setCreateBy(loginUser.getUserId());
                            comment.setCreateUserId(loginUser.getUserId());
                            comment.setCreateUsername(loginUser.getRealName());
                        } else {
                            //上一个节点可能是超时自动审批通过结束后，自动流转到当前节点，对于超时自动审批通过的，loginUser为null
                            SysUserRespDTO sysUserRespDTO = sysUserApi.getUserById(submitUser);
                            comment.setCreateBy(sysUserRespDTO.getUserId());
                            comment.setCreateUserId(submitUser);
                            comment.setCreateUsername(sysUserRespDTO.getRealName());
                            option = new FlowApiOptionDTO();
                            option.setDeleteTimeoutTaskJob(false);
                            option.setIsTimeoutAutoComplete(true);
                        }
                        Task task = flowApiService.getTaskById(delegateTask.getId());
                        comment.fillWith(task);
                        comment.setApprovalType(FlowApprovalType.AGREE);
                        comment.setDeptOfApproveUser(deptOfApproveUser.toString());
                        comment.setTaskComment(StrFormatter.format("自动跳过审批。审批人 [{}], 跳过原因 [{}]。",
                            SecurityUtils.getLoginUser() == null ? submitUser : SecurityUtils.getLoginUserName(), "配置的岗位里人员包含上一个节点的审批人，自动审批通过"));
                        //对于岗位自动跳过的，真正的审批人也需要主动拾取下任务，修复已办任务无记录问题
                        taskService.claim(delegateTask.getId(), submitUser);
                        flowApiService.completeTask(task, comment, null, option);
                    }
                }
            }
        }
    }

    private void buildUserLoginIdList(
        List<FlowUserInfoVO> userInfoList, Set<String> loginNameSet) {
        if (CollUtil.isEmpty(userInfoList)) {
            return;
        }
        for (FlowUserInfoVO userInfo : userInfoList) {
            if (!loginNameSet.contains(userInfo.getUserId())) {
                loginNameSet.add(userInfo.getUserId());
            }
        }
    }


}

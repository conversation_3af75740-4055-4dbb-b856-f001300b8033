package cn.com.sipsg.common.flow.core.listener;

import cn.com.sipsg.common.flow.core.constant.FlowConstant;
import cn.com.sipsg.common.flow.core.entity.FlowWorkOrder;
import cn.com.sipsg.common.flow.core.service.FlowWorkOrderService;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.flowable.common.engine.api.delegate.Expression;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.ExecutionListener;

/**
 * 工作流流程结束后，触发回调业务接口的监听器
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Slf4j
public class FlowEndInteractionBusinessListener implements ExecutionListener {

    private final transient FlowWorkOrderService flowWorkOrderService = SpringUtil.getBean(FlowWorkOrderService.class);

    private Expression businessCallbackUrl;

    @Override
    public void notify(DelegateExecution execution) {
        log.info("监听器：{}", FlowEndInteractionBusinessListener.class);
        if (!StrUtil.equals("end", execution.getEventName())) {
            return;
        }
        Object callBackUrl = businessCallbackUrl.getValue(execution);
        if (null == callBackUrl) {
            log.info("流程,processInstanceId值为：{}的流程执行结束，发起回调，配置了回调监听器，但是未配置回调接口，无法执行回调", execution.getProcessInstanceId());
            return;
        }
        Object token = execution.getVariable(FlowConstant.AUTHORIZATION_HEADER);
        String processInstanceId = execution.getProcessInstanceId();
        FlowWorkOrder workOrder = flowWorkOrderService.getFlowWorkOrderByProcessInstanceId(processInstanceId);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("processInstanceId", processInstanceId);
        jsonObject.put("businessKey", workOrder.getBusinessKey());
        jsonObject.put("processDefinitionKey", workOrder.getProcessDefinitionKey());
        jsonObject.put("processDefinitionId", workOrder.getProcessDefinitionId());
        jsonObject.put("approvalType", execution.getVariable("operationType").toString());
        log.info("流程processDefinitionKey为：{},processInstanceId值为：{}的流程执行结束，发起回调，回调请求参数为：{}，请求Token为：{}，请求回调接口路径为：{}", workOrder.getProcessDefinitionKey(), processInstanceId,
            com.alibaba.fastjson2.JSONObject.toJSONString(jsonObject), token.toString(), callBackUrl.toString());
        String body = HttpRequest.post(callBackUrl.toString())
            .header(FlowConstant.AUTHORIZATION_HEADER, token.toString())
            .body(jsonObject.toJSONString())
            .execute().body();
        log.info("流程processDefinitionKey为：{},processInstanceId值为：{}，的流程执行结束，发起回调，回调结果为：{}", workOrder.getProcessDefinitionKey(), processInstanceId, body);

    }
}

package cn.com.sipsg.common.flow.core.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * 流程发布信息的Vo对象
 *
 * <AUTHOR>
 * @since 2025/04/14
 */

@Getter
@Setter
@Schema(description = "流程发布信息的Vo对象")
public class FlowEntryPublishVO {

    /**
     * 主键Id。
     */
    @Schema(description = "主键Id")
    private String entryPublishId;

    /**
     * 发布版本。
     */
    @Schema(description = "发布版本")
    private Integer publishVersion;

    /**
     * 流程引擎中的流程定义Id。
     */
    @Schema(description = "流程引擎中的流程定义Id")
    private String processDefinitionId;

    /**
     * 激活状态。
     */
    @Schema(description = "激活状态")
    private Boolean activeStatus;

    /**
     * 是否为主版本。
     */
    @Schema(description = "是否为主版本")
    private Boolean mainVersion;

    /**
     * 创建者Id。
     */
    @Schema(description = "创建者Id")
    private String createBy;

    /**
     * 发布时间。
     */
    @Schema(description = "发布时间")
    private LocalDateTime publishTime;
}

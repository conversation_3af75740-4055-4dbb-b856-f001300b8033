package cn.com.sipsg.common.flow.core.listener;

import cn.com.sipsg.common.flow.core.constant.FlowApprovalType;
import cn.com.sipsg.common.flow.core.constant.FlowConstant;
import cn.com.sipsg.common.flow.core.entity.FlowTaskComment;
import cn.com.sipsg.common.flow.core.entity.FlowTaskExt;
import cn.com.sipsg.common.flow.core.service.FlowApiService;
import cn.com.sipsg.common.flow.core.service.FlowTaskCommentService;
import cn.com.sipsg.common.flow.core.service.FlowTaskExtService;
import cn.com.sipsg.common.security.core.LoginUser;
import cn.com.sipsg.common.security.core.util.SecurityUtils;
import cn.com.sipsg.module.system.api.upms.SysUserApi;
import cn.com.sipsg.module.system.api.upms.dto.SysUserRespDTO;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.text.StrFormatter;
import cn.hutool.extra.spring.SpringUtil;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.TaskService;
import org.flowable.engine.delegate.TaskListener;
import org.flowable.task.api.Task;
import org.flowable.task.service.delegate.DelegateTask;

/**
 * 通过监听器实现动态指定审批人时，当前节点和上一个节点审批人是同一个人时自动跳过监听器
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Slf4j
public class AynamicAppointApproveUserSkipListener implements TaskListener {

    private final transient FlowTaskExtService flowTaskExtService = SpringUtil.getBean(FlowTaskExtService.class);

    private final transient FlowApiService flowApiService = SpringUtil.getBean(FlowApiService.class);

    private final transient SysUserApi sysUserApi = SpringUtil.getBean(SysUserApi.class);

    private final transient TaskService taskService = SpringUtil.getBean(TaskService.class);

    private final transient FlowTaskCommentService flowTaskCommentService = SpringUtil.getBean(FlowTaskCommentService.class);

    private Boolean isAutoSkip = Convert.toBool(SpringUtil.getProperty("medox.flow.aynamicAppointApproveUserAutoSkip.enable"), false);

    @Override
    public void notify(DelegateTask delegateTask) {
        if (isAutoSkip) {
            FlowTaskComment latestFlowTask = flowTaskCommentService.lambdaQuery()
                .eq(FlowTaskComment::getProcessInstanceId, delegateTask.getProcessInstanceId()).orderByDesc(FlowTaskComment::getCreateTime).last("limit 1").one();
            //对于撤销按钮效果是又回到了当前节点，如果当前节点配置了该监听器会自动跳过的，此时不让它跳过，需手动审批.对当前节点进行干预操作，比如换一个人，不能让跳过，需要手动审批
            if (null != latestFlowTask && FlowApprovalType.REVOKE.equals(latestFlowTask.getApprovalType()) ||
                FlowApprovalType.INTERVENE.equals(latestFlowTask.getApprovalType())) {
                return;
            }
            FlowTaskExt flowTaskExt = flowTaskExtService.getByProcessDefinitionIdAndTaskId(delegateTask.getProcessDefinitionId(), delegateTask.getTaskDefinitionKey());
            if (flowTaskExt.getGroupType().equals(FlowConstant.GROUP_TYPE_DYNAMIC_APPOINT_APPROVE_USER)) {
                //对于有些项目，一个流程中会有很多节点都是动态指定的审批人，指定的字段是不同的字段，所以每个节点要记录下自己动态审批人指定的是什么字段，这里需要去flowTaskExt表里获取字段
                //通过字段去act_ru_variable表里进行查询，获取指定的审批人是谁
                Map<String, Object> variables = delegateTask.getVariables();
                Object userId = variables.get(flowTaskExt.getDynamicAppointField());
                if (null == userId) {
                    log.info(delegateTask.getName() + "节点设置的动态指定审批人参数未赋值");
                }
                //上一个节点的审批人
                String submitUser = (String)delegateTask.getVariable(FlowConstant.SUBMIT_USER_VAR);
                //上一步审批人审批时所属的部门
                String deptOfApproveUser = (String)delegateTask.getVariable(FlowConstant.DEPT_OF_APPROVE_USER);
                if (null != userId && userId.equals(submitUser)) {
                    LoginUser loginUser = SecurityUtils.getLoginUser();
                    FlowTaskComment comment = new FlowTaskComment();
                    if (null != loginUser) {
                        comment.setCreateBy(loginUser.getUserId());
                        comment.setCreateUserId(loginUser.getUserId());
                        comment.setCreateUsername(loginUser.getRealName());
                        //因为是自动跳过，审批人所属部门来源于上一步值
                        comment.setDeptOfApproveUser(deptOfApproveUser);
                    } else {
                        //对于超时自动审批通过的，loginUser为null，重新根据账号查询用户信息
                        SysUserRespDTO sysUserRespDTO = sysUserApi.getUserById(submitUser);
                        comment.setCreateBy(sysUserRespDTO.getUserId());
                        comment.setCreateUserId(submitUser);
                        comment.setCreateUsername(sysUserRespDTO.getRealName());
                        //因为是自动跳过，审批人所属部门来源于上一步值
                        comment.setDeptOfApproveUser(deptOfApproveUser);
                    }
                    Task task = flowApiService.getTaskById(delegateTask.getId());
                    comment.fillWith(task);
                    comment.setApprovalType(FlowApprovalType.AGREE);
                    comment.setTaskComment(StrFormatter.format("自动跳过审批。审批人 [{}], 跳过原因 [{}]。",
                        loginUser == null ? submitUser : loginUser.getUsername(), "指定的审批人员和上一个节点的审批人相同，自动审批通过"));
                    //对于动态指定的审批人，act_run_task表里记录任务处理人值的assignee字段是没有值的，这时候需要主动拾取下任务，缺此动作的话已办任务该用户会缺少该次审批记录(效果是赋值act_run_task和act_hi_taskinst表记录的assignee值)
                    taskService.claim(delegateTask.getId(), String.valueOf(userId));
                    flowApiService.completeTask(task, comment, null, null);
                }
            }
        }
    }

}

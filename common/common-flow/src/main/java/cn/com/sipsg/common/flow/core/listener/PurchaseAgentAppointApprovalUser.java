package cn.com.sipsg.common.flow.core.listener;

import cn.com.sipsg.common.flow.core.constant.FlowConstant;
import cn.hutool.core.util.ObjectUtil;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.delegate.TaskListener;
import org.flowable.task.service.delegate.DelegateTask;
import org.springframework.stereotype.Component;

/**
 * 采购合约部经办人任务监听器，用来动态指定处理人
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Slf4j
@Component
public class PurchaseAgentAppointApprovalUser implements TaskListener {

    @Override
    public void notify(DelegateTask delegateTask) {

        Map<String, Object> variables = delegateTask.getVariables();
        Object userId = variables.get(FlowConstant.PURCHASEAGENT);
        if (ObjectUtil.isNotNull(userId)) {
            delegateTask.setAssignee(String.valueOf(userId));
        }

    }
}

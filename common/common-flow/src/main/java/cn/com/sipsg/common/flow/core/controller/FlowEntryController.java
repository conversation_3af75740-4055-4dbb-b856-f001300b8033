package cn.com.sipsg.common.flow.core.controller;

import cn.com.sipsg.common.exception.BusinessException;
import cn.com.sipsg.common.flow.core.constant.FlowTaskType;
import cn.com.sipsg.common.flow.core.entity.FlowEntry;
import cn.com.sipsg.common.flow.core.entity.FlowEntryPublish;
import cn.com.sipsg.common.flow.core.pojo.bo.FlowEntryBO;
import cn.com.sipsg.common.flow.core.pojo.bo.FlowEntryPageBO;
import cn.com.sipsg.common.flow.core.pojo.vo.FlowEntryPublishVO;
import cn.com.sipsg.common.flow.core.pojo.vo.FlowEntryVO;
import cn.com.sipsg.common.flow.core.pojo.vo.TaskInfoVO;
import cn.com.sipsg.common.flow.core.service.FlowApiService;
import cn.com.sipsg.common.flow.core.service.FlowEntryService;
import cn.com.sipsg.common.flow.core.service.FlowTaskExtService;
import cn.com.sipsg.common.flow.core.util.MyCommonUtil;
import cn.com.sipsg.common.operationlog.core.annotation.OperationLog;
import cn.com.sipsg.common.operationlog.core.enums.OperationTypeEnum;
import cn.com.sipsg.common.pojo.CommonResult;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.util.BeanUtils;
import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.xml.stream.XMLStreamException;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.bpmn.model.ExtensionElement;
import org.flowable.bpmn.model.FlowElement;
import org.flowable.bpmn.model.Process;
import org.flowable.bpmn.model.SequenceFlow;
import org.flowable.bpmn.model.StartEvent;
import org.flowable.bpmn.model.UserTask;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 工作流流程定义接口。
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Tag(name = "工作流-流程定义控制器")
@RequiredArgsConstructor
@RestController
@RequestMapping("/flow/flowEntry")
public class FlowEntryController {

    private final FlowEntryService flowEntryService;

    private final FlowApiService flowApiService;

    private final FlowTaskExtService flowTaskExtService;

    /**
     * 新增工作流对象数据
     *
     * @param flowEntryBO 工作流对象BO新增对象
     * @return 新增对象主键Id
     */
    @OperationLog(module = "流程定义", value = "新增流程定义", type = OperationTypeEnum.SAVE)
    @Operation(summary = "新增流程定义基础信息")
    @SaCheckPermission(value = "flow:entry:all")
    @PostMapping("/save")
    public CommonResult<String> save(@Validated @RequestBody FlowEntryBO flowEntryBO) {
        String flowEntryId = flowEntryService.saveNew(flowEntryBO);
        return CommonResult.data(flowEntryId);
    }

    /**
     * 更新工作流对象数据
     *
     * @param flowEntryBO 工作流对象BO新增对象
     */
    @OperationLog(module = "流程定义", value = "更新流程定义", type = OperationTypeEnum.UPDATE)
    @Operation(summary = "更新流程定义")
    @SaCheckPermission(value = "flow:entry:all")
    @PostMapping("/update")
    public CommonResult<Void> update(@Validated @RequestBody FlowEntryBO flowEntryBO) {
        flowEntryService.update(flowEntryBO);
        return CommonResult.success();
    }

    /**
     * 删除工作流对象数据
     *
     * @param entryId 删除对象主键Id
     */
    @OperationLog(module = "流程定义", value = "删除流程定义", type = OperationTypeEnum.DELETE)
    @Operation(summary = "删除流程定义")
    @Parameter(name = "entryId", description = "流程定义主键id", required = true, example = "1234")
    @SaCheckPermission(value = "flow:entry:all")
    @PostMapping("/delete")
    public CommonResult<Void> delete(@RequestParam String entryId) {
        flowEntryService.remove(entryId);
        return CommonResult.success();
    }

    /**
     * 发布工作流。 流程发布步骤 一、前置校验 1.流程基本校验：入参（entryId）校验、流程是否存在校验、应用(Appcode)所属流程校验 2.流程完整性校验：流程图是否存在、流程是否存在开始结点、流程开始节点是否有连线 二、流程发布
     *
     * @param entryId 流程主键Id
     */
    @OperationLog(module = "流程定义", value = "发布流程", type = OperationTypeEnum.OTHER)
    @Operation(summary = "发布工作流")
    @Parameter(name = "entryId", description = "流程定义ID", required = true, example = "12545")
    @SaCheckPermission(value = "flow:entry:all")
    @PostMapping("/publish")
    public CommonResult<Void> publish(@RequestParam String entryId) throws XMLStreamException {
        FlowEntry flowEntry = flowEntryService.checkExist(entryId);
        if (null == flowEntry) {
            throw new BusinessException("数据验证失败，根据entryId未查询到该流程定义");
        }
        if (StringUtils.isBlank(flowEntry.getBpmnXml())) {
            throw new BusinessException("数据验证失败，该流程没有流程图不能被发布！");
        }
        //流程图是否存在、流程是否存在开始结点、流程开始节点是否有连线,首个用户任务节点封装
        TaskInfoVO taskInfoVO = this.verifyAndGetInitialTaskInfo(flowEntry);
        String taskInfo = JSON.toJSONString(taskInfoVO);
        flowEntryService.publish(flowEntry, taskInfo);
        return CommonResult.success();
    }

    /**
     * 列出符合过滤条件的工作流列表
     *
     * @param flowEntryPageBO 查询条件，分页参数
     * @return 应答结果对象，包含查询结果集
     */
    @OperationLog(module = "流程定义", value = "流程定义分页查询列表", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "流程定义分页查询列表")
    @Parameter(name = "entryId", description = "流程定义ID", required = true, example = "12545")
    @SaCheckPermission(value = "flow:entry:all")
    @PostMapping("/page")
    public CommonResult<CommonPageVO<FlowEntryVO>> page(@RequestBody FlowEntryPageBO flowEntryPageBO) {
        CommonPageVO<FlowEntryVO> page = flowEntryService.page(flowEntryPageBO);
        return CommonResult.data(page);
    }

    /**
     * 查看指定工作流对象详情
     *
     * @param entryId 指定对象主键Id
     * @return 应答结果对象，包含对象详情
     */
    @OperationLog(module = "流程定义", value = "流程定义分页查询列表", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "查看指定工作流对象详情")
    @Parameter(name = "entryId", description = "流程定义ID", required = true, example = "12545")
    @SaCheckPermission(value = "flow:entry:all")
    @GetMapping("/detail")
    public CommonResult<FlowEntryVO> detail(@RequestParam String entryId) {
        FlowEntry flowEntry = flowEntryService.checkExist(entryId);
        if (null == flowEntry) {
            throw new BusinessException("数据验证失败，根据entryId未查询到该流程定义");
        }
        FlowEntry entryDeep = flowEntryService.getByIdDeep(entryId);
        FlowEntryVO flowEntryVO = BeanUtils.copyProperties(entryDeep, FlowEntryVO.class);
        return CommonResult.data(flowEntryVO);
    }

    /**
     * 列出指定流程的发布版本列表
     *
     * @param entryId 流程主键Id
     * @return 应答结果对象，包含流程发布列表数据
     */
    @OperationLog(module = "流程定义", value = "列出指定流程的发布版本列表", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "列出指定流程的发布版本列表")
    @Parameter(name = "entryId", description = "流程定义ID", required = true, example = "12545")
    @SaCheckPermission(value = "flow:entry:all")
    @GetMapping("/listFlowEntryPublish")
    public CommonResult<List<FlowEntryPublishVO>> listFlowEntryPublish(@RequestParam String entryId) {
        FlowEntry flowEntry = flowEntryService.checkExist(entryId);
        if (null == flowEntry) {
            throw new BusinessException("数据验证失败，根据entryId未查询到该流程定义");
        }
        List<FlowEntryPublish> flowEntryPublishList = flowEntryService.getFlowEntryPublishList(entryId);
        List<FlowEntryPublishVO> flowEntryPublishVOS = BeanUtils.copyToList(flowEntryPublishList, FlowEntryPublishVO.class);
        return CommonResult.data(flowEntryPublishVOS);
    }

    /**
     * 以字典形式返回全部FlowEntry数据集合。字典的键值为[entryId, procDefinitionName]
     *
     * @param flowEntryBO 过滤对象
     * @return 应答结果对象，包含的数据为 List<Map<String, String>>，map中包含两条记录，key的值分别是id和name，value对应具体数据
     */
    @OperationLog(module = "流程定义", value = "以字典形式返回全部FlowEntry数据集合", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "以字典形式返回全部FlowEntry数据集合")
    @GetMapping("/listDict")
    public CommonResult<List<Map<String, Object>>> listDict(FlowEntryBO flowEntryBO) {
        List<FlowEntry> resultList = flowEntryService.getFlowEntryList(BeanUtils.copyProperties(flowEntryBO, FlowEntry.class), null);
        return CommonResult.data(MyCommonUtil.toDictDataList(resultList, FlowEntry::getEntryId, FlowEntry::getProcessDefinitionName));
    }

    /**
     * 根据流程Id，获取流程引擎需要的流程标识和流程名称
     *
     * @param entryId 流程Id
     * @return 流程的部分数据
     */
    @OperationLog(module = "流程定义", value = "根据流程Id，获取流程引擎需要的流程标识和流程名称", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "根据流程Id，获取流程引擎需要的流程标识和流程名称")
    @Parameter(name = "entryId", description = "流程定义实体类id", required = true, example = "123456")
    @GetMapping("/viewDict")
    public CommonResult<Map<String, Object>> viewDict(@RequestParam String entryId) {
        FlowEntry flowEntry = flowEntryService.getByEntryId(entryId);
        if (null == flowEntry) {
            throw new BusinessException("数据验证失败，根据entryId未查询到该流程定义");
        }
        Map<String, Object> resultMap = new HashMap<>(2);
        resultMap.put("processDefinitionKey", flowEntry.getProcessDefinitionKey());
        resultMap.put("processDefinitionName", flowEntry.getProcessDefinitionName());
        return CommonResult.data(resultMap);
    }

    /**
     * 切换指定工作流的发布版本设置为主版本
     *
     * @param entryId           流程实例id
     * @param newEntryPublishId 新的工作流发布主版本对象的主键Id
     */
    @OperationLog(module = "流程定义", value = "切换指定工作的发布主版本", type = OperationTypeEnum.UPDATE)
    @Operation(summary = "切换指定工作流的发布版本设置为主版本")
    @Parameter(name = "entryId", description = "流程定义实体类id", required = true, example = "123456")
    @SaCheckPermission(value = "flow:entry:all")
    @PostMapping("/updateMainVersion")
    public CommonResult updateMainVersion(@RequestParam String entryId, @RequestParam String newEntryPublishId) {
        FlowEntry flowEntry = flowEntryService.checkExist(entryId);
        if (null == flowEntry) {
            throw new BusinessException("数据验证失败，根据entryId未查询到该流程定义");
        }
        FlowEntryPublish flowEntryPublish = flowEntryService.getFlowEntryPublishFromCache(newEntryPublishId);
        if (flowEntryPublish == null) {
            throw new BusinessException("数据验证失败，当前流程发布版本并不存在，请刷新后重试!");
        }
        if (ObjectUtil.notEqual(entryId, flowEntryPublish.getEntryId())) {
            throw new BusinessException("数据验证失败，当前工作流并不包含该工作流发布版本数据，请刷新后重试！");
        }
        if (BooleanUtil.isTrue(flowEntryPublish.getMainVersion())) {
            throw new BusinessException("数据验证失败，该版本已经为当前工作流的发布主版本，不能重复设置！");
        }
        flowEntryService.updateFlowEntryMainVersion(flowEntryService.getById(entryId), flowEntryPublish);
        return CommonResult.success();
    }

    /**
     * 挂起工作流的指定发布版本
     *
     * @param entryPublishId 工作流发布版本实体类主键Id
     */
    @OperationLog(module = "流程定义", value = "挂起工作流的指定发布版本", type = OperationTypeEnum.UPDATE)
    @Operation(summary = "挂起工作流的指定发布版本")
    @Parameter(name = "entryPublishId", description = "流程发布实体类id", required = true, example = "123456")
    @SaCheckPermission(value = "flow:entry:all")
    @PostMapping("/suspendFlowEntryPublish")
    public CommonResult suspendFlowEntryPublish(@RequestParam String entryPublishId) {
        FlowEntryPublish flowEntryPublish = flowEntryService.getFlowEntryPublishFromCache(entryPublishId);
        if (null == flowEntryPublish) {
            throw new BusinessException("数据验证失败，当前流程发布版本并不存在，请刷新后重试!");
        }
        FlowEntry flowEntry = flowEntryService.checkExist(flowEntryPublish.getEntryId());
        if (null == flowEntry) {
            throw new BusinessException("数据验证失败，根据entryId未查询到该流程定义");
        }
        if (BooleanUtil.isFalse(flowEntryPublish.getActiveStatus())) {
            throw new BusinessException("数据验证失败，当前流程发布版本已处于挂起状态！");
        }
        flowEntryService.suspendFlowEntryPublish(flowEntryPublish);
        return CommonResult.success();
    }

    /**
     * 激活工作流的指定发布版本
     *
     * @param entryPublishId 工作流发布版本实体类Id
     */
    @OperationLog(module = "流程定义", value = "激活工作流的指定发布版本", type = OperationTypeEnum.UPDATE)
    @Operation(summary = "激活工作流的指定发布版本")
    @Parameter(name = "entryPublishId", description = "流程发布实体类id", required = true, example = "123456")
    @SaCheckPermission(value = "flow:entry:all")
    @PostMapping("/activateFlowEntryPublish")
    public CommonResult activateFlowEntryPublish(@RequestParam String entryPublishId) {
        FlowEntryPublish flowEntryPublish = flowEntryService.getFlowEntryPublishFromCache(entryPublishId);
        if (flowEntryPublish == null) {
            throw new BusinessException("数据验证失败，当前流程发布版本并不存在，请刷新后重试！");
        }
        flowEntryService.checkExist(flowEntryPublish.getEntryId());
        if (BooleanUtil.isTrue(flowEntryPublish.getActiveStatus())) {
            throw new BusinessException("数据验证失败，当前流程发布版本已处于激活状态！");
        }
        flowEntryService.activateFlowEntryPublish(flowEntryPublish);
        return CommonResult.success();
    }

    private TaskInfoVO verifyAndGetInitialTaskInfo(FlowEntry flowEntry) throws XMLStreamException {
        BpmnModel bpmnModel = flowApiService.convertToBpmnModel(flowEntry.getBpmnXml());
        Process process = bpmnModel.getMainProcess();
        if (process == null) {
            throw new BusinessException("数据验证失败，当前流程标识 [" + flowEntry.getProcessDefinitionKey() + "] 关联的流程模型并不存在！");
        }
        //FlowElement是流程图中所有的元素，包括开始事件节点，结束事件节点，用户任务，节点之间的连线，其中用户任务是包括页面配置的所有信息,操作按钮，处理人，监听器，扩展属性， attributes封装的是退回设置和自动同意设置
        Collection<FlowElement> elementList = process.getFlowElements();
        FlowElement startEvent = null;
        // 验证流程是否包含 [开始事件] 节点
        for (FlowElement flowElement : elementList) {
            if (flowElement instanceof StartEvent) {
                startEvent = flowElement;
                break;
            }
        }
        if (startEvent == null) {
            throw new BusinessException("数据验证失败，当前流程图没有包含 [开始事件] 节点，请修改流程图！");
        }
        FlowElement firstTask = this.findFirstTask(elementList, startEvent);
        if (firstTask == null) {
            throw new BusinessException("数据验证失败，当前流程图没有包含 [开始事件] 节点没有任何连线，请修改流程图！");
        }
        TaskInfoVO taskInfoVo;
        if (firstTask instanceof UserTask) {
            UserTask userTask = (UserTask)firstTask;
            String formKey = userTask.getFormKey();
            if (StrUtil.isNotBlank(formKey)) {
                taskInfoVo = JSON.parseObject(formKey, TaskInfoVO.class);
            } else {
                taskInfoVo = new TaskInfoVO();
            }
            taskInfoVo.setAssignee(userTask.getAssignee());
            taskInfoVo.setTaskKey(userTask.getId());
            taskInfoVo.setTaskType(FlowTaskType.USER_TYPE);
            //map里封装的是bpmnjs文件里的extensionElements结构里面的信息，如variableList,operationList(操作按钮信息),properties（扩展属性信息）,copyItemList(抄送配置)
            Map<String, List<ExtensionElement>> extensionMap = userTask.getExtensionElements();
            if (MapUtil.isNotEmpty(extensionMap)) {
                taskInfoVo.setOperationList(flowTaskExtService.buildOperationListExtensionElement(extensionMap));
                taskInfoVo.setVariableList(flowTaskExtService.buildVariableListExtensionElement(extensionMap));
            }
        } else {
            taskInfoVo = new TaskInfoVO();
            taskInfoVo.setTaskType(FlowTaskType.OTHER_TYPE);
        }
        return taskInfoVo;
    }

    private FlowElement findFirstTask(Collection<FlowElement> elementList, FlowElement startEvent) {
        for (FlowElement flowElement : elementList) {
            //SequenceFlow是流程图中的线
            if (flowElement instanceof SequenceFlow) {
                SequenceFlow sequenceFlow = (SequenceFlow)flowElement;
                if (sequenceFlow.getSourceFlowElement().equals(startEvent)) {
                    return sequenceFlow.getTargetFlowElement();
                }
            }
        }
        return null;
    }
}

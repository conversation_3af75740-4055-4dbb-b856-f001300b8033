package cn.com.sipsg.common.flow.core.util;

import cn.hutool.core.collection.CollUtil;
import java.util.Collection;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 常用的基本工具方法
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public class MyCommonUtil {


    /**
     * 适用于所有类型的字典格式数据。该常量为字典的键字段。
     */
    public static final String DICT_ID = "id";

    /**
     * 适用于所有类型的字典格式数据。该常量为字典的名称字段。
     */
    public static final String DICT_NAME = "name";

    /**
     * 适用于所有类型的字典格式数据。该常量为字典的键父字段。
     */
    public static final String PARENT_ID = "parentId";

    /**
     * 转换为字典格式的数据列表。
     *
     * @param dataList   源数据列表。
     * @param idGetter   获取字典Id字段值的函数方法。
     * @param nameGetter 获取字典名字段值的函数方法。
     * @param <M>        源数据对象类型。
     * @param <R>        字典Id的类型。
     * @return 字典格式的数据列表。
     */
    public static <M, R> List<Map<String, Object>> toDictDataList(
        Collection<M> dataList, Function<M, R> idGetter, Function<M, String> nameGetter) {
        if (CollUtil.isEmpty(dataList)) {
            return new LinkedList<>();
        }
        return dataList.stream().map(item -> {
            Map<String, Object> dataMap = new HashMap<>(2);
            dataMap.put(DICT_ID, idGetter.apply(item));
            dataMap.put(DICT_NAME, nameGetter.apply(item));
            return dataMap;
        }).collect(Collectors.toList());
    }

}

package cn.com.sipsg.common.flow.core.listener;

import cn.com.sipsg.common.flow.core.constant.FlowConstant;
import cn.com.sipsg.common.flow.core.constant.FlowUserTaskExtData;
import cn.com.sipsg.common.flow.core.entity.FlowTaskExt;
import cn.com.sipsg.common.flow.core.pojo.vo.FlowTaskVO;
import cn.com.sipsg.common.flow.core.pojo.vo.FlowUserInfoVO;
import cn.com.sipsg.common.flow.core.service.FlowApiService;
import cn.com.sipsg.common.flow.core.service.FlowTaskExtService;
import cn.com.sipsg.common.flow.core.util.BaseFlowNotifyUtil;
import cn.com.sipsg.module.system.api.upms.SysUserApi;
import cn.com.sipsg.module.system.api.upms.dto.SysUserRespDTO;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.delegate.TaskListener;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.Task;
import org.flowable.task.service.delegate.DelegateTask;

/**
 * 任务进入待办状态时的通知监听器。
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Slf4j
public class FlowTaskNotifyListener implements TaskListener {

    private final transient FlowTaskExtService flowTaskExtService = SpringUtil.getBean(FlowTaskExtService.class);

    private final transient FlowApiService flowApiService = SpringUtil.getBean(FlowApiService.class);

    private final transient BaseFlowNotifyUtil baseFlowNotifyUtil = SpringUtil.getBean(BaseFlowNotifyUtil.class);

    private final transient SysUserApi sysUserApi = SpringUtil.getBean(SysUserApi.class);

    @Override
    public void notify(DelegateTask delegateTask) {
        String definitionId = delegateTask.getProcessDefinitionId();
        ProcessDefinition processDefinition = flowApiService.getProcessDefinitionById(definitionId);
        String instanceId = delegateTask.getProcessInstanceId();
        String taskId = delegateTask.getId();
        String taskKey = delegateTask.getTaskDefinitionKey();
        FlowTaskExt taskExt = flowTaskExtService.getByProcessDefinitionIdAndTaskId(definitionId, taskKey);
        if (StrUtil.isBlank(taskExt.getExtraDataJson())) {
            return;
        }
        FlowUserTaskExtData extData = JSON.parseObject(taskExt.getExtraDataJson(), FlowUserTaskExtData.class);
        if (CollUtil.isEmpty(extData.getFlowNotifyTypeList())) {
            return;
        }
        ProcessInstance instance = flowApiService.getProcessInstance(instanceId);
        Object initiator = flowApiService.getProcessInstanceVariable(instanceId, FlowConstant.PROC_INSTANCE_INITIATOR_VAR);
        boolean isMultiInstanceTask = flowApiService.isMultiInstanceTask(definitionId, taskKey);
        Task task = flowApiService.getProcessInstanceActiveTask(instanceId, taskId);
        List<FlowUserInfoVO> userInfoList = flowTaskExtService.getCandidateUserInfoList(instanceId, taskExt, task, isMultiInstanceTask, false);
        if (CollUtil.isEmpty(userInfoList)) {
            log.warn("ProcessDefinition [{}] Task [{}] don't find the candidate users for notification.",
                instance.getProcessDefinitionName(), task.getName());
            return;
        }
        for (String notifyType : extData.getFlowNotifyTypeList()) {
            FlowTaskVO flowTaskVo = new FlowTaskVO();
            flowTaskVo.setProcessDefinitionId(definitionId);
            flowTaskVo.setProcessDefinitionName(processDefinition.getName());
            flowTaskVo.setProcessInstanceId(instanceId);
            flowTaskVo.setTaskKey(taskKey);
            flowTaskVo.setTaskName(delegateTask.getName());
            flowTaskVo.setTaskId(delegateTask.getId());
            flowTaskVo.setBusinessKey(instance.getBusinessKey());
            SysUserRespDTO sysUserRespDTO = sysUserApi.getUserById(initiator.toString());
            flowTaskVo.setProcessInstanceInitiator(sysUserRespDTO.getRealName());
            baseFlowNotifyUtil.doNotify(notifyType, userInfoList, flowTaskVo);
        }
    }
}

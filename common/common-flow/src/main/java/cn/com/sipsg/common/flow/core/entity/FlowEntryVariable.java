package cn.com.sipsg.common.flow.core.entity;

import cn.com.sipsg.common.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;

/**
 * 流程变量实体对象。
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@FieldNameConstants
@TableName(value = "medox_flow_entry_variable", autoResultMap = true)
public class FlowEntryVariable extends BaseDO {

    /**
     * 主键Id。
     */
    @TableId(value = "variable_id")
    private String variableId;

    /**
     * 流程Id。
     */
    private String entryId;

    /**
     * 变量名。
     */
    private String variableName;

    /**
     * 显示名。
     */
    private String showName;

    /**
     * 流程变量类型。
     */
    private Integer variableType;

    /**
     * 绑定数据源Id。
     */
    private String bindDatasourceId;

    /**
     * 绑定数据源关联Id。
     */
    private String bindRelationId;

    /**
     * 绑定字段Id。
     */
    private String bindColumnId;

    /**
     * 是否内置。
     */
    private Boolean builtIn;

    /**
     * 内置内部类
     */
    /*@Mapper
    public interface FlowEntryVariableModelMapper extends BaseModelMapper<FlowEntryVariableVo, FlowEntryVariable> {
    }

    public static final FlowEntryVariableModelMapper INSTANCE = Mappers.getMapper(FlowEntryVariableModelMapper.class);*/
}

package cn.com.sipsg.common.flow.core.listener;

import cn.com.sipsg.common.flow.core.constant.FlowConstant;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.delegate.TaskListener;
import org.flowable.task.service.delegate.DelegateTask;

/**
 * 任务驳回后重新提交到该任务的通知监听器。
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Slf4j
public class FlowTaskRejectBackListener implements TaskListener {

    @Override
    public void notify(DelegateTask delegateTask) {
        Map<String, Object> variables = delegateTask.getVariables();
        if (variables.get(FlowConstant.REJECT_BACK_TO_SOURCE_DATA_VAR) != null) {
            delegateTask.setAssignee(variables.get(FlowConstant.REJECT_BACK_TO_SOURCE_DATA_VAR).toString());
        }
    }
}

package cn.com.sipsg.common.flow.core.service;

import cn.com.sipsg.common.flow.core.entity.PostAdjustment;
import cn.com.sipsg.common.flow.core.pojo.bo.PostAdjustmentBO;
import cn.com.sipsg.common.flow.core.pojo.bo.PostAdjustmentSaveDraftBO;
import cn.com.sipsg.common.flow.core.pojo.vo.FlowWorkOrderVO;
import cn.com.sipsg.common.mybatis.core.service.BaseServiceX;
import java.util.List;

/**
 * 岗位调整数据操作服务接口。
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public interface PostAdjustmentService extends BaseServiceX<PostAdjustment> {


    /**
     * 保存表单数据同时启动流程
     *
     * @param postAdjustmentBO 请求对象
     */
    void saveNewAndStartProcess(PostAdjustmentBO postAdjustmentBO);
    /**
     * 保存主表数据同时提交工作流任务。
     *
     * @param processInstanceId 流程实例Id。
     * @param taskId            流程任务Id。
     * @param flowTaskComment   审批对象。
     * @param taskVariableData  任务变量数据对象。
     * @param postAdjustment    新增主表对象。
     */
    /*void saveNewAndTakeTask(
        String processInstanceId,
        String taskId,
        FlowTaskComment flowTaskComment,
        JSONObject taskVariableData,
        PostAdjustment postAdjustment);*/

    /**
     * 启动流程同时保存草稿数据。
     *
     * @param processDefinitionId 流程定义Id。
     * @param masterData          主表数据。
     * @param slaveData           从表数据。
     * @return 流程工单对象。
     */
    //FlowWorkOrder saveNewDraftAndStartProcess(String processDefinitionId, String masterData, String slaveData,String deptOfSubmitUser);


    /**
     * 更新主表数据同时提交工作流任务。
     *
     * @param postAdjustmentBO 原有主数据对象。
     * @return 成功返回true，否则false。
     */
    boolean updateAndTakeTask(PostAdjustmentBO postAdjustmentBO);

    void buildDraftData(List<FlowWorkOrderVO> flowWorkOrderVOS);

    /**
     * 保存或更新草稿数据
     *
     * @param postAdjustmentSaveDraftBO 岗位调整请求对象
     */
    void saveOrUpdateDraft(PostAdjustmentSaveDraftBO postAdjustmentSaveDraftBO);

    /**
     * 删除指定数据。
     *
     * @param id 主键Id。
     * @return 成功返回true，否则false。
     */
    //boolean remove(String id);

    /**
     * 获取单表查询结果。由于没有关联数据查询，因此在仅仅获取单表数据的场景下，效率更高。 如果需要同时获取关联数据，请移步(getPostAdjustmentListWithRelation)方法。
     *
     * @param filter  过滤对象。
     * @param orderBy 排序参数。
     * @return 查询结果集。
     */
    //List<PostAdjustment> getPostAdjustmentList(PostAdjustment filter, String orderBy);

    /**
     * 获取主表的查询结果，以及主表关联的字典数据和一对一从表数据，以及一对一从表的字典数据。 该查询会涉及到一对一从表的关联过滤，或一对多从表的嵌套关联过滤，因此性能不如单表过滤。 如果仅仅需要获取主表数据，请移步(getPostAdjustmentList)，以便获取更好的查询性能。
     *
     * @param filter  主表过滤对象。
     * @param orderBy 排序参数。
     * @return 查询结果集。
     */
    //List<PostAdjustment> getPostAdjustmentListWithRelation(PostAdjustment filter, String orderBy);

    //void startAndTakeFirstTask(String processDefinitionId, Object dataId, FlowTaskComment comment, JSONObject variables);

    /**
     * 生成第一个任务
     *
     * @param processInstanceId
     * @param taskId
     * @param dataId
     * @param comment
     * @param variables
     */
    /*void takeMyFirstTask(
        String processInstanceId, String taskId, Object dataId, FlowTaskComment comment, JSONObject variables);

    void takeTask(Task task, Object dataId, FlowTaskComment comment, JSONObject variables);

    void updateStatus(FlowWorkOrder workOrder);*/
}

package cn.com.sipsg.common.flow.core.service.impl;

import cn.com.sipsg.common.exception.BusinessException;
import cn.com.sipsg.common.flow.core.constant.FlowConstant;
import cn.com.sipsg.common.flow.core.constant.FlowUserTaskExtData;
import cn.com.sipsg.common.flow.core.constant.FlowVariableType;
import cn.com.sipsg.common.flow.core.entity.FlowCategory;
import cn.com.sipsg.common.flow.core.entity.FlowEntry;
import cn.com.sipsg.common.flow.core.entity.FlowEntryPublish;
import cn.com.sipsg.common.flow.core.entity.FlowEntryPublishVariable;
import cn.com.sipsg.common.flow.core.entity.FlowEntryVariable;
import cn.com.sipsg.common.flow.core.entity.FlowTaskExt;
import cn.com.sipsg.common.flow.core.enums.FlowEntryStatusEnum;
import cn.com.sipsg.common.flow.core.listener.AutoSkipTaskListener;
import cn.com.sipsg.common.flow.core.listener.DeptPostLeaderListener;
import cn.com.sipsg.common.flow.core.listener.FlowFinishedListener;
import cn.com.sipsg.common.flow.core.listener.FlowRuTaskAbnormalCheckListener;
import cn.com.sipsg.common.flow.core.listener.FlowTaskNotifyListener;
import cn.com.sipsg.common.flow.core.listener.FlowTaskRejectBackListener;
import cn.com.sipsg.common.flow.core.listener.FlowTaskTimeoutListener;
import cn.com.sipsg.common.flow.core.listener.FlowUserTaskListener;
import cn.com.sipsg.common.flow.core.listener.UpDeptPostLeaderListener;
import cn.com.sipsg.common.flow.core.listener.UpdateLatestApprovalStatusListener;
import cn.com.sipsg.common.flow.core.mapper.FlowEntryMapper;
import cn.com.sipsg.common.flow.core.mapper.FlowEntryPublishMapper;
import cn.com.sipsg.common.flow.core.mapper.FlowEntryPublishVariableMapper;
import cn.com.sipsg.common.flow.core.pojo.bo.FlowEntryBO;
import cn.com.sipsg.common.flow.core.pojo.bo.FlowEntryPageBO;
import cn.com.sipsg.common.flow.core.pojo.dto.FlowElementExtPropertyDTO;
import cn.com.sipsg.common.flow.core.pojo.dto.FlowEntryDTO;
import cn.com.sipsg.common.flow.core.pojo.dto.FlowEntryExtensionDataDTO;
import cn.com.sipsg.common.flow.core.pojo.dto.FlowTaskPostCandidateGroupDTO;
import cn.com.sipsg.common.flow.core.pojo.vo.FlowEntryVO;
import cn.com.sipsg.common.flow.core.service.FlowApiService;
import cn.com.sipsg.common.flow.core.service.FlowCategoryService;
import cn.com.sipsg.common.flow.core.service.FlowEntryService;
import cn.com.sipsg.common.flow.core.service.FlowEntryVariableService;
import cn.com.sipsg.common.flow.core.service.FlowTaskExtService;
import cn.com.sipsg.common.flow.core.util.FlowRedisKeyUtil;
import cn.com.sipsg.common.mybatis.core.query.LambdaQueryWrapperX;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.redis.core.util.RedisUtil;
import cn.com.sipsg.common.security.core.LoginUser;
import cn.com.sipsg.common.security.core.util.SecurityUtils;
import cn.com.sipsg.common.util.AssertUtils;
import cn.com.sipsg.common.util.BeanUtils;
import cn.com.sipsg.common.util.ValidationUtils;
import cn.com.sipsg.common.validation.group.UpdateGroup;
import cn.com.sipsg.common.flow.core.pojo.dto.AnalyzedNodeDTO;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import javax.validation.Validator;
import javax.xml.stream.XMLInputFactory;
import javax.xml.stream.XMLStreamException;
import javax.xml.stream.XMLStreamReader;
import lombok.Cleanup;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.flowable.bpmn.converter.BpmnXMLConverter;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.bpmn.model.ExtensionAttribute;
import org.flowable.bpmn.model.FieldExtension;
import org.flowable.bpmn.model.FlowElement;
import org.flowable.bpmn.model.SequenceFlow;
import org.flowable.bpmn.model.UserTask;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.repository.Deployment;
import org.flowable.engine.repository.ProcessDefinition;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 流程实例实现类
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FlowEntryServiceImpl extends ServiceImpl<FlowEntryMapper, FlowEntry> implements FlowEntryService {


    private final FlowEntryMapper flowEntryMapper;

    private final FlowEntryPublishMapper flowEntryPublishMapper;

    private final FlowEntryPublishVariableMapper flowEntryPublishVariableMapper;

    private final FlowEntryVariableService flowEntryVariableService;

    @Autowired
    private FlowCategoryService flowCategoryService;

    @Autowired
    private FlowTaskExtService flowTaskExtService;

    @Autowired
    private FlowApiService flowApiService;

    private final RepositoryService repositoryService;

    @Autowired
    private RedisUtil redisUtil;

    private final Validator validator;

    private final RedissonClient redissonClient;

    private static final Integer FLOW_ENTRY_PUBLISH_TTL = 60 * 60 * 24;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public String saveNew(FlowEntryBO flowEntryBO) {
        flowEntryBO.setEntryId(null);
        chekSaveOrUpdate(flowEntryBO);
        checkProcessDefinitionKeyExit(flowEntryBO);
        flowCategoryService.checkExist(flowEntryBO.getCategoryId());
        FlowEntry flowEntry = BeanUtils.copyProperties(flowEntryBO, FlowEntry.class);
        flowEntry.setStatus(FlowEntryStatusEnum.UNPUBLISHED.getCode());
        LoginUser loginUser = SecurityUtils.getLoginUser();
        flowEntry.setUpdateBy(loginUser.getUserId());
        flowEntry.setCreateBy(loginUser.getUserId());
        flowEntry.setEncodedRule(JSONObject.toJSONString(flowEntryBO.getEncodedRule()));
        this.save(flowEntry);
        this.insertBuiltinEntryVariables(flowEntry.getEntryId());
        return flowEntry.getEntryId();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void publish(FlowEntry flowEntry, String initTaskInfo) throws XMLStreamException {
        redisUtil.removeFormCache(FlowRedisKeyUtil.makeFlowEntryKey(flowEntry.getProcessDefinitionKey()));
        FlowCategory flowCategory = flowCategoryService.getById(flowEntry.getCategoryId());
        InputStream xmlStream = new ByteArrayInputStream(flowEntry.getBpmnXml().getBytes(StandardCharsets.UTF_8));
        @Cleanup XMLStreamReader reader = XMLInputFactory.newInstance().createXMLStreamReader(xmlStream);
        BpmnXMLConverter converter = new BpmnXMLConverter();
        BpmnModel bpmnModel = converter.convertToBpmnModel(reader);
        bpmnModel.getMainProcess().setName(flowEntry.getProcessDefinitionName());
        bpmnModel.getMainProcess().setId(flowEntry.getProcessDefinitionKey());
        flowApiService.addProcessInstanceEndListener(bpmnModel, FlowFinishedListener.class);
        //FlowElement是从bpmnModel里解析出的包括开始结束节点，用户任务，流程之间的连线
        Collection<FlowElement> elementList = bpmnModel.getMainProcess().getFlowElements();
        //sequenceFlow是流程图上的连线对象，有自己节点id,节点source,节点target等信息
        List<FlowElement> sequenceFlowList = elementList.stream().filter(SequenceFlow.class::isInstance).collect(Collectors.toList());
        for (FlowElement sequenceFlow : sequenceFlowList) {
            /*
             *获取第一根连线配置的扩展属性，即开始事件节点连接的第一个用户任务节点的线，线上配置的扩展属性properties，发现只有第一根线可配置扩展属性，其他线都配置不了
             * 解析流转线上是否配置名为“latestApprovalStatus”的扩展属性，存在的话，添加一个更新工单状态的流程监听器
             **/
            FlowElementExtPropertyDTO extProperty = flowTaskExtService.buildFlowElementExt(sequenceFlow);
            if (extProperty != null && extProperty.getLatestApprovalStatus() != null) {
                List<FieldExtension> fieldExtensions = new LinkedList<>();
                FieldExtension fieldExtension = new FieldExtension();
                fieldExtension.setFieldName(FlowConstant.LATEST_APPROVAL_STATUS_KEY);
                fieldExtension.setStringValue(extProperty.getLatestApprovalStatus().toString());
                fieldExtensions.add(fieldExtension);
                flowApiService.addExecutionListener(sequenceFlow, UpdateLatestApprovalStatusListener.class, "start", fieldExtensions);
            }
        }
        /*
         * 从bpmnModel中解析出UserTask任务的基础配置信息，审批处理人信息，按钮操作信息
         * 获取指定流程图中所有UserTask对象的扩展节点信息，构建FLowTaskExt对象列表。
         */
        List<FlowTaskExt> flowTaskExtList = flowTaskExtService.buildTaskExtList(bpmnModel);
        if (StrUtil.isNotBlank(flowEntry.getExtensionData())) {
            //将flowEntry基础信息配置的总的通知类型添加到流程的每一个节点上
            this.mergeTaskNotifyData(flowEntry, flowTaskExtList);
        }
        //解析UserTask，添加各种用户任务监听器
        this.processFlowTaskExtList(flowTaskExtList, elementList);
        Deployment deploy = repositoryService.createDeployment()
            .addBpmnModel(flowEntry.getProcessDefinitionKey() + ".bpmn", bpmnModel)
            //.tenantId(TokenData.takeFromRequest().getAppCode())
            .tenantId(null)
            .name(flowEntry.getProcessDefinitionName())
            .key(flowEntry.getProcessDefinitionKey())
            .category(flowCategory.getCode())
            .deploy();
        ProcessDefinition processDefinition = flowApiService.getProcessDefinitionByDeployId(deploy.getId());
        FlowEntryPublish flowEntryPublish = new FlowEntryPublish();
        flowEntryPublish.setEntryId(flowEntry.getEntryId());
        flowEntryPublish.setProcessDefinitionId(processDefinition.getId());
        flowEntryPublish.setDeployId(processDefinition.getDeploymentId());
        flowEntryPublish.setPublishVersion(processDefinition.getVersion());
        flowEntryPublish.setActiveStatus(true);
        flowEntryPublish.setMainVersion(flowEntry.getStatus().equals(FlowEntryStatusEnum.UNPUBLISHED.getCode()));
        flowEntryPublish.setCreateBy(SecurityUtils.getLoginUser().getUserId());
        flowEntryPublish.setPublishTime(LocalDateTime.now());
        flowEntryPublish.setInitTaskInfo(initTaskInfo);
        AnalyzedNodeDTO rootNode = flowApiService.analyzeBpmnRoads(processDefinition.getId());
        flowEntryPublish.setAnalyzedNodeJson(JSON.toJSONString(rootNode));
        flowEntryPublishMapper.insert(flowEntryPublish);
        FlowEntry updatedFlowEntry = new FlowEntry();
        updatedFlowEntry.setEntryId(flowEntry.getEntryId());
        updatedFlowEntry.setStatus(FlowEntryStatusEnum.PUBLISHED.getCode());
        updatedFlowEntry.setLatestPublishTime(LocalDateTime.now());
        // 对于从未发布过的工作，第一次发布的时候会将本地发布重为主版本。
        if (flowEntry.getStatus().equals(FlowEntryStatusEnum.UNPUBLISHED.getCode())) {
            updatedFlowEntry.setMainEntryPublishId(flowEntryPublish.getEntryPublishId());
        }
        flowEntryMapper.updateById(updatedFlowEntry);
        FlowEntryVariable flowEntryVariableFilter = new FlowEntryVariable();
        flowEntryVariableFilter.setEntryId(flowEntry.getEntryId());
        List<FlowEntryVariable> flowEntryVariableList = flowEntryVariableService.getFlowEntryVariableList(flowEntryVariableFilter, null);
        if (CollUtil.isNotEmpty(flowTaskExtList)) {
            flowTaskExtList.forEach(t -> t.setProcessDefinitionId(processDefinition.getId()));
            flowTaskExtService.saveBatch(flowTaskExtList);
        }
        //将FlowEntryVariable参数，即flowEntry的配置的流程变量，每一次发布会维护到FlowEntryPublishVariable里
        this.insertEntryPublishVariables(flowEntryVariableList, flowEntryPublish.getEntryPublishId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(FlowEntryBO flowEntryBO) {
        ValidationUtils.validate(validator, flowEntryBO, UpdateGroup.class);
        chekSaveOrUpdate(flowEntryBO);
        FlowEntry originalFlowEntry = this.getById(flowEntryBO.getEntryId());
        if (ObjectUtil.notEqual(flowEntryBO.getProcessDefinitionKey(), originalFlowEntry.getProcessDefinitionKey())) {
            if (originalFlowEntry.getStatus().equals(FlowEntryStatusEnum.PUBLISHED.getCode())) {
                AssertUtils.isTrue(Boolean.TRUE, "数据验证失败，当前流程为发布状态，流程标识不能修改！");
            }
        }
        FlowEntry flowEntry = BeanUtils.copyProperties(flowEntryBO, FlowEntry.class);
        flowEntry.setEncodedRule(JSONObject.toJSONString(flowEntryBO.getEncodedRule()));
        this.updateById(flowEntry);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void remove(String entryId) {
        checkExist(entryId);
        FlowEntry flowEntry = this.getById(entryId);
        List<String> processDefinitionIds = flowEntryPublishMapper.selectList(FlowEntryPublish::getEntryId, entryId).stream().map(FlowEntryPublish::getProcessDefinitionId).collect(Collectors.toList());
        Boolean haveActiveTasks = flowApiService.isHaveActiveTasksByProcessDefinitionIdsIn(processDefinitionIds);
        if (haveActiveTasks) {
            throw new BusinessException("该流程有正在运行的任务，无法删除流程设计，请走完流程后删除");
        }
        redisUtil.removeFormCache(FlowRedisKeyUtil.makeFlowEntryKey(flowEntry.getProcessDefinitionKey()));
        this.removeById(flowEntry);
        flowEntryVariableService.removeByEntryId(entryId);
    }

    @Override
    public CommonPageVO<FlowEntryVO> page(FlowEntryPageBO flowEntryPageBO) {
        LambdaQueryWrapperX<FlowEntry> queryWrapperX = new LambdaQueryWrapperX<>();
        queryWrapperX.eqIfPresent(FlowEntry::getCategoryId, flowEntryPageBO.getCategoryId());
        queryWrapperX.likeIfPresent(FlowEntry::getProcessDefinitionName, flowEntryPageBO.getProcessDefinitionName());
        queryWrapperX.likeIfPresent(FlowEntry::getProcessDefinitionKey, flowEntryPageBO.getProcessDefinitionKey());
        queryWrapperX.eqIfPresent(FlowEntry::getStatus, flowEntryPageBO.getStatus());
        return this.pageDeep(flowEntryPageBO, queryWrapperX).convert(flowEntry -> BeanUtils.copyProperties(flowEntry, FlowEntryVO.class));
    }

    @Override
    public List<FlowEntry> getFlowEntryList(FlowEntry filter, String orderBy) {
        if (filter == null) {
            filter = new FlowEntry();
        }
        LambdaQueryWrapperX<FlowEntry> queryWrapperX = new LambdaQueryWrapperX<>();
        queryWrapperX.eqIfPresent(FlowEntry::getProcessDefinitionKey, filter.getProcessDefinitionKey());
        queryWrapperX.likeIfPresent(FlowEntry::getProcessDefinitionName, filter.getProcessDefinitionName());
        queryWrapperX.eqIfPresent(FlowEntry::getStatus, filter.getStatus());
        queryWrapperX.eqIfPresent(FlowEntry::getCategoryId, filter.getCategoryId());
        return this.list(queryWrapperX);
    }

    @Override
    public FlowEntry getFlowEntryFromCache(String processDefinitionKey) {
        String key = FlowRedisKeyUtil.makeFlowEntryKey(processDefinitionKey);
        LambdaQueryWrapper<FlowEntry> qw = new LambdaQueryWrapper<>();
        qw.eq(FlowEntry::getProcessDefinitionKey, processDefinitionKey);
        FlowEntry flowEntry;
        RBucket<Object> bucket = redissonClient.getBucket(key);
        if (!bucket.isExists()) {
            //因为增加了一个流程逻辑删除的功能，删除后，照顾到工单列表那里还可以看到原先申请的工单数据
            //flowEntry = this.getOneDeep(qw);
            flowEntry = flowEntryMapper.getByProcessDefinitionKey(processDefinitionKey);
            if (flowEntry != null) {
                bucket.set(JSON.toJSONString(flowEntry));
            }
        } else {
            flowEntry = JSON.parseObject((String)bucket.get(), FlowEntry.class);
        }
        return flowEntry;
    }

    @Override
    public List<FlowEntryPublish> getFlowEntryPublishList(String entryId) {
        FlowEntryPublish filter = new FlowEntryPublish();
        filter.setEntryId(entryId);
        LambdaQueryWrapper<FlowEntryPublish> queryWrapper = new LambdaQueryWrapper<>(filter);
        queryWrapper.orderByDesc(FlowEntryPublish::getCreateTime);
        return flowEntryPublishMapper.selectList(queryWrapper);
    }

    @Override
    public List<FlowEntryPublish> getFlowEntryPublishList(Set<String> processDefinitionIdSet) {
        LambdaQueryWrapper<FlowEntryPublish> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(FlowEntryPublish::getProcessDefinitionId, processDefinitionIdSet);
        return flowEntryPublishMapper.selectList(queryWrapper);
    }

    @Override
    public FlowEntryPublish getFlowEntryPublishFromCache(String entryPublishId) {
        String key = FlowRedisKeyUtil.makeFlowEntryPublishKey(entryPublishId);
        return redisUtil.getFromCache(key, entryPublishId, flowEntryPublishMapper::selectById, FlowEntryPublish.class, FLOW_ENTRY_PUBLISH_TTL);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateFlowEntryMainVersion(FlowEntry flowEntry, FlowEntryPublish newMainFlowEntryPublish) {
        redisUtil.removeFormCache(FlowRedisKeyUtil.makeFlowEntryKey(flowEntry.getProcessDefinitionKey()));
        redisUtil.removeFormCache(FlowRedisKeyUtil.makeFlowEntryPublishKey(newMainFlowEntryPublish.getEntryPublishId()));
        FlowEntryPublish oldMainFlowEntryPublish = flowEntryPublishMapper.selectById(flowEntry.getMainEntryPublishId());
        if (oldMainFlowEntryPublish != null) {
            redisUtil.removeFormCache(FlowRedisKeyUtil.makeFlowEntryPublishKey(oldMainFlowEntryPublish.getEntryPublishId()));
            oldMainFlowEntryPublish.setMainVersion(false);
            flowEntryPublishMapper.updateById(oldMainFlowEntryPublish);
        }
        newMainFlowEntryPublish.setMainVersion(true);
        flowEntryPublishMapper.updateById(newMainFlowEntryPublish);
        FlowEntry updatedEntry = new FlowEntry();
        updatedEntry.setEntryId(flowEntry.getEntryId());
        updatedEntry.setMainEntryPublishId(newMainFlowEntryPublish.getEntryPublishId());
        flowEntryMapper.updateById(updatedEntry);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void suspendFlowEntryPublish(FlowEntryPublish flowEntryPublish) {
        redisUtil.removeFormCache(FlowRedisKeyUtil.makeFlowEntryPublishKey(flowEntryPublish.getEntryPublishId()));
        FlowEntryPublish updatedEntryPublish = new FlowEntryPublish();
        updatedEntryPublish.setEntryPublishId(flowEntryPublish.getEntryPublishId());
        updatedEntryPublish.setActiveStatus(false);
        flowEntryPublishMapper.updateById(updatedEntryPublish);
        flowApiService.suspendProcessDefinition(flowEntryPublish.getProcessDefinitionId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void activateFlowEntryPublish(FlowEntryPublish flowEntryPublish) {
        redisUtil.removeFormCache(FlowRedisKeyUtil.makeFlowEntryPublishKey(flowEntryPublish.getEntryPublishId()));
        FlowEntryPublish updatedEntryPublish = new FlowEntryPublish();
        updatedEntryPublish.setEntryPublishId(flowEntryPublish.getEntryPublishId());
        updatedEntryPublish.setActiveStatus(true);
        flowEntryPublishMapper.updateById(updatedEntryPublish);
        flowApiService.activateProcessDefinition(flowEntryPublish.getProcessDefinitionId());
    }

    @Override
    public boolean existByProcessDefinitionKey(String processDefinitionKey) {
        FlowEntry filter = new FlowEntry();
        filter.setProcessDefinitionKey(processDefinitionKey);
        return CollUtil.isNotEmpty(this.getFlowEntryList(filter, null));
    }

    @Override
    public FlowEntryDTO getFlowEntryByProcessDefinitionId(String processDefinitionId) {
        FlowEntryDTO flowEntryDto = new FlowEntryDTO();
        FlowEntryPublish flowEntryPublish = flowEntryPublishMapper.selectOne(Wrappers.lambdaQuery(FlowEntryPublish.class)
            .eq(FlowEntryPublish::getProcessDefinitionId, processDefinitionId));
        if (ObjectUtil.isNull(flowEntryPublish)) {
            return flowEntryDto;
        }
        FlowEntry flowEntry = this.getById(flowEntryPublish.getEntryId());
        BeanUtils.copyProperties(flowEntry, flowEntryDto);
        flowEntryDto.setPublishVersion(flowEntryPublish.getPublishVersion());
        return flowEntryDto;
    }

    @Override
    public List<FlowEntry> getByCategoryIdAndStatus(String categoryId, String status) {
        LambdaQueryWrapperX<FlowEntry> queryWrapperX = new LambdaQueryWrapperX<>();
        queryWrapperX.eq(FlowEntry::getCategoryId, categoryId);
        queryWrapperX.eq(FlowEntry::getStatus, status);
        return this.list(queryWrapperX);
    }

    @Override
    public List<FlowEntry> getByCategoryId(String categoryId) {
        LambdaQueryWrapperX<FlowEntry> queryWrapperX = new LambdaQueryWrapperX<>();
        queryWrapperX.eq(FlowEntry::getCategoryId, categoryId);
        return this.list(queryWrapperX);
    }

    @Override
    public List<FlowEntry> getFlowEntryListByEntryIds(Set<String> entryIdSet) {
        LambdaQueryWrapper<FlowEntry> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(FlowEntry::getEntryId, entryIdSet);
        return this.list(queryWrapper);
    }

    @Override
    public FlowEntry getByEntryId(String entryId) {
        return flowEntryMapper.getByEntryId(entryId);
    }

    @Override
    public List<FlowEntry> getFlowEntryByProcessDefinitionKeyIn(List<String> processDefinitionKeys) {
        LambdaQueryWrapperX<FlowEntry> queryWrapperX = new LambdaQueryWrapperX<>();
        queryWrapperX.in(FlowEntry::getProcessDefinitionKey, processDefinitionKeys);
        return this.list(queryWrapperX);
    }

    private void chekSaveOrUpdate(FlowEntryBO flowEntryBO) {
        checkExist(flowEntryBO.getEntryId());
        this.checkProcessDefinitionKeyExit(flowEntryBO);
    }

    private void checkProcessDefinitionKeyExit(FlowEntryBO flowEntryBO) {
        long count = this.count(new LambdaQueryWrapperX<FlowEntry>().eq(FlowEntry::getProcessDefinitionKey, flowEntryBO.getProcessDefinitionKey()).neIfPresent(FlowEntry::getEntryId, flowEntryBO.getEntryId()));
        AssertUtils.isTrue(count > 0, String.format("数据验证失败，该流程定义标识已存在【%s】已存在", flowEntryBO.getProcessDefinitionKey()));
    }

    /**
     * 初始化两个流程变量
     *
     * @param entryId 流程id
     **/
    private void insertBuiltinEntryVariables(String entryId) {
        FlowEntryVariable operationTypeVariable = new FlowEntryVariable();
        operationTypeVariable.setEntryId(entryId);
        operationTypeVariable.setVariableName(FlowConstant.OPERATION_TYPE_VAR);
        operationTypeVariable.setShowName("审批类型");
        operationTypeVariable.setVariableType(FlowVariableType.TASK);
        operationTypeVariable.setBuiltIn(true);
        flowEntryVariableService.save(operationTypeVariable);
        FlowEntryVariable startUserNameVariable = new FlowEntryVariable();
        startUserNameVariable.setEntryId(entryId);
        startUserNameVariable.setVariableName("startUserName");
        startUserNameVariable.setShowName("流程启动用户");
        startUserNameVariable.setVariableType(FlowVariableType.INSTANCE);
        startUserNameVariable.setBuiltIn(true);
        flowEntryVariableService.save(startUserNameVariable);
    }

    private void insertEntryPublishVariables(List<FlowEntryVariable> entryVariableList, String entryPublishId) {
        if (CollUtil.isEmpty(entryVariableList)) {
            return;
        }
        List<FlowEntryPublishVariable> entryPublishVariableList = BeanUtils.copyToList(entryVariableList, FlowEntryPublishVariable.class);
        for (FlowEntryPublishVariable variable : entryPublishVariableList) {
            variable.setVariableId(null);
            variable.setEntryPublishId(entryPublishId);
        }
        flowEntryPublishVariableMapper.insertBatch(entryPublishVariableList);
    }

    private void mergeTaskNotifyData(FlowEntry flowEntry, List<FlowTaskExt> flowTaskExtList) {
        FlowEntryExtensionDataDTO flowEntryExtensionData = JSON.parseObject(flowEntry.getExtensionData(), FlowEntryExtensionDataDTO.class);
        if (CollUtil.isEmpty(flowEntryExtensionData.getNotifyTypes())) {
            return;
        }
        List<String> flowTaskNotifyTypes = flowEntryExtensionData.getNotifyTypes().stream().filter(StrUtil::isNotBlank).collect(Collectors.toList());
        if (CollUtil.isEmpty(flowTaskNotifyTypes)) {
            return;
        }
        for (FlowTaskExt flowTaskExt : flowTaskExtList) {
            if (flowTaskExt.getExtraDataJson() == null) {
                //节点没有配置“通知类型”，加载全局配置“通知类型”
                JSONObject o = new JSONObject();
                o.put(FlowConstant.USER_TASK_NOTIFY_TYPES_KEY, flowTaskNotifyTypes);
                flowTaskExt.setExtraDataJson(o.toJSONString());
            } else {
                JSONObject extraDataJson = JSON.parseObject(flowTaskExt.getExtraDataJson());
                FlowUserTaskExtData taskExtData = JSON.parseObject(flowTaskExt.getExtraDataJson(), FlowUserTaskExtData.class);
                if (CollUtil.isEmpty(taskExtData.getFlowNotifyTypeList())) {
                    //节点没有配置“通知类型”，加载全局配置“通知类型”
                    extraDataJson.put("flowNotifyTypeList", flowTaskNotifyTypes);
                } else {
                    //节点没有配置“通知类型”，加载节点配置和全局配置“通知类型”
                    Set<String> notifyTypesSet = taskExtData.getFlowNotifyTypeList()
                        .stream().filter(StrUtil::isNotBlank).collect(Collectors.toSet());
                    notifyTypesSet.addAll(flowTaskNotifyTypes);
                    extraDataJson.put("flowNotifyTypeList", new LinkedList<>(notifyTypesSet));
                }
                flowTaskExt.setExtraDataJson(JSON.toJSONString(extraDataJson));
            }
        }
    }

    private void processFlowTaskExtList(List<FlowTaskExt> flowTaskExtList, Collection<FlowElement> elementList) {
        Map<String, FlowElement> elementMap = elementList.stream().filter(UserTask.class::isInstance).collect(Collectors.toMap(FlowElement::getId, c -> c));
        for (FlowTaskExt t : flowTaskExtList) {
            UserTask userTask = (UserTask)elementMap.get(t.getTaskId());
            flowApiService.addTaskCreateListener(userTask, FlowUserTaskListener.class);
            /*//不同岗位，上一个节点审批人和下一个节点岗位包含的人一样，则自动通过节点任务，该功能在执行监听器里是用开关进行控制要不要执行的，这里只是在发布的时候给add进去
            flowApiService.addTaskCreateListener(userTask, AynamicAppointApproveUserSkipListener.class);*/
            Map<String, List<ExtensionAttribute>> attributes = userTask.getAttributes();
            if (CollUtil.isNotEmpty(attributes.get(FlowConstant.USER_TASK_AUTO_SKIP_KEY))) {
                //自动跳过监听器由原来的执行监听器改为使用任务监听器，为了修复执行监听器获取不到TaskId问题
                flowApiService.addTaskCreateListener(userTask, AutoSkipTaskListener.class);
            }
            // 如果流程图中包含部门领导审批和上级部门领导审批的选项
            if (StrUtil.equals(t.getGroupType(), FlowConstant.GROUP_TYPE_UP_DEPT_POST_LEADER)) {
                userTask.setCandidateGroups(CollUtil.newArrayList("${" + FlowConstant.GROUP_TYPE_UP_DEPT_POST_LEADER_VAR + "}"));
                flowApiService.addTaskCreateListener(userTask, UpDeptPostLeaderListener.class);
            } else if (StrUtil.equals(t.getGroupType(), FlowConstant.GROUP_TYPE_DEPT_POST_LEADER)) {
                userTask.setCandidateGroups(CollUtil.newArrayList("${" + FlowConstant.GROUP_TYPE_DEPT_POST_LEADER_VAR + "}"));
                flowApiService.addTaskCreateListener(userTask, DeptPostLeaderListener.class);
            } else if (StrUtil.equals(t.getGroupType(), FlowConstant.GROUP_TYPE_POST)) {
                Assert.notNull(t.getDeptPostListJson());
                List<FlowTaskPostCandidateGroupDTO> groupDataList = JSON.parseArray(t.getDeptPostListJson(), FlowTaskPostCandidateGroupDTO.class);
                List<String> candidateGroupList = FlowTaskPostCandidateGroupDTO.buildCandidateGroupList(groupDataList);
                userTask.setCandidateGroups(candidateGroupList);
            }
            if (t.getExtraDataJson() != null) {
                FlowUserTaskExtData userTaskExtData = JSON.parseObject(t.getExtraDataJson(), FlowUserTaskExtData.class);
                //为用户任务添加 “任务进入待办状态时的通知监听器”
                if (CollUtil.isNotEmpty(userTaskExtData.getFlowNotifyTypeList())) {
                    flowApiService.addTaskCreateListener(userTask, FlowTaskNotifyListener.class);
                }
                //为用户任务添加 “任务驳回后重新提交到该任务的通知监听器”
                if (userTaskExtData.getRejectType().equals(FlowUserTaskExtData.REJECT_TYPE_BACK_TO_SOURCE)) {
                    flowApiService.addTaskCreateListener(userTask, FlowTaskRejectBackListener.class);
                }
                //为用户任务添加 "超时设置时间到期自动触发审批操作"
                if (StrUtil.isNotBlank(userTaskExtData.getTimeoutAutoApprove()) || StrUtil.isNotBlank(userTaskExtData.getTimeoutSendMessage())) {
                    flowApiService.addTaskCreateListener(userTask, FlowTaskTimeoutListener.class);
                }
            }
            //为每个用户任务添加任务处理人校验监听器,对于未查询到人的任务节点记录异常任务数据
            flowApiService.addTaskCreateListener(userTask, FlowRuTaskAbnormalCheckListener.class);
        }
    }
}

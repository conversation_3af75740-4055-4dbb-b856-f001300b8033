package cn.com.sipsg.common.flow.core.listener;

import cn.com.sipsg.common.flow.core.constant.FlowTaskStatus;
import cn.com.sipsg.common.flow.core.entity.FlowTaskComment;
import cn.com.sipsg.common.flow.core.entity.FlowWorkOrder;
import cn.com.sipsg.common.flow.core.service.FlowTaskCommentService;
import cn.com.sipsg.common.flow.core.service.FlowWorkOrderService;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.ExecutionListener;

/**
 * 流程实例监听器，在流程实例结束的时候，需要完成一些自定义的业务行为。如： 1. 更新流程工单表的审批状态字段。 2. 业务数据同步,更新业务表中流程状态(flowStatus)和流程审批状态(latestApprovalStatus)。
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Slf4j
public class FlowFinishedListener implements ExecutionListener {

    private final transient FlowWorkOrderService flowWorkOrderService = SpringUtil.getBean(FlowWorkOrderService.class);

    private final transient FlowTaskCommentService flowTaskCommentService = SpringUtil.getBean(FlowTaskCommentService.class);


    @Override
    public void notify(DelegateExecution execution) {
        log.info("监听器：{}", cn.com.sipsg.common.flow.core.listener.FlowFinishedListener.class);
        if (!StrUtil.equals("end", execution.getEventName())) {
            return;
        }
        String processInstanceId = execution.getProcessInstanceId();
        FlowWorkOrder workOrder = flowWorkOrderService.getFlowWorkOrderByProcessInstanceId(processInstanceId);
        if (workOrder == null) {
            return;
        }
        int flowStatus = FlowTaskStatus.FINISHED;
        if (workOrder.getFlowStatus().equals(FlowTaskStatus.CANCELLED)
            || workOrder.getFlowStatus().equals(FlowTaskStatus.STOPPED)) {
            flowStatus = workOrder.getFlowStatus();
        }
        workOrder.setFlowStatus(flowStatus);
        // 更新流程工单中的j流程状态。
        //如果流程的最后一个节点是设置了超时任务自动审批的，则会无登录用户，无法通过SecurityUtils.getLoginUser()获取用户相关信息，
        // 工单的update方法无法获取udateBy会报错，此处获取最后一个审批记录的审批人作为更新人
        FlowTaskComment latestFlowTaskComment = flowTaskCommentService.getLatestFlowTaskComment(processInstanceId);
        flowWorkOrderService.updateFlowStatusByProcessInstanceId(processInstanceId, flowStatus, latestFlowTaskComment.getCreateUserId());
        if (workOrder.getOnlineTableId() != null) {
            // 处理动态表单工作流的自定义状态更新。
            //todo 此处是更新动态表单的表里状态字段
        } else {
            // 处理路由表单工作里的自定义状态更新。
            // 调用业务端接口更新流程状态(flowStatus)和流程审批状态(latestApprovalStatus)。
        }
        if (flowStatus == FlowTaskStatus.FINISHED) {
            // 动态表单不支持该功能，仅限于路由表单工作流可用。
        }
        //flowTaskTimeoutJobService.deleteByProcessInstanceId(execution.getProcessInstanceId());
    }
}

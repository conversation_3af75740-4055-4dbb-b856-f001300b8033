package cn.com.sipsg.common.flow.core.entity;

import cn.com.sipsg.common.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;

/**
 * 流程任务扩展实体对象。
 *
 * <AUTHOR>
 * @since 2025/04/14
 */

@Getter
@Setter
@FieldNameConstants
@TableName(value = "medox_flow_task_ext", autoResultMap = true)
public class FlowTaskExt extends BaseDO {

    /**
     * 流程引擎的定义Id。
     */
    @TableField(value = "process_definition_id")
    private String processDefinitionId;

    /**
     * 流程引擎任务Id。
     */
    private String taskId;

    /**
     * 操作列表JSON。
     */
    private String operationListJson;

    /**
     * 变量列表JSON。
     */
    private String variableListJson;

    /**
     * 存储多实例的assigneeList的JSON。
     */
    private String assigneeListJson;

    /**
     * 分组类型。
     */
    private String groupType;

    /**
     * 保存岗位相关的数据。
     */
    private String deptPostListJson;

    /**
     * 逗号分隔的角色Id。
     */
    private String roleIds;

    /**
     * 逗号分隔的部门Id。
     */
    private String deptIds;

    /**
     * 逗号分隔候选用户名。
     */
    private String candidateUsernames;

    /**
     * 抄送相关的数据。
     */
    private String copyListJson;

    /**
     * 用户任务的扩展属性，存储为JSON的字符串格式。
     */
    private String extraDataJson;

    /**
     * 动态指定审批人或审批部门类型的传参字段
     */
    private String dynamicAppointField;
}

package cn.com.sipsg.common.flow.core.listener;

import cn.com.sipsg.common.flow.core.constant.FlowConstant;
import cn.com.sipsg.common.flow.core.constant.FlowUserTaskExtData;
import cn.com.sipsg.common.flow.core.entity.FlowTaskExt;
import cn.com.sipsg.common.flow.core.entity.FlowTaskTimeoutJob;
import cn.com.sipsg.common.flow.core.service.FlowTaskExtService;
import cn.com.sipsg.common.flow.core.service.FlowTaskTimeoutJobService;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.delegate.TaskListener;
import org.flowable.task.service.delegate.DelegateTask;

/**
 * 流程任务超时监听器。
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Slf4j
public class FlowTaskTimeoutListener implements TaskListener {

    private final transient FlowTaskExtService flowTaskExtService = SpringUtil.getBean(FlowTaskExtService.class);

    private final transient FlowTaskTimeoutJobService flowTaskTimeoutJobService = SpringUtil.getBean(FlowTaskTimeoutJobService.class);

    @Override
    public void notify(DelegateTask delegateTask) {
        FlowTaskExt taskExt = flowTaskExtService.getByProcessDefinitionIdAndTaskId(
            delegateTask.getProcessDefinitionId(), delegateTask.getTaskDefinitionKey());
        if (StrUtil.isNotBlank(taskExt.getExtraDataJson())) {
            FlowUserTaskExtData taskExtData = JSON.parseObject(taskExt.getExtraDataJson(), FlowUserTaskExtData.class);
            // 解析超时自动审批这种类型
            if (StrUtil.isNotBlank(taskExtData.getTimeoutAutoApprove())) {
                FlowTaskTimeoutJob job = new FlowTaskTimeoutJob();
                job.setProcessDefinitionId(delegateTask.getProcessDefinitionId());
                job.setProcessInstanceId(delegateTask.getProcessInstanceId());
                job.setTaskKey(delegateTask.getTaskDefinitionKey());
                job.setTaskId(delegateTask.getId());
                job.setHandleType(FlowConstant.TIME_OUT_AUTO_APPROVE);
                //超时处理方式，发送消息：timeoutSendMessage  自动审批：timeoutAutoApprove
                job.setHandleWay(taskExtData.getTimeoutAutoApprove());
                job.setTimeoutHours(taskExtData.getTimeoutAutoApproveHours());
                job.setDefaultAssignee(taskExtData.getDefaultAssignee());
                job.setDeptOfApproveUser(taskExtData.getDeptOfApproveUser());
                job.setApproveOpinion(taskExtData.getTimeoutAutoApproveOpinion());
                flowTaskTimeoutJobService.saveNew(job);
            }
            // 解析消息通知
            if (StrUtil.isNotBlank(taskExtData.getTimeoutSendMessage())) {
                FlowTaskTimeoutJob sendMessageJob = new FlowTaskTimeoutJob();
                sendMessageJob.setProcessDefinitionId(delegateTask.getProcessDefinitionId());
                sendMessageJob.setProcessInstanceId(delegateTask.getProcessInstanceId());
                sendMessageJob.setTaskKey(delegateTask.getTaskDefinitionKey());
                sendMessageJob.setTaskId(delegateTask.getId());
                sendMessageJob.setHandleType(FlowConstant.TIME_OUT_SEND_MESSAGE);
                sendMessageJob.setHandleWay(taskExtData.getTimeoutSendMessage());
                sendMessageJob.setTimeoutHours(taskExtData.getTimeoutSendMessageHours());
                flowTaskTimeoutJobService.saveNew(sendMessageJob);
            }
        }
    }
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.sipsg.common.flow.core.mapper.FlowWorkOrderMapper">
    <resultMap id="BaseResultMap" type="cn.com.sipsg.common.flow.core.entity.FlowWorkOrder">
        <id column="work_order_id" jdbcType="VARCHAR" property="workOrderId"/>
        <result column="work_order_code" jdbcType="VARCHAR" property="workOrderCode"/>
        <result column="process_definition_key" jdbcType="VARCHAR" property="processDefinitionKey"/>
        <result column="process_definition_name" jdbcType="VARCHAR" property="processDefinitionName"/>
        <result column="process_definition_id" jdbcType="VARCHAR" property="processDefinitionId"/>
        <result column="process_instance_id" jdbcType="VARCHAR" property="processInstanceId"/>
        <result column="online_table_id" jdbcType="VARCHAR" property="onlineTableId"/>
        <result column="table_name" jdbcType="VARCHAR" property="tableName"/>
        <result column="business_key" jdbcType="VARCHAR" property="businessKey"/>
        <result column="latest_approval_status" jdbcType="INTEGER" property="latestApprovalStatus"/>
        <result column="flow_status" jdbcType="INTEGER" property="flowStatus"/>
        <result column="submit_username" jdbcType="VARCHAR" property="submitUsername"/>
        <result column="dept_id" jdbcType="VARCHAR" property="deptId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="del_flag" jdbcType="VARCHAR" property="delFlag"/>
    </resultMap>

    <!-- 如果有逻辑删除字段过滤，请写到这里 -->
    <sql id="filterRef">
        <!-- 这里必须加上全包名，否则当filterRef被其他Mapper.xml包含引用的时候，就会调用Mapper.xml中的该SQL片段 -->
        <include refid="cn.com.sipsg.common.flow.core.mapper.FlowWorkOrderMapper.inputFilterRef"/>
        AND zz_flow_work_order.del_flag = ${@com.sipsg.center.common.core.constant.GlobalDeletedFlag@NORMAL}
    </sql>

    <!-- 这里仅包含调用接口输入的主表过滤条件 -->
    <sql id="inputFilterRef">
        <if test="flowWorkOrderFilter != null">
            <if test="flowWorkOrderFilter.processInstanceId != null and flowWorkOrderFilter.processInstanceId != ''">
                AND medox_flow_work_order.process_instance_id = #{flowWorkOrderFilter.processInstanceId}
            </if>
            <if test="flowWorkOrderFilter.workOrderCode != null and flowWorkOrderFilter.workOrderCode != ''">
                AND medox_flow_work_order.work_order_code = #{flowWorkOrderFilter.workOrderCode}
            </if>
            <if test="flowWorkOrderFilter.processDefinitionKey != null and flowWorkOrderFilter.processDefinitionKey != ''">
                AND medox_flow_work_order.process_definition_key = #{flowWorkOrderFilter.processDefinitionKey}
            </if>
            <if test="flowWorkOrderFilter.latestApprovalStatus != null">
                AND medox_flow_work_order.latest_approval_status = #{flowWorkOrderFilter.latestApprovalStatus}
            </if>
            <if test="flowWorkOrderFilter.flowStatus != null">
                AND medox_flow_work_order.flow_status = #{flowWorkOrderFilter.flowStatus}
            </if>
            <if test="flowWorkOrderFilter.createTimeStartTim != null ">
                AND medox_flow_work_order.create_time &gt;= #{flowWorkOrderFilter.createTimeStartTim}
            </if>
            <if test="flowWorkOrderFilter.createTimeEndTim != null ">
                AND medox_flow_work_order.create_time &lt;= #{flowWorkOrderFilter.createTimeEndTim}
            </if>
            <if test="flowWorkOrderFilter.createUserId != null">
                AND medox_flow_work_order.create_user_id = #{flowWorkOrderFilter.createUserId}
            </if>
        </if>
    </sql>

    <select id="getFlowWorkOrderList" resultMap="BaseResultMap" parameterType="cn.com.sipsg.common.flow.core.entity.FlowWorkOrder">
        SELECT * FROM medox_flow_work_order
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>
</mapper>

package cn.com.sipsg.common.flow.core.pojo.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 工单编码规则
 * 
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "工单编码规则")
public class EncodedRuleBO {

    /**
     * 工单编码规则是否勾选，true,false
     */
    @Schema(description = "工单编码规则是否勾选，true,false")
    private Boolean calculateWhenView;

    /**
     * 前缀
     */
    @Schema(description = "前缀")
    private String prefix;

    /**
     * 规则，精确到日，精确到小时....
     */
    @Schema(description = "规则，精确到日，精确到小时....")
    private String precisionTo;

    /**
     * 后缀
     */
    @Schema(description = "后缀")
    private String middle;

    /**
     * 序号宽度
     */
    @Schema(description = "序号宽度")
    private Integer idWidth;
}

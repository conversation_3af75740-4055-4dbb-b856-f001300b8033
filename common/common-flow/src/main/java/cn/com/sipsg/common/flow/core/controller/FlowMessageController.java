package cn.com.sipsg.common.flow.core.controller;

import cn.com.sipsg.common.exception.enums.ErrorCodeEnum;
import cn.com.sipsg.common.flow.core.constant.FlowMessageType;
import cn.com.sipsg.common.flow.core.entity.FlowMessage;
import cn.com.sipsg.common.flow.core.pojo.bo.FlowMessagePageBO;
import cn.com.sipsg.common.flow.core.pojo.vo.FlowMessageVO;
import cn.com.sipsg.common.flow.core.service.FlowMessageService;
import cn.com.sipsg.common.pojo.CommonResult;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 工作流查询
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Tag(name = "工作流系统消息控制器")
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/flow/flowMessage")
public class FlowMessageController {

    private final FlowMessageService flowMessageService;

    /**
     * 获取当前用户的未读消息总数
     *
     * @return 应答结果对象，包含当前用户的未读消息总数
     */
    @Operation(summary = "获取当前用户的未读消息总数")
    @GetMapping("/getMessageCount")
    public CommonResult<JSONObject> getMessageCount() {
        JSONObject resultData = new JSONObject();
        resultData.put("remindingMessageCount", flowMessageService.countRemindingMessageListByUser());
        resultData.put("copyMessageCount", flowMessageService.countCopyMessageByUser());
        return CommonResult.data(resultData);
    }

    /**
     * 获取当前用户的催办消息列表
     *
     * @param flowMessagePageBo 催办消息BO
     * @return 催办消息列表
     */
    @Operation(summary = "获取当前用户的催办消息列表")
    @PostMapping("/listRemindingTask")
    public CommonResult<CommonPageVO<FlowMessageVO>> listRemindingTask(@Validated @RequestBody FlowMessagePageBO flowMessagePageBo) {
        if (ObjectUtil.isNull(flowMessagePageBo.getCurrent())) {
            flowMessagePageBo.setCurrent(0L);
        }
        if (ObjectUtil.isNull(flowMessagePageBo.getSize())) {
            flowMessagePageBo.setSize(10L);
        }
        return CommonResult.data(flowMessageService.pageRemindingMessageByUser(flowMessagePageBo));
    }

    /**
     * 获取当前用户的抄送消息列表。不仅仅包含，其中包括当前用户所属角色、部门和岗位的候选组抄送消息。NOTE：白名单接口
     *
     * @param flowMessagePageBO 催办消息BO
     * @return 应答结果对象，包含查询结果集
     */
    @Operation(summary = "获取当前用户的抄送消息列表")
    @PostMapping("/listCopyMessage")
    public CommonResult<CommonPageVO<FlowMessageVO>> listCopyMessage(@Validated @RequestBody FlowMessagePageBO flowMessagePageBO) {
        if (ObjectUtil.isNull(flowMessagePageBO.getCurrent())) {
            flowMessagePageBO.setCurrent(0L);
        }
        if (ObjectUtil.isNull(flowMessagePageBO.getSize())) {
            flowMessagePageBO.setSize(10L);
        }
        return CommonResult.data(flowMessageService.pageCopyMessageByUser(flowMessagePageBO));
    }

    /**
     * 将抄送消息设置为已读
     *
     * @param messageId 消息Id
     */
    @Operation(summary = "读取抄送消息，同时更新当前用户对指定抄送消息的读取状态")
    @Parameter(name = "messageId", description = "消息Id", required = true)
    @GetMapping("/readCopyTask")
    public CommonResult readCopyTask(@RequestParam String messageId) {
        String errorMessage;
        // 验证流程任务的合法性。
        FlowMessage flowMessage = flowMessageService.getById(messageId);
        if (flowMessage == null) {
            return CommonResult.fail(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        if (flowMessage.getMessageType() != FlowMessageType.COPY_TYPE) {
            errorMessage = "数据验证失败，当前消息不是抄送类型消息！";
            return CommonResult.fail(errorMessage);
        }
        if (!flowMessageService.isCandidateIdentityOnMessage(messageId)) {
            errorMessage = "数据验证失败，当前用户没有权限访问该消息！";
            return CommonResult.fail(errorMessage);
        }
        // 将当前消息更新为已读
        flowMessageService.readCopyTask(messageId);
        return CommonResult.success();
    }

}

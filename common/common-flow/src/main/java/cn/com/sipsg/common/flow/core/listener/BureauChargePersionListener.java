package cn.com.sipsg.common.flow.core.listener;

import com.alibaba.fastjson2.JSONArray;
import java.util.Arrays;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.ExecutionListener;
import org.flowable.engine.impl.persistence.entity.ExecutionEntityImpl;
import org.springframework.stereotype.Component;

/**
 * 动态指定会签人监听器，可通过apparitorHead字段将会签人用户id值传递过来
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Slf4j
@Component
public class BureauChargePersionListener implements ExecutionListener {

    @Override
    public void notify(DelegateExecution execution) {

        //流程图中会签节点无法配置会签人时，通过任务变量 apparitorHead 动态的指定到会签节点上，任务变量携带的是会签人用户id
        JSONArray assigneeList = (JSONArray)execution.getVariable("assigneeList");
        if (null == assigneeList) {
            assigneeList = new JSONArray();
        }
        String assigneeString = (String)execution.getVariable("apparitorHead");
        if (StringUtils.isNotBlank(assigneeString)) {
            List<String> assignees = Arrays.asList(assigneeString.split(","));
            assigneeList.clear();
            assigneeList.addAll(assignees);
        }
        execution.setVariable("assigneeList", assigneeList);
        log.info("BureauChargePersionListener监听器处理节点:{}，节点中文名称：{}，分配的会签人：{}", ((ExecutionEntityImpl)execution).getActivityId(), ((ExecutionEntityImpl)execution).getActivityName(), assigneeList.toString());

    }
}

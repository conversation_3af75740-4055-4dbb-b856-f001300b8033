package cn.com.sipsg.common.flow.core.service.impl;

import cn.com.sipsg.common.flow.core.entity.FlowTaskComment;
import cn.com.sipsg.common.flow.core.entity.FlowTaskExt;
import cn.com.sipsg.common.flow.core.mapper.FlowTaskCommentMapper;
import cn.com.sipsg.common.flow.core.pojo.vo.FlowTaskCommentVO;
import cn.com.sipsg.common.flow.core.service.FlowTaskCommentService;
import cn.com.sipsg.common.flow.core.service.FlowTaskExtService;
import cn.com.sipsg.common.flow.core.service.FlowWorkOrderService;
import cn.com.sipsg.common.security.core.LoginUser;
import cn.com.sipsg.common.security.core.util.SecurityUtils;
import cn.com.sipsg.common.util.BeanUtils;
import cn.com.sipsg.module.system.api.upms.SysUserApi;
import cn.com.sipsg.module.system.api.upms.dto.SysUserRespDTO;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 流程任务批注数据操作服务类
 *
 * <AUTHOR>
 * @since 2025/04/14
 **/
@Slf4j
@Service("flowTaskCommentService")
@RequiredArgsConstructor
public class FlowTaskCommentServiceImpl extends ServiceImpl<FlowTaskCommentMapper, FlowTaskComment> implements FlowTaskCommentService {

    @Autowired
    private FlowTaskCommentMapper flowTaskCommentMapper;

    private final SysUserApi sysUserApi;

    @Autowired
    private FlowWorkOrderService flowWorkOrderService;

    @Autowired
    private FlowTaskExtService flowTaskExtService;

    /**
     * 保存新增对象。
     *
     * @param flowTaskComment 新增对象。
     * @return 返回新增对象。
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public FlowTaskComment saveNew(FlowTaskComment flowTaskComment) {
        if (StrUtil.isEmpty(flowTaskComment.getCreateBy())) {
            LoginUser loginUser = SecurityUtils.getLoginUser();
            flowTaskComment.setCreateBy(loginUser.getUserId());
            flowTaskComment.setCreateUserId(loginUser.getUserId());
            flowTaskComment.setCreateUsername(loginUser.getRealName());
            flowTaskComment.setUpdateBy(loginUser.getUserId());
        }
        flowTaskComment.setCreateTime(LocalDateTime.now());
        this.save(flowTaskComment);
        return flowTaskComment;
    }

    /**
     * 查询指定流程实例Id下的所有审批任务的批注。
     *
     * @param processInstanceId 流程实例Id。
     * @return 查询结果集。
     */
    @Override
    public List<FlowTaskCommentVO> getFlowTaskCommentList(String processInstanceId) {
        List<FlowTaskComment> flowTaskComments = flowTaskCommentMapper.selectList(Wrappers.lambdaQuery(FlowTaskComment.class)
            .eq(FlowTaskComment::getProcessInstanceId, processInstanceId)
            .orderByAsc(FlowTaskComment::getCreateTime));
        return this.toVO(flowTaskComments);
    }

    private List<FlowTaskCommentVO> toVO(List<FlowTaskComment> flowTaskComments) {
        String processDefinitionId = flowWorkOrderService.getFlowWorkOrderByProcessInstanceId(flowTaskComments.get(0).getProcessInstanceId()).getProcessDefinitionId();
        List<FlowTaskExt> flowTaskExts = flowTaskExtService.getByProcessDefinitionId(processDefinitionId);
        Map<String, String> taskExtMap = flowTaskExts.stream().collect(Collectors.toMap(FlowTaskExt::getTaskId, FlowTaskExt::getOperationListJson));
        return flowTaskComments.stream().map(flowTaskComment -> {
            FlowTaskCommentVO flowTaskCommentVO = new FlowTaskCommentVO();
            BeanUtils.copyProperties(flowTaskComment, flowTaskCommentVO);
            if (StringUtils.isNotBlank(flowTaskComment.getDelegateAssignee())) {
                // delegateAssignee字段保存的是转办加签用户id
                List<String> userIds = Arrays.asList(StringUtils.split(flowTaskComment.getDelegateAssignee(), ","));
                String userNames = sysUserApi.getUserListByIds(userIds).stream().map(SysUserRespDTO::getUsername).collect(Collectors.joining(","));
                flowTaskCommentVO.setDelegateAssigneeUserName(userNames);
            }
            //封装审批类型对应的按钮名称
            if (taskExtMap.containsKey(flowTaskComment.getTaskKey())) {
                JSONArray jsonArray = JSONArray.parseArray(taskExtMap.get(flowTaskComment.getTaskKey()));
                if (!jsonArray.isEmpty()) {
                    String approvalType = flowTaskComment.getApprovalType();
                    for (int i = 0; i < jsonArray.size(); i++) {
                        JSONObject jsonObject = jsonArray.getJSONObject(i);
                        if (jsonObject.containsValue(approvalType)) {
                            flowTaskCommentVO.setButtonName(jsonObject.get("label").toString());
                            break;
                        }
                    }
                }
            }
            return flowTaskCommentVO;
        }).collect(Collectors.toList());
    }

    @Override
    public List<FlowTaskComment> getFlowTaskCommentListByTaskIds(Set<String> taskIdSet) {
        LambdaQueryWrapper<FlowTaskComment> queryWrapper =
            new LambdaQueryWrapper<FlowTaskComment>().in(FlowTaskComment::getTaskId, taskIdSet);
        queryWrapper.orderByDesc(FlowTaskComment::getId);
        return flowTaskCommentMapper.selectList(queryWrapper);
    }

    @Override
    public FlowTaskComment getLatestFlowTaskComment(String processInstanceId) {
        LambdaQueryWrapper<FlowTaskComment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FlowTaskComment::getProcessInstanceId, processInstanceId);
        queryWrapper.orderByDesc(FlowTaskComment::getCreateTime);
        IPage<FlowTaskComment> pageData = flowTaskCommentMapper.selectPage(new Page<>(1, 1), queryWrapper);
        return CollUtil.isEmpty(pageData.getRecords()) ? null : pageData.getRecords().get(0);
    }

    @Override
    public FlowTaskComment getLatestFlowTaskComment(String processInstanceId, String taskDefinitionKey) {
        LambdaQueryWrapper<FlowTaskComment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FlowTaskComment::getProcessInstanceId, processInstanceId);
        queryWrapper.eq(FlowTaskComment::getTaskKey, taskDefinitionKey);
        queryWrapper.orderByDesc(FlowTaskComment::getCreateTime);
        IPage<FlowTaskComment> pageData = flowTaskCommentMapper.selectPage(new Page<>(1, 1), queryWrapper);
        return CollUtil.isEmpty(pageData.getRecords()) ? null : pageData.getRecords().get(0);
    }

    @Override
    public FlowTaskComment getFirstFlowTaskComment(String processInstanceId) {
        LambdaQueryWrapper<FlowTaskComment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FlowTaskComment::getProcessInstanceId, processInstanceId);
        queryWrapper.orderByAsc(FlowTaskComment::getCreateTime);
        IPage<FlowTaskComment> pageData = flowTaskCommentMapper.selectPage(new Page<>(1, 1), queryWrapper);
        return CollUtil.isEmpty(pageData.getRecords()) ? null : pageData.getRecords().get(0);
    }

    @Override
    public List<FlowTaskComment> getFlowTaskCommentListByExecutionId(
        String processInstanceId, String taskId, String executionId) {
        LambdaQueryWrapper<FlowTaskComment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FlowTaskComment::getProcessInstanceId, processInstanceId);
        queryWrapper.eq(FlowTaskComment::getTaskId, taskId);
        queryWrapper.eq(FlowTaskComment::getExecutionId, executionId);
        queryWrapper.orderByAsc(FlowTaskComment::getCreateTime);
        return flowTaskCommentMapper.selectList(queryWrapper);
    }

    @Override
    public List<FlowTaskComment> getFlowTaskCommentListByMultiInstanceExecId(String multiInstanceExecId) {
        LambdaQueryWrapper<FlowTaskComment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FlowTaskComment::getMultiInstanceExecId, multiInstanceExecId);
        return flowTaskCommentMapper.selectList(queryWrapper);
    }

    @Override
    public List<FlowTaskComment> getFlowTaskCommentListByProcessInstanceIdAndTaskKey(String processInstanceId, String taskKey) {
        LambdaQueryWrapper<FlowTaskComment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FlowTaskComment::getProcessInstanceId, processInstanceId);
        queryWrapper.eq(FlowTaskComment::getTaskKey, taskKey);
        queryWrapper.orderByDesc(FlowTaskComment::getCreateTime);
        return this.list(queryWrapper);
    }

    @Override
    public List<FlowTaskComment> getFlowTaskCommentOrderByCreatTime(String processInstanceId) {
        LambdaQueryWrapper<FlowTaskComment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FlowTaskComment::getProcessInstanceId, processInstanceId);
        queryWrapper.orderByDesc(FlowTaskComment::getCreateTime);
        return flowTaskCommentMapper.selectList(queryWrapper);
    }

    @Override
    public FlowTaskComment getLatestFlowTaskCommentListByProcessInstanceIdAndCreateLoginName(String processInstanceId, String createLoginName) {
        LambdaQueryWrapper<FlowTaskComment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FlowTaskComment::getProcessInstanceId, processInstanceId);
        queryWrapper.eq(FlowTaskComment::getCreateUserId, createLoginName);
        queryWrapper.orderByDesc(FlowTaskComment::getId);
        IPage<FlowTaskComment> pageData = flowTaskCommentMapper.selectPage(new Page<>(1, 1), queryWrapper);
        return CollUtil.isEmpty(pageData.getRecords()) ? null : pageData.getRecords().get(0);
    }
}

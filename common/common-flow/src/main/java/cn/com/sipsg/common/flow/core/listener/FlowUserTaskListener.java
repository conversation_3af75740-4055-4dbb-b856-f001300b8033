package cn.com.sipsg.common.flow.core.listener;


import cn.com.sipsg.common.flow.core.constant.FlowConstant;
import cn.hutool.extra.spring.SpringUtil;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.delegate.TaskListener;
import org.flowable.task.service.delegate.DelegateTask;

/**
 * 流程任务通用监听器。
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Slf4j
public class FlowUserTaskListener implements TaskListener {

    private final transient RuntimeService runtimeService = SpringUtil.getBean(RuntimeService.class);

    private final transient TaskService taskService = SpringUtil.getBean(TaskService.class);

    @Override
    public void notify(DelegateTask delegateTask) {
        Map<String, Object> variables = delegateTask.getVariables();
        if (variables.get(FlowConstant.DELEGATE_ASSIGNEE_VAR) != null) {
            //通过taskService.setAssignee()可解决干预到动态指定审批人节点后，任务人手动审批后无已办记录问题
            taskService.setAssignee(delegateTask.getId(), variables.get(FlowConstant.DELEGATE_ASSIGNEE_VAR).toString());
            runtimeService.removeVariableLocal(delegateTask.getExecutionId(), FlowConstant.DELEGATE_ASSIGNEE_VAR);
        }
    }
}

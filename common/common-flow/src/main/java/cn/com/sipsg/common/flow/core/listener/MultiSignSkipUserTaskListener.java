package cn.com.sipsg.common.flow.core.listener;

import cn.com.sipsg.common.flow.core.constant.FlowTaskStatus;
import cn.com.sipsg.common.flow.core.entity.FlowTaskComment;
import cn.com.sipsg.common.flow.core.service.FlowApiService;
import cn.com.sipsg.common.flow.core.service.FlowTaskCommentService;
import cn.com.sipsg.module.system.api.upms.SysUserApi;
import cn.com.sipsg.module.system.api.upms.dto.SysUserRespDTO;
import cn.hutool.core.text.StrFormatter;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson2.JSONObject;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.flowable.bpmn.model.FlowElement;
import org.flowable.engine.TaskService;
import org.flowable.engine.delegate.TaskListener;
import org.flowable.task.api.Task;
import org.flowable.task.service.delegate.DelegateTask;

/**
 * 对于会签任务，会签人如果有和上一个节点审批人一样，则自动跳过该用户的会签任务， 需要在扩展属性里配置该任务监听器，发布后设置为主版本
 */
@Slf4j
public class MultiSignSkipUserTaskListener implements TaskListener {

    private final transient FlowApiService flowApiService = SpringUtil.getBean(FlowApiService.class);

    private final transient TaskService taskService = SpringUtil.getBean(TaskService.class);

    private final transient FlowTaskCommentService flowTaskCommentService = SpringUtil.getBean(FlowTaskCommentService.class);

    private final transient SysUserApi sysUserApi = SpringUtil.getBean(SysUserApi.class);

    @Override
    public void notify(DelegateTask delegateTask) {
        String taskId = delegateTask.getId();
        String processInstanceId = delegateTask.getProcessInstanceId();
        log.info("开始执行MultiSignSkipUserTaskListener任务监听器，当前用户任务是：{},节点中文名称：{},processInstanceId为：{},", delegateTask.getTaskDefinitionKey(), delegateTask.getName(), processInstanceId);
        //查询与当前任务连线的上一个任务节点
        FlowElement flowElement = flowApiService.getPreviousFlowElementByProcessDefinitionKeyAndTaskKey(delegateTask.getProcessDefinitionId(), processInstanceId, delegateTask.getTaskDefinitionKey());
        if (null == flowElement) {
            log.info("未查询到上一个节点信息，无法获取上一个节点的审批用户，当前用户任务是：{},processInstanceId为：{},节点中文名称：{}", delegateTask.getTaskDefinitionKey(), processInstanceId, delegateTask.getName());
            return;
        }
        //获取上一个任务节点的审批用户getLatestFlowTaskComment
        List<FlowTaskComment> flowTaskCommentList = flowTaskCommentService.getFlowTaskCommentListByProcessInstanceIdAndTaskKey(processInstanceId, flowElement.getId());
        List<String> approveUsers = flowTaskCommentList.stream().map(FlowTaskComment::getCreateUserId).collect(Collectors.toList());
        Map<String, List<FlowTaskComment>> flowTaskCommentMap = flowTaskCommentList.stream().collect(Collectors.groupingBy(FlowTaskComment::getCreateUserId));
        //如果上一个节点的审批用户集合包含当前任务的任务处理人，则自动审批通过，调用会签任务审批同意方法
        if (approveUsers.contains(delegateTask.getAssignee())) {
            log.info("上一个节点：{}，中文名：{}，审批用户集合为：{},包含当前待办任务任务处理人：{}，节点该用户任务自动审批通过", flowElement.getId(), flowElement.getName(), approveUsers.toString(), delegateTask.getAssignee());
            JSONObject taskVariableData = new JSONObject();
            taskVariableData.put("latestApprovalStatus", FlowTaskStatus.STOPPED);
            //获取当前多实例中其中一个任务
            Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
            SysUserRespDTO userById = sysUserApi.getUserById(delegateTask.getAssignee());
            //封装审批记录类
            FlowTaskComment flowTaskComment = new FlowTaskComment();
            flowTaskComment.setApprovalType("multi_agree");
            flowTaskComment.setCreateUserId(userById.getUserId());
            flowTaskComment.setCreateBy(userById.getUserId());
            flowTaskComment.setUpdateBy(userById.getUserId());
            flowTaskComment.setCreateUsername(userById.getRealName());
            //获取当前任务人在上一个节点的审批记录，获取在上一个节点审批时选择的所属部门字段值
            flowTaskComment.setDeptOfApproveUser(flowTaskCommentMap.get(delegateTask.getAssignee()).get(0).getDeptOfApproveUser());
            flowTaskComment.setTaskComment(StrFormatter.format("自动跳过审批,审批人 [{}], 跳过原因 [{}]。",
                userById.getUsername(), "会签用户里包含上一个节点审批人，默认自动审批通过"));
            //调用审批方法，具体执行审批动作，维护业务记录
            flowApiService.completeTask(task, flowTaskComment, taskVariableData, null);
        }
    }
}

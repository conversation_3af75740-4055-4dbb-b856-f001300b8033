package cn.com.sipsg.common.flow.core.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

/**
 * 流程任务的批注
 *
 * <AUTHOR>
 * @since 2025/04/14
 */

@Getter
@Setter
@Schema(description = "流程任务的批注")
public class FlowTaskCommentDTO {

    /**
     * 流程任务触发按钮类型，内置值可参考FlowTaskButton。
     */
    @Schema(description = "流程任务触发按钮类型")
    @NotNull(message = "数据验证失败，任务的审批类型不能为空！")
    private String approvalType;

    /**
     * 流程任务的批注内容。
     */
    @Schema(description = "流程任务的批注内容,即审批意见")
    //@NotBlank(message = "数据验证失败，任务审批内容不能为空！")
    private String taskComment;

    /**
     * 委托指定人，比如加签、转办等。
     */
    @Schema(description = "委托指定人，比如加签、转办等")
    private String delegateAssignee;

    /**
     * 文件原始名称
     */
    @Schema(description = "文件原始名称")
    private String fileOriginalName;

    /**
     * 文件上传的groupCode
     */
    @Schema(description = "文件上传的groupCode")
    private String groupCode;

    /**
     * 发起人指定所属部门id
     */
    @Schema(description = "发起人指定所属部门id")
    @NotBlank(message = "发起人指定的所属部门id不能为空")
    private String deptOfSubmitUser;

    /**
     * 审批人审批时选的所属部门
     */
    @Schema(description = "审批人审批时选的所属部门")
    private String deptOfApproveUser;

}

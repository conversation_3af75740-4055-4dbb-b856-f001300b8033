package cn.com.sipsg.common.flow.core.listener;

import cn.com.sipsg.common.flow.core.constant.FlowApprovalType;
import cn.com.sipsg.common.flow.core.constant.FlowConstant;
import cn.com.sipsg.common.flow.core.entity.FlowTaskComment;
import cn.com.sipsg.common.flow.core.entity.FlowTaskExt;
import cn.com.sipsg.common.flow.core.pojo.vo.FlowTaskCommentVO;
import cn.com.sipsg.common.flow.core.service.FlowApiService;
import cn.com.sipsg.common.flow.core.service.FlowTaskCommentService;
import cn.com.sipsg.common.flow.core.service.FlowTaskExtService;
import cn.com.sipsg.common.security.core.LoginUser;
import cn.com.sipsg.common.security.core.util.SecurityUtils;
import cn.com.sipsg.common.util.BeanUtils;
import cn.com.sipsg.module.system.api.upms.SysDeptApi;
import cn.com.sipsg.module.system.api.upms.SysUserApi;
import cn.com.sipsg.module.system.api.upms.dto.SysDeptRespDTO;
import cn.com.sipsg.module.system.api.upms.dto.SysUserRespDTO;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.StrFormatter;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import java.util.Collections;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.flowable.bpmn.model.ExtensionAttribute;
import org.flowable.bpmn.model.UserTask;
import org.flowable.engine.delegate.TaskListener;
import org.flowable.task.api.Task;
import org.flowable.task.service.delegate.DelegateTask;

/**
 * 流程任务自动审批跳过的监听器。
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Slf4j
public class AutoSkipTaskListener implements TaskListener {

    private final transient FlowTaskCommentService flowTaskCommentService = SpringUtil.getBean(FlowTaskCommentService.class);

    private final transient FlowApiService flowApiService = SpringUtil.getBean(FlowApiService.class);

    private final transient FlowTaskExtService flowTaskExtService = SpringUtil.getBean(FlowTaskExtService.class);

    private final transient SysUserApi sysUserApi = SpringUtil.getBean(SysUserApi.class);

    private final transient SysDeptApi sysDeptApi = SpringUtil.getBean(SysDeptApi.class);

    /**
     * 流程的发起者等于当前任务的Assignee。
     */
    private static final String EQ_START_USER = "0";

    /**
     * 上一步的提交者等于当前任务的Assignee。
     */
    private static final String EQ_PREV_SUBMIT_USER = "1";

    /**
     * 当前任务的Assignee之前提交过审核。
     */
    private static final String EQ_HISTORIC_SUBMIT_USER = "2";

    @Override
    public void notify(DelegateTask t) {
        UserTask userTask = flowApiService.getUserTask(t.getProcessDefinitionId(), t.getTaskDefinitionKey());
        List<ExtensionAttribute> attributes = userTask.getAttributes().get(FlowConstant.USER_TASK_AUTO_SKIP_KEY);
        Set<String> skipTypes = new HashSet<>(StrUtil.split(attributes.get(0).getValue(), ","));
        String assignedUser = this.getAssignedUser(userTask, t.getProcessDefinitionId(), t.getExecutionId());
        if (StrUtil.isBlank(assignedUser)) {
            return;
        }
        for (String skipType : skipTypes) {
            if (this.verifyAndHandle(userTask, t, skipType, assignedUser)) {
                return;
            }
        }
    }

    private boolean verifyAndHandle(UserTask userTask, DelegateTask task, String skipType, String assignedUser) {
        FlowTaskComment comment = null;
        switch (skipType) {
            case EQ_START_USER:
                Object v = task.getVariable(FlowConstant.PROC_INSTANCE_START_USER_NAME_VAR);
                if (ObjectUtil.equal(v, assignedUser)) {
                    comment = flowTaskCommentService.getFirstFlowTaskComment(task.getProcessInstanceId());
                    ////flowApiService.completeTask()方法里会调用flowTaskCommentService.saveNew()方法，需要先把主键置空，不然会报错
                    comment.setId(null);
                }
                break;
            case EQ_PREV_SUBMIT_USER:
                Object v2 = task.getVariable(FlowConstant.SUBMIT_USER_VAR);
                //上一步审批人审批时所属的部门
                Object deptOfApproveUser = task.getVariable(FlowConstant.DEPT_OF_APPROVE_USER);
                if (ObjectUtil.equal(v2, assignedUser)) {
                    LoginUser loginUser = SecurityUtils.getLoginUser();
                    comment = new FlowTaskComment();
                    if (null == loginUser) {
                        // loginUser为null则为上一个节点可能是超时任务自动流转的，没有登陆人信息,此时查询的是上一个节点哪个用户审批的
                        FlowTaskComment latestFlowTaskComment = flowTaskCommentService.getLatestFlowTaskComment(task.getProcessInstanceId());
                        comment.setCreateBy(latestFlowTaskComment.getCreateBy());
                        comment.setCreateUserId(latestFlowTaskComment.getCreateUserId());
                        comment.setCreateUsername(latestFlowTaskComment.getCreateUsername());
                        comment.setUpdateBy(latestFlowTaskComment.getCreateBy());
                        //封装审批人是以哪一个部门身份审批的，因为是自动跳过的，所以取上一次审批记录的
                        comment.setDeptOfApproveUser(latestFlowTaskComment.getDeptOfApproveUser());
                    } else {
                        comment.setCreateBy(loginUser.getUserId());
                        comment.setCreateUserId(loginUser.getUserId());
                        comment.setCreateUsername(loginUser.getRealName());
                        comment.setUpdateBy(loginUser.getUserId());
                        comment.setDeptOfApproveUser(deptOfApproveUser.toString());
                    }
                }
                break;
            case EQ_HISTORIC_SUBMIT_USER:
                List<FlowTaskCommentVO> comments = flowTaskCommentService.getFlowTaskCommentList(task.getProcessInstanceId());
                List<FlowTaskComment> resultComments = new LinkedList<>();
                for (FlowTaskCommentVO c : comments) {
                    if (StrUtil.equals(c.getCreateUserId(), assignedUser)) {
                        resultComments.add(BeanUtils.copyProperties(c, FlowTaskComment.class));
                    }
                }
                if (CollUtil.isNotEmpty(resultComments)) {
                    comment = resultComments.get(0);
                    //flowApiService.completeTask()方法里会调用flowTaskCommentService.saveNew()方法，需要先把主键置空，不然会报错
                    comment.setId(null);
                }
                break;
            default:
                break;
        }

        //UserTask userTask = (UserTask) execution.getCurrentFlowElement();
        FlowTaskExt flowTaskExt = flowTaskExtService.getByProcessDefinitionIdAndTaskId(task.getProcessDefinitionId(), userTask.getId());
        JSONObject extraDataJson = JSONUtil.parseObj(flowTaskExt.getExtraDataJson());
        Boolean autoSkipFirstParallelingTask = extraDataJson.getBool("AutoSkipFirstParallelingTask");
        Task t = flowApiService.getTaskById(task.getId());
        if (comment != null) {
            comment.fillWith(t);
            comment.setApprovalType(FlowApprovalType.AGREE);
            this.recordAutoSkip(userTask, comment, skipType, t);
            return true;
        } else if (ObjectUtil.isNotNull(autoSkipFirstParallelingTask) && autoSkipFirstParallelingTask) {
            //获取审批人信息
            String assignedUserPart = this.getAssignedUser(userTask, task.getProcessDefinitionId(), task.getExecutionId());
            comment = new FlowTaskComment();
            comment.fillWith(t);
            comment.setApprovalType(FlowApprovalType.AGREE);
            SysUserRespDTO sysUserRespDTO = sysUserApi.getUserById(assignedUserPart);
            if (ObjectUtil.isNotNull(sysUserRespDTO)) {
                comment.setCreateBy(sysUserRespDTO.getUserId());
                comment.setCreateUsername(sysUserRespDTO.getRealName());
            }
            comment.setCreateUserId(assignedUserPart);
            comment.setUpdateBy(assignedUserPart);
            comment.setDeptOfApproveUser(sysUserRespDTO.getDeptList().get(0) == null ? null : sysUserRespDTO.getDeptList().get(0).getDeptId());
            this.recordAutoSkip(userTask, comment, skipType, t);
            return true;
        }
        return false;
    }

    /**
     * @param userTask 用户任务对象
     * @param comment  提价记录对象
     * @param skipType 跳过类型
     * @param task     具体待办任务
     */
    private void recordAutoSkip(UserTask userTask, FlowTaskComment comment, String skipType, Task task) {
        String assigneeUserId = userTask.getAssignee();
        if (assigneeUserId.startsWith("${") && assigneeUserId.endsWith("}")) {
            String variableName = assigneeUserId.substring(2, assigneeUserId.length() - 1);
            assigneeUserId = flowApiService.getExecutionVariableStringWithSafe(task.getExecutionId(), variableName);
        }
        SysUserRespDTO userById = sysUserApi.getUserById(assigneeUserId);
        comment.setTaskComment(StrFormatter.format("自动跳过审批。审批人 [{}], 跳过原因 [{}]。", userById.getUsername(), this.getMessageBySkipType(skipType)));
        this.encapDeptOfApproveUser(comment, assigneeUserId);
        flowApiService.completeTask(task, comment, null, null);
    }

    private void encapDeptOfApproveUser(FlowTaskComment comment, String assigneeUserId) {
        if (StrUtil.isBlank(comment.getDeptOfApproveUser())) {
            List<SysDeptRespDTO> deptListByUserId = sysDeptApi.getDeptListByUserId(assigneeUserId);
            comment.setDeptOfApproveUser(deptListByUserId.get(0).getDeptId());
        }
    }

    private String getAssignedUser(UserTask userTask, String processDefinitionId, String executionId) {
        String assignedUser = userTask.getAssignee();
        if (StrUtil.isNotBlank(assignedUser)) {
            //任务处理人候选类型： 处理用户(流程发起人/指定审批人)
            if (assignedUser.startsWith("${") && assignedUser.endsWith("}")) {
                String variableName = assignedUser.substring(2, assignedUser.length() - 1);
                assignedUser = flowApiService.getExecutionVariableStringWithSafe(executionId, variableName);
            }
        } else {
            FlowTaskExt flowTaskExt = flowTaskExtService.getByProcessDefinitionIdAndTaskId(processDefinitionId, userTask.getId());
            List<String> candidateUsernames;
            if (StrUtil.isBlank(flowTaskExt.getCandidateUsernames())) {
                candidateUsernames = Collections.emptyList();
            } else if (!StrUtil.equals(flowTaskExt.getCandidateUsernames(), "${" + FlowConstant.TASK_APPOINTED_ASSIGNEE_VAR + "}")) {
                //任务处理人候选类型：候选用户组
                candidateUsernames = StrUtil.split(flowTaskExt.getCandidateUsernames(), ",");
            } else {
                String value = flowApiService.getExecutionVariableStringWithSafe(executionId, FlowConstant.TASK_APPOINTED_ASSIGNEE_VAR);
                candidateUsernames = value == null ? null : StrUtil.split(value, ",");
            }
            if (candidateUsernames != null && candidateUsernames.size() == 1) {
                assignedUser = candidateUsernames.get(0);
            }
        }
        return assignedUser;
    }

    private String getMessageBySkipType(String skipType) {
        switch (skipType) {
            case EQ_PREV_SUBMIT_USER:
                return "审批人与上一审批节点处理人相同";
            case EQ_START_USER:
                return "审批人为发起人";
            case EQ_HISTORIC_SUBMIT_USER:
                return "审批人审批过";
            default:
                return "";
        }
    }
}

package cn.com.sipsg.common.flow.core.pojo.vo;

import cn.hutool.core.date.DatePattern;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDate;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 岗位申请DTO对象
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "岗位申请DTO对象")
public class PostAdjustmentDTO {

    /**
     * 主键。
     */
    @Schema(description = "主键")
    private String id;

    /**
     * 申请人。
     */
    @Schema(description = "申请人")
    private String applyUser;

    /**
     * 入职日期。
     */
    @Schema(description = "入职日期")
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDate entryDate;

    /**
     * 原部门。
     */
    @Schema(description = "原部门")
    private String originalDept;

    /**
     * 目标部门。
     */
    @Schema(description = "目标部门")
    private String targetDept;

    /**
     * 原岗位。
     */
    @Schema(description = "原岗位")
    private String originalPost;

    /**
     * 目标岗位。
     */
    @Schema(description = "目标岗位")
    private String targetPost;

    /**
     * 原岗位职级。
     */
    @Schema(description = "原岗位职级")
    private String originalClass;

    /**
     * 目标岗位职级。
     */
    @Schema(description = "目标岗位职级")
    private String targetClass;

    /**
     * 生效日期。
     */
    @Schema(description = "生效日期")
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDate effectiveDate;

    /**
     * 工作交接人。
     */
    @Schema(description = "工作交接人")
    private String workHandoverPerson;

    /**
     * 动态指定部门id。
     */
    @Schema(description = "动态指定部门id")
    private String dynamicApproverDept;

    /**
     * 抄送对象。
     */
    @Schema(description = "抄送对象")
    private String copyUser;

    /**
     * apply_user / original_dept LIKE搜索字符串。
     */
    @Schema(description = "搜索关键字")
    private String searchString;

    /**
     * 条件路径转换条件参数。
     */
    @Schema(description = "条件路径转换条件参数")
    private String processDecision;

    /**
     * 指定处理人字段
     */
    @Schema(description = "指定处理人字段")
    private String appointApprovalUser;

}

package cn.com.sipsg.common.flow.core.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * 任务节点用户信息响应DTO对象
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "任务节点用户信息响应DTO对象")
public class FlowUserInfoRespDTO {

    /**
     * 用户Id。
     */
    @Schema(description = "用户Id")
    private String userId;

    /**
     * 登录用户名。
     */
    @Schema(description = "登录用户名")
    private String username;

    /**
     * 用户部门Id。
     */
    @Schema(description = "用户部门Id")
    private String deptId;

    /**
     * 用户显示名称。
     */
    @Schema(description = "用户显示名称")
    private String realName;

    /**
     * 用户头像的Url。
     */
    @Schema(description = "用户头像的Url")
    private String avatar;

    /**
     * 用户状态(0: 正常 1: 锁定)。
     */
    @Schema(description = "用户状态(0: 正常 1: 锁定)")
    private Integer status;

    /**
     * 最后审批时间。
     */
    @Schema(description = "最后审批时间")
    private LocalDateTime lastApprovalTime;

    /**
     * 用户邮箱。
     */
    @Schema(description = "用户邮箱")
    private String email;

    /**
     * 用户手机。
     */
    @Schema(description = "用户手机")
    private String mobile;


}

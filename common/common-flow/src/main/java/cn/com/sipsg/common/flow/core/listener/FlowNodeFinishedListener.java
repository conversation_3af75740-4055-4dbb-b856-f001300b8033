package cn.com.sipsg.common.flow.core.listener;

import cn.com.sipsg.common.flow.core.constant.FlowConstant;
import cn.com.sipsg.common.flow.core.entity.FlowWorkOrder;
import cn.com.sipsg.common.flow.core.service.FlowWorkOrderService;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.flowable.common.engine.api.delegate.Expression;
import org.flowable.engine.delegate.TaskListener;
import org.flowable.task.service.delegate.DelegateTask;

/**
 * 节点执行完后触发的调用业务接口监听器，业务按照自身需要，配置该监听器，配置即可触发
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Slf4j
public class FlowNodeFinishedListener implements TaskListener {

    private final transient FlowWorkOrderService flowWorkOrderService = SpringUtil.getBean(FlowWorkOrderService.class);

    //nodeFinishedCallbackUrl是用来获取节点上配置的业务回调接口地址的对象，该参数名是固定的
    private Expression nodeFinishedCallbackUrl;

    @Override
    public void notify(DelegateTask delegateTask) {
        log.info("任务节点结束监听器：{}", FlowNodeFinishedListener.class);
        Object businessCallbackUrl = nodeFinishedCallbackUrl.getValue(delegateTask);
        if (null == businessCallbackUrl) {
            log.info("流程processInstanceId值为：{}，任务节点名称：{} 完成，未配置回调业务的接口，节点完成后回调动作不执行", delegateTask.getProcessInstanceId(), delegateTask.getName());
            return;
        }
        Object token = delegateTask.getVariable(FlowConstant.AUTHORIZATION_HEADER);
        String processInstanceId = delegateTask.getProcessInstanceId();
        FlowWorkOrder workOrder = flowWorkOrderService.getFlowWorkOrderByProcessInstanceId(processInstanceId);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("processInstanceId", processInstanceId);
        jsonObject.put("businessKey", workOrder.getBusinessKey());
        jsonObject.put("processDefinitionKey", workOrder.getProcessDefinitionKey());
        jsonObject.put("processDefinitionId", workOrder.getProcessDefinitionId());
        jsonObject.put("approvalType", delegateTask.getVariable("operationType").toString());
        log.info("流程processDefinitionKey为：{},processInstanceId值为：{}的任务节点名称：{}，节点执行完成，发起回调，回调请求参数为：{}，请求Token为：{}，请求回调接口路径为：{}",
            workOrder.getProcessDefinitionKey(), processInstanceId, delegateTask.getName(),
            com.alibaba.fastjson2.JSONObject.toJSONString(jsonObject), token.toString(), businessCallbackUrl.toString());
        String body = HttpRequest.post(businessCallbackUrl.toString())
            .header(FlowConstant.AUTHORIZATION_HEADER, token.toString())
            .body(jsonObject.toJSONString())
            .execute().body();
        log.info("流程processDefinitionKey为：{},processInstanceId值为：{}，的流程执行结束，发起回调，回调结果为：{}", workOrder.getProcessDefinitionKey(), processInstanceId, body);
    }
}

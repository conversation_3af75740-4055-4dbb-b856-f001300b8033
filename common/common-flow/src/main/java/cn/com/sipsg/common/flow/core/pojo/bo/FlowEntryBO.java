package cn.com.sipsg.common.flow.core.pojo.bo;

import cn.com.sipsg.common.validation.group.UpdateGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

/**
 * 流程定义实体类BO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "流程定义实体类BO")
public class FlowEntryBO {

    /**
     * 主键Id。
     */
    @Schema(description = "主键Id")
    @NotNull(message = "数据验证失败，主键不能为空！", groups = {UpdateGroup.class})
    private String entryId;

    /**
     * 流程名称。
     */
    @Schema(description = "流程名称")
    @NotBlank(message = "数据验证失败，流程名称不能为空！")
    private String processDefinitionName;

    /**
     * 流程标识Key。
     */
    @Schema(description = "流程标识Key")
    @NotBlank(message = "数据验证失败，流程标识Key不能为空！")
    private String processDefinitionKey;

    /**
     * 流程分类。
     */
    @Schema(description = "流程分类")
    @NotNull(message = "数据验证失败，流程分类不能为空！")
    private String categoryId;

    /**
     * 流程状态。
     */
    @Schema(description = "流程状态")
    private Integer status;

    /**
     * 流程定义的xml。
     */
    @Schema(description = "流程定义的xml")
    private String bpmnXml;

    /**
     * 流程图类型。0: 普通流程图，1: 钉钉风格的流程图。
     */
    @Schema(description = "流程图类型。0: 普通流程图，1: 钉钉风格的流程图。")
    private Integer diagramType;

    /**
     * 绑定表单类型。
     */
    @Schema(description = "绑定表单类型, 0 动态表单，1 路由表单", example = "0,1")
    @NotNull(message = "数据验证失败，工作流绑定表单类型不能为空！")
    private Integer bindFormType;

    /**
     * 动态表单的页面Id。
     */
    @Schema(description = "动态表单的页面Id")
    private String pageId;

    /**
     * 动态表单的缺省路由名称。
     */
    @Schema(description = "动态表单的缺省路由名称,路由表单的话需要传值")
    private String defaultRouterName;

    /**
     * 动态表单Id。
     */
    @Schema(description = "动态表单Id")
    private String defaultFormId;

    /**
     * 工单表编码字段的编码规则，如果为空则不计算工单编码。
     */
    @Schema(description = "工单表编码字段的编码规则")
    private EncodedRuleBO encodedRule;

    /**
     * 流程的自定义扩展数据(JSON格式)。
     */
    @Schema(description = "流程的自定义扩展数据")
    private String extensionData;

    /**
     * 流程发布版本
     */
    @Schema(description = "流程发布版本")
    private Integer publishVersion;


    /**
     * 新的工作流发布主版本对象的主键Id
     */
    @Schema(description = "新的工作流发布主版本对象的主键Id")
    private String newEntryPublishId;
}

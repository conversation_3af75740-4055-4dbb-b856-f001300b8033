package cn.com.sipsg.common.flow.core.pojo.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 流程分类BO对象
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "流程分类BO")
public class FlowCategoryBO {

    /**
     * 显示名称。
     */
    @Schema(description = "显示名称", example = "分类名称")
    private String name;

    /**
     * 分类编码。
     */
    @Schema(description = "分类编码", example = "categoryCode")
    private String code;
}

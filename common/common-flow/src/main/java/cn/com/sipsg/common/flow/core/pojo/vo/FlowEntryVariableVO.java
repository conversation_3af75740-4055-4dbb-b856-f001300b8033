package cn.com.sipsg.common.flow.core.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * 流程变量VO对象
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "流程变量VO对象")
public class FlowEntryVariableVO {

    /**
     * 主键Id。
     */
    @Schema(description = "主键Id")
    private String variableId;

    /**
     * 流程Id。
     */
    @Schema(description = "流程Id")
    private String entryId;

    /**
     * 变量名。
     */
    @Schema(description = "变量名")
    private String variableName;

    /**
     * 显示名。
     */
    @Schema(description = "显示名")
    private String showName;

    /**
     * 变量类型。
     */
    @Schema(description = "变量类型")
    private Integer variableType;

    /**
     * 绑定数据源Id。
     */
    @Schema(description = "绑定数据源Id")
    private String bindDatasourceId;

    /**
     * 绑定数据源关联Id。
     */
    @Schema(description = "绑定数据源关联Id")
    private String bindRelationId;

    /**
     * 绑定字段Id。
     */
    @Schema(description = "绑定字段Id")
    private String bindColumnId;

    /**
     * 是否内置。
     */
    @Schema(description = "是否内置")
    private Boolean builtIn;

    /**
     * 创建时间。
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;
}

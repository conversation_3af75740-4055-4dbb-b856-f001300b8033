package cn.com.sipsg.common.flow.core.pojo.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDate;
import lombok.Getter;
import lombok.Setter;

/**
 * 岗位调整VO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "岗位调整VO")
public class PostAdjustmentVO {

    /**
     * 主键。
     */
    @Schema(description = "主键")
    @TableId(value = "id")
    private String id;

    /**
     * 申请人。
     */
    @Schema(description = "申请人")
    private String applyUser;

    /**
     * 入职日期。
     */
    @Schema(description = "入职日期")
    private LocalDate entryDate;

    /**
     * 原部门。
     */
    @Schema(description = "原部门")
    private String originalDept;

    /**
     * 目标部门。
     */
    @Schema(description = "目标部门")
    private String targetDept;

    /**
     * 原岗位。
     */
    @Schema(description = "原岗位")
    private String originalPost;

    /**
     * 目标岗位。
     */
    @Schema(description = "目标岗位")
    private String targetPost;

    /**
     * 原岗位职级。
     */
    @Schema(description = "原岗位职级")
    private String originalClass;

    /**
     * 目标岗位职级。
     */
    @Schema(description = "目标岗位职级")
    private String targetClass;

    /**
     * 生效日期。
     */
    @Schema(description = "生效日期")
    private LocalDate effectiveDate;

    /**
     * 工作交接人。
     */
    @Schema(description = "工作交接人")
    private String workHandoverPerson;

    /**
     * 流程状态。
     */
    @Schema(description = "流程状态")
    private Integer flowStatus;

    /**
     * 流程审批状态。
     */
    @Schema(description = "流程审批状态")
    private Integer latestApprovalStatus;

    /**
     * 动态指定部门id。
     */
    @Schema(description = "动态指定部门id")
    private String dynamicApproverDept;

    /**
     * 抄送对象。
     */
    @Schema(description = "抄送对象")
    private String copyUser;

    /**
     * 指定处理用户
     */
    @Schema(description = "指定处理用户")
    private String appointApprovalUser;


}

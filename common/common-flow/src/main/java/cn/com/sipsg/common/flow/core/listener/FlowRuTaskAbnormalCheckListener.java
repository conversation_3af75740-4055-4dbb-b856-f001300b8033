package cn.com.sipsg.common.flow.core.listener;

import cn.com.sipsg.common.constant.CommonConstants;
import cn.com.sipsg.common.enums.CommonStatusEnum;
import cn.com.sipsg.common.flow.core.constant.FlowApprovalType;
import cn.com.sipsg.common.flow.core.constant.FlowConstant;
import cn.com.sipsg.common.flow.core.entity.FlowRuTaskAbnormalRecord;
import cn.com.sipsg.common.flow.core.entity.FlowTaskComment;
import cn.com.sipsg.common.flow.core.entity.FlowTaskExt;
import cn.com.sipsg.common.flow.core.pojo.FlowTaskPostCandidateGroup;
import cn.com.sipsg.common.flow.core.pojo.dto.FlowEntryDTO;
import cn.com.sipsg.common.flow.core.service.FlowEntryService;
import cn.com.sipsg.common.flow.core.service.FlowMessageService;
import cn.com.sipsg.common.flow.core.service.FlowRuTaskAbnormalRecordService;
import cn.com.sipsg.common.flow.core.service.FlowTaskCommentService;
import cn.com.sipsg.common.flow.core.service.FlowTaskExtService;
import cn.com.sipsg.common.security.core.util.SecurityUtils;
import cn.com.sipsg.common.util.CollectionUtils;
import cn.com.sipsg.module.system.api.upms.SysUserApi;
import cn.com.sipsg.module.system.api.upms.dto.SysUserRespDTO;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.delegate.TaskListener;
import org.flowable.task.api.Task;
import org.flowable.task.service.delegate.DelegateTask;
import org.springframework.stereotype.Component;

/**
 * 流程生成待办任务异常处理人校验监听器(主要校验根据给出的角色，部门，动态指定的条件是否能查询到对应的人员)
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Slf4j
@Component
public class FlowRuTaskAbnormalCheckListener implements TaskListener {

    private final transient FlowTaskCommentService flowTaskCommentService = SpringUtil.getBean(FlowTaskCommentService.class);

    private final transient FlowTaskExtService flowTaskExtService = SpringUtil.getBean(FlowTaskExtService.class);

    private final transient SysUserApi sysUserApi = SpringUtil.getBean(SysUserApi.class);

    private final transient FlowRuTaskAbnormalRecordService ruTaskAbnormalRecordService = SpringUtil.getBean(FlowRuTaskAbnormalRecordService.class);

    private final transient FlowEntryService flowEntryService = SpringUtil.getBean(FlowEntryService.class);

    private final transient FlowMessageService flowMessageService = SpringUtil.getBean(FlowMessageService.class);

    private final transient RuntimeService runtimeService = SpringUtil.getBean(RuntimeService.class);

    @Override
    public void notify(DelegateTask delegateTask) {
        try {
            //如果是从其他节点干预到当前节点，当前节点的校验逻辑就不用触发了，干预操作已经选择处理人了，不用重复校验了
            String approvalType = flowTaskCommentService.getLatestFlowTaskComment(delegateTask.getProcessInstanceId()).getApprovalType();
            if (FlowApprovalType.INTERVENE.equals(approvalType)) {
                return;
            }
            //干预操作，选择当前节点，处理用户选了一个人，此时不要走后面的校验逻辑代码了
            FlowTaskComment latestFlowTaskComment = flowTaskCommentService.getLatestFlowTaskComment(delegateTask.getProcessInstanceId(), delegateTask.getTaskDefinitionKey());
            if (null != latestFlowTaskComment && latestFlowTaskComment.getApprovalType().equals(FlowApprovalType.INTERVENE)) {
                return;
            }
            FlowTaskExt flowTaskExt = flowTaskExtService.getByProcessDefinitionIdAndTaskId(delegateTask.getProcessDefinitionId(), delegateTask.getTaskDefinitionKey());
            String groupType = flowTaskExt.getGroupType();
            if (groupType.equals(FlowConstant.GROUP_TYPE_ASSIGNEE) || groupType.equals(FlowConstant.GROUP_TYPE_USERS) || groupType.equals(FlowConstant.GROUP_TYPE_DYNAMIC_APPOINT_APPROVE_USER)) {
                //具体到人的，能在页面选到说明系统中有，暂时不做校验
                return;
            }
            FlowRuTaskAbnormalRecord flowRuTaskAbnormalRecord = null;
            List<SysUserRespDTO> userListByCondition;
            switch (groupType) {
                case FlowConstant.GROUP_TYPE_ROLE:
                    //角色类型的任务处理人
                    userListByCondition = sysUserApi.getUserListByRoleIds(Arrays.asList(StringUtils.split(flowTaskExt.getRoleIds(), StrUtil.COMMA)));
                    if (CollectionUtils.isAnyEmpty(userListByCondition)) {
                        flowRuTaskAbnormalRecord = this.creatRecord(delegateTask, groupType, flowTaskExt.getRoleIds(), "任务处理人设置的角色未查询到角色对应的人");
                    }
                    break;
                case FlowConstant.GROUP_TYPE_DEPT:
                    //部门类型的任务处理人
                    userListByCondition = sysUserApi.getUserListByDeptIds(Arrays.asList(StringUtils.split(flowTaskExt.getDeptIds(), StrUtil.COMMA)));
                    if (CollectionUtils.isAnyEmpty(userListByCondition)) {
                        flowRuTaskAbnormalRecord = this.creatRecord(delegateTask, groupType, flowTaskExt.getDeptIds(), "任务处理人设置的部门未查询到部门对应的人");
                    }
                    break;
                case FlowConstant.GROUP_TYPE_DYNAMIC_APPOINT_APPROVE_DEPT:
                    //动态指定部门的任务处理人
                    Object deptId = delegateTask.getVariable(String.valueOf(flowTaskExt.getDynamicAppointField()));
                    userListByCondition = sysUserApi.getUserListByDeptIds(Arrays.asList(StringUtils.split(deptId.toString(), StrUtil.COMMA)));
                    if (CollectionUtils.isAnyEmpty(userListByCondition)) {
                        flowRuTaskAbnormalRecord = this.creatRecord(delegateTask, groupType, deptId.toString(), "任务处理人设置的动态指定部门未查询到对应的人");
                    }
                    break;
                case FlowConstant.GROUP_TYPE_POST:
                    userListByCondition = getPostTypeUsers(flowTaskExt, delegateTask);
                    if (CollectionUtils.isAnyEmpty(userListByCondition)) {
                        flowRuTaskAbnormalRecord = this.creatRecord(delegateTask, groupType, flowTaskExt.getDeptPostListJson(), "任务处理人设置的岗位条件未查询到对应的人");
                    }
                    break;
                case FlowConstant.GROUP_TYPE_DEPT_POST_LEADER:
                    Object leaderDeptPostId = delegateTask.getVariable(FlowConstant.GROUP_TYPE_DEPT_POST_LEADER_VAR);
                    if (null != leaderDeptPostId) {
                        List<SysUserRespDTO> userListByDeptPostIds = sysUserApi.getUserListByDeptPostIds(Collections.singletonList(leaderDeptPostId.toString()));
                        if (CollectionUtils.isAnyEmpty(userListByDeptPostIds)) {
                            flowRuTaskAbnormalRecord = this.creatRecord(delegateTask, groupType, leaderDeptPostId.toString(), "任务处理人设置的流程发起人部门领导未查询到对应的人");
                        }
                    }
                    break;
                case FlowConstant.GROUP_TYPE_UP_DEPT_POST_LEADER:
                    Object upLeaderDeptPostId = delegateTask.getVariable(FlowConstant.GROUP_TYPE_UP_DEPT_POST_LEADER_VAR);
                    if (upLeaderDeptPostId == null) {
                        //为null标识当前发起人没有上级部门岗位领导这个岗位，此时也算作异常
                        flowRuTaskAbnormalRecord = this.creatRecord(delegateTask, groupType, null, "任务处理人设置的流程发起人上级部门领导未查询到对应的人");
                    } else {
                        List<SysUserRespDTO> userListByDeptPostIds = sysUserApi.getUserListByDeptPostIds(Collections.singletonList(upLeaderDeptPostId.toString()));
                        if (CollectionUtils.isAnyEmpty(userListByDeptPostIds)) {
                            flowRuTaskAbnormalRecord = this.creatRecord(delegateTask, groupType, upLeaderDeptPostId.toString(), "任务处理人设置的流程发起人上级部门领导未查询到对应的人");
                        }
                    }
                    break;
                default:
                    break;
            }
            if (null != flowRuTaskAbnormalRecord) {
                ruTaskAbnormalRecordService.save(flowRuTaskAbnormalRecord);
                //给超级管理员发送一个抄送信息
                JSONObject copyDataJson = new JSONObject();
                copyDataJson.put(FlowConstant.GROUP_TYPE_USER_VAR, CommonConstants.SUPER_ADMIN_USER_ID);
                flowMessageService.saveNewCopyMessage((Task)delegateTask, copyDataJson, false, SecurityUtils.getLoginUserId() == null ? CommonConstants.SUPER_ADMIN_USER_ID : SecurityUtils.getLoginUserId());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    private List<SysUserRespDTO> getPostTypeUsers(FlowTaskExt flowTaskExt, DelegateTask delegateTask) {
        Assert.notNull(flowTaskExt.getDeptPostListJson());
        List<FlowTaskPostCandidateGroup> groupDataList = JSONArray.parseArray(flowTaskExt.getDeptPostListJson(), FlowTaskPostCandidateGroup.class);
        List<SysUserRespDTO> sysUserRespDTOS = new ArrayList<>();
        for (FlowTaskPostCandidateGroup groupData : groupDataList) {
            switch (groupData.getType()) {
                case FlowConstant.GROUP_TYPE_SELF_DEPT_POST_VAR:
                    this.getUsersByPost(groupData.getPostId(), sysUserRespDTOS, delegateTask, FlowConstant.SELF_DEPT_POST_PREFIX);
                    break;
                case FlowConstant.GROUP_TYPE_SIBLING_DEPT_POST_VAR:
                    this.getUsersByPost(groupData.getPostId(), sysUserRespDTOS, delegateTask, FlowConstant.SIBLING_DEPT_POST_PREFIX);
                    break;
                case FlowConstant.GROUP_TYPE_UP_DEPT_POST_VAR:
                    this.getUsersByPost(groupData.getPostId(), sysUserRespDTOS, delegateTask, FlowConstant.UP_DEPT_POST_PREFIX);
                    break;
                case FlowConstant.GROUP_TYPE_ALL_DEPT_POST_VAR:
                    List<SysUserRespDTO> userListByPostIds = sysUserApi.getUserListByPostIds(Collections.singletonList(groupData.getPostId()));
                    sysUserRespDTOS.addAll(userListByPostIds);
                    break;
                case FlowConstant.GROUP_TYPE_DEPT_POST_VAR:
                    List<SysUserRespDTO> userListByDeptPostIds = sysUserApi.getUserListByDeptPostIds(Collections.singletonList(groupData.getDeptPostId()));
                    sysUserRespDTOS.addAll(userListByDeptPostIds);
                    break;
                default:
                    break;
            }
        }
        return sysUserRespDTOS;
    }

    private List<SysUserRespDTO> getUsersByPost(String postId, List<SysUserRespDTO> sysUserRespDTOS, DelegateTask delegateTask, String type) {
        Object deptPostId = delegateTask.getVariable(type + postId);
        if (null == deptPostId) {
            return sysUserRespDTOS;
        }
        //根据部门岗位id查询人员
        List<SysUserRespDTO> userListByDeptPostIds = sysUserApi.getUserListByDeptPostIds(Collections.singletonList(deptPostId.toString()));
        sysUserRespDTOS.addAll(userListByDeptPostIds);
        return sysUserRespDTOS;
    }

    private FlowRuTaskAbnormalRecord creatRecord(DelegateTask delegateTask, String groupType, String typeValue, String reason) {
        FlowRuTaskAbnormalRecord ruTaskAbnormalRecord = new FlowRuTaskAbnormalRecord();
        ruTaskAbnormalRecord.setTaskKey(delegateTask.getTaskDefinitionKey());
        ruTaskAbnormalRecord.setTaskId(delegateTask.getId());
        ruTaskAbnormalRecord.setGroupType(groupType);
        ruTaskAbnormalRecord.setTypeValue(typeValue);
        ruTaskAbnormalRecord.setTaskName(delegateTask.getName());
        ruTaskAbnormalRecord.setProcessDefinitionId(delegateTask.getProcessDefinitionId());
        ruTaskAbnormalRecord.setProcessInstanceId(delegateTask.getProcessInstanceId());
        ruTaskAbnormalRecord.setReason(reason);
        ruTaskAbnormalRecord.setStatus(CommonStatusEnum.DISABLE.getCode());
        FlowEntryDTO flowEntryDTO = flowEntryService.getFlowEntryByProcessDefinitionId(delegateTask.getProcessDefinitionId());
        ruTaskAbnormalRecord.setProcessDefinitionKey(flowEntryDTO.getProcessDefinitionKey());
        return ruTaskAbnormalRecord;
    }
}

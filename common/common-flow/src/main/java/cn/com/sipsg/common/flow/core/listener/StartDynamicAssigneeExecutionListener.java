package cn.com.sipsg.common.flow.core.listener;

import cn.com.sipsg.common.flow.core.constant.FlowConstant;
import com.alibaba.fastjson2.JSONArray;
import java.util.Arrays;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.ExecutionListener;
import org.flowable.engine.impl.persistence.entity.ExecutionEntityImpl;
import org.springframework.stereotype.Component;

/**
 * 动态指定会签人java类 流程图中会签节点无法配置会签人时，通过任务变量动态的指定到会签节点上，任务变量携带的是会签人账号
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Slf4j
@Component
public class StartDynamicAssigneeExecutionListener implements ExecutionListener {

    /**
     * 流程图中会签节点无法配置会签人时，通过任务变量动态的指定到会签节点上，任务变量携带的是会签人账号
     *
     * @param execution 执行监听器对象
     */
    @Override
    public void notify(DelegateExecution execution) {
        JSONArray assigneeList = (JSONArray)execution.getVariable("assigneeList");
        if (null == assigneeList) {
            assigneeList = new JSONArray();
        }
        String appointApprovalUserString = (String)execution.getVariable(FlowConstant.DYNAMIC_APPOINT_COUNTERSIGNER);
        if (StringUtils.isNotBlank(appointApprovalUserString)) {
            List<String> asList = Arrays.asList(appointApprovalUserString.split(","));
            assigneeList.clear();
            assigneeList.addAll(asList);
        }
        execution.setVariable("assigneeList", assigneeList);
        log.info("StartDynamicAssigneeExecutionListener监听器处理的流程实例id为:{},节点:{}，节点中文名称：{}，分配的会签人：{}", execution.getProcessInstanceId(), ((ExecutionEntityImpl)execution).getActivityId(),
            ((ExecutionEntityImpl)execution).getActivityName(), assigneeList.toString());
    }
}

package cn.com.sipsg.common.flow.core.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 发起流程返回结果VO对象
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Setter
@Getter
@Schema(description = "发起流程返回结果VO对象")
public class FlowStartAndTakeUserTaskVO {

    /**
     * 流程实例id
     */
    @Schema(description = "流程实例id")
    private String processInstanceId;

    /**
     * 流程定义id
     */
    @Schema(description = "流程定义id")
    private String processDefinitionId;

    /**
     * 流程定义key
     */
    @Schema(description = "流程定义key")
    private String processDefinitionKey;

    /**
     * 生成的工单id
     */
    @Schema(description = "生成的工单id")
    private String workOrderId;


    public FlowStartAndTakeUserTaskVO(String processInstanceId, String processDefinitionId, String processDefinitionKey, String workOrderId) {
        this.processInstanceId = processInstanceId;
        this.processDefinitionId = processDefinitionId;
        this.processDefinitionKey = processDefinitionKey;
        this.workOrderId = workOrderId;
    }

}

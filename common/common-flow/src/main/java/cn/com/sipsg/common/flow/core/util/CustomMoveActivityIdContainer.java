package cn.com.sipsg.common.flow.core.util;

import java.util.List;
import org.flowable.engine.impl.runtime.MoveActivityIdContainer;

/**
 * 自定义移动任务Id的容器类。
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public class CustomMoveActivityIdContainer extends MoveActivityIdContainer {

    public CustomMoveActivityIdContainer(String singleActivityId, String moveToActivityId) {
        super(singleActivityId, moveToActivityId);
    }

    public CustomMoveActivityIdContainer(List<String> activityIds, List<String> moveToActivityIds) {
        super(activityIds.get(0), moveToActivityIds.get(0));
        this.activityIds = activityIds;
        this.moveToActivityIds = moveToActivityIds;
    }
}

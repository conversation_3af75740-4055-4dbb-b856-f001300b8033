package cn.com.sipsg.common.flow.core.pojo.bo;

import cn.com.sipsg.common.pojo.bo.SortablePageBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 工作流消息的分页BO
 * 
 * <AUTHOR>
 * @since 2025/04/14
 **/
@Getter
@Setter
@Schema(description = "工作流消息的分页BO")
public class FlowMessagePageBO extends SortablePageBO {

    /**
    * true表示已读，false表示未读
    */
    @Schema(description = "true表示已读，false表示未读")
    private Boolean read;
}

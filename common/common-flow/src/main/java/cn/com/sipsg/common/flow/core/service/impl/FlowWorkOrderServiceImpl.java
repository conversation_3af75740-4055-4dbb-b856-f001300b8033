package cn.com.sipsg.common.flow.core.service.impl;

import cn.com.sipsg.common.flow.core.constant.FlowTaskStatus;
import cn.com.sipsg.common.flow.core.entity.FlowEntry;
import cn.com.sipsg.common.flow.core.entity.FlowWorkOrder;
import cn.com.sipsg.common.flow.core.entity.FlowWorkOrderExt;
import cn.com.sipsg.common.flow.core.mapper.FlowWorkOrderExtMapper;
import cn.com.sipsg.common.flow.core.mapper.FlowWorkOrderMapper;
import cn.com.sipsg.common.flow.core.pojo.bo.FlowWorkOrderPageBO;
import cn.com.sipsg.common.flow.core.pojo.dto.ColumnEncodedRuleDTO;
import cn.com.sipsg.common.flow.core.pojo.dto.FlowWorkOrderReqDTO;
import cn.com.sipsg.common.flow.core.pojo.vo.FlowWorkOrderVO;
import cn.com.sipsg.common.flow.core.service.FlowApiService;
import cn.com.sipsg.common.flow.core.service.FlowEntryService;
import cn.com.sipsg.common.flow.core.service.FlowWorkOrderService;
import cn.com.sipsg.common.mybatis.core.query.LambdaQueryWrapperX;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.redis.core.util.RedisUtil;
import cn.com.sipsg.common.security.core.LoginUser;
import cn.com.sipsg.common.security.core.util.SecurityUtils;
import cn.com.sipsg.common.util.AssertUtils;
import cn.com.sipsg.common.util.BeanUtils;
import cn.com.sipsg.common.util.CollectionUtils;
import cn.com.sipsg.module.system.api.upms.SysUserApi;
import cn.com.sipsg.module.system.api.upms.dto.SysUserRespDTO;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.runtime.ProcessInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 作流工单实体对象实现类
 *
 * <AUTHOR>
 * <AUTHOR>
 * @since 2025/04/14
 * @since 2025/04/14
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FlowWorkOrderServiceImpl extends ServiceImpl<FlowWorkOrderMapper, FlowWorkOrder> implements FlowWorkOrderService {


    @Autowired
    private FlowWorkOrderMapper flowWorkOrderMapper;

    @Autowired
    private FlowWorkOrderExtMapper flowWorkOrderExtMapper;

    @Autowired
    private FlowApiService flowApiService;

    @Autowired
    private FlowEntryService flowEntryService;

    @Autowired
    private RedisUtil redisUtil;

    private final SysUserApi sysUserApi;


    /**
     * 保存新增对象。
     *
     * @param instance      流程实例对象。
     * @param dataId        流程实例的BusinessKey。
     * @param onlineTableId 在线数据表的主键Id。
     * @param tableName     面向静态表单所使用的表名。
     * @return 新增的工作流工单对象。
     */
    @Transactional
    @Override
    public FlowWorkOrder saveNew(ProcessInstance instance, Object dataId, String onlineTableId, String tableName, String deptOfSubmitUser) {
        // 正常插入流程工单数据。
        FlowWorkOrder flowWorkOrder = this.createWith(instance, deptOfSubmitUser);
        flowWorkOrder.setWorkOrderCode(this.generateWorkOrderCode(instance.getProcessDefinitionKey()));
        flowWorkOrder.setBusinessKey(dataId.toString());
        // todo onlineTableId和tableName字段动态表单设计已改变，这边可能不做维护了
        //flowWorkOrder.setOnlineTableId(onlineTableId);
        //flowWorkOrder.setTableName(tableName);
        flowWorkOrder.setFlowStatus(FlowTaskStatus.SUBMITTED);
        try {
            flowWorkOrderMapper.insert(flowWorkOrder);
        } catch (DuplicateKeyException e) {
            // 数据插入过程中，如果抛出 “数据重复值 (DuplicationKeyException)” 时，会捕捉该异常。
            // 执行 SQL 查询操作，判断本次计算的工单编码是否已经存在。如不存在，该异常则为其他字段值重复所引起，可直接再次抛出。
            if (flowWorkOrderMapper.getCountByWorkOrderCode(flowWorkOrder.getWorkOrderCode()) == 0) {
                throw e;
            }
            log.info("WorkOrderCode [{}] exists and recalculate.", flowWorkOrder.getWorkOrderCode());
            // 如存在该工单编码的数据，则可以理解为负责计算工单编码的 Redis 出现了问题，
            // 需要为该工单字段所关联的 Redis 原子计数器重新设置初始值。
            this.recalculateWorkOrderCode(instance.getProcessDefinitionKey());
            // 重新初始化后，再次执行generateWorkOrderCode方法计算出新的工单编码。
            flowWorkOrder.setWorkOrderCode(this.generateWorkOrderCode(instance.getProcessDefinitionKey()));
            // 并再次提交当前的工单数据。
            flowWorkOrderMapper.insert(flowWorkOrder);
        }
        log.info("flowWorkOrder的id为：{}", flowWorkOrder.getWorkOrderId());
        return flowWorkOrder;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public FlowWorkOrder saveNewWithDraft(String processDefinitionKey, String processDefinitionName, String processDefinitionId, String formData) {
        //FlowWorkOrder flowWorkOrder = this.createWith(instance, deptOfSubmitUser);
        FlowWorkOrder flowWorkOrder = new FlowWorkOrder();
        flowWorkOrder.setProcessDefinitionKey(processDefinitionKey);
        flowWorkOrder.setProcessDefinitionName(processDefinitionName);
        flowWorkOrder.setProcessDefinitionId(processDefinitionId);
        flowWorkOrder.setWorkOrderCode(this.generateWorkOrderCode(processDefinitionKey));
        flowWorkOrder.setSubmitUsername(SecurityUtils.getLoginUserId());
        flowWorkOrder.setFlowStatus(FlowTaskStatus.DRAFT);
        //动态表单无需区分主表从表数据了，把表单数据直接存下来，表单回显时返回
        FlowWorkOrderExt flowWorkOrderExt = BeanUtil.copyProperties(flowWorkOrder, FlowWorkOrderExt.class);
        flowWorkOrderMapper.insert(flowWorkOrder);
        //表单草稿数据保存至draftData字段里
        flowWorkOrderExt.setDraftData(formData);
        flowWorkOrderExt.setWorkOrderId(flowWorkOrder.getWorkOrderId());
        flowWorkOrderExtMapper.insert(flowWorkOrderExt);
        return flowWorkOrder;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateDraft(String workOrderId, String fromData) {
        FlowWorkOrderExt flowWorkOrderExt = new FlowWorkOrderExt();
        flowWorkOrderExt.setDraftData(fromData);
        flowWorkOrderExtMapper.update(flowWorkOrderExt, new LambdaQueryWrapper<FlowWorkOrderExt>().eq(FlowWorkOrderExt::getWorkOrderId, workOrderId));
    }

    /**
     * 删除指定数据。
     *
     * @param workOrderId 主键Id。
     * @return 成功返回true，否则false。
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean remove(String workOrderId) {
        return flowWorkOrderMapper.deleteById(workOrderId) == 1;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void removeByProcessInstanceId(String processInstanceId) {
        LambdaQueryWrapperX<FlowWorkOrder> queryWrapperX = new LambdaQueryWrapperX<>();
        queryWrapperX.eq(FlowWorkOrder::getProcessInstanceId, processInstanceId);
        this.remove(queryWrapperX);
    }

    @Override
    public List<FlowWorkOrder> getFlowWorkOrderList(FlowWorkOrderReqDTO filter, String orderBy) {
        LambdaQueryWrapperX<FlowWorkOrder> queryWrapperX = new LambdaQueryWrapperX<>();
        queryWrapperX.eqIfPresent(FlowWorkOrder::getProcessInstanceId, filter.getProcessInstanceId());
        queryWrapperX.eqIfPresent(FlowWorkOrder::getWorkOrderCode, filter.getWorkOrderCode());
        queryWrapperX.eqIfPresent(FlowWorkOrder::getProcessDefinitionKey, filter.getProcessDefinitionKey());
        queryWrapperX.eqIfPresent(FlowWorkOrder::getLatestApprovalStatus, filter.getLatestApprovalStatus());
        queryWrapperX.eqIfPresent(FlowWorkOrder::getFlowStatus, filter.getFlowStatus());
        queryWrapperX.betweenIfPresent(FlowWorkOrder::getCreateTime, filter.getCreateTimeStart(), filter.getCreateTimeEnd());
        queryWrapperX.orderByAsc(FlowWorkOrder::getCreateTime);
        return this.list(queryWrapperX);
    }

    @Override
    public FlowWorkOrder getFlowWorkOrderByProcessInstanceId(String processInstanceId) {
        FlowWorkOrder filter = new FlowWorkOrder();
        filter.setProcessInstanceId(processInstanceId);
        return flowWorkOrderMapper.selectOne(new QueryWrapper<>(filter));
    }

    @Override
    public boolean existByBusinessKey(String tableName, Object businessKey, boolean unfinished) {
        LambdaQueryWrapper<FlowWorkOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FlowWorkOrder::getBusinessKey, businessKey.toString());
        queryWrapper.eq(FlowWorkOrder::getTableName, tableName);
        if (unfinished) {
            queryWrapper.notIn(FlowWorkOrder::getFlowStatus,
                FlowTaskStatus.FINISHED, FlowTaskStatus.CANCELLED, FlowTaskStatus.STOPPED);
        }
        return flowWorkOrderMapper.selectCount(queryWrapper) > 0;
    }

    @Transactional
    @Override
    public void updateFlowStatusByProcessInstanceId(String processInstanceId, Integer flowStatus, String updateBy) {
        if (flowStatus == null) {
            return;
        }
        FlowWorkOrder flowWorkOrder = new FlowWorkOrder();
        flowWorkOrder.setFlowStatus(flowStatus);
        if (FlowTaskStatus.FINISHED != flowStatus) {
            flowWorkOrder.setUpdateTime(LocalDateTime.now());
            flowWorkOrder.setUpdateBy(SecurityUtils.getLoginUserId());
        }
        LambdaQueryWrapper<FlowWorkOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FlowWorkOrder::getProcessInstanceId, processInstanceId);
        if (null == SecurityUtils.getLoginUser()) {
            // SecurityUtils.getLoginUser() 表示可能是超时任务自动通过触发的，没有登录
            flowWorkOrder.setUpdateBy(updateBy == null ? "" : updateBy);
        }
        flowWorkOrderMapper.update(flowWorkOrder, queryWrapper);
    }

    @Transactional
    @Override
    public FlowWorkOrder updateLatestApprovalStatusByProcessInstanceId(String processInstanceId, Integer approvalStatus) {
        FlowWorkOrder flowWorkOrder = this.getFlowWorkOrderByProcessInstanceId(processInstanceId);
        if (approvalStatus == null) {
            return flowWorkOrder;
        }
        flowWorkOrder.setLatestApprovalStatus(approvalStatus);
        flowWorkOrderMapper.updateById(flowWorkOrder);
        // 目前动态表单没有标识表的某个字段是状态字段这种功能，该处更新字段状态值功能暂时废弃
        /*if (flowWorkOrder.getOnlineTableId() != null) {
            // 处理在线表单工作流的自定义状态更新。
            // 更新工单状态时同步更新在线表单入库的数据，状态字段值
            //flowCustomExtFactory.getOnlineBusinessDataExtHelper().updateFlowStatus(flowWorkOrder);
        } else {
            // 处理路由表单工作里的自定义状态更新。
            //flowCustomExtFactory.getBusinessDataExtHelper().updateFlowStatus(flowWorkOrder);
        }*/
        return flowWorkOrder;
    }

    @Override
    public boolean hasDataPermOnFlowWorkOrder(String processInstanceId) {
        FlowWorkOrder filter = new FlowWorkOrder();
        filter.setProcessInstanceId(processInstanceId);
        return flowWorkOrderMapper.selectCount(new QueryWrapper<>(filter)) > 0;
    }

    @Override
    public void fillUserShowNameByLoginName(List<FlowWorkOrderVO> dataList) {
        Set<String> loginNameSet = dataList.stream().map(FlowWorkOrderVO::getSubmitUsername).collect(Collectors.toSet());
        if (CollUtil.isEmpty(loginNameSet)) {
            return;
        }
        Map<String, String> userNameMap = CollectionUtils.convertMap(sysUserApi.getUserListByUsernames(loginNameSet), SysUserRespDTO::getUsername, SysUserRespDTO::getRealName);
        dataList.forEach(workOrder -> {
            if (StrUtil.isNotBlank(workOrder.getSubmitUsername())) {
                workOrder.setUserShowName(userNameMap.get(workOrder.getSubmitUsername()));
            }
        });
    }

    @Override
    public FlowWorkOrderExt getFlowWorkOrderExtByWorkOrderId(String workOrderId) {
        return flowWorkOrderExtMapper.selectOne(
            new LambdaQueryWrapper<FlowWorkOrderExt>().eq(FlowWorkOrderExt::getWorkOrderId, workOrderId));
    }

    @Override
    public List<FlowWorkOrderExt> getFlowWorkOrderExtByWorkOrderIds(Set<String> workOrderIds) {
        return flowWorkOrderExtMapper.selectList(new LambdaQueryWrapper<FlowWorkOrderExt>().in(FlowWorkOrderExt::getWorkOrderId, workOrderIds));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void removeDraft(FlowWorkOrder flowWorkOrder) {
        flowWorkOrderMapper.deleteById(flowWorkOrder.getWorkOrderId());
        flowApiService.stopProcessInstance(flowWorkOrder.getProcessInstanceId(), "撤销草稿", true);
        //return flowApiService.stopProcessInstance(flowWorkOrder.getProcessInstanceId(), "撤销草稿", true);
    }

    @Override
    public CommonPageVO<FlowWorkOrderVO> page(FlowWorkOrderPageBO flowWorkOrderPageBO) {
        LambdaQueryWrapperX<FlowWorkOrder> queryWrapperX = new LambdaQueryWrapperX<>();
        queryWrapperX.eq(FlowWorkOrder::getProcessDefinitionKey, flowWorkOrderPageBO.getProcessDefinitionKey());
        queryWrapperX.eqIfPresent(FlowWorkOrder::getFlowStatus, flowWorkOrderPageBO.getFlowStatus());
        queryWrapperX.eqIfPresent(FlowWorkOrder::getWorkOrderCode, flowWorkOrderPageBO.getWorkOrderCode());
        queryWrapperX.betweenIfPresent(FlowWorkOrder::getCreateTime, flowWorkOrderPageBO.getCreateTimeStart(), flowWorkOrderPageBO.getCreateTimeEnd());
        queryWrapperX.orderByDesc(FlowWorkOrder::getCreateTime);
        return this.page(flowWorkOrderPageBO, queryWrapperX).convert(flowWorkOrder -> BeanUtils.copyProperties(flowWorkOrder, FlowWorkOrderVO.class));
    }

    @Override
    public List<FlowWorkOrder> getFlowWorkOrderByProcessInstanceIdIn(Set<String> processInstanceIdSet) {
        return flowWorkOrderMapper.selectList(new LambdaQueryWrapper<FlowWorkOrder>().in(FlowWorkOrder::getProcessInstanceId, processInstanceIdSet));
    }

    private FlowWorkOrder createWith(ProcessInstance instance, String deptOfSubmitUser) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        FlowWorkOrder flowWorkOrder = new FlowWorkOrder();
        flowWorkOrder.setProcessDefinitionKey(instance.getProcessDefinitionKey());
        flowWorkOrder.setProcessDefinitionName(instance.getProcessDefinitionName());
        flowWorkOrder.setProcessDefinitionId(instance.getProcessDefinitionId());
        flowWorkOrder.setProcessInstanceId(instance.getId());
        flowWorkOrder.setSubmitUsername(loginUser.getUserId());
        flowWorkOrder.setCreateBy(loginUser.getUserId());
        flowWorkOrder.setCreateTime(LocalDateTime.now());
        AssertUtils.isTrue(CollUtil.isEmpty(loginUser.getDeptIds()), "用户未关联部门");
        ArrayList<String> deptIds = new ArrayList<>(loginUser.getDeptIds());
        if (StrUtil.isNotEmpty(deptOfSubmitUser)) {
            if (deptIds.stream().filter(e -> StrUtil.equals(e, deptOfSubmitUser)).findFirst().isPresent()) {
                flowWorkOrder.setDeptOfSubmitUser(deptIds.stream().filter(e -> StrUtil.equals(e, deptOfSubmitUser)).findFirst().get());
            }
        } else {
            flowWorkOrder.setDeptOfSubmitUser(deptIds.get(0));
        }
        return flowWorkOrder;
    }

    private String generateWorkOrderCode(String processDefinitionKey) {
        FlowEntry flowEntry = flowEntryService.getFlowEntryFromCache(processDefinitionKey);
        if (StrUtil.isBlank(flowEntry.getEncodedRule())) {
            return null;
        }
        ColumnEncodedRuleDTO rule = JSON.parseObject(flowEntry.getEncodedRule(), ColumnEncodedRuleDTO.class);
        if (rule.getIdWidth() == null) {
            rule.setIdWidth(10);
        }
        return redisUtil.generateTransId(
            rule.getPrefix(), rule.getPrecisionTo(), rule.getMiddle(), rule.getIdWidth());
    }

    private void recalculateWorkOrderCode(String processDefinitionKey) {
        FlowEntry flowEntry = flowEntryService.getFlowEntryFromCache(processDefinitionKey);
        if (StrUtil.isBlank(flowEntry.getEncodedRule())) {
            return;
        }
        // 获取当前流程定义中，为工单编码字段设置的规则配置信息。
        ColumnEncodedRuleDTO rule = JSON.parseObject(flowEntry.getEncodedRule(), ColumnEncodedRuleDTO.class);
        if (rule.getIdWidth() == null) {
            rule.setIdWidth(10);
        }
        // 根据当前规则中的数据，计算出该规则在Redis中AtomicLong对象的键。
        String prefix = redisUtil.calculateTransIdPrefix(rule.getPrefix(), rule.getPrecisionTo(), rule.getMiddle());
        // 根据该键(规则前缀)计算出符合该前缀的工单编码的最大值。
        String maxWorkOrderCode = flowWorkOrderMapper.getMaxWorkOrderCodeByPrefix(prefix + "%");
        // 移除前缀部分，剩余部分即为计数器的最大值。
        String maxValue = StrUtil.removePrefix(maxWorkOrderCode, prefix);
        // 用当前的最大值，为该key的AtomicLong对象设置初始值，后面的请求都会在该值上原子性加一了。
        redisUtil.initTransId(prefix, Long.valueOf(maxValue));
    }
}

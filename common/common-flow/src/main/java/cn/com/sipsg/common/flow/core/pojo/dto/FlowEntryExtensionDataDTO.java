package cn.com.sipsg.common.flow.core.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * 流程扩展数据对象。
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "流程扩展数据对象")
public class FlowEntryExtensionDataDTO {

    /**
     * 通知类型。
     */
    @Schema(description = "通知类型")
    private List<String> notifyTypes;

    /**
     * 流程审批状态字典数据列表。Map的key是id和name。
     */
    @Schema(description = "流程审批状态字典数据列表。Map的key是id和name")
    private List<Map<String, String>> approvalStatusDict;

    /**
     * 级联删除业务数据。
     */
    @Schema(description = "级联删除业务数据")
    private Boolean cascadeDeleteBusinessData = false;
}

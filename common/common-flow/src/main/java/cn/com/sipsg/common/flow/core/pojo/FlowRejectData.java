package cn.com.sipsg.common.flow.core.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 流程驳回数据。主要用于RejectType为1，既重新提交会驳回人的驳回类型。
 *
 * <AUTHOR>
 * @since 2025/04/14
 **/
@Getter
@Setter
@Schema(description = "流程驳回数据")
public class FlowRejectData {
    /**
     * 驳回用户名。
     */
    @Schema(description = "驳回用户名")
    private String sourceUser;

    /**
     * 从哪个任务驳回的任务定义标识。
     */
    @Schema(description = "从哪个任务驳回的任务定义标识")
    private String sourceTaskKey;

    /**
     * 驳回到的目标任务定义标识。
     */
    @Schema(description = "驳回到的目标任务定义标识")
    private List<String> targetTaskKeys;
}

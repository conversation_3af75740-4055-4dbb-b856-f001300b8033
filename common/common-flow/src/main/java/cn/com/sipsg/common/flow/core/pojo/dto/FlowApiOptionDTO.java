package cn.com.sipsg.common.flow.core.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 超时任务是否删除对象
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Setter
@Getter
@Schema(description = "超时任务是否删除对象")
public class FlowApiOptionDTO {

    /**
     * 是否删除任务超时作业
     */
    @Schema(description = "是否删除任务超时作业")
    private boolean deleteTimeoutTaskJob = false;

    /**
    * 是否超时自动审批
    */
    @Schema(description = "是否超时自动审批")
    private Boolean isTimeoutAutoComplete = false;

}

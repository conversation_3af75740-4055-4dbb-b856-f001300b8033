package cn.com.sipsg.common.flow.core.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 编码字段的编码规则
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "编码字段的编码规则")
public class ColumnEncodedRuleDTO {

    /**
     * 是否显示是计算并回显
     */
    @Schema(description = "是否显示是计算并回显")
    private Boolean calculateWhenView;

    /**
     * 前缀
     */
    @Schema(description = "前缀")
    private String prefix;

    /**
     * 精确到DAYS/HOURS/MINUTES/SECONDS
     */
    @Schema(description = "精确到DAYS/HOURS/MINUTES/SECONDS")
    private String precisionTo;

    /**
     * 中缀
     */
    @Schema(description = "中缀")
    private String middle;

    /**
     * 流水序号的字符宽度，不足的前面补0
     */
    @Schema(description = "流水序号的字符宽度，不足的前面补0")
    private Integer idWidth;

}

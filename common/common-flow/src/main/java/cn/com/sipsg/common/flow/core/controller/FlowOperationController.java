package cn.com.sipsg.common.flow.core.controller;

import static cn.com.sipsg.common.flow.core.constant.FlowConstant.ACTIVE_MULTI_INST_TASK;
import static cn.com.sipsg.common.flow.core.constant.FlowConstant.MULTI_SIGN_TASK_EXECUTION_ID_VAR;
import static cn.com.sipsg.common.flow.core.constant.FlowConstant.SHOW_NAME;

import cn.com.sipsg.common.enums.CommonStatusEnum;
import cn.com.sipsg.common.exception.enums.ErrorCodeEnum;
import cn.com.sipsg.common.flow.core.api.FlowableApi;
import cn.com.sipsg.common.flow.core.constant.FlowApprovalType;
import cn.com.sipsg.common.flow.core.constant.FlowBackType;
import cn.com.sipsg.common.flow.core.constant.FlowConstant;
import cn.com.sipsg.common.flow.core.constant.FlowMessageType;
import cn.com.sipsg.common.flow.core.constant.FlowTaskStatus;
import cn.com.sipsg.common.flow.core.constant.FlowUserTaskExtData;
import cn.com.sipsg.common.flow.core.entity.FlowEntry;
import cn.com.sipsg.common.flow.core.entity.FlowEntryPublish;
import cn.com.sipsg.common.flow.core.entity.FlowMessage;
import cn.com.sipsg.common.flow.core.entity.FlowMultiInstanceTrans;
import cn.com.sipsg.common.flow.core.entity.FlowRuTaskAbnormalRecord;
import cn.com.sipsg.common.flow.core.entity.FlowTaskComment;
import cn.com.sipsg.common.flow.core.entity.FlowTaskExt;
import cn.com.sipsg.common.flow.core.entity.FlowWorkOrder;
import cn.com.sipsg.common.flow.core.entity.FlowWorkOrderExt;
import cn.com.sipsg.common.flow.core.pojo.SubmitConsignBO;
import cn.com.sipsg.common.flow.core.pojo.bo.FlowStartAndTakeUserTaskBO;
import cn.com.sipsg.common.flow.core.pojo.bo.FlowSubmitUserTaskBO;
import cn.com.sipsg.common.flow.core.pojo.bo.FlowTaskPageBO;
import cn.com.sipsg.common.flow.core.pojo.bo.RejectRuntimeTaskBO;
import cn.com.sipsg.common.flow.core.pojo.dto.FlowEntryDTO;
import cn.com.sipsg.common.flow.core.pojo.dto.FlowReqDTO;
import cn.com.sipsg.common.flow.core.pojo.dto.FlowRespDTO;
import cn.com.sipsg.common.flow.core.pojo.dto.FlowSubmitUserReqDTO;
import cn.com.sipsg.common.flow.core.pojo.dto.FlowTaskPostCandidateGroupDTO;
import cn.com.sipsg.common.flow.core.pojo.vo.ActivityTaskVO;
import cn.com.sipsg.common.flow.core.pojo.vo.FlowStartAndTakeUserTaskVO;
import cn.com.sipsg.common.flow.core.pojo.vo.FlowTaskCommentVO;
import cn.com.sipsg.common.flow.core.pojo.vo.FlowTaskExtVO;
import cn.com.sipsg.common.flow.core.pojo.vo.FlowTaskVO;
import cn.com.sipsg.common.flow.core.pojo.vo.FlowUserInfoVO;
import cn.com.sipsg.common.flow.core.pojo.vo.TaskInfoVO;
import cn.com.sipsg.common.flow.core.service.FlowApiService;
import cn.com.sipsg.common.flow.core.service.FlowEntryService;
import cn.com.sipsg.common.flow.core.service.FlowMessageService;
import cn.com.sipsg.common.flow.core.service.FlowMultiInstanceTransService;
import cn.com.sipsg.common.flow.core.service.FlowRuTaskAbnormalRecordService;
import cn.com.sipsg.common.flow.core.service.FlowTaskCommentService;
import cn.com.sipsg.common.flow.core.service.FlowTaskExtService;
import cn.com.sipsg.common.flow.core.service.FlowTaskTimeoutJobService;
import cn.com.sipsg.common.flow.core.service.FlowWorkOrderService;
import cn.com.sipsg.common.flow.core.util.FlowCustomExtFactory;
import cn.com.sipsg.common.flow.core.util.FlowOperationHelper;
import cn.com.sipsg.common.mybatis.core.util.MyBatisUtils;
import cn.com.sipsg.common.operationlog.core.annotation.OperationLog;
import cn.com.sipsg.common.operationlog.core.enums.OperationTypeEnum;
import cn.com.sipsg.common.pojo.CommonResult;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.security.core.util.SecurityUtils;
import cn.com.sipsg.common.util.AssertUtils;
import cn.com.sipsg.common.util.BeanUtils;
import cn.com.sipsg.common.util.CollectionUtils;
import cn.com.sipsg.module.system.api.upms.SysUserApi;
import cn.com.sipsg.module.system.api.upms.dto.SysUserRespDTO;
import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.flowable.bpmn.converter.BpmnXMLConverter;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.bpmn.model.FlowElement;
import org.flowable.bpmn.model.UserTask;
import org.flowable.engine.TaskService;
import org.flowable.engine.history.HistoricActivityInstance;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.task.api.Task;
import org.flowable.task.api.TaskInfo;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.springframework.util.StreamUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 工作流流程操作接口。
 *
 * <AUTHOR>
 * @since 2025/04/14
 */

@Tag(name = "工作流流程操作控制器")
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/flow/flowOperation")
public class FlowOperationController {

    private final FlowEntryService flowEntryService;

    private final FlowTaskCommentService flowTaskCommentService;

    private final FlowTaskExtService flowTaskExtService;

    private final FlowApiService flowApiService;

    private final FlowWorkOrderService flowWorkOrderService;

    private final FlowMessageService flowMessageService;

    private final FlowOperationHelper flowOperationHelper;

    private final FlowCustomExtFactory flowCustomExtFactory;

    private final FlowMultiInstanceTransService flowMultiInstanceTransService;

    private final FlowTaskTimeoutJobService flowTaskTimeoutJobService;

    private final TaskService taskService;

    private final SysUserApi sysUserApi;

    private final FlowableApi flowableApi;

    private final FlowRuTaskAbnormalRecordService flowRuTaskAbnormalRecordService;

    /**
     * 发起流程
     *
     * @param flowStartAndTakeUserTaskBO 发起流程，请求参数对象
     * @return 返回的流程实例，流程定义等参数
     */
    @OperationLog(module = "工作流查询", value = "发起流程", type = OperationTypeEnum.OTHER)
    @Operation(summary = "发起流程")
    @PostMapping("/startAndTakeUserTask")
    public CommonResult<FlowStartAndTakeUserTaskVO> startAndTakeUserTask(@Validated @RequestBody FlowStartAndTakeUserTaskBO flowStartAndTakeUserTaskBO) {
        FlowRespDTO flowRespDTO = flowableApi.startAndTakeUserTask(BeanUtils.copyProperties(flowStartAndTakeUserTaskBO, FlowReqDTO.class));
        FlowStartAndTakeUserTaskVO flowStartAndTakeUserTaskVO = BeanUtils.copyProperties(flowRespDTO, FlowStartAndTakeUserTaskVO.class);
        return CommonResult.data(flowStartAndTakeUserTaskVO);
    }

    /**
     * 审批用户任务
     *
     * @param flowSubmitUserTaskBO 审批用户任务，请求参数对象
     */
    @OperationLog(module = "工作流查询", value = "审批用户任务", type = OperationTypeEnum.OTHER)
    @Operation(summary = "审批用户任务")
    @PostMapping("/submitUserTask")
    public CommonResult submitUserTask(@Validated @RequestBody FlowSubmitUserTaskBO flowSubmitUserTaskBO) {
        flowableApi.submitUserTask(BeanUtils.copyProperties(flowSubmitUserTaskBO, FlowSubmitUserReqDTO.class));
        return CommonResult.success();
    }

    /**
     * 根据指定流程的主版本，发起一个流程实例
     *
     * @param processDefinitionKey 流程标识
     */
    @OperationLog(module = "工作流查询", value = "根据指定流程的主版本，发起一个流程实例", type = OperationTypeEnum.OTHER)
    @Operation(summary = "根据指定流程的主版本，发起一个流程实例")
    @Parameters({
        @Parameter(name = "processDefinitionKey", description = "流程标识", required = true, example = "11111"),
        @Parameter(name = "deptOfSubmitUser", description = "申请人所属部门", required = true, example = "11111")
    })
    @PostMapping("/startOnly")
    public CommonResult<Void> startOnly(@RequestParam String processDefinitionKey, @RequestParam String deptOfSubmitUser) {
        FlowEntryPublish flowEntryPublish = null;
        try {
            // 1. 验证流程数据的合法性。
            FlowEntry flowEntry = flowOperationHelper.verifyAndGetFlowEntry(processDefinitionKey);
            // 2. 验证流程一个用户任务的合法性。
            flowEntryPublish = flowEntry.getMainFlowEntryPublish();
            TaskInfoVO taskInfo = flowOperationHelper.verifyAndGetInitialTaskInfo(flowEntryPublish, false);
        } catch (Exception e) {
            return CommonResult.fail(e.getMessage());
        }

        flowApiService.start(flowEntryPublish.getProcessDefinitionId(), null, deptOfSubmitUser);
        return CommonResult.success();
    }

    /**
     * 获取开始节点之后的第一个任务节点的数据
     *
     * @param processDefinitionKey 流程标识
     * @return 任务节点的自定义对象数据
     */
    @OperationLog(module = "工作流查询", value = "获取开始节点之后的第一个任务节点的数据", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "获取开始节点之后的第一个任务节点的数据")
    @Parameter(name = "processDefinitionKey", description = "流程标识", required = true, example = "1111")
    @GetMapping("/viewInitialTaskInfo")
    public CommonResult<TaskInfoVO> viewInitialTaskInfo(@RequestParam String processDefinitionKey) {
        FlowEntryPublish flowEntryPublish = null;
        try {
            // 1. 验证流程数据的合法性。
            FlowEntry flowEntry = flowOperationHelper.verifyAndGetFlowEntry(processDefinitionKey);
            // 2. 验证流程一个用户任务的合法性。
            flowEntryPublish = flowEntry.getMainFlowEntryPublish();
            String initTaskInfo = flowEntryPublish.getInitTaskInfo();
            TaskInfoVO taskInfo = StrUtil.isBlank(initTaskInfo) ? null : JSON.parseObject(initTaskInfo, TaskInfoVO.class);
            if (taskInfo != null) {
                taskInfo.setAssignedMe(StrUtil.equalsAny(taskInfo.getAssignee(), SecurityUtils.getLoginUser().getUserId(), FlowConstant.START_USER_NAME_VAR));
            }
            taskInfo.setProcessDefinitionName(flowEntry.getProcessDefinitionName());
            taskInfo.setPublishVersion(flowEntryPublish.getPublishVersion());
            return CommonResult.data(taskInfo);
        } catch (Exception e) {
            return CommonResult.fail(e.getMessage());
        }
    }

    /**
     * 获取流程运行时指定任务的信息
     *
     * @param processDefinitionId 流程引擎的定义Id
     * @param processInstanceId   流程引擎的实例Id
     * @param taskId              流程引擎的任务Id
     * @return 任务节点的自定义对象数据
     */
    @OperationLog(module = "工作流查询", value = "获取流程运行时指定任务的信息", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "获取流程运行时指定任务的信息")
    @Parameters({
        @Parameter(name = "processDefinitionId", description = "流程引擎的定义Id", required = true, example = "1111"),
        @Parameter(name = "processInstanceId", description = "流程引擎的实例Id", required = true, example = "1111"),
        @Parameter(name = "taskId", description = "流程引擎的任务Id", required = true, example = "1111")
    })
    @GetMapping("/viewRuntimeTaskInfo")
    public CommonResult<TaskInfoVO> viewRuntimeTaskInfo(@RequestParam String processDefinitionId, @RequestParam String processInstanceId, @RequestParam String taskId) {
        Task task = flowApiService.getProcessInstanceActiveTask(processInstanceId, taskId);
        try {
            TaskInfoVO taskInfoVO = flowOperationHelper.verifyAndGetRuntimeTaskInfo(task);
            FlowTaskExt flowTaskExt = flowTaskExtService.getByProcessDefinitionIdAndTaskId(processDefinitionId, taskInfoVO.getTaskKey());
            if (flowTaskExt != null) {
                if (StrUtil.isNotBlank(flowTaskExt.getOperationListJson())) {
                    taskInfoVO.setOperationList(JSON.parseArray(flowTaskExt.getOperationListJson(), JSONObject.class));
                }
                if (StrUtil.isNotBlank(flowTaskExt.getVariableListJson())) {
                    taskInfoVO.setVariableList(JSON.parseArray(flowTaskExt.getVariableListJson(), JSONObject.class));
                }
            }
            FlowEntryDTO flowEntryDto = flowEntryService.getFlowEntryByProcessDefinitionId(task.getProcessDefinitionId());
            taskInfoVO.setProcessDefinitionName(flowEntryDto.getProcessDefinitionName());
            taskInfoVO.setPublishVersion(flowEntryDto.getPublishVersion());
            taskInfoVO.setRouterName(flowEntryDto.getDefaultRouterName());
            taskInfoVO.setTaskName(task.getName());
            taskInfoVO.setTaskKey(task.getTaskDefinitionKey());
            return CommonResult.data(taskInfoVO);
        } catch (Exception e) {
            return CommonResult.fail(e.getMessage());
        }
    }

    /**
     * 获取流程运行时指定任务的信息
     *
     * @param processDefinitionId 流程引擎的定义Id
     * @param processInstanceId   流程引擎的实例Id
     * @param taskId              流程引擎的任务Id
     * @return 任务节点的自定义对象数据
     */
    @OperationLog(module = "工作流查询", value = "获取流程运行时指定任务的信息", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "获取流程运行时指定任务的信息")
    @Parameters({
        @Parameter(name = "processDefinitionId", description = "流程引擎的定义Id", required = true, example = "1111"),
        @Parameter(name = "processInstanceId", description = "流程引擎的实例Id", required = true, example = "1111"),
        @Parameter(name = "taskId", description = "流程引擎的任务Id", required = true, example = "1111")
    })
    @GetMapping("/viewHistoricTaskInfo")
    public CommonResult<TaskInfoVO> viewHistoricTaskInfo(
        @RequestParam String processDefinitionId,
        @RequestParam String processInstanceId,
        @RequestParam String taskId) {
        HistoricTaskInstance taskInstance = flowApiService.getHistoricTaskInstance(processInstanceId, taskId);
        String userId = SecurityUtils.getLoginUser().getUserId();
        if (!StrUtil.equals(taskInstance.getAssignee(), userId)) {
            return CommonResult.fail("数据验证失败，当前用户不是指派人！");
        }
        TaskInfoVO taskInfoVO = JSON.parseObject(taskInstance.getFormKey(), TaskInfoVO.class);
        FlowTaskExt flowTaskExt = flowTaskExtService.getByProcessDefinitionIdAndTaskId(processDefinitionId, taskInstance.getTaskDefinitionKey());
        if (flowTaskExt != null) {
            if (StrUtil.isNotBlank(flowTaskExt.getOperationListJson())) {
                taskInfoVO.setOperationList(JSON.parseArray(flowTaskExt.getOperationListJson(), JSONObject.class));
            }
            if (StrUtil.isNotBlank(flowTaskExt.getVariableListJson())) {
                taskInfoVO.setVariableList(JSON.parseArray(flowTaskExt.getVariableListJson(), JSONObject.class));
            }
        }
        FlowEntryDTO flowEntryDto = flowEntryService.getFlowEntryByProcessDefinitionId(processDefinitionId);
        taskInfoVO.setProcessDefinitionName(flowEntryDto.getProcessDefinitionName());
        taskInfoVO.setPublishVersion(flowEntryDto.getPublishVersion());
        taskInfoVO.setTaskKey(taskInstance.getTaskDefinitionKey());
        taskInfoVO.setProcessDefinitionKey(flowEntryDto.getProcessDefinitionKey());
        taskInfoVO.setRouterName(flowEntryDto.getDefaultRouterName());
        return CommonResult.data(taskInfoVO);
    }

    /**
     * 获取第一个提交表单数据的任务信息
     *
     * @param processInstanceId 流程实例Id
     * @return 任务节点的自定义对象数据
     */
    @OperationLog(module = "工作流查询", value = "获取第一个提交表单数据的任务信息", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "获取流程运行时指定任务的信息")
    @Parameter(name = "processInstanceId", description = "流程实例Id", required = true, example = "1111")
    @GetMapping("/viewInitialHistoricTaskInfo")
    public CommonResult<TaskInfoVO> viewInitialHistoricTaskInfo(@RequestParam String processInstanceId) {
        List<FlowTaskCommentVO> taskCommentList = flowTaskCommentService.getFlowTaskCommentList(processInstanceId);
        if (CollUtil.isEmpty(taskCommentList)) {
            return CommonResult.fail("数据不存在，请刷新后重试");
        }
        FlowTaskCommentVO taskComment = taskCommentList.get(0);
        HistoricTaskInstance task = flowApiService.getHistoricTaskInstance(processInstanceId, taskComment.getTaskId());
        if (StrUtil.isBlank(task.getFormKey())) {
            return CommonResult.fail("数据验证失败，指定任务的formKey属性不存在，请重新修改流程图！");
        }
        TaskInfoVO taskInfo = JSON.parseObject(task.getFormKey(), TaskInfoVO.class);
        taskInfo.setTaskKey(task.getTaskDefinitionKey());
        FlowEntryDTO flowEntryDto = flowEntryService.getFlowEntryByProcessDefinitionId(task.getProcessDefinitionId());
        taskInfo.setProcessDefinitionName(flowEntryDto.getProcessDefinitionName());
        taskInfo.setPublishVersion(flowEntryDto.getPublishVersion());
        return CommonResult.data(taskInfo);
    }

    /**
     * 获取任务的审批用户信息列表
     *
     * @param processDefinitionId 流程定义Id
     * @param processInstanceId   流程实例Id
     * @param taskId              流程任务Id
     * @param historic            是否为历史任务
     * @return 任务相关的用户信息列表
     */
    @OperationLog(module = "工作流查询", value = "获取任务的用户信息列表", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "获取任务的用户信息列表")
    @Parameters({
        @Parameter(name = "processDefinitionId", description = "流程定义Id", required = true, example = "1111"),
        @Parameter(name = "processInstanceId", description = "流程实例Id", required = true, example = "1111"),
        @Parameter(name = "taskId", description = "流程任务Id", required = true, example = "1111"),
        @Parameter(name = "historic", description = "是否为历史任务", required = true, example = "true")
    })
    @GetMapping("/viewTaskUserInfo")
    public CommonResult<List<FlowUserInfoVO>> viewTaskUserInfo(
        @RequestParam String processDefinitionId,
        @RequestParam String processInstanceId,
        @RequestParam String taskId,
        @RequestParam Boolean historic) {
        TaskInfo taskInfo;
        HistoricTaskInstance hisotricTask;
        if (BooleanUtil.isFalse(historic)) {
            taskInfo = flowApiService.getTaskById(taskId);
            if (ObjectUtil.isNull(taskInfo)) {
                hisotricTask = flowApiService.getHistoricTaskInstance(processInstanceId, taskId);
                taskInfo = hisotricTask;
                historic = true;
            }
        } else {
            hisotricTask = flowApiService.getHistoricTaskInstance(processInstanceId, taskId);
            taskInfo = hisotricTask;
        }
        if (ObjectUtil.isNull(taskInfo)) {
            return CommonResult.fail("数据验证失败，任务Id不存在！");
        }
        String taskKey = taskInfo.getTaskDefinitionKey();
        FlowTaskExt taskExt = flowTaskExtService.getByProcessDefinitionIdAndTaskId(processDefinitionId, taskKey);
        //判断该任务节点是否是多实例会签任务
        boolean isMultiInstanceTask = flowApiService.isMultiInstanceTask(taskInfo.getProcessDefinitionId(), taskKey);
        //查询该任务节点有那些用户可以进行审批
        List<FlowUserInfoVO> resultUserInfoList = flowTaskExtService.getCandidateUserInfoList(processInstanceId, taskExt, taskInfo, isMultiInstanceTask, historic);
        if (BooleanUtil.isTrue(historic) || isMultiInstanceTask) {
            //该节点是历史任务或者是多实例会签任务会执行如下封装
            List<FlowUserInfoVO> historicUserInfoList = new ArrayList<>();
            List<FlowTaskComment> taskCommentList = buildApprovedFlowTaskCommentList(taskInfo, isMultiInstanceTask);
            Map<String, FlowUserInfoVO> resultUserInfoMap = resultUserInfoList.stream().collect(Collectors.toMap(FlowUserInfoVO::getUserId, c -> c));
            for (FlowTaskComment taskComment : taskCommentList) {
                FlowUserInfoVO flowUserInfoVO = resultUserInfoMap.get(taskComment.getCreateUserId());
                if (flowUserInfoVO != null) {
                    flowUserInfoVO.setLastApprovalTime(taskComment.getCreateTime());
                    historicUserInfoList.add(flowUserInfoVO);
                }
            }
            //有些节点任务比如任务处理人指定的候选用户组，是多个人，但是审批的时候是其中一个人进行的审批，对于历史任务，用户只返回真正审批的人，而不是全部返回能进行审批的人信息，这边是做了过滤
            if (CollectionUtil.isNotEmpty(historicUserInfoList)) {
                resultUserInfoList = historicUserInfoList;
            }
        }
        return CommonResult.data(resultUserInfoList);
    }

    /**
     * 查看指定流程实例的草稿数据 查看指定流程实例的草稿数据。对于路由表单，不要调用该接口，可以调用业务自己写的详情接口
     *
     * @param workOrderId 工单表主键id
     * @return 流程实例的草稿数据。
     */
    @OperationLog(module = "工作流查询", value = "查看指定流程实例的草稿数据", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "查看指定流程实例的草稿数据")
    @Parameter(name = "workOrderId", description = "工单表主键id", required = true, example = "1111")
    @GetMapping("/viewDraftData")
    public CommonResult<JSONObject> viewDraftData(@RequestParam String workOrderId) {
        try {
            FlowWorkOrder flowWorkOrder = flowWorkOrderService.getById(workOrderId);
            AssertUtils.isTrue(flowWorkOrder == null, "未查询到工单对应的草稿数据！");
            FlowWorkOrderExt flowWorkOrderExt = flowWorkOrderService.getFlowWorkOrderExtByWorkOrderId(flowWorkOrder.getWorkOrderId());
            if (StrUtil.isBlank(flowWorkOrderExt.getDraftData())) {
                return CommonResult.data(null);
            }
            //获取草稿 JSONObject 数据
            JSONObject formData = JSON.parseObject(flowWorkOrderExt.getDraftData());
            //加载路由表单保存的表单草稿数据，项目侧也可以单独写一个加载业务详情接口来加载表单数据，不需要用该接口
            return CommonResult.data(formData);
        } catch (Exception e) {
            return CommonResult.fail(e.getMessage());
        }
    }

    /**
     * 根据消息Id，获取流程Id关联的业务数据。
     *
     * @param messageId 抄送消息Id
     * @param snapshot  是否获取抄送或传阅时任务的业务快照数据。如果为true，后续任务导致的业务数据修改，将不会返回给前端
     * @return 抄送消息关联的流程实例业务数据
     */
    @OperationLog(module = "工作流查询", value = "根据消息Id，获取流程Id关联的业务数据", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "根据消息Id，获取流程Id关联的业务数据")
    @Parameters({
        @Parameter(name = "messageId", description = "抄送消息Id", required = true, example = "1111"),
        @Parameter(name = "snapshot", description = "是否获取抄送或传阅时任务的业务快照数据", required = true, example = "true")
    })
    @GetMapping("/viewCopyBusinessData")
    public CommonResult<JSONObject> viewCopyBusinessData(@RequestParam String messageId, @RequestParam(required = false) Boolean snapshot) {
        // 验证流程任务的合法性。
        FlowMessage flowMessage = flowMessageService.getById(messageId);
        if (ObjectUtil.isNull(flowMessage)) {
            return CommonResult.fail(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        if (ObjectUtil.notEqual(flowMessage.getMessageType(), FlowMessageType.COPY_TYPE)) {
            return CommonResult.fail("数据验证失败，当前消息不是抄送类型消息！");
        }
        if (ObjectUtil.isNull(flowMessage.getOnlineFormData()) || flowMessage.getOnlineFormData()) {
            return CommonResult.fail("数据验证失败，当前消息为动态表单数据，不能通过该接口获取！");
        }
        if (!flowMessageService.isCandidateIdentityOnMessage(messageId)) {
            return CommonResult.fail("数据验证失败，当前用户没有权限访问该消息！");
        }
        JSONObject businessObject = null;
        if (ObjectUtil.isNotNull(snapshot) && snapshot) {
            if (StrUtil.isNotBlank(flowMessage.getBusinessDataShot())) {
                businessObject = JSON.parseObject(flowMessage.getBusinessDataShot());
            }
            return CommonResult.data(businessObject);
        }
        HistoricProcessInstance instance = flowApiService.getHistoricProcessInstance(flowMessage.getProcessInstanceId());
        // 如果业务主数据为空，则直接返回。
        if (StrUtil.isBlank(instance.getBusinessKey())) {
            return CommonResult.fail("数据验证失败，当前消息为所属流程实例没有包含业务主键Id！");
        }
        //注释起来的这块是加载业务表单数据的接口，业务侧该调用业务详情接口
        String businessData = flowCustomExtFactory.getBusinessDataExtHelper().getBusinessData(
            flowMessage.getProcessDefinitionKey(), flowMessage.getProcessInstanceId(), instance.getBusinessKey());
        if (StrUtil.isNotBlank(businessData)) {
            businessObject = JSON.parseObject(businessData);
        }
        // 将当前消息更新为已读
        flowMessageService.readCopyTask(messageId);
        return CommonResult.data(businessObject);
    }

    /**
     * 获取多实例会签任务的指派人列表
     *
     * @param processInstanceId 流程实例Id
     * @param taskId            多实例任务的上一级任务Id
     * @return 应答结果，指定会签任务的指派人列表
     */
    @OperationLog(module = "工作流查询", value = "获取多实例会签任务的指派人列表", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "获取多实例会签任务的指派人列表")
    @Parameters({
        @Parameter(name = "processInstanceId", description = "流程实例Id", required = true, example = "1111"),
        @Parameter(name = "taskId", description = "多实例任务的上一级任务Id", required = true, example = "1111")
    })
    @GetMapping("/listMultiSignAssignees")
    public CommonResult<List<JSONObject>> listMultiSignAssignees(@RequestParam String processInstanceId, @RequestParam String taskId) {
        try {
            JSONObject jsonObject = flowOperationHelper.doVerifyMultiSign(processInstanceId, taskId);
            Task activeMultiInstanceTask = jsonObject.getObject(ACTIVE_MULTI_INST_TASK, Task.class);
            String multiInstanceExecId = flowApiService.getExecutionVariableStringWithSafe(activeMultiInstanceTask.getExecutionId(), MULTI_SIGN_TASK_EXECUTION_ID_VAR);
            FlowMultiInstanceTrans trans = flowMultiInstanceTransService.getWithAssigneeListByMultiInstanceExecId(multiInstanceExecId);
            List<FlowTaskComment> commentList = flowTaskCommentService.getFlowTaskCommentListByMultiInstanceExecId(multiInstanceExecId);
            List<String> assigneeList = StrUtil.split(trans.getAssigneeList(), ",");
            Set<String> approvedAssigneeSet = commentList.stream().map(FlowTaskComment::getCreateUserId).collect(Collectors.toSet());
            List<JSONObject> resultList = new LinkedList<>();
            List<SysUserRespDTO> userListByIds = sysUserApi.getUserListByIds(assigneeList);
            Map<String, String> usernameMap = CollectionUtils.convertMap(userListByIds, SysUserRespDTO::getUserId, SysUserRespDTO::getRealName);
            for (String assignee : assigneeList) {
                JSONObject resultData = new JSONObject();
                resultData.put("assignee", assignee);
                resultData.put(SHOW_NAME, usernameMap.get(assignee));
                //approved为true表示该账号已经审批过了，减签无法选择该用户，前端会控制
                resultData.put("approved", approvedAssigneeSet.contains(assignee));
                resultList.add(resultData);
            }
            return CommonResult.data(resultList);
        } catch (Exception e) {
            return CommonResult.fail(e.getMessage());
        }
    }

    /**
     * 提交多实例加签或减签
     *
     * @param submitConsignBO 加签减签请求参数对象
     */
    @OperationLog(module = "工作流查询", value = "提交多实例加签或减签", type = OperationTypeEnum.UPDATE)
    @Operation(summary = "提交多实例加签或减签")
    @PostMapping("/submitConsign")
    public CommonResult<Void> submitConsign(@RequestBody @Validated SubmitConsignBO submitConsignBO) {
        try {
            JSONObject verifyResult = flowOperationHelper.doVerifyMultiSign(submitConsignBO.getProcessInstanceId(), submitConsignBO.getTaskId());
            HistoricTaskInstance taskInstance = verifyResult.getObject("taskInstance", HistoricTaskInstance.class);
            Task activeMultiInstanceTask = verifyResult.getObject(ACTIVE_MULTI_INST_TASK, Task.class);
            String multiInstanceExecId = flowApiService.getExecutionVariableStringWithSafe(activeMultiInstanceTask.getExecutionId(), MULTI_SIGN_TASK_EXECUTION_ID_VAR);
            JSONArray newAssignees = submitConsignBO.getNewAssignees();
            Boolean isAdd = submitConsignBO.getIsAdd();
            if (ObjectUtil.isNull(isAdd)) {
                isAdd = true;
            }
            if (BooleanUtil.isFalse(isAdd)) {
                List<FlowTaskComment> commentList = flowTaskCommentService.getFlowTaskCommentListByMultiInstanceExecId(multiInstanceExecId);
                if (CollUtil.isNotEmpty(commentList)) {
                    Set<String> approvedAssigneeSet = commentList.stream().map(FlowTaskComment::getCreateUserId).collect(Collectors.toSet());
                    String loginName = this.findExistAssignee(approvedAssigneeSet, newAssignees);
                    if (!StrUtil.isEmpty(loginName)) {
                        return CommonResult.fail("数据验证失败，用户 [" + loginName + "] 已经审批，不能减签该用户！");
                    }
                }
            } else {
                // 避免同一人被重复加签。
                FlowMultiInstanceTrans trans = flowMultiInstanceTransService.getWithAssigneeListByMultiInstanceExecId(multiInstanceExecId);
                Set<String> assigneeSet = new HashSet<>(StrUtil.split(trans.getAssigneeList(), ","));
                String loginName = this.findExistAssignee(assigneeSet, newAssignees);
                if (!StrUtil.isEmpty(loginName)) {
                    return CommonResult.fail("数据验证失败，用户 [" + loginName + "] 已经是会签人，不能重复指定！");
                }
            }
            flowApiService.submitConsign(taskInstance, activeMultiInstanceTask, newAssignees, isAdd, submitConsignBO.getDeptOfApproveUser());
            return CommonResult.success();
        } catch (Exception e) {
            return CommonResult.fail(e.getMessage());
        }
    }

    /**
     * 返回当前用户待办的任务列表
     *
     * @param flowTaskPageBO 分页对象
     * @return 返回当前用户待办的任务列表。如果指定流程标识，则仅返回该流程的待办任务列表
     */
    @OperationLog(module = "工作流查询", value = "返回当前用户待办的任务列表", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "返回当前用户待办的任务列表")
    @PostMapping("/listRuntimeTask")
    public CommonResult<CommonPageVO<FlowTaskVO>> listRuntimeTask(@RequestBody FlowTaskPageBO flowTaskPageBO) {
        return CommonResult.data(flowApiService.getTaskListByUserName(flowTaskPageBO));
    }


    /**
     * 挂起或者恢复超时任务
     *
     * @param processInstanceId 流程实例Id
     * @param taskId            任务Id
     * @param taskStatus        任务状态
     * @return 挂起或者恢复超时任务的结果
     */
    @OperationLog(module = "工作流查询", value = "挂起或者恢复超时任务", type = OperationTypeEnum.UPDATE)
    @Operation(summary = "挂起或者恢复超时任务")
    @Parameters({
        @Parameter(name = "processInstanceId", description = "流程实例Id", required = true, example = "1111"),
        @Parameter(name = "taskId", description = "任务实例Id", required = true, example = "1111"),
        @Parameter(name = "taskStatus", description = "任务状态", required = true, example = "1")
    })
    @GetMapping("/hangUpOrRecoverTaskJob")
    public CommonResult<Boolean> hangUpOrRecoverTaskJob(@RequestParam String processInstanceId, @RequestParam String taskId, @RequestParam Integer taskStatus) {
        Boolean updateResult = flowTaskTimeoutJobService.hangUpOrRecoverTaskJob(processInstanceId, taskId, taskStatus);
        return CommonResult.data(updateResult);
    }

    /**
     * 返回当前用户待办的任务数量
     *
     * @return 返回当前用户待办的任务数量
     */
    @OperationLog(module = "工作流查询", value = "返回当前用户待办的任务数量", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "返回当前用户待办的任务数量")
    @PostMapping("/countRuntimeTask")
    public CommonResult<Long> countRuntimeTask() {
        long totalCount = flowApiService.getTaskCountByUserName(SecurityUtils.getLoginUserId());
        return CommonResult.data(totalCount);
    }

    /**
     * 获取指定任务的可驳回的用户任务列表
     *
     * @param processInstanceId 流程实例Id
     * @param taskId            待办任务Id
     * @return 指定任务的可回退用户任务列表
     */
    @OperationLog(module = "工作流查询", value = "获取指定任务的可回退用户任务列表", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "获取指定任务的可回退用户任务列表")
    @Parameters({
        @Parameter(name = "processInstanceId", description = "流程实例Id", required = true, example = "11111"),
        @Parameter(name = "taskId", description = "待办任务Id", required = true, example = "11111")
    })
    @GetMapping("/listRejectCandidateUserTask")
    public CommonResult<List<FlowTaskVO>> listRejectCandidateUserTask(@RequestParam String processInstanceId, @RequestParam String taskId) {
        Task task = flowApiService.getProcessInstanceActiveTask(processInstanceId, taskId);
        if (ObjectUtil.isNull(task)) {
            return CommonResult.fail("数据验证失败，指定的任务Id不存在，请刷新后重试！");
        }
        List<UserTask> userTaskList = flowApiService.getRejectCandidateUserTaskList(task);
        List<FlowTaskVO> resultList = new LinkedList<>();
        if (CollUtil.isNotEmpty(userTaskList)) {
            for (UserTask userTask : userTaskList) {
                FlowTaskVO flowTaskVO = new FlowTaskVO();
                flowTaskVO.setTaskKey(userTask.getId());
                flowTaskVO.setShowName(userTask.getName());
                resultList.add(flowTaskVO);
            }
        }
        return CommonResult.data(resultList);
    }

    /**
     * 自由跳转接口
     *
     * @param processInstanceId 流程实例Id
     * @param sourceTaskId      当前待办任务Id
     * @param targetTaskKey     跳转目标任务的定义标识
     * @param taskComment       跳转注释说明
     * @param delegateAssignee  指派人(多人之间逗号分割)
     * @return 跳转应答结果
     */
    @OperationLog(module = "工作流查询", value = "自由跳转接口", type = OperationTypeEnum.OTHER)
    @Operation(summary = "自由跳转接口")
    @Parameters({
        @Parameter(name = "processInstanceId", description = "流程实例Id", required = true, example = "11111"),
        @Parameter(name = "sourceTaskId", description = "当前待办任务Id", required = true, example = "11111"),
        @Parameter(name = "targetTaskKey", description = "跳转目标任务的定义标识", required = true, example = "11111"),
        @Parameter(name = "taskComment", description = "跳转注释说明", required = true, example = "11111"),
        @Parameter(name = "delegateAssignee", description = "指派人(多人之间逗号分割)", required = false, example = "11111"),
        @Parameter(name = "deptOfApproveUser", description = "审批人所属部门", required = true, example = "11111")
    })
    @PostMapping("/freeJumpTo")
    public CommonResult<Void> freeJumpTo(
        @RequestParam String processInstanceId,
        @RequestParam String sourceTaskId,
        @RequestParam String targetTaskKey,
        @RequestParam String taskComment,
        @RequestParam String deptOfApproveUser,
        @RequestParam(required = false) String delegateAssignee) {
        try {
            Task taskResult = flowOperationHelper.verifySubmitAndGetTask(processInstanceId, sourceTaskId, null);
            flowApiService.freeJumpTo(taskResult, targetTaskKey, taskComment, delegateAssignee, deptOfApproveUser);
            return CommonResult.success();
        } catch (Exception e) {
            return CommonResult.fail(e.getMessage());
        }
    }

    /**
     * 主动驳回当前的待办任务到起点，只用当前待办任务的指派人或者候选者才能完成该操作
     *
     * @param rejectRuntimeTaskBO 驳回到起点入参BO对象
     * @return 操作应答结果
     */
    @OperationLog(module = "工作流查询", value = "主动驳回当前的待办任务到开始节点", type = OperationTypeEnum.OTHER)
    @Operation(summary = "主动驳回当前的待办任务到开始节点")
    @PostMapping("/rejectToStartUserTask")
    public CommonResult<Void> rejectToStartUserTask(@RequestBody @Validated RejectRuntimeTaskBO rejectRuntimeTaskBO) {
        Task taskResult = flowOperationHelper.verifySubmitAndGetTask(rejectRuntimeTaskBO.getProcessInstanceId(), rejectRuntimeTaskBO.getTaskId(), null);
        FlowTaskComment firstTaskComment = flowTaskCommentService.getFirstFlowTaskComment(rejectRuntimeTaskBO.getProcessInstanceId());
        flowApiService.backToRuntimeTask(taskResult, firstTaskComment.getTaskKey(), FlowBackType.REJECT, rejectRuntimeTaskBO.getTaskComment(), rejectRuntimeTaskBO.getTaskVariableData(), rejectRuntimeTaskBO.getDeptOfApproveUser());
        return CommonResult.success();
    }

    /**
     * 驳回，驳回到历史任务接口：主动驳回当前的待办任务，只用当前待办任务的指派人或者候选者才能完成该操作
     *
     * @param rejectRuntimeTaskBO 主动驳回当前的待办任务BO对象
     */
    @OperationLog(module = "工作流查询", value = "主动驳回当前的待办任务", type = OperationTypeEnum.OTHER)
    @Operation(summary = "主动驳回当前的待办任务")
    @PostMapping("/rejectRuntimeTask")
    public CommonResult<Void> rejectRuntimeTask(@RequestBody @Validated RejectRuntimeTaskBO rejectRuntimeTaskBO) {
        Task taskResult = flowOperationHelper.verifySubmitAndGetTask(rejectRuntimeTaskBO.getProcessInstanceId(), rejectRuntimeTaskBO.getTaskId(), null);
        flowApiService.backToRuntimeTask(taskResult, rejectRuntimeTaskBO.getTargetTaskKey(), FlowBackType.REJECT,
            rejectRuntimeTaskBO.getTaskComment(), rejectRuntimeTaskBO.getTaskVariableData(), rejectRuntimeTaskBO.getDeptOfApproveUser());
        return CommonResult.success();
    }

    /**
     * 撤销操作，撤回当前用户提交的，但是尚未被审批的待办任务。只有已办任务的指派人才能完成该操作
     *
     * @param rejectRuntimeTaskBO 撤销操作入参BO对象
     * @return 操作应答结果
     */
    @OperationLog(module = "工作流查询", value = "撤回当前用户提交的", type = OperationTypeEnum.OTHER)
    @Operation(summary = "撤回当前用户提交的")
    @PostMapping("/revokeHistoricTask")
    public CommonResult<Void> reVOkeHistoricTask(@RequestBody @Validated RejectRuntimeTaskBO rejectRuntimeTaskBO) {
        if (!flowApiService.existActiveProcessInstance(rejectRuntimeTaskBO.getProcessInstanceId())) {
            return CommonResult.fail("数据验证失败，当前流程实例已经结束，不能执行撤回！");
        }
        HistoricTaskInstance taskInstance = flowApiService.getHistoricTaskInstance(rejectRuntimeTaskBO.getProcessInstanceId(), rejectRuntimeTaskBO.getTaskId());
        if (taskInstance == null) {
            return CommonResult.fail("数据验证失败，当前任务不存在！");
        }
        if (!StrUtil.equals(taskInstance.getAssignee(), SecurityUtils.getLoginUserId())) {
            return CommonResult.fail("数据验证失败，任务指派人与当前用户不匹配！");
        }
        FlowTaskComment latestComment = flowTaskCommentService.getLatestFlowTaskComment(rejectRuntimeTaskBO.getProcessInstanceId());
        if (latestComment == null) {
            return CommonResult.fail("数据验证失败，当前实例没有任何审批提交记录！");
        }
        if (!latestComment.getTaskId().equals(rejectRuntimeTaskBO.getTaskId())) {
            return CommonResult.fail("数据验证失败，当前审批任务已被办理，不能撤回！");
        }
        List<Task> activeTaskList = flowApiService.getProcessInstanceActiveTaskList(rejectRuntimeTaskBO.getProcessInstanceId());
        if (CollUtil.isEmpty(activeTaskList)) {
            return CommonResult.fail("数据验证失败，当前流程没有任何待办任务！");
        }
        if (latestComment.getApprovalType().equals(FlowApprovalType.TRANSFER)) {
            if (activeTaskList.size() > 1) {
                return CommonResult.fail("数据验证失败，转办任务数量不能多于1个！");
            }
            // 如果是转办任务，无需节点跳转，将指派人改为当前用户即可。
            Task task = activeTaskList.get(0);
            task.setAssignee(SecurityUtils.getLoginUserName());
            return CommonResult.success();
        }
        List<String> activitiIds = activeTaskList.stream().map(Task::getTaskDefinitionKey).collect(Collectors.toList());
        List<String> childActivitiIds = flowApiService.getChildActivitiIdList(taskInstance.getProcessDefinitionId(), taskInstance.getTaskDefinitionKey(), activitiIds);
        if (CollUtil.isEmpty(childActivitiIds)) {
            return CommonResult.fail("数据验证失败，当前任务的待撤销任务并不存在！");
        }
        Task reVOkedTask = activeTaskList.stream().filter(t -> childActivitiIds.contains(t.getTaskDefinitionKey())).findFirst().orElse(null);
        flowApiService.backToRuntimeTask(reVOkedTask, null, FlowBackType.REVOKE, rejectRuntimeTaskBO.getTaskComment(), rejectRuntimeTaskBO.getTaskVariableData(), rejectRuntimeTaskBO.getDeptOfApproveUser());
        return CommonResult.success();
    }

    /**
     * 获取当前流程任务的审批列表
     *
     * @param processInstanceId 当前运行时的流程实例Id
     * @return 当前流程实例的详情数据
     */
    @OperationLog(module = "工作流查询", value = "获取当前流程任务的审批列表", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "获取当前流程任务的审批列表")
    @Parameter(name = "processInstanceId", description = "当前运行时的流程实例Id", required = true, example = "1111")
    @GetMapping("/listFlowTaskComment")
    public CommonResult<List<FlowTaskCommentVO>> listFlowTaskComment(@RequestParam String processInstanceId) {
        List<FlowTaskCommentVO> flowTaskCommentList = flowTaskCommentService.getFlowTaskCommentList(processInstanceId);
        return CommonResult.data(flowTaskCommentList);
    }

    /**
     * 获取指定流程定义的流程图。
     *
     * @param processDefinitionId 流程定义Id
     * @return 流程图
     */
    @OperationLog(module = "工作流查询", value = "获取指定流程定义的流程图", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "获取指定流程定义的流程图")
    @Parameter(name = "processDefinitionId", description = "流程定义Id", required = true, example = "1111")
    @GetMapping("/viewProcessBpmn")
    public CommonResult<String> viewProcessBpmn(@RequestParam String processDefinitionId) throws IOException {
        BpmnXMLConverter converter = new BpmnXMLConverter();
        BpmnModel bpmnModel = flowApiService.getBpmnModelByDefinitionId(processDefinitionId);
        byte[] xmlBytes = converter.convertToXML(bpmnModel);
        InputStream in = new ByteArrayInputStream(xmlBytes);
        return CommonResult.data(StreamUtils.copyToString(in, StandardCharsets.UTF_8));
    }

    /**
     * 获取流程图高亮数据
     *
     * @param processInstanceId 流程实例Id
     * @return 流程图高亮数据
     */
    @OperationLog(module = "工作流查询", value = "获取流程图高亮数据", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "获取流程图高亮数据")
    @Parameter(name = "processInstanceId", description = "流程实例Id", required = true, example = "1111")
    @GetMapping("/viewHighlightFlowData")
    public CommonResult<JSONObject> viewHighlightFlowData(@RequestParam String processInstanceId) {
        List<HistoricActivityInstance> activityInstanceList = flowApiService.getHistoricActivityInstanceList(processInstanceId);
        Set<String> finishedTaskSet = activityInstanceList.stream()
            .filter(s -> !StrUtil.equals(s.getActivityType(), "sequenceFlow"))
            .map(HistoricActivityInstance::getActivityId).collect(Collectors.toSet());
        Set<String> finishedSequenceFlowSet = activityInstanceList.stream()
            .filter(s -> StrUtil.equals(s.getActivityType(), "sequenceFlow"))
            .map(HistoricActivityInstance::getActivityId).collect(Collectors.toSet());
        //获取流程实例当前正在待办的节点
        List<HistoricActivityInstance> unfinishedInstanceList = flowApiService.getHistoricUnfinishedInstanceList(processInstanceId);
        Set<String> unfinishedTaskSet = new LinkedHashSet<>();
        for (HistoricActivityInstance unfinishedActivity : unfinishedInstanceList) {
            unfinishedTaskSet.add(unfinishedActivity.getActivityId());
        }
        JSONObject jsonData = new JSONObject();
        jsonData.put("finishedTaskSet", finishedTaskSet);
        jsonData.put("finishedSequenceFlowSet", finishedSequenceFlowSet);
        jsonData.put("unfinishedTaskSet", unfinishedTaskSet);
        //封装当前版本流程的所有节点信息,用来移动端自由跳功能下拉使用
        List<FlowElement> allUserTask = flowApiService.getProcessAllElements(activityInstanceList.get(0).getProcessDefinitionId())
            .stream().filter(element -> element instanceof UserTask).collect(Collectors.toList());
        List<ActivityTaskVO> activityTaskVOS = allUserTask.stream().map(userTask -> {
            ActivityTaskVO activityTaskVO = new ActivityTaskVO();
            activityTaskVO.setActivityId(userTask.getId());
            activityTaskVO.setActivityName(userTask.getName());
            return activityTaskVO;
        }).collect(Collectors.toList());
        jsonData.put("activityTask", activityTaskVOS);
        return CommonResult.data(jsonData);
    }

    /**
     * 获取当前用户的已办任务列表
     *
     * @param flowTaskPageBO 分页对象
     * @return 查询结果应答
     */
    @OperationLog(module = "工作流查询", value = "获取当前用户的已办理的审批任务列表", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "获取当前用户的已办理的审批任务列表")
    @PostMapping("/listHistoricTask")
    public CommonResult<CommonPageVO<Map<String, Object>>> listHistoricTask(@RequestBody FlowTaskPageBO flowTaskPageBO) throws ParseException {
        CommonPageVO<HistoricTaskInstance> pageData = flowApiService.getHistoricTaskInstanceFinishedList(flowTaskPageBO);
        List<Map<String, Object>> resultList = new LinkedList<>();
        pageData.getRecords().forEach(instance -> resultList.add(BeanUtil.beanToMap(instance)));
        List<HistoricTaskInstance> taskInstanceList = pageData.getRecords();
        if (CollUtil.isNotEmpty(taskInstanceList)) {
            Set<String> instanceIdSet = taskInstanceList.stream().map(HistoricTaskInstance::getProcessInstanceId).collect(Collectors.toSet());
            List<HistoricProcessInstance> instanceList = flowApiService.getHistoricProcessInstanceList(instanceIdSet);
            Set<String> userIdSet = instanceList.stream().map(HistoricProcessInstance::getStartUserId).collect(Collectors.toSet());
            Map<String, String> userInfoMap = CollectionUtils.convertMap(sysUserApi.getUserListByIds(userIdSet), SysUserRespDTO::getUserId, SysUserRespDTO::getRealName);
            Map<String, String> userNameMap = CollectionUtils.convertMap(sysUserApi.getUserListByIds(userIdSet), SysUserRespDTO::getUserId, SysUserRespDTO::getUsername);
            Map<String, HistoricProcessInstance> instanceMap = instanceList.stream().collect(Collectors.toMap(HistoricProcessInstance::getId, c -> c));
            resultList.forEach(result -> {
                HistoricProcessInstance instance = instanceMap.get(result.get("processInstanceId").toString());
                result.put("processDefinitionKey", instance.getProcessDefinitionKey());
                result.put("processDefinitionName", instance.getProcessDefinitionName());
                result.put("startUser", userNameMap.get(instance.getStartUserId()));
                result.put(SHOW_NAME, userInfoMap.get(instance.getStartUserId()));
                result.put("businessKey", instance.getBusinessKey());
            });
            Set<String> taskIdSet = taskInstanceList.stream().map(HistoricTaskInstance::getId).collect(Collectors.toSet());
            List<FlowTaskComment> commentList = flowTaskCommentService.getFlowTaskCommentListByTaskIds(taskIdSet);
            Map<String, List<FlowTaskComment>> commentMap = commentList.stream().collect(Collectors.groupingBy(FlowTaskComment::getTaskId));
            resultList.forEach(result -> {
                List<FlowTaskComment> comments = commentMap.get(result.get("id").toString());
                if (CollUtil.isNotEmpty(comments)) {
                    result.put("approvalType", comments.get(0).getApprovalType());
                    comments.remove(0);
                }
            });
        }
        Page<Map<String, Object>> page = MyBatisUtils.buildPage(flowTaskPageBO);
        page.setRecords(resultList);
        page.setTotal(pageData.getTotal());
        return CommonResult.data(MyBatisUtils.buildPage(page));
    }

    /**
     * 查询当前用户的历史任务数据
     *
     * @param flowTaskPageBO 分页对象
     * @return 查询结果应答
     */
    @OperationLog(module = "工作流查询", value = "根据输入参数查询，当前用户的历史流程数据", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "查询当前用户的历史任务数据")
    @PostMapping("/listHistoricProcessInstance")
    public CommonResult<CommonPageVO<Map<String, Object>>> listHistoricProcessInstance(@RequestBody FlowTaskPageBO flowTaskPageBO) throws ParseException {
        CommonPageVO<HistoricProcessInstance> pageData = flowApiService.getHistoricProcessInstanceList(flowTaskPageBO, true);
        Set<String> userIdSet = pageData.getRecords().stream().map(HistoricProcessInstance::getStartUserId).collect(Collectors.toSet());
        Map<String, String> userInfoMap = CollectionUtils.convertMap(sysUserApi.getUserListByIds(userIdSet), SysUserRespDTO::getUserId, SysUserRespDTO::getRealName);
        Map<String, String> userNameMap = CollectionUtils.convertMap(sysUserApi.getUserListByIds(userIdSet), SysUserRespDTO::getUserId, SysUserRespDTO::getUsername);
        List<Map<String, Object>> resultList = new LinkedList<>();
        pageData.getRecords().forEach(instance -> {
            Map<String, Object> data = BeanUtil.beanToMap(instance);
            data.put(SHOW_NAME, userInfoMap.get(instance.getStartUserId()));
            data.put("startUserId", userNameMap.get(instance.getStartUserId()));
            resultList.add(data);
        });
        Page<Map<String, Object>> page = MyBatisUtils.buildPage(flowTaskPageBO);
        page.setRecords(resultList);
        page.setTotal(pageData.getTotal());
        return CommonResult.data(MyBatisUtils.buildPage(page));
    }

    /**
     * 根据输入参数,查询所有历史流程数据
     *
     * @param flowTaskPageBO 分页对象
     * @return 查询结果
     */
    @OperationLog(module = "工作流查询", value = "根据输入参数,查询所有历史流程数据", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "根据输入参数,查询所有历史流程数据")
    @SaCheckPermission(value = "flow:operation:all")
    @PostMapping("/listAllHistoricProcessInstance")
    public CommonResult<CommonPageVO<Map<String, Object>>> listAllHistoricProcessInstance(@RequestBody FlowTaskPageBO flowTaskPageBO) throws ParseException {
        CommonPageVO<HistoricProcessInstance> pageData = flowApiService.getHistoricProcessInstanceList(flowTaskPageBO, false);
        List<Map<String, Object>> resultList = new LinkedList<>();
        pageData.getRecords().forEach(instance -> resultList.add(BeanUtil.beanToMap(instance)));
        List<String> unfinishedProcessInstanceIds = pageData.getRecords().stream()
            .filter(c -> c.getEndTime() == null).map(HistoricProcessInstance::getId).collect(Collectors.toList());
        Page<Map<String, Object>> page = MyBatisUtils.buildPage(flowTaskPageBO);
        page.setRecords(resultList);
        page.setTotal(pageData.getTotal());
        if (CollUtil.isEmpty(unfinishedProcessInstanceIds)) {
            return CommonResult.data(MyBatisUtils.buildPage(page));
        }
        Set<String> startUserIds = pageData.getRecords().stream().map(HistoricProcessInstance::getStartUserId).collect(Collectors.toSet());
        Map<String, String> userMap = CollectionUtils.convertMap(sysUserApi.getUserListByIds(startUserIds), SysUserRespDTO::getUserId, SysUserRespDTO::getUsername);
        List<Task> taskList = flowApiService.getTaskListByProcessInstanceIds(unfinishedProcessInstanceIds);
        Map<String, List<Task>> taskMap = taskList.stream().collect(Collectors.groupingBy(Task::getProcessInstanceId));
        for (Map<String, Object> result : resultList) {
            String processInstanceId = result.get("processInstanceId").toString();
            List<Task> instanceTaskList = taskMap.get(processInstanceId);
            if (instanceTaskList != null) {
                JSONArray taskArray = new JSONArray();
                for (Task task : instanceTaskList) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("taskId", task.getId());
                    jsonObject.put("taskName", task.getName());
                    jsonObject.put("taskKey", task.getTaskDefinitionKey());
                    jsonObject.put("assignee", task.getAssignee());
                    taskArray.add(jsonObject);
                }
                result.put("runtimeTaskInfoList", taskArray);
                FlowWorkOrder flowWorkOrder = flowWorkOrderService.getFlowWorkOrderByProcessInstanceId(processInstanceId);
                result.put("workOrderId", null == flowWorkOrder ? null : flowWorkOrder.getWorkOrderId());
            }
            //封装username
            result.put("startUserName", userMap.get(result.get("startUserId")));
        }
        return CommonResult.data(MyBatisUtils.buildPage(page));
    }

    /**
     * 催办工单，只有流程发起人才可以催办工单
     *
     * @param workOrderId 工单Id
     * @return 应答结果
     */
    @OperationLog(module = "工作流查询", value = "催办工单，只有流程发起人才可以催办工单", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "催办工单，只有流程发起人才可以催办工单")
    @PostMapping("/remindRuntimeTask")
    public CommonResult<Void> remindRuntimeTask(@RequestParam String workOrderId) {
        FlowWorkOrder flowWorkOrder = flowWorkOrderService.getById(workOrderId);
        if (ObjectUtil.isNull(flowWorkOrder)) {
            return CommonResult.fail(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        if (ObjectUtil.notEqual(flowWorkOrder.getCreateBy(), SecurityUtils.getLoginUserId())) {
            return CommonResult.fail("数据验证失败，只有流程发起人才能催办工单!");
        }
        if (flowWorkOrder.getFlowStatus().equals(FlowTaskStatus.FINISHED)
            || flowWorkOrder.getFlowStatus().equals(FlowTaskStatus.CANCELLED)
            || flowWorkOrder.getFlowStatus().equals(FlowTaskStatus.STOPPED)) {
            return CommonResult.fail("数据验证失败，已经结束的流程，不能催办工单！");
        }
        if (flowWorkOrder.getFlowStatus().equals(FlowTaskStatus.DRAFT)) {
            return CommonResult.fail("数据验证失败，流程草稿不能催办工单！");
        }
        flowMessageService.saveNewRemindMessage(flowWorkOrder);
        return CommonResult.success();
    }

    /**
     * 撤销工作流工单，仅当没有进入任何审批流程之前，才可以取消工单
     *
     * @param workOrderId  工单Id
     * @param cancelReason 取消原因
     * @return 应答结果
     */
    @OperationLog(module = "工作流查询", value = "撤销工作流工单", type = OperationTypeEnum.UPDATE)
    @Operation(summary = "撤销工作流工单")
    @PostMapping("/cancelWorkOrder")
    public CommonResult<Void> cancelWorkOrder(@RequestParam String workOrderId, @RequestParam String cancelReason) {
        FlowWorkOrder flowWorkOrder = flowWorkOrderService.getById(workOrderId);
        if (ObjectUtil.isNull(flowWorkOrder)) {
            return CommonResult.fail(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        if (ObjectUtil.notEqual(flowWorkOrder.getFlowStatus(), FlowTaskStatus.SUBMITTED)
            && ObjectUtil.notEqual(flowWorkOrder.getFlowStatus(), FlowTaskStatus.DRAFT)) {
            return CommonResult.fail("数据验证失败，当前流程已经进入审批状态，不能撤销工单！");
        }
        if (ObjectUtil.notEqual(flowWorkOrder.getCreateBy(), SecurityUtils.getLoginUserId())) {
            return CommonResult.fail("数据验证失败，当前用户不是工单所有者，不能撤销工单！");
        }
        // 草稿工单直接删除当前工单。
        if (flowWorkOrder.getFlowStatus().equals(FlowTaskStatus.DRAFT)) {
            flowWorkOrderService.removeDraft(flowWorkOrder);
        } else {
            flowApiService.stopProcessInstance(flowWorkOrder.getProcessInstanceId(), cancelReason, true);
        }
        return CommonResult.success();
    }

    /**
     * 主动干预当前的待办任务，任何有该接口操作权限的用户均可执行该干预操作
     *
     * @param rejectRuntimeTaskBO 干预操作入参BO
     * @return 操作应答结果
     */
    @OperationLog(module = "工作流查询", value = "主动干预当前的待办任务", type = OperationTypeEnum.UPDATE)
    @Operation(summary = "主动干预当前的待办任务")
    @SaCheckPermission(value = "flow:operation:all")
    @PostMapping("/interveneRuntimeTask")
    public CommonResult<Void> interveneRuntimeTask(@RequestBody RejectRuntimeTaskBO rejectRuntimeTaskBO) {
        Task task = flowApiService.getProcessInstanceActiveTask(rejectRuntimeTaskBO.getProcessInstanceId(), rejectRuntimeTaskBO.getTaskId());
        if (ObjectUtil.isNull(task)) {
            return CommonResult.fail("数据验证失败，该流程实例的待办任务Id不存在，请刷新后重试！");
        }
        if (StrUtil.isAllBlank(rejectRuntimeTaskBO.getTargetTaskKey(), rejectRuntimeTaskBO.getDelegateAssignee())) {
            return CommonResult.fail("数据验证失败，指派人和跳转任务不能同时为空！");
        }
        // 如果驳回到的任务是空，就可以直接走转办逻辑。
        if (StrUtil.isBlank(rejectRuntimeTaskBO.getTargetTaskKey())) {
            FlowTaskComment flowTaskComment = new FlowTaskComment();
            flowTaskComment.setDelegateAssignee(rejectRuntimeTaskBO.getDelegateAssignee());
            flowTaskComment.setApprovalType(FlowApprovalType.INTERVENE);
            flowApiService.transferTo(task, flowTaskComment);
            return CommonResult.success();
        }
        flowApiService.interveneTo(task, rejectRuntimeTaskBO.getTargetTaskKey(), rejectRuntimeTaskBO.getTaskComment(), rejectRuntimeTaskBO.getDelegateAssignee());
        //判断干预的任务节点是不是异常记录节点，是的话更新下状态
        flowRuTaskAbnormalRecordService.updateStatusByTaskId(rejectRuntimeTaskBO.getTaskId());
        return CommonResult.success();
    }

    /**
     * 终止流程实例，将任务从当前节点直接流转到主流程的结束事件
     *
     * @param processInstanceId 流程实例Id
     * @param stopReason        停止原因
     * @return 执行结果应答
     */
    @OperationLog(module = "工作流查询", value = "终止流程实例", type = OperationTypeEnum.UPDATE)
    @Operation(summary = "终止流程实例")
    @SaCheckPermission(value = "flow:operation:all")
    @PostMapping("/stopProcessInstance")
    public CommonResult<Void> stopProcessInstance(@RequestParam String processInstanceId, @RequestParam String stopReason) {
        flowApiService.stopProcessInstance(processInstanceId, stopReason, false);
        return CommonResult.success();
    }

    /**
     * 删除流程实例
     *
     * @param processInstanceId 流程实例Id
     * @return 执行结果应答
     */
    @OperationLog(module = "工作流查询", value = "删除流程实例", type = OperationTypeEnum.DELETE)
    @Operation(summary = "删除流程实例")
    @SaCheckPermission(value = "flow:operation:all")
    @PostMapping("/deleteProcessInstance")
    public CommonResult<Void> deleteProcessInstance(@RequestParam String processInstanceId) {
        flowApiService.deleteProcessInstance(processInstanceId);
        return CommonResult.success();
    }

    /**
     * 根据流程实例 processInstanceId 和 taskId获取 HistoricTaskInstance历史任务
     *
     * @param processInstanceId 流程实例Id
     * @param taskId            任务id
     * @return 历史任务列表
     */
    @OperationLog(module = "工作流查询", value = "根据流程实例 processInstanceId 和 taskId获取 HistoricTaskInstance历史任务", type = OperationTypeEnum.QUERY)
    @Operation(summary = "根据流程实例 processInstanceId 和 taskId获取 HistoricTaskInstance历史任务")
    @Parameters({@Parameter(name = "processInstanceId", description = "流程实例Id", required = true, example = "1111"),
        @Parameter(name = "taskId", description = "任务实例Id", required = true, example = "1111")})
    @GetMapping("/getHistoricTaskByInstanceIndOrTaskId")
    public CommonResult getHistoricTaskByInstanceIndOrTaskId(@RequestParam String processInstanceId, @RequestParam(required = false) String taskId) {
        List<HistoricTaskInstance> historicTaskInstances = flowApiService.getHistoricTaskInstances(processInstanceId, taskId);
        List<Map<String, Object>> resultList = new LinkedList<>();
        historicTaskInstances.forEach(instance -> resultList.add(BeanUtil.beanToMap(instance)));
        return CommonResult.data(resultList);
    }

    private List<FlowTaskComment> buildApprovedFlowTaskCommentList(TaskInfo taskInfo, boolean isMultiInstanceTask) {
        List<FlowTaskComment> taskCommentList;
        if (isMultiInstanceTask) {
            String multiInstanceExecId;
            FlowMultiInstanceTrans trans = flowMultiInstanceTransService.getByExecutionId(taskInfo.getExecutionId(), taskInfo.getId());
            if (trans != null) {
                multiInstanceExecId = trans.getMultiInstanceExecId();
            } else {
                multiInstanceExecId = flowApiService.getExecutionVariableStringWithSafe(taskInfo.getExecutionId(), MULTI_SIGN_TASK_EXECUTION_ID_VAR);
            }
            taskCommentList = flowTaskCommentService.getFlowTaskCommentListByMultiInstanceExecId(multiInstanceExecId);
        } else {
            taskCommentList = flowTaskCommentService.getFlowTaskCommentListByExecutionId(taskInfo.getProcessInstanceId(), taskInfo.getId(), taskInfo.getExecutionId());
        }
        return taskCommentList;
    }

    /**
     * 获取已办节点任务和待办节点任务审批人相关信息
     *
     * @param processInstanceId 流程实例Id
     * @return 已办节点任务和待办节点任务审批人相关信息
     */
    @OperationLog(module = "工作流查询", value = "获取已办节点任务和待办节点任务审批人相关信息", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "获取已办节点任务和待办节点任务审批人相关信息")
    @Parameter(name = "processInstanceId", description = "流程实例Id", required = true, example = "1111")
    @GetMapping("/getFlowTaskCommentAndRunningTaskUser")
    public CommonResult<List<FlowTaskCommentVO>> getFlowTaskCommentAndRunningTaskUser(@RequestParam String processInstanceId) {
        //待办任务，会签任务会生成多条task,还有有的项目会用并行流程
        List<Task> taskList = taskService.createTaskQuery().processInstanceId(processInstanceId).list();
        List<FlowTaskComment> flowTaskCommentList = flowTaskCommentService.getFlowTaskCommentOrderByCreatTime(processInstanceId);
        List<FlowTaskCommentVO> flowTaskCommentVOs = BeanUtil.copyToList(flowTaskCommentList, FlowTaskCommentVO.class);
        List<FlowTaskCommentVO> taskRunFlowTaskCommentVOs = new ArrayList<>();
        if (CollUtil.isNotEmpty(taskList)) {
            for (Task taskInfo : taskList) {
                List<FlowUserInfoVO> resultFlowUserInfoVOList = CollUtil.newArrayList();
                String taskKey = taskInfo.getTaskDefinitionKey();
                FlowTaskExt taskExt = flowTaskExtService.getByProcessDefinitionIdAndTaskId(taskInfo.getProcessDefinitionId(), taskKey);
                boolean isMultiInstanceTask = flowApiService.isMultiInstanceTask(taskInfo.getProcessDefinitionId(), taskKey);
                List<FlowUserInfoVO> resultUserInfoList = flowTaskExtService.getCandidateUserInfoList(processInstanceId, taskExt, taskInfo, isMultiInstanceTask, false);
                resultFlowUserInfoVOList.addAll(resultUserInfoList);
                String collect = resultFlowUserInfoVOList.stream().map(FlowUserInfoVO::getRealName).distinct().collect(Collectors.joining(","));
                FlowTaskCommentVO flowTaskCommentVO = new FlowTaskCommentVO();
                flowTaskCommentVO.setCreateUsername(collect);
                flowTaskCommentVO.setTaskKey(taskKey);
                flowTaskCommentVO.setTaskName(taskInfo.getName());
                flowTaskCommentVO.setCumulativeTime(this.getCumulativeTime(taskInfo.getCreateTime(), taskInfo.getProcessInstanceId(), taskInfo.getId()));
                flowTaskCommentVO.setApprovalType("");
                flowTaskCommentVO.setCreateTime(DateUtil.toLocalDateTime(taskInfo.getCreateTime()));
                //标记当前待办任务节点是否是异常数据
                FlowRuTaskAbnormalRecord flowRuTaskAbnormalRecord = flowRuTaskAbnormalRecordService.getByTaskIdAndStatus(taskInfo.getId(), CommonStatusEnum.DISABLE.getCode());
                if (null != flowRuTaskAbnormalRecord) {
                    flowTaskCommentVO.setIsAbnormalTask(Boolean.TRUE);
                    flowTaskCommentVO.setAbnormalTaskReason(flowRuTaskAbnormalRecord.getReason());
                }
                taskRunFlowTaskCommentVOs.add(flowTaskCommentVO);
            }
        }
        List<FlowTaskCommentVO> result = new ArrayList<>();
        Map<String, List<FlowTaskCommentVO>> taskRunFlowTaskCommentMap = taskRunFlowTaskCommentVOs.stream().collect(Collectors.groupingBy(FlowTaskCommentVO::getTaskKey));
        for (Map.Entry<String, List<FlowTaskCommentVO>> entry : taskRunFlowTaskCommentMap.entrySet()) {
            List<FlowTaskCommentVO> taskCommentVOList = entry.getValue();
            FlowTaskCommentVO flowTaskCommentVO = entry.getValue().get(0);
            if (taskCommentVOList.size() != 1) {
                String collect = taskCommentVOList.stream().map(FlowTaskCommentVO::getCreateUsername).distinct().collect(Collectors.joining(","));
                flowTaskCommentVO.setCreateUsername(collect);
            }
            result.add(flowTaskCommentVO);
        }
        Map<String, List<FlowTaskCommentVO>> listMap = flowTaskCommentVOs.stream().collect(Collectors.groupingBy(FlowTaskCommentVO::getTaskKey));
        for (Map.Entry<String, List<FlowTaskCommentVO>> entry : listMap.entrySet()) {
            FlowTaskCommentVO flowTaskCommentVO = entry.getValue().get(0);
            //已审批过的会签任务此时会有多条，取最新的一次审批记录，但是审批人要拼接起来
            if (flowTaskCommentVO.getApprovalType().contains("multi_")) {
                String userNames = entry.getValue().stream().map(FlowTaskCommentVO::getCreateUsername).distinct().collect(Collectors.joining(","));
                flowTaskCommentVO.setCreateUsername(userNames);
            }
            List<String> taskKeys = result.stream().map(FlowTaskCommentVO::getTaskKey).collect(Collectors.toList());
            //对于驳回后再次回到这个节点，这个节点又是当前待办任务节点时，之前的审批记录就不要返回了(前端实现的逻辑要求)
            if (!taskKeys.contains(flowTaskCommentVO.getTaskKey())) {
                result.add(flowTaskCommentVO);
            }
        }
        return CommonResult.data(result);
    }

    /**
     * 根据流程实例获取流程状态和当前待办任务（包括任务处理人，任务创建时间），已办节点信息
     *
     * @param processInstanceId 流程实例id
     * @return 返回流程状态和待办任务信息
     */
    @OperationLog(module = "工作流查询接口", value = "根据流程实例获取流程状态和当前待办任务", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "根据流程实例获取流程状态和当前待办任务")
    @Parameter(name = "processInstanceId", description = "流程实例Id", required = true, example = "1111")
    @GetMapping("/getFlowInfoByProcessInstanceId")
    public CommonResult getFlowInfoByprocessInstanceId(@RequestParam String processInstanceId) {
        JSONObject data = this.viewHighlightFlowData(processInstanceId).getData();
        List<Task> tasks = taskService.createTaskQuery().processInstanceId(processInstanceId).active().list();
        JSONArray waitingTasks = new JSONArray();
        for (Task task : tasks) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("taskId", task.getId());
            jsonObject.put("processInstanceId", task.getProcessInstanceId());
            jsonObject.put("processDefinitionId", task.getProcessDefinitionId());
            jsonObject.put("executionId", task.getExecutionId());
            jsonObject.put("name", task.getName());
            jsonObject.put("taskKey", task.getTaskDefinitionKey());

            jsonObject.put("createTime", task.getCreateTime());
            CommonResult<List<FlowUserInfoVO>> responseResult = this.viewTaskUserInfo(task.getProcessDefinitionId(), task.getProcessInstanceId(), task.getId(), false);
            jsonObject.put("assigneeInfo", responseResult.getData());
            waitingTasks.add(jsonObject);
        }
        data.put("waitingTasks", waitingTasks);
        FlowWorkOrder flowWorkOrder = flowWorkOrderService.getFlowWorkOrderByProcessInstanceId(processInstanceId);
        JSONObject flowWorkOrderObject = new JSONObject();
        flowWorkOrderObject.put("workOrderId", flowWorkOrder.getWorkOrderId());
        flowWorkOrderObject.put("businessKey", flowWorkOrder.getBusinessKey());
        flowWorkOrderObject.put("flowStatus", flowWorkOrder.getFlowStatus());
        flowWorkOrderObject.put("processDefinitionKey", flowWorkOrder.getProcessDefinitionKey());
        flowWorkOrderObject.put("processDefinitionName", flowWorkOrder.getProcessDefinitionName());
        data.put("flowWorkOrderInfo", flowWorkOrderObject);
        return CommonResult.data(data);
    }

    /**
     * 获取待办任务节点，审批用户信息
     *
     * @param processInstanceId 流程实例Id。
     * @return 任务相关的用户信息列表。
     */
    @OperationLog(module = "工作流查询接口", value = "获取待办任务节点，审批用户信息", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "获取待办任务节点，审批用户信息")
    @Parameter(name = "processInstanceId", description = "流程实例Id", required = true, example = "1111")
    @GetMapping("/getRunningTaskUserInfo")
    public CommonResult<List<FlowUserInfoVO>> getRunningTaskUserInfo(@RequestParam String processInstanceId) {
        List<Task> taskList = taskService.createTaskQuery().processInstanceId(processInstanceId).list();
        if (CollectionUtils.isAnyEmpty(taskList)) {
            return CommonResult.data(new ArrayList<>());
        }
        List<FlowUserInfoVO> resultFlowUserInfoVoList = CollUtil.newArrayList();
        for (Task taskInfo : taskList) {
            String taskKey = taskInfo.getTaskDefinitionKey();
            FlowTaskExt taskExt = flowTaskExtService.getByProcessDefinitionIdAndTaskId(taskInfo.getProcessDefinitionId(), taskKey);
            boolean isMultiInstanceTask = flowApiService.isMultiInstanceTask(taskInfo.getProcessDefinitionId(), taskKey);
            List<FlowUserInfoVO> resultUserInfoList = flowTaskExtService.getCandidateUserInfoList(processInstanceId, taskExt, taskInfo, isMultiInstanceTask, false);
            resultFlowUserInfoVoList.addAll(resultUserInfoList);
        }
        return CommonResult.data(resultFlowUserInfoVoList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(FlowUserInfoVO::getUsername))), ArrayList::new)));
    }

    /**
     * 根据流程实例id获取已办节点审批人，当前待办任务审批用户（该接口是针对三部项目的接口，类似功能可参考公用的/getFlowTaskCommentAndRunningTaskUser这个接口）
     *
     * @param processInstanceId 流程实例Id
     * @return 任务相关的用户信息列表。
     */
    @OperationLog(module = "工作流查询接口", value = "获取待办任务节点，审批用户信息", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "根据流程实例id获取已办节点审批人，当前待办任务审批用户")
    @Parameter(name = "processInstanceId", description = "流程实例Id", required = true, example = "1111")
    @GetMapping("/getFinishedTaskAndRunningTaskApproverUser")
    public CommonResult<Map<String, List<FlowTaskCommentVO>>> getFinishedTaskAndRunningTaskApproverUser(@RequestParam String processInstanceId) {
        //待办任务，会签任务会生成多条task,还有三部项目会用并行流程
        List<Task> taskList = taskService.createTaskQuery().processInstanceId(processInstanceId).list();
        List<FlowTaskCommentVO> flowTaskCommentList = flowTaskCommentService.getFlowTaskCommentList(processInstanceId);
        if (CollUtil.isNotEmpty(taskList)) {
            for (Task taskInfo : taskList) {
                List<FlowUserInfoVO> resultFlowUserInfoVoList = CollUtil.newArrayList();
                String taskKey = taskInfo.getTaskDefinitionKey();
                FlowTaskExt taskExt = flowTaskExtService.getByProcessDefinitionIdAndTaskId(taskInfo.getProcessDefinitionId(), taskKey);
                boolean isMultiInstanceTask = flowApiService.isMultiInstanceTask(taskInfo.getProcessDefinitionId(), taskKey);
                List<FlowUserInfoVO> resultUserInfoList = flowTaskExtService.getCandidateUserInfoList(processInstanceId, taskExt, taskInfo, isMultiInstanceTask, false);
                resultFlowUserInfoVoList.addAll(resultUserInfoList);
                resultFlowUserInfoVoList.stream().forEach(e -> {
                    FlowTaskCommentVO flowTaskComment = new FlowTaskCommentVO();
                    flowTaskComment.setCreateUserId(e.getUserId());
                    flowTaskComment.setCreateUsername(e.getRealName());
                    flowTaskComment.setCreateBy(e.getUserId());
                    flowTaskComment.setTaskKey(taskKey);
                    flowTaskCommentList.add(flowTaskComment);
                });
            }
        }
        Map<String, List<FlowTaskCommentVO>> listMap = flowTaskCommentList.stream().collect(Collectors.groupingBy(FlowTaskCommentVO::getTaskKey));
        for (Map.Entry<String, List<FlowTaskCommentVO>> entry : listMap.entrySet()) {
            ArrayList<FlowTaskCommentVO> value = entry.getValue().stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(FlowTaskCommentVO::getCreateUserId))), ArrayList::new));
            listMap.put(entry.getKey(), value);
        }
        return CommonResult.data(listMap);
    }

    /**
     * 根据流程定义processDefinitionKey获取流程图的bpmn_xml数据 该接口获取的xml文件是从 FlowEntry 表里获取的，是总的流程图，该接口适用于三部的需求
     *
     * @param processDefinitionKey 流程定义key
     * @return 返回流程图的xml数据
     */
    @OperationLog(module = "工作流查询接口", value = "根据流程定义processDefinitionKey获取流程图的bpmn_xml数据", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "根据流程定义processDefinitionKey获取流程图的bpmn_xml数据")
    @Parameter(name = "processDefinitionKey", description = "流程定义key", required = true, example = "leaveApplyDefinitionKey")
    @GetMapping("/getBpmnXmlByProcessDefinitionKey")
    public CommonResult<String> getBpmnXmlByProcessDefinitionKey(@RequestParam String processDefinitionKey) {
        FlowEntry entry = flowEntryService.getFlowEntryFromCache(processDefinitionKey);
        return CommonResult.data(entry.getBpmnXml());
    }


    /**
     * 根据流程processDefinitionId获取该版本流程各节点配置信息
     *
     * @param processDefinitionId 流程发布后生成的流程定义id值
     * @return 流程各节点配置的信息
     */
    @OperationLog(module = "工作流查询接口", value = "根据流程processDefinitionId获取该版本流程各节点配置信息", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "根据流程processDefinitionId获取该版本流程各节点配置信息")
    @Parameter(name = "processDefinitionId", description = "流程发布后生成的流程定义id值", required = true, example = "postAdjustment:91:dc467a17-6aac-11ef-8536-6c3c8c4baef5")
    @GetMapping("/getFlowTaskExtByProcessDefinitionId")
    public CommonResult<List<FlowTaskExtVO>> getFlowTaskExtByProcessDefinitionId(@RequestParam String processDefinitionId) {
        List<FlowTaskExt> flowTaskExts = flowTaskExtService.getByProcessDefinitionId(processDefinitionId);
        List<FlowTaskExtVO> flowTaskExtVOS = this.toVO(flowTaskExts);
        return CommonResult.data(flowTaskExtVOS);
    }

    /**
     * 验证流程结束回调业务接口功能，demo接口
     *
     * @param userTaskBO 请求条件
     * @param request    请求对象
     * @return
     */
    @PostMapping("/testCallbackBusiness")
    public CommonResult testCallbackBusiness(@RequestBody FlowSubmitUserTaskBO userTaskBO, HttpServletRequest request) {
        String token = request.getHeader("Authorization");
        log.info("验证请求/flowOperation/testCallbackBusiness接口的token为：{}", token);
        log.info("验证请求/flowOperation/testCallbackBusiness接口的参数为：{}", JSONObject.toJSONString(userTaskBO));
        FlowWorkOrder flowWorkOrder = flowWorkOrderService.getFlowWorkOrderByProcessInstanceId(userTaskBO.getProcessInstanceId());
        return CommonResult.data(flowWorkOrder);
    }

    private List<FlowTaskExtVO> toVO(List<FlowTaskExt> flowTaskExts) {
        return flowTaskExts.stream().map(flowTaskExt -> {
            FlowTaskExtVO flowTaskExtVO = new FlowTaskExtVO();
            BeanUtils.copyProperties(flowTaskExt, flowTaskExtVO);
            flowTaskExtVO.setUserTaskExtData(JSON.parseObject(flowTaskExt.getExtraDataJson(), FlowUserTaskExtData.class));
            flowTaskExtVO.setDeptPostList(JSON.parseArray(flowTaskExt.getDeptPostListJson(), FlowTaskPostCandidateGroupDTO.class));
            flowTaskExtVO.setOperationList(JSON.parseArray(flowTaskExt.getOperationListJson(), JSONObject.class));
            flowTaskExtVO.setVariableList(JSON.parseArray(flowTaskExt.getVariableListJson(), JSONObject.class));
            return flowTaskExtVO;
        }).collect(Collectors.toList());
    }


    private String getCumulativeTime(Date createTime, String processInstanceId, String taskId) {
        LocalDateTime dateTimeLocal = LocalDateTimeUtil.of(createTime);
        Duration between = Duration.between(dateTimeLocal, LocalDateTimeUtil.of(new Date()));
        long toMinutes = between.toMinutes();
        //判断该条待办任务是否有超时任务挂起动作，如果有，累计时间需要把挂起至恢复这段的时间剔除掉
        Integer hangUpTotalTime = flowTaskTimeoutJobService.getHangUpTotalTime(processInstanceId, taskId);
        long betweenMinutes = toMinutes - hangUpTotalTime.longValue();
        long hour = betweenMinutes / 60;
        long min = betweenMinutes % 60;
        String hourString = String.valueOf(hour);
        String minString = String.valueOf(min);
        if (hourString.length() == 1) {
            hourString = "0" + hourString;
        }
        if (minString.length() == 1) {
            minString = "0" + minString;
        }
        return hourString + ":" + minString;
    }

    private String findExistAssignee(Set<String> assigneeSet, JSONArray assigneeArray) {
        for (int i = 0; i < assigneeArray.size(); i++) {
            String loginName = assigneeArray.getString(i);
            if (assigneeSet.contains(loginName)) {
                return loginName;
            }
        }
        return null;
    }

}

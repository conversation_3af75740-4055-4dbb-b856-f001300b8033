package cn.com.sipsg.common.flow.core.timer;

import cn.com.sipsg.common.flow.core.constant.FlowTaskTimeoutJobStatus;
import cn.com.sipsg.common.flow.core.entity.FlowTaskTimeoutJob;
import cn.com.sipsg.common.flow.core.service.FlowTaskTimeoutJobService;
import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.TaskService;
import org.flowable.task.api.Task;
import org.flowable.task.api.TaskQuery;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 流程任务超时处理定时器
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@EnableScheduling
@Component
@Slf4j
@RequiredArgsConstructor
public class FlowTaskTimeoutTimer {

    private final FlowTaskTimeoutJobService flowTaskTimeoutJobService;

    private final RedissonClient redissonClient;

    private final TaskService taskService;

    /**
     * 每多少分钟执行一次。
     */
    @Scheduled(cron = "${medox.flow.flowTasktimeoutJob.cron}")
    public void execute() {
        //查询非挂起状态，并且未执行状态的job
        List<FlowTaskTimeoutJob> jobList = flowTaskTimeoutJobService.getExecutableList();
        if (CollUtil.isEmpty(jobList)) {
            return;
        }
        for (FlowTaskTimeoutJob job : jobList) {
            // 分布式锁控制重复执行某一条任务
            RLock disLock = redissonClient.getLock("FLOW_TASK_TIMEOUT_TIMEER" + job.getId());
            //if (!disLock.tryLock()) {return;}
            disLock.lock();
            try {
                TaskQuery query = taskService.createTaskQuery().active().taskId(job.getTaskId());
                Task task = query.singleResult();
                if (task != null) {
                    log.info("开始执行超时任务，task的processInstanceId为：{}，taskKey为：{}，taskId为：{},任务执行类型为：{}",
                        task.getProcessInstanceId(), task.getTaskDefinitionKey(), task.getId(), job.getHandleType());
                    flowTaskTimeoutJobService.executeJob(job, task);
                } else {
                    // task==null 时的情况有该task生成了发送通知和自动审批的任务，在时间还没触发前，可能已经被手动审批了，或者自动审批通过比超时发送消息先执行了，导致task为null，即任务已经处理了，act_ru_task表里已经没有记录了
                    // 此时将状态更新为1，这样定时调度下次就不会查询到了，主要处理一个节点同时设置超时自动处理和发送系统消息两种形式
                    //还有一种情况任务节点如果设置了自动同意，自动跳过功能时，此时 task == null
                    log.info("task为null，即该条任务已经被处理了，task的processInstanceId为：{}，taskId为：{},任务执行类型为：{},job的id为：{}",
                        job.getProcessInstanceId(), job.getTaskId(), job.getHandleType(), job.getId());
                    job.setStatus(FlowTaskTimeoutJobStatus.SUCCESS);
                    job.setUpdateTime(LocalDateTime.now());
                    flowTaskTimeoutJobService.updateById(job);
                }
            } catch (Exception e) {
                log.error("Failed to call FlowTaskTimeoutTimer.execute", e);
            } finally {
                disLock.unlock();
            }
        }
    }
}

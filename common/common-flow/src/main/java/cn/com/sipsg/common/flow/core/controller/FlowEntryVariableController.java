package cn.com.sipsg.common.flow.core.controller;

import cn.com.sipsg.common.flow.core.entity.FlowEntryVariable;
import cn.com.sipsg.common.flow.core.pojo.dto.FlowEntryVariableBO;
import cn.com.sipsg.common.flow.core.pojo.vo.FlowEntryVariableVO;
import cn.com.sipsg.common.flow.core.service.FlowEntryVariableService;
import cn.com.sipsg.common.operationlog.core.annotation.OperationLog;
import cn.com.sipsg.common.operationlog.core.enums.OperationTypeEnum;
import cn.com.sipsg.common.pojo.CommonResult;
import cn.com.sipsg.common.util.BeanUtils;
import cn.com.sipsg.common.validation.UpdateValidated;
import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 工作流流程变量接口
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Tag(name = "工作流流程变量控制器")
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/flow/flowEntryVariable")
public class FlowEntryVariableController {

    private final FlowEntryVariableService flowEntryVariableService;

    /**
     * 新增流程变量数据
     *
     * @param flowEntryVariableDto 新增对象
     * @return 应答结果对象，包含新增对象主键Id
     */
    @OperationLog(module = "流程变量", value = "新增流程变量数据", type = OperationTypeEnum.SAVE)
    @Operation(summary = "新增流程变量数据")
    @SaCheckPermission(value = "flow:entry:all")
    @PostMapping("/save")
    public CommonResult<String> save(@Validated @RequestBody FlowEntryVariableBO flowEntryVariableDto) {
        FlowEntryVariable flowEntryVariable = BeanUtils.copyProperties(flowEntryVariableDto, FlowEntryVariable.class);
        flowEntryVariable = flowEntryVariableService.saveNew(flowEntryVariable);
        return CommonResult.data(flowEntryVariable.getVariableId());
    }

    /**
     * 更新流程变量数据
     *
     * @param flowEntryVariableBO 更新操作BO对象
     */
    @OperationLog(module = "流程变量", value = "更新流程变量数据", type = OperationTypeEnum.UPDATE)
    @Operation(summary = "更新流程变量数据")
    @SaCheckPermission(value = "flow:entry:all")
    @PostMapping("/update")
    public CommonResult update(@UpdateValidated @RequestBody FlowEntryVariableBO flowEntryVariableBO) {
        flowEntryVariableService.update(flowEntryVariableBO);
        return CommonResult.success();
    }

    /**
     * 删除流程变量数据
     *
     * @param variableId 删除对象主键Id
     * @return 应答结果对象
     */
    @OperationLog(module = "流程变量", value = "删除流程变量数据", type = OperationTypeEnum.DELETE)
    @Operation(summary = "删除流程变量数据")
    @Parameter(name = "variableId", description = "删除对象主键Id", required = true, example = "123456")
    @SaCheckPermission(value = "flow:entry:all")
    @PostMapping("/delete")
    public CommonResult<Void> delete(@RequestParam String variableId) {
        flowEntryVariableService.checkExist(variableId);
        flowEntryVariableService.removeById(variableId);
        return CommonResult.success();
    }

    /**
     * 根据 流程实例entryId查询流程变量参数列表
     *
     * @param entryId 指定对象主键Id
     * @return 应答结果对象，包含对象详情
     */
    @OperationLog(module = "流程变量", value = "列出符合过滤条件的流程变量列表", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "列出符合过滤条件的流程变量列表")
    @SaCheckPermission(value = "flow:entry:all")
    @PostMapping("/list")
    public CommonResult<List<FlowEntryVariableVO>> list(@RequestParam String entryId) {
        List<FlowEntryVariableVO> flowEntryVariableVOS = flowEntryVariableService.getFlowEntryVariableListByEntryId(entryId);
        return CommonResult.data(flowEntryVariableVOS);
    }

    /**
     * 查看指定流程变量对象详情
     *
     * @param variableId 指定对象主键Id
     * @return 应答结果对象，包含对象详情
     */
    @OperationLog(module = "流程变量", value = "查看指定流程变量对象详情", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "查看指定流程变量对象详情")
    @Parameter(name = "variableId", description = "流程变量对象主键Id", required = true, example = "1111")
    @SaCheckPermission(value = "flow:entry:all")
    @GetMapping("/detail")
    public CommonResult<FlowEntryVariableVO> detail(@RequestParam String variableId) {
        FlowEntryVariable entryVariable = flowEntryVariableService.checkExist(variableId);
        FlowEntryVariableVO flowEntryVariableVO = BeanUtils.copyProperties(entryVariable, FlowEntryVariableVO.class);
        return CommonResult.data(flowEntryVariableVO);
    }

}

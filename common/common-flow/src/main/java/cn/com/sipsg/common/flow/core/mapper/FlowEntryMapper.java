package cn.com.sipsg.common.flow.core.mapper;

import cn.com.sipsg.common.flow.core.entity.FlowEntry;
import cn.com.sipsg.common.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * FlowEntry数据操作访问接口。
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Mapper
public interface FlowEntryMapper extends BaseMapperX<FlowEntry> {

    @Select("select * from medox_flow_entry mfe where mfe.entry_id = #{entryId}")
    FlowEntry getByEntryId(@Param("entryId") String entryId);

    @Select("select * from medox_flow_entry mfe where mfe.process_definition_key = #{processDefinitionKey}")
    FlowEntry getByProcessDefinitionKey(@Param("processDefinitionKey") String processDefinitionKey);
}

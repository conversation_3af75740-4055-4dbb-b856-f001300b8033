package cn.com.sipsg.common.flow.core.pojo.bo;

import cn.com.sipsg.common.flow.core.pojo.dto.FlowTaskCommentDTO;
import cn.com.sipsg.common.flow.core.pojo.vo.PostAdjustmentDTO;
import com.alibaba.fastjson2.JSONObject;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

/**
 * 岗位申请BO对象
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "岗位申请BO对象")
public class PostAdjustmentBO {

    /**
     * 流程标识
     */
    @Schema(description = "流程标识")
    @NotNull(message = "流程标识不能为空")
    private String processDefinitionKey;

    /**
     * 审批提交数据
     */
    @Schema(description = "审批提交数据")
    @NotNull(message = "流程审批对象不能为空")
    private FlowTaskCommentDTO flowTaskCommentDTO;

    /**
     * 任务提交变量
     */
    @Schema(description = "任务提交变量")
    @NotNull(message = "任务提交变量不能为空")
    private JSONObject taskVariableData;

    /**
     * 表单提交数据
     */
    @Schema(description = "表单提交数据")
    private PostAdjustmentDTO postAdjustmentDTO;

    /**
     * 抄送数据
     */
    @Schema(description = "抄送数据")
    private JSONObject copyData;

    /**
    * 流程实例id
    */
    @Schema(description = "流程实例id")
    private String processInstanceId;

    /**
    * 任务id
    */
    @Schema(description = "任务id")
    private String taskId;

    /**
     * 工单记录表主键id值
     */
    @Schema(description = "工单记录表主键id值")
    private String workOrderId;

}

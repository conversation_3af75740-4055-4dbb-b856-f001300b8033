package cn.com.sipsg.common.flow.core.mapper;

import cn.com.sipsg.common.flow.core.entity.FlowCategory;
import cn.com.sipsg.common.mybatis.core.mapper.BaseMapperX;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * FlowCategory数据操作访问接口。
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Mapper
public interface FlowCategoryMapper extends BaseMapperX<FlowCategory> {

    /**
     * 获取过滤后的对象列表。
     *
     * @param flowCategoryFilter 主表过滤对象。
     * @param orderBy            排序字符串，order by从句的参数。
     * @return 对象列表。
     */
    List<FlowCategory> getFlowCategoryList(
        @Param("flowCategoryFilter") FlowCategory flowCategoryFilter, @Param("orderBy") String orderBy);
}

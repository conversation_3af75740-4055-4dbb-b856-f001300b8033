package cn.com.sipsg.common.flow.core.listener;

import cn.com.sipsg.common.flow.core.constant.FlowApprovalType;
import cn.com.sipsg.common.flow.core.entity.FlowTaskComment;
import cn.com.sipsg.common.flow.core.service.FlowTaskCommentService;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import java.util.Arrays;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.flowable.common.engine.api.delegate.Expression;
import org.flowable.engine.delegate.TaskListener;
import org.flowable.task.service.delegate.DelegateTask;
import org.springframework.stereotype.Component;

/**
 * 路由表单在页面动态的指定审批用户,可以采用添加监听器类的方式去实现
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Slf4j
@Component
public class FlowAppointApprovalUser implements TaskListener {

    // appointApprovalUser  是任务监听器注入字段属性对象
    private Expression appointApprovalUser;

    private final transient FlowTaskCommentService flowTaskCommentService = SpringUtil.getBean(FlowTaskCommentService.class);

    @Override
    public void notify(DelegateTask delegateTask) {
        //如果是从其他节点干预到动态指定人的节点，则动态指定人的逻辑就不用触发了，干预操作已经给生成任务处理人了
        String approvalType = flowTaskCommentService.getLatestFlowTaskComment(delegateTask.getProcessInstanceId()).getApprovalType();
        if (FlowApprovalType.INTERVENE.equals(approvalType)) {
            return;
        }
        //以下为任务监听器注入属性字段获取方式,此种方式在任务监听器和执行监听器中都可以作为注入属性值获取方式
        Object fieldName = appointApprovalUser.getValue(delegateTask);
        if (null == fieldName) {
            return;
        }
        //当前节点是动态指定审批人类型，干预操作，选择当前节点，处理用户换了一个人，此时不要走后面逻辑再次指定逻辑,否则又再次指定了
        FlowTaskComment latestFlowTaskComment = flowTaskCommentService.getLatestFlowTaskComment(delegateTask.getProcessInstanceId(), delegateTask.getTaskDefinitionKey());
        if (null != latestFlowTaskComment && latestFlowTaskComment.getApprovalType().equals(FlowApprovalType.INTERVENE)) {
            return;
        }
        Object userId = delegateTask.getVariable(String.valueOf(fieldName));
        if (ObjectUtil.isNotNull(userId)) {
            List<String> userIds = Arrays.asList(StringUtils.split(String.valueOf(userId), StrUtil.COMMA));
            delegateTask.addCandidateUsers(userIds);
        }
    }
}

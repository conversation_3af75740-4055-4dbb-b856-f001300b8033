package cn.com.sipsg.common.flow.core.listener;

import cn.hutool.core.util.StrUtil;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.flowable.common.engine.api.delegate.Expression;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.ExecutionListener;

/**
 * 执行监听器调试
 *
 * <AUTHOR>
 * @since 2025/04/14
 **/
@Slf4j
public class ExecutionListenerDemo implements ExecutionListener {

    @Setter
    private Expression ceshi;

    @Setter
    private Expression ceshitwo;


    @Override
    public void notify(DelegateExecution execution) {

        //执行监听器中获取注入属性值
        String ceshi1 = (String)ceshi.getValue(execution);
        String ceshitwo1 = (String)ceshitwo.getValue(execution);

        if (!StrUtil.equals("end", execution.getEventName())) {
            return;
        }
       /*Expression fixedValue =
           ApplicationContextHolder.getBean(Expression.class);
       List<? extends DelegateExecution> executions = execution.getExecutions();*/
        log.info("TestExecutionListener的execution：{}", execution.toString());
    }
}

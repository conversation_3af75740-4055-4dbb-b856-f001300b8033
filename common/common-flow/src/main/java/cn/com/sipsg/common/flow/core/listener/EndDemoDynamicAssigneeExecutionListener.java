package cn.com.sipsg.common.flow.core.listener;

import cn.com.sipsg.module.system.api.upms.SysUserApi;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson2.JSONArray;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.ExecutionListener;
import org.flowable.engine.impl.persistence.entity.ExecutionEntityImpl;

/**
 * 动态指定会签人java类 流程图中会签节点无法配置会签人时，通过任务变量动态的指定到会签节点上，任务变量携带的是会签人账号， 该监听类是在会签任务结束时为了动态的给下个节点设置会签人参数而开发的，不具有共用性
 */
@Data
@Slf4j
public class EndDemoDynamicAssigneeExecutionListener implements ExecutionListener {

    private final transient SysUserApi sysUserApi = SpringUtil.getBean(SysUserApi.class);

    @Override
    public void notify(DelegateExecution execution) {

        JSONArray assigneeList = (JSONArray)execution.getVariable("assigneeList");

        Integer nrOfInstances = (Integer)execution.getVariable("nrOfInstances");
        Integer nrOfCompletedInstances = (Integer)execution.getVariable("nrOfCompletedInstances");
        if (null != nrOfInstances && null != nrOfCompletedInstances && nrOfInstances.equals(nrOfCompletedInstances)) {
            //业务可以根据自己的需要，获取到需要指定的会签人，设置到assigneeList列表里
            execution.setVariable("assigneeList", assigneeList);
            log.info("EndDemoDynamicAssigneeExecutionListener监听器处理流程实例id为:{},节点:{}，节点中文名称：{}，分配的会签人：{}", execution.getProcessInstanceId(), ((ExecutionEntityImpl)execution).getActivityId(),
                ((ExecutionEntityImpl)execution).getActivityName(), assigneeList.toString());
        }
    }
}

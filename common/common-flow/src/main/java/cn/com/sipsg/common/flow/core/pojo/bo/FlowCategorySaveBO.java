package cn.com.sipsg.common.flow.core.pojo.bo;

import cn.com.sipsg.common.validation.group.UpdateGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

/**
 * 流程分类的保存更新BO对象
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "流程分类的保存更新BO对象")
public class FlowCategorySaveBO {

    /**
     * 主键Id。
     */
    @Schema(description = "主键Id", example = "1111")
    @NotBlank(message = "主键categoryId不能为空！", groups = {UpdateGroup.class})
    private String categoryId;

    /**
     * 显示名称。
     */
    @Schema(description = "显示名称", requiredMode = RequiredMode.REQUIRED, example = "分类名称")
    @NotBlank(message = "显示名称不能为空！")
    private String name;

    /**
     * 分类编码。
     */
    @Schema(description = "分类编码", requiredMode = RequiredMode.REQUIRED, example = "categoryCode")
    @NotBlank(message = "分类编码不能为空！")
    private String code;

    /**
     * 实现顺序。
     */
    @Schema(description = "实现顺序", requiredMode = RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "实现顺序不能为空！")
    private Integer showOrder;
}

package cn.com.sipsg.common.flow.core.entity;

import cn.com.sipsg.common.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;

/**
 * 流程任务消息的候选身份实体对象。
 *
 * <AUTHOR>
 * @since 2025/04/14
 */

@Getter
@Setter
@FieldNameConstants
@TableName(value = "medox_flow_msg_candidate_identity", autoResultMap = true)
public class FlowMessageCandidateIdentity extends BaseDO {

    /**
     * 主键Id。
     */
    @TableId(value = "id")
    private String id;

    /**
     * 任务消息Id。
     */
    private String messageId;

    /**
     * 候选身份类型。
     */
    private String candidateType;

    /**
     * 候选身份Id。
     */
    private String candidateId;
}

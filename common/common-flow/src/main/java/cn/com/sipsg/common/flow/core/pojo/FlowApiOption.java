package cn.com.sipsg.common.flow.core.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * FlowApi操作对象

 * <AUTHOR>
 * @since 2025/04/14
 **/
@Getter
@Setter
@Schema(description = "FlowApi操作对象")
public class FlowApiOption {
    /**
     * 是否删除任务超时作业。
     */
    @Schema(description = "是否删除任务超时作业")
    private boolean deleteTimeoutTaskJob = false;

    /**
     * 是否超时自动通过
     */
    @Schema(description = "是否超时自动通过")
    private Boolean isTimeoutAutoComplete = false;
}

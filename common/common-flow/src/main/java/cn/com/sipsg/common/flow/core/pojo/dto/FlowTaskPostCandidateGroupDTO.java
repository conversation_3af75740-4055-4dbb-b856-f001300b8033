package cn.com.sipsg.common.flow.core.pojo.dto;

import cn.com.sipsg.common.flow.core.constant.FlowConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.LinkedList;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

/**
 * 流程任务岗位候选组数据。仅用于流程任务的候选组类型为岗位时。
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "流程任务岗位候选组数据。仅用于流程任务的候选组类型为岗位时")
public class FlowTaskPostCandidateGroupDTO {

    /**
     * 唯一值，目前仅前端使用。
     */
    @Schema(description = "唯一值")
    private String id;

    /**
     * 岗位类型。 1. 所有部门岗位审批变量，值为 (allDeptPost)。 2. 本部门岗位审批变量，值为 (selfDeptPost)。 3. 上级部门岗位审批变量，值为 (upDeptPost)。 4. 任意部门关联的岗位审批变量，值为 (deptPost)。
     */
    @Schema(description = "岗位类型")
    private String type;

    /**
     * 岗位Id。type为(1,2,3)时使用该值。
     */
    @Schema(description = "岗位Id")
    private String postId;

    /**
     * 部门岗位Id。type为(4)时使用该值。
     */
    @Schema(description = "部门岗位Id")
    private String deptPostId;

    public static List<String> buildCandidateGroupList(List<FlowTaskPostCandidateGroupDTO> groupDataList) {
        List<String> candidateGroupList = new LinkedList<>();
        for (FlowTaskPostCandidateGroupDTO groupData : groupDataList) {
            switch (groupData.getType()) {
                case FlowConstant.GROUP_TYPE_ALL_DEPT_POST_VAR:
                    candidateGroupList.add(groupData.getPostId());
                    break;
                case FlowConstant.GROUP_TYPE_DEPT_POST_VAR:
                    candidateGroupList.add(groupData.getDeptPostId());
                    break;
                case FlowConstant.GROUP_TYPE_SELF_DEPT_POST_VAR:
                    candidateGroupList.add("${" + FlowConstant.SELF_DEPT_POST_PREFIX + groupData.getPostId() + "}");
                    break;
                case FlowConstant.GROUP_TYPE_SIBLING_DEPT_POST_VAR:
                    candidateGroupList.add("${" + FlowConstant.SIBLING_DEPT_POST_PREFIX + groupData.getPostId() + "}");
                    break;
                case FlowConstant.GROUP_TYPE_UP_DEPT_POST_VAR:
                    candidateGroupList.add("${" + FlowConstant.UP_DEPT_POST_PREFIX + groupData.getPostId() + "}");
                    break;
                default:
                    break;
            }
        }
        return candidateGroupList;
    }
}

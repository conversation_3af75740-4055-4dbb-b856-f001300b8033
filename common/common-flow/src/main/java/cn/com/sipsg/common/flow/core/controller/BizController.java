package cn.com.sipsg.common.flow.core.controller;

import cn.com.sipsg.common.flow.core.pojo.bo.SysDeptQueryBO;
import cn.com.sipsg.common.flow.core.pojo.bo.SysPostQueryBO;
import cn.com.sipsg.common.flow.core.pojo.bo.SysRoleQueryBO;
import cn.com.sipsg.common.flow.core.pojo.bo.SysUserQueryBO;
import cn.com.sipsg.common.flow.core.pojo.vo.SysDeptPostVO;
import cn.com.sipsg.common.flow.core.pojo.vo.SysDeptVO;
import cn.com.sipsg.common.flow.core.pojo.vo.SysPostVO;
import cn.com.sipsg.common.flow.core.pojo.vo.SysRoleVO;
import cn.com.sipsg.common.flow.core.pojo.vo.SysUserVO;
import cn.com.sipsg.common.operationlog.core.annotation.OperationLog;
import cn.com.sipsg.common.operationlog.core.enums.OperationTypeEnum;
import cn.com.sipsg.common.pojo.CommonResult;
import cn.com.sipsg.common.util.BeanUtils;
import cn.com.sipsg.module.system.api.upms.SysDeptApi;
import cn.com.sipsg.module.system.api.upms.SysPostApi;
import cn.com.sipsg.module.system.api.upms.SysRoleApi;
import cn.com.sipsg.module.system.api.upms.SysUserApi;
import cn.com.sipsg.module.system.api.upms.dto.SysDeptPostRespDTO;
import cn.com.sipsg.module.system.api.upms.dto.SysDeptQueryReqDTO;
import cn.com.sipsg.module.system.api.upms.dto.SysDeptRespDTO;
import cn.com.sipsg.module.system.api.upms.dto.SysPostQueryReqDTO;
import cn.com.sipsg.module.system.api.upms.dto.SysPostRespDTO;
import cn.com.sipsg.module.system.api.upms.dto.SysRoleQueryReqDTO;
import cn.com.sipsg.module.system.api.upms.dto.SysRoleRespDTO;
import cn.com.sipsg.module.system.api.upms.dto.SysUserQueryReqDTO;
import cn.com.sipsg.module.system.api.upms.dto.SysUserRespDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import java.util.Arrays;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 业务Controller,获取其他系统模块数据的接口
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Tag(name = "工作流-业务控制器")
@RequiredArgsConstructor
@RestController
@RequestMapping("/flow/biz")
public class BizController {

    private final SysPostApi sysPostApi;

    private final SysUserApi sysUserApi;

    private final SysDeptApi sysDeptApi;

    private final SysRoleApi sysRoleApi;

    /**
     * 根据用户名查询用户信息接口
     *
     * @param username
     * @return 用户信息
     */
    @OperationLog(module = "工作流-业务控制器", value = "根据用户名查询用户信息接口", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "根据用户名查询用户信息接口")
    @Parameter(name = "username", description = "用户名", required = true)
    @GetMapping("/getUserInfoByUsername")
    public CommonResult<SysUserVO> getUserInfoByUsername(@RequestParam String username) {
        SysUserRespDTO userRespDTO = sysUserApi.getOneByUsername(username);
        return CommonResult.data(BeanUtils.copyProperties(userRespDTO, SysUserVO.class));
    }

    /**
     * 根据用户名(多个以逗号拼接)查询用户信息接口
     *
     * @param usernames 用户名(多个以逗号拼接)
     * @return 用户信息
     */
    @OperationLog(module = "工作流-业务控制器", value = "根据用户名(多个以逗号拼接)查询用户信息接口", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "根据用户名(多个以逗号拼接)查询用户信息接口")
    @Parameter(name = "usernames", description = "用户名(多个以逗号拼接)", required = true)
    @GetMapping("/getUserInfoByUsernames")
    public CommonResult<List<SysUserVO>> getUserInfoByUsernames(@RequestParam String usernames) {
        List<String> userNames = Arrays.asList(usernames.split(","));
        List<SysUserRespDTO> respDTOS = sysUserApi.getUserListByUsernames(userNames);
        return CommonResult.data(BeanUtils.copyToList(respDTOS, SysUserVO.class));
    }


    /**
     * 根据用户id查询用户信息接口
     *
     * @param userId 用户id
     * @return 用户信息
     */
    @OperationLog(module = "工作流-业务控制器", value = "根据用户id查询用户信息接口", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "根据用户id查询用户信息接口")
    @Parameter(name = "userId", description = "用户id(多个以逗号拼接)", required = true)
    @GetMapping("/getUserInfoByUserId")
    public CommonResult<SysUserVO> getUserInfoByUserId(@RequestParam String userId) {
        SysUserRespDTO sysUserRespDTO = sysUserApi.getUserById(userId);
        return CommonResult.data(BeanUtils.copyProperties(sysUserRespDTO, SysUserVO.class));
    }


    /**
     * 根据用户id(多个以逗号拼接)查询用户信息接口
     *
     * @param userIds 用户id(多个以逗号拼接)
     * @return 用户信息
     */
    @OperationLog(module = "工作流-业务控制器", value = "根据用户id(多个以逗号拼接)查询用户信息接口", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "根据用户id(多个以逗号拼接)查询用户信息接口")
    @Parameter(name = "userIds", description = "用户id(多个以逗号拼接)", required = true)
    @GetMapping("/getUserInfoByUserIds")
    public CommonResult<List<SysUserVO>> getUserInfoByUserIds(@RequestParam String userIds) {
        List<String> userIdList = Arrays.asList(userIds.split(","));
        List<SysUserRespDTO> respDTOS = sysUserApi.getUserListByIds(userIdList);
        return CommonResult.data(BeanUtils.copyToList(respDTOS, SysUserVO.class));
    }


    /**
     * 查询用户列表
     *
     * @param sysUserQueryBO 用户过滤查询对象
     * @return 用户列表
     */
    @OperationLog(module = "工作流-业务控制器", value = "查询用户列表", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "查询用户列表")
    @PostMapping("/getUserList")
    public CommonResult<List<SysUserVO>> getUserList(@RequestBody SysUserQueryBO sysUserQueryBO) {
        List<SysUserRespDTO> userList = sysUserApi.getUserList(BeanUtils.copyProperties(sysUserQueryBO, SysUserQueryReqDTO.class));
        return CommonResult.data(BeanUtils.copyToList(userList, SysUserVO.class));
    }

    /**
     * 查询用户及关联信息列表（附带部门信息）
     *
     * @param sysUserQueryBO 用户过滤查询对象
     * @return 用户列表
     */
    @OperationLog(module = "工作流-业务控制器", value = "查询用户及关联信息列表", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "查询用户及关联信息列表")
    @PostMapping("/getUserListWithRelation")
    public CommonResult<List<SysUserVO>> getUserListWithRelation(@RequestBody SysUserQueryBO sysUserQueryBO) {
        List<SysUserRespDTO> userListWithRelation = sysUserApi.getUserListWithRelation(BeanUtils.copyProperties(sysUserQueryBO, SysUserQueryReqDTO.class));
        return CommonResult.data(BeanUtils.copyToList(userListWithRelation, SysUserVO.class));
    }

    /**
     * 查询部门列表
     *
     * @param sysDeptQueryBO 部门过滤查询对象
     * @return 部门列表
     */
    @OperationLog(module = "工作流-业务控制器", value = "查询部门列表", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "查询部门列表")
    @PostMapping("/getDeptList")
    public CommonResult<List<SysDeptVO>> getDeptList(@RequestBody SysDeptQueryBO sysDeptQueryBO) {
        List<SysDeptRespDTO> sysDeptRespDTOS = sysDeptApi.getDeptList(BeanUtils.copyProperties(sysDeptQueryBO, SysDeptQueryReqDTO.class));
        return CommonResult.data(BeanUtils.copyToList(sysDeptRespDTOS, SysDeptVO.class));
    }

    /**
     * 查询岗位列表
     *
     * @param sysPostQueryBO 岗位过滤查询对象
     * @return 岗位列表
     */
    @OperationLog(module = "工作流-业务控制器", value = "查询岗位列表", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "查询岗位列表")
    @PostMapping("/getPostList")
    public CommonResult<List<SysPostVO>> getPostList(@RequestBody SysPostQueryBO sysPostQueryBO) {
        List<SysPostRespDTO> respDTOList = sysPostApi.getPostList(BeanUtils.copyProperties(sysPostQueryBO, SysPostQueryReqDTO.class));
        return CommonResult.data(BeanUtils.copyToList(respDTOList, SysPostVO.class));
    }

    /**
     * 查询角色列表
     *
     * @param sysRoleQueryBO 角色过滤查询对象
     * @return 角色列表
     */
    @OperationLog(module = "工作流-业务控制器", value = "查询角色列表", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "查询角色列表")
    @PostMapping("/getRoleList")
    public CommonResult<List<SysRoleVO>> getRoleList(@RequestBody SysRoleQueryBO sysRoleQueryBO) {
        List<SysRoleRespDTO> roleRespDTOS = sysRoleApi.getRoleList(BeanUtils.copyProperties(sysRoleQueryBO, SysRoleQueryReqDTO.class));
        return CommonResult.data(BeanUtils.copyToList(roleRespDTOS, SysRoleVO.class));
    }

    /**
     * 根据部门id查询部门岗位集合
     *
     * @param deptId 部门id
     * @return 部门岗位数据集合
     */
    @OperationLog(module = "工作流-业务控制器", value = "根据部门id查询部门岗位集合", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "根据部门id查询部门岗位集合")
    @Parameter(name = "deptId", description = "部门id", required = true)
    @GetMapping("/getDeptPostListByDeptId")
    public CommonResult<List<SysDeptPostVO>> getDeptPostListByDeptId(@RequestParam(required = false) String deptId) {
        List<SysDeptPostRespDTO> deptPostListByDeptId = sysDeptApi.getDeptPostListByDeptId(deptId);
        return CommonResult.data(BeanUtils.copyToList(deptPostListByDeptId, SysDeptPostVO.class));
    }

    /**
     * 根据岗位id查询岗位信息
     *
     * @param postId 岗位id
     * @return 岗位信息
     */
    @OperationLog(module = "工作流-业务控制器", value = "根据岗位id查询岗位信息", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "根据岗位id查询岗位信息")
    @Parameter(name = "postId", description = "岗位id", required = true)
    @GetMapping("/getPostByPostId")
    public CommonResult<SysPostVO> getPostByPostId(@RequestParam String postId) {
        SysPostRespDTO postRespDTO = sysPostApi.getPostByPostId(postId);
        return CommonResult.data(BeanUtils.copyProperties(postRespDTO, SysPostVO.class));
    }

    /**
     * 根据部门id查询部门信息
     *
     * @param deptId 部门id
     * @return 部门信息
     */
    @OperationLog(module = "工作流-业务控制器", value = "根据部门id查询部门信息", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "根据部门id查询部门信息")
    @Parameter(name = "deptId", description = "部门id", required = true)
    @GetMapping("/getDeptByDeptId")
    public CommonResult<SysDeptVO> getDeptByDeptId(@RequestParam String deptId) {
        SysDeptRespDTO deptByDeptId = sysDeptApi.getDeptByDeptId(deptId);
        return CommonResult.data(BeanUtils.copyProperties(deptByDeptId, SysDeptVO.class));
    }


    /**
     * 根据部门岗位id查询部门岗位数据
     *
     * @param deptPostId 部门岗位id
     * @return 部门岗位数据
     */
    @OperationLog(module = "工作流-业务控制器", value = "根据部门岗位id查询部门岗位数据", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "根据部门岗位id查询部门岗位数据")
    @Parameter(name = "deptPostId", description = "部门岗位id", required = true)
    @GetMapping("/getDeptPostInfoByDeptPostId")
    public CommonResult<SysDeptPostVO> getDeptPostInfoByDeptPostId(@RequestParam(required = false) String deptPostId) {
        SysDeptPostRespDTO deptPostInfoByDeptPostId = sysDeptApi.getDeptPostInfoByDeptPostId(deptPostId);
        return CommonResult.data(BeanUtils.copyProperties(deptPostInfoByDeptPostId, SysDeptPostVO.class));
    }


    /**
     * 根据角色id获取角色信息
     *
     * @param roleId 角色id
     * @return 角色信息
     */
    @OperationLog(module = "工作流-业务控制器", value = "查询角色信息", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "查询角色信息")
    @Parameter(name = "roleId", description = "角色id值", required = true)
    @GetMapping("/getRoleByRoleId")
    public CommonResult<SysRoleVO> getRoleInfoByRoleId(@RequestParam String roleId) {
        SysRoleRespDTO roleRespDTO = sysRoleApi.getRoleById(roleId);
        return CommonResult.data(BeanUtils.copyProperties(roleRespDTO, SysRoleVO.class));
    }

}

package cn.com.sipsg.common.flow.core.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 工作流通知消息Dto对象
 *
 * <AUTHOR>
 * @since 2025/04/14
 */

@Getter
@Setter
@Schema(description = "工作流通知消息Dto对象")
public class FlowMessageDTO {

    /**
     * 消息类型。
     */
    @Schema(description = "消息类型")
    private Integer messageType;

    /**
     * 工单Id。
     */
    @Schema(description = "工单Id")
    private String workOrderId;

    /**
     * 流程名称。
     */
    @Schema(description = "流程名称")
    private String processDefinitionName;

    /**
     * 流程任务名称。
     */
    @Schema(description = "流程任务名称")
    private String taskName;

    /**
     * 更新时间范围过滤起始值(>=)。
     */
    @Schema(description = "updateTime 范围过滤起始值")
    private String updateTimeStart;

    /**
     * 更新时间范围过滤结束值(<=)。
     */
    @Schema(description = "updateTime 范围过滤结束值")
    private String updateTimeEnd;
}

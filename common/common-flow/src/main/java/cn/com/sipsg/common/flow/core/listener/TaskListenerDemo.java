package cn.com.sipsg.common.flow.core.listener;

import org.flowable.engine.delegate.TaskListener;
import org.flowable.engine.impl.el.FixedValue;
import org.flowable.task.service.delegate.DelegateTask;

/**
 * 用户任务监听器调试
 *
 * <AUTHOR>
 * @since 2025/04/14
 **/
public class TaskListenerDemo implements TaskListener {

    // ceshi ceshitwo 两个属性为任务监听器注入属性
    private FixedValue ceshi;

    private FixedValue ceshitwo;

    @Override
    public void notify(DelegateTask delegateTask) {
        //以下为任务监听器注入属性值获取方式,此种方式在任务监听器和执行监听器中都可以作为注入属性值获取方式
        String ceshiText = ceshi.getExpressionText();
        String ceshitwoText = ceshitwo.getExpressionText();
    }
}

package cn.com.sipsg.common.flow.core.pojo.bo;

import cn.com.sipsg.common.validation.group.UpdateGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;

/**
 * 保存BO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "保存BO")
public class FlowRuTaskAbnormalRecordSaveBO {

    /**
     * 主键id
     */
    @Schema(description = "主键id")
    @NotBlank(message = "主键id不能为空", groups = {UpdateGroup.class})
    private String id;

    /**
     * 任务id
     */
    @Schema(description = "任务id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "任务id不能为空")
    private String taskId;

    /**
     * 流程实例id
     */
    @Schema(description = "流程实例id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "流程实例id不能为空")
    private String processInstanceId;

    /**
     * 流程定义id,即每次发版生成的唯一id
     */
    @Schema(description = "流程定义id,即每次发版生成的唯一id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "流程定义id,即每次发版生成的唯一id不能为空")
    private String processDefinitionId;

    /**
     * 节点标识
     */
    @Schema(description = "节点标识", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "节点标识不能为空")
    private String taskKey;

    /**
     * 当前节点任务处理人类型
     */
    @Schema(description = "当前节点任务处理人类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "当前节点任务处理人类型不能为空")
    private String groupType;

    /**
     * 任务处理人配置的值
     */
    @Schema(description = "任务处理人配置的值", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "任务处理人配置的值不能为空")
    private String typeValue;

    /**
     * 流程定义标识
     */
    @Schema(description = "流程定义标识", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "流程定义标识不能为空")
    private String processDefinitionKey;

    /**
     * 节点中文名称
     */
    @Schema(description = "节点中文名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "节点中文名称不能为空")
    private String taskName;

    /**
     * 异常原因
     */
    @Schema(description = "异常原因")
    private String reason;

}
package cn.com.sipsg.common.flow.core.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import com.alibaba.fastjson2.JSONArray;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

/**
 * 加签BO对象
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "加签BO对象")
public class SubmitConsignBO {

    /**
     * 流程实例processInstanceId
     */
    @Schema(description = "processInstanceId")
    @NotNull(message = "processInstanceId不能为空")
    private String processInstanceId;

    /**
     * 任务id
     */
    @Schema(description = "taskId")
    @NotNull(message = "任务taskId不能为空")
    private String taskId;

    /**
     * 加签人，支持多人
     */
    @Schema(description = "加签人")
    @NotNull(message = "加签人不能为空")
    private JSONArray newAssignees;

    /**
     * 是否加签
     */
    @Schema(description = "是否加签")
    private Boolean isAdd;

    /**
    * 审批人审批时指定的部门id数据
    */
    @Schema(description = "审批人审批时指定的部门id数据")
    @NotNull(message = "审批人审批时指定的部门id不能为空")
    private String deptOfApproveUser;

}

package cn.com.sipsg.common.flow.core.mapper;


import cn.com.sipsg.common.flow.core.entity.PostAdjustment;
import cn.com.sipsg.common.mybatis.core.mapper.BaseMapperX;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 岗位调整数据操作访问接口。
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Mapper
public interface PostAdjustmentMapper extends BaseMapperX<PostAdjustment> {

    /**
     * 批量插入对象列表。
     *
     * @param postAdjustmentList 新增对象列表。
     */
    void insertList(List<PostAdjustment> postAdjustmentList);

    /**
     * 获取过滤后的对象列表。
     *
     * @param postAdjustmentFilter 主表过滤对象。
     * @param orderBy 排序字符串，order by从句的参数。
     * @return 对象列表。
     */
    List<PostAdjustment> getPostAdjustmentList(
            @Param("postAdjustmentFilter") PostAdjustment postAdjustmentFilter, @Param("orderBy") String orderBy);
}

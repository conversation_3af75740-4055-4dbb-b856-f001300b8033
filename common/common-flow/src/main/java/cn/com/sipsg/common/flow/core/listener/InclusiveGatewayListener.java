package cn.com.sipsg.common.flow.core.listener;

import cn.hutool.core.map.MapUtil;
import cn.hutool.extra.spring.SpringUtil;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.flowable.bpmn.model.FlowElement;
import org.flowable.bpmn.model.Gateway;
import org.flowable.bpmn.model.SequenceFlow;
import org.flowable.bpmn.model.UserTask;
import org.flowable.engine.TaskService;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.ExecutionListener;

/**
 * 包容网关执行监听器 1.处理工作流驳回经过网关后再审批特殊功能 2.流转路径任务ID（全小写形式，值允许数字字母） +InclusiveGatewayExecutionDecision：包容网关执行决策 变量 例如：流转路径任务ID：Flow19rwjuk  则决策变量名为：flow19rwjuk + InclusiveGatewayExecutionDecision：flow19rwjukInclusiveGatewayExecutionDecision
 *
 * <AUTHOR>
 * @since 2025/04/14
 **/
@Slf4j
public class InclusiveGatewayListener implements ExecutionListener {

    private transient TaskService taskService = SpringUtil.getBean(TaskService.class);

    @Override
    public void notify(DelegateExecution delegateExecution) {
        //flow19rwjukInclusiveGatewayExecutionDecision flow193noh0InclusiveGatewayExecutionDecision
        Map<String, Object> variableMap = MapUtil.newHashMap();
        variableMap.put("flow19rwjukInclusiveGatewayExecutionDecision", false);
        variableMap.put("flow193noh0InclusiveGatewayExecutionDecision", true);
        delegateExecution.setVariables(variableMap);
        Gateway inclusiveGateway = null;
        FlowElement currentFlowElement = delegateExecution.getCurrentFlowElement();
        if (currentFlowElement instanceof Gateway) {
            inclusiveGateway = (Gateway)currentFlowElement;
            List<SequenceFlow> outgoingFlows = inclusiveGateway.getOutgoingFlows();
            for (SequenceFlow outgoingFlow : outgoingFlows) {
                FlowElement targetFlowElement = outgoingFlow.getTargetFlowElement();
                if (targetFlowElement instanceof UserTask) {
                    UserTask userTask = (UserTask)targetFlowElement;
                    // todo 网关驳回再审批处理
                } else {
                    log.warn("非用户任务节点，不参与决断...");
                    continue;
                }
            }
        } else {
            log.warn("非网关接口，不参与决断...");
            return;
        }
    }
}

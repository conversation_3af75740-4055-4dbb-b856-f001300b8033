package cn.com.sipsg.common.flow.core.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 流程的Dto对象
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "流程的Dto对象")
public class FlowEntryDTO {

    /**
     * 主键Id。
     */
    @Schema(description = "主键Id")
    private String entryId;

    /**
     * 流程名称。
     */
    @Schema(description = "流程名称")
    private String processDefinitionName;

    /**
     * 流程标识Key。
     */
    @Schema(description = "流程标识Key")
    private String processDefinitionKey;

    /**
     * 流程分类。
     */
    @Schema(description = "流程分类")
    private String categoryId;

    /**
     * 流程状态。
     */
    @Schema(description = "流程状态")
    private Integer status;

    /**
     * 流程定义的xml。
     */
    @Schema(description = "流程定义的xml")
    private String bpmnXml;

    /**
     * 流程图类型。0: 普通流程图，1: 钉钉风格的流程图。
     */
    @Schema(description = "流程图类型。0: 普通流程图，1: 钉钉风格的流程图。")
    private Integer diagramType;

    /**
     * 绑定表单类型。
     */
    @Schema(description = "绑定表单类型")
    private Integer bindFormType;

    /**
     * 动态表单的页面Id。
     */
    @Schema(description = "动态表单的页面Id")
    private String pageId;

    /**
     * 动态表单的缺省路由名称。
     */
    @Schema(description = "动态表单的缺省路由名称")
    private String defaultRouterName;

    /**
     * 动态表单Id。
     */
    @Schema(description = "动态表单Id")
    private String defaultFormId;

    /**
     * 工单表编码字段的编码规则，如果为空则不计算工单编码。
     */
    @Schema(description = "工单表编码字段的编码规则")
    private String encodedRule;

    /**
     * 流程的自定义扩展数据(JSON格式)。
     */
    @Schema(description = "流程的自定义扩展数据")
    private String extensionData;

    /**
     * 流程发布版本
     */
    @Schema(description = "流程发布版本")
    private Integer publishVersion;
}

package cn.com.sipsg.common.flow.core.mapper;

import cn.com.sipsg.common.flow.core.entity.FlowMessageCandidateIdentity;
import cn.com.sipsg.common.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 流程任务消息的候选身份数据操作访问接口。
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Mapper
public interface FlowMessageCandidateIdentityMapper extends BaseMapperX<FlowMessageCandidateIdentity> {

}

package cn.com.sipsg.common.flow.core.controller.demo;

import cn.com.sipsg.common.exception.BusinessException;
import cn.com.sipsg.common.flow.core.constant.FlowTaskStatus;
import cn.com.sipsg.common.flow.core.entity.PostAdjustment;
import cn.com.sipsg.common.flow.core.pojo.bo.FlowWorkOrderPageBO;
import cn.com.sipsg.common.flow.core.pojo.bo.PostAdjustmentBO;
import cn.com.sipsg.common.flow.core.pojo.bo.PostAdjustmentSaveDraftBO;
import cn.com.sipsg.common.flow.core.pojo.vo.FlowTaskVO;
import cn.com.sipsg.common.flow.core.pojo.vo.FlowWorkOrderVO;
import cn.com.sipsg.common.flow.core.pojo.vo.PostAdjustmentVO;
import cn.com.sipsg.common.flow.core.service.FlowApiService;
import cn.com.sipsg.common.flow.core.service.FlowWorkOrderService;
import cn.com.sipsg.common.flow.core.service.PostAdjustmentService;
import cn.com.sipsg.common.flow.core.util.FlowOperationHelper;
import cn.com.sipsg.common.operationlog.core.annotation.OperationLog;
import cn.com.sipsg.common.operationlog.core.enums.OperationTypeEnum;
import cn.com.sipsg.common.pojo.CommonResult;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.util.AssertUtils;
import cn.com.sipsg.common.util.BeanUtils;
import cn.com.sipsg.common.util.CollectionUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.Parameter;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.Task;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 这边是演示demo，这边是演示demo,这边是演示demo，这边编写了个演示controller，作为参考对象，项目上可以参考用来发起流程，审批任务
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Tag(name = "岗位调整管理接口")
@RestController
@RequestMapping("/flow/demo/postAdjustment")
@RequiredArgsConstructor
public class PostAdjustmentController {

    private final FlowOperationHelper flowOperationHelper;

    private final PostAdjustmentService postAdjustmentService;

    private final FlowApiService flowApiService;

    private final FlowWorkOrderService flowWorkOrderService;

    /**
     * 提交表单数据并发起流程
     *
     * @param postAdjustmentBO 提交表单BO对象
     */
    @OperationLog(module = "岗位调整", value = "提交表单并发起流程", type = OperationTypeEnum.SAVE)
    @Operation(summary = "提交表单数据并发起流程")
    @PostMapping("/saveFormDataAndTakeUserTask")
    public CommonResult<Void> saveFormDataAndTakeUserTask(@RequestBody @Validated PostAdjustmentBO postAdjustmentBO) {
        AssertUtils.isTrue(StrUtil.isBlank(postAdjustmentBO.getFlowTaskCommentDTO().getDeptOfSubmitUser()), "请确认用户所属部门关系！");
        postAdjustmentService.saveNewAndStartProcess(postAdjustmentBO);
        return CommonResult.success();
    }


    /**
     * 更新表单数据并审批用户任务
     *
     * @param postAdjustmentBO 更新表单并审批用户任务BO对象
     */
    @OperationLog(module = "岗位调整", value = "根据用户名查询用户信息接口", type = OperationTypeEnum.UPDATE)
    @Operation(summary = "更新表单数据并审批用户任务")
    @PostMapping("/updatePostAdjustmentAndSubmitUserTask")
    public CommonResult<Void> updatePostAdjustmentAndSubmitUserTask(@RequestBody @Validated PostAdjustmentBO postAdjustmentBO) {
        AssertUtils.isTrue(StrUtil.isBlank(postAdjustmentBO.getFlowTaskCommentDTO().getDeptOfApproveUser()), "请确认用户所属部门关系！");
        postAdjustmentService.updateAndTakeTask(postAdjustmentBO);
        return CommonResult.success();
    }


    /**
     * 获取当前流程实例对应的业务详情数据
     *
     * @param processDefinitionKey 流程标识key
     * @param processInstanceId    流程实例id
     * @param taskId               任务id
     * @return 岗位申请表单信息
     */
    @OperationLog(module = "岗位调整", value = "获取当前流程实例对应的业务详情数据", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "获取当前流程实例对应的业务详情数据")
    @Parameters({
        @Parameter(name = "processDefinitionKey", description = "流程标识key", required = true, example = "1111"),
        @Parameter(name = "processInstanceId", description = "流程实例id", required = true, example = "1111"),
        @Parameter(name = "taskId", description = "任务id", required = true, example = "1111")
    })
    @GetMapping("/viewTaskBusinessData")
    public CommonResult<PostAdjustmentVO> viewTaskBusinessData(@RequestParam String processDefinitionKey, @RequestParam String processInstanceId, @RequestParam String taskId) {
        // 验证流程任务的合法性。
        Task task = flowApiService.getProcessInstanceActiveTask(processInstanceId, taskId);
        flowOperationHelper.verifyAndGetRuntimeTaskInfo(task);
        flowOperationHelper.verifyAndGetRuntimeTaskInfo(task);
        ProcessInstance instance = flowApiService.getProcessInstance(processInstanceId);
        if (!StrUtil.equals(instance.getProcessDefinitionKey(), processDefinitionKey)) {
            throw new BusinessException("数据验证失败，请求流程标识与流程实例不匹配，请核对！");
        }
        String dataId = instance.getBusinessKey();
        PostAdjustment postAdjustment = postAdjustmentService.getById(dataId);
        AssertUtils.isTrue(postAdjustment == null, "数据不存在，请传递正确参数");
        return CommonResult.data(BeanUtils.copyProperties(postAdjustment, PostAdjustmentVO.class));
    }


    /**
     * 获取已办任务和历史任务流程实例对应的表单业务数据
     *
     * @param processDefinitionKey 流程标识key
     * @param processInstanceId    流程实例id
     * @return 岗位表单信息
     */
    @OperationLog(module = "岗位调整", value = "获取已办任务和历史任务流程实例对应的表单业务数据", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "获取已办任务和历史任务流程实例对应的表单业务数据")
    @Parameters({
        @Parameter(name = "processDefinitionKey", description = "流程标识key", required = true, example = "1111"),
        @Parameter(name = "processInstanceId", description = "流程实例id", required = true, example = "1111")
    })
    @GetMapping("/viewHistoricTaskBusinessData")
    public CommonResult<PostAdjustmentVO> viewHistoricTaskBusinessData(@RequestParam String processDefinitionKey, @RequestParam String processInstanceId) {
        HistoricProcessInstance instance = flowOperationHelper.verifyAndGetHistoricProcessInstance(processInstanceId, null);
        if (!StrUtil.equals(instance.getProcessDefinitionKey(), processDefinitionKey)) {
            throw new BusinessException("数据验证失败，请求流程标识与流程实例不匹配，请核对！");
        }
        String businessKey = instance.getBusinessKey();
        if (StrUtil.isBlank(businessKey)) {
            // 对于没有提交过任何用户任务的场景，可直接返回空数据。
            return CommonResult.data(null);
        }
        PostAdjustment postAdjustment = postAdjustmentService.getById(businessKey);
        AssertUtils.isTrue(postAdjustment == null, "数据不存在，请传递正确参数");
        return CommonResult.data(BeanUtils.copyProperties(postAdjustment, PostAdjustmentVO.class));
    }

    /**
     * 根据流程定义标识processDefinitionKey分页查询工单列表
     *
     * @param flowWorkOrderPageBO 工单列表查询对象
     * @return 工单列表分页对象
     */
    @OperationLog(module = "岗位调整", value = "根据流程定义标识processDefinitionKey分页查询工单列表", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "根据流程定义标识processDefinitionKey分页查询工单列表")
    @PostMapping("/workOrderPageByProcessDefinitionKey")
    public CommonResult<CommonPageVO<FlowWorkOrderVO>> workOrderPageByProcessDefinitionKey(@Validated @RequestBody FlowWorkOrderPageBO flowWorkOrderPageBO) {
        CommonPageVO<FlowWorkOrderVO> page = flowWorkOrderService.page(flowWorkOrderPageBO);
        if (CollectionUtils.isAnyEmpty(page.getRecords())) {
            return CommonResult.data(new CommonPageVO<>());
        }
        //封装工单上一次审批状态字典
        flowOperationHelper.buildWorkOrderApprovalStatus(flowWorkOrderPageBO.getProcessDefinitionKey(), page.getRecords());
        // 根据工单的提交用户名获取用户的显示名称，便于前端显示
        flowWorkOrderService.fillUserShowNameByLoginName(page.getRecords());
        // 组装工单中需要返回给前端的流程任务数据。
        flowOperationHelper.buildWorkOrderTaskInfo(page.getRecords());
        // 获取非草稿工单中的流程实例businessKey，并作为主键Id集合去查询主表中的数据，如果有从表会自动关联从表数据
        Set<String> businessKeySet = page.getRecords().stream().filter(c -> c.getBusinessKey() != null).map(FlowWorkOrderVO::getBusinessKey).collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(businessKeySet)) {
            // 将业务表单数据与工单数据绑定，之后再返回给前端。
            this.buildWorkOrderBusinessData(page.getRecords(), businessKeySet);
        }
        List<FlowWorkOrderVO> flowWorkOrderVOS = page.getRecords().stream().filter(c -> c.getFlowStatus().equals(FlowTaskStatus.DRAFT)).collect(Collectors.toList());
        //将业务的表单数据封装到masterData字段上
        postAdjustmentService.buildDraftData(flowWorkOrderVOS);
        return CommonResult.data(page);
    }

    /**
     * 保存一个草稿信息
     *
     * @param postAdjustmentSaveDraftBO 岗位调整保存草稿信息
     * @return 草稿保存返回对象
     */
    @OperationLog(module = "岗位调整", value = "保存一个草稿信息", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "保存一个草稿信息")
    @PostMapping("/saveOrUpdateDraft")
    public CommonResult<FlowTaskVO> startAndSaveDraft(@Validated @RequestBody PostAdjustmentSaveDraftBO postAdjustmentSaveDraftBO) {
        postAdjustmentService.saveOrUpdateDraft(postAdjustmentSaveDraftBO);
        return CommonResult.success();
    }


    private void buildWorkOrderBusinessData(List<FlowWorkOrderVO> records, Set<String> businessKeySet) {
        List<PostAdjustment> postAdjustmentList = postAdjustmentService.listByIdsDeep(businessKeySet);
        Map<String, PostAdjustment> adjustmentMap = postAdjustmentList.stream().collect(Collectors.toMap(PostAdjustment::getId, c -> c));
        records.stream().forEach(e -> {
            PostAdjustment postAdjustment = adjustmentMap.get(e.getBusinessKey());
            if (null != postAdjustment) {
                e.setFormData(JSONObject.from(postAdjustment));
            }
        });
    }

}

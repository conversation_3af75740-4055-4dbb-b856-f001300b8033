package cn.com.sipsg.common.flow.core.service;

import cn.com.sipsg.common.flow.core.entity.FlowEntryVariable;
import cn.com.sipsg.common.flow.core.pojo.dto.FlowEntryVariableBO;
import cn.com.sipsg.common.flow.core.pojo.vo.FlowEntryVariableVO;
import cn.com.sipsg.common.mybatis.core.service.BaseServiceX;
import java.util.List;
import java.util.Set;

/**
 * 流程变量数据操作服务接口。
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public interface FlowEntryVariableService extends BaseServiceX<FlowEntryVariable> {

    /**
     * 保存新增对象。
     *
     * @param flowEntryVariable 新增对象。
     * @return 返回新增对象。
     */
    FlowEntryVariable saveNew(FlowEntryVariable flowEntryVariable);

    /**
     * 更新数据对象。
     *
     * @param flowEntryVariableBO 更新的BO对象
     * @return 成功返回true，否则false。
     */
    boolean update(FlowEntryVariableBO flowEntryVariableBO);

    /**
     * 删除指定数据。
     *
     * @param variableId 主键Id。
     * @return 成功返回true，否则false。
     */
    boolean remove(String variableId);

    /**
     * 删除指定流程Id的所有变量。
     *
     * @param entryId 流程Id。
     */
    void removeByEntryId(String entryId);

    /**
     * 获取单表查询结果。由于没有关联数据查询，因此在仅仅获取单表数据的场景下，效率更高。 如果需要同时获取关联数据，请移步(getFlowEntryVariableListWithRelation)方法。
     *
     * @param filter  过滤对象。
     * @param orderBy 排序参数。
     * @return 查询结果集。
     */
    List<FlowEntryVariable> getFlowEntryVariableList(FlowEntryVariable filter, String orderBy);

    /**
     * 根据 entryId 查询流程变量参数
     *
     * @param entryId 流程变量id
     * @return 流程VO对象
     */
    List<FlowEntryVariableVO> getFlowEntryVariableListByEntryId(String entryId);

    /**
     * 根据variableId 获取FlowEntryVariable列表
     *
     * @param variableIdSet 主键id集合
     * @return FlowEntryVariable列表
     */
    List<FlowEntryVariable> getFlowEntryVariableListByIdIn(Set<String> variableIdSet);
}

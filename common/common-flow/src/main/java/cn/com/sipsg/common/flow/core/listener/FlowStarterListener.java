package cn.com.sipsg.common.flow.core.listener;

import cn.com.sipsg.common.flow.core.constant.FlowConstant;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.ExecutionListener;

/**
 * 流程节点拒绝到流程发起节点时，补偿流程发起人变量参数，适用于以下情况 1.流程发起节点审批对象配置为：流程发起人； 2.流程从任一任务节点通过 ”拒绝“ 操作流转到流程发起节点
 *
 * <AUTHOR>
 * @since 2025/04/14
 **/
public class FlowStarterListener implements ExecutionListener {

    @Override
    public void notify(DelegateExecution delegateExecution) {
        //拒绝重新办理时，补偿流程发起用户变量数据
        delegateExecution.setVariable(FlowConstant.PROC_INSTANCE_START_USER_NAME_VAR, delegateExecution.getVariable(FlowConstant.PROC_INSTANCE_INITIATOR_VAR));
    }
}

package cn.com.sipsg.common.flow.core.api.impl;

import cn.com.sipsg.common.exception.BusinessException;
import cn.com.sipsg.common.flow.core.api.FlowableApi;
import cn.com.sipsg.common.flow.core.constant.FlowApprovalType;
import cn.com.sipsg.common.flow.core.constant.FlowConstant;
import cn.com.sipsg.common.flow.core.constant.FlowTaskStatus;
import cn.com.sipsg.common.flow.core.entity.FlowEntry;
import cn.com.sipsg.common.flow.core.entity.FlowMultiInstanceTrans;
import cn.com.sipsg.common.flow.core.entity.FlowTaskComment;
import cn.com.sipsg.common.flow.core.entity.FlowTaskExt;
import cn.com.sipsg.common.flow.core.entity.FlowWorkOrder;
import cn.com.sipsg.common.flow.core.pojo.dto.FlowReqDTO;
import cn.com.sipsg.common.flow.core.pojo.dto.FlowRespDTO;
import cn.com.sipsg.common.flow.core.pojo.dto.FlowSubmitUserReqDTO;
import cn.com.sipsg.common.flow.core.pojo.dto.FlowTaskExtRespDTO;
import cn.com.sipsg.common.flow.core.pojo.dto.FlowUserInfoRespDTO;
import cn.com.sipsg.common.flow.core.pojo.dto.FlowWorkOrderReqDTO;
import cn.com.sipsg.common.flow.core.pojo.dto.FlowWorkOrderRespDTO;
import cn.com.sipsg.common.flow.core.pojo.vo.FlowUserInfoVO;
import cn.com.sipsg.common.flow.core.pojo.vo.FlowWorkOrderVO;
import cn.com.sipsg.common.flow.core.service.FlowApiService;
import cn.com.sipsg.common.flow.core.service.FlowMultiInstanceTransService;
import cn.com.sipsg.common.flow.core.service.FlowTaskCommentService;
import cn.com.sipsg.common.flow.core.service.FlowTaskExtService;
import cn.com.sipsg.common.flow.core.service.FlowWorkOrderService;
import cn.com.sipsg.common.flow.core.util.FlowOperationHelper;
import cn.com.sipsg.common.security.core.util.SecurityUtils;
import cn.com.sipsg.common.util.AssertUtils;
import cn.com.sipsg.common.util.BeanUtils;
import cn.com.sipsg.common.util.ValidationUtils;
import cn.com.sipsg.module.system.api.upms.SysUserApi;
import cn.com.sipsg.module.system.api.upms.dto.SysUserRespDTO;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONObject;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import javax.validation.Validator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.flowable.engine.history.HistoricActivityInstance;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.Task;
import org.flowable.task.api.TaskInfo;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 工作流接口实现类
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FlowableApiImpl implements FlowableApi {

    @Autowired
    private FlowOperationHelper flowOperationHelper;

    @Autowired
    private FlowApiService flowApiService;

    @Autowired
    private FlowWorkOrderService flowWorkOrderService;

    @Autowired
    private Validator validator;

    @Autowired
    private FlowTaskExtService flowTaskExtService;

    @Autowired
    private FlowMultiInstanceTransService flowMultiInstanceTransService;

    @Autowired
    private FlowTaskCommentService flowTaskCommentService;

    @Autowired
    private SysUserApi sysUserApi;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FlowRespDTO startAndTakeUserTask(FlowReqDTO flowReqDTO) {
        ValidationUtils.validate(validator, flowReqDTO);
        // 验证流程管理数据状态的合法性。
        FlowEntry flowEntry = flowOperationHelper.verifyFullAndGetFlowEntry(flowReqDTO.getProcessDefinitionKey());
        String processDefinitionId = flowEntry.getMainFlowEntryPublish().getProcessDefinitionId();
        FlowTaskComment flowTaskComment = BeanUtils.copyProperties(flowReqDTO.getFlowTaskCommentDTO(), FlowTaskComment.class);
        flowTaskComment.setDeptOfApproveUser(flowReqDTO.getFlowTaskCommentDTO().getDeptOfSubmitUser());
        JSONObject taskVariableData = flowReqDTO.getTaskVariableData();
        //审批人所属部门信息
        taskVariableData.put(FlowConstant.DEPT_OF_APPROVE_USER, flowReqDTO.getFlowTaskCommentDTO().getDeptOfSubmitUser());
        // 这里把传阅数据放到任务变量中，是为了避免给流程数据操作方法增加额外的方法调用参数。
        if (MapUtil.isNotEmpty(flowReqDTO.getCopyData())) {
            if (flowReqDTO.getTaskVariableData() == null) {
                taskVariableData = new JSONObject();
            }
            taskVariableData.put(FlowConstant.COPY_DATA_KEY, flowReqDTO.getCopyData());
        }
        String token = StpUtil.getTokenValue();
        //将Token值封装进任务变量参数里，为了：用在节点完成后，业务如果需要触发FlowNodeFinishedListener任务完成后回调业务接口，业务回调需要携带token值(Token只用来做接口鉴权，暂不考虑用来根据Token解析为具体的人)
        taskVariableData.put(FlowConstant.AUTHORIZATION_HEADER, token);
        //启动流程
        ProcessInstance instance = flowApiService.start(processDefinitionId, flowReqDTO.getBusinessKey(), flowReqDTO.getFlowTaskCommentDTO().getDeptOfSubmitUser());
        log.info("processInstance的id:{}", instance.getProcessInstanceId());
        if (StringUtils.isNotBlank(flowReqDTO.getWorkOrderId())) {
            //不为null表示之前保存了草稿数据生成了工单，此时需要将启动生成的流程数据维护进工单记录中
            FlowWorkOrder flowWorkOrder = flowWorkOrderService.getById(flowReqDTO.getWorkOrderId());
            flowWorkOrder.setProcessInstanceId(instance.getId());
            flowWorkOrder.setDeptOfSubmitUser(flowReqDTO.getFlowTaskCommentDTO().getDeptOfSubmitUser());
            flowWorkOrder.setSubmitUsername(SecurityUtils.getLoginUserId());
            flowWorkOrder.setFlowStatus(FlowTaskStatus.SUBMITTED);
            flowWorkOrderService.updateById(flowWorkOrder);
        } else {
            //保存工单数据
            flowWorkOrderService.saveNew(instance, flowReqDTO.getBusinessKey(), null, null, flowReqDTO.getFlowTaskCommentDTO().getDeptOfSubmitUser());
        }
        //处理第一个任务节点
        flowApiService.takeFirstTask(instance.getProcessInstanceId(), flowTaskComment, taskVariableData);
        // 这里需要在创建工单后再次更新一下工单状态，在flowApiService.completeTask中的更新，
        // 因为当时没有创建工单对象，更新会不起任何作用，所以这里要补偿一下，补偿该节点审批意见
        // 1-同意 2-拒绝 3-驳回 4-会签同意 5-会签拒绝
        Integer approvalStatus = MapUtil.getInt(taskVariableData, FlowConstant.LATEST_APPROVAL_STATUS_KEY);
        FlowWorkOrder flowWorkOrder = flowWorkOrderService.updateLatestApprovalStatusByProcessInstanceId(instance.getId(), approvalStatus);
        return new FlowRespDTO(instance.getProcessInstanceId(), instance.getProcessDefinitionId(), instance.getProcessDefinitionKey(), flowWorkOrder.getWorkOrderId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitUserTask(FlowSubmitUserReqDTO flowSubmitUserReqDTO) {
        String token = StpUtil.getTokenValue();
        ValidationUtils.validate(validator, flowSubmitUserReqDTO);
        AssertUtils.isTrue(StringUtils.isBlank(flowSubmitUserReqDTO.getFlowTaskCommentDTO().getDeptOfApproveUser()), "审批人审批时选的所属部门不能为空");
        String processInstanceId = flowSubmitUserReqDTO.getProcessInstanceId();
        AssertUtils.isTrue(StringUtils.isBlank(processInstanceId), "请传递流程实例processInstanceId及对应的值");
        JSONObject copyData = flowSubmitUserReqDTO.getCopyData();
        JSONObject taskVariableData = flowSubmitUserReqDTO.getTaskVariableData();
        Task task = flowOperationHelper.verifySubmitAndGetTask(processInstanceId, flowSubmitUserReqDTO.getTaskId(), flowSubmitUserReqDTO.getFlowTaskCommentDTO());
        ProcessInstance instance = flowApiService.getProcessInstance(processInstanceId);
        if (!StrUtil.equals(instance.getProcessDefinitionKey(), flowSubmitUserReqDTO.getProcessDefinitionKey())) {
            throw new BusinessException("数据验证失败，请求流程标识与流程实例不匹配，请核对!");
        }
        FlowTaskComment flowTaskComment = BeanUtils.copyProperties(flowSubmitUserReqDTO.getFlowTaskCommentDTO(), FlowTaskComment.class);
        //此处针对审批用户存在多部门情况，明确当前用户所属部门关系，将部门关系维护到taskVariableData
        taskVariableData.put(FlowConstant.DEPT_OF_APPROVE_USER, flowSubmitUserReqDTO.getFlowTaskCommentDTO().getDeptOfApproveUser());
        //流程全部结束时会触发业务的回调，即会在FlowEndInteractionBusinessListener调用业务的接口，业务接口也需要封装token，此处是把Token作为任务变量存储在act_ru_variable里
        taskVariableData.put(FlowConstant.AUTHORIZATION_HEADER, token);
        //将审批操作类型，更新至任务变量中，触发业务回调时需要将审批对应的类型返回
        taskVariableData.put(FlowConstant.OPERATION_TYPE_VAR, flowTaskComment.getApprovalType());
        // 这里把传阅数据放到任务变量中，是为了避免给流程数据操作方法增加额外的方法调用参数。
        if (MapUtil.isNotEmpty(copyData)) {
            if (taskVariableData == null) {
                taskVariableData = new JSONObject();
            }
            taskVariableData.put(FlowConstant.COPY_DATA_KEY, copyData);
        }
        //开始做流程审批处理
        int flowStatus = FlowTaskStatus.APPROVING;
        if (flowTaskComment.getApprovalType().equals(FlowApprovalType.REFUSE)) {
            flowStatus = FlowTaskStatus.REFUSED;
        } else if (flowTaskComment.getApprovalType().equals(FlowApprovalType.STOP)) {
            flowStatus = FlowTaskStatus.FINISHED;
        }
        if (flowTaskComment.getApprovalType().equals(FlowApprovalType.STOP)) {
            Integer s = MapUtil.getInt(taskVariableData, FlowConstant.LATEST_APPROVAL_STATUS_KEY);
            flowWorkOrderService.updateLatestApprovalStatusByProcessInstanceId(task.getProcessInstanceId(), s);
            flowApiService.stopProcessInstance(task.getProcessInstanceId(), flowTaskComment.getTaskComment(), flowStatus);
        } else {
            flowWorkOrderService.updateFlowStatusByProcessInstanceId(task.getProcessInstanceId(), flowStatus, null);
            flowApiService.completeTask(task, flowTaskComment, taskVariableData, null);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void stopProcessInstance(String processInstanceId, String stopReason) {
        AssertUtils.isTrue(StringUtils.isBlank(processInstanceId), "请传递流程实例processInstanceId及对应的值");
        AssertUtils.isTrue(StringUtils.isBlank(processInstanceId), "请填写终止原因");
        flowApiService.stopProcessInstance(processInstanceId, stopReason, false);
    }

    @Override
    public List<FlowTaskExtRespDTO> getFlowTaskExt(String processDefinitionId, String taskKey) {
        List<FlowTaskExt> flowTaskExts = flowTaskExtService.getNodeInfoByProcessDefinitioniIdAndTaskKey(processDefinitionId, taskKey);
        return BeanUtils.copyToList(flowTaskExts, FlowTaskExtRespDTO.class);
    }

    @Override
    public List<FlowUserInfoRespDTO> viewTaskUserInfo(String processDefinitionId, String processInstanceId, String taskId, Boolean historic) {
        AssertUtils.isTrue(StringUtils.isBlank(processDefinitionId) || StringUtils.isBlank(processInstanceId)
            || StringUtils.isBlank(taskId) || historic == null, "请完整传递必要的参数！");
        TaskInfo taskInfo;
        HistoricTaskInstance hisotricTask;
        if (BooleanUtil.isFalse(historic)) {
            taskInfo = flowApiService.getTaskById(taskId);
            if (taskInfo == null) {
                hisotricTask = flowApiService.getHistoricTaskInstance(processInstanceId, taskId);
                taskInfo = hisotricTask;
                historic = true;
            }
        } else {
            hisotricTask = flowApiService.getHistoricTaskInstance(processInstanceId, taskId);
            taskInfo = hisotricTask;
        }
        if (taskInfo == null) {
            throw new BusinessException("数据验证失败，任务Id不存在！");
        }
        String taskKey = taskInfo.getTaskDefinitionKey();
        FlowTaskExt taskExt = flowTaskExtService.getByProcessDefinitionIdAndTaskId(processDefinitionId, taskKey);
        boolean isMultiInstanceTask = flowApiService.isMultiInstanceTask(taskInfo.getProcessDefinitionId(), taskKey);
        List<FlowUserInfoVO> resultUserInfoList =
            flowTaskExtService.getCandidateUserInfoList(processInstanceId, taskExt, taskInfo, isMultiInstanceTask, historic);
        if (BooleanUtil.isTrue(historic) || isMultiInstanceTask) {
            List<FlowTaskComment> taskCommentList = buildApprovedFlowTaskCommentList(taskInfo, isMultiInstanceTask);
            Map<String, FlowUserInfoVO> resultUserInfoMap =
                resultUserInfoList.stream().collect(Collectors.toMap(FlowUserInfoVO::getUsername, c -> c));
            for (FlowTaskComment taskComment : taskCommentList) {
                FlowUserInfoVO flowUserInfoVo = resultUserInfoMap.get(taskComment.getCreateUserId());
                if (flowUserInfoVo != null) {
                    flowUserInfoVo.setLastApprovalTime(taskComment.getCreateTime());
                }
            }
        }
        return BeanUtils.copyToList(resultUserInfoList, FlowUserInfoRespDTO.class);
    }


    @Override
    public List<FlowUserInfoRespDTO> viewTaskUserInfoByTaskDefKey(String processDefinitionId, String processInstanceId, String taskDefKeys, Boolean historic) {
        AssertUtils.isTrue(StringUtils.isBlank(processDefinitionId) || StringUtils.isBlank(processInstanceId)
            || StringUtils.isBlank(taskDefKeys) || historic == null, "请完整传递必要的参数！");
        List<FlowUserInfoRespDTO> resultFlowUserInfoVoList = CollUtil.newArrayList();
        List<String> taskDefKeyList = StrUtil.split(taskDefKeys, ",");
        if (CollUtil.isNotEmpty(taskDefKeyList)) {
            for (String taskDefKey : taskDefKeyList) {
                List<Task> taskList = flowApiService.getTaskByTaskDefKeyAndProcessInstanceId(taskDefKey, processInstanceId);
                if (CollUtil.isNotEmpty(taskList)) {
                    Task taskInfo = taskList.get(0);
                    AssertUtils.isTrue(ObjectUtil.isNull(taskInfo), "数据验证失败，任务或者流程不存在！");
                    String taskKey = taskInfo.getTaskDefinitionKey();
                    FlowTaskExt taskExt = flowTaskExtService.getByProcessDefinitionIdAndTaskId(processDefinitionId, taskKey);
                    boolean isMultiInstanceTask = flowApiService.isMultiInstanceTask(taskInfo.getProcessDefinitionId(), taskKey);
                    List<FlowUserInfoVO> resultUserInfoList =
                        flowTaskExtService.getCandidateUserInfoList(processInstanceId, taskExt, taskInfo, isMultiInstanceTask, historic);
                    CollUtil.addAll(resultFlowUserInfoVoList, resultUserInfoList);
                    //针对于有些节点通过字段appointApprovalUser以动态指定审批人的形式指定审批用户的，以上代码逻辑是查询不到的，需要去act_ru_variable表里进行查询，审批人封装在text字段里
                    //对于三部的项目，一个流程中会有很多节点都是动态指定的审批人，指定的字段是不同的字段，所以每个节点要记录下自己动态审批人指定的是什么字段，这里是让业务同事将字段配置在了其他设置的扩展属性里
                    //通过字段去act_ru_variable表里进行查询，获取指定的审批人是谁
                    //JSONObject extraDataJson = JSON.parseObject(taskExt.getExtraDataJson());
                    com.alibaba.fastjson.JSONObject extraDataJson = JSON.parseObject(taskExt.getExtraDataJson());
                    if (null != extraDataJson) {
                        Set<String> extraFields = extraDataJson.keySet();
                        log.info("节点：{}，extraFields字段为：{}", taskDefKey, extraFields.toString());
                        extraFields.stream().forEach(extraField -> {
                            Object candidateUsernames = flowApiService.getTaskVariableStringWithSafe(taskInfo.getId(), extraField);
                            if (null != candidateUsernames) {
                                Set<String> approveNames = new HashSet<>();
                                approveNames.add(candidateUsernames.toString());
                                List<SysUserRespDTO> userListByUsernames = sysUserApi.getUserListByUsernames(approveNames);
                                List<FlowUserInfoRespDTO> userInfoList = BeanUtils.copyToList(userListByUsernames, FlowUserInfoRespDTO.class);
                                resultFlowUserInfoVoList.addAll(userInfoList);
                            }
                        });
                    }
                }
            }
        }
        return resultFlowUserInfoVoList;
    }

    @Override
    public FlowWorkOrderRespDTO getWorkOrderInfo(FlowWorkOrderReqDTO flowWorkOrderReqDTO) {
        if (ObjectUtil.isNull(flowWorkOrderReqDTO)) {
            flowWorkOrderReqDTO = new FlowWorkOrderReqDTO();
        }
        List<FlowWorkOrderVO> flowWorkOrderList = BeanUtils.copyToList(flowWorkOrderService.getFlowWorkOrderList(flowWorkOrderReqDTO, null), FlowWorkOrderVO.class);
        if (CollUtil.isEmpty(flowWorkOrderList)) {
            return null;
        }
        //根据工单上一节点审批结果状态码，补充审批结果
        //1-同意 2-拒绝 3-驳回 4-会签同意 5-会签拒绝
        flowOperationHelper.buildWorkOrderApprovalStatus(flowWorkOrderList.get(0).getProcessDefinitionKey(), flowWorkOrderList);
        // 组装工单中需要返回给前端的流程任务数据。
        flowOperationHelper.buildWorkOrderTaskInfo(flowWorkOrderList);
        return BeanUtils.copyProperties(flowWorkOrderList.get(0), FlowWorkOrderRespDTO.class);
    }

    @Override
    public JSONObject listTasksByProcessInstanceId(String processInstanceId) {
        AssertUtils.isTrue(StringUtils.isBlank(processInstanceId), "请传递查询参数");
        //获取流程实例当前已经办理的节点
        List<HistoricActivityInstance> activityInstanceList =
            flowApiService.getHistoricActivityInstanceList(processInstanceId);
        Set<HistoricActivityInstance> finishedTaskSet = activityInstanceList.stream()
            .filter(s -> !StrUtil.equals(s.getActivityType(), "sequenceFlow"))
            .collect(Collectors.toSet());
        //获取流程实例当前正在待办的节点
        List<HistoricActivityInstance> unfinishedTaskList =
            flowApiService.getHistoricUnfinishedInstanceList(processInstanceId);
        JSONObject jsonData = new JSONObject();
        jsonData.put("finishedTaskSet", finishedTaskSet);
        jsonData.put("unfinishedTaskSet", unfinishedTaskList);
        return jsonData;
    }

    private List<FlowTaskComment> buildApprovedFlowTaskCommentList(TaskInfo taskInfo, boolean isMultiInstanceTask) {
        List<FlowTaskComment> taskCommentList;
        if (isMultiInstanceTask) {
            String multiInstanceExecId;
            FlowMultiInstanceTrans trans = flowMultiInstanceTransService.getByExecutionId(taskInfo.getExecutionId(), taskInfo.getId());
            if (trans != null) {
                multiInstanceExecId = trans.getMultiInstanceExecId();
            } else {
                multiInstanceExecId = flowApiService.getExecutionVariableStringWithSafe(
                    taskInfo.getExecutionId(), FlowConstant.MULTI_SIGN_TASK_EXECUTION_ID_VAR);
            }
            taskCommentList = flowTaskCommentService.getFlowTaskCommentListByMultiInstanceExecId(multiInstanceExecId);
        } else {
            taskCommentList = flowTaskCommentService.getFlowTaskCommentListByExecutionId(
                taskInfo.getProcessInstanceId(), taskInfo.getId(), taskInfo.getExecutionId());
        }
        return taskCommentList;
    }

}

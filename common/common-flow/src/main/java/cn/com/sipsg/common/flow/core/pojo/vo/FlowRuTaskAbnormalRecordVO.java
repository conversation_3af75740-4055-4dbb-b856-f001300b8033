package cn.com.sipsg.common.flow.core.pojo.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fhs.core.trans.vo.VO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * VO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "VO")
public class FlowRuTaskAbnormalRecordVO implements VO {

    /**
     * 主键id
     */
    @TableId("id")
    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED)
    private String id;

    /**
     * 任务id
     */
    @Schema(description = "任务id", requiredMode = Schema.RequiredMode.REQUIRED)
    private String taskId;

    /**
     * 流程实例id
     */
    @Schema(description = "流程实例id", requiredMode = Schema.RequiredMode.REQUIRED)
    private String processInstanceId;

    /**
     * 流程定义id,即每次发版生成的唯一id
     */
    @Schema(description = "流程定义id,即每次发版生成的唯一id", requiredMode = Schema.RequiredMode.REQUIRED)
    private String processDefinitionId;

    /**
     * 节点标识
     */
    @Schema(description = "节点标识", requiredMode = Schema.RequiredMode.REQUIRED)
    private String taskKey;

    /**
     * 当前节点任务处理人类型
     */
    @Schema(description = "当前节点任务处理人类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private String groupType;

    /**
     * 任务处理人配置的值
     */
    @Schema(description = "任务处理人配置的值", requiredMode = Schema.RequiredMode.REQUIRED)
    private String typeValue;

    /**
     * 流程定义标识
     */
    @Schema(description = "流程定义标识", requiredMode = Schema.RequiredMode.REQUIRED)
    private String processDefinitionKey;

    /**
     * 流程名称。
     */
    private String processDefinitionName;

    /**
     * 节点中文名称
     */
    @Schema(description = "节点中文名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String taskName;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 异常原因
     */
    @Schema(description = "异常原因")
    private String reason;

    /**
     * 数据状态，0：异常未处理，1：已干预操作
     */
    @Schema(description = "数据状态，0：异常未处理，1：已干预操作")
    private Integer status;

}
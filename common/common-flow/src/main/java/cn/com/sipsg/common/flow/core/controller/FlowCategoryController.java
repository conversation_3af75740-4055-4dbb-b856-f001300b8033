package cn.com.sipsg.common.flow.core.controller;

import cn.com.sipsg.common.flow.core.pojo.bo.FlowCategoryBO;
import cn.com.sipsg.common.flow.core.pojo.bo.FlowCategoryPageBO;
import cn.com.sipsg.common.flow.core.pojo.bo.FlowCategorySaveBO;
import cn.com.sipsg.common.flow.core.pojo.vo.FlowCategoryVO;
import cn.com.sipsg.common.flow.core.service.FlowCategoryService;
import cn.com.sipsg.common.operationlog.core.annotation.OperationLog;
import cn.com.sipsg.common.operationlog.core.enums.OperationTypeEnum;
import cn.com.sipsg.common.pojo.CommonResult;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.validation.AddValidated;
import cn.com.sipsg.common.validation.UpdateValidated;
import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 工作流流程分类Controller
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Tag(name = "工作流-流程分类控制器")
@RestController
@RequiredArgsConstructor
@RequestMapping("/flow/flowCategory")
public class FlowCategoryController {

    private final FlowCategoryService flowCategoryService;

    /**
     * 新增FlowCategory数据
     *
     * @param bo 流程分类新增bo
     * @return 流程分类id
     */
    @OperationLog(module = "工作流流程分类", value = "新增分类编码", type = OperationTypeEnum.SAVE)
    @Operation(summary = "新增流程分类")
    @SaCheckPermission(value = "flow:category:all")
    @PostMapping("/save")
    public CommonResult<String> save(@AddValidated @RequestBody FlowCategorySaveBO bo) {
        String cateGoryId = flowCategoryService.save(bo);
        return CommonResult.data(cateGoryId);
    }

    /**
     * 编辑流程分类
     *
     * @param bo 流程分类新增bo
     */
    @OperationLog(module = "工作流流程分类", value = "编辑分类编码", type = OperationTypeEnum.UPDATE)
    @Operation(summary = "编辑流程分类")
    @SaCheckPermission(value = "flow:category:all")
    @PostMapping("/update")
    public CommonResult update(@UpdateValidated @RequestBody FlowCategorySaveBO bo) {
        flowCategoryService.update(bo);
        return CommonResult.success();
    }

    /**
     * 删除FlowCategory数据
     *
     * @param categoryId 删除对象主键Id
     */
    @OperationLog(module = "工作流流程分类", value = "删除分类编码", type = OperationTypeEnum.DELETE)
    @Operation(summary = "删除流程分类")
    @Parameter(name = "categoryId", description = "流程分类ID", required = true, example = "1111")
    @SaCheckPermission(value = "flow:category:all")
    @PostMapping("/delete")
    public CommonResult delete(@RequestParam String categoryId) {
        flowCategoryService.delete(categoryId);
        return CommonResult.success();
    }

    /**
     * 流程分类列表查询接口
     *
     * @param flowCategoryPageBO 流程分类分页查询BO对象
     * @return 分页数据
     */
    @OperationLog(module = "工作流流程分类", value = "流程分类列表查询", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "流程分类列表查询")
    @SaCheckPermission(value = "flow:category:all")
    @PostMapping("/page")
    public CommonResult<CommonPageVO<FlowCategoryVO>> page(@Validated @RequestBody FlowCategoryPageBO flowCategoryPageBO) {
        CommonPageVO<FlowCategoryVO> page = flowCategoryService.page(flowCategoryPageBO);
        return CommonResult.data(page);
    }

    /**
     * 查看流程分类详情
     *
     * @param categoryId 流程分类id
     * @return 流程分类返回前端VO对象
     */
    @OperationLog(module = "工作流流程分类", value = "查看流程分类详情", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "查看流程分类详情")
    @Parameter(name = "categoryId", description = "流程分类ID", required = true, example = "1111")
    @SaCheckPermission(value = "flow:category:all")
    @GetMapping("/detail")
    public CommonResult<FlowCategoryVO> view(@RequestParam String categoryId) {
        FlowCategoryVO flowCategoryVO = flowCategoryService.detail(categoryId);
        return CommonResult.data(flowCategoryVO);
    }

    /**
     * 以字典形式返回全部FlowCategory数据集合。字典的键值为[categoryId, name]
     *
     * @param flowCategoryBO 流程分类查询BO对象
     * @return 返回的List<Map < String, Object>> 对象
     */
    @OperationLog(module = "工作流流程分类", value = "以字典形式返回全部FlowCategory数据集合", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "以字典形式返回全部FlowCategory数据集合")
    @SaCheckPermission(value = "flow:entry:all")
    @PostMapping("/listDict")
    public CommonResult<List<Map<String, Object>>> listDict(@RequestBody(required = false) FlowCategoryBO flowCategoryBO) {
        List<Map<String, Object>> flowCategoryMap = flowCategoryService.listDict(flowCategoryBO);
        return CommonResult.data(flowCategoryMap);
    }

    /**
     * 根据字典Id集合，获取查询后的字典数据
     *
     * @param dictIds 字典Id集合
     * @return 应答结果对象，包含字典形式的数据集合
     */
    @OperationLog(module = "工作流流程分类", value = "根据字典Id集合，获取查询后的字典数据", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "根据字典Id集合，获取查询后的字典数据")
    @PostMapping("/listDictByIds")
    public CommonResult<List<Map<String, Object>>> listDictByIds(@RequestBody List<String> dictIds) {
        List<Map<String, Object>> flowCategoryMap = flowCategoryService.listDictByIds(new HashSet<>(dictIds));
        return CommonResult.data(flowCategoryMap);
    }

}

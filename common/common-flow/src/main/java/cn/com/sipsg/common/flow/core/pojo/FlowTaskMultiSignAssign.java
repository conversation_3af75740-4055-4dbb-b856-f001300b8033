package cn.com.sipsg.common.flow.core.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 表示多实例任务的指派人信息
 *
 * <AUTHOR>
 * @since 2025/04/14
 **/
@Getter
@Setter
@Schema(description = "表示多实例任务的指派人信息")
public class FlowTaskMultiSignAssign {
    /**
     * 指派人类型。参考常量类 UserFilterGroup。
     */
    @Schema(description = "指派人类型")
    private String assigneeType;
    /**
     * 逗号分隔的指派人列表。
     */
    @Schema(description = "逗号分隔的指派人列表")
    private String assigneeList;
}

package cn.com.sipsg.common.flow.core.listener;

import com.alibaba.fastjson2.JSONArray;
import java.util.Arrays;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.ExecutionListener;
import org.flowable.engine.impl.persistence.entity.ExecutionEntityImpl;
import org.springframework.stereotype.Component;

/**
 * 动态指定会签人监听器，可通过businessDeptLeader字段将会签人用户id值传递过来，此监听器业务侧可以拿过来直接用
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Slf4j
@Component
public class BusinessDeptLeaderListener implements ExecutionListener {


    @Override
    public void notify(DelegateExecution execution) {
        JSONArray assigneeList = (JSONArray)execution.getVariable("assigneeList");
        if (null == assigneeList) {
            assigneeList = new JSONArray();
        }
        String businessDeptLeaderString = (String)execution.getVariable("businessDeptLeader");
        if (StringUtils.isNotBlank(businessDeptLeaderString)) {
            List<String> asList = Arrays.asList(businessDeptLeaderString.split(","));
            assigneeList.clear();
            assigneeList.addAll(asList);
        }
        execution.setVariable("assigneeList", assigneeList);
        log.info("BusinessDeptLeaderListener监听器处理节点:{}，节点中文名称：{}，分配的会签人：{}", ((ExecutionEntityImpl)execution).getActivityId(), ((ExecutionEntityImpl)execution).getActivityName(), assigneeList.toString());
    }
}

package cn.com.sipsg.common.flow.core.entity;

import cn.com.sipsg.common.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;
import java.sql.Timestamp;
import java.util.Map;

/**
 * 工作流工单实体对象。
 *
 * <AUTHOR>
 * @since 2025/04/14
 */

@Getter
@Setter
@FieldNameConstants
@TableName(value = "medox_flow_work_order", autoResultMap = true)
public class FlowWorkOrder extends BaseDO {

    /**
     * 主键Id。
     */
    @TableId(value = "work_order_id")
    private String workOrderId;

    /**
     * 工单编码字段。
     */
    private String workOrderCode;

    /**
     * 流程定义标识。
     */
    private String processDefinitionKey;

    /**
     * 流程名称。
     */
    private String processDefinitionName;

    /**
     * 流程引擎的定义Id。
     */
    private String processDefinitionId;

    /**
     * 流程实例Id。
     */
    private String processInstanceId;

    /**
     * 动态表单的主表Id。
     */
    private String onlineTableId;

    /**
     * 静态表单所使用的数据表名。
     */
    private String tableName;

    /**
     * 业务主键值。
     */
    private String businessKey;

    /**
     * 最近的审批状态。
     */
    private Integer latestApprovalStatus;

    /**
     * 流程状态。参考FlowTaskStatus常量值对象。
     */
    private Integer flowStatus;

    /**
     * 提交用户登录名称。
     */
    private String submitUsername;

    /**
     * 提交用户所在部门Id。
     */
    private String deptOfSubmitUser;

    /**
     * createTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String createTimeStart;

    @TableField(exist = false)
    private Timestamp createTimeStartTim;

    /**
     * createTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String createTimeEnd;

    @TableField(exist = false)
    private Timestamp createTimeEndTim;

    //todo @RelationConstDict改注解有什么用
    /*@RelationConstDict(
            masterIdField = "flowStatus",
            constantDictClass = FlowTaskStatus.class)*/
    @TableField(exist = false)
    private Map<String, Object> flowStatusDictMap;

    /**
     * 内置mapper
     */
    /*@Mapper
    public interface FlowWorkOrderModelMapper extends BaseModelMapper<FlowWorkOrderVo, FlowWorkOrder> {
    }

    public static final FlowWorkOrderModelMapper INSTANCE = Mappers.getMapper(FlowWorkOrderModelMapper.class);*/
}

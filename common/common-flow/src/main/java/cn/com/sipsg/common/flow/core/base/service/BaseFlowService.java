package cn.com.sipsg.common.flow.core.base.service;

import cn.com.sipsg.common.flow.core.entity.FlowWorkOrder;
import cn.com.sipsg.common.mybatis.core.service.BaseServiceX;
import cn.com.sipsg.common.util.BeanUtils;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.text.StrFormatter;
import cn.hutool.core.util.ReflectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.io.Serializable;
import java.lang.reflect.ParameterizedType;
import lombok.extern.slf4j.Slf4j;

/**
 * 路由表单工作流服务抽象类,涉及使用工作流功能的实现类由继承ServiceImpl<M,T>改为继承 BaseFlowService 同时，子类需要调用 BaseBusinessDataExtHelper.doRegister(String processDefinitionKey, BaseFlowService<?, ?> service)将实现类注册进去，如加上以下这段代码：
 *
 * <AUTHOR>
 * @PostConstruct public void registerBusinessDataExtHelper() { flowCustomExtFactory.getBusinessDataExtHelper().doRegister("postAdjustment", this); }
 * @since 2025/04/14
 */
@Slf4j
public abstract class BaseFlowService<M extends BaseMapper<T>, T> extends ServiceImpl<M, T> implements BaseServiceX<T> {

    /**
     * 当前Service关联的主Model实体对象的Class。
     */
    protected final Class<T> modelClass;


    protected BaseFlowService() {
        Class<?> type = getClass();
        while (!(type.getGenericSuperclass() instanceof ParameterizedType)) {
            type = type.getSuperclass();
        }
        modelClass = (Class<T>)((ParameterizedType)type.getGenericSuperclass()).getActualTypeArguments()[1];
    }


    /**
     * 获取业务详情数据。
     *
     * @param processInstanceId 流程实例Id。
     * @param businessKey       业务主键Id。如果与实际主键值类型不同，需要在子类中自行完成类型转换。
     * @return 业务主表数据，以及关联从表数据。
     */
    @SuppressWarnings("unchecked")
    public String getBusinessData(String processInstanceId, String businessKey) {
        //Serializable id = this.convertToKeyValue(businessKey);
        //M data = this.getByIdWithRelation(businessKey, MyRelationParam.full());
        T entry = this.getByIdDeep(businessKey);

        //return JSON.toJSONStringWithDateFormat(entry, DatePattern.NORM_DATETIME_PATTERN);
        return JSON.toJSONString(entry, DatePattern.NORM_DATETIME_PATTERN);
    }


    /**
     * 规格化工单扩展表中保存的草稿数据。如果仅仅包含主表数据，可以使用当前的缺省实现。
     *
     * @param processInstanceId 流程实例Id。
     * @param masterData        草稿中的主表数据。
     * @param slaveData         草稿中的全部关联从表数据。
     * @return 格式化后用于前端显示的业务草稿数据。
     */
    public String getNormalizedDraftData(String processInstanceId, JSONObject masterData, JSONObject slaveData) {
        // 看到这个异常千万别慌，这是及时的提示您，如果草稿中包含了关联从表数据，就一定要在业务的ServiceImpl中实现该方法。
        // 在子类中实现主表和关联从表数据的数据组装。通用的方法中，只能解析主表数据，关联从表各式各样，所以需要在子类中实现。
        if (slaveData != null) {
            throw new UnsupportedOperationException(
                "Please implement getNormalizedDraftData by self, because draft data includes related slaveData");
        }
        if (masterData == null) {
            return JSON.toJSONString(ReflectUtil.newInstance(Class.class));
        }
        T model = (T)BeanUtils.copyProperties(masterData, modelClass);
        //return JSON.toJSONStringWithDateFormat(model, DatePattern.NORM_DATETIME_PATTERN);
        return JSON.toJSONString(model, DatePattern.NORM_DATETIME_PATTERN);
    }

    /**
     * 根据工单对象删除流程业务数据。
     *
     * @param workOrder 工单对象。
     */
    @SuppressWarnings("unchecked")
    public void removeByWorkOrder(FlowWorkOrder workOrder) {
        Serializable id = workOrder.getBusinessKey();
        T data = this.getById(workOrder.getBusinessKey());
        if (data == null) {
            String msg = StrFormatter.format("WorkOrderId [{}] don't find business data by key [{}] while calling [removeByWorkOrder].",
                workOrder.getWorkOrderId(), workOrder.getBusinessKey());
            log.warn(msg);
            return;
        }
        // 删除业务数据
        this.removeById(id);
    }

}

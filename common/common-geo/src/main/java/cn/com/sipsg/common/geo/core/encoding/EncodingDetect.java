package cn.com.sipsg.common.geo.core.encoding;

import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * <Detect encoding .>
 * Copyright (C) <2009>  <Fluck,ACC <a href="http://androidos.cc/dev">...</a>>
 * <p>
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * <p>
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 * <p>
 * EncodingDetect.java<br>
 * 自动获取文件的编码
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/04/14
 */
@Slf4j
public class EncodingDetect {

    /**
     * 得到文件的编码
     *
     * @param filePath 文件路径
     * @return 文件的编码
     */
    public static String getJavaEncode(String filePath) {
        BytesEncodingDetect s = new BytesEncodingDetect();
        return BytesEncodingDetect.javaname[s.detectEncoding(new File(filePath))];
    }

    public static void readFile(String file, String code) {
        BufferedReader fr;
        try {
            String myCode = code != null && !code.isEmpty() ? code : "UTF8";
            InputStreamReader read = new InputStreamReader(Files.newInputStream(Paths.get(file)), myCode);

            fr = new BufferedReader(read);
            String line;
            int flag = 1;
            // 读取每一行，如果结束了，line会为空
            while ((line = fr.readLine()) != null && !line.trim().isEmpty()) {
                if (flag == 1) {
                    //去掉文件头
                    line = line.substring(1);
                    flag++;
                }
                // 每一行创建一个Student对象，并存入数组中
            }
            fr.close();
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
    }

}
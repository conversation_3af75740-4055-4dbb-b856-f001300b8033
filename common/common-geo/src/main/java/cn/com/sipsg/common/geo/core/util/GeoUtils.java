package cn.com.sipsg.common.geo.core.util;

import cn.com.sipsg.common.constant.CommonConstants;
import cn.com.sipsg.common.geo.core.pojo.Point;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.HexUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.filter.SimplePropertyPreFilter;
import java.io.IOException;
import java.io.Reader;
import java.io.StringReader;
import java.io.StringWriter;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.geotools.data.DataUtilities;
import org.geotools.data.simple.SimpleFeatureCollection;
import org.geotools.feature.FeatureCollection;
import org.geotools.feature.FeatureIterator;
import org.geotools.feature.simple.SimpleFeatureBuilder;
import org.geotools.geojson.feature.FeatureJSON;
import org.geotools.geojson.geom.GeometryJSON;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.CoordinateSequence;
import org.locationtech.jts.geom.Envelope;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.LineString;
import org.locationtech.jts.geom.LinearRing;
import org.locationtech.jts.geom.MultiLineString;
import org.locationtech.jts.geom.MultiPoint;
import org.locationtech.jts.geom.MultiPolygon;
import org.locationtech.jts.geom.Polygon;
import org.locationtech.jts.geom.impl.CoordinateArraySequence;
import org.locationtech.jts.io.ParseException;
import org.locationtech.jts.io.WKBReader;
import org.locationtech.jts.io.WKTReader;
import org.locationtech.jts.io.WKTWriter;
import org.locationtech.jts.io.geojson.GeoJsonWriter;
import org.opengis.feature.simple.SimpleFeature;
import org.opengis.feature.simple.SimpleFeatureType;

/**
 * Geo工具集
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Slf4j
public class GeoUtils {

    /**
     * 由wkt格式的geometry生成geoJson
     *
     * @param wkt wkt
     * @return geoJson
     */
    @SneakyThrows
    public static String wktToJson(String wkt) {
        WKTReader reader = new WKTReader();
        Geometry geometry = reader.read(wkt);
        StringWriter writer = new StringWriter();
        GeometryJSON g = new GeometryJSON(20);
        g.write(geometry, writer);
        return writer.toString();
    }

    /**
     * wkt转geoJson
     *
     * @param map         map
     * @param shapeColumn shape字段
     * @return geoJson
     */
    @SneakyThrows
    public static HashMap wktToGeoJson(Map<String, Object> map, String shapeColumn) {
        HashMap<String, Object> geoJson = new HashMap<>();
        GeoJsonWriter jsonWriter = new GeoJsonWriter();
        jsonWriter.setEncodeCRS(false);
        String wkt = null;
        String json = null;
        if (null != map.get(shapeColumn)) {
            wkt = map.get(shapeColumn).toString();
        }
        if (StrUtil.isNotBlank(wkt)) {
            WKTReader reader = new WKTReader();
            Geometry geometry = reader.read(wkt);
            json = jsonWriter.write(geometry);
        }

        geoJson.put("type", "Feature");
        geoJson.put("geometry", JSONObject.parse(json));
        map.remove(shapeColumn);
        geoJson.put("properties", map);
        return geoJson;
    }

    /**
     * wkt转geoJson
     *
     * @param map map
     * @return geoJson
     */
    @SneakyThrows
    public static HashMap wktToGeoJson(Map<String, Object> map) {
        HashMap<String, Object> geoJson = new HashMap<>();
        GeoJsonWriter jsonWriter = new GeoJsonWriter();
        jsonWriter.setEncodeCRS(false);
        String wkt = null;
        String json = null;
        if (null != map.get(CommonConstants.DEFAULT_SHAPE_COLUMN)) {
            wkt = map.get(CommonConstants.DEFAULT_SHAPE_COLUMN).toString();
        }
        if (StrUtil.isNotBlank(wkt)) {
            WKTReader reader = new WKTReader();
            Geometry geometry = reader.read(wkt);
            json = jsonWriter.write(geometry);
        }

        geoJson.put("type", "Feature");
        geoJson.put("geometry", JSONObject.parse(json));
        map.remove(CommonConstants.DEFAULT_SHAPE_COLUMN);
        geoJson.put("properties", map);
        return geoJson;
    }

    /**
     * geoJson转wkt
     *
     * @param geoJson geoJson
     * @return wkt
     */
    @SneakyThrows
    public static String geoJsonToWkt(String geoJson) {
        Reader reader = new StringReader(geoJson);
        Geometry geometry = new GeometryJSON().read(reader);
        return geometry.toText();
    }

    /**
     * GeoJSON转为Geometry
     *
     * @param geoJson
     * @return Geometry
     */

    public static Geometry geoJsonToGeometry(String geoJson) {
        GeometryJSON gjson = new GeometryJSON(20);
        Reader reader = new StringReader(geoJson);
        Geometry geometry = null;
        try {
            geometry = gjson.read(reader);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        return geometry;
    }

    /**
     * wkt转Geometry
     *
     * @param wkt
     * @return
     */
    public static Geometry wktToGeometry(String wkt) {
        WKTReader reader = new WKTReader();
        Geometry geometry = null;
        try {
            geometry = reader.read(wkt);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }

        return geometry;
    }

    /**
     * 获取几何的的四至xy
     *
     * @param geometries 几何图形
     * @return key：xMax、xMin、yMax、yMin
     */
    public static Map<String, Double> getEnvelopeMap(List<Geometry> geometries) {
        Map<String, Double> map = new HashMap<>();
        if (CollUtil.isEmpty(geometries)) {
            return map;
        }

        double minX = Double.MAX_VALUE;
        double minY = Double.MAX_VALUE;
        double maxX = Double.MIN_VALUE;
        double maxY = Double.MIN_VALUE;

        for (Geometry geometry : geometries) {
            Envelope envelope = geometry.getEnvelopeInternal();
            minX = Math.min(minX, envelope.getMinX());
            minY = Math.min(minY, envelope.getMinY());
            maxX = Math.max(maxX, envelope.getMaxX());
            maxY = Math.max(maxY, envelope.getMaxY());
        }
        map.put("xMin", minX);
        map.put("yMin", minY);
        map.put("xMax", maxX);
        map.put("yMax", maxY);
        return map;
    }

    /**
     * 判断是否在地图范围内
     *
     * @param map
     * @param extents
     * @return
     */
    public static Boolean getRangeVerification(Map<String, Double> map, String extents) {
        String tempExtents = StrUtil.replace(extents, StrUtil.BACKSLASH, "");
        JSONObject extent = JSON.parseObject(tempExtents);

        double xmin = getDoubleValue(extent, "xmin");
        double ymin = getDoubleValue(extent, "ymin");
        double xmax = getDoubleValue(extent, "xmax");
        double ymax = getDoubleValue(extent, "ymax");

        return (xmin == Double.MIN_VALUE || map.get("xMin") >= xmin) &&
            (xmax == Double.MIN_VALUE || map.get("xMax") <= xmax) &&
            (ymin == Double.MIN_VALUE || map.get("yMin") >= ymin) &&
            (ymax == Double.MIN_VALUE || map.get("yMax") <= ymax);
    }

    /**
     * 获取空间范围坐标
     *
     * @param obj
     * @param key
     * @return
     */
    private static double getDoubleValue(JSONObject obj, String key) {
        if (obj.containsKey(key)) {
            return obj.getDouble(key);
        } else {
            return Double.MIN_VALUE;
        }
    }

    /**
     * 多面转为面
     *
     * @param geojson
     * @return
     */
    public static cn.hutool.json.JSONObject multiPolygonToPolygon(String geojson) {
        cn.hutool.json.JSONObject geoJsonObject = new cn.hutool.json.JSONObject(geojson);

        cn.hutool.json.JSONArray features = geoJsonObject.getJSONArray("features");
        cn.hutool.json.JSONArray newFeatures = new cn.hutool.json.JSONArray();

        for (int i = 0; i < features.size(); i++) {
            cn.hutool.json.JSONObject feature = features.getJSONObject(i);
            String geometryType = feature.getJSONObject("geometry").getStr("type");

            if ("MultiPolygon".equals(geometryType)) {
                cn.hutool.json.JSONArray coordinates = feature.getJSONObject("geometry").getJSONArray("coordinates");

                for (int j = 0; j < coordinates.size(); j++) {
                    cn.hutool.json.JSONArray polygonCoordinates = coordinates.getJSONArray(j);

                    cn.hutool.json.JSONObject newFeature = new cn.hutool.json.JSONObject();
                    newFeature.put("type", "Feature");

                    cn.hutool.json.JSONObject geometry = new cn.hutool.json.JSONObject();
                    geometry.put("type", "Polygon");
                    geometry.put("coordinates", polygonCoordinates);
                    newFeature.put("geometry", geometry);

                    newFeature.put("properties", feature.getJSONObject("properties"));

                    newFeatures.add(newFeature);
                }
            } else {
                newFeatures.add(feature);
            }
        }

        // 提取除了features之外的其他字段
        cn.hutool.json.JSONObject extraFields = new cn.hutool.json.JSONObject(geoJsonObject);
        extraFields.remove("features");
        cn.hutool.json.JSONObject newGeoJsonObject = new cn.hutool.json.JSONObject();
        newGeoJsonObject.putAll(extraFields);
        newGeoJsonObject.put("features", newFeatures);
        return newGeoJsonObject;
    }

    /**
     * 面转为多面
     *
     * @param geojson
     * @return
     */
    public static cn.hutool.json.JSONObject polygonToMultiPolygon(String geojson) {
        cn.hutool.json.JSONObject geoJsonObject = new cn.hutool.json.JSONObject(geojson);

        cn.hutool.json.JSONArray features = geoJsonObject.getJSONArray("features");
        cn.hutool.json.JSONArray newFeatures = new cn.hutool.json.JSONArray();

        for (int i = 0; i < features.size(); i++) {
            cn.hutool.json.JSONObject feature = features.getJSONObject(i);
            cn.hutool.json.JSONObject geometry = feature.getJSONObject("geometry");

            if ("Polygon".equals(geometry.getStr("type"))) {
                cn.hutool.json.JSONObject newFeature = new cn.hutool.json.JSONObject();
                newFeature.put("type", "Feature");

                cn.hutool.json.JSONObject newGeometry = new cn.hutool.json.JSONObject();
                newGeometry.put("type", "MultiPolygon");

                cn.hutool.json.JSONArray coordinates = new cn.hutool.json.JSONArray();
                coordinates.add(geometry.getJSONArray("coordinates"));

                newGeometry.put("coordinates", coordinates);
                newFeature.put("geometry", newGeometry);

                newFeature.put("properties", feature.getJSONObject("properties"));

                newFeatures.add(newFeature);
            } else {
                newFeatures.add(feature);
            }
        }

        // 提取除了features之外的其他字段
        cn.hutool.json.JSONObject extraFields = new cn.hutool.json.JSONObject(geoJsonObject);
        extraFields.remove("features");
        cn.hutool.json.JSONObject newGeoJsonObject = new cn.hutool.json.JSONObject();
        newGeoJsonObject.putAll(extraFields);
        newGeoJsonObject.put("features", newFeatures);
        return newGeoJsonObject;
    }


    /**
     * 十六进制转wkt
     *
     * @param hex 十六进制
     * @return wkt
     */
    @SneakyThrows
    public static String hexToWkt(String hex) {
        return byteToWkt(HexUtil.decodeHex(hex));
    }

    /**
     * byte转wkt
     *
     * @param bty byte数组
     * @return wkt
     */
    @SneakyThrows
    public static String byteToWkt(byte[] bty) {
        WKBReader reader = new WKBReader();
        WKTWriter writer = new WKTWriter();
        return writer.write(reader.read(bty));
    }

    /**
     * sqlserver获取空间类型
     *
     * @param bty 字节数组
     * @return wkt
     */
    @SneakyThrows
    public static String mssqlByteToWkt(byte[] bty) {
        com.microsoft.sqlserver.jdbc.Geometry geometry = com.microsoft.sqlserver.jdbc.Geometry.STGeomFromWKB(bty);
        return geometry.STAsText();
    }

    /**
     * mysql获取空间类型
     *
     * @param bty 字节数组
     * @return wkt
     */
    @SneakyThrows
    public static String mysqlByteToWkt(byte[] bty) {
        //first four bytes of the geometry are the SRID,
        //followed by the actual WKB.  Determine the SRID
        //这里是取字节数组的前4个来解析srid
        byte[] sridBytes = new byte[4];
        System.arraycopy(bty, 0, sridBytes, 0, 4);
        //        boolean bigEndian = (bty[4] == 0x00);
        boolean bigEndian = bty[4] == 0x00;
        // 解析srid
        int srid = 0;
        if (bigEndian) {
            for (int i = 0; i < sridBytes.length; i++) {
                srid = (srid << 8) + (sridBytes[i] & 0xff);
            }
        } else {
            for (int i = 0; i < sridBytes.length; i++) {
                srid += (sridBytes[i] & 0xff) << (8 * i);
            }
        }
        //use the JTS WKBReader for WKB parsing
        WKBReader wkbReader = new WKBReader();
        // 使用geotool的WKBReader 把字节数组转成geometry对象。
        //copy the byte array, removing the first four
        //SRID bytes
        byte[] wkb = new byte[bty.length - 4];
        System.arraycopy(bty, 4, wkb, 0, wkb.length);
        Geometry geometry = wkbReader.read(wkb);
        geometry.setSRID(srid);
        return geometry.toText();
    }

    /**
     * arcgis转wkt
     *
     * @param json arcgis
     * @return wkt
     */
    public static String arcgis2wkt(String json) {
        GeometryFactory geometryFactory = new GeometryFactory();
        String wkt = null;
        JSONObject feature = JSONObject.parseObject(json);
        JSONObject geometry = feature.getJSONObject("geometry");

        if (geometry != null) {
            JSONArray rings = geometry.getJSONArray("rings");
            if (rings != null) {
                List<LinearRing> ringList = new ArrayList<>();

                // 遍历 rings
                for (int j = 0; j < rings.size(); j++) {
                    JSONArray ring = rings.getJSONArray(j);

                    List<Coordinate> coordinateList = new ArrayList<>();
                    for (int k = 0; k < ring.size(); k++) {
                        JSONArray point = ring.getJSONArray(k);
                        Coordinate coordinate = new Coordinate(point.getDouble(0), point.getDouble(1));
                        coordinateList.add(coordinate);
                    }
                    if (CollUtil.isNotEmpty(coordinateList)) {
                        Coordinate[] coordinates = new Coordinate[coordinateList.size()];
                        coordinateList.toArray(coordinates);
                        LinearRing linearRing = geometryFactory.createLinearRing(coordinates);
                        ringList.add(linearRing);
                    }
                }

                if (CollUtil.isNotEmpty(ringList)) {
                    // 不只一个环
                    if (ringList.size() > 1) {
                        int flag = 0;
                        List<Polygon> polygons = new ArrayList<>();
                        for (LinearRing linearRing : ringList) {
                            Polygon polygon = new Polygon(linearRing, null, geometryFactory);
                            polygons.add(polygon);
                        }
                        // 遍历是否有相交
                        for (int j = 0; j < polygons.size(); j++) {
                            Polygon p1 = polygons.get(j);
                            for (int k = j + 1; k < polygons.size(); k++) {
                                Polygon p2 = polygons.get(k);
                                if (p1.intersects(p2)) {
                                    flag = 1;
                                }
                            }
                        }
                        // 有相交，把第一个环作为孔处理
                        if (flag == 1) {
                            LinearRing[] holeList = new LinearRing[1];
                            LinearRing linearRing = ringList.get(0);
                            List<LinearRing> holes = Collections.singletonList(linearRing);
                            holes.toArray(holeList);
                            Polygon polygon = new Polygon(ringList.get(1), holeList, geometryFactory);

                            wkt = polygon.toText();
                        } else {
                            // 无相交，按multipolygon处理
                            Polygon[] ps = new Polygon[polygons.size()];
                            polygons.toArray(ps);
                            MultiPolygon multiPolygon = geometryFactory.createMultiPolygon(ps);

                            wkt = multiPolygon.toText();
                        }
                    } else {
                        // 之有一个环，按polygon处理
                        Polygon polygon = new Polygon(ringList.get(0), null, geometryFactory);

                        wkt = polygon.toText();
                    }
                }
            }

            // 点
            Double x = geometry.getDouble("x");
            if (x != null) {
                Double y = geometry.getDouble("y");
                Coordinate coord = new Coordinate(x, y);
                org.locationtech.jts.geom.Point point = geometryFactory.createPoint(coord);

                wkt = point.toText();
            }

            // 线
            JSONArray paths = geometry.getJSONArray("paths");
            if (paths != null) {
                for (int j = 0; j < paths.size(); j++) {
                    JSONArray path = paths.getJSONArray(j);

                    List<Coordinate> coordinateList = new ArrayList<>();
                    for (int k = 0; k < path.size(); k++) {
                        JSONArray point = path.getJSONArray(k);
                        Coordinate coordinate = new Coordinate(point.getDouble(0), point.getDouble(1));
                        coordinateList.add(coordinate);
                    }
                    if (CollUtil.isNotEmpty(coordinateList)) {
                        Coordinate[] coordinates = new Coordinate[coordinateList.size()];
                        coordinateList.toArray(coordinates);
                        LineString lineString = geometryFactory.createLineString(coordinates);

                        wkt = lineString.toText();
                    }
                }

            }
        }

        return wkt;
    }

    /**
     * wkt转geometry
     *
     * @param wkt wkt
     * @return geometry
     */
    @SneakyThrows
    public static Geometry wkt2Geometry(String wkt) {
        WKTReader reader = new WKTReader();
        return reader.read(wkt);
    }

    public static List<Point> json2points(String json, Integer reverseXy) {
        // 读取geojson
        JSONArray jsonArray = JSON.parseArray(json);
        List<Point> pointDTOs = new ArrayList<>();
        int polygonNum = 0;

        for (Iterator iterator = jsonArray.iterator(); iterator.hasNext(); ) {
            // 删除 properties，不然解析失败
            JSONObject jsonObject = (JSONObject)iterator.next();
            SimplePropertyPreFilter filter = new SimplePropertyPreFilter();
            filter.getExcludes().add("properties");
            FeatureJSON fjson = new FeatureJSON();
            String jsonCleaned = JSON.toJSONString(jsonObject, filter);
            Reader reader = new StringReader(jsonCleaned);
            FeatureCollection featureCollection = null;

            try {
                featureCollection = fjson.readFeatureCollection(reader);
            } catch (IOException e) {
                e.printStackTrace();
            }

            if (featureCollection != null) {
                FeatureIterator features = featureCollection.features();

                // 遍历所有 feature，geometry
                while (features.hasNext()) {
                    SimpleFeature feature = (SimpleFeature)features.next();
                    Geometry geometry = (Geometry)feature.getDefaultGeometry();

                    String geometryType = geometry.getGeometryType();
                    org.locationtech.jts.geom.Point p;
                    if (Geometry.TYPENAME_POLYGON.equals(geometryType)) {
                        Polygon poly = (Polygon)geometry;
                        int interRingNum = poly.getNumInteriorRing();
                        int r = 0;
                        if (interRingNum > 0) {
                            for (; r < interRingNum; r++) {
                                LinearRing ring = poly.getInteriorRingN(r);
                                for (int j = 0; j < ring.getNumPoints(); j++) {
                                    p = ring.getPointN(j);
                                    Point point = new Point();
                                    point.setPolygonNo(polygonNum);
                                    point.setPntIndex(j);
                                    if (reverseXy == 1) {
                                        point.setX(BigDecimal.valueOf(p.getY()));
                                        point.setY(BigDecimal.valueOf(p.getX()));
                                    } else {
                                        point.setX(BigDecimal.valueOf(p.getX()));
                                        point.setY(BigDecimal.valueOf(p.getY()));
                                    }
                                    point.setRoundNo(r);
                                    pointDTOs.add(point);
                                }
                            }
                        }
                        for (int i = 0; i < poly.getExteriorRing().getNumPoints(); i++) {
                            p = poly.getExteriorRing().getPointN(i);
                            Point point = new Point();
                            point.setPolygonNo(polygonNum);
                            point.setPntIndex(i);
                            if (reverseXy == 1) {
                                point.setX(BigDecimal.valueOf(p.getY()));
                                point.setY(BigDecimal.valueOf(p.getX()));
                            } else {
                                point.setX(BigDecimal.valueOf(p.getX()));
                                point.setY(BigDecimal.valueOf(p.getY()));
                            }
                            point.setRoundNo(r);
                            pointDTOs.add(point);
                        }
                    } else if (Geometry.TYPENAME_MULTIPOLYGON.equals(geometryType)) {
                        int numGeometries = geometry.getNumGeometries();
                        for (int i = 0; i < numGeometries; i++) {
                            Polygon poly = (Polygon)geometry.getGeometryN(i);
                            int interRingNum = poly.getNumInteriorRing();
                            int r = 0;
                            if (interRingNum > 0) {
                                for (; r < interRingNum; r++) {
                                    LinearRing ring = poly.getInteriorRingN(r);
                                    for (int j = 0; j < ring.getNumPoints(); j++) {
                                        p = ring.getPointN(j);
                                        Point point = new Point();
                                        point.setPolygonNo(polygonNum);
                                        point.setPntIndex(j);
                                        if (reverseXy == 1) {
                                            point.setX(BigDecimal.valueOf(p.getY()));
                                            point.setY(BigDecimal.valueOf(p.getX()));
                                        } else {
                                            point.setX(BigDecimal.valueOf(p.getX()));
                                            point.setY(BigDecimal.valueOf(p.getY()));
                                        }
                                        point.setRoundNo(r + i);
                                        pointDTOs.add(point);
                                    }
                                }
                            }
                            for (int j = 0; j < poly.getExteriorRing().getNumPoints(); j++) {
                                p = poly.getExteriorRing().getPointN(j);
                                Point point = new Point();
                                point.setPolygonNo(polygonNum);
                                point.setPntIndex(j);
                                if (reverseXy == 1) {
                                    point.setX(BigDecimal.valueOf(p.getY()));
                                    point.setY(BigDecimal.valueOf(p.getX()));
                                } else {
                                    point.setX(BigDecimal.valueOf(p.getX()));
                                    point.setY(BigDecimal.valueOf(p.getY()));
                                }
                                point.setRoundNo(r + i);
                                pointDTOs.add(point);
                            }
                        }
                    } else if (Geometry.TYPENAME_LINESTRING.equals(geometryType)) {
                        LineString l = (LineString)geometry;
                        for (int i = 0; i < l.getNumPoints(); i++) {
                            p = l.getPointN(i);
                            Point point = new Point();
                            point.setPolygonNo(polygonNum);
                            point.setPntIndex(i);
                            if (reverseXy == 1) {
                                point.setX(BigDecimal.valueOf(p.getY()));
                                point.setY(BigDecimal.valueOf(p.getX()));
                            } else {
                                point.setX(BigDecimal.valueOf(p.getX()));
                                point.setY(BigDecimal.valueOf(p.getY()));
                            }
                            point.setRoundNo(0);
                            pointDTOs.add(point);
                        }
                    } else if (Geometry.TYPENAME_MULTILINESTRING.equals(geometryType)) {
                        MultiLineString lines = (MultiLineString)geometry;
                        int lineNum = lines.getNumGeometries();
                        for (int i = 0; i < lineNum; i++) {
                            LineString l = (LineString)lines.getGeometryN(i);
                            for (int j = 0; j < l.getNumPoints(); j++) {
                                p = l.getPointN(j);
                                Point point = new Point();
                                point.setPolygonNo(polygonNum);
                                point.setPntIndex(j);
                                if (reverseXy == 1) {
                                    point.setX(BigDecimal.valueOf(p.getY()));
                                    point.setY(BigDecimal.valueOf(p.getX()));
                                } else {
                                    point.setX(BigDecimal.valueOf(p.getX()));
                                    point.setY(BigDecimal.valueOf(p.getY()));
                                }
                                point.setRoundNo(i);
                                pointDTOs.add(point);
                            }
                        }
                    } else if (Geometry.TYPENAME_MULTIPOINT.equals(geometryType)) {
                        MultiPoint points = (MultiPoint)geometry;
                        int pointNum = points.getNumGeometries();
                        for (int i = 0; i < pointNum; i++) {
                            p = (org.locationtech.jts.geom.Point)points.getGeometryN(i);
                            Point point = new Point();
                            point.setPolygonNo(polygonNum);
                            point.setPntIndex(0);
                            if (reverseXy == 1) {
                                point.setX(BigDecimal.valueOf(p.getY()));
                                point.setY(BigDecimal.valueOf(p.getX()));
                            } else {
                                point.setX(BigDecimal.valueOf(p.getX()));
                                point.setY(BigDecimal.valueOf(p.getY()));
                            }
                            point.setRoundNo(i);
                            pointDTOs.add(point);
                        }
                    } else if (Geometry.TYPENAME_POINT.equals(geometryType)) {
                        p = (org.locationtech.jts.geom.Point)geometry;
                        Point point = new Point();
                        point.setPolygonNo(polygonNum);
                        point.setPntIndex(0);
                        if (reverseXy == 1) {
                            point.setX(BigDecimal.valueOf(p.getY()));
                            point.setY(BigDecimal.valueOf(p.getX()));
                        } else {
                            point.setX(BigDecimal.valueOf(p.getX()));
                            point.setY(BigDecimal.valueOf(p.getY()));
                        }
                        point.setRoundNo(0);
                        pointDTOs.add(point);
                    } else {
                        log.info("No matched geometry.");
                    }
                    polygonNum++;
                }
            }
        }
        return pointDTOs;
    }

    public static List<String> json2wkt(String json) {
        // 读取geojson
        FeatureJSON fjson = new FeatureJSON();
        Reader reader = new StringReader(json);
        FeatureCollection featureCollection = null;
        try {
            featureCollection = fjson.readFeatureCollection(reader);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }

        List<String> wkts = new ArrayList<>();
        if (featureCollection != null) {
            FeatureIterator features = featureCollection.features();
            while (features.hasNext()) {
                SimpleFeature feature = (SimpleFeature)features.next();
                Geometry geometry = (Geometry)feature.getDefaultGeometry();
                wkts.add(geometry.toString());
            }
        }
        return wkts;
    }

    public static String wkt2json(List<String> wkts) {
        String geoJson = null;
        try {
            WKTReader reader = new WKTReader();
            StringWriter writer = new StringWriter();
            FeatureJSON featureJSON = new FeatureJSON();
            List<SimpleFeature> features = new ArrayList<>();

            SimpleFeatureType polygonType = DataUtilities.createType("Location", "the_geom:Polygon,test:String");
            SimpleFeatureType multiPolygonType = DataUtilities.createType("Location", "the_geom:MultiPolygon");
            SimpleFeatureType pointType = DataUtilities.createType("Location", "the_geom:Point");
            SimpleFeatureType multiPointType = DataUtilities.createType("Location", "the_geom:MultiPoint");
            SimpleFeatureType lineStringType = DataUtilities.createType("Location", "the_geom:LineString");
            SimpleFeatureType multiLineStringType = DataUtilities.createType("Location", "the_geom:MultiLineString");

            for (String wkt : wkts) {
                Geometry geometry = reader.read(wkt);
                String geoType = geometry.getGeometryType();
                if (Geometry.TYPENAME_POLYGON.equals(geoType)) {
                    SimpleFeatureBuilder polygonFeatureBuilder = new SimpleFeatureBuilder(polygonType);
                    polygonFeatureBuilder.add(geometry);
                    SimpleFeature feature = polygonFeatureBuilder.buildFeature(null);
                    features.add(feature);
                } else if (Geometry.TYPENAME_MULTIPOLYGON.equals(geoType)) {
                    SimpleFeatureBuilder multiPolygonFeatureBuilder = new SimpleFeatureBuilder(multiPolygonType);
                    multiPolygonFeatureBuilder.add(geometry);
                    SimpleFeature feature = multiPolygonFeatureBuilder.buildFeature(null);
                    features.add(feature);
                } else if (Geometry.TYPENAME_POINT.equals(geoType)) {
                    SimpleFeatureBuilder pointFeatureBuilder = new SimpleFeatureBuilder(pointType);
                    pointFeatureBuilder.add(geometry);
                    SimpleFeature feature = pointFeatureBuilder.buildFeature(null);
                    features.add(feature);
                } else if (Geometry.TYPENAME_MULTIPOINT.equals(geoType)) {
                    SimpleFeatureBuilder multiPointFeatureBuilder = new SimpleFeatureBuilder(multiPointType);
                    multiPointFeatureBuilder.add(geometry);
                    SimpleFeature feature = multiPointFeatureBuilder.buildFeature(null);
                    features.add(feature);
                } else if (Geometry.TYPENAME_LINESTRING.equals(geoType)) {
                    SimpleFeatureBuilder lineStringFeatureBuilder = new SimpleFeatureBuilder(lineStringType);
                    lineStringFeatureBuilder.add(geometry);
                    SimpleFeature feature = lineStringFeatureBuilder.buildFeature(null);
                    features.add(feature);
                } else if (Geometry.TYPENAME_MULTILINESTRING.equals(geoType)) {
                    SimpleFeatureBuilder multiLineStringFeatureBuilder = new SimpleFeatureBuilder(multiLineStringType);
                    multiLineStringFeatureBuilder.add(geometry);
                    SimpleFeature feature = multiLineStringFeatureBuilder.buildFeature(null);
                    features.add(feature);
                }
            }

            SimpleFeatureCollection collection = DataUtilities.collection(features);
            featureJSON.writeFeatureCollection(collection, writer);
            geoJson = writer.toString();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return geoJson;
    }

    public static String wkt2arcgis(String wkt) {
        JSONObject geometryObject = new JSONObject();
        try {
            WKTReader reader = new WKTReader();
            Geometry geometry = reader.read(wkt);
            String geoType = geometry.getGeometryType();
            if (Geometry.TYPENAME_POLYGON.equals(geoType)) {
                JSONArray ringsArray = new JSONArray();
                Polygon poly = (Polygon)geometry;
                int interRingNum = poly.getNumInteriorRing();
                int r = 0;

                // 如果有孔
                if (interRingNum > 0) {
                    JSONArray interRingArray = new JSONArray();
                    for (; r < interRingNum; r++) {
                        LinearRing ring = poly.getInteriorRingN(r);
                        for (int j = 0; j < ring.getNumPoints(); j++) {
                            JSONArray pointArray = new JSONArray();
                            org.locationtech.jts.geom.Point p = ring.getPointN(j);
                            pointArray.add(BigDecimal.valueOf(p.getX()));
                            pointArray.add(BigDecimal.valueOf(p.getY()));
                            interRingArray.add(pointArray);
                        }
                    }
                    ringsArray.add(interRingArray);
                }
                // 外环
                JSONArray outerRingArray = new JSONArray();
                for (int i = 0; i < poly.getExteriorRing().getNumPoints(); i++) {
                    JSONArray pointArray = new JSONArray();
                    org.locationtech.jts.geom.Point p = poly.getExteriorRing().getPointN(i);
                    pointArray.add(BigDecimal.valueOf(p.getX()));
                    pointArray.add(BigDecimal.valueOf(p.getY()));
                    outerRingArray.add(pointArray);
                }
                ringsArray.add(outerRingArray);
                geometryObject.put("rings", ringsArray);
            } else if (Geometry.TYPENAME_POINT.equals(geoType)) {
                org.locationtech.jts.geom.Point p = (org.locationtech.jts.geom.Point)geometry;
                geometryObject.put("x", BigDecimal.valueOf(p.getX()));
                geometryObject.put("y", BigDecimal.valueOf(p.getY()));
            } else if (Geometry.TYPENAME_LINESTRING.equals(geoType)) {
                JSONArray lineArray = new JSONArray();
                LineString l = (LineString)geometry;
                for (int i = 0; i < l.getNumPoints(); i++) {
                    JSONArray pointArray = new JSONArray();
                    org.locationtech.jts.geom.Point p = l.getPointN(i);
                    pointArray.add(BigDecimal.valueOf(p.getX()));
                    pointArray.add(BigDecimal.valueOf(p.getX()));
                    lineArray.add(pointArray);
                }
                JSONArray linesArray = new JSONArray();
                linesArray.add(lineArray);
                geometryObject.put("paths", linesArray);
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }

        return geometryObject.toJSONString();
    }

    /**
     * 界址点转换为多边形
     *
     * @param polygons 界址点
     * @return 多边形
     */
    public static String changePolygon(List<Point> polygons, Boolean reverseXy) {
        if (CollUtil.isNotEmpty(polygons)) {
            Map<Integer, List<Point>> polygonMap = polygons
                .stream().sorted(Comparator.comparing(Point::getPolygonNo))
                .filter(x -> x.getPolygonNo() != null)
                .collect(Collectors.groupingBy(Point::getPolygonNo));
            if (polygonMap != null) {
                // Create a GeometryFactory if you don't have one already
                GeometryFactory geometryFactory = new GeometryFactory();
                List<Geometry> polygonList = new ArrayList<>();
                for (Integer polygonNo : polygonMap.keySet()) {
                    //同一个polygonNo下
                    List<Point> childPolygons = polygonMap.get(polygonNo);
                    Map<Integer, List<Point>> childPolygonMap = childPolygons
                        .stream().sorted(Comparator.comparing(Point::getRoundNo))
                        .filter(x -> x.getRoundNo() != null).collect(Collectors.groupingBy(Point::getRoundNo));

                    //图形
                    Polygon polygon;
                    //最外层一圈
                    List<LinearRing> rings = new ArrayList<>();
                    //孔
                    List<LinearRing> holeList = new ArrayList<>();
                    LinearRing[] holes = null;
                    List<org.locationtech.jts.geom.Point> points = new ArrayList<>();
                    List<LineString> lineStrings = new ArrayList<>();
                    for (Integer roundNo : childPolygonMap.keySet()) {
                        //同一个roundNo下
                        List<Point> roundPolygons = childPolygonMap.get(roundNo);

                        if (roundPolygons.size() == 1) {
                            // Point 情况
                            Coordinate coord = null;
                            if (reverseXy) {
                                coord = new Coordinate(roundPolygons.get(0).getY().doubleValue(), roundPolygons.get(0).getX().doubleValue());
                            } else {
                                coord = new Coordinate(roundPolygons.get(0).getX().doubleValue(), roundPolygons.get(0).getY().doubleValue());
                            }
                            org.locationtech.jts.geom.Point point = geometryFactory.createPoint(coord);
                            points.add(point);
                        } else {
                            List<Coordinate> coordinateList = new ArrayList<>();
                            Coordinate[] coords = new Coordinate[roundPolygons.size()];
                            if (reverseXy) {
                                for (Point roundPolygon : roundPolygons) {
                                    Coordinate coord = new Coordinate(roundPolygon.getY().doubleValue(), roundPolygon.getX().doubleValue());
                                    coordinateList.add(coord);
                                }
                            } else {
                                for (Point roundPolygon : roundPolygons) {
                                    Coordinate coord = new Coordinate(roundPolygon.getX().doubleValue(), roundPolygon.getY().doubleValue());
                                    coordinateList.add(coord);
                                }
                            }
                            coordinateList.toArray(coords);

                            if (coordinateList.get(0).equals(coordinateList.get(coordinateList.size() - 1))) {
                                // polygon 情况
                                if (roundNo > 0) {
                                    LinearRing ring = geometryFactory.createLinearRing(coords);
                                    rings.add(ring);
                                } else {
                                    if (childPolygonMap.keySet().size() > 1) {
                                        holeList.add(geometryFactory.createLinearRing(coords));
                                    } else {
                                        LinearRing ring = geometryFactory.createLinearRing(coords);
                                        rings.add(ring);
                                    }
                                }
                            } else {
                                // linestring 情况
                                CoordinateSequence cs = new CoordinateArraySequence(coords);
                                LineString ls = new LineString(cs, geometryFactory);
                                lineStrings.add(ls);
                            }
                        }
                    }

                    // multiPoint 情况
                    if (CollUtil.isNotEmpty(points)) {
                        if (points.size() > 1) {
                            org.locationtech.jts.geom.Point[] pts = new org.locationtech.jts.geom.Point[points.size()];
                            points.toArray(pts);
                            MultiPoint multiPoint = new MultiPoint(points.toArray(pts), geometryFactory);
                            polygonList.add(multiPoint);
                        } else {
                            polygonList.addAll(points);
                        }
                    }

                    // multiLineString 情况
                    if (CollUtil.isNotEmpty(lineStrings)) {
                        if (lineStrings.size() > 1) {
                            LineString[] mls = new LineString[lineStrings.size()];
                            lineStrings.toArray(mls);
                            MultiLineString multiLineString = new MultiLineString(mls, geometryFactory);
                            polygonList.add(multiLineString);
                        } else {
                            polygonList.addAll(lineStrings);
                        }
                    }

                    // polygon 情况
                    if (CollUtil.isNotEmpty(rings)) {
                        Polygon[] polygonArr = new Polygon[rings.size()];

                        if (CollUtil.isNotEmpty(holeList)) {
                            holes = new LinearRing[holeList.size()];
                            holeList.toArray(holes);
                        }
                        polygon = new Polygon(rings.get(0), holes, geometryFactory);

                        if (rings.size() > 1) {
                            polygonArr[0] = polygon;
                            for (int i = 1; i < rings.size(); i++) {
                                polygonArr[i] = new Polygon(rings.get(i), null, geometryFactory);
                            }
                            MultiPolygon multiPolygon = geometryFactory.createMultiPolygon(polygonArr);
                            polygonList.add(multiPolygon);
                        } else {
                            polygonList.add(polygon);
                        }
                    }
                }

                if (CollUtil.isNotEmpty(polygonList)) {
                    if (polygonList.size() > 1) {
                        Polygon[] polygonArr = new Polygon[polygonList.size()];
                        polygonArr = polygonList.toArray(polygonArr);
                        MultiPolygon multiPolygon = geometryFactory.createMultiPolygon(polygonArr);
                        return multiPolygon.toString();
                    } else {
                        return polygonList.get(0).toString();
                    }
                }
            }
        }
        return null;
    }

}

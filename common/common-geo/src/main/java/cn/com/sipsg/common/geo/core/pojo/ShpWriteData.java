package cn.com.sipsg.common.geo.core.pojo;

import lombok.Getter;
import lombok.Setter;

/**
 * 空间写入数据
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
public class ShpWriteData<T> extends ShpData<T> {

    private static final long serialVersionUID = -4637139512969763567L;

    /**
     * 是否为追加写入
     */
    private boolean appendWrite = false;

    /**
     * shp文件所在文件夹路径
     */
    private String folder;

    /**
     * shp文件名
     */
    private String fileName;

    /**
     * 空间字段的几何类型，可选：Point、LineString、Polygon、MultiPoint、MultiLineString、MultiPolygon
     */
    private String geometryType;

}

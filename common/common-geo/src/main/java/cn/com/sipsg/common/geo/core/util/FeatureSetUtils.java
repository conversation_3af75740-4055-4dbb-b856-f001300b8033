package cn.com.sipsg.common.geo.core.util;

import cn.com.sipsg.common.exception.BusinessException;
import cn.com.sipsg.common.geo.core.pojo.FeatureSet;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSONObject;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import org.geotools.data.collection.ListFeatureCollection;
import org.geotools.feature.FeatureCollection;
import org.geotools.feature.FeatureIterator;
import org.geotools.feature.NameImpl;
import org.geotools.feature.simple.SimpleFeatureBuilder;
import org.geotools.feature.simple.SimpleFeatureTypeBuilder;
import org.geotools.feature.type.AttributeDescriptorImpl;
import org.geotools.feature.type.GeometryDescriptorImpl;
import org.geotools.feature.type.GeometryTypeImpl;
import org.geotools.geojson.GeoJSON;
import org.geotools.geojson.feature.FeatureJSON;
import org.geotools.geojson.geom.GeometryJSON;
import org.opengis.feature.simple.SimpleFeature;
import org.opengis.feature.simple.SimpleFeatureType;
import org.opengis.feature.type.AttributeDescriptor;
import org.opengis.feature.type.GeometryDescriptor;
import org.opengis.feature.type.GeometryType;

/**
 * 特征集工具集
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public class FeatureSetUtils {

    public static JSONObject serialize(FeatureSet set) throws IOException {
        try (ByteArrayOutputStream stream = new ByteArrayOutputStream()) {
            GeoJSON.write(set.getCollection(), stream);
            JSONObject coll = JSONObject.parseObject(stream.toString(CharsetUtil.UTF_8));
            set.setCollection(null);
            JSONObject result = JSONObject.from(set);
            result.put("Collection", coll);
            return result;
        }
    }

    @SuppressWarnings("unchecked")
    public static FeatureCollection<SimpleFeatureType, SimpleFeature> readGeoJson(String geoJson, String isConvert) throws Exception {
        cn.hutool.json.JSONObject jsonObject = new cn.hutool.json.JSONObject(geoJson);
        if (StrUtil.isNotBlank(isConvert) && isConvert.equals("1")) {
            Set<String> geometryType = getGeometryType(geoJson);
            if (geometryType.contains("MultiPolygon")) {
                jsonObject = GeoUtils.polygonToMultiPolygon(geoJson);
            }
        }
        FeatureJSON fjson = new FeatureJSON(new GeometryJSON(20));
        FeatureCollection<SimpleFeatureType, SimpleFeature> features = fjson.readFeatureCollection(jsonObject.toString());

        SimpleFeatureType schema = features.getSchema();
        Map<String, String> mapping = remappingFields(schema);
        List<String> newNames = new ArrayList<>(mapping.values());
        Map<String, String> reverseMapping = new HashMap<>();
        for (Map.Entry<String, String> entry : mapping.entrySet()) {
            reverseMapping.put(entry.getValue(), entry.getKey());
        }
        SimpleFeatureType schemaNew = recreate(schema, mapping, jsonObject.toString());
        int numAtts = schemaNew.getAttributeCount();
        List<SimpleFeature> outFeatures = new ArrayList<>();
        try (FeatureIterator<SimpleFeature> featuresIEnum = features.features()) {
            while (featuresIEnum.hasNext()) {
                SimpleFeature f = featuresIEnum.next();
                List<Object> attributes = new ArrayList<>(numAtts);
                attributes.add(f.getDefaultGeometry());
                for (AttributeDescriptor descriptor : schemaNew.getAttributeDescriptors()) {
                    if (!(descriptor instanceof GeometryDescriptor)) {
                        String newName = descriptor.getLocalName();
                        int index = newNames.indexOf(newName);
                        if (index != -1) {
                            String oldName = reverseMapping.get(newName);
                            attributes.add(f.getAttribute(oldName));
                        } else {
                            attributes.add(null);
                        }
                    }
                }

                SimpleFeature fNew = SimpleFeatureBuilder.build(schemaNew, attributes, f.getID());
                outFeatures.add(fNew);
            }
            return new ListFeatureCollection(schemaNew, outFeatures);
        }
    }

    private static Map<String, String> remappingFields(SimpleFeatureType schema) {
        Map<String, String> mapping = new HashMap<>();
        List<AttributeDescriptor> attributes = schema.getAttributeDescriptors();
        for (int i = 0; i < attributes.size(); i++) {
            AttributeDescriptor attrib = attributes.get(i);
            String originName = attrib.getLocalName();
            if (isAllChinese(originName)) {
                if (originName.length() > 3) {
                    String newName = originName.substring(0, 3);
                    mapping.put(originName, newName);
                } else {
                    mapping.put(originName, originName);
                }
            } else {
                if (originName.length() > 11) {
                    String newName = originName.substring(0, 10);
                    mapping.put(originName, newName);
                } else {
                    mapping.put(originName, originName);
                }
            }
        }
        return mapping;
    }

    private static boolean isAllChinese(String str) {
        for (int i = 0; i < str.length(); i++) {
            if (str.charAt(i) < 0x4e00 || str.charAt(i) > 0x9fa5) {
                return false;
            }
        }
        return true;
    }

    private static SimpleFeatureType recreate(SimpleFeatureType schema, Map<String, String> mapping, String geoJson) throws ClassNotFoundException {
        GeometryDescriptor geomDescNew = null;
        List<AttributeDescriptor> attrsNew = new ArrayList<>();
        GeometryDescriptor geomDesc = schema.getGeometryDescriptor();
        if (ObjectUtil.isNotNull(geomDesc)) {
            geomDescNew = new GeometryDescriptorImpl(
                geomDesc.getType(),
                new NameImpl("the_geom"),
                geomDesc.getMinOccurs(),
                geomDesc.getMaxOccurs(),
                geomDesc.isNillable(),
                geomDesc.getDefaultValue());
            attrsNew.add(geomDescNew);
        } else {
            cn.hutool.json.JSONObject geoJsonObject = JSONUtil.parseObj(geoJson);
            JSONArray tempFeatures = geoJsonObject.getJSONArray("features");
            Optional<String> geometryType = tempFeatures.stream()
                .map(feature -> (cn.hutool.json.JSONObject)feature)
                .filter(feature -> ObjectUtil.isNotNull(feature.get("geometry")))
                .map(feature -> feature.getJSONObject("geometry").getStr("type"))
                .findFirst();

            if (geometryType.isPresent()) {
                String type = geometryType.get();
                Class<?> geometryClass = Class.forName("org.locationtech.jts.geom." + type);

                GeometryType geomTypeNew = new GeometryTypeImpl(
                    new NameImpl("the_geom"),
                    geometryClass,
                    null,
                    false,
                    false,
                    null,
                    null,
                    null);

                geomDescNew = new GeometryDescriptorImpl(
                    geomTypeNew,
                    new NameImpl("the_geom"),
                    0, 1, true, null);
                attrsNew.add(geomDescNew);
            } else {
                throw new BusinessException("geometry均为空值，请重新获取结果");
            }
        }

        List<AttributeDescriptor> attrsOld = schema.getAttributeDescriptors();
        for (AttributeDescriptor att : attrsOld) {
            if (att != geomDesc) {
                String name = att.getLocalName();
                if (mapping.containsKey(name)) {
                    String newName = mapping.get(name);
                    AttributeDescriptor attNew = new AttributeDescriptorImpl(att.getType(),
                        new NameImpl(newName),
                        att.getMinOccurs(),
                        att.getMaxOccurs(),
                        att.isNillable(),
                        att.getDefaultValue());
                    attrsNew.add(attNew);
                } else {
                    attrsNew.add(att);
                }
            }
        }

        SimpleFeatureTypeBuilder featureTypeBuilder = new SimpleFeatureTypeBuilder();
        featureTypeBuilder.setName(schema.getName());
        featureTypeBuilder.setCRS(schema.getCoordinateReferenceSystem());
        featureTypeBuilder.setDefaultGeometry(geomDescNew.getLocalName());
        featureTypeBuilder.setAbstract(schema.isAbstract());
        featureTypeBuilder.setDescription(schema.getDescription());
        featureTypeBuilder.setAttributes(attrsNew);

        return featureTypeBuilder.buildFeatureType();
    }

    public static Set<String> getGeometryType(String geoJson) {
        cn.hutool.json.JSONObject geoJsonObject = JSONUtil.parseObj(geoJson);
        JSONArray tempFeatures = geoJsonObject.getJSONArray("features");
        Set<String> geometryTypes = new HashSet<>();
        tempFeatures.forEach(feature -> {
            cn.hutool.json.JSONObject featureObj = (cn.hutool.json.JSONObject)feature;
            if (ObjectUtil.isNotNull(featureObj.get("geometry"))) {
                String geometryType = featureObj.getJSONObject("geometry").getStr("type");
                geometryTypes.add(geometryType);
            }
        });
        return geometryTypes;
    }
}

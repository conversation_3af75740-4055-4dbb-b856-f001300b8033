package cn.com.sipsg.common.geo.core.service;

import com.alibaba.fastjson2.JSONArray;

import java.io.File;
import java.io.InputStream;

/**
 * 空间处理服务接口
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public interface GeoService {

    /**
     * GeoJson转文件
     *
     * @param geoJson GeoJson
     * @return 空间文件
     */
    File geoJson2File(JSONArray geoJson) throws Exception;

    /**
     * 文件转GeoJson
     *
     * @param inputStream    文件流
     * @param fileName       文件名
     * @param srid           坐标系
     * @param extent         空间范围
     * @param coordinateFlag 是否开启坐标系强制校验
     * @return GeoJson
     */
    JSONArray file2GeoJson(InputStream inputStream, String fileName, String srid, String extent, String coordinateFlag);

}

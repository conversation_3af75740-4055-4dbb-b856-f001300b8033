package cn.com.sipsg.common.geo.core.service.impl;

import cn.com.sipsg.common.exception.BusinessException;
import cn.com.sipsg.common.geo.config.GeoProperties;
import cn.com.sipsg.common.geo.core.service.GeoService;
import cn.com.sipsg.common.geo.core.util.GeoUtils;
import cn.com.sipsg.common.geo.core.util.ShapeFileUtils;
import cn.com.sipsg.common.util.AssertUtils;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import java.io.File;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.locationtech.jts.geom.Geometry;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

/**
 * 空间处理服务实现类
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GeoServiceImpl implements GeoService {

    private static final String FILE_TYPE_SHP = "shp";

    private static final String FILE_TYPE_ZIP = "zip";

    private static final String FILE_TYPE_DWG = "dwg";

    private final GeoProperties geoProperties;

    @Override
    public File geoJson2File(JSONArray geoJson) throws Exception {
        return ShapeFileUtils.features2ShapeFile(geoJson);
    }

    @Override
    public JSONArray file2GeoJson(InputStream inputStream, String fileName, String srid, String extent, String coordinateFlag) {
        File targetFile = null;
        // 文件扩展名
        String extName = FileUtil.extName(fileName).toLowerCase();
        if (!StrUtil.equalsAny(extName, FILE_TYPE_SHP, FILE_TYPE_ZIP, FILE_TYPE_DWG)) {
            throw new BusinessException("不支持的文件类型");
        }
        try {
            targetFile = ShapeFileUtils.shapeFile2Temp(inputStream, fileName);
            JSONArray array = new JSONArray();
            if (FILE_TYPE_SHP.equals(extName) || FILE_TYPE_ZIP.equals(extName)) {
                HttpRequest request = HttpRequest.post(geoProperties.getCoordinateTransformUrl() + "/geo/convert/shapeFile2GeoJsonBound")
                    .timeout(10000)
                    .form("file", targetFile);
                if (StrUtil.isNotBlank(srid)) {
                    request.form("srid", srid);
                }
                if (StrUtil.isNotBlank(extent)) {
                    request.form("extent", extent);
                }
                request.form("coordinateFlag", coordinateFlag);
                HttpResponse response = request.execute();

                JSONObject jsonObject = JSONObject.parseObject(response.body());
                AssertUtils.isTrue(jsonObject.getIntValue("code") != HttpStatus.OK.value(), jsonObject.getString("msg"));
                JSONObject resultJson = new JSONObject();
                JSONArray dataList = jsonObject.getJSONArray("data");
                JSONObject dataObject = dataList.getJSONObject(0);
                String name = dataObject.getString("Name");
                JSONObject collection = dataObject.getJSONObject("Collection");
                String spatialReferenceText = dataObject.getString("SpatialReferenceText");
                resultJson.put("Name", name);
                resultJson.put("Collection", collection);
                resultJson.put("SpatialReferenceText", spatialReferenceText);
                array.add(resultJson);
            } else {
                HttpResponse response = HttpRequest.post(geoProperties.getCoordinateTransformUrl() + "/geo/convert/dwg2GeoJson")
                    .timeout(10000)
                    .form("file", targetFile)
                    .execute();
                AssertUtils.isTrue(response.getStatus() != HttpStatus.OK.value(), "转换失败");
                JSONObject jsonObject = JSONObject.parseObject(response.body());
                AssertUtils.isTrue(jsonObject.getIntValue("code") != HttpStatus.OK.value(), jsonObject.getString("msg"));
                if (StrUtil.isNotBlank(extent)) {
                    List<Geometry> geometryList = new ArrayList<>();
                    JSONObject data = JSON.parseObject(jsonObject.get("data").toString());
                    JSONArray features = data.getJSONArray("features");
                    for (int i = 0; i < features.size(); i++) {
                        JSONObject feature = features.getJSONObject(i);
                        JSONObject geometryObject = feature.getJSONObject("geometry");
                        Geometry geometry = GeoUtils.geoJsonToGeometry(geometryObject.toString());
                        geometryList.add(geometry);
                    }

                    Map<String, Double> envelopeMap = GeoUtils.getEnvelopeMap(geometryList);
                    if (!GeoUtils.getRangeVerification(envelopeMap, extent)) {
                        throw new RuntimeException("请检查数据范围");
                    }
                }
                JSONObject resultJson = new JSONObject();
                resultJson.put("Name", fileName);
                resultJson.put("Collection", JSONUtil.parse(jsonObject.get("data")));
                resultJson.put("SpatialReferenceText", null);
                array.add(resultJson);
            }
            return array;
        } catch (Exception e) {
            log.error("上传的文件转GeoJson失败", e);
            throw new BusinessException(e.getMessage());
        } finally {
            IoUtil.close(inputStream);
            FileUtil.del(targetFile.getParentFile());
        }
    }

}

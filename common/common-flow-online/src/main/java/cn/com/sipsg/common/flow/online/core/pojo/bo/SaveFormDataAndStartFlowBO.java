package cn.com.sipsg.common.flow.online.core.pojo.bo;

import cn.com.sipsg.common.flow.core.pojo.dto.FlowTaskCommentDTO;
import com.alibaba.fastjson2.JSONObject;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

/**
 * 表单提交并发起流程BO
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Getter
@Setter
@Schema(description = "表单提交并发起流程BO")
public class SaveFormDataAndStartFlowBO {

    /**
     * 流程标识
     */
    @Schema(description = "流程标识")
    @NotNull(message = "流程标识不能为空")
    private String processDefinitionKey;

    /**
     * 审批提交数据
     */
    @Schema(description = "审批提交数据")
    @NotNull(message = "流程审批对象不能为空")
    private FlowTaskCommentDTO flowTaskCommentDTO;

    /**
     * 任务提交变量
     */
    @Schema(description = "任务提交变量")
    @NotNull(message = "任务提交变量不能为空")
    private JSONObject taskVariableData;

    /**
     * 表单填写数据对象
     */
    @Schema(description = "表单填写数据对象")
    @NotNull(message = "表单填写数据对象不能为空")
    private JSONObject formData;


    /**
     * 抄送数据
     */
    @Schema(description = "抄送数据")
    private JSONObject copyData;

    /**
     * 流程实例id
     */
    @Schema(description = "processInstanceId")
    private String processInstanceId;

    /**
     * 任务id
     */
    @Schema(description = "taskId")
    private String taskId;


    /**
     * 工单记录表主键id值
     */
    @Schema(description = "工单记录表主键id值")
    private String workOrderId;

    /**
     * 表单formId
     */
    @Schema(description = "formId")
    private String formId;

}

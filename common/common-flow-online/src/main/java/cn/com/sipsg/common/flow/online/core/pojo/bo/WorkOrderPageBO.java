package cn.com.sipsg.common.flow.online.core.pojo.bo;

import cn.com.sipsg.common.pojo.bo.SortablePageBO;
import cn.hutool.core.date.DatePattern;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

/**
 * 工单列表分页查询BO对象
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Setter
@Getter
@Schema(description = "工单列表分页查询BO对象")
public class WorkOrderPageBO extends SortablePageBO {

    /**
     * 流程定义key
     */
    @Schema(description = "流程定义key")
    @NotBlank(message = "流程定义key不能为空")
    private String processDefinitionKey;

    /**
     * 工单编号
     */
    @Schema(description = "工单编号")
    private String workOrderCode;

    /**
     * 工单状态
     */
    @Schema(description = "工单状态")
    private Integer flowStatus;


    /**
     * createTime 范围过滤起始值
     */
    @Schema(description = "createTime 范围过滤起始值")
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime createTimeStart;

    /**
     * createTime 范围过滤结束值(<=)。
     */
    @Schema(description = "createTime 范围过滤结束值")
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime createTimeEnd;

}

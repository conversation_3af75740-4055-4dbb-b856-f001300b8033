package cn.com.sipsg.common.flow.online.core.service.impl;

import cn.com.sipsg.common.flow.core.base.service.BaseFlowOnlineService;
import cn.com.sipsg.common.flow.core.util.FlowCustomExtFactory;
import cn.com.sipsg.common.online.core.pojo.dto.OnlineCrudFormDTO;
import cn.com.sipsg.common.online.core.service.OnlineCrudService;
import cn.com.sipsg.common.online.core.service.OnlineSubFormService;
import cn.com.sipsg.common.util.AssertUtils;
import javax.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 在线表单和流程监听器进行数据对接时的服务实现类。
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Slf4j
@RequiredArgsConstructor
@Service("flowOnlineBusinessService")
public class FlowOnlineBusinessServiceImpl implements BaseFlowOnlineService {


    private final FlowCustomExtFactory flowCustomExtFactory;

    private final OnlineSubFormService onlineSubFormService;

    private final OnlineCrudService onlineCrudService;

    @PostConstruct
    public void doRegister() {
        flowCustomExtFactory.getOnlineBusinessDataExtHelper().setOnlineBusinessService(this);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateFlowStatus(String fromId, String businessKey) {

    }

    @Override
    public void deleteBusinessData(String fromId, String businessKey) {
        OnlineCrudFormDTO onlineCrudFormDTO = onlineSubFormService.configList(fromId);
        AssertUtils.isTrue(null == onlineCrudFormDTO, "根据fromId未获取表单信息");
        // 调用动态表单接口删除表单数据
        onlineCrudService.delete(onlineCrudFormDTO, businessKey);
    }

}

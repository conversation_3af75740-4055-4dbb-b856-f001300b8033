package cn.com.sipsg.common.flow.online.core.controller;

import cn.com.sipsg.common.exception.BusinessException;
import cn.com.sipsg.common.flow.core.constant.FlowMessageType;
import cn.com.sipsg.common.flow.core.entity.FlowEntry;
import cn.com.sipsg.common.flow.core.entity.FlowEntryPublish;
import cn.com.sipsg.common.flow.core.entity.FlowMessage;
import cn.com.sipsg.common.flow.core.entity.FlowWorkOrder;
import cn.com.sipsg.common.flow.core.entity.FlowWorkOrderExt;
import cn.com.sipsg.common.flow.core.pojo.dto.FlowRespDTO;
import cn.com.sipsg.common.flow.core.pojo.vo.FlowEntryVO;
import cn.com.sipsg.common.flow.core.pojo.vo.FlowStartAndTakeUserTaskVO;
import cn.com.sipsg.common.flow.core.pojo.vo.FlowTaskVO;
import cn.com.sipsg.common.flow.core.pojo.vo.FlowWorkOrderVO;
import cn.com.sipsg.common.flow.core.pojo.vo.TaskInfoVO;
import cn.com.sipsg.common.flow.core.service.FlowApiService;
import cn.com.sipsg.common.flow.core.service.FlowEntryService;
import cn.com.sipsg.common.flow.core.service.FlowMessageService;
import cn.com.sipsg.common.flow.core.service.FlowWorkOrderService;
import cn.com.sipsg.common.flow.core.util.FlowOperationHelper;
import cn.com.sipsg.common.flow.online.core.pojo.bo.SaveFormDataAndStartFlowBO;
import cn.com.sipsg.common.flow.online.core.pojo.bo.StartAndSaveDraftBO;
import cn.com.sipsg.common.flow.online.core.pojo.bo.SubmitUserTaskBO;
import cn.com.sipsg.common.flow.online.core.pojo.bo.WorkOrderPageBO;
import cn.com.sipsg.common.flow.online.core.service.FlowOnlineOperationService;
import cn.com.sipsg.common.online.core.entity.OnlineSubForm;
import cn.com.sipsg.common.online.core.pojo.dto.OnlineCrudFormDTO;
import cn.com.sipsg.common.online.core.service.OnlineCrudService;
import cn.com.sipsg.common.online.core.service.OnlineSubFormService;
import cn.com.sipsg.common.operationlog.core.annotation.OperationLog;
import cn.com.sipsg.common.operationlog.core.enums.OperationTypeEnum;
import cn.com.sipsg.common.pojo.CommonResult;
import cn.com.sipsg.common.pojo.vo.CommonPageVO;
import cn.com.sipsg.common.util.AssertUtils;
import cn.com.sipsg.common.util.BeanUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.Task;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 工作流-动态表单操作控制器
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Tag(name = "工作流-动态表单操作控制器")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/flowOnline/flowOnlineOperation")
public class FlowOnlineOperationController {

    private final FlowEntryService flowEntryService;

    private final OnlineSubFormService onlineSubFormService;

    private final FlowOnlineOperationService flowOnlineOperationService;

    private final FlowApiService flowApiService;

    private final FlowOperationHelper flowOperationHelper;

    private final FlowWorkOrderService flowWorkOrderService;

    private final OnlineCrudService onlineCrudService;

    private final FlowMessageService flowMessageService;


    /**
     * 保存草稿数据，并创建工单
     *
     * @param startAndSaveDraftBO 保存草稿BO对象
     * @return 草稿创建的数据
     */
    @OperationLog(module = "工作流和动态表单操作模块", value = "保存草稿数据，并创建工单", type = OperationTypeEnum.SAVE)
    @Operation(summary = "保存草稿数据，并创建工单")
    @PostMapping("/saveOrUpdateDraft")
    public CommonResult<FlowTaskVO> startAndSaveDraft(@Validated @RequestBody StartAndSaveDraftBO startAndSaveDraftBO) {
        flowOnlineOperationService.saveOrUpdateDraft(startAndSaveDraftBO);
        return CommonResult.success();
    }


    /**
     * 保存表单数据并且发起流程
     *
     * @param saveFormDataAndStartFlowBO 表单数据提交和发起流程BO对象
     */
    @Operation(summary = "保存表单数据并且发起流程")
    @OperationLog(module = "工作流和动态表单操作模块", value = "保存表单数据并且发起流程", type = OperationTypeEnum.SAVE)
    @PostMapping("/startAndTakeUserTask")
    public CommonResult startAndTakeUserTask(@RequestBody @Validated SaveFormDataAndStartFlowBO saveFormDataAndStartFlowBO) {
        flowOnlineOperationService.saveFormDataAndStartFlow(saveFormDataAndStartFlowBO);
        return CommonResult.success();
    }


    /**
     * 审批用户任务
     *
     * @param submitUserTaskBO 审批用户任务的BO对象
     */
    @OperationLog(module = "工作流和动态表单操作模块", value = "审批用户任务", type = OperationTypeEnum.UPDATE)
    @Operation(summary = "审批用户任务")
    @PostMapping("/submitUserTask")
    public CommonResult submitUserTask(@Validated @RequestBody SubmitUserTaskBO submitUserTaskBO) {
        AssertUtils.isTrue(StringUtils.isBlank(submitUserTaskBO.getFlowTaskCommentDTO().getDeptOfApproveUser()), "请确认用户所属部门关系！");
        flowOnlineOperationService.submitUserTask(submitUserTaskBO);
        return CommonResult.success();
    }


    /**
     * 查看流程实例草稿数据
     *
     * @param workOrderId 工单表主键id
     * @return 草稿数据
     */
    @OperationLog(module = "工作流和动态表单操作模块", value = "查看流程实例草稿数据", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "查看流程实例草稿数据")
        @Parameter(name = "workOrderId", description = "工单表主键id", required = true)
    @GetMapping("/viewDraftData")
    public CommonResult<JSONObject> viewDraftData(@RequestParam String workOrderId) {
        FlowWorkOrder flowWorkOrder = flowWorkOrderService.getById(workOrderId);
        AssertUtils.isTrue(flowWorkOrder == null, "未查询到工单对应的草稿数据！");
        FlowWorkOrderExt flowWorkOrderExt = flowWorkOrderService.getFlowWorkOrderExtByWorkOrderId(flowWorkOrder.getWorkOrderId());
        if (StrUtil.isBlank(flowWorkOrderExt.getDraftData())) {
            return CommonResult.data(null);
        }
        //目前是将保存的草稿字符串转换成JSONObject对象的形式返回给前端展示
        return CommonResult.data(JSONObject.parseObject(flowWorkOrderExt.getDraftData()));
    }


    /**
     * 获取当前流程实例的表单数据
     *
     * @param processInstanceId 流程实例id
     * @param taskId            任务id
     * @return 返回的表单数据JSONObject对象
     */
    @OperationLog(module = "工作流和动态表单操作模块", value = "获取当前流程实例的表单数据", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "获取当前流程实例的表单数据")
    @Parameters({
        @Parameter(name = "processInstanceId", description = "流程实例id", required = true),
        @Parameter(name = "taskId", description = "任务id", required = true)
    })
    @GetMapping("/viewUserTask")
    public CommonResult<Map<String, Object>> viewUserTask(@RequestParam String processInstanceId, @RequestParam String taskId) {
        // 验证流程任务的合法性
        Task task = flowApiService.getProcessInstanceActiveTask(processInstanceId, taskId);
        ProcessInstance instance = flowApiService.getProcessInstance(processInstanceId);
        // 如果业务主数据为空，则直接返回。
        if (StrUtil.isBlank(instance.getBusinessKey())) {
            return CommonResult.data(null);
        }
        TaskInfoVO taskInfoVO = flowOperationHelper.verifyAndGetRuntimeTaskInfo(task);
        // 验证在线表单及其关联数据源的合法性。
        OnlineCrudFormDTO onlineCrudFormDTO = onlineSubFormService.configList(taskInfoVO.getFormId());
        AssertUtils.isTrue(null == onlineCrudFormDTO, "根据fromId未获取表单信息");
        // 调用动态表单接口获取表单数据
        Map<String, Object> detail = onlineCrudService.detail(onlineCrudFormDTO, instance.getBusinessKey());
        return CommonResult.data(detail);
    }

    /**
     * 获取已经结束的流程实例的表单数据
     *
     * @param processInstanceId 流程实例id
     * @param taskId            任务id
     * @return 表单数据 JSONObject 对象
     */
    @OperationLog(module = "工作流和动态表单操作模块", value = "获取已经结束的流程实例的表单数据", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "获取已经结束的流程实例的表单数据")
    @Parameters({
        @Parameter(name = "processInstanceId", description = "流程实例id", required = true),
        @Parameter(name = "taskId", description = "任务id", required = true)
    })
    @GetMapping("/viewHistoricProcessInstance")
    public CommonResult<Map<String, Object>> viewHistoricProcessInstance(@RequestParam String processInstanceId, @RequestParam(required = false) String taskId) {
        // 验证流程实例的合法性。
        HistoricProcessInstance historicProcessInstance = flowOperationHelper.verifyAndGetHistoricProcessInstance(processInstanceId, taskId);
        if (StrUtil.isBlank(historicProcessInstance.getBusinessKey())) {
            // 对于没有提交过任何用户任务的场景，可直接返回空数据。
            return CommonResult.data(new JSONObject());
        }
        FlowEntryPublish flowEntryPublish = flowEntryService.getFlowEntryPublishList(CollUtil.newHashSet(historicProcessInstance.getProcessDefinitionId())).get(0);
        TaskInfoVO taskInfoVO = JSON.parseObject(flowEntryPublish.getInitTaskInfo(), TaskInfoVO.class);
        // 验证在线表单及其关联数据源的合法性。
        OnlineCrudFormDTO onlineCrudFormDTO = onlineSubFormService.configList(taskInfoVO.getFormId());
        AssertUtils.isTrue(null == onlineCrudFormDTO, "根据fromId未获取表单信息");
        // 调用动态表单接口获取表单数据
        Map<String, Object> detail = onlineCrudService.detail(onlineCrudFormDTO, historicProcessInstance.getBusinessKey());
        return CommonResult.data(detail);
    }


    /**
     * 根据消息Id，获取流程Id关联的业务数据
     *
     * @param messageId 抄送消息Id
     * @return 抄送消息关联的表单业务数据
     */
    @OperationLog(module = "工作流和动态表单操作模块", value = "根据消息Id，获取流程Id关联的业务数据", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "根据消息Id，获取流程Id关联的业务数据")
    @Parameter(name = "messageId", description = "抄送消息id", required = true)
    @GetMapping("/viewCopyBusinessData")
    public CommonResult<Map<String, Object>> viewCopyBusinessData(@RequestParam String messageId) {
        // 验证流程任务的合法性。
        FlowMessage flowMessage = flowMessageService.checkExist(messageId);
        if (flowMessage.getMessageType() != FlowMessageType.COPY_TYPE) {
            throw new BusinessException("数据验证失败，当前消息不是抄送类型消息！");
        }
        if (flowMessage.getOnlineFormData() == null || !flowMessage.getOnlineFormData()) {
            throw new BusinessException("数据验证失败，当前消息为静态路由表单数据，不能通过该接口获取！");
        }
        if (!flowMessageService.isCandidateIdentityOnMessage(messageId)) {
            throw new BusinessException("数据验证失败，当前用户没有权限访问该消息！");
        }
        HistoricProcessInstance instance = flowApiService.getHistoricProcessInstance(flowMessage.getProcessInstanceId());
        // 如果业务主数据为空，则直接返回。
        if (StrUtil.isBlank(instance.getBusinessKey())) {
            throw new BusinessException("数据验证失败，当前消息为所属流程实例没有包含业务主键Id！");
        }
        String formId = flowMessage.getBusinessDataShot();
        // 验证在线表单及其关联数据源的合法性
        OnlineCrudFormDTO onlineCrudFormDTO = onlineSubFormService.configList(formId);
        AssertUtils.isTrue(null == onlineCrudFormDTO, "根据fromId未获取表单信息");
        // 调用动态表单接口获取表单数据
        Map<String, Object> detail = onlineCrudService.detail(onlineCrudFormDTO, instance.getBusinessKey());
        // 将当前消息更新为已读
        flowMessageService.readCopyTask(messageId);
        return CommonResult.data(detail);
    }


    /**
     * 工作流工单列表查询
     *
     * @param workOrderPageBO 工单列表查询对象
     * @return 分页结果对象
     */
    @OperationLog(module = "工作流和动态表单操作模块", value = "工单列表分页查询", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "工单列表分页查询")
    @PostMapping("/listWorkOrder")
    public CommonResult<CommonPageVO<FlowWorkOrderVO>> listWorkOrder(@RequestBody @Validated WorkOrderPageBO workOrderPageBO) {
        CommonPageVO<FlowWorkOrderVO> pageVO = flowOnlineOperationService.page(workOrderPageBO);
        return CommonResult.data(pageVO);
    }

    /**
     * 根据流程实例processInstanceId查询工单信息（工单状态）
     *
     * @param processInstanceId 流程实例id
     * @return 工单对象
     */
    @OperationLog(module = "工作流和动态表单操作模块", value = "根据流程实例processInstanceId查询工单信息", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "根据流程实例processInstanceId查询工单信息")
    @Parameter(name = "processInstanceId", description = "流程实例id", required = true)
    @GetMapping("/getFlowWorkOrderByProcessInstanceId")
    public CommonResult<FlowWorkOrder> listWorkOrder(@RequestParam String processInstanceId) {
        FlowWorkOrder flowWorkOrder = flowWorkOrderService.getFlowWorkOrderByProcessInstanceId(processInstanceId);
        return CommonResult.data(flowWorkOrder);
    }


    /**
     * 获取所有流程对象，同时获取流程绑定的动态表单对象列表
     *
     * @return 流程设计对象，报错流程配置的表单信息
     */
    @OperationLog(module = "工作流和动态表单操作模块", value = "获取所有流程对象，同时获取流程绑定的在线表单对象列表", type = OperationTypeEnum.QUERY, saveResponse = false)
    @Operation(summary = "获取所有流程对象，同时获取关联的动态表单对象列表")
    @GetMapping("/listFlowEntryForm")
    public CommonResult<List<FlowEntryVO>> listFlowEntryForm() {
        List<FlowEntry> flowEntryList = flowEntryService.list();
        List<FlowEntryVO> flowEntryVoList = BeanUtils.copyToList(flowEntryList, FlowEntryVO.class);
        if (CollUtil.isNotEmpty(flowEntryVoList)) {
            Set<String> pageIdSet = flowEntryVoList.stream().map(FlowEntryVO::getPageId).collect(Collectors.toSet());
            // 根据pageId查询page下的表单
            List<OnlineSubForm> formList = onlineSubFormService.getFormListByMainIds(pageIdSet);
            formList.forEach(f -> f.setWidgetJson(null));
            Map<String, List<OnlineSubForm>> formMap = formList.stream().collect(Collectors.groupingBy(OnlineSubForm::getMainId));
            for (FlowEntryVO flowEntryVO : flowEntryVoList) {
                List<OnlineSubForm> flowEntryFormList = formMap.get(flowEntryVO.getPageId());
                flowEntryVO.setFormList(BeanUtils.copyToMapList(flowEntryFormList));
            }
        }
        return CommonResult.data(flowEntryVoList);
    }

    /**
     * 表单是由动态表单设计出来的页面，流程又是配置成自定义表单样子，此时保存路由表单数据并且发起流程(此接口非通用接口)
     *
     * @param saveFormDataAndStartFlowBO 表单数据提交和发起流程BO对象
     */
    @Operation(summary = "保存表单部分数据并且发起流程")
    @OperationLog(module = "工作流和动态表单操作模块", value = "保存表单部分数据并且发起流程", type = OperationTypeEnum.SAVE)
    @PostMapping("/savePartFormDataAndStartFlow")
    public CommonResult<FlowStartAndTakeUserTaskVO> savePartFormDataAndStartFlow(@RequestBody @Validated SaveFormDataAndStartFlowBO saveFormDataAndStartFlowBO) {
        FlowRespDTO flowRespDTO = flowOnlineOperationService.savePartFormDataAndStartFlow(saveFormDataAndStartFlowBO);
        FlowStartAndTakeUserTaskVO flowStartAndTakeUserTaskVO = BeanUtils.copyProperties(flowRespDTO, FlowStartAndTakeUserTaskVO.class);
        return CommonResult.data(flowStartAndTakeUserTaskVO);
    }

}

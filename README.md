# 轨道交通服务器系统

## 项目简介

轨道交通服务器系统是一个基于Spring Boot微服务架构的综合管理平台，主要用于轨道交通相关业务的管理和监控。

## 技术架构

- **框架**: Spring Boot + Spring Cloud Gateway
- **数据库**: MySQL + PostgreSQL + Doris + Redis + MyBatis-Plus
- **认证**: Sa-Token
- **文档**: Swagger/Knife4j
- **构建工具**: Maven
- **JDK版本**: Java 8+

## 模块结构

```
rail-transit-server/
├── common/                          # 公共模块
│   ├── common-core/                # 核心工具类
│   ├── common-web/                 # Web相关配置
│   ├── common-security/            # 安全认证
│   ├── common-redis/               # Redis配置
│   ├── common-mybatis/             # MyBatis配置
│   ├── common-swagger/             # API文档配置
│   └── ...                        # 其他公共模块
├── rail-transit-server-gateway/     # 网关服务
├── rail-transit-server-module-system/  # 系统管理模块
│   ├── rail-transit-server-module-system-api/
│   └── rail-transit-server-module-system-biz/
├── rail-transit-server-module-business/ # 业务管理模块
│   ├── rail-transit-server-module-business-api/
│   └── rail-transit-server-module-business-biz/
└── rail-transit-server-module-data/    # 数据管理模块
    ├── rail-transit-server-module-data-api/
    └── rail-transit-server-module-data-biz/
```

## 快速开始

### 环境要求

- JDK 8+
- Maven 3.6+
- MySQL 5.7+
- Redis 3.0+

### 安装步骤

1. **克隆项目**
   ```bash
   git clone [项目地址]
   cd rail-transit-server
   ```

2. **数据库配置**
   - 创建MySQL数据库
   - 执行数据库初始化脚本
   - 配置Redis连接

3. **修改配置文件**
   ```bash
   # 修改各模块的application.yml配置文件
   # 配置数据库连接信息
   # 配置Redis连接信息
   ```

4. **编译项目**
   ```bash
   mvn clean install
   ```

5. **启动服务**
   ```bash
   # 启动网关服务
   cd rail-transit-server-gateway
   mvn spring-boot:run
   
   # 启动系统管理模块
   cd rail-transit-server-module-system/rail-transit-server-module-system-biz
   mvn spring-boot:run
   
   # 启动业务管理模块
   cd rail-transit-server-module-business/rail-transit-server-module-business-biz
   mvn spring-boot:run
   
   # 启动数据管理模块
   cd rail-transit-server-module-data/rail-transit-server-module-data-biz
   mvn spring-boot:run
   ```

## 系统访问

### 默认账号密码
- **用户名**: `sysadmin`
- **密码**: `123@abcd`

### 接口文档地址
- **Swagger文档**: http://localhost:9999/doc.html
- **系统管理**: http://localhost:9999/system/
- **业务管理**: http://localhost:9999/business/
- **数据管理**: http://localhost:9999/data/

## 主要功能模块

### 系统管理模块
- 用户管理
- 角色管理
- 权限管理
- 菜单管理
- 部门管理
- 字典管理
- 操作日志

### 业务管理模块
- 监督管理
- 通知公告
- 业务流程管理
- 数据统计分析

### 数据管理模块
- 数据采集
- 数据处理
- 数据存储
- 数据查询

## 开发规范

### 代码规范
- 遵循阿里巴巴Java开发手册
- 使用统一的代码格式化配置
- 必要的注释和文档

### 接口规范
- RESTful API设计
- 统一的返回格式
- 完整的接口文档

### 数据库规范
- 统一的命名规范
- 合理的索引设计
- 数据库版本管理

## 部署说明

### Docker部署
```bash
# 构建镜像
docker build -t rail-transit-server .

# 运行容器
docker run -d -p 9999:9999 rail-transit-server
```

### 生产环境部署
1. 配置生产环境数据库
2. 修改生产环境配置文件
3. 使用Maven打包
4. 部署到服务器

## 常见问题

### Q: 启动时出现端口占用错误？
A: 检查端口是否被占用，修改配置文件中的端口号。

### Q: 数据库连接失败？
A: 检查数据库配置信息，确保数据库服务正常运行。

### Q: Redis连接失败？
A: 检查Redis配置信息，确保Redis服务正常运行。

## 更新日志

### v1.0.0 (2025-07-29)
- 初始版本发布
- 基础功能模块完成
- 系统管理功能
- 业务管理功能
- 数据管理功能

## 联系方式

如有问题或建议，请联系开发团队。

## 许可证

本项目采用 [许可证名称] 许可证。
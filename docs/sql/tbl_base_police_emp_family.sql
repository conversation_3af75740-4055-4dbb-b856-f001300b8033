-- 警员家庭成员关联表
CREATE TABLE tbl_base_police_emp_family (
    id VARCHAR(64) NOT NULL COMMENT '主键ID',
    police_emp_id VARCHAR(64) NOT NULL COMMENT '警员ID',
    relationship VARCHAR(50) NOT NULL COMMENT '关系（如：配偶、子女、父母等）',
    name VARCHAR(100) NOT NULL COMMENT '家庭成员姓名',

    available BOOLEAN DEFAULT TRUE COMMENT '是否有效（逻辑删除标识）',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_user_id VARCHAR(64) COMMENT '创建用户ID',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    update_user_id VARCHAR(64) COMMENT '更新用户ID',
    PRIMARY KEY (id)
);

-- 表注释
COMMENT ON TABLE tbl_base_police_emp_family IS '警员家庭成员关联表，用于存储警员的家庭成员信息';

-- 字段注释
COMMENT ON COLUMN tbl_base_police_emp_family.id IS '主键ID';
COMMENT ON COLUMN tbl_base_police_emp_family.police_emp_id IS '警员ID，关联tbl_base_police_emp表';
COMMENT ON COLUMN tbl_base_police_emp_family.relationship IS '关系（如：配偶、子女、父母等）';
COMMENT ON COLUMN tbl_base_police_emp_family.name IS '家庭成员姓名';

COMMENT ON COLUMN tbl_base_police_emp_family.available IS '是否有效（逻辑删除标识）';
COMMENT ON COLUMN tbl_base_police_emp_family.create_time IS '创建时间';
COMMENT ON COLUMN tbl_base_police_emp_family.create_user_id IS '创建用户ID';
COMMENT ON COLUMN tbl_base_police_emp_family.update_time IS '更新时间';
COMMENT ON COLUMN tbl_base_police_emp_family.update_user_id IS '更新用户ID';

-- 创建索引
-- 警员ID索引（用于根据警员查询家庭成员）
CREATE INDEX idx_police_emp_family_police_emp_id ON tbl_base_police_emp_family(police_emp_id);

-- 有效性索引（用于逻辑删除查询）
CREATE INDEX idx_police_emp_family_available ON tbl_base_police_emp_family(available);

-- 复合索引（警员ID + 有效性，用于查询某警员的有效家庭成员）
CREATE INDEX idx_police_emp_family_police_emp_id_available ON tbl_base_police_emp_family(police_emp_id, available);

-- 姓名索引（用于按姓名搜索家庭成员）
CREATE INDEX idx_police_emp_family_name ON tbl_base_police_emp_family(name);

-- 关系索引（用于按关系类型查询）
CREATE INDEX idx_police_emp_family_relationship ON tbl_base_police_emp_family(relationship);
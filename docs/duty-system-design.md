# 轨道交通值班排班系统设计文档

## 1. 系统概述

轨道交通值班排班系统是一个用于管理警务人员值班安排的综合性系统，支持岗位管理、班次设置、班组管理、排班规则制定和人员排班等核心功能。

## 2. 数据库表结构关系

### 2.1 核心表概览

| 表名 | 中文名称 | 主要作用 |
|------|----------|----------|
| `tbl_duty_post` | 岗位表 | 定义值班岗位（如：巡逻岗、1号口） |
| `tbl_duty_turn` | 班次表 | 定义时间段（如：白班、夜班） |
| `tbl_duty_turn_group` | 班组表 | 岗位班组管理（如：1号岗夜班A组、2号岗白班B组） |
| `tbl_duty_platoon_group` | 排班组表 | 人员排班组（如：第一排班组、第二排班组） |
| `tbl_duty_shifts` | 排班规则表 | 轮班策略（如：7天轮一次，5休2） |
| `tbl_duty_shifts_detail` | 排班规则明细表 | 具体规则（周一上夜班、周二休息） |
| `tbl_duty_emp` | **排班结果表** | **核心表：最终排班结果** |
| `tbl_duty_emp_shift_detail` | 排班调整历史表 | 调班轨迹记录 |

### 2.2 辅助表

| 表名 | 中文名称 | 主要作用 |
|------|----------|----------|
| `tbl_base_police_emp` | 警务人员表 | 存储人员基本信息 |
| `tbl_base_police_org` | 警务机构表 | 存储组织架构信息 |
| `tbl_base_station` | 站点表 | 存储地铁站点信息 |

## 3. 表关系详解

### 3.1 核心业务关系

```mermaid
graph TD
    A[岗位 tbl_duty_post] --> B[班组 tbl_duty_turn_group]
    C[班次 tbl_duty_turn] --> B
    D[排班规则 tbl_duty_shifts] --> E[排班规则明细 tbl_duty_shifts_detail]
    B --> F[排班结果 tbl_duty_emp]
    E --> F
    G[排班组 tbl_duty_platoon_group] --> F
    H[警务人员 tbl_base_police_emp] --> F
    F --> I[排班调整历史 tbl_duty_emp_shift_detail]
```

### 3.2 关键关系说明

#### 3.2.1 岗位与班次关系
- **岗位（Post）** ←→ **班次（Turn）**
- 通过中间表 `tbl_duty_post_turn` 定义"这个岗位能排什么班"
- 例如：1号岗亭支持白班、夜班

#### 3.2.2 岗位与班组关系
- **岗位（Post）** ←→ **班组（Turn Group）**
- 例如：1号岗亭分配给夜班A组负责

#### 3.2.3 班组与排班规则关系
- **班组（Turn Group）** ←→ **排班规则（Shifts）**
- 例如：夜班A组使用7天轮一次的排班规则

#### 3.2.4 最终排班关系
- **排班结果（Emp）** = 最终生效的排班记录
- 直接查这个表就能知道：谁、哪天、值哪岗、上哪班

## 4. 业务流程

### 4.1 排班设置流程

```mermaid
sequenceDiagram
    participant Admin as 管理员
    participant System as 排班系统
    participant DB as 数据库

    Admin->>System: 1. 定义岗位
    System->>DB: 插入 tbl_duty_post
    
    Admin->>System: 2. 定义班次
    System->>DB: 插入 tbl_duty_turn
    
    Admin->>System: 3. 创建班组
    System->>DB: 插入 tbl_duty_turn_group
    
    Admin->>System: 4. 制定排班规则
    System->>DB: 插入 tbl_duty_shifts
    System->>DB: 插入 tbl_duty_shifts_detail
    
    Admin->>System: 5. 执行排班
    System->>DB: 生成 tbl_duty_emp 记录
```

### 4.2 日常排班流程

1. **定规则** → 使用 `tbl_duty_shifts` 和 `tbl_duty_shifts_detail` 制定轮班规则
2. **绑班组** → 确定哪个班组使用这个排班规则
3. **班组负责岗位** → 岗位和班组绑定
4. **按规则排人** → 最终生成到 `tbl_duty_emp` 里
5. **调班改班** → 操作历史记在 `tbl_duty_emp_shift_detail`

## 5. 核心字段说明

### 5.1 tbl_duty_emp（排班结果表）
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | Long | 主键ID |
| emp_id | String | 人员ID（关联 tbl_base_police_emp） |
| duty_date | String | 值班日期 |
| post_id | Long | 岗位ID（关联 tbl_duty_post） |
| turn_id | Long | 班次ID（关联 tbl_duty_turn） |
| police_org_id | String | 所属机构ID |
| available | Boolean | 是否有效 |

### 5.2 tbl_duty_post（岗位表）
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | Long | 主键ID |
| post_name | String | 岗位名称 |
| post_role | String | 岗位职责 |
| police_org_id | String | 所属机构ID |

### 5.3 tbl_duty_turn（班次表）
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | Long | 主键ID |
| turn_name | String | 班次名称 |
| start_time | String | 开始时间 |
| end_time | String | 结束时间 |
| start_day | Integer | 开始日期偏移 |
| end_day | Integer | 结束日期偏移 |

## 6. 查询场景示例

### 6.1 查询当天值班人员
```sql
SELECT 
    emp.name as emp_name,
    post.post_name,
    turn.turn_name,
    org.name as org_name
FROM tbl_duty_emp de
LEFT JOIN tbl_base_police_emp emp ON de.emp_id = emp.id
LEFT JOIN tbl_duty_post post ON de.post_id = post.id
LEFT JOIN tbl_duty_turn turn ON de.turn_id = turn.id
LEFT JOIN tbl_base_police_org org ON de.police_org_id = org.id
WHERE de.duty_date = CURDATE()
AND de.available = true;
```

### 6.2 查询班组成员
```sql
SELECT DISTINCT
    emp.id,
    emp.name,
    emp.code as police_number
FROM tbl_duty_emp de
LEFT JOIN tbl_base_police_emp emp ON de.emp_id = emp.id
WHERE de.post_id = ? 
AND de.available = true
AND de.duty_date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
ORDER BY emp.name;
```

### 6.3 查询站点值班统计
```sql
SELECT 
    station.name as station_name,
    org.name as org_name,
    COUNT(de.id) as police_count
FROM tbl_base_station station
LEFT JOIN tbl_base_police_org org ON station.police_org_id = org.id
LEFT JOIN tbl_duty_emp de ON de.police_org_id = org.id 
    AND de.duty_date = CURDATE() 
    AND de.available = true
WHERE station.status = 2
GROUP BY station.id, org.id
ORDER BY police_count DESC;
```

## 7. 系统特点

### 7.1 优势
- **灵活性强**：支持复杂的排班规则设置
- **可追溯性**：完整的调班历史记录
- **扩展性好**：模块化设计，易于扩展
- **数据完整性**：严格的外键约束保证数据一致性

### 7.2 核心设计原则
- **分离关注点**：岗位、班次、人员、规则分别管理
- **历史可追溯**：所有变更都有历史记录
- **规则驱动**：通过规则自动生成排班
- **灵活调整**：支持临时调班和特殊安排

## 8. 现有接口分析

### 8.1 已实现的核心接口

#### 8.1.1 值班动态管理
- `onDutyDynamic()` - 值班动态统计，符合MD设计逻辑
- `getStationOnDutyInfo(stationCode)` - 根据站点查询在岗警力
- `getStationPoliceCount()` - 获取各站点值班民警数量统计

#### 8.1.2 基础数据管理
- `saveDutyTurn(dto)` - 新增/修改班次类型，符合tbl_duty_turn表设计
- `deleteDutyTurn(id)` - 删除班次类型
- `addPostCategory(dto)` - 新增岗位类别，符合tbl_duty_post表设计
- `listOrg()` - 获取单位列表

#### 8.1.3 班组管理
- `listDutyTeam()` - 获取班组管理列表，符合tbl_duty_turn_group表设计 ✅
- `listPostDutyDesign(orgId)` - 岗位班次设置列表 ✅

#### 8.1.4 排班查询与统计
- `queryDutySchedule(dto)` - 查询排班信息，直接查询tbl_duty_emp核心表
- `editDutySchedule(dto)` - 编辑排班信息
- `personalDutyStatistics(dto)` - 个人排班统计
- `listSimplePostCategory(orgId)` - 查询单位下岗位类别

### 8.2 接口与MD设计的符合度分析

#### ✅ 完全符合的部分
1. **核心表使用正确**：主要操作都围绕`tbl_duty_emp`核心表进行
2. **关联查询合理**：正确关联了人员、岗位、班次、组织等表
3. **业务逻辑清晰**：值班动态、排班查询、统计分析等功能完整
4. **班组管理完整**：`/business/duty/manage/duty_team/list` 接口已实现班组管理功能

#### ⚠️ 部分缺失的功能
1. **排班规则管理**：缺少对`tbl_duty_shifts`和`tbl_duty_shifts_detail`的操作接口
2. **排班组管理**：缺少对`tbl_duty_platoon_group`的管理接口
3. **调班历史**：缺少对`tbl_duty_emp_shift_detail`的历史记录管理
4. **自动排班**：缺少基于规则自动生成排班的功能

#### 📋 两个"组"的区别说明
- **班组（tbl_duty_turn_group）**：与具体岗位绑定的班组，如"1号岗夜班A组"
  - 字段：`post_id`（岗位ID）、`group_name`（班组名称）
  - 已有接口：`listDutyTeam()` ✅
  
- **排班组（tbl_duty_platoon_group）**：用于排班规则的人员分组，如"第一排班组"
  - 字段：`name`（排班组名称）、`type`（排班组类型）
  - 缺少接口：需要补充排班组管理功能 ⚠️

#### 🔧 需要补充的接口

```java
// 排班规则管理
Boolean saveShiftsRule(DutyShiftsDTO dto);
List<DutyShiftsVO> listShiftsRules(String orgId);
Boolean deleteShiftsRule(String id);

// 排班组管理  
Boolean savePlatoonGroup(DutyPlatoonGroupDTO dto);
List<DutyPlatoonGroupVO> listPlatoonGroups(String orgId);

// 自动排班
Boolean generateScheduleByRule(ScheduleGenerateDTO dto);

// 调班历史
List<DutyShiftHistoryVO> getShiftHistory(String empId, LocalDate startDate, LocalDate endDate);
Boolean recordShiftChange(DutyShiftChangeDTO dto);
```

### 8.3 实现建议

#### 8.3.1 短期优化
1. **补充排班规则管理接口**，完善规则驱动的排班逻辑
2. **增加调班历史记录功能**，提升系统可追溯性
3. **优化查询性能**，添加必要的数据库索引

#### 8.3.2 中期扩展
1. **实现自动排班算法**，基于规则自动生成排班计划
2. **增加排班冲突检测**，避免人员重复排班
3. **支持批量操作**，提升排班效率

#### 8.3.3 长期规划
1. **智能排班推荐**，基于历史数据和人员偏好
2. **移动端支持**，值班签到和状态更新
3. **数据分析报表**，值班质量和效率分析

## 9. 扩展说明

### 9.1 未来可扩展功能
- 自动排班算法优化
- 移动端值班签到
- 值班质量评估
- 智能调班推荐
- 值班统计分析

### 9.2 性能优化建议
- 对 `duty_date` 字段建立索引
- 对 `emp_id` 和 `post_id` 建立复合索引
- 定期清理历史数据
- 使用缓存提升查询性能

---

**文档版本**：v1.1  
**最后更新**：2025-01-16  
**维护人员**：开发团队
